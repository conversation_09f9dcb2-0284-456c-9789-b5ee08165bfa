{"version": 3, "file": "detail.js", "sources": ["pages/device/detail.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZGV2aWNlL2RldGFpbC52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"device-detail-container\">\r\n\t\t<!-- 设备基本信息卡片 -->\r\n\t\t<view class=\"detail-card\">\r\n\t\t\t<view class=\"card-header\">\r\n\t\t\t\t<text class=\"card-title\">基本信息</text>\r\n\t\t\t\t<view class=\"device-status\" :class=\"deviceInfo.status\">{{ getStatusText(deviceInfo.status) }}</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"info-item\">\r\n\t\t\t\t<text class=\"info-label\">设备名称</text>\r\n\t\t\t\t<text class=\"info-value\">{{ deviceInfo.name }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\">\r\n\t\t\t\t<text class=\"info-label\">设备类型</text>\r\n\t\t\t\t<text class=\"info-value\">{{ deviceInfo.type }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\">\r\n\t\t\t\t<text class=\"info-label\">设备型号</text>\r\n\t\t\t\t<text class=\"info-value\">{{ deviceInfo.model }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\">\r\n\t\t\t\t<text class=\"info-label\">设备编号</text>\r\n\t\t\t\t<text class=\"info-value\">{{ deviceInfo.deviceId }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\">\r\n\t\t\t\t<text class=\"info-label\">安装位置</text>\r\n\t\t\t\t<text class=\"info-value\">{{ getLocation() }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\">\r\n\t\t\t\t<text class=\"info-label\">生产厂商</text>\r\n\t\t\t\t<text class=\"info-value\">{{ deviceInfo.manufacturer || '未知' }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\">\r\n\t\t\t\t<text class=\"info-label\">安装日期</text>\r\n\t\t\t\t<text class=\"info-value\">{{ formatDate(deviceInfo.installTime) }}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 运行状态卡片 -->\r\n\t\t<view class=\"detail-card\">\r\n\t\t\t<view class=\"card-header\">\r\n\t\t\t\t<text class=\"card-title\">运行状态</text>\r\n\t\t\t\t<text class=\"refresh-btn\" @click=\"refreshData\">刷新</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"status-panel\">\r\n\t\t\t\t<view class=\"status-item\" v-for=\"(metric, index) in deviceMetrics\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"metric-value\" :class=\"{ alert: metric.isAlert }\">{{ metric.value }}</view>\r\n\t\t\t\t\t<view class=\"metric-label\">{{ metric.label }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"chart-container\" v-if=\"deviceInfo.type === 'pump' || deviceInfo.type === 'sensor'\">\r\n\t\t\t\t<!-- 图表区域，实际项目中集成echart或其他图表库 -->\r\n\t\t\t\t<view class=\"chart-placeholder\">\r\n\t\t\t\t\t<text>实时数据趋势图</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 告警信息卡片 -->\r\n\t\t<view class=\"detail-card\" v-if=\"alarmList.length > 0\">\r\n\t\t\t<view class=\"card-header\">\r\n\t\t\t\t<text class=\"card-title\">告警信息</text>\r\n\t\t\t\t<text class=\"view-all\" @click=\"navigateToAlarmList\">查看全部</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"alarm-list\">\r\n\t\t\t\t<view class=\"alarm-item\" v-for=\"(alarm, index) in alarmList\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"alarm-icon\" :class=\"alarm.level\"></view>\r\n\t\t\t\t\t<view class=\"alarm-content\">\r\n\t\t\t\t\t\t<view class=\"alarm-title\">{{ alarm.title }}</view>\r\n\t\t\t\t\t\t<view class=\"alarm-desc\">{{ alarm.description }}</view>\r\n\t\t\t\t\t\t<view class=\"alarm-time\">{{ alarm.time }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 维护记录卡片 -->\r\n\t\t<view class=\"detail-card\">\r\n\t\t\t<view class=\"card-header\">\r\n\t\t\t\t<text class=\"card-title\">维护记录</text>\r\n\t\t\t\t<text class=\"view-all\" @click=\"navigateToMaintenanceList\">查看全部</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"maintenance-list\">\r\n\t\t\t\t<view class=\"maintenance-item\" v-for=\"(record, index) in maintenanceRecords\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"maintenance-time\">\r\n\t\t\t\t\t\t<text class=\"date\">{{ formatMaintenanceDate(record.time) }}</text>\r\n\t\t\t\t\t\t<text class=\"time\">{{ formatMaintenanceTime(record.time) }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"maintenance-content\">\r\n\t\t\t\t\t\t<view class=\"maintenance-title\">{{ record.title }}</view>\r\n\t\t\t\t\t\t<view class=\"maintenance-desc\">{{ record.description }}</view>\r\n\t\t\t\t\t\t<view class=\"maintenance-operator\">操作人：{{ record.operator }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"empty-list\" v-if=\"maintenanceRecords.length === 0\">\r\n\t\t\t\t\t<text>暂无维护记录</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 操作按钮 -->\r\n\t\t<view class=\"action-buttons\">\r\n\t\t\t<view class=\"action-btn warning\" @click=\"reportFault\">\r\n\t\t\t\t<text class=\"iconfont icon-warning\"></text>\r\n\t\t\t\t<text>上报故障</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"action-btn primary\" @click=\"createMaintenance\">\r\n\t\t\t\t<text class=\"iconfont icon-maintain\"></text>\r\n\t\t\t\t<text>添加维护</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// 引入API模块\r\n\timport { deviceApi } from '../../utils/api';\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tdeviceId: '',\r\n\t\t\t\tdeviceInfo: {\r\n\t\t\t\t\tdeviceId: '',\r\n\t\t\t\t\tname: '',\r\n\t\t\t\t\ttype: '',\r\n\t\t\t\t\tmodel: '',\r\n\t\t\t\t\tstatus: 'offline',\r\n\t\t\t\t\tlocation: {\r\n\t\t\t\t\t\tbuilding: '',\r\n\t\t\t\t\t\tfloor: '',\r\n\t\t\t\t\t\troom: '',\r\n\t\t\t\t\t\tcoordinates: {\r\n\t\t\t\t\t\t\tlat: 0,\r\n\t\t\t\t\t\t\tlng: 0\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tmanufacturer: '',\r\n\t\t\t\t\tinstallTime: '',\r\n\t\t\t\t\tlastMaintenance: '',\r\n\t\t\t\t\tnextMaintenance: ''\r\n\t\t\t\t},\r\n\t\t\t\tdeviceMetrics: [],\r\n\t\t\t\talarmList: [],\r\n\t\t\t\tmaintenanceRecords: [],\r\n\t\t\t\tisLoading: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\t// 获取路由参数中的设备ID\r\n\t\t\tthis.deviceId = options.id;\r\n\t\t\t// 加载设备详情\r\n\t\t\tthis.loadDeviceDetails();\r\n\t\t\t// 加载设备运行指标\r\n\t\t\tthis.loadDeviceMetrics();\r\n\t\t\t// 加载设备告警信息\r\n\t\t\tthis.loadDeviceAlarms();\r\n\t\t\t// 加载设备维护记录\r\n\t\t\tthis.loadMaintenanceRecords();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 加载设备详情\r\n\t\t\tloadDeviceDetails() {\r\n\t\t\t\tthis.isLoading = true;\r\n\t\t\t\t\r\n\t\t\t\t// 调用API获取设备详情\r\n\t\t\t\tdeviceApi.getDetail(this.deviceId)\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\t\t\tthis.deviceInfo = res.data;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.showError(res.message || '加载失败');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tthis.showError('网络异常，请稍后重试');\r\n\t\t\t\t\t\tconsole.error('获取设备详情失败:', err);\r\n\t\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 如果后端API还未就绪，可以使用模拟数据\r\n\t\t\t\t/*\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.deviceInfo = this.getMockDeviceDetail();\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t}, 500);\r\n\t\t\t\t*/\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 加载设备运行指标\r\n\t\t\tloadDeviceMetrics() {\r\n\t\t\t\t// 模拟API调用\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t// 实际项目中应该调用API\r\n\t\t\t\t\t// uni.request({\r\n\t\t\t\t\t//     url: `/api/devices/${this.deviceId}/metrics`,\r\n\t\t\t\t\t//     method: 'GET',\r\n\t\t\t\t\t//     success: (res) => {\r\n\t\t\t\t\t//         this.deviceMetrics = res.data;\r\n\t\t\t\t\t//     }\r\n\t\t\t\t\t// });\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 模拟数据\r\n\t\t\t\t\tswitch(this.deviceInfo.type) {\r\n\t\t\t\t\t\tcase 'pump':\r\n\t\t\t\t\t\t\tthis.deviceMetrics = [\r\n\t\t\t\t\t\t\t\t{ label: '流量', value: '200 m³/h', isAlert: false },\r\n\t\t\t\t\t\t\t\t{ label: '温度', value: '65 °C', isAlert: false },\r\n\t\t\t\t\t\t\t\t{ label: '压力', value: '0.4 MPa', isAlert: false },\r\n\t\t\t\t\t\t\t\t{ label: '功率', value: '5.5 kW', isAlert: false }\r\n\t\t\t\t\t\t\t];\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'valve':\r\n\t\t\t\t\t\t\tthis.deviceMetrics = [\r\n\t\t\t\t\t\t\t\t{ label: '开度', value: '80%', isAlert: false },\r\n\t\t\t\t\t\t\t\t{ label: '压差', value: '0.2 MPa', isAlert: false }\r\n\t\t\t\t\t\t\t];\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'sensor':\r\n\t\t\t\t\t\t\tthis.deviceMetrics = [\r\n\t\t\t\t\t\t\t\t{ label: '温度', value: '72 °C', isAlert: true },\r\n\t\t\t\t\t\t\t\t{ label: '信号强度', value: '95%', isAlert: false }\r\n\t\t\t\t\t\t\t];\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'controller':\r\n\t\t\t\t\t\t\tthis.deviceMetrics = [\r\n\t\t\t\t\t\t\t\t{ label: 'CPU使用率', value: '45%', isAlert: false },\r\n\t\t\t\t\t\t\t\t{ label: '内存使用率', value: '60%', isAlert: false },\r\n\t\t\t\t\t\t\t\t{ label: '网络状态', value: '正常', isAlert: false }\r\n\t\t\t\t\t\t\t];\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tdefault:\r\n\t\t\t\t\t\t\tthis.deviceMetrics = [];\r\n\t\t\t\t\t}\r\n\t\t\t\t}, 600);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 加载设备告警信息\r\n\t\t\tloadDeviceAlarms() {\r\n\t\t\t\t// 模拟API调用\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t// 实际项目中应该调用API\r\n\t\t\t\t\t// uni.request({\r\n\t\t\t\t\t//     url: `/api/devices/${this.deviceId}/alarms`,\r\n\t\t\t\t\t//     method: 'GET',\r\n\t\t\t\t\t//     success: (res) => {\r\n\t\t\t\t\t//         this.alarmList = res.data;\r\n\t\t\t\t\t//     }\r\n\t\t\t\t\t// });\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 模拟数据\r\n\t\t\t\t\tthis.alarmList = [\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: 'al001',\r\n\t\t\t\t\t\t\tlevel: 'warning',\r\n\t\t\t\t\t\t\ttitle: '温度过高警告',\r\n\t\t\t\t\t\t\tdescription: '设备温度达到75°C，接近阈值（80°C）',\r\n\t\t\t\t\t\t\ttime: '2023-12-05 14:30:25'\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: 'al002',\r\n\t\t\t\t\t\t\tlevel: 'error',\r\n\t\t\t\t\t\t\ttitle: '压力异常',\r\n\t\t\t\t\t\t\tdescription: '压力低于正常运行范围，可能存在泄漏',\r\n\t\t\t\t\t\t\ttime: '2023-12-04 08:15:10'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t];\r\n\t\t\t\t}, 700);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 加载维护记录\r\n\t\t\tloadMaintenanceRecords() {\r\n\t\t\t\t// 模拟API调用\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t// 实际项目中应该调用API\r\n\t\t\t\t\t// uni.request({\r\n\t\t\t\t\t//     url: `/api/devices/${this.deviceId}/maintenance`,\r\n\t\t\t\t\t//     method: 'GET',\r\n\t\t\t\t\t//     success: (res) => {\r\n\t\t\t\t\t//         this.maintenanceRecords = res.data;\r\n\t\t\t\t\t//     }\r\n\t\t\t\t\t// });\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 模拟数据\r\n\t\t\t\t\tthis.maintenanceRecords = [\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: 'mr001',\r\n\t\t\t\t\t\t\ttitle: '例行检查',\r\n\t\t\t\t\t\t\tdescription: '检查设备运行状态，更换润滑油，无异常',\r\n\t\t\t\t\t\t\ttime: '2023-12-01 10:15:00',\r\n\t\t\t\t\t\t\toperator: '张工'\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: 'mr002',\r\n\t\t\t\t\t\t\ttitle: '故障维修',\r\n\t\t\t\t\t\t\tdescription: '更换损坏的轴承，恢复设备运行',\r\n\t\t\t\t\t\t\ttime: '2023-11-15 14:30:00',\r\n\t\t\t\t\t\t\toperator: '李工'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t];\r\n\t\t\t\t}, 800);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 刷新数据\r\n\t\t\trefreshData() {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '刷新中...'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\tthis.loadDeviceMetrics();\r\n\t\t\t\tthis.loadDeviceAlarms();\r\n\t\t\t\t\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '数据已更新',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t});\r\n\t\t\t\t}, 800);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 上报故障\r\n\t\t\treportFault() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/fault/report?deviceId=${this.deviceId}`\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 添加维护记录\r\n\t\t\tcreateMaintenance() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/maintenance/add?deviceId=${this.deviceId}`\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 跳转到告警列表\r\n\t\t\tnavigateToAlarmList() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/alarm/list?deviceId=${this.deviceId}`\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 跳转到维护记录列表\r\n\t\t\tnavigateToMaintenanceList() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/maintenance/list?deviceId=${this.deviceId}`\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取状态文本\r\n\t\t\tgetStatusText(status) {\r\n\t\t\t\tswitch(status) {\r\n\t\t\t\t\tcase 'online':\r\n\t\t\t\t\t\treturn '在线';\r\n\t\t\t\t\tcase 'offline':\r\n\t\t\t\t\t\treturn '离线';\r\n\t\t\t\t\tcase 'fault':\r\n\t\t\t\t\t\treturn '故障';\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t\t\treturn '未知';\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取位置文本\r\n\t\t\tgetLocation() {\r\n\t\t\t\tconst loc = this.deviceInfo.location;\r\n\t\t\t\tif (!loc) return '未知位置';\r\n\t\t\t\t\r\n\t\t\t\treturn `${loc.building || ''} ${loc.floor || ''} ${loc.room || ''}`.trim();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 格式化日期\r\n\t\t\tformatDate(dateString) {\r\n\t\t\t\tif (!dateString) return '未知';\r\n\t\t\t\t\r\n\t\t\t\tconst date = new Date(dateString);\r\n\t\t\t\treturn `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 格式化维护日期\r\n\t\t\tformatMaintenanceDate(dateString) {\r\n\t\t\t\tif (!dateString) return '';\r\n\t\t\t\t\r\n\t\t\t\tconst date = new Date(dateString);\r\n\t\t\t\treturn `${date.getMonth() + 1}月${date.getDate()}日`;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 格式化维护时间\r\n\t\t\tformatMaintenanceTime(dateString) {\r\n\t\t\t\tif (!dateString) return '';\r\n\t\t\t\t\r\n\t\t\t\tconst date = new Date(dateString);\r\n\t\t\t\treturn `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.device-detail-container {\r\n\t\tpadding: 30rpx;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tmin-height: 100vh;\r\n\t}\r\n\t\r\n\t.detail-card {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 12rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\r\n\t\t\r\n\t\t.card-header {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin-bottom: 24rpx;\r\n\t\t\t\r\n\t\t\t.card-title {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tpadding-left: 20rpx;\r\n\t\t\t\t\r\n\t\t\t\t&::before {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t\ttop: 8rpx;\r\n\t\t\t\t\twidth: 6rpx;\r\n\t\t\t\t\theight: 28rpx;\r\n\t\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\t\tborder-radius: 3rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.device-status {\r\n\t\t\t\tpadding: 4rpx 16rpx;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\t\r\n\t\t\t\t&.online {\r\n\t\t\t\t\tbackground-color: $uni-color-success;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&.offline {\r\n\t\t\t\t\tbackground-color: $uni-text-color-grey;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&.fault {\r\n\t\t\t\t\tbackground-color: $uni-color-error;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.refresh-btn, .view-all {\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.info-item {\r\n\t\tdisplay: flex;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\t\r\n\t\t.info-label {\r\n\t\t\twidth: 180rpx;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: $uni-text-color-grey;\r\n\t\t}\r\n\t\t\r\n\t\t.info-value {\r\n\t\t\tflex: 1;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: $uni-text-color;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.status-panel {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\t\r\n\t\t.status-item {\r\n\t\t\twidth: 33.33%;\r\n\t\t\ttext-align: center;\r\n\t\t\tmargin-bottom: 30rpx;\r\n\t\t\t\r\n\t\t\t.metric-value {\r\n\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t\r\n\t\t\t\t&.alert {\r\n\t\t\t\t\tcolor: $uni-color-error;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.metric-label {\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.chart-container {\r\n\t\tmargin-top: 20rpx;\r\n\t\t\r\n\t\t.chart-placeholder {\r\n\t\t\theight: 300rpx;\r\n\t\t\tbackground-color: #f5f5f5;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\t\r\n\t\t\ttext {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.alarm-list {\r\n\t\t.alarm-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\r\n\t\t\t.alarm-icon {\r\n\t\t\t\twidth: 16rpx;\r\n\t\t\t\theight: 16rpx;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tmargin-top: 12rpx;\r\n\t\t\t\tmargin-right: 16rpx;\r\n\t\t\t\t\r\n\t\t\t\t&.warning {\r\n\t\t\t\t\tbackground-color: $uni-color-warning;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&.error {\r\n\t\t\t\t\tbackground-color: $uni-color-error;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.alarm-content {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\t\r\n\t\t\t\t.alarm-title {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.alarm-desc {\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.alarm-time {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.maintenance-list {\r\n\t\t.maintenance-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\tmargin-bottom: 30rpx;\r\n\t\t\t\r\n\t\t\t.maintenance-time {\r\n\t\t\t\twidth: 120rpx;\r\n\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\r\n\t\t\t\t.date {\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t\tmargin-bottom: 4rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.time {\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.maintenance-content {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tpadding-left: 30rpx;\r\n\t\t\t\t\r\n\t\t\t\t&::before {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t\ttop: 0;\r\n\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\twidth: 2rpx;\r\n\t\t\t\t\tbackground-color: #ddd;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&::after {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tleft: -4rpx;\r\n\t\t\t\t\ttop: 10rpx;\r\n\t\t\t\t\twidth: 10rpx;\r\n\t\t\t\t\theight: 10rpx;\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.maintenance-title {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.maintenance-desc {\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.maintenance-operator {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.empty-list {\r\n\t\t\ttext-align: center;\r\n\t\t\tpadding: 40rpx 0;\r\n\t\t\t\r\n\t\t\ttext {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.action-buttons {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tpadding: 20rpx 30rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tbox-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);\r\n\t\tdisplay: flex;\r\n\t\t\r\n\t\t.action-btn {\r\n\t\t\tflex: 1;\r\n\t\t\theight: 80rpx;\r\n\t\t\tborder-radius: 40rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tmargin: 0 10rpx;\r\n\t\t\t\r\n\t\t\t.iconfont {\r\n\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.primary {\r\n\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.warning {\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tcolor: $uni-color-warning;\r\n\t\t\t\tborder: 1px solid $uni-color-warning;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style> ", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/device/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["deviceApi", "uni"], "mappings": ";;;AA4HC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,UAAU;AAAA,MACV,YAAY;AAAA,QACX,UAAU;AAAA,QACV,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,UAAU;AAAA,UACT,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,YACZ,KAAK;AAAA,YACL,KAAK;AAAA,UACN;AAAA,QACA;AAAA,QACD,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,MACjB;AAAA,MACD,eAAe,CAAE;AAAA,MACjB,WAAW,CAAE;AAAA,MACb,oBAAoB,CAAE;AAAA,MACtB,WAAW;AAAA,IACZ;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AAEf,SAAK,WAAW,QAAQ;AAExB,SAAK,kBAAiB;AAEtB,SAAK,kBAAiB;AAEtB,SAAK,iBAAgB;AAErB,SAAK,uBAAsB;AAAA,EAC3B;AAAA,EACD,SAAS;AAAA;AAAA,IAER,oBAAoB;AACnB,WAAK,YAAY;AAGjBA,0BAAU,UAAU,KAAK,QAAQ,EAC/B,KAAK,SAAO;AACZC,sBAAG,MAAC,YAAW;AACf,YAAI,IAAI,SAAS,KAAK;AACrB,eAAK,aAAa,IAAI;AAAA,eAChB;AACN,eAAK,UAAU,IAAI,WAAW,MAAM;AAAA,QACrC;AAAA,OACA,EACA,MAAM,SAAO;AACbA,sBAAG,MAAC,YAAW;AACf,aAAK,UAAU,YAAY;AAC3BA,6EAAc,aAAa,GAAG;AAAA,MAC/B,CAAC;AAAA,IASF;AAAA;AAAA,IAGD,oBAAoB;AAEnB,iBAAW,MAAM;AAWhB,gBAAO,KAAK,WAAW,MAAI;AAAA,UAC1B,KAAK;AACJ,iBAAK,gBAAgB;AAAA,cACpB,EAAE,OAAO,MAAM,OAAO,YAAY,SAAS,MAAO;AAAA,cAClD,EAAE,OAAO,MAAM,OAAO,SAAS,SAAS,MAAO;AAAA,cAC/C,EAAE,OAAO,MAAM,OAAO,WAAW,SAAS,MAAO;AAAA,cACjD,EAAE,OAAO,MAAM,OAAO,UAAU,SAAS,MAAM;AAAA;AAEhD;AAAA,UACD,KAAK;AACJ,iBAAK,gBAAgB;AAAA,cACpB,EAAE,OAAO,MAAM,OAAO,OAAO,SAAS,MAAO;AAAA,cAC7C,EAAE,OAAO,MAAM,OAAO,WAAW,SAAS,MAAM;AAAA;AAEjD;AAAA,UACD,KAAK;AACJ,iBAAK,gBAAgB;AAAA,cACpB,EAAE,OAAO,MAAM,OAAO,SAAS,SAAS,KAAM;AAAA,cAC9C,EAAE,OAAO,QAAQ,OAAO,OAAO,SAAS,MAAM;AAAA;AAE/C;AAAA,UACD,KAAK;AACJ,iBAAK,gBAAgB;AAAA,cACpB,EAAE,OAAO,UAAU,OAAO,OAAO,SAAS,MAAO;AAAA,cACjD,EAAE,OAAO,SAAS,OAAO,OAAO,SAAS,MAAO;AAAA,cAChD,EAAE,OAAO,QAAQ,OAAO,MAAM,SAAS,MAAM;AAAA;AAE9C;AAAA,UACD;AACC,iBAAK,gBAAgB;QACvB;AAAA,MACA,GAAE,GAAG;AAAA,IACN;AAAA;AAAA,IAGD,mBAAmB;AAElB,iBAAW,MAAM;AAWhB,aAAK,YAAY;AAAA,UAChB;AAAA,YACC,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,OAAO;AAAA,YACP,aAAa;AAAA,YACb,MAAM;AAAA,UACN;AAAA,UACD;AAAA,YACC,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,OAAO;AAAA,YACP,aAAa;AAAA,YACb,MAAM;AAAA,UACP;AAAA;MAED,GAAE,GAAG;AAAA,IACN;AAAA;AAAA,IAGD,yBAAyB;AAExB,iBAAW,MAAM;AAWhB,aAAK,qBAAqB;AAAA,UACzB;AAAA,YACC,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,aAAa;AAAA,YACb,MAAM;AAAA,YACN,UAAU;AAAA,UACV;AAAA,UACD;AAAA,YACC,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,aAAa;AAAA,YACb,MAAM;AAAA,YACN,UAAU;AAAA,UACX;AAAA;MAED,GAAE,GAAG;AAAA,IACN;AAAA;AAAA,IAGD,cAAc;AACbA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACR,CAAC;AAED,WAAK,kBAAiB;AACtB,WAAK,iBAAgB;AAErB,iBAAW,MAAM;AAChBA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACD,GAAE,GAAG;AAAA,IACN;AAAA;AAAA,IAGD,cAAc;AACbA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,gCAAgC,KAAK,QAAQ;AAAA,MACnD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB;AACnBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,mCAAmC,KAAK,QAAQ;AAAA,MACtD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,sBAAsB;AACrBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,8BAA8B,KAAK,QAAQ;AAAA,MACjD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,4BAA4B;AAC3BA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,oCAAoC,KAAK,QAAQ;AAAA,MACvD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,cAAc,QAAQ;AACrB,cAAO,QAAM;AAAA,QACZ,KAAK;AACJ,iBAAO;AAAA,QACR,KAAK;AACJ,iBAAO;AAAA,QACR,KAAK;AACJ,iBAAO;AAAA,QACR;AACC,iBAAO;AAAA,MACT;AAAA,IACA;AAAA;AAAA,IAGD,cAAc;AACb,YAAM,MAAM,KAAK,WAAW;AAC5B,UAAI,CAAC;AAAK,eAAO;AAEjB,aAAO,GAAG,IAAI,YAAY,EAAE,IAAI,IAAI,SAAS,EAAE,IAAI,IAAI,QAAQ,EAAE,GAAG;IACpE;AAAA;AAAA,IAGD,WAAW,YAAY;AACtB,UAAI,CAAC;AAAY,eAAO;AAExB,YAAM,OAAO,IAAI,KAAK,UAAU;AAChC,aAAO,GAAG,KAAK,YAAW,CAAE,IAAI,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,KAAK,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,IACvH;AAAA;AAAA,IAGD,sBAAsB,YAAY;AACjC,UAAI,CAAC;AAAY,eAAO;AAExB,YAAM,OAAO,IAAI,KAAK,UAAU;AAChC,aAAO,GAAG,KAAK,aAAa,CAAC,IAAI,KAAK,SAAS;AAAA,IAC/C;AAAA;AAAA,IAGD,sBAAsB,YAAY;AACjC,UAAI,CAAC;AAAY,eAAO;AAExB,YAAM,OAAO,IAAI,KAAK,UAAU;AAChC,aAAO,GAAG,OAAO,KAAK,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,KAAK,YAAY,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,IACjG;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjZD,GAAG,WAAW,eAAe;"}