"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      settings: {
        newOrderNotice: true,
        orderStatusNotice: true,
        orderTimeoutNotice: true,
        patrolPlanNotice: true,
        patrolDelayNotice: false,
        systemUpdateNotice: true,
        accountSecurityNotice: true,
        inAppNotice: true,
        smsNotice: false
      }
    };
  },
  onLoad() {
    this.loadMessageSettings();
  },
  methods: {
    // 加载消息设置
    loadMessageSettings() {
      const settings = common_vendor.index.getStorageSync("messageSettings");
      if (settings) {
        this.settings = Object.assign({}, this.settings, settings);
      }
    },
    // 切换设置状态
    toggleSetting(key) {
      this.settings[key] = !this.settings[key];
    },
    // 保存设置
    saveSettings() {
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.setStorageSync("messageSettings", this.settings);
        common_vendor.index.showToast({
          title: "设置已保存",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      }, 1e3);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $data.settings.systemUpdateNotice,
    b: common_vendor.o(($event) => $options.toggleSetting("systemUpdateNotice")),
    c: $data.settings.accountSecurityNotice,
    d: common_vendor.o(($event) => $options.toggleSetting("accountSecurityNotice")),
    e: $data.settings.inAppNotice,
    f: common_vendor.o(($event) => $options.toggleSetting("inAppNotice")),
    g: $data.settings.smsNotice,
    h: common_vendor.o(($event) => $options.toggleSetting("smsNotice")),
    i: common_vendor.o((...args) => $options.saveSettings && $options.saveSettings(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/user/message-settings.js.map
