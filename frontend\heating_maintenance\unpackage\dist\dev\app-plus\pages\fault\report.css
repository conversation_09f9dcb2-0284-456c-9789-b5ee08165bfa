/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.fault-report-container {
  padding: 0.625rem;
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 3.75rem;
}
.form-card {
  background-color: #fff;
  border-radius: 0.375rem;
  padding: 0.9375rem;
  margin-bottom: 0.625rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.form-header {
  margin-bottom: 0.625rem;
}
.form-header .form-title {
  font-size: 0.9375rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.25rem;
  display: block;
}
.form-header .form-subtitle {
  font-size: 0.75rem;
  color: #666;
}
.form-group .form-item {
  margin-bottom: 0.75rem;
}
.form-group .form-item:last-child {
  margin-bottom: 0;
}
.form-group .form-item .form-label {
  font-size: 0.875rem;
  color: #333;
  margin-bottom: 0.375rem;
  display: block;
}
.form-group .form-item .form-label.required::before {
  content: "*";
  color: #f5222d;
  margin-right: 0.125rem;
}
.form-group .form-item .form-input-container {
  position: relative;
  width: 100%;
  background-color: #f8f8f8;
  border-radius: 0.25rem;
  border: 0.03125rem solid #e5e5e5;
  transition: background-color 0.2s;
}
.form-group .form-item .form-input-container:active {
  background-color: #f0f0f0;
}
.form-group .form-item .form-picker-container {
  background-color: #f8f8f8;
  padding: 0.625rem;
  border-radius: 0.25rem;
  border: 0.03125rem solid #e5e5e5;
  position: relative;
  overflow: hidden;
  transition: background-color 0.2s;
}
.form-group .form-item .form-picker-container:active {
  background-color: #f0f0f0;
}
.form-group .form-item .form-picker-container::after {
  content: "";
  position: absolute;
  right: 0.625rem;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 0.375rem solid transparent;
  border-right: 0.375rem solid transparent;
  border-top: 0.375rem solid #999;
  pointer-events: none;
}
.form-group .form-item .form-picker {
  width: 100%;
  height: 100%;
  border: none;
  background: transparent;
}
.form-group .form-item .form-picker .picker-text {
  font-size: 0.875rem;
  color: #333;
  padding-right: 1.25rem;
}
.form-group .form-item .form-picker .picker-text.placeholder {
  color: #808080;
}
.form-group .form-item .form-value {
  font-size: 0.875rem;
  color: #333;
  padding: 0.625rem;
  background-color: #f8f8f8;
  border-radius: 0.25rem;
}
.form-group .form-item .level-options {
  display: flex;
  justify-content: space-between;
}
.form-group .form-item .level-options .level-option {
  flex: 1;
  height: 2.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 0.3125rem;
  background-color: #f8f8f8;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  color: #333;
}
.form-group .form-item .level-options .level-option:first-child {
  margin-left: 0;
}
.form-group .form-item .level-options .level-option:last-child {
  margin-right: 0;
}
.form-group .form-item .level-options .level-option.active {
  background-color: #1890ff;
  color: #fff;
}
.form-group .form-item .form-textarea {
  width: 100%;
  height: 6.25rem;
  padding: 0.625rem;
  box-sizing: border-box;
  background-color: #f8f8f8;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  position: relative;
}
.form-group .form-item .textarea-counter {
  position: absolute;
  right: 0.9375rem;
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: #666;
}
.upload-area .image-list {
  display: flex;
  flex-wrap: wrap;
}
.upload-area .image-list .image-item, .upload-area .image-list .upload-item {
  width: 6.25rem;
  height: 6.25rem;
  margin-right: 0.625rem;
  margin-bottom: 0.625rem;
  position: relative;
}
.upload-area .image-list .image-item uni-image {
  width: 100%;
  height: 100%;
  border-radius: 0.25rem;
}
.upload-area .image-list .image-item .delete-icon {
  position: absolute;
  top: -0.5rem;
  right: -0.5rem;
  width: 1.25rem;
  height: 1.25rem;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1rem;
}
.upload-area .image-list .upload-item {
  border: 0.0625rem dashed #ddd;
  border-radius: 0.25rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.upload-area .image-list .upload-item .iconfont {
  font-size: 1.875rem;
  color: #666;
  margin-bottom: 0.5rem;
}
.upload-area .image-list .upload-item uni-text {
  font-size: 0.75rem;
  color: #666;
}
.upload-area .video-container {
  width: 100%;
  height: 12.5rem;
  position: relative;
}
.upload-area .video-container uni-video {
  width: 100%;
  height: 100%;
  border-radius: 0.25rem;
}
.upload-area .video-container .delete-icon {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 1.25rem;
  height: 1.25rem;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1rem;
}
.upload-area .video-container .video-actions {
  margin-top: 0.625rem;
  display: flex;
  justify-content: center;
}
.upload-area .video-container .video-actions .video-action-btn {
  background-color: #f5f5f5;
  color: #333;
  padding: 0.3125rem 0.9375rem;
  border-radius: 0.9375rem;
  font-size: 0.75rem;
  box-shadow: 0 0.0625rem 0.15625rem rgba(0, 0, 0, 0.1);
}
.submit-btn-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 0.625rem;
  background-color: #fff;
  box-shadow: 0 -0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.submit-btn-container .submit-btn {
  width: 100%;
  height: 2.75rem;
  line-height: 2.75rem;
  background-color: #1890ff;
  color: #fff;
  font-size: 1rem;
  border-radius: 0.25rem;
}
.form-input {
  background-color: transparent;
  padding: 0.625rem;
  width: 100%;
  height: 2.5rem;
  box-sizing: border-box;
  font-size: 0.875rem;
  color: #333;
  border: none;
}
.form-input-container {
  position: relative;
  width: 100%;
  background-color: #f8f8f8;
  border-radius: 0.25rem;
  border: 0.03125rem solid #e5e5e5;
  transition: background-color 0.2s;
}
.form-input-container:active {
  background-color: #f0f0f0;
}
.address-inputs {
  display: flex;
  align-items: center;
  margin-bottom: 0.3125rem;
}
.address-inputs.error {
  border-color: #fa436a;
  background-color: #fff0f0;
  animation: shake 0.5s ease-in-out;
}
.address-input-item {
  position: relative;
  background-color: #f8f8f8;
  border-radius: 0.25rem;
  border: 0.03125rem solid #e5e5e5;
  flex: 1;
  padding-right: 1.875rem;
}
.address-input-item.error {
  border-color: #fa436a;
  background-color: #fff0f0;
  animation: shake 0.5s ease-in-out;
}
.address-input-item .form-input {
  height: 2.5rem;
  padding: 0 0.625rem;
  font-size: 0.875rem;
}
.address-input-item .address-input-label {
  position: absolute;
  right: 0.625rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.75rem;
  color: #909399;
}
.room-input {
  flex: 1.5;
}
.address-separator {
  padding: 0 0.3125rem;
  color: #909399;
  font-size: 0.9375rem;
  font-weight: bold;
}
.address-hint {
  font-size: 0.75rem;
  color: #909399;
  margin-top: 0.1875rem;
  margin-left: 0.3125rem;
}
.address-preview {
  margin-top: 0.3125rem;
  padding: 0.46875rem;
  background-color: #f0f8ff;
  border-radius: 0.25rem;
  border: 0.03125rem solid #c8e1ff;
  display: flex;
  align-items: center;
}
.address-preview .preview-label {
  font-size: 0.8125rem;
  color: #606266;
  margin-right: 0.3125rem;
}
.address-preview .preview-value {
  font-size: 0.9375rem;
  color: #409EFF;
  font-weight: bold;
}
@keyframes shake {
0%, 100% {
    transform: translateX(0);
}
20%, 60% {
    transform: translateX(-5px);
}
40%, 80% {
    transform: translateX(5px);
}
}