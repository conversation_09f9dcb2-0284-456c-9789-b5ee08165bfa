{"version": 3, "file": "control.js", "sources": ["pages/valves/control.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdmFsdmVzL2NvbnRyb2wudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"valve-control-page\"> \r\n\t\t\r\n\t\t<!-- 选择区域 -->\r\n\t\t<view class=\"select-area\">\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"form-label\">小区</text>\r\n\t\t\t\t<picker @change=\"handleHeatUnitChange\" :value=\"heatUnitIndex\" :range=\"heatUnitOptions\" class=\"form-picker\">\r\n\t\t\t\t\t<view class=\"picker-content\">\r\n\t\t\t\t\t\t<text class=\"picker-value\">{{ heatUnitOptions[heatUnitIndex] || '请选择' }}</text>\r\n\t\t\t\t\t\t<text class=\"iconfont icon-arrow-down\"></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</picker>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"form-label\">楼栋</text>\r\n\t\t\t\t<picker @change=\"handleBuildingChange\" :value=\"buildingIndex\" :range=\"buildingOptions\" class=\"form-picker\" :disabled=\"!heatUnitId\">\r\n\t\t\t\t\t<view class=\"picker-content\">\r\n\t\t\t\t\t\t<text class=\"picker-value\">{{ buildingOptions[buildingIndex] || '请选择' }}</text>\r\n\t\t\t\t\t\t<text class=\"iconfont icon-arrow-down\"></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</picker>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"form-label\">单元</text>\r\n\t\t\t\t<picker @change=\"handleUnitChange\" :value=\"unitIndex\" :range=\"unitOptions\" class=\"form-picker\" :disabled=\"!buildingId\">\r\n\t\t\t\t\t<view class=\"picker-content\">\r\n\t\t\t\t\t\t<text class=\"picker-value\">{{ unitOptions[unitIndex] || '请选择' }}</text>\r\n\t\t\t\t\t\t<text class=\"iconfont icon-arrow-down\"></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</picker>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"form-label\">户号</text>\r\n\t\t\t\t<input class=\"form-input\" type=\"text\" v-model=\"houseNumber\" placeholder=\"请输入户号，如：101\" />\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<button class=\"search-btn\" @click=\"searchValves\">搜索阀门</button>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 加载中 -->\r\n\t\t<view class=\"loading-container\" v-if=\"loading\">\r\n\t\t\t<uni-load-more :status=\"'loading'\" :content-text=\"loadingText\"></uni-load-more>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 没有阀门数据 -->\r\n\t\t<view class=\"empty-container\" v-else-if=\"valvesList.length === 0 && !loading\">\r\n\t\t\t<image src=\"/static/images/empty.png\" mode=\"aspectFit\" class=\"empty-image\"></image>\r\n\t\t\t<text class=\"empty-text\">未找到相关阀门</text>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 阀门列表 -->\r\n\t\t<view class=\"valves-list\" v-else>\r\n\t\t\t<view class=\"valve-card\" v-for=\"(valve, index) in valvesList\" :key=\"index\" @click=\"selectValve(valve)\">\r\n\t\t\t\t<view class=\"valve-header\">\r\n\t\t\t\t\t<text class=\"valve-name\">{{ valve.name }}</text>\r\n\t\t\t\t\t<view class=\"status-tag\" :class=\"'status-' + valve.status\">{{ getStatusText(valve.status) }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"valve-info\">\r\n\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t<text class=\"info-label\">开度</text>\r\n\t\t\t\t\t\t<text class=\"info-value\">{{ valve.openDegree || 0 }}%</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t<text class=\"info-label\">最后操作</text>\r\n\t\t\t\t\t\t<text class=\"info-value\">{{ valve.lastOperationTime || '无' }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tloading: false,\r\n\t\t\t\tloadingText: {\r\n\t\t\t\t\tcontentdown: '加载中...',\r\n\t\t\t\t\tcontentrefresh: '加载中...',\r\n\t\t\t\t\tcontentnomore: '没有更多数据'\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\t// 筛选条件\r\n\t\t\t\theatUnitOptions: ['东区小区', '西区小区', '南区小区'],\r\n\t\t\t\theatUnitIndex: 0,\r\n\t\t\t\theatUnitId: null,\r\n\t\t\t\t\r\n\t\t\t\tbuildingOptions: [],\r\n\t\t\t\tbuildingIndex: 0,\r\n\t\t\t\tbuildingId: null,\r\n\t\t\t\t\r\n\t\t\t\tunitOptions: [],\r\n\t\t\t\tunitIndex: 0,\r\n\t\t\t\tunitId: null,\r\n\t\t\t\t\r\n\t\t\t\thouseNumber: '',\r\n\t\t\t\t\r\n\t\t\t\t// 阀门数据\r\n\t\t\t\tvalvesList: [],\r\n\t\t\t\tcurrentValve: null,\r\n\t\t\t\t\r\n\t\t\t\t// 阀门控制\r\n\t\t\t\ttargetOpenDegree: 0\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.loadHeatUnits();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 加载小区列表\r\n\t\t\tloadHeatUnits() {\r\n\t\t\t\t// 实际应用中这里会调用API获取小区列表\r\n\t\t\t\t// 这里使用模拟数据\r\n\t\t\t\tthis.heatUnitOptions = ['东区小区', '西区小区', '南区小区'];\r\n\t\t\t\tthis.heatUnitIndex = 0;\r\n\t\t\t\tthis.heatUnitId = 1;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 小区选择变化\r\n\t\t\thandleHeatUnitChange(e) {\r\n\t\t\t\tthis.heatUnitIndex = e.detail.value;\r\n\t\t\t\tthis.heatUnitId = this.heatUnitIndex + 1; // 假设id从1开始\r\n\t\t\t\t\r\n\t\t\t\t// 重置楼栋和单元\r\n\t\t\t\tthis.buildingOptions = [];\r\n\t\t\t\tthis.buildingIndex = 0;\r\n\t\t\t\tthis.buildingId = null;\r\n\t\t\t\tthis.unitOptions = [];\r\n\t\t\t\tthis.unitIndex = 0;\r\n\t\t\t\tthis.unitId = null;\r\n\t\t\t\t\r\n\t\t\t\t// 加载该小区的楼栋列表\r\n\t\t\t\tthis.loadBuildings();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 加载楼栋列表\r\n\t\t\tloadBuildings() {\r\n\t\t\t\t// 实际应用中这里会调用API获取楼栋列表\r\n\t\t\t\t// 这里使用模拟数据\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.buildingOptions = ['1号楼', '2号楼', '3号楼'];\r\n\t\t\t\t\tthis.buildingIndex = 0;\r\n\t\t\t\t\tthis.buildingId = 1;\r\n\t\t\t\t}, 500);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 楼栋选择变化\r\n\t\t\thandleBuildingChange(e) {\r\n\t\t\t\tthis.buildingIndex = e.detail.value;\r\n\t\t\t\tthis.buildingId = this.buildingIndex + 1; // 假设id从1开始\r\n\t\t\t\t\r\n\t\t\t\t// 重置单元\r\n\t\t\t\tthis.unitOptions = [];\r\n\t\t\t\tthis.unitIndex = 0;\r\n\t\t\t\tthis.unitId = null;\r\n\t\t\t\t\r\n\t\t\t\t// 加载该楼栋的单元列表\r\n\t\t\t\tthis.loadUnits();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 加载单元列表\r\n\t\t\tloadUnits() {\r\n\t\t\t\t// 实际应用中这里会调用API获取单元列表\r\n\t\t\t\t// 这里使用模拟数据\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.unitOptions = ['1单元', '2单元', '3单元'];\r\n\t\t\t\t\tthis.unitIndex = 0;\r\n\t\t\t\t\tthis.unitId = 1;\r\n\t\t\t\t}, 500);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 单元选择变化\r\n\t\t\thandleUnitChange(e) {\r\n\t\t\t\tthis.unitIndex = e.detail.value;\r\n\t\t\t\tthis.unitId = this.unitIndex + 1; // 假设id从1开始\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 搜索阀门\r\n\t\t\tsearchValves() {\r\n\t\t\t\tthis.loading = true;\r\n\t\t\t\tthis.valvesList = [];\r\n\t\t\t\t\r\n\t\t\t\t// 构建请求参数\r\n\t\t\t\tconst params = {\r\n\t\t\t\t\theat_unit_id: this.heatUnitId\r\n\t\t\t\t};\r\n\t\t\t\t\r\n\t\t\t\tif (this.buildingId) {\r\n\t\t\t\t\tparams.building_id = this.buildingId;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (this.unitId) {\r\n\t\t\t\t\tparams.unit_id = this.unitId;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (this.houseNumber.trim()) {\r\n\t\t\t\t\tparams.house_number = this.houseNumber.trim();\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 模拟API调用\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t// 模拟数据\r\n\t\t\t\t\tthis.valvesList = [\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: 1,\r\n\t\t\t\t\t\t\tname: this.heatUnitOptions[this.heatUnitIndex] + ' ' + \r\n\t\t\t\t\t\t\t\t  this.buildingOptions[this.buildingIndex] + ' ' + \r\n\t\t\t\t\t\t\t\t  this.unitOptions[this.unitIndex] + ' ' + \r\n\t\t\t\t\t\t\t\t  (this.houseNumber || '101') + ' 入户阀门',\r\n\t\t\t\t\t\t\tstatus: 'open',\r\n\t\t\t\t\t\t\topenDegree: 80,\r\n\t\t\t\t\t\t\tlastOperationTime: '2025-04-03 14:35:20',\r\n\t\t\t\t\t\t\toperator: '系统',\r\n\t\t\t\t\t\t\toperationType: '自动开启(缴费成功)'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t];\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t}, 1000);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 选择阀门\r\n\t\t\tselectValve(valve) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/valves/detail?id=${valve.id}`\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取状态文本\r\n\t\t\tgetStatusText(status) {\r\n\t\t\t\tconst statusMap = {\r\n\t\t\t\t\t'open': '已开启',\r\n\t\t\t\t\t'closed': '已关闭',\r\n\t\t\t\t\t'error': '异常'\r\n\t\t\t\t};\r\n\t\t\t\treturn statusMap[status] || status;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 返回上一页\r\n\t\t\tnavigateBack() {\r\n\t\t\t\tuni.navigateBack();\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.valve-control-page {\r\n\tmin-height: 100vh;\r\n\tbackground-color: #f5f7fa;\r\n\tpadding-bottom: 30rpx;\r\n}\r\n\r\n.header {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\theight: 88rpx;\r\n\tbackground-color: #fff;\r\n\tpadding: 0 30rpx;\r\n\tposition: relative;\r\n\tbox-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);\r\n\t\r\n\t.back-button {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\t\r\n\t.page-title {\r\n\t\tflex: 1;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 34rpx;\r\n\t\tfont-weight: 500;\r\n\t\tpadding-right: 60rpx;\r\n\t}\r\n}\r\n\r\n.select-area {\r\n\tmargin: 20rpx 30rpx;\r\n\tbackground-color: #fff;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 20rpx 30rpx;\r\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\t\r\n\t.form-item {\r\n\t\tmargin-bottom: 20rpx;\r\n\t\t\r\n\t\t.form-label {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #333;\r\n\t\t\tmargin-bottom: 10rpx;\r\n\t\t\tdisplay: block;\r\n\t\t}\r\n\t\t\r\n\t\t.form-picker, .form-input {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 80rpx;\r\n\t\t\tborder: 1px solid #e5e5e5;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\tpadding: 0 20rpx;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tbackground-color: #f8f9fc;\r\n\t\t\t\r\n\t\t\t.picker-content {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\theight: 80rpx;\r\n\t\t\t\t\r\n\t\t\t\t.picker-value {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.form-input {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.search-btn {\r\n\t\twidth: 100%;\r\n\t\theight: 80rpx;\r\n\t\tline-height: 80rpx;\r\n\t\ttext-align: center;\r\n\t\tbackground-color: #1989fa;\r\n\t\tcolor: #fff;\r\n\t\tborder-radius: 8rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n}\r\n\r\n.loading-container, .empty-container {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 100rpx 0;\r\n\t\r\n\t.empty-image {\r\n\t\twidth: 200rpx;\r\n\t\theight: 200rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t\r\n\t.empty-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n}\r\n\r\n.valves-list {\r\n\tpadding: 0 30rpx;\r\n\t\r\n\t.valve-card {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 12rpx;\r\n\t\tpadding: 24rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\t\t\r\n\t\t.valve-header {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin-bottom: 16rpx;\r\n\t\t\t\r\n\t\t\t.valve-name {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.valve-info {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-wrap: wrap;\r\n\t\t\t\r\n\t\t\t.info-item {\r\n\t\t\t\twidth: 50%;\r\n\t\t\t\tmargin-bottom: 16rpx;\r\n\t\t\t\t\r\n\t\t\t\t.info-label {\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\tmargin-bottom: 4rpx;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.info-value {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.status-tag {\r\n\tpadding: 6rpx 16rpx;\r\n\tborder-radius: 30rpx;\r\n\tfont-size: 24rpx;\r\n\tcolor: #fff;\r\n\t\r\n\t&.status-open {\r\n\t\tbackground-color: #2ecc71;\r\n\t}\r\n\t\r\n\t&.status-closed {\r\n\t\tbackground-color: #95a5a6;\r\n\t}\r\n\t\r\n\t&.status-error {\r\n\t\tbackground-color: #e74c3c;\r\n\t}\r\n}\r\n</style> ", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/valves/control.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AA6EC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,SAAS;AAAA,MACT,aAAa;AAAA,QACZ,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,eAAe;AAAA,MACf;AAAA;AAAA,MAGD,iBAAiB,CAAC,QAAQ,QAAQ,MAAM;AAAA,MACxC,eAAe;AAAA,MACf,YAAY;AAAA,MAEZ,iBAAiB,CAAE;AAAA,MACnB,eAAe;AAAA,MACf,YAAY;AAAA,MAEZ,aAAa,CAAE;AAAA,MACf,WAAW;AAAA,MACX,QAAQ;AAAA,MAER,aAAa;AAAA;AAAA,MAGb,YAAY,CAAE;AAAA,MACd,cAAc;AAAA;AAAA,MAGd,kBAAkB;AAAA,IACnB;AAAA,EACA;AAAA,EACD,SAAS;AACR,SAAK,cAAa;AAAA,EAClB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,gBAAgB;AAGf,WAAK,kBAAkB,CAAC,QAAQ,QAAQ,MAAM;AAC9C,WAAK,gBAAgB;AACrB,WAAK,aAAa;AAAA,IAClB;AAAA;AAAA,IAGD,qBAAqB,GAAG;AACvB,WAAK,gBAAgB,EAAE,OAAO;AAC9B,WAAK,aAAa,KAAK,gBAAgB;AAGvC,WAAK,kBAAkB;AACvB,WAAK,gBAAgB;AACrB,WAAK,aAAa;AAClB,WAAK,cAAc;AACnB,WAAK,YAAY;AACjB,WAAK,SAAS;AAGd,WAAK,cAAa;AAAA,IAClB;AAAA;AAAA,IAGD,gBAAgB;AAGf,iBAAW,MAAM;AAChB,aAAK,kBAAkB,CAAC,OAAO,OAAO,KAAK;AAC3C,aAAK,gBAAgB;AACrB,aAAK,aAAa;AAAA,MAClB,GAAE,GAAG;AAAA,IACN;AAAA;AAAA,IAGD,qBAAqB,GAAG;AACvB,WAAK,gBAAgB,EAAE,OAAO;AAC9B,WAAK,aAAa,KAAK,gBAAgB;AAGvC,WAAK,cAAc;AACnB,WAAK,YAAY;AACjB,WAAK,SAAS;AAGd,WAAK,UAAS;AAAA,IACd;AAAA;AAAA,IAGD,YAAY;AAGX,iBAAW,MAAM;AAChB,aAAK,cAAc,CAAC,OAAO,OAAO,KAAK;AACvC,aAAK,YAAY;AACjB,aAAK,SAAS;AAAA,MACd,GAAE,GAAG;AAAA,IACN;AAAA;AAAA,IAGD,iBAAiB,GAAG;AACnB,WAAK,YAAY,EAAE,OAAO;AAC1B,WAAK,SAAS,KAAK,YAAY;AAAA,IAC/B;AAAA;AAAA,IAGD,eAAe;AACd,WAAK,UAAU;AACf,WAAK,aAAa;AAGH,OAAA;AAAA,QACd,cAAc,KAAK;AAAA,MACnB;AAED,UAAI,KAAK,YAAY;AACC,aAAK;AAAA,MAC3B;AAEA,UAAI,KAAK,QAAQ;AACC,aAAK;AAAA,MACvB;AAEA,UAAI,KAAK,YAAY,QAAQ;AACN,aAAK,YAAY;MACxC;AAGA,iBAAW,MAAM;AAEhB,aAAK,aAAa;AAAA,UACjB;AAAA,YACC,IAAI;AAAA,YACJ,MAAM,KAAK,gBAAgB,KAAK,aAAa,IAAI,MAC9C,KAAK,gBAAgB,KAAK,aAAa,IAAI,MAC3C,KAAK,YAAY,KAAK,SAAS,IAAI,OAClC,KAAK,eAAe,SAAS;AAAA,YACjC,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,mBAAmB;AAAA,YACnB,UAAU;AAAA,YACV,eAAe;AAAA,UAChB;AAAA;AAGD,aAAK,UAAU;AAAA,MACf,GAAE,GAAI;AAAA,IACP;AAAA;AAAA,IAGD,YAAY,OAAO;AAClBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,2BAA2B,MAAM,EAAE;AAAA,MACzC,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,cAAc,QAAQ;AACrB,YAAM,YAAY;AAAA,QACjB,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,SAAS;AAAA;AAEV,aAAO,UAAU,MAAM,KAAK;AAAA,IAC5B;AAAA;AAAA,IAGD,eAAe;AACdA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvPD,GAAG,WAAW,eAAe;"}