{"version": 3, "file": "permission-mixin.js", "sources": ["utils/permission-mixin.js"], "sourcesContent": ["import { guardPageAccess } from './auth.js';\r\n\r\n/**\r\n * 页面权限混入\r\n * 在页面组件中使用：\r\n * 1. 导入混入：import permissionMixin from '@/utils/permission-mixin.js';\r\n * 2. 在组件配置中混入：mixins: [permissionMixin]\r\n * 3. 在组件 data 中设置权限代码：permissionCode: 'your:permission:code'\r\n * \r\n * 如果用户无权访问，会自动重定向并阻止页面加载\r\n */\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 默认允许访问\r\n      permissionCode: '', \r\n      // 控制内容是否显示，用于避免权限检查过程中内容闪烁\r\n      canShowContent: false\r\n    };\r\n  },\r\n  \r\n  onLoad() {\r\n    // 检查是否设置了权限编码\r\n    if (this.permissionCode) {\r\n      // 检查权限\r\n      if (!guardPageAccess(this.permissionCode)) {\r\n        return; // 无权限访问，已重定向，停止加载\r\n      }\r\n    }\r\n    \r\n    // 有权限或无需权限，设置内容可见\r\n    this.canShowContent = true;\r\n    \r\n    // 如果存在原始onLoad方法，调用它\r\n    if (this.$options.originalOnLoad) {\r\n      this.$options.originalOnLoad.call(this);\r\n    }\r\n  },\r\n  \r\n  // 创建前保存原始onLoad方法\r\n  beforeCreate() {\r\n    // 保存原始的onLoad方法，以便在权限检查后调用\r\n    if (this.$options.onLoad && this.$options.onLoad !== this.$options.mixins[0].onLoad) {\r\n      this.$options.originalOnLoad = this.$options.onLoad;\r\n    }\r\n  }\r\n}; "], "names": ["guardPageAccess"], "mappings": ";;AAWA,MAAe,kBAAA;AAAA,EACb,OAAO;AACL,WAAO;AAAA;AAAA,MAEL,gBAAgB;AAAA;AAAA,MAEhB,gBAAgB;AAAA,IACtB;AAAA,EACG;AAAA,EAED,SAAS;AAEP,QAAI,KAAK,gBAAgB;AAEvB,UAAI,CAACA,WAAe,gBAAC,KAAK,cAAc,GAAG;AACzC;AAAA,MACD;AAAA,IACF;AAGD,SAAK,iBAAiB;AAGtB,QAAI,KAAK,SAAS,gBAAgB;AAChC,WAAK,SAAS,eAAe,KAAK,IAAI;AAAA,IACvC;AAAA,EACF;AAAA;AAAA,EAGD,eAAe;AAEb,QAAI,KAAK,SAAS,UAAU,KAAK,SAAS,WAAW,KAAK,SAAS,OAAO,CAAC,EAAE,QAAQ;AACnF,WAAK,SAAS,iBAAiB,KAAK,SAAS;AAAA,IAC9C;AAAA,EACF;AACH;;"}