/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.profile-container {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding-bottom: 40rpx;
}
.form-card {
  margin: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.05);
}
.form-card .form-item {
  padding: 24rpx 20rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}
.form-card .form-item:last-child {
  border-bottom: none;
}
.form-card .form-item .form-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}
.form-card .form-item .form-input input {
  height: 80rpx;
  font-size: 32rpx;
  color: #333;
  width: 100%;
}
.form-card .form-item .form-input .input-readonly {
  height: 80rpx;
  line-height: 80rpx;
  font-size: 32rpx;
  color: #999;
  display: block;
}
.submit-btn {
  margin: 60rpx 30rpx;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #1890ff;
  color: #fff;
  text-align: center;
  border-radius: 12rpx;
  font-size: 32rpx;
  box-shadow: 0 6rpx 20rpx rgba(24, 144, 255, 0.3);
  font-weight: bold;
}
.submit-btn:active {
  opacity: 0.9;
  transform: translateY(2rpx);
}