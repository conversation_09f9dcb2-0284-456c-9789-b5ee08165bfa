
package com.heating.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heating.dto.patrol.PatrolRecordSubmitRequest;
import com.heating.dto.patrol.PatrolRecordUpdateRequest;
import com.heating.service.DeviceService;
import com.heating.service.PatrolService;
import com.heating.dto.patrol.PatrolPlanCreateRequest;
import com.heating.dto.patrol.PatrolPlanCreateResponse;
import com.heating.dto.patrol.PatrolPlanListRequest;
import com.heating.dto.patrol.PatrolPlanListResponse;
import com.heating.dto.patrol.PatrolPlanDetailResponse;
import com.heating.dto.patrol.PatrolItemResponse;
import com.heating.dto.patrol.PatrolMessageResponse;
import com.heating.repository.PatrolPlanRepository;
import com.heating.repository.PatrolItemRepository;
import com.heating.repository.PatrolRecordRepository;
import com.heating.repository.PatrolResultRepository;
import com.heating.repository.UserRepository;
import com.heating.repository.DeviceRepository;
import com.heating.repository.TDevicePatrolItemRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import com.heating.dto.patrol.PatrolResultUpdateRequest;
import com.heating.dto.patrol.PatrolStatsResponse;
import com.heating.entity.patrol.TPatrolItem;
import com.heating.entity.patrol.TPatrolPlan;
import com.heating.entity.patrol.TPatrolRecord;
import com.heating.entity.patrol.TPatrolResult;
import com.heating.entity.user.TUser;
import com.heating.entity.user.SystemRole;
import com.heating.entity.device.TDevice;
import com.heating.dto.patrol.PatrolItemDictionaryResponse;
import com.heating.dto.patrol.PatrolCategoryResponse;
import com.heating.entity.PatrolItemDictionary;
import com.heating.entity.PatrolCategory;
import com.heating.entity.TDevicePatrolItem;
import com.heating.repository.PatrolItemDictionaryRepository;
import com.heating.repository.PatrolCategoryRepository;
import com.heating.dto.patrol.PatrolExecutorResponse;
import com.heating.dto.patrol.PatrolRecordListRequest;
import com.heating.dto.patrol.PatrolRecordListResponse;
import com.heating.dto.patrol.PatrolResultDetailResponse;
import com.heating.dto.patrol.PatrolResultDetailFullResponse;
import lombok.extern.slf4j.Slf4j;

import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.DayOfWeek;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.HashMap;
import java.util.stream.Collectors;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.time.Duration;

import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;

@Service
@Slf4j
public class PatrolServiceImpl implements PatrolService {

    @Autowired
    private PatrolPlanRepository patrolPlanRepository;

    @Autowired
    private PatrolItemRepository patrolItemRepository;
    
    @Autowired
    private DeviceService deviceService;

    @Autowired
    private PatrolRecordRepository patrolRecordRepository;

    @Autowired
    private PatrolResultRepository patrolResultRepository;
    
    @Autowired
    private UserRepository userRepository;

    @Autowired
    private DeviceRepository deviceRepository;
    
    @Autowired
    private EntityManager entityManager;

    @Autowired
    private PatrolItemDictionaryRepository patrolItemDictionaryRepository;
    
    @Autowired
    private PatrolCategoryRepository patrolCategoryRepository;
    
    @Autowired
    private TDevicePatrolItemRepository devicePatrolItemRepository;

    @Autowired
    private ObjectMapper objectMapper; // 自动注入 Jackson 的 ObjectMapper

    @Override
    @Transactional
    public PatrolPlanCreateResponse createPatrolPlan(PatrolPlanCreateRequest request) {
        // 1. 参数校验
        validateRequest(request);
        
        // 2. 创建巡检计划
        TPatrolPlan plan = new TPatrolPlan();
        //自动生成planNo
        String planNo = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        plan.setPlanNo(planNo);
        plan.setName(request.getName());
        plan.setPatrolType(request.getPatrolType());
        plan.setStartDate(request.getStartDate());
        plan.setEndDate(request.getEndDate());
        plan.setExecutorIds(request.getExecutorIds());
        plan.setScheduleType(request.getScheduleType());
        plan.setScheduleInterval(request.getScheduleInterval());
        plan.setScheduleWeekDays(request.getScheduleWeekDays());
        plan.setScheduleMonthDays(request.getScheduleMonthDays());
        plan.setDeviceIds(request.getDeviceIds());
        plan.setLocations(request.getLocations());
        plan.setHeatUnitId(request.getHeatUnitId());

        // 获取当前日期
        LocalDate today = LocalDate.now();
        
        // 判断开始日期是否小于等于当前日期
        boolean startDateIsToday = plan.getStartDate().compareTo(today) <= 0;
        
        // 如果开始日期小于等于当前日期，检查是否需要立即执行
        if (startDateIsToday) {
            // 设置状态为执行中
            plan.setStatus("processing");
        } else {
            // 否则设置状态为待执行
            plan.setStatus("pending");
        }
        
        plan.setCreateTime(LocalDateTime.now());
        plan.setUpdateTime(LocalDateTime.now());    
        
        plan = patrolPlanRepository.save(plan);

        // 3. 创建巡检项目
        List<TPatrolItem> items = new ArrayList<>(); 
        for (PatrolPlanCreateRequest.PatrolDeviceItem deviceItem : request.getPatrolItem()) {
            TPatrolItem item = new TPatrolItem();
            item.setPatrolPlanId(plan.getId()); 
            // 设置设备巡检项目ID，而不是直接设置itemDictionaryId
            if (deviceItem.getDevicePatrolItemId() != null) {
                item.setDevicePatrolItemId(deviceItem.getDevicePatrolItemId());
                // 不需要直接设置itemDictionaryId字段，这是通过关联表获取的
            }
            item.setCreateTime(LocalDateTime.now());    
            item.setUpdateTime(LocalDateTime.now());
            items.add(item);
        }
        
        patrolItemRepository.saveAll(items);
        
        // 4. 如果开始日期小于等于当前日期，检查是否需要立即生成巡检记录
        if (startDateIsToday) {
            boolean needsExecution = checkIfNeedsExecution(plan, today);
            
            if (needsExecution) {
                // 检查今天是否已经生成了执行记录
                boolean hasRecordToday = patrolRecordRepository.existsByPatrolPlanIdAndExecutionDate(plan.getId(), today);
                
                if (!hasRecordToday) {
                    // 创建巡检记录
                    createPatrolRecordForPlan(plan, today);
                    log.info("巡检计划[{}]({})创建时已生成今日巡检记录", plan.getId(), plan.getName());
                }
            }
        }
        
        // 5. 构建响应
        PatrolPlanCreateResponse response = new PatrolPlanCreateResponse();
        response.setPlanNo(planNo);
        response.setMessage("巡检计划创建成功");
        
        return response;
    }
    
    /**
     * 检查巡检计划在指定日期是否需要执行
     * 
     * @param plan 巡检计划
     * @param date 指定日期
     * @return 是否需要执行
     */
    private boolean checkIfNeedsExecution(TPatrolPlan plan, LocalDate date) {
        // 获取指定日期是周几（1-7，周一到周日）
        int dayOfWeek = date.getDayOfWeek().getValue();
        
        // 获取指定日期是本月第几天（1-31）
        int dayOfMonth = date.getDayOfMonth();
        
        // 根据调度类型检查是否需要执行
        switch (plan.getScheduleType()) {
            case "daily":
                // 每天执行
                return true;
                
            case "weekly":
                // 检查是否是计划的周执行日
                if (plan.getScheduleWeekDays() != null && plan.getScheduleWeekDays().contains(dayOfWeek)) {
                    return true;
                }
                break;
                
            case "monthly":
                // 检查是否是计划的月执行日
                if (plan.getScheduleMonthDays() != null && plan.getScheduleMonthDays().contains(dayOfMonth)) {
                    return true;
                }
                break;
                
            case "custom":
                // 自定义间隔，第一次创建时应该执行
                return true;
        }
        
        return false;
    }
    
    /**
     * 为巡检计划创建巡检记录
     * 
     * @param plan 巡检计划
     * @param executionDate 执行日期
     */
    private void createPatrolRecordForPlan(TPatrolPlan plan, LocalDate executionDate) {
        log.info("为计划[{}]({})创建巡检记录", plan.getId(), plan.getName());
        
        // 获取执行人ID列表
        List<Object> executorIdObjects = plan.getExecutorIds();
        if (executorIdObjects == null || executorIdObjects.isEmpty()) {
            log.warn("计划[{}]({})没有指定执行人，无法创建巡检记录", plan.getId(), plan.getName());
            return;
        }
        
        // 获取第一个执行人ID
        Object firstExecutorObj = executorIdObjects.get(0);
        Long executorId;
        
        // 处理可能的类型转换
        if (firstExecutorObj instanceof Integer) {
            executorId = ((Integer) firstExecutorObj).longValue();
        } else if (firstExecutorObj instanceof Long) {
            executorId = (Long) firstExecutorObj;
        } else {
            // 尝试解析为Long
            try {
                executorId = Long.parseLong(firstExecutorObj.toString());
            } catch (Exception e) {
                log.error("无法解析执行人ID: {}", firstExecutorObj, e);
                return;
            }
        }
        
        // 创建巡检记录
        TPatrolRecord record = new TPatrolRecord();
        record.setPatrolPlanId(plan.getId().intValue());
        record.setExecutorId(executorId);
        record.setExecutionDate(executionDate);
        LocalDate today = LocalDate.now();
        // 设置开始时间为当天 00:00:00
        LocalDateTime startOfDay = today.atStartOfDay();
        // 设置结束时间为当天 23:59:59
        LocalDateTime endOfDay = today.atTime(23, 59, 59, 999_999_999);

        record.setStartTime(startOfDay); // 设置为当前时间，实际执行时会更新
        record.setEndTime(endOfDay);   // 设置为当前时间，实际执行时会更新
        record.setStatus("pending");              // 初始状态为待执行
        record.setRemark("系统自动生成");
        record.setCreateTime(LocalDateTime.now());
        record.setUpdateTime(LocalDateTime.now());
        
        // 保存巡检记录
        patrolRecordRepository.save(record);
        
        log.info("巡检记录创建成功，记录ID: {}", record.getId());
    }

    private void validateRequest(PatrolPlanCreateRequest request) {
        if (request.getName() == null || request.getName().trim().isEmpty()) {
            throw new IllegalArgumentException("计划名称不能为空");
        }
        
        if (request.getScheduleType() == null) {
            throw new IllegalArgumentException("调度类型不能为空");
        }
        
        if (request.getStartDate() == null) {
            throw new IllegalArgumentException("开始日期不能为空");
        }
        
        if (request.getDeviceIds() == null || request.getDeviceIds().isEmpty()) {
            throw new IllegalArgumentException("巡检设备不能为空");
        } 
    }

    @Override
    @Transactional
    @CacheEvict(value = "patrolResultDetails", allEntries = true)
    public boolean submitPatrolRecord(PatrolRecordSubmitRequest request) {
        // 1. 参数校验
        validatePatrolRecordRequest(request);
        
        // 2. 查找并更新巡检记录，而不是创建新记录
        TPatrolRecord record = patrolRecordRepository.findById(request.getId())
                .orElseThrow(() -> new IllegalArgumentException("巡检记录不存在，ID: " + request.getId()));
        if(record.getStatus().equals("overdue"))
        {
            record.setIsLateEntry(1);
        }
        // 更新记录信息
        record.setExecutorId(request.getExecutorId());
        record.setStartTime(request.getStartTime());
        record.setEndTime(request.getEndTime());
        record.setStatus(request.getStatus());
        record.setRemark(request.getRemark());
        record.setUpdateTime(LocalDateTime.now());
        record.setExecutionDate(request.getStartTime().toLocalDate());
        
        // 保存更新后的记录
        patrolRecordRepository.save(record);
        
        
        // 3. 保存新的巡检结果
        List<TPatrolResult> results = new ArrayList<>();
        for (PatrolRecordSubmitRequest.PatrolItemResult item : request.getPatrolResults()) {
            TPatrolResult result = new TPatrolResult();
            result.setPatrolRecordId(record.getId());
            result.setPatrolItemId(item.getPatrolItemId());
            result.setCheckResult(item.getCheckResult());
            result.setParamValue(item.getParamValue());
            result.setDescription(item.getDescription());
            result.setImages(item.getImages());
            result.setLatitude(item.getLatitude());
            result.setLongitude(item.getLongitude());
            result.setCreateTime(LocalDateTime.now());
            result.setUpdateTime(LocalDateTime.now());  
            results.add(result);
        }
        
        patrolResultRepository.saveAll(results);

        return true;
    }
    
    private void validatePatrolRecordRequest(PatrolRecordSubmitRequest request) {
        if (request.getId() == 0) {
            throw new IllegalArgumentException("巡检记录ID不能为空");
        }
        
        if (request.getExecutorId() == null) {
            throw new IllegalArgumentException("执行人ID不能为空");
        }
        
        if (request.getStartTime() == null) {
            throw new IllegalArgumentException("开始时间不能为空");
        }
        
        if (request.getPatrolResults() == null || request.getPatrolResults().isEmpty()) {
            throw new IllegalArgumentException("巡检项目结果不能为空");
        }
        
        // 验证状态是否合法
        List<String> validStatus = Arrays.asList("pending", "processing", "completed");
        if (!validStatus.contains(request.getStatus())) {
            throw new IllegalArgumentException("无效的状态值");
        }
    }

    @Override
    @Transactional
    @CacheEvict(value = "patrolResultDetails", allEntries = true)
    public boolean updatePatrolRecord(Long recordId, PatrolRecordUpdateRequest request) {
        // 1. 参数校验
        validatePatrolRecordUpdateRequest(request);
        // 2. 查询并更新巡检记录
        TPatrolRecord record = patrolRecordRepository.findById(recordId)
                .orElseThrow(() -> new IllegalArgumentException("巡检记录不存在"));

        // 更新基本信息
        if (request.getEndTime() != null) {
            record.setEndTime(request.getEndTime());
        }
        if (request.getStatus() != null) {
            record.setStatus(request.getStatus());
        }
        if (request.getRemark() != null) {
            record.setRemark(request.getRemark());
        }
        record.setUpdateTime(LocalDateTime.now());
        patrolRecordRepository.save(record);
        return true;
    }


    private void validatePatrolRecordUpdateRequest(PatrolRecordUpdateRequest request) {
        if (request.getStatus() != null) {
            // 验证状态是否合法
            List<String> validStatus = Arrays.asList("pending", "processing", "completed");
            if (!validStatus.contains(request.getStatus())) {
                throw new IllegalArgumentException("无效的状态值");
            }
        }
    }

    @Override
    @Transactional
    @CacheEvict(value = "patrolResultDetails", allEntries = true)
    public boolean updatePatrolResult(Long resultId, PatrolResultUpdateRequest request) {
        // 1. 参数校验
        validatePatrolResultUpdateRequest(request);
        // 2. 查询并更新巡检结果
        TPatrolResult result = patrolResultRepository.findById(resultId)
                .orElseThrow(() -> new IllegalArgumentException("巡检结果不存在"));     

        // 更新基本信息
        if (request.getCheckResult() != null) {
            result.setCheckResult(request.getCheckResult());
        }
        if (request.getParamValue() != null) {
            result.setParamValue(request.getParamValue());
        }
        if (request.getDescription() != null) {
            result.setDescription(request.getDescription());
        }   
        if (request.getImages() != null) {
            result.setImages(request.getImages());
        } 
        result.setUpdateTime(LocalDateTime.now()); 
        patrolResultRepository.save(result);
                
        return true;
    }   

    private void validatePatrolResultUpdateRequest(PatrolResultUpdateRequest request) {
        if (request.getCheckResult() != null) {
          
        }
    }   

    @Override
    public PatrolStatsResponse getPatrolStats() {
        // 获取总记录数
        long total = patrolRecordRepository.count();
        
        // 获取已完成记录数
        long completed = patrolRecordRepository.countByStatus("completed");
        
        // 获取待处理记录数
        long pending = patrolRecordRepository.countByStatus("pending");
        
        return PatrolStatsResponse.builder()
                .total((int)total)
                .completed((int)completed)
                .pending((int)pending)
                .build();
    }

    /**
     * 获取巡检计划列表，支持按名称、状态、类型、时间、项目权限筛选，并支持分页
     * @param request 巡检计划查询条件
     * @return 巡检计划列表
     */
    @Override
    public Map<String, Object> getPatrolPlanList(PatrolPlanListRequest request) {
        try {
            int page = (request != null && request.getPage() != null) ? request.getPage(): 1;
            int pageSize = (request != null && request.getPageSize() != null) ? request.getPageSize() : 10;

            LocalDate[] dateRange = parseSearchDate(request.getSearchDate());
            LocalDate dateStart = (dateRange != null) ? dateRange[0] : null;
            LocalDate dateEnd = (dateRange != null) ? dateRange[1] : null;
            // 计算偏移量
            int offset = (page - 1) * pageSize;

            // 处理项目权限参数：若传递"0"则表示全部权限，否则按逗号分隔的项目ID进行过滤
            String heatUnitId = (request != null) ? request.getHeatUnitId() : null;
            log.info("巡检计划查询 - 项目权限参数: {}", heatUnitId);

            // 获取分页数据
            List<TPatrolPlan> plans = patrolPlanRepository.findPatrolPlans(
                    request.getName(),
                    request.getStatus(),
                    request.getPatrolType(),
                    heatUnitId,
                    dateStart,
                    dateEnd,
                    offset,
                    pageSize);

            // 获取总记录数
            long total = patrolPlanRepository.countPatrolPlans(
                    request.getName(),
                    request.getStatus(),
                    request.getPatrolType(),
                    heatUnitId,
                    dateStart,
                    dateEnd);;

            // 获取当前日期，用于判断isPlay
            LocalDate currentDate = LocalDate.now();
            // 获取当前是周几（1-7，周一到周日）
            int currentDayOfWeek = currentDate.getDayOfWeek().getValue();
            // 获取当前是几号
            int currentDayOfMonth = currentDate.getDayOfMonth();
            
            // 转换为响应对象
            List<PatrolPlanListResponse> responseList = new ArrayList<>();
            for (TPatrolPlan plan : plans) {
                PatrolPlanListResponse response = convertToListResponse(plan);
                
                // 判断isPlay
                boolean isPlay = false;
                
                // 首先判断当前日期是否在计划的开始和结束日期范围内
                boolean isInDateRange = (currentDate.isEqual(plan.getStartDate()) || currentDate.isAfter(plan.getStartDate())) 
                    && (plan.getEndDate() == null || currentDate.isEqual(plan.getEndDate()) || currentDate.isBefore(plan.getEndDate()));
                
                if (isInDateRange) {
                    switch (plan.getScheduleType()) {
                        case "daily":
                            // 每日执行，只要在日期范围内就为true
                            isPlay = true;
                            break;
                            
                        case "weekly":
                            // 判断当前周几是否在计划的执行周天内
                            if (plan.getScheduleWeekDays() != null) {
                                isPlay = plan.getScheduleWeekDays().contains(currentDayOfWeek);
                            }
                            break;
                            
                        case "monthly":
                            // 判断当前日期是否在计划的执行月天内
                            if (plan.getScheduleMonthDays() != null) {
                                isPlay = plan.getScheduleMonthDays().contains(String.valueOf(currentDayOfMonth));
                            }
                            break;
                            
                        case "custom":
                            // 自定义间隔的处理逻辑保持不变
                            if (plan.getScheduleInterval() != null) {
                                // 获取最近一次执行记录
                                Optional<TPatrolRecord> lastRecord = patrolRecordRepository
                                        .findTopByPatrolPlanIdOrderByExecutionDateDesc(plan.getId());
                                
                                if (lastRecord.isPresent()) {
                                    LocalDate lastExecutionDate = lastRecord.get().getExecutionDate();
                                    long daysSinceLastExecution = java.time.temporal.ChronoUnit.DAYS.between(lastExecutionDate, currentDate);
                                    isPlay = daysSinceLastExecution >= plan.getScheduleInterval();
                                } else {
                                    // 没有执行记录，应该执行
                                    isPlay = true;
                                }
                            }
                            break;
                    }
                }
                
                response.setIsPlay(isPlay);
                responseList.add(response);
            }
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("list", responseList);
            result.put("total", total);
            result.put("page", page);
            result.put("pageSize", pageSize);
            result.put("totalPages", (total + pageSize - 1) / pageSize);

            return result;
            
        } catch (Exception e) {
            // 记录错误日志
            log.error("获取巡检计划列表失败: " + e.getMessage(), e);
            throw new RuntimeException("获取巡检计划列表失败", e);
        }
    }
    
    private PatrolPlanListResponse convertToListResponse(TPatrolPlan plan) {
        PatrolPlanListResponse response = new PatrolPlanListResponse();
        response.setId(plan.getId());
        response.setName(plan.getName());
        response.setPlanNo(plan.getPlanNo());
        response.setPatrolType(plan.getPatrolType());
        response.setStartDate(plan.getStartDate());
        response.setEndDate(plan.getEndDate());
        response.setLocations(plan.getLocations());
        response.setStatus(plan.getStatus());
        response.setScheduleType(plan.getScheduleType());
        // 获取执行人姓名列表
        List<String> executorNames = new ArrayList<>();
        response.setExecutors(executorNames);
        
        return response;
    }

    /*
    * 根据传递过来的值判断查询的日期范围
    * @param searchDate 巡检计划时间查询条件
    * @return LocalDate[] 时间范围
    * */
    private LocalDate[] parseSearchDate(String searchDate) {
        if (searchDate == null || "all".equalsIgnoreCase(searchDate)) {
            return null; // 表示不加时间条件
        }

        LocalDate now = LocalDate.now();
        LocalDate start = null;
        LocalDate end = null;

        switch (searchDate.toLowerCase()) {
            case "today":
                start = now;
                end = now;
                break;

            case "this_week":
                start = now.with(DayOfWeek.MONDAY);
                end = now.with(DayOfWeek.SUNDAY);
                break;

            case "this_month":
                start = now.withDayOfMonth(1);
                end = now.withDayOfMonth(now.lengthOfMonth());
                break;

            case "last_month":
                LocalDate firstDayOfLastMonth = now.minusMonths(1).withDayOfMonth(1);
                start = firstDayOfLastMonth;
                end = firstDayOfLastMonth.plusMonths(1).minusDays(1);
                break;

            default:
                try {
                    // 如果是格式如 "yyyy-MM-dd" 的日期，只设置 start 和 end 都为这天
                    LocalDate customDate = LocalDate.parse(searchDate);
                    start = customDate;
                    end = customDate;
                } catch (DateTimeParseException e) {
                    // 默认用今天
                    start = now;
                    end = now;
                }
                break;
        }

        return new LocalDate[]{start, end};
    }
    /**
     * 从PatrolItem获取设备信息和字典信息
     * 需要在处理之前填充字段
     * @param items 巡检项目列表
     */
    private void enrichPatrolItems(List<TPatrolItem> items) {
        for (TPatrolItem item : items) {
            try {
                // 通过devicePatrolItemId获取设备ID和巡检项目字典ID
                Long devicePatrolItemId = item.getDevicePatrolItemId();
                if (devicePatrolItemId != null) {
                    // 设置设备ID
                    Long deviceId = getDeviceIdFromPatrolItem(devicePatrolItemId);
                    item.setDeviceId(deviceId);
                    
                    // 设置巡检项目字典ID
                    Long itemDictionaryId = getItemDictionaryIdFromPatrolItem(devicePatrolItemId);
                    item.setItemDictionaryId(itemDictionaryId);
                }
            } catch (Exception e) {
                log.error("填充巡检项目信息失败: {}", e.getMessage());
            }
        }
    }

    /**
     * 获取巡检计划详情，包括基本信息和巡检项目列表
     * @param planId 巡检计划ID
     * @return 巡检计划详情
     */
    @Override
    public PatrolPlanDetailResponse getPatrolPlanDetail(Long planId) {
        if (planId == null) {
            throw new IllegalArgumentException("巡检计划ID不能为空");
        }
        
        // 查询巡检计划基本信息
        TPatrolPlan plan = patrolPlanRepository.findById(planId)
                .orElseThrow(() -> new IllegalArgumentException("巡检计划不存在"));
        
        PatrolPlanDetailResponse response = new PatrolPlanDetailResponse();

        // 调试信息
        log.info("获取巡检计划详情: {}", plan);
        
        // 设置基本信息
        response.setId(plan.getId());
        response.setPlanNo(plan.getPlanNo());
        response.setName(plan.getName());
        response.setPatrolType(plan.getPatrolType());
        response.setStartDate(plan.getStartDate());
        response.setEndDate(plan.getEndDate());
        
        // 正确处理executorIds，因为可能存在Integer类型的值
        List<Object> executorIdObjects = new ArrayList<>();
        if (plan.getExecutorIds() != null) {
            // 转换plan.getExecutorIds()中的元素为合适的Object类型
            for (Object obj : plan.getExecutorIds()) {
                executorIdObjects.add(obj);
            }
        }
        response.setExecutorIds(executorIdObjects);
        
        response.setScheduleType(plan.getScheduleType());
        response.setScheduleInterval(plan.getScheduleInterval());
        response.setScheduleWeekDays(plan.getScheduleWeekDays());
        response.setScheduleMonthDays(plan.getScheduleMonthDays());
        response.setDeviceIds(plan.getDeviceIds());
        response.setLocations(plan.getLocations());
        response.setStatus(plan.getStatus());
        
        // 获取执行人员姓名列表
        List<String> executorNames = new ArrayList<>();
        if (plan.getExecutorIds() != null && !plan.getExecutorIds().isEmpty()) {
            // 处理executorIds，这是从数据库JSON字段读取的，可能是Integer或Long
            for (Object executorIdObj : plan.getExecutorIds()) {
                try {
                    Long executorId;
                    // 处理可能存在的Integer到Long的转换
                    if (executorIdObj instanceof Integer) {
                        executorId = ((Integer) executorIdObj).longValue();
                    } else if (executorIdObj instanceof Long) {
                        executorId = (Long) executorIdObj;
                    } else if (executorIdObj instanceof Number) {
                        executorId = ((Number) executorIdObj).longValue();
                    } else {
                        // 如果是字符串，尝试解析为Long
                        executorId = Long.parseLong(executorIdObj.toString());
                    }
                    
                    Optional<TUser> userOpt = userRepository.findById(executorId);
                    userOpt.ifPresent(user -> executorNames.add(user.getName()));
                } catch (Exception e) {
                    // 记录异常但继续处理其他ID
                    log.error("Error processing executor ID: " + executorIdObj + ", " + e.getMessage());
                }
            }
        }
        response.setExecutorNames(executorNames);
        
        // 查询关联的巡检项目
        List<TPatrolItem> items = patrolItemRepository.findByPatrolPlanId(planId);
        
        // 填充设备ID和巡检项目字典ID
        enrichPatrolItems(items);
        
        List<PatrolPlanDetailResponse.PatrolItemDetail> itemDetails = new ArrayList<>();
        
        for (TPatrolItem item : items) 
        {
            PatrolPlanDetailResponse.PatrolItemDetail detail = new PatrolPlanDetailResponse.PatrolItemDetail();
            detail.setId(item.getId());
            
            // 设置设备ID和设备名称
            Long deviceId = item.getDeviceId();
            
            if (deviceId != null) {
                try {
                    Optional<TDevice> deviceOpt = deviceRepository.findById(deviceId);
                    if (deviceOpt.isPresent()) {
                        detail.setDeviceName(deviceOpt.get().getName());
                    } else {
                        detail.setDeviceName("未知设备");
                    }
                } catch (Exception e) {
                    log.error("获取设备名称失败: {}", e.getMessage());
                    detail.setDeviceName("获取设备信息出错");
                }
            } else {
                detail.setDeviceName("未指定设备");
            }

            // 设置巡检项目字典信息
            Long itemDictionaryId = item.getItemDictionaryId();
            
            if (itemDictionaryId != null) {
                try {
                    // 从巡检项目字典表中获取详细信息
                    Optional<PatrolItemDictionary> itemDictionaryOpt = patrolItemDictionaryRepository.findById(itemDictionaryId);
                    itemDictionaryOpt.ifPresent(itemDictionary -> {
                        detail.setItemName(itemDictionary.getItemName());
                        detail.setCategoryName(itemDictionary.getCategoryName());
                        detail.setParamType(itemDictionary.getParamType());
                        detail.setUnit(itemDictionary.getUnit()); 
                        
                        // 获取正常范围信息
                        // 需要从t_device_patrol_item表中查询正常范围
                        String normalRange = getNormalRangeFromDevicePatrolItem(deviceId, itemDictionaryId);
                        detail.setNormalRange(normalRange);
                        
                        detail.setCheckMethod(itemDictionary.getCheckMethod());
                        detail.setImportance(itemDictionary.getImportance());
                        detail.setDescription(itemDictionary.getDescription());
                    });
                } catch (Exception e) {
                    log.error("获取巡检项目字典信息失败: {}", e.getMessage());
                    detail.setItemName("获取项目信息出错");
                }
            } else {
                // 设置默认值
                detail.setItemName("未知项目");
                detail.setCategoryName("未知类别");
            }
            
            itemDetails.add(detail);
        }
        
        response.setPatrolItems(itemDetails);
        
        return response;
    }
    
    /**
     * 从t_device_patrol_item表中获取正常范围
     * 
     * @param deviceId 设备ID
     * @param patrolItemDictId 巡检项目字典ID
     * @return 正常范围值
     */
    private String getNormalRangeFromDevicePatrolItem(Long deviceId, Long patrolItemDictId) {
        if (deviceId == null || patrolItemDictId == null) {
            return "未设置范围";
        }
        
        try {
            // 使用Repository查询设备巡检项表中的正常范围
            String normalRange = devicePatrolItemRepository.findNormalRangeByDeviceIdAndPatrolItemDictId(deviceId, patrolItemDictId);
            
            if (normalRange != null && !normalRange.isEmpty()) {
                return normalRange;
            }
            
            // 如果找不到记录或范围为空，返回默认值
            return "未设置范围";
        } catch (Exception e) {
            log.error("查询巡检项目正常范围失败: " + e.getMessage(), e);
            return "查询范围失败";
        }
    }

    /**
     * 从设备巡检项中获取设备ID
     * 
     * @param devicePatrolItemId 设备巡检项ID
     * @return 设备ID
     */
    private Long getDeviceIdFromPatrolItem(Long devicePatrolItemId) {
        if (devicePatrolItemId == null) {
            return null;
        }
        
        try {
            // 从t_device_patrol_item表中查询设备ID
            Optional<TDevicePatrolItem> devicePatrolItem = devicePatrolItemRepository.findById(devicePatrolItemId);
            if (devicePatrolItem.isPresent()) {
                return devicePatrolItem.get().getDeviceId();
            }
            
            log.warn("未找到设备巡检项: {}", devicePatrolItemId);
            return null;
        } catch (Exception e) {
            log.error("获取设备ID失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 从设备巡检项中获取巡检项目字典ID
     * 
     * @param devicePatrolItemId 设备巡检项ID
     * @return 巡检项目字典ID
     */
    private Long getItemDictionaryIdFromPatrolItem(Long devicePatrolItemId) {
        if (devicePatrolItemId == null) {
            return null;
        }
        
        try {
            // 从t_device_patrol_item表中查询巡检项目字典ID
            Optional<TDevicePatrolItem> devicePatrolItem = devicePatrolItemRepository.findById(devicePatrolItemId);
            if (devicePatrolItem.isPresent()) {
                return devicePatrolItem.get().getPatrolItemDictId();
            }
            
            log.warn("未找到设备巡检项: {}", devicePatrolItemId);
            return null;
        } catch (Exception e) {
            log.error("获取巡检项目字典ID失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据巡检计划ID获取巡检项目列表
     * @param planId 巡检计划ID
     * @return 巡检项目列表
     */
    @Override
    @Cacheable(value = "patrolItems", key = "#planId", unless = "#result == null")
    public List<PatrolItemResponse> getPatrolItems(Long planId) {
        // 验证planId是否为空
        if (planId == null) {
            throw new IllegalArgumentException("巡检计划ID不能为空");
        }
        
        log.info("从数据库获取巡检项目列表, planId: {}", planId);
        
        // 验证巡检计划是否存在
        if (!patrolPlanRepository.existsById(planId)) {
            throw new IllegalArgumentException("巡检计划不存在");
        }
        
        // 查询关联的巡检项目
        List<TPatrolItem> items = patrolItemRepository.findByPatrolPlanId(planId);
        
        // 填充设备ID和巡检项目字典ID
        enrichPatrolItems(items);
        
        List<PatrolItemResponse> itemResponses = new ArrayList<>(); 
        for (TPatrolItem item : items) 
        {
            PatrolItemResponse response = new PatrolItemResponse();
            response.setId(item.getId());
            response.setDeviceId(item.getDeviceId()); 
            response.setItemDictionaryId(item.getItemDictionaryId());

            // 获取设备名称
            try 
            {
                Optional<TDevice> deviceOpt = deviceRepository.findById(item.getDeviceId());
                if (deviceOpt.isPresent()) {
                    response.setDeviceName(deviceOpt.get().getName());
                } else {
                    response.setDeviceName("未知设备");
                }  
            } catch (Exception e) {
                // 如果获取设备名称失败，设置默认值，避免整个接口报错
                response.setDeviceId(-1L);
                response.setDeviceName("获取设备信息出错");
            }

            // 从巡检项目字典表中获取详细信息
            Optional<PatrolItemDictionary> itemDictionaryOpt = patrolItemDictionaryRepository.findById(item.getItemDictionaryId());
            itemDictionaryOpt.ifPresent(itemDictionary -> {
                response.setItemCode(itemDictionary.getItemCode());
                response.setItemName(itemDictionary.getItemName());
                response.setCategoryName(itemDictionary.getCategoryName());
                response.setParamType(itemDictionary.getParamType());
                response.setUnit(itemDictionary.getUnit()); 
                response.setCheckMethod(itemDictionary.getCheckMethod());
                response.setImportance(itemDictionary.getImportance());
                response.setDescription(itemDictionary.getDescription());
            }); 
            itemResponses.add(response);
        }
        
        return itemResponses;
    }
 
    /**
     * 获取巡检项目字典列表
     * @param categoryId 可选的类别ID参数，为null时获取全部
     * @return 巡检项目字典列表
     */
    @Override
    public List<PatrolItemDictionaryResponse> getPatrolItemDictionary(Long categoryId) {
        List<PatrolItemDictionary> itemDictionaries;
        
        if (categoryId != null) {
            // 按类别ID查询
            itemDictionaries = patrolItemDictionaryRepository.findByCategoryIdAndIsActive(categoryId, true);
        } else {
            // 查询所有启用的巡检项目
            itemDictionaries = patrolItemDictionaryRepository.findByIsActive(true);
        }
        
        // 转换为DTO返回
        return itemDictionaries.stream()
                .map(this::convertToPatrolItemDictionaryResponse)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取巡检项目类别列表
     * @return 巡检项目类别列表
     */
    @Override
    public List<PatrolCategoryResponse> getPatrolCategories() {
        // 查询所有启用的类别，并按排序号排序
        List<PatrolCategory> categories = patrolCategoryRepository.findAllByOrderBySortOrderAsc();
        
        // 过滤只返回启用的类别
        categories = categories.stream()
                .filter(category -> category.getIsActive() != null && category.getIsActive())
                .collect(Collectors.toList());
        
        // 转换为DTO返回
        return categories.stream()
                .map(this::convertToPatrolCategoryResponse)
                .collect(Collectors.toList());
    }
    
    /**
     * 将实体转换为DTO
     */
    private PatrolItemDictionaryResponse convertToPatrolItemDictionaryResponse(PatrolItemDictionary entity) {
        PatrolItemDictionaryResponse dto = new PatrolItemDictionaryResponse();
        dto.setId(entity.getId());
        dto.setItemCode(entity.getItemCode());
        dto.setItemName(entity.getItemName());
        dto.setCategoryId(entity.getCategoryId());
        dto.setCategoryName(entity.getCategoryName());
        dto.setParamType(entity.getParamType());
        dto.setUnit(entity.getUnit()); 
        dto.setCheckMethod(entity.getCheckMethod());
        dto.setImportance(entity.getImportance());
        dto.setDescription(entity.getDescription());
        dto.setIsActive(entity.getIsActive());
        return dto;
    }
    
    /**
     * 将实体转换为DTO
     */
    private PatrolCategoryResponse convertToPatrolCategoryResponse(PatrolCategory entity) {
        PatrolCategoryResponse dto = new PatrolCategoryResponse();
        dto.setId(entity.getId());
        dto.setCategoryCode(entity.getCategoryCode());
        dto.setCategoryName(entity.getCategoryName());
        dto.setParentId(entity.getParentId());
        dto.setDescription(entity.getDescription());
        dto.setSortOrder(entity.getSortOrder());
        dto.setIsActive(entity.getIsActive());
        return dto;
    }

    @Override
    public Map<String, Object>  getPatrolRecordList(PatrolRecordListRequest request) {
        log.info("获取巡检记录列表: {}", request);
        int page = (request != null && request.getPage() != null) ? request.getPage(): 1;
        int pageSize = (request != null && request.getPageSize() != null) ? request.getPageSize() : 10;

        // 处理项目小区权限参数：若传递"0"则表示全部权限，否则按逗号分隔的项目ID进行过滤
        String heatUnitId = (request != null) ? request.getHeatUnitId() : null;
        log.info("巡检记录查询 - 项目权限参数: {}", heatUnitId);

        List<Integer> heatUnitIds = null;
        if (heatUnitId != null && !heatUnitId.trim().isEmpty()) {
            // 检查是否包含"0"，如果包含则表示全部权限
            if (heatUnitId.contains("0")) {
                log.info("项目权限包含0，获取全部项目的巡检记录");
                heatUnitIds = null; // null表示不进行项目权限过滤
            } else {
                // 解析项目ID列表
                try {
                    heatUnitIds = Arrays.stream(heatUnitId.split(","))
                            .map(String::trim)
                            .filter(s -> !s.isEmpty())
                            .map(Integer::parseInt)
                            .collect(Collectors.toList());
                    log.info("项目权限过滤，允许的项目ID: {}", heatUnitIds);
                } catch (NumberFormatException e) {
                    log.warn("项目权限参数格式错误: {}, 将不进行项目权限过滤", heatUnitId);
                    heatUnitIds = null;
                }
            }
        } else {
            log.info("未提供项目权限参数，将不进行项目权限过滤");
        }

        // 如果未提供executorId，返回空结果
        if (request.getExecutorId() == null) {
            log.info("未提供执行人ID，返回空结果");
            Map<String, Object> emptyResult = new HashMap<>();
            emptyResult.put("list", new ArrayList<>());
            emptyResult.put("total", 0);
            emptyResult.put("page", page);
            emptyResult.put("pageSize", pageSize);
            emptyResult.put("totalPages", 0);
            return emptyResult;
        }

        log.info("根据执行人ID和项目权限过滤巡检记录: executorId={}, heatUnitIds={}", request.getExecutorId(), heatUnitIds);

        // 构建分页和排序条件
        Pageable pageable = PageRequest.of(
            request.getPage() - 1,
            request.getPageSize(),
            Sort.by(Sort.Direction.DESC, "createTime")
        );

        // 根据是否有项目权限过滤选择不同的查询方法
        long total;
        Page<TPatrolRecord> recordsPage;

        if (heatUnitIds != null) {
            // 有项目权限过滤
            total = patrolRecordRepository.countPatrolRecordsWithHeatUnit(
                request.getStatus(),
                request.getExecutorId(),
                request.getStartDate(),
                request.getEndDate(),
                heatUnitIds);
            recordsPage = patrolRecordRepository.findByConditionsWithHeatUnit(
                request.getStatus(),
                request.getExecutorId(),
                request.getStartDate(),
                request.getEndDate(),
                heatUnitIds,
                pageable
            );
        } else {
            // 无项目权限过滤
            total = patrolRecordRepository.countPatrolRecords(
                request.getStatus(),
                request.getExecutorId(),
                request.getStartDate(),
                request.getEndDate());
            recordsPage = patrolRecordRepository.findByConditions(
                request.getStatus(),
                request.getExecutorId(),
                request.getStartDate(),
                request.getEndDate(),
                pageable
            );
        }
        
        // 转换查询结果为响应对象
        List<PatrolRecordListResponse> responseList = new ArrayList<>();
        recordsPage.getContent().forEach(record -> {
            PatrolRecordListResponse response = new PatrolRecordListResponse();
            response.setId(record.getId());
            response.setPatrolPlanId(record.getPatrolPlanId());
            response.setExecutorId(record.getExecutorId());
            response.setStartTime(record.getStartTime());
            response.setEndTime(record.getEndTime());
            response.setStatus(record.getStatus());
            response.setRemark(record.getRemark());
            response.setCreateTime(record.getCreateTime());
            response.setUpdateTime(record.getUpdateTime());
            response.setExecutionDate(record.getExecutionDate());
            
            // 获取执行人员信息
            userRepository.findById(record.getExecutorId()).ifPresent(user -> {
                response.setExecutorName(user.getName());
            });
            
            // 获取巡检计划信息
            patrolPlanRepository.findById((long) record.getPatrolPlanId()).ifPresent(plan -> {
                response.setPlanName(plan.getName());
                response.setPatrolType(plan.getPatrolType());
            });
            
            responseList.add(response);
        });
        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("list", responseList);
        result.put("total", total);
        result.put("page", page);
        result.put("pageSize", pageSize);
        result.put("totalPages", (total + pageSize - 1) / pageSize);
        return result;
    }

    @Override
    public List<PatrolResultDetailResponse> getPatrolResultDetail(Long recordId) {
        log.info("获取巡检结果详情, recordId: {}", recordId);
        
        // 验证巡检记录是否存在
//        TPatrolRecord record = patrolRecordRepository.findById(recordId)
//            .orElseThrow(() -> new IllegalArgumentException("巡检记录不存在: " + recordId));
        
        // 获取巡检结果列表
        List<TPatrolResult> resultList = patrolResultRepository.findResultsWithItemsByRecordId(recordId);
        
        // 转换为响应对象
        List<PatrolResultDetailResponse> responseList = new ArrayList<>();
        
        for (TPatrolResult result : resultList) {
            PatrolResultDetailResponse response = new PatrolResultDetailResponse();
            response.setId(result.getId());
            response.setPatrolItemId(result.getPatrolItemId());
            response.setCheckResult(result.getCheckResult());
            response.setParamValue(result.getParamValue());
            response.setDescription(result.getDescription());
            response.setImages(result.getImages());
            response.setLatitude(result.getLatitude());
            response.setLongitude(result.getLongitude());
            response.setCreateTime(result.getCreateTime() != null ? result.getCreateTime().toString() : "");
            response.setUpdateTime(result.getUpdateTime() != null ? result.getUpdateTime().toString() : "");
            
            // 获取巡检项目信息
            patrolItemRepository.findById(result.getPatrolItemId()).ifPresent(item -> {
                // 填充设备ID和巡检项目字典ID
                Long devicePatrolItemId = item.getDevicePatrolItemId();
                if (devicePatrolItemId != null) {
                    // 设置设备ID
                    Long deviceId = getDeviceIdFromPatrolItem(devicePatrolItemId);
                    item.setDeviceId(deviceId);

                    // 设置巡检项目字典ID
                    Long itemDictionaryId = getItemDictionaryIdFromPatrolItem(devicePatrolItemId);
                    item.setItemDictionaryId(itemDictionaryId);
                    
                    // 获取巡检项目字典信息
                    patrolItemDictionaryRepository.findById(itemDictionaryId).ifPresent(dictionary -> {
                        response.setItemName(dictionary.getItemName());
                        response.setParamType(dictionary.getParamType());
                        response.setUnit(dictionary.getUnit());
                        response.setCheckMethod(dictionary.getCheckMethod());
                        response.setCheckDescription(dictionary.getDescription());
                        // 直接使用字典里的类别名称
                        response.setCategoryName(dictionary.getCategoryName());
                    });
                    
                    // 获取设备信息
                    response.setDeviceId(deviceId);
                    deviceRepository.findById(deviceId).ifPresent(device -> {
                        response.setDeviceName(device.getName());
                        response.setDeviceType(device.getType());
                    });
                }
            });
            
            responseList.add(response);
        }
        
        return responseList;
    }

    /**
     * 获取优化版的巡检结果详情（减少数据库查询）
     * 使用单个JOIN查询获取所有关联数据，避免N+1查询问题
     * 
     * @param recordId 巡检记录ID
     * @return 巡检结果详情列表
     */
    private List<PatrolResultDetailResponse> getOptimizedPatrolResultDetail(Long recordId) {
        log.info("开始获取优化版巡检结果详情, recordId: {}", recordId);
        long startTime = System.currentTimeMillis();
        
        try {
            // 使用优化的JOIN查询获取所有关联数据
            List<Object[]> resultDataList = patrolResultRepository.findAllResultDataByRecordId(recordId);
            
            log.info("查询结果数量: {}", resultDataList != null ? resultDataList.size() : 0);
            
            if (resultDataList == null || resultDataList.isEmpty()) {
                log.warn("未找到巡检结果数据, recordId: {}", recordId);
                return new ArrayList<>();
            }
            
            List<PatrolResultDetailResponse> responseList = new ArrayList<>(resultDataList.size());
            
            for (int rowIndex = 0; rowIndex < resultDataList.size(); rowIndex++) {
                try {
                    Object[] row = resultDataList.get(rowIndex);
                    PatrolResultDetailResponse response = new PatrolResultDetailResponse();
                    int i = 0;
                    
                    // 记录数据类型日志（仅对第一行记录进行此操作，以减少日志量）
                    if (rowIndex == 0) {
                        StringBuilder types = new StringBuilder("数据类型: ");
                        for (Object obj : row) {
                            types.append(obj != null ? obj.getClass().getSimpleName() : "null").append(", ");
                        }
                        log.debug(types.toString());
                    }
                    
                    // 设置巡检结果字段 - 按SQL查询中的顺序填充
                    if (row[i] != null) {
                        try {
                            response.setId(((Number)row[i]).longValue());
                        } catch (Exception e) {
                            log.error("转换ID字段失败: {} ({})", row[i], row[i].getClass().getName(), e);
                            response.setId(null);
                        }
                    } else {
                        response.setId(null);
                    }
                    i++;
                    
                    // patrol_record_id at index 1, skip it
                    i++;
                    
                    if (row[i] != null) {
                        try {
                            response.setPatrolItemId(((Number)row[i]).longValue());
                        } catch (Exception e) {
                            log.error("转换PatrolItemId字段失败: {} ({})", row[i], row[i].getClass().getName(), e);
                            response.setPatrolItemId(null);
                        }
                    } else {
                        response.setPatrolItemId(null);
                    }
                    i++;
                    
                    response.setCheckResult((String)row[i++]);
                    response.setParamValue((String)row[i++]);
                    response.setDescription((String)row[i++]);
                    
                    // 处理图片数组
                    String imagesStr = (String)row[i++];
                    if (imagesStr != null && !imagesStr.isEmpty()) {
                        try {
                            List<String> imagesList = objectMapper.readValue(imagesStr, new TypeReference<List<String>>() {});
                            response.setImages(imagesList);
                        } catch (Exception e) {
                            log.error("解析图片数据失败: {}", e.getMessage());
                            response.setImages(new ArrayList<>());
                        }
                    } else {
                        response.setImages(new ArrayList<>());
                    }
                    
                    // 继续设置其他字段
                    if (row[i] != null) {
                        try {
                            response.setLatitude(((Number)row[i]).doubleValue());
                        } catch (Exception e) {
                            log.error("转换Latitude字段失败: {} ({})", row[i], row[i].getClass().getName(), e);
                            response.setLatitude(null);
                        }
                    } else {
                        response.setLatitude(null);
                    }
                    i++;
                    
                    if (row[i] != null) {
                        try {
                            response.setLongitude(((Number)row[i]).doubleValue());
                        } catch (Exception e) {
                            log.error("转换Longitude字段失败: {} ({})", row[i], row[i].getClass().getName(), e);
                            response.setLongitude(null);
                        }
                    } else {
                        response.setLongitude(null);
                    }
                    i++;
                    
                    // 处理日期时间
                    Object createTimeObj = row[i++];
                    response.setCreateTime(createTimeObj != null ? createTimeObj.toString() : "");
                    Object updateTimeObj = row[i++];
                    response.setUpdateTime(updateTimeObj != null ? updateTimeObj.toString() : "");
                    
                    // 跳过device_patrol_item_id，不需要在响应中
                    i++;
                    
                    // 设置字典相关字段
                    response.setItemName((String)row[i++]);
                    response.setParamType((String)row[i++]);
                    response.setUnit((String)row[i++]);
                    response.setCheckMethod((String)row[i++]);
                    response.setCheckDescription((String)row[i++]);
                    response.setCategoryName((String)row[i++]);
                    
                    // 设置设备相关字段
                    if (row[i] != null) {
                        try {
                            response.setDeviceId(((Number)row[i]).longValue());
                        } catch (Exception e) {
                            log.error("转换DeviceId字段失败: {} ({})", row[i], row[i].getClass().getName(), e);
                            response.setDeviceId(null);
                        }
                    } else {
                        response.setDeviceId(null);
                    }
                    i++;
                    
                    response.setDeviceName((String)row[i++]);
                    response.setDeviceType((String)row[i++]);
                    
                    responseList.add(response);
                } catch (Exception e) {
                    log.error("处理巡检结果行数据时出错, 行索引: {}", rowIndex, e);
                }
            }
            
            long endTime = System.currentTimeMillis();
            log.info("优化版巡检结果详情获取完成, 条目数: {}, 耗时: {}ms", responseList.size(), endTime - startTime);
            
            return responseList;
        } catch (Exception e) {
            log.error("获取优化版巡检结果详情失败", e);
            // 出错时返回空列表，而不是抛出异常
            return new ArrayList<>();
        }
    }

    /**
     * 获取巡检结果详情的完整数据（优化性能）
     * 使用缓存提高访问速度
     * 
     * @param recordId 巡检记录ID
     * @return 完整的巡检结果详情响应
     */
    @Override
    @Cacheable(value = "patrolResultDetails", key = "#recordId", unless = "#result == null")
    public PatrolResultDetailFullResponse getPatrolResultDetailFull(Long recordId) {
        log.info("开始获取巡检结果详情（优化版）, recordId: {}", recordId);
        long startTime = System.currentTimeMillis();
        
        try {
            // 创建返回对象
            PatrolResultDetailFullResponse fullResponse = new PatrolResultDetailFullResponse();
            
            // 1. 获取巡检记录信息 - 使用单次查询同时获取关联数据
            TPatrolRecord record = patrolRecordRepository.findById(recordId)
                    .orElseThrow(() -> new IllegalArgumentException("巡检记录不存在"));
                    
            // 2. 预加载关联数据
            // 获取执行人信息
            TUser executor = null;
            if (record.getExecutorId() != null) {
                executor = userRepository.findById(record.getExecutorId()).orElse(null);
            }
            
            // 获取巡检计划信息
            TPatrolPlan plan = null;
            if (record.getPatrolPlanId() != 0) {
                plan = patrolPlanRepository.findById((long)record.getPatrolPlanId()).orElse(null);
            }
            
            // 3. 设置记录信息
            PatrolResultDetailFullResponse.RecordInfo recordInfo = new PatrolResultDetailFullResponse.RecordInfo();
            recordInfo.setId(record.getId());
            recordInfo.setPatrolPlanId(record.getPatrolPlanId());
            recordInfo.setPlanName(plan != null ? plan.getName() : "");
            recordInfo.setExecutionDate(record.getExecutionDate() != null ? record.getExecutionDate().toString() : "");
            recordInfo.setStartTime(record.getStartTime() != null ? record.getStartTime().toString() : "");
            recordInfo.setEndTime(record.getEndTime() != null ? record.getEndTime().toString() : "");
            recordInfo.setStatus(record.getStatus());
            recordInfo.setExecutorId(record.getExecutorId());
            recordInfo.setIsLateEntry(record.getIsLateEntry());
            recordInfo.setExecutorName(executor != null ? executor.getName() : "");
            recordInfo.setExecutorPhone(executor != null ? executor.getPhone() : "");
            recordInfo.setExecutorAvatar(executor != null ? executor.getAvatar() : "");
            recordInfo.setRemark(record.getRemark());
            recordInfo.setLocations(plan != null ? plan.getLocations() : "");
            recordInfo.setCreateTime(record.getCreateTime() != null ? record.getCreateTime().toString() : "");
            recordInfo.setUpdateTime(record.getUpdateTime() != null ? record.getUpdateTime().toString() : "");
            fullResponse.setRecordInfo(recordInfo);
            
            // 4. 获取巡检结果或任务列表
            List<PatrolResultDetailResponse> resultDetails;
            
            if ("pending".equals(record.getStatus()) || "overdue".equals(record.getStatus())) {
                // 对于待执行或已超时状态，获取巡检任务信息
                if (plan != null) {
                    // 使用缓存的查询结果
                    List<PatrolItemResponse> patrolItems = this.getPatrolItems(plan.getId());
                    resultDetails = convertPatrolItemsToResultDetails(patrolItems, recordId);
                } else {
                    resultDetails = new ArrayList<>();
                }
            } else {
                // 已完成状态，优先使用优化版方法获取实际巡检结果
                try {
                    resultDetails = getOptimizedPatrolResultDetail(recordId);
                    
                    // 如果优化版方法返回空列表，但应该有数据，则使用备选方法
                    if (resultDetails.isEmpty()) {
                        log.warn("优化版查询返回空结果，尝试使用简单查询方法获取巡检结果");
                        resultDetails = getSimplePatrolResultDetail(recordId);
                        
                        // 如果简单查询也返回空列表，则使用原始方法
                        if (resultDetails.isEmpty()) {
                            log.warn("简单查询返回空结果，尝试使用原始方法获取巡检结果");
                            resultDetails = getPatrolResultDetail(recordId);
                        }
                    }
                } catch (Exception e) {
                    log.error("优化版方法获取巡检结果失败，尝试使用简单查询方法", e);
                    try {
                        resultDetails = getSimplePatrolResultDetail(recordId);
                        
                        // 如果简单查询返回空列表，则使用原始方法
                        if (resultDetails.isEmpty()) {
                            log.warn("简单查询返回空结果，尝试使用原始方法获取巡检结果");
                            resultDetails = getPatrolResultDetail(recordId);
                        }
                    } catch (Exception e2) {
                        log.error("简单查询方法也失败，使用原始方法作为最后备选", e2);
                        resultDetails = getPatrolResultDetail(recordId);
                    }
                }
            }
            
            // 设置结果列表
            fullResponse.setResultList(resultDetails);
            
            // 5. 设置统计信息
            PatrolResultDetailFullResponse.Summary summary = new PatrolResultDetailFullResponse.Summary();
            summary.setTotalItems(resultDetails.size());
            
            // 根据状态计算正常/异常数量
            if ("pending".equals(record.getStatus()) || "overdue".equals(record.getStatus())) {
                summary.setNormalCount(0);
                summary.setAbnormalCount(0);
            } else {
                // 优化：使用循环计数而不是流操作，可能更高效
                int normalCount = 0;
                int abnormalCount = 0;
                for (PatrolResultDetailResponse result : resultDetails) {
                    if ("normal".equals(result.getCheckResult())) {
                        normalCount++;
                    } else if ("abnormal".equals(result.getCheckResult())) {
                        abnormalCount++;
                    }
                }
                summary.setNormalCount(normalCount);
                summary.setAbnormalCount(abnormalCount);
            }
            
            // 根据状态设置完成率
            if ("completed".equals(record.getStatus())) {
                summary.setCompletionRate("100%");
            } else if ("pending".equals(record.getStatus()) || "overdue".equals(record.getStatus())) {
                summary.setCompletionRate("0%");
            } else {
                summary.setCompletionRate("-");
            }
            
            // 计算巡检持续时间
            String duration = "-";
            if (record.getStartTime() != null && record.getEndTime() != null) {
                Duration timeDuration = Duration.between(record.getStartTime(), record.getEndTime());
                long hours = timeDuration.toHours();
                long minutes = timeDuration.toMinutesPart();
                duration = hours + "小时" + minutes + "分钟";
            }
            summary.setDuration(duration);
            fullResponse.setSummary(summary);
            
            // 6. 添加巡检计划信息
            if (plan != null) {
                Map<String, Object> planInfo = new HashMap<>();
                planInfo.put("id", plan.getId());
                planInfo.put("planNo", plan.getPlanNo());
                planInfo.put("name", plan.getName());
                planInfo.put("patrolType", plan.getPatrolType());
                planInfo.put("startDate", plan.getStartDate());
                planInfo.put("endDate", plan.getEndDate());
                planInfo.put("scheduleType", plan.getScheduleType());
                planInfo.put("scheduleInterval", plan.getScheduleInterval());
                planInfo.put("scheduleWeekDays", plan.getScheduleWeekDays());
                planInfo.put("scheduleMonthDays", plan.getScheduleMonthDays());
                planInfo.put("status", plan.getStatus());
                planInfo.put("locations", plan.getLocations());
                fullResponse.setPlanInfo(planInfo);
            }
            
            long endTime = System.currentTimeMillis();
            log.info("获取巡检结果详情完成（优化版），耗时: {}ms", endTime - startTime);
            return fullResponse;
        } catch (Exception e) {
            log.error("获取巡检结果详情失败", e);
            // 出现异常时返回一个基本的响应对象，避免前端收到null
            PatrolResultDetailFullResponse errorResponse = new PatrolResultDetailFullResponse();
            
            // 设置基本记录信息
            PatrolResultDetailFullResponse.RecordInfo recordInfo = new PatrolResultDetailFullResponse.RecordInfo();
            recordInfo.setId(recordId);
            errorResponse.setRecordInfo(recordInfo);
            
            // 设置空的结果列表
            errorResponse.setResultList(new ArrayList<>());
            
            // 设置基本统计信息
            PatrolResultDetailFullResponse.Summary summary = new PatrolResultDetailFullResponse.Summary();
            summary.setTotalItems(0);
            summary.setNormalCount(0);
            summary.setAbnormalCount(0);
            summary.setCompletionRate("-");
            summary.setDuration("-");
            errorResponse.setSummary(summary);
            
            return errorResponse;
        }
    }
    
    /**
     * 将巡检任务项目列表转换为巡检结果详情格式
     * @param patrolItems 巡检任务项目列表
     * @param recordId 巡检记录ID
     * @return 巡检结果详情列表
     */
    private List<PatrolResultDetailResponse> convertPatrolItemsToResultDetails(
            List<PatrolItemResponse> patrolItems, Long recordId) {
        List<PatrolResultDetailResponse> resultDetails = new ArrayList<>(patrolItems.size());
        
        for (PatrolItemResponse item : patrolItems) {
            PatrolResultDetailResponse detail = new PatrolResultDetailResponse();
            
            // 设置ID为null，表示尚未有结果
            detail.setId(null);
            
            // 设置巡检项目相关信息
            detail.setPatrolItemId(item.getId());
            detail.setItemName(item.getItemName());
            
            // 设置设备相关信息
            detail.setDeviceId(item.getDeviceId());
            detail.setDeviceName(item.getDeviceName());
            detail.setDeviceType(null); // 不确定PatrolItemResponse中是否有此字段
            
            // 设置类别和参数相关信息
            detail.setCategoryName(item.getCategoryName());
            detail.setCheckMethod(item.getCheckMethod());
            detail.setParamType(item.getParamType());
            detail.setUnit(item.getUnit());
            
            // 设置结果相关字段为null或默认值，表示尚未有结果
            detail.setCheckResult(null);
            detail.setParamValue(null);
            detail.setDescription(item.getDescription());
            detail.setCheckDescription(null);
            detail.setImages(new ArrayList<>());
            detail.setLatitude(null);
            detail.setLongitude(null);
            detail.setCreateTime(null);
            detail.setUpdateTime(null);
            
            resultDetails.add(detail);
        }
        
        return resultDetails;
    }

    /**
     * 获取巡检计划执行人列表
     * 
     * @param planId 巡检计划ID
     * @return 执行人列表
     */
    @Override
    public List<PatrolExecutorResponse> getPatrolExecutors(Long planId) {
        log.info("获取巡检计划执行人列表: planId={}", planId);
        
        try {
             // 根据planId获取巡检计划
             TPatrolPlan plan = patrolPlanRepository.findById(planId)
                .orElseThrow(() -> new IllegalArgumentException("巡检计划不存在: " + planId));  
            
            // 根据巡检计划获取计划执行人
            List<Object> executorIds = plan.getExecutorIds();

            // 根据执行人ID获取执行人信息
            List<Long> executorIdList = new ArrayList<>();
            for (Object executorIdObj : executorIds) {
                if (executorIdObj instanceof Long) {
                    executorIdList.add((Long) executorIdObj);
                } else if (executorIdObj instanceof Integer) {
                    executorIdList.add(((Integer) executorIdObj).longValue());  
                }
            } 
            // 根据执行人ID获取执行人信息
            List<TUser> executors = userRepository.findAllById(executorIdList);

            // 根据执行人信息获取执行人列表
            List<PatrolExecutorResponse> executorResponses = executors.stream()
                .map(executor -> new PatrolExecutorResponse(
                    executor.getId(),
                    executor.getName(),
                    executor.getRole(), 
                    executor.getDepartment()
                ))
                .collect(Collectors.toList());

            return executorResponses; 
                
        } catch (Exception e) {
            log.error("获取巡检计划执行人列表失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取巡检消息
     * 获取巡检记录表中所有的待执行和已超时的巡检记录
     * 
     * @param userId 用户ID
     * @param role 用户角色，如果是'admin'或'manage'，则获取所有记录，否则根据userId过滤
     * @return 巡检消息列表
     */
    @Override
    public List<PatrolMessageResponse> getPatrolMessages(Long userId, String role) {
        log.info("获取巡检消息开始: userId={}, role={}", userId, role);
        List<PatrolMessageResponse> messages = new ArrayList<>();
        
        // 处理多角色情况（角色可能是逗号分隔的字符串）
        boolean hasAdminRole = false;
        if (role != null && !role.isEmpty()) {
            String[] roles = role.split(",");
            for (String r : roles) {
                String roleTrim = r.trim();
                if ("admin".equals(roleTrim) || "manage".equals(roleTrim)) {
                    hasAdminRole = true;
                    break;
                }
            }
        }
        
        // 获取所有状态为"pending"(待执行)和"overdue"(已超时)的巡检记录
        List<TPatrolRecord> pendingRecords;
        
        if (hasAdminRole) {
            // 管理员角色获取所有记录
            pendingRecords = patrolRecordRepository.findByStatusIn(Arrays.asList("pending", "overdue"));
            log.info("管理员角色，查询到{}个待执行或已超时的巡检记录", pendingRecords.size());
        } else if (userId != null) {
            // 非管理员角色，根据用户ID过滤
            pendingRecords = patrolRecordRepository.findByStatusInAndExecutorId(
                Arrays.asList("pending", "overdue"), userId);
            log.info("非管理员角色，查询到{}个用户{}的待执行或已超时巡检记录", pendingRecords.size(), userId);
        } else {
            // 非管理员角色且未提供用户ID，返回空列表
            log.info("非管理员角色且未提供用户ID，返回空列表");
            return Collections.emptyList();
        }
        
        // 记录已处理的计划ID，避免同一计划生成多条消息
        Set<Long> processedPlanIds = new HashSet<>();
        
        for (TPatrolRecord record : pendingRecords) {
            // 获取巡检计划ID
            Long planId = Long.valueOf(record.getPatrolPlanId());
            
            // 如果该计划已经处理过，跳过
//            if (processedPlanIds.contains(planId)) {
//                continue;
//            }
            
            // 获取巡检计划信息
            Optional<TPatrolPlan> planOpt = patrolPlanRepository.findById(planId);
            if (!planOpt.isPresent()) {
                log.warn("巡检记录[{}]对应的巡检计划[{}]不存在", record.getId(), planId);
                continue;
            }
            
            TPatrolPlan plan = planOpt.get();
            
            // 获取执行人ID列表
            List<Long> executorIds = new ArrayList<>();
            if (plan.getExecutorIds() != null) {
                for (Object executorIdObj : plan.getExecutorIds()) {
                    if (executorIdObj instanceof Integer) {
                        executorIds.add(((Integer) executorIdObj).longValue());
                    } else if (executorIdObj instanceof Long) {
                        executorIds.add((Long) executorIdObj);
                    } else {
                        try {
                            executorIds.add(Long.parseLong(executorIdObj.toString()));
                        } catch (Exception e) {
                            log.warn("无法解析执行人ID: {}", executorIdObj);
                        }
                    }
                }
            }
            
            // 如果没有执行人，则使用记录中的执行人ID
            if (executorIds.isEmpty() && record.getExecutorId() != null) {
                executorIds.add(record.getExecutorId());
            }
            
            // 创建巡检消息
            PatrolMessageResponse message = PatrolMessageResponse.builder()
                    .id(record.getId())
                    .name(plan.getName())
                    .executorIds(executorIds)
                    .build();
            
            messages.add(message);
            log.info("添加巡检消息: 计划ID: {}, 名称: {}, 记录ID: {}, 状态: {}", 
                    planId, plan.getName(), record.getId(), record.getStatus());
            
            // 标记该计划已处理
            processedPlanIds.add(planId);
        }
        
        log.info("获取巡检消息完成，共{}条消息", messages.size());
        return messages;
    }

    /**
     * 使用简单查询获取巡检结果详情
     * 当优化版方法失败时作为备选方案使用
     * 
     * @param recordId 巡检记录ID
     * @return 巡检结果详情列表
     */
    private List<PatrolResultDetailResponse> getSimplePatrolResultDetail(Long recordId) {
        log.info("使用简单查询获取巡检结果详情, recordId: {}", recordId);
        
        try {
            // 使用简单查询获取巡检结果数据
            List<TPatrolResult> resultList = patrolResultRepository.findSimpleResultsByRecordId(recordId);
            
            if (resultList == null || resultList.isEmpty()) {
                log.warn("未找到巡检结果数据(简单查询), recordId: {}", recordId);
                return new ArrayList<>();
            }
            
            List<PatrolResultDetailResponse> responseList = new ArrayList<>(resultList.size());
            
            for (TPatrolResult result : resultList) {
                PatrolResultDetailResponse response = new PatrolResultDetailResponse();
                
                // 设置基本字段
                response.setId(result.getId());
                response.setPatrolItemId(result.getPatrolItemId());
                response.setCheckResult(result.getCheckResult());
                response.setParamValue(result.getParamValue());
                response.setDescription(result.getDescription());
                response.setImages(result.getImages());
                response.setLatitude(result.getLatitude());
                response.setLongitude(result.getLongitude());
                response.setCreateTime(result.getCreateTime() != null ? result.getCreateTime().toString() : "");
                response.setUpdateTime(result.getUpdateTime() != null ? result.getUpdateTime().toString() : "");
                
                // 尝试获取巡检项目和设备信息
                try {
                    patrolItemRepository.findById(result.getPatrolItemId()).ifPresent(item -> {
                        Long devicePatrolItemId = item.getDevicePatrolItemId();
                        if (devicePatrolItemId != null) {
                            // 获取设备ID
                            Long deviceId = getDeviceIdFromPatrolItem(devicePatrolItemId);
                            
                            // 获取设备信息
                            if (deviceId != null) {
                                response.setDeviceId(deviceId);
                                deviceRepository.findById(deviceId).ifPresent(device -> {
                                    response.setDeviceName(device.getName());
                                    response.setDeviceType(device.getType());
                                });
                            }
                            
                            // 获取巡检项目字典ID
                            Long itemDictionaryId = getItemDictionaryIdFromPatrolItem(devicePatrolItemId);
                            if (itemDictionaryId != null) {
                                patrolItemDictionaryRepository.findById(itemDictionaryId).ifPresent(dictionary -> {
                                    response.setItemName(dictionary.getItemName());
                                    response.setParamType(dictionary.getParamType());
                                    response.setUnit(dictionary.getUnit());
                                    response.setCheckMethod(dictionary.getCheckMethod());
                                    response.setCheckDescription(dictionary.getDescription());
                                    response.setCategoryName(dictionary.getCategoryName());
                                });
                            }
                        }
                    });
                } catch (Exception e) {
                    log.error("获取巡检项目关联信息失败: {}", e.getMessage());
                    // 设置默认值
                    if (response.getItemName() == null) response.setItemName("未知项目");
                    if (response.getDeviceName() == null) response.setDeviceName("未知设备");
                }
                
                responseList.add(response);
            }
            
            return responseList;
        } catch (Exception e) {
            log.error("简单查询获取巡检结果详情失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取有限数量的巡检工单信息
     * 根据用户ID和角色进行权限控制，返回指定数量的巡检工单
     * 
     * @param userId 用户ID
     * @param role 用户角色，如果是'admin'或'manage'，则获取所有记录，否则根据userId过滤
     * @param limit 限制返回的记录数量
     * @return 巡检工单列表
     */
    @Override
    public List<PatrolRecordListResponse> getLimitedPatrolRecords(Long userId, String role, Integer limit) {
        log.info("获取有限数量的巡检工单信息: userId={}, role={}, limit={}", userId, role, limit);
        
        if (limit == null || limit <= 0) {
            limit = 5; // 默认获取5条数据
        }
        
        // 检查角色是否包含admin或manage
        boolean hasAdminRole = false;
        if (role != null && !role.isEmpty()) {
            String[] roles = role.split(",");
            for (String r : roles) {
                String roleTrim = r.trim();
                if ("admin".equals(roleTrim) || "manage".equals(roleTrim)) {
                    hasAdminRole = true;
                    break;
                }
            }
        }
        
        // 构建分页和排序条件
        Pageable pageable = PageRequest.of(0, limit, Sort.by(Sort.Direction.DESC, "createTime"));
        
        // 执行查询
        Page<TPatrolRecord> records;
        
        if (hasAdminRole) {
            // 管理员角色，获取所有记录
            log.info("以管理员权限获取巡检工单");
            records = patrolRecordRepository.findByConditions(null, null, null, null, pageable);
        } else if (userId != null) {
            // 普通用户，只获取自己的记录
            log.info("以普通用户权限获取巡检工单, userId={}", userId);
            records = patrolRecordRepository.findByConditions(null, userId, null, null, pageable);
        } else {
            // 未提供有效用户ID且非管理员，返回空列表
            log.warn("未提供有效用户ID且非管理员权限，返回空列表");
            return new ArrayList<>();
        }
        
        // 转换为响应对象
        List<PatrolRecordListResponse> responseList = new ArrayList<>();
        records.getContent().forEach(record -> {
            PatrolRecordListResponse response = new PatrolRecordListResponse();
            response.setId(record.getId());
            response.setPatrolPlanId(record.getPatrolPlanId());
            response.setExecutorId(record.getExecutorId());
            response.setStartTime(record.getStartTime());
            response.setEndTime(record.getEndTime());
            response.setStatus(record.getStatus());
            response.setRemark(record.getRemark());
            response.setCreateTime(record.getCreateTime());
            response.setUpdateTime(record.getUpdateTime());
            response.setExecutionDate(record.getExecutionDate());
            
            // 获取执行人员信息
            userRepository.findById(record.getExecutorId()).ifPresent(user -> {
                response.setExecutorName(user.getName());
            });
            
            // 获取巡检计划信息
            patrolPlanRepository.findById((long) record.getPatrolPlanId()).ifPresent(plan -> {
                response.setPlanName(plan.getName());
                response.setPatrolType(plan.getPatrolType());
            });
            
            responseList.add(response);
        });
        
        log.info("获取到{}条巡检工单记录", responseList.size());
        return responseList;
    }
}

