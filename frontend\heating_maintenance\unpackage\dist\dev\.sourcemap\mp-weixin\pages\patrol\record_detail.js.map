{"version": 3, "file": "record_detail.js", "sources": ["pages/patrol/record_detail.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcGF0cm9sL3JlY29yZF9kZXRhaWwudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"page-container\">\r\n    <!-- 顶部信息卡片 -->\r\n    <view class=\"order-info-card\">\r\n      <view class=\"order-header\">\r\n        <view class=\"order-number\">\r\n          <text>{{ recordInfo.planName || \"-\" }}</text>\r\n        </view>\r\n        <view class=\"order-status\" :class=\"getStatusClass(recordInfo.status)\">\r\n          {{ getStatusText(recordInfo.status) }}\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"order-location\">\r\n        <view class=\"location-info\">\r\n          <text class=\"location-name\">{{ recordInfo.locations || \"未知\"}}</text>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"patrol-info\">\r\n        <view class=\"patrol-executor\">\r\n          <text class=\"patrol-label\">执行人员：</text>\r\n          <text class=\"patrol-value\">{{ recordInfo.executorName || \"未知\" }}</text>\r\n        </view>\r\n        <view class=\"patrol-date\">\r\n          <text class=\"patrol-label\">执行日期：</text>\r\n          <text class=\"patrol-value\">{{ formatDateOnly(recordInfo.executionDate) }}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 详情选项卡 -->\r\n    <view class=\"detail-tabs\">\r\n      <view\r\n        v-for=\"(tab, index) in tabs\"\r\n        :key=\"index\"\r\n        class=\"tab-item\"\r\n        :class=\"{ active: currentTab === index }\"\r\n        @click=\"switchTab(index)\"\r\n      >\r\n        {{ tab.name }}\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 加载中状态显示 -->\r\n   <!-- <view class=\"loading-container\" v-if=\"loading\">\r\n      <view class=\"loading-spinner\"></view>\r\n      <text class=\"loading-text\">加载中...</text>\r\n    </view> -->\r\n\r\n    <!-- 错误提示 -->\r\n    <view class=\"error-container\" v-if=\"showError\">\r\n      <view class=\"error-icon\">!</view>\r\n      <text class=\"error-message\">{{ errorMessage }}</text>\r\n      <button class=\"retry-button\" @click=\"loadPatrolResults\">重试</button>\r\n    </view>\r\n\r\n    <!-- 详情内容区域 - 使用普通view替代swiper -->\r\n    <view class=\"content-container\" v-if=\"!loading && !showError\">\r\n      <!-- 巡检信息 Tab -->\r\n      <view class=\"tab-content\" v-if=\"currentTab === 0\">\r\n        <view class=\"scroll-view-content\">\r\n          <view class=\"info-section\">\r\n            <view class=\"section-content\">\r\n              <view class=\"info-item\">\r\n                <text class=\"info-label\">所属计划</text>\r\n                <text class=\"info-value\">{{ planInfo?.planName || recordInfo.planName || \"-\" }}</text>\r\n              </view>\r\n              <view class=\"info-item\" v-if=\"planInfo?.planNo\">\r\n                <text class=\"info-label\">计划编号</text>\r\n                <text class=\"info-value\">{{ planInfo.planNo }}</text>\r\n              </view>\r\n              <view class=\"info-item\" v-if=\"planInfo?.patrolType\">\r\n                <text class=\"info-label\">巡检类型</text>\r\n                <text class=\"info-value\">{{ planInfo.patrolType }}</text>\r\n              </view>\r\n              <view class=\"info-item\" v-if=\"planInfo?.scheduleType\">\r\n                <text class=\"info-label\">周期类型</text>\r\n                <text class=\"info-value\">{{ getScheduleTypeText(planInfo.scheduleType) }}</text>\r\n              </view>\r\n              <view class=\"info-item\" v-if=\"planInfo?.scheduleType === 'weekly' && planInfo?.scheduleWeekDays\">\r\n                <text class=\"info-label\">执行日</text>\r\n                <text class=\"info-value\">{{ formatWeekDays(planInfo.scheduleWeekDays) }}</text>\r\n              </view>\r\n              <view class=\"info-item\" v-if=\"planInfo?.scheduleType === 'monthly' && planInfo?.scheduleMonthDays\">\r\n                <text class=\"info-label\">执行日</text>\r\n                <text class=\"info-value\">{{ formatMonthDays(planInfo.scheduleMonthDays) }}</text>\r\n              </view>\r\n              <view class=\"info-item\" v-if=\"planInfo?.scheduleType === 'custom' && planInfo?.scheduleInterval\">\r\n                <text class=\"info-label\">执行间隔</text>\r\n                <text class=\"info-value\">{{ planInfo.scheduleInterval }} 天</text>\r\n              </view>\r\n              <view class=\"info-item\">\r\n                <text class=\"info-label\">开始日期</text>\r\n                <text class=\"info-value\">{{ formatDateOnly(planInfo?.startDate) }}</text>\r\n              </view>\r\n              <view class=\"info-item\" v-if=\"planInfo?.endDate\">\r\n                <text class=\"info-label\">结束日期</text>\r\n                <text class=\"info-value\">{{ formatDateOnly(planInfo.endDate) }}</text>\r\n              </view>\r\n              <view class=\"info-item\">\r\n                <text class=\"info-label\">开始时间</text>\r\n                <text class=\"info-value\">{{ formatTimeOnly(recordInfo.startTime) }}</text>\r\n              </view>\r\n              <view class=\"info-item\">\r\n                <text class=\"info-label\">结束时间</text>\r\n                <text class=\"info-value\">{{ formatTimeOnly(recordInfo.endTime) }}</text>\r\n              </view>\r\n              <view class=\"info-item\">\r\n                <text class=\"info-label\">巡检地点</text>\r\n                <text class=\"info-value\">{{ recordInfo.locations || \"-\" }}</text>\r\n              </view>\r\n              <view class=\"info-item\" v-if=\"recordInfo.executorPhone\">\r\n                <text class=\"info-label\">联系电话</text>\r\n                <text class=\"info-value\">{{ recordInfo.executorPhone }}</text>\r\n              </view>\r\n              <view class=\"info-item\" v-if=\"recordInfo.remark\">\r\n                <text class=\"info-label\">备注</text>\r\n                <text class=\"info-value\">{{ recordInfo.remark }}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 巡检任务/结果 Tab -->\r\n      <view class=\"tab-content\" v-if=\"currentTab === 1\">\r\n        <view class=\"scroll-view-content\">\r\n          <view class=\"patrol-results-header\">\r\n            <text class=\"results-title\">{{ recordInfo.status === 'pending' || recordInfo.status === 'overdue' ? '巡检任务' : '巡检结果' }}</text>\r\n            <view class=\"results-summary\">\r\n              <text class=\"results-count\">共 {{ patrolResults.length }} 项</text>\r\n              <text\r\n                class=\"abnormal-count\"\r\n                v-if=\"resultSummary && resultSummary.abnormalCount > 0 && recordInfo.status !== 'pending' && recordInfo.status !== 'overdue'\"\r\n              >\r\n                异常 {{ resultSummary.abnormalCount }} 项\r\n              </text>\r\n            </view>\r\n          </view>\r\n\r\n          <view v-if=\"patrolResults.length === 0\" class=\"empty-results\">\r\n            <image src=\"/static/images/empty.png\" mode=\"aspectFit\"></image>\r\n            <text>{{ recordInfo.status === 'pending' || recordInfo.status === 'overdue' ? '暂无巡检任务' : '暂无巡检结果' }}</text>\r\n          </view>\r\n\r\n          <view v-else class=\"task-list\">\r\n            <view\r\n              class=\"task-item\"\r\n              v-for=\"(result, index) in patrolResults\"\r\n              :key=\"index\"\r\n              @click=\"toggleTaskDetails(index)\"\r\n            >\r\n              <view class=\"task-header\">\r\n                <view class=\"task-left\">\r\n                  <view class=\"task-status\" \r\n                    :class=\"recordInfo.status === 'pending' ? 'pending' : \r\n                           recordInfo.status === 'overdue' ? 'overdue' : \r\n                           result.checkResult === 'normal' ? 'completed' : 'abnormal'\"></view>\r\n                  <text class=\"task-title\">{{\r\n                    result.itemName || `巡检项 ${index + 1}`\r\n                  }}</text>\r\n                </view>\r\n                <text\r\n                  class=\"task-status-text\"\r\n                  :class=\"recordInfo.status === 'pending' ? 'status-pending' : \r\n                          recordInfo.status === 'overdue' ? 'status-overdue' : \r\n                          result.checkResult === 'normal' ? 'status-normal' : 'status-abnormal'\"\r\n                >\r\n                  {{ getTaskStatusText(result) }}\r\n                </text>\r\n              </view>\r\n              <view class=\"task-details\" v-if=\"result.showDetails\">\r\n                <view class=\"detail-row\" v-if=\"result.deviceName\">\r\n                  <text class=\"detail-label\">设备信息</text>\r\n                   <text class=\"detail-value\">{{ result.deviceName }}{{ result.deviceType ? ` (${result.deviceType})` : '' }}</text>\r\n                </view>\r\n                <view class=\"detail-row\" v-if=\"result.categoryName\">\r\n                  <text class=\"detail-label\">检查类别</text>\r\n                  <text class=\"detail-value\">{{ result.categoryName }}</text>\r\n                </view>\r\n                <view class=\"detail-row\" v-if=\"result.checkMethod\">\r\n                  <text class=\"detail-label\">检查方法</text>\r\n                  <text class=\"detail-value\">{{ result.checkMethod }}</text>\r\n                </view>\r\n                <view class=\"detail-row\" v-if=\"result.checkDescription\">\r\n                  <text class=\"detail-label\">描述说明</text>\r\n                  <text class=\"detail-value\">{{ result.checkDescription }}</text>\r\n                </view>\r\n                <view class=\"detail-row\" v-if=\"result.paramValue\">\r\n                  <text class=\"detail-label\">参数值</text>\r\n                  <text class=\"detail-value\" :class=\"{ 'value-abnormal': result.checkResult === 'abnormal' }\"\r\n                    >{{ result.paramValue\r\n                    }}{{ result.unit ? \" \" + result.unit : \"\" }}</text\r\n                  >\r\n                </view>\r\n                <view class=\"detail-row\" v-if=\"result.normalRange\">\r\n                  <text class=\"detail-label\">正常范围</text>\r\n                  <text class=\"detail-value\">{{ result.normalRange }}</text>\r\n                </view>\r\n                <view class=\"detail-row\" v-if=\"result.description\">\r\n                  <text class=\"detail-label\">备注</text>\r\n                  <text class=\"detail-value\">{{ result.description }}</text>\r\n                </view>\r\n\r\n                <!-- 图片展示 -->\r\n                <view\r\n                  class=\"task-images\"\r\n                  v-if=\"result.images && result.images.length > 0\"\r\n                >\r\n                  <text class=\"images-title\">巡检照片</text>\r\n                  <view class=\"image-list\">\r\n                    <image \r\n                      v-for=\"(img, imgIndex) in result.images\" \r\n                      :key=\"imgIndex\" \r\n                      :src=\"getFullImageUrl(img)\" \r\n                      @click=\"previewImage(result.images, imgIndex)\" \r\n                      mode=\"aspectFill\"\r\n                    ></image>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 照片汇总 Tab -->\r\n      <view class=\"tab-content\" v-if=\"currentTab === 2\">\r\n        <view class=\"scroll-view-content\">\r\n          <view v-if=\"getAllImages().length > 0\" class=\"all-images\">\r\n            <view\r\n              v-for=\"(imageInfo, index) in getAllImages()\"\r\n              :key=\"index\"\r\n              class=\"image-card\"\r\n              @click=\"previewImage(getAllImagesUrls(), index)\"\r\n            >\r\n              <image :src=\"getFullImageUrl(imageInfo.url)\" mode=\"aspectFill\"></image>\r\n              <view class=\"image-item-name\">{{ imageInfo.itemName }}</view>\r\n            </view>\r\n          </view>\r\n          \r\n          <view v-else class=\"empty-results\">\r\n            <view class=\"empty-content\">\r\n              <image src=\"/static/images/empty.png\" mode=\"aspectFit\"></image>\r\n              <text>暂无巡检照片</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n\t<!-- 底部操作按钮 -->\r\n\t<PermissionCheck permission=\"patrol:plans:execute\">\r\n\t <view class=\"action-buttons\" v-if=\"recordInfo.status === 'pending' ||  recordInfo.status === 'overdue'\">\r\n\t \t\t<view class=\"action-btn start\"  @click=\"startPatrol(recordInfo.id,recordInfo.patrolPlanId)\">\r\n\t \t\t\t<text>开始巡检</text>\r\n\t \t\t</view>\r\n\t \t</view>\r\n\t</PermissionCheck>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { patrolApi } from \"@/utils/api.js\";\r\nimport uploadUtils from \"@/utils/upload.js\";\r\nimport PermissionCheck from \"@/components/PermissionCheck.vue\";\r\n\r\nexport default {\r\n  components: {\r\n    PermissionCheck\r\n  },\r\n  data() {\r\n    return {\r\n      loading: true,\r\n      showError: false,\r\n      errorMessage: \"\",\r\n      recordId: null,\r\n      recordInfo: {},\r\n      patrolResults: [],\r\n      resultSummary: null,\r\n      planInfo: null,\r\n      currentTab: 0,\r\n      tabs: [\r\n        { name: '巡检信息' },\r\n        { name: '巡检任务及结果' },\r\n        { name: '巡检照片' }\r\n      ]\r\n    };\r\n  },\r\n  onLoad(options) {\r\n    if (options && options.id) {\r\n      this.recordId = options.id;\r\n      this.loadPatrolResults();\r\n    } else {\r\n      this.showError = true;\r\n      this.errorMessage = \"未找到记录ID，无法加载详情\";\r\n      this.loading = false;\r\n    }\r\n  },\r\n  onReady() {\r\n    // 确保初始化时显示第一个选项卡\r\n    this.currentTab = 0;\r\n  },\r\n  methods: {\r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        pending: \"待执行\",\r\n        processing: \"执行中\",\r\n        completed: \"已完成\",\r\n        overdue: \"已超时\"\r\n      };\r\n      return statusMap[status] || \"未知\";\r\n    },\r\n\r\n    // 获取状态样式类\r\n    getStatusClass(status) {\r\n      const statusClassMap = {\r\n        pending: \"status-pending\",\r\n        processing: \"status-processing\",\r\n        completed: \"status-completed\",\r\n        overdue: \"status-overdue\"\r\n      };\r\n      return statusClassMap[status] || \"\";\r\n    },\r\n    // 开始巡检\r\n    startPatrol(id,planId) {\r\n      uni.navigateTo({\r\n        url: `/pages/patrol/execute?id=${id}&&planId=${planId}`\r\n      });\r\n    },\r\n    // 加载巡检结果\r\n    loadPatrolResults() {\r\n      // 检查API是否可用\r\n      if (!patrolApi || !patrolApi.getPatrolResultDetail) {\r\n        this.showError = true;\r\n        this.errorMessage = \"API服务不可用\";\r\n        this.loading = false;\r\n        return;\r\n      }\r\n\t  // 显示加载提示\r\n\t  uni.showLoading({\r\n\t    title: \"加载中...\",\r\n\t  });\r\n      // 加载巡检结果详情\r\n      patrolApi\r\n        .getPatrolResultDetail(this.recordId)\r\n        .then((res) => {\r\n          this.loading = false;\r\n\r\n          if (res.code === 200 && res.data) {\r\n            // 处理API响应数据\r\n            this.recordInfo = res.data.recordInfo || {};\r\n            \r\n            // 处理巡检结果列表，确保图片数据格式正确\r\n            if (res.data.resultList && Array.isArray(res.data.resultList)) {\r\n              this.patrolResults = res.data.resultList.map(result => {\r\n                // 确保images字段是数组\r\n                if (result.images && !Array.isArray(result.images)) {\r\n                  // 如果是字符串，尝试解析JSON\r\n                  try {\r\n                    result.images = JSON.parse(result.images);\r\n                  } catch (e) {\r\n                    // 如果解析失败，将其转为数组\r\n                    result.images = result.images ? [result.images] : [];\r\n                  }\r\n                }\r\n                return result;\r\n              });\r\n            } else {\r\n              this.patrolResults = [];\r\n            }\r\n            \r\n            this.resultSummary = res.data.summary || null;\r\n            this.planInfo = res.data.planInfo || null;\r\n\t\t\t uni.hideLoading();\r\n          } else {\r\n            // 只显示警告，不阻止显示其他信息\r\n\t\t\t uni.hideLoading();\r\n            console.warn(\"获取巡检结果失败:\", res.message);\r\n            this.patrolResults = [];\r\n          }\r\n        })\r\n        .catch((err) => {\r\n\t\t\t uni.hideLoading();\r\n          this.loading = false;\r\n          console.error(\"加载巡检结果失败:\", err);\r\n          uni.showToast({\r\n            title: \"网络异常，无法加载巡检结果\",\r\n            icon: \"none\",\r\n            duration: 2000,\r\n          });\r\n          this.patrolResults = [];\r\n        });\r\n    },\r\n    // 返回上一页\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n\r\n    // 预览图片\r\n    previewImage(images, current) {\r\n      // 将所有图片路径转换为完整URL\r\n      const fullUrls = images.map(path => this.getFullImageUrl(path));\r\n      uni.previewImage({\r\n        urls: fullUrls,\r\n        current: fullUrls[current],\r\n      });\r\n    },\r\n\r\n    // 获取完整的图片URL\r\n    getFullImageUrl(path) {\r\n      if (!path) return '';\r\n      \r\n      // 如果已经是完整URL，直接返回\r\n      if (path.startsWith('http')) {\r\n        return path;\r\n      }\r\n      \r\n      // 使用uploadUtils获取完整URL\r\n      return uploadUtils.getFileUrl(path);\r\n    },\r\n\r\n    // 格式化日期（不含时间）\r\n    formatDateOnly(date) {\r\n      if (!date) return \"-\";\r\n\r\n      // 处理数组格式的日期 [year, month, day]\r\n      if (Array.isArray(date)) {\r\n        if (date.length >= 3) {\r\n          const year = date[0];\r\n          const month = String(date[1]).padStart(2, \"0\");\r\n          const day = String(date[2]).padStart(2, \"0\");\r\n          return `${year}-${month}-${day}`;\r\n        }\r\n        return date.join(\"-\"); // 如果无法解析，返回用-连接的数组\r\n      }\r\n\r\n      // 其他格式处理\r\n      try {\r\n        const d = new Date(date);\r\n        if (!isNaN(d.getTime())) {\r\n          const year = d.getFullYear();\r\n          const month = String(d.getMonth() + 1).padStart(2, \"0\");\r\n          const day = String(d.getDate()).padStart(2, \"0\");\r\n          return `${year}-${month}-${day}`;\r\n        }\r\n        return String(date);\r\n      } catch (e) {\r\n        console.error(\"格式化日期出错:\", e);\r\n        return String(date);\r\n      }\r\n    },\r\n\r\n    // 格式化日期时间\r\n    formatDateTime(dateTime) {\r\n      if (!dateTime) return \"-\";\r\n\r\n      // 处理数组格式的日期时间 [year, month, day]或[year, month, day, hour, minute, second]\r\n      if (Array.isArray(dateTime)) {\r\n        if (dateTime.length >= 3) {\r\n          const year = dateTime[0];\r\n          const month = String(dateTime[1]).padStart(2, \"0\");\r\n          const day = String(dateTime[2]).padStart(2, \"0\");\r\n\r\n          // 如果包含时间部分\r\n          if (dateTime.length >= 5) {\r\n            const hour = String(dateTime[3]).padStart(2, \"0\");\r\n            const minute = String(dateTime[4]).padStart(2, \"0\");\r\n            const second =\r\n              dateTime.length > 5 ? String(dateTime[5]).padStart(2, \"0\") : \"00\";\r\n            return `${year}-${month}-${day} ${hour}:${minute}:${second}`;\r\n          }\r\n\r\n          return `${year}-${month}-${day}`;\r\n        }\r\n        return dateTime.join(\"-\"); // 如果无法解析，返回用-连接的数组\r\n      }\r\n\r\n      // 确保dateTime是字符串类型\r\n      const dateTimeStr = String(dateTime);\r\n\r\n      // 处理ISO格式日期时间\r\n      if (dateTimeStr.includes(\"T\")) {\r\n        const date = new Date(dateTimeStr);\r\n        const year = date.getFullYear();\r\n        const month = String(date.getMonth() + 1).padStart(2, \"0\");\r\n        const day = String(date.getDate()).padStart(2, \"0\");\r\n        const hours = String(date.getHours()).padStart(2, \"0\");\r\n        const minutes = String(date.getMinutes()).padStart(2, \"0\");\r\n        const seconds = String(date.getSeconds()).padStart(2, \"0\");\r\n        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n      }\r\n\r\n      // 如果是数字类型（时间戳），转换为日期字符串\r\n      if (!isNaN(dateTime) && typeof dateTime === \"number\") {\r\n        const date = new Date(dateTime);\r\n        const year = date.getFullYear();\r\n        const month = String(date.getMonth() + 1).padStart(2, \"0\");\r\n        const day = String(date.getDate()).padStart(2, \"0\");\r\n        const hours = String(date.getHours()).padStart(2, \"0\");\r\n        const minutes = String(date.getMinutes()).padStart(2, \"0\");\r\n        const seconds = String(date.getSeconds()).padStart(2, \"0\");\r\n        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n      }\r\n\r\n      // 对于其他格式的字符串，尝试用Date解析\r\n      try {\r\n        const date = new Date(dateTimeStr);\r\n        if (!isNaN(date.getTime())) {\r\n          const year = date.getFullYear();\r\n          const month = String(date.getMonth() + 1).padStart(2, \"0\");\r\n          const day = String(date.getDate()).padStart(2, \"0\");\r\n          const hours = String(date.getHours()).padStart(2, \"0\");\r\n          const minutes = String(date.getMinutes()).padStart(2, \"0\");\r\n          const seconds = String(date.getSeconds()).padStart(2, \"0\");\r\n\r\n          // 确认是否有时间部分\r\n          if (hours !== \"00\" || minutes !== \"00\" || seconds !== \"00\") {\r\n            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n          } else {\r\n            return `${year}-${month}-${day}`;\r\n          }\r\n        }\r\n\r\n        // 如果Date解析失败，尝试按原样返回或处理特殊格式\r\n        return dateTimeStr;\r\n      } catch (e) {\r\n        console.error(\"格式化日期时间出错:\", e, dateTimeStr);\r\n        return dateTimeStr; // 如果无法格式化，返回原始值\r\n      }\r\n    },\r\n\r\n    // 格式化时间，只提取时间部分 (HH:MM:SS)\r\n    formatTimeOnly(dateTime) {\r\n      if (!dateTime) return \"-\";\r\n\r\n      // 先获取完整的日期时间格式\r\n      const fullDateTime = this.formatDateTime(dateTime);\r\n\r\n      // 如果包含空格（日期和时间分隔符），提取时间部分\r\n      if (fullDateTime.includes(\" \")) {\r\n        return fullDateTime.split(\" \")[1];\r\n      }\r\n\r\n      return fullDateTime; // 如果没有空格，可能只是时间或格式不符合预期，直接返回\r\n    },\r\n\r\n    // 获取巡检计划类型文本\r\n    getScheduleTypeText(scheduleType) {\r\n      if (!scheduleType) return \"-\";\r\n      const scheduleTypeMap = {\r\n        daily: \"每日巡检\",\r\n        weekly: \"每周巡检\",\r\n        monthly: \"每月巡检\",\r\n        custom: \"自定义巡检\"\r\n      };\r\n      return scheduleTypeMap[scheduleType] || scheduleType;\r\n    },\r\n\r\n    // 切换任务详情显示/隐藏\r\n    toggleTaskDetails(index) {\r\n      // 如果patrolResults[index]没有showDetails属性，添加它\r\n      if (this.patrolResults[index] && !this.patrolResults[index].hasOwnProperty('showDetails')) {\r\n        this.$set(this.patrolResults[index], 'showDetails', true);\r\n      } else {\r\n        this.$set(this.patrolResults[index], 'showDetails', !this.patrolResults[index].showDetails);\r\n      }\r\n    },\r\n\r\n    // 格式化周执行日文本\r\n    formatWeekDays(weekDays) {\r\n      if (!weekDays || !Array.isArray(weekDays)) return \"-\";\r\n      \r\n      const weekDayNames = {\r\n        1: \"周一\",\r\n        2: \"周二\",\r\n        3: \"周三\",\r\n        4: \"周四\",\r\n        5: \"周五\",\r\n        6: \"周六\",\r\n        7: \"周日\"\r\n      };\r\n      \r\n      return weekDays.map(day => weekDayNames[day] || `周${day}`).join(\", \");\r\n    },\r\n\r\n    // 格式化月执行日文本\r\n    formatMonthDays(monthDays) {\r\n      if (!monthDays || !Array.isArray(monthDays)) return \"-\";\r\n      return monthDays.map(day => `${day}日`).join(\", \");\r\n    },\r\n\r\n    // 获取巡检项目状态文本\r\n    getTaskStatusText(result) {\r\n      if (this.recordInfo.status === 'pending') {\r\n        return '待执行';\r\n      } else if (this.recordInfo.status === 'overdue') {\r\n        return '已超时';\r\n      } else {\r\n        return result.checkResult === 'normal' ? '正常' : '异常';\r\n      }\r\n    },\r\n\r\n    // 获取所有图片信息\r\n    getAllImages() {\r\n      const images = [];\r\n      this.patrolResults.forEach(result => {\r\n        if (result.images && Array.isArray(result.images)) {\r\n          result.images.forEach(img => {\r\n            images.push({\r\n              url: this.getFullImageUrl(img),\r\n              itemName: result.itemName || `巡检项 ${result.index + 1}`\r\n            });\r\n          });\r\n        }\r\n      });\r\n      return images;\r\n    },\r\n\r\n    // 获取所有图片URL\r\n    getAllImagesUrls() {\r\n      const urls = [];\r\n      this.getAllImages().forEach(image => {\r\n        urls.push(image.url);\r\n      });\r\n      return urls;\r\n    },\r\n\r\n    // 切换选项卡\r\n    switchTab(index) {\r\n      this.currentTab = index;\r\n      // 添加延迟，确保DOM更新后再滚动到顶部\r\n      setTimeout(() => {\r\n        uni.pageScrollTo({\r\n          scrollTop: 0,\r\n          duration: 0\r\n        });\r\n      }, 50);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.page-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 100vh;\r\n  background-color: #f5f5f5;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.order-info-card {\r\n  background-color: #fff;\r\n  border-radius: 12rpx;\r\n  overflow: hidden;\r\n  margin: 20rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n  padding: 30rpx;\r\n\r\n  .order-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 20rpx;\r\n\r\n    .order-number {\r\n      font-size: 32rpx;\r\n      font-weight: bold;\r\n      color: #333;\r\n    }\r\n\r\n    .order-status {\r\n      padding: 6rpx 16rpx;\r\n      border-radius: 6rpx;\r\n      font-size: 24rpx;\r\n      text-align: center;\r\n      min-width: 80rpx;\r\n\r\n      &.status-pending {\r\n        background-color: rgba(250, 173, 20, 0.1);\r\n        color: #faad14; // 黄色\r\n      }\r\n\r\n      &.status-processing {\r\n        background-color: rgba(24, 144, 255, 0.1);\r\n        color: #1890ff; // 蓝色\r\n      }\r\n\r\n      &.status-completed {\r\n        background-color: rgba(82, 196, 26, 0.1);\r\n        color: #52c41a; // 绿色\r\n      }\r\n\r\n      &.status-overdue {\r\n        background-color: rgba(245, 34, 45, 0.1);\r\n        color: #f5222d; // 红色\r\n      }\r\n    }\r\n  }\r\n\r\n  .order-location {\r\n    margin-bottom: 20rpx;\r\n\r\n    .location-info {\r\n      font-size: 28rpx;\r\n      color: #666;\r\n      \r\n      .location-name {\r\n        font-size: 32rpx;\r\n        color: #333;\r\n        font-weight: bold;\r\n      }\r\n    }\r\n  }\r\n\r\n  .patrol-info {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-top: 20rpx;\r\n\r\n    .patrol-executor, .patrol-date {\r\n      .patrol-label {\r\n        font-size: 24rpx;\r\n        color: #999;\r\n        margin-right: 10rpx;\r\n      }\r\n\r\n      .patrol-value {\r\n        font-size: 28rpx;\r\n        color: #333;\r\n        font-weight: bold;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.detail-tabs {\r\n  display: flex;\r\n  background-color: #fff;\r\n  margin: 0 20rpx 20rpx;\r\n  border-radius: 12rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n  position: sticky; /* 使选项卡固定在顶部 */\r\n  top: 0;\r\n  z-index: 10;\r\n\r\n  .tab-item {\r\n    flex: 1;\r\n    text-align: center;\r\n    height: 80rpx;\r\n    line-height: 80rpx;\r\n    font-size: 28rpx;\r\n    color: #666;\r\n    position: relative;\r\n\r\n    &.active {\r\n      color: #1890ff;\r\n      font-weight: bold;\r\n\r\n      &::after {\r\n        content: \"\";\r\n        position: absolute;\r\n        bottom: 0;\r\n        left: 50%;\r\n        transform: translateX(-50%);\r\n        width: 40%;\r\n        height: 4rpx;\r\n        background-color: #1890ff;\r\n        border-radius: 2rpx;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 40vh;\r\n\r\n  .loading-spinner {\r\n    width: 70rpx;\r\n    height: 70rpx;\r\n    border: 6rpx solid #f3f3f3;\r\n    border-top: 6rpx solid #1890ff;\r\n    border-radius: 50%;\r\n    animation: spin 1s linear infinite;\r\n    margin-bottom: 20rpx;\r\n  }\r\n\r\n  .loading-text {\r\n    font-size: 28rpx;\r\n    color: #666;\r\n  }\r\n\r\n  @keyframes spin {\r\n    0% {\r\n      transform: rotate(0deg);\r\n    }\r\n    100% {\r\n      transform: rotate(360deg);\r\n    }\r\n  }\r\n}\r\n\r\n.error-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 80rpx 40rpx;\r\n  width: 100%;\r\n  box-sizing: border-box;\r\n\r\n  .error-icon {\r\n    width: 80rpx;\r\n    height: 80rpx;\r\n    line-height: 80rpx;\r\n    text-align: center;\r\n    background-color: #fff2f0;\r\n    border-radius: 50%;\r\n    color: #f5222d;\r\n    font-size: 50rpx;\r\n    font-weight: bold;\r\n    margin-bottom: 20rpx;\r\n  }\r\n\r\n  .error-message {\r\n    font-size: 28rpx;\r\n    color: #666;\r\n    text-align: center;\r\n    margin-bottom: 30rpx;\r\n  }\r\n\r\n  .retry-button {\r\n    background-color: #1890ff;\r\n    color: #fff;\r\n    font-size: 28rpx;\r\n    padding: 16rpx 40rpx;\r\n    border-radius: 8rpx;\r\n    border: none;\r\n  }\r\n}\r\n\r\n.content-container {\r\n  flex: 1;\r\n  overflow: visible; /* 改为visible，允许内容自然流动 */\r\n  padding-bottom: 120rpx; /* 为底部按钮留出空间 */\r\n}\r\n\r\n.tab-content {\r\n  height: auto;\r\n  min-height: 60vh; /* 确保内容区域有足够的高度 */\r\n  padding: 0 20rpx;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.scroll-view-content {\r\n  height: auto; /* 移除固定高度限制，改为自适应内容高度 */\r\n  max-height: none; /* 移除最大高度限制 */\r\n  overflow: visible; /* 确保内容可见 */\r\n}\r\n\r\n.info-section {\r\n  background-color: #fff;\r\n  border-radius: 12rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\r\n  .section-title {\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n    color: #333;\r\n    margin-bottom: 20rpx;\r\n    padding-left: 20rpx;\r\n    // border-left: 6rpx solid #1890ff;\r\n  }\r\n\r\n  .section-content {\r\n    .info-item {\r\n      display: flex;\r\n      padding: 16rpx 0;\r\n      border-bottom: 2rpx solid #f5f5f5;\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      .info-label {\r\n        width: 160rpx;\r\n        font-size: 28rpx;\r\n        color: #666;\r\n      }\r\n\r\n      .info-value {\r\n        flex: 1;\r\n        font-size: 28rpx;\r\n        color: #333;\r\n\r\n        &.normal-count {\r\n          color: #52c41a;\r\n          font-weight: bold;\r\n        }\r\n        \r\n        &.abnormal-count {\r\n          color: #f5222d;\r\n          font-weight: bold;\r\n        }\r\n        \r\n        &.status-value {\r\n          font-weight: bold;\r\n          \r\n          &.pending {\r\n            color: #faad14; // 黄色 - 待执行\r\n          }\r\n          \r\n          &.processing {\r\n            color: #1890ff; // 蓝色 - 执行中\r\n          }\r\n          \r\n          &.completed {\r\n            color: #52c41a; // 绿色 - 已完成\r\n          }\r\n          \r\n          &.overdue {\r\n            color: #f5222d; // 红色 - 已超时\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.patrol-results-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n  padding: 20rpx;\r\n  background-color: #fff;\r\n  border-radius: 12rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\r\n  .results-title {\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n    color: #333;\r\n  }\r\n\r\n  .results-summary {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .results-count {\r\n      font-size: 26rpx;\r\n      color: #999;\r\n      margin-right: 16rpx;\r\n    }\r\n\r\n    .abnormal-count {\r\n      font-size: 26rpx;\r\n      color: #f5222d;\r\n      font-weight: bold;\r\n    }\r\n  }\r\n}\r\n\r\n.empty-results {\r\n  padding: 0;\r\n  text-align: center;\r\n  background-color: #fff;\r\n  border-radius: 12rpx;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n  min-height: 500rpx; /* 增加最小高度，确保有足够空间 */\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n  .empty-content {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 40rpx 0;\r\n  }\r\n\r\n  image {\r\n    width: 200rpx;\r\n    height: 200rpx;\r\n    margin-bottom: 30rpx;\r\n  }\r\n\r\n  text {\r\n    font-size: 28rpx;\r\n    color: #999;\r\n    display: block;\r\n    width: 100%;\r\n    text-align: center;\r\n    line-height: 1.5;\r\n  }\r\n}\r\n\r\n.task-list {\r\n  .task-item {\r\n    padding: 20rpx;\r\n    background-color: #fff;\r\n    border-radius: 12rpx;\r\n    margin-bottom: 20rpx;\r\n    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\r\n    .task-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 16rpx;\r\n\r\n      .task-left {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .task-status {\r\n          width: 20rpx;\r\n          height: 20rpx;\r\n          border-radius: 50%;\r\n          margin-right: 10rpx;\r\n\r\n          &.completed {\r\n            background-color: #52c41a;\r\n          }\r\n\r\n          &.abnormal {\r\n            background-color: #f5222d;\r\n          }\r\n          \r\n          &.pending {\r\n            background-color: #faad14;\r\n          }\r\n          \r\n          &.overdue {\r\n            background-color: #f5222d;\r\n          }\r\n        }\r\n\r\n        .task-title {\r\n          font-size: 30rpx;\r\n          font-weight: bold;\r\n          color: #333;\r\n        }\r\n      }\r\n\r\n      .task-status-text {\r\n        padding: 4rpx 16rpx;\r\n        border-radius: 20rpx;\r\n        font-size: 24rpx;\r\n        \r\n        &.status-normal {\r\n          background-color: rgba(82, 196, 26, 0.1);\r\n          color: #52c41a;\r\n        }\r\n        \r\n        &.status-abnormal {\r\n          background-color: rgba(245, 34, 45, 0.1);\r\n          color: #f5222d;\r\n        }\r\n        \r\n        &.status-pending {\r\n          background-color: rgba(250, 173, 20, 0.1);\r\n          color: #faad14; // 黄色\r\n        }\r\n        \r\n        &.status-overdue {\r\n          background-color: rgba(245, 34, 45, 0.1);\r\n          color: #f5222d; // 红色\r\n        }\r\n      }\r\n    }\r\n\r\n    .task-details {\r\n      background-color: #f8f8f8;\r\n      border-radius: 8rpx;\r\n      padding: 16rpx;\r\n      margin-top: 16rpx;\r\n\r\n      .detail-row {\r\n        display: flex;\r\n        margin-bottom: 16rpx;\r\n\r\n        .detail-label {\r\n          width: 120rpx;\r\n          font-size: 26rpx;\r\n          color: #666;\r\n        }\r\n\r\n        .detail-value {\r\n          flex: 1;\r\n          font-size: 26rpx;\r\n          color: #333;\r\n          word-break: break-all;\r\n          \r\n          &.value-abnormal {\r\n            color: #f5222d;\r\n          }\r\n        }\r\n      }\r\n\r\n      .task-images {\r\n        margin-top: 20rpx;\r\n\r\n        .images-title {\r\n          font-size: 28rpx;\r\n          font-weight: bold;\r\n          color: #333;\r\n          margin-bottom: 10rpx;\r\n        }\r\n\r\n        .image-list {\r\n          display: flex;\r\n          flex-wrap: wrap;\r\n          margin: 0 -10rpx;\r\n\r\n          image {\r\n            width: 33.33%;\r\n            height: 200rpx;\r\n            padding: 10rpx;\r\n            box-sizing: border-box;\r\n            flex-shrink: 0;\r\n            border-radius: 8rpx;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.all-images {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: 0 -10rpx;\r\n  padding: 20rpx;\r\n  background-color: #fff;\r\n  border-radius: 12rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\r\n  .image-card {\r\n    width: 33.33%;\r\n    padding: 10rpx;\r\n    box-sizing: border-box;\r\n    position: relative;\r\n    height: 220rpx;\r\n\r\n    image {\r\n      width: 100%;\r\n      height: 180rpx;\r\n      object-fit: cover;\r\n      border-radius: 8rpx;\r\n    }\r\n\r\n    .image-item-name {\r\n      font-size: 24rpx;\r\n      color: #666;\r\n      margin-top: 10rpx;\r\n      white-space: nowrap;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n    }\r\n  }\r\n}\r\n   .action-buttons {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tpadding: 20rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\tbox-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);\r\n\t\t\r\n\t\t.action-btn {\r\n\t\t\tflex: 1;\r\n\t\t\theight: 80rpx;\r\n\t\t\tline-height: 80rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tmargin: 0 10rpx;\r\n\t\t\t\r\n\t\t\t&.start {\r\n\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.continue {\r\n\t\t\t\tbackground-color: $uni-color-success;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.complete {\r\n\t\t\t\tbackground-color: $uni-color-warning;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n.section-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n  margin: 20rpx 0;\r\n  padding-left: 20rpx;\r\n  border-left: 6rpx solid #1890ff;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/patrol/record_detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "patrolApi", "uploadUtils"], "mappings": ";;;;;AA0QA,MAAK,kBAAmB,MAAW;AAEnC,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,SAAS;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,YAAY,CAAE;AAAA,MACd,eAAe,CAAE;AAAA,MACjB,eAAe;AAAA,MACf,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,EAAE,MAAM,OAAQ;AAAA,QAChB,EAAE,MAAM,UAAW;AAAA,QACnB,EAAE,MAAM,OAAO;AAAA,MACjB;AAAA;EAEH;AAAA,EACD,OAAO,SAAS;AACd,QAAI,WAAW,QAAQ,IAAI;AACzB,WAAK,WAAW,QAAQ;AACxB,WAAK,kBAAiB;AAAA,WACjB;AACL,WAAK,YAAY;AACjB,WAAK,eAAe;AACpB,WAAK,UAAU;AAAA,IACjB;AAAA,EACD;AAAA,EACD,UAAU;AAER,SAAK,aAAa;AAAA,EACnB;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,cAAc,QAAQ;AACpB,YAAM,YAAY;AAAA,QAChB,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,SAAS;AAAA;AAEX,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,eAAe,QAAQ;AACrB,YAAM,iBAAiB;AAAA,QACrB,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,SAAS;AAAA;AAEX,aAAO,eAAe,MAAM,KAAK;AAAA,IAClC;AAAA;AAAA,IAED,YAAY,IAAG,QAAQ;AACrBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4BAA4B,EAAE,YAAY,MAAM;AAAA,MACvD,CAAC;AAAA,IACF;AAAA;AAAA,IAED,oBAAoB;AAElB,UAAI,CAACC,UAAU,aAAG,CAACA,UAAS,UAAC,uBAAuB;AAClD,aAAK,YAAY;AACjB,aAAK,eAAe;AACpB,aAAK,UAAU;AACf;AAAA,MACF;AAEHD,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAEEC,gBAAQ,UACL,sBAAsB,KAAK,QAAQ,EACnC,KAAK,CAAC,QAAQ;AACb,aAAK,UAAU;AAEf,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAEhC,eAAK,aAAa,IAAI,KAAK,cAAc,CAAA;AAGzC,cAAI,IAAI,KAAK,cAAc,MAAM,QAAQ,IAAI,KAAK,UAAU,GAAG;AAC7D,iBAAK,gBAAgB,IAAI,KAAK,WAAW,IAAI,YAAU;AAErD,kBAAI,OAAO,UAAU,CAAC,MAAM,QAAQ,OAAO,MAAM,GAAG;AAElD,oBAAI;AACF,yBAAO,SAAS,KAAK,MAAM,OAAO,MAAM;AAAA,gBAC1C,SAAS,GAAG;AAEV,yBAAO,SAAS,OAAO,SAAS,CAAC,OAAO,MAAM,IAAI;gBACpD;AAAA,cACF;AACA,qBAAO;AAAA,YACT,CAAC;AAAA,iBACI;AACL,iBAAK,gBAAgB;UACvB;AAEA,eAAK,gBAAgB,IAAI,KAAK,WAAW;AACzC,eAAK,WAAW,IAAI,KAAK,YAAY;AAC7CD,wBAAG,MAAC,YAAW;AAAA,eACF;AAEbA,wBAAG,MAAC,YAAW;AACPA,wBAAa,MAAA,MAAA,QAAA,yCAAA,aAAa,IAAI,OAAO;AACrC,eAAK,gBAAgB;QACvB;AAAA,OACD,EACA,MAAM,CAAC,QAAQ;AACpBA,sBAAG,MAAC,YAAW;AACT,aAAK,UAAU;AACfA,sBAAA,MAAA,MAAA,SAAA,yCAAc,aAAa,GAAG;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACZ,CAAC;AACD,aAAK,gBAAgB;MACvB,CAAC;AAAA,IACJ;AAAA;AAAA,IAED,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA;AAAA,IAGD,aAAa,QAAQ,SAAS;AAE5B,YAAM,WAAW,OAAO,IAAI,UAAQ,KAAK,gBAAgB,IAAI,CAAC;AAC9DA,oBAAAA,MAAI,aAAa;AAAA,QACf,MAAM;AAAA,QACN,SAAS,SAAS,OAAO;AAAA,MAC3B,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB,MAAM;AACpB,UAAI,CAAC;AAAM,eAAO;AAGlB,UAAI,KAAK,WAAW,MAAM,GAAG;AAC3B,eAAO;AAAA,MACT;AAGA,aAAOE,aAAW,YAAC,WAAW,IAAI;AAAA,IACnC;AAAA;AAAA,IAGD,eAAe,MAAM;AACnB,UAAI,CAAC;AAAM,eAAO;AAGlB,UAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,YAAI,KAAK,UAAU,GAAG;AACpB,gBAAM,OAAO,KAAK,CAAC;AACnB,gBAAM,QAAQ,OAAO,KAAK,CAAC,CAAC,EAAE,SAAS,GAAG,GAAG;AAC7C,gBAAM,MAAM,OAAO,KAAK,CAAC,CAAC,EAAE,SAAS,GAAG,GAAG;AAC3C,iBAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,QAChC;AACA,eAAO,KAAK,KAAK,GAAG;AAAA,MACtB;AAGA,UAAI;AACF,cAAM,IAAI,IAAI,KAAK,IAAI;AACvB,YAAI,CAAC,MAAM,EAAE,QAAS,CAAA,GAAG;AACvB,gBAAM,OAAO,EAAE;AACf,gBAAM,QAAQ,OAAO,EAAE,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACtD,gBAAM,MAAM,OAAO,EAAE,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAC/C,iBAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,QAChC;AACA,eAAO,OAAO,IAAI;AAAA,MACpB,SAAS,GAAG;AACVF,sBAAc,MAAA,MAAA,SAAA,yCAAA,YAAY,CAAC;AAC3B,eAAO,OAAO,IAAI;AAAA,MACpB;AAAA,IACD;AAAA;AAAA,IAGD,eAAe,UAAU;AACvB,UAAI,CAAC;AAAU,eAAO;AAGtB,UAAI,MAAM,QAAQ,QAAQ,GAAG;AAC3B,YAAI,SAAS,UAAU,GAAG;AACxB,gBAAM,OAAO,SAAS,CAAC;AACvB,gBAAM,QAAQ,OAAO,SAAS,CAAC,CAAC,EAAE,SAAS,GAAG,GAAG;AACjD,gBAAM,MAAM,OAAO,SAAS,CAAC,CAAC,EAAE,SAAS,GAAG,GAAG;AAG/C,cAAI,SAAS,UAAU,GAAG;AACxB,kBAAM,OAAO,OAAO,SAAS,CAAC,CAAC,EAAE,SAAS,GAAG,GAAG;AAChD,kBAAM,SAAS,OAAO,SAAS,CAAC,CAAC,EAAE,SAAS,GAAG,GAAG;AAClD,kBAAM,SACJ,SAAS,SAAS,IAAI,OAAO,SAAS,CAAC,CAAC,EAAE,SAAS,GAAG,GAAG,IAAI;AAC/D,mBAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM,IAAI,MAAM;AAAA,UAC5D;AAEA,iBAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,QAChC;AACA,eAAO,SAAS,KAAK,GAAG;AAAA,MAC1B;AAGA,YAAM,cAAc,OAAO,QAAQ;AAGnC,UAAI,YAAY,SAAS,GAAG,GAAG;AAC7B,cAAM,OAAO,IAAI,KAAK,WAAW;AACjC,cAAM,OAAO,KAAK;AAClB,cAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,cAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,cAAM,QAAQ,OAAO,KAAK,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AACrD,cAAM,UAAU,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACzD,cAAM,UAAU,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACzD,eAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,OAAO,IAAI,OAAO;AAAA,MAC/D;AAGA,UAAI,CAAC,MAAM,QAAQ,KAAK,OAAO,aAAa,UAAU;AACpD,cAAM,OAAO,IAAI,KAAK,QAAQ;AAC9B,cAAM,OAAO,KAAK;AAClB,cAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,cAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,cAAM,QAAQ,OAAO,KAAK,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AACrD,cAAM,UAAU,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACzD,cAAM,UAAU,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACzD,eAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,OAAO,IAAI,OAAO;AAAA,MAC/D;AAGA,UAAI;AACF,cAAM,OAAO,IAAI,KAAK,WAAW;AACjC,YAAI,CAAC,MAAM,KAAK,QAAS,CAAA,GAAG;AAC1B,gBAAM,OAAO,KAAK;AAClB,gBAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,gBAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,gBAAM,QAAQ,OAAO,KAAK,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AACrD,gBAAM,UAAU,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACzD,gBAAM,UAAU,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AAGzD,cAAI,UAAU,QAAQ,YAAY,QAAQ,YAAY,MAAM;AAC1D,mBAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,OAAO,IAAI,OAAO;AAAA,iBACxD;AACL,mBAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,UAChC;AAAA,QACF;AAGA,eAAO;AAAA,MACT,SAAS,GAAG;AACVA,sBAAA,MAAA,MAAA,SAAA,yCAAc,cAAc,GAAG,WAAW;AAC1C,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,eAAe,UAAU;AACvB,UAAI,CAAC;AAAU,eAAO;AAGtB,YAAM,eAAe,KAAK,eAAe,QAAQ;AAGjD,UAAI,aAAa,SAAS,GAAG,GAAG;AAC9B,eAAO,aAAa,MAAM,GAAG,EAAE,CAAC;AAAA,MAClC;AAEA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,oBAAoB,cAAc;AAChC,UAAI,CAAC;AAAc,eAAO;AAC1B,YAAM,kBAAkB;AAAA,QACtB,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA;AAEV,aAAO,gBAAgB,YAAY,KAAK;AAAA,IACzC;AAAA;AAAA,IAGD,kBAAkB,OAAO;AAEvB,UAAI,KAAK,cAAc,KAAK,KAAK,CAAC,KAAK,cAAc,KAAK,EAAE,eAAe,aAAa,GAAG;AACzF,aAAK,KAAK,KAAK,cAAc,KAAK,GAAG,eAAe,IAAI;AAAA,aACnD;AACL,aAAK,KAAK,KAAK,cAAc,KAAK,GAAG,eAAe,CAAC,KAAK,cAAc,KAAK,EAAE,WAAW;AAAA,MAC5F;AAAA,IACD;AAAA;AAAA,IAGD,eAAe,UAAU;AACvB,UAAI,CAAC,YAAY,CAAC,MAAM,QAAQ,QAAQ;AAAG,eAAO;AAElD,YAAM,eAAe;AAAA,QACnB,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA;AAGL,aAAO,SAAS,IAAI,SAAO,aAAa,GAAG,KAAK,IAAI,GAAG,EAAE,EAAE,KAAK,IAAI;AAAA,IACrE;AAAA;AAAA,IAGD,gBAAgB,WAAW;AACzB,UAAI,CAAC,aAAa,CAAC,MAAM,QAAQ,SAAS;AAAG,eAAO;AACpD,aAAO,UAAU,IAAI,SAAO,GAAG,GAAG,GAAG,EAAE,KAAK,IAAI;AAAA,IACjD;AAAA;AAAA,IAGD,kBAAkB,QAAQ;AACxB,UAAI,KAAK,WAAW,WAAW,WAAW;AACxC,eAAO;AAAA,MACT,WAAW,KAAK,WAAW,WAAW,WAAW;AAC/C,eAAO;AAAA,aACF;AACL,eAAO,OAAO,gBAAgB,WAAW,OAAO;AAAA,MAClD;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACb,YAAM,SAAS,CAAA;AACf,WAAK,cAAc,QAAQ,YAAU;AACnC,YAAI,OAAO,UAAU,MAAM,QAAQ,OAAO,MAAM,GAAG;AACjD,iBAAO,OAAO,QAAQ,SAAO;AAC3B,mBAAO,KAAK;AAAA,cACV,KAAK,KAAK,gBAAgB,GAAG;AAAA,cAC7B,UAAU,OAAO,YAAY,OAAO,OAAO,QAAQ,CAAC;AAAA,YACtD,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,mBAAmB;AACjB,YAAM,OAAO,CAAA;AACb,WAAK,aAAY,EAAG,QAAQ,WAAS;AACnC,aAAK,KAAK,MAAM,GAAG;AAAA,MACrB,CAAC;AACD,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,UAAU,OAAO;AACf,WAAK,aAAa;AAElB,iBAAW,MAAM;AACfA,sBAAAA,MAAI,aAAa;AAAA,UACf,WAAW;AAAA,UACX,UAAU;AAAA,QACZ,CAAC;AAAA,MACF,GAAE,EAAE;AAAA,IACN;AAAA,EACF;AACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjoBA,GAAG,WAAW,eAAe;"}