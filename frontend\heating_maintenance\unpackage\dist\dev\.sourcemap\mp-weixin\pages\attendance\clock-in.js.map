{"version": 3, "file": "clock-in.js", "sources": ["pages/attendance/clock-in.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYXR0ZW5kYW5jZS9jbG9jay1pbi52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"clock-in-container\">\r\n\t\t<!-- 顶部状态栏 -->\r\n\t\t<view class=\"status-header\">\r\n\t\t\t<view class=\"date-info\">\r\n\t\t\t\t<text class=\"date\">{{ currentDate }}</text>\r\n\t\t\t\t<text class=\"time\">{{ currentTime }}</text>\r\n\t\t\t</view>\r\n\t\t\t<!-- 管理员设置按钮 -->\r\n\t\t\t<PermissionCheck permission=\"attendance:clock-rules\">\r\n\t\t\t\t<view class=\"admin-settings\" @click=\"goToAdminSettings\">\r\n\t\t\t\t\t<text class=\"settings-icon\">⚙️</text>\r\n\t\t\t\t\t<text class=\"settings-text\">规则设置</text>\r\n\t\t\t\t</view>\r\n\t\t\t </PermissionCheck>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 打卡状态展示 -->\r\n\t\t<view class=\"clock-status\">\r\n\t\t\t<view class=\"status-card\">\r\n\t\t\t\t<view class=\"work-time\">\r\n\t\t\t\t\t<view class=\"time-item\">\r\n\t\t\t\t\t\t<text class=\"time-label\">上班时间</text>\r\n\t\t\t\t\t\t<text class=\"time-value\">{{ attendanceRules.clockInTime || '08:30' }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"time-divider\"></view>\r\n\t\t\t\t\t<view class=\"time-item\">\r\n\t\t\t\t\t\t<text class=\"time-label\">下班时间</text>\r\n\t\t\t\t\t\t<text class=\"time-value\">{{ attendanceRules.clockOutTime || '17:30' }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"clock-records\">\r\n\t\t\t\t\t<view class=\"record-item\">\r\n\t\t\t\t\t\t<text class=\"record-label\">上班打卡</text>\r\n\t\t\t\t\t\t<text class=\"record-value\" :class=\"{'not-clocked': !clockInTime}\">\r\n\t\t\t\t\t\t\t{{ clockInTime || '未打卡' }}\r\n\t\t\t\t\t\t</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"record-item\">\r\n\t\t\t\t\t\t<text class=\"record-label\">下班打卡</text>\r\n\t\t\t\t\t\t<text class=\"record-value\" :class=\"{'not-clocked': !clockOutTime}\">\r\n\t\t\t\t\t\t\t{{ clockOutTime || '未打卡' }}\r\n\t\t\t\t\t\t</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 打卡按钮区域 -->\r\n\t\t<view class=\"clock-action\">\r\n\t\t\t<!-- 移除位置错误提示和授权按钮 -->\r\n\t\t\t\r\n\t\t\t<view class=\"clock-circle\" @click=\"handleClockIn\">\r\n\t\t\t\t<view class=\"circle-inner\">\r\n\t\t\t\t\t<text class=\"clock-text\">{{ getClockButtonText() }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"action-desc\">\r\n\t\t\t\t<text>{{ getActionDescription() }}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 打卡记录 -->\r\n\t\t<view class=\"recent-records\">\r\n\t\t\t<view class=\"section-title\">最近打卡记录</view>\r\n\t\t\t<view class=\"record-list\">\r\n\t\t\t\t<view class=\"record-empty\" v-if=\"recentRecords.length === 0\">\r\n\t\t\t\t\t<text>暂无打卡记录</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"record-item\" v-for=\"(record, index) in recentRecords\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"record-date\">\r\n\t\t\t\t\t\t<text class=\"date\">{{ record.date }}</text>\r\n\t\t\t\t\t\t<text class=\"week\">{{ record.week }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"record-details\">\r\n\t\t\t\t\t\t<view class=\"detail-item\">\r\n\t\t\t\t\t\t\t<text class=\"detail-label\">上班</text>\r\n\t\t\t\t\t\t\t<text class=\"detail-value\" :class=\"{'abnormal': record.clockInStatus !== 'normal'}\">\r\n\t\t\t\t\t\t\t\t{{ record.clockInTime }}\r\n\t\t\t\t\t\t\t\t<text class=\"status-tag\" v-if=\"record.clockInStatus === 'late'\">迟到</text>\r\n\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"detail-item\">\r\n\t\t\t\t\t\t\t<text class=\"detail-label\">下班</text>\r\n\t\t\t\t\t\t\t<text class=\"detail-value\" :class=\"{'abnormal': record.clockOutStatus !== 'normal'}\">\r\n\t\t\t\t\t\t\t\t{{ record.clockOutTime }}\r\n\t\t\t\t\t\t\t\t<text class=\"status-tag\" v-if=\"record.clockOutStatus === 'early'\">早退</text>\r\n\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 补卡申请弹窗 -->\r\n\t\t<!-- <uni-popup ref=\"supplementPopup\" type=\"center\">\r\n\t\t\t<view class=\"supplement-popup\">\r\n\t\t\t\t<view class=\"popup-title\">补卡申请</view>\r\n\t\t\t\t<view class=\"popup-content\">\r\n\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t<view class=\"form-label\">补卡日期</view>\r\n\t\t\t\t\t\t<view class=\"form-input date-picker\">\r\n\t\t\t\t\t\t\t<picker mode=\"date\" :value=\"supplementDate\" @change=\"supplementDate = $event.detail.value\">\r\n\t\t\t\t\t\t\t\t<view>{{ supplementDate }}</view>\r\n\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t<text class=\"icon-calendar\">📅</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t<view class=\"form-label\">补卡类型</view>\r\n\t\t\t\t\t\t<view class=\"form-input\">\r\n\t\t\t\t\t\t\t<picker :range=\"supplementTypes\" :value=\"supplementTypeIndex\" @change=\"supplementTypeIndex = $event.detail.value\">\r\n\t\t\t\t\t\t\t\t<view>{{ supplementTypes[supplementTypeIndex] }}</view>\r\n\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t<view class=\"form-label\">申请原因</view>\r\n\t\t\t\t\t\t<view class=\"form-input\">\r\n\t\t\t\t\t\t\t<textarea v-model=\"supplementReason\" placeholder=\"请输入补卡原因\" maxlength=\"200\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t<view class=\"form-label\">证明材料</view>\r\n\t\t\t\t\t\t<view class=\"supplement-images\">\r\n\t\t\t\t\t\t\t<view class=\"image-item\" v-for=\"(image, index) in supplementImages\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<image class=\"supplement-image\" :src=\"image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t\t<view class=\"image-delete\" @tap=\"deleteSupplementImage(index)\">×</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"image-add\" @tap=\"chooseSupplementImage\" v-if=\"supplementImages.length < 3\">\r\n\t\t\t\t\t\t\t\t<view class=\"image-add-icon\">+</view>\r\n\t\t\t\t\t\t\t\t<view class=\"image-add-text\">上传</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"popup-footer\">\r\n\t\t\t\t\t\t<button class=\"btn-cancel\" @click=\"$refs.supplementPopup.close()\" :disabled=\"loading\">取消</button>\r\n\t\t\t\t\t\t<button class=\"btn-confirm\" @click=\"submitSupplementApplication\" :disabled=\"loading\">提交</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup> -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { attendanceApi } from '@/utils/api.js';\r\n\timport PermissionCheck from \"@/components/PermissionCheck.vue\"; // 导入权限检查组件\r\n\t\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t  PermissionCheck, // 本地注册组件\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 当前时间相关\r\n\t\t\t\ttimer: null,\r\n\t\t\t\tcurrentDate: '',\r\n\t\t\t\tcurrentTime: '',\r\n\t\t\t\t\r\n\t\t\t\t// 位置相关\r\n\t\t\t\tlatitude: 0, // 默认纬度，使用默认值\r\n\t\t\t\tlongitude: 0, // 默认经度，使用默认值\r\n\t\t\t\tclockStatus: 'normal', // 默认为normal，始终允许打卡\r\n\t\t\t\tlocationError: '', // 存储定位错误信息\r\n\t\t\t\tuseNativeGeolocation: true, // 默认使用原生定位\r\n\t\t\t\tlocationPermissionDenied: false, // 位置权限是否被拒绝\r\n\t\t\t\thasShownLocationError: false, // 是否已经显示过位置错误提醒\r\n\t\t\t\t\r\n\t\t\t\t// 今日打卡记录\r\n\t\t\t\tclockInTime: '',\r\n\t\t\t\tclockOutTime: '',\r\n\t\t\t\t\r\n\t\t\t\t// 补卡相关\r\n\t\t\t\tsupplementDate: this.formatDate(new Date()),\r\n\t\t\t\tsupplementTypes: ['上班打卡', '下班打卡'],\r\n\t\t\t\tsupplementTypeIndex: 0,\r\n\t\t\t\tsupplementReason: '',\r\n\t\t\t\tsupplementImages: [],\r\n\t\t\t\t\r\n\t\t\t\t// 最近打卡记录\r\n\t\t\t\trecentRecords: [],\r\n\t\t\t\t\r\n\t\t\t\t// 考勤规则\r\n\t\t\t\tattendanceRules: {\r\n\t\t\t\t\tclockInTime: '08:30',\r\n\t\t\t\t\tclockOutTime: '17:30',\r\n\t\t\t\t\tallowedDistance: 500, // 允许打卡的距离范围(米)\r\n\t\t\t\t\tlateThreshold: 15, // 迟到阈值(分钟)\r\n\t\t\t\t\tearlyLeaveThreshold: 15, // 早退阈值(分钟)\r\n\t\t\t\t\tlocationUploadInterval: 1 // 位置上传时间间隔(分钟)\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\t// 加载状态\r\n\t\t\t\tloading: false,\r\n\t\t\t\t\r\n\t\t\t\t// 是否是管理员\r\n\t\t\t\tisAdmin: false,\r\n\t\t\t\t\r\n\t\t\t\t// 定位轨迹上传定时器\r\n\t\t\t\tlocationUploadTimer: null,\r\n\t\t\t\tisUploadingLocation: false, // 是否正在上传位置\r\n\t\t\t\tisGettingLocation: false // 是否正在获取位置\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 移除canClockIn计算属性，始终允许打卡\r\n\t\t\tcanClockIn() {\r\n\t\t\t\treturn true; // 始终允许打卡\r\n\t\t\t},\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t// 初始化时间显示\r\n\t\t\tthis.updateDateTime();\r\n\t\t\t\r\n\t\t\t// 设置定时器更新时间\r\n\t\t\tthis.timer = setInterval(() => {\r\n\t\t\t\tthis.updateDateTime();\r\n\t\t\t}, 1000);\r\n\t\t\t\r\n\t\t\t// 初始化错误提醒标志位\r\n\t\t\tthis.hasShownLocationError = false;\r\n\t\t\tthis.isGettingLocation = false;\r\n\t\t\t\r\n\t\t\t// 获取考勤规则（先获取规则）\r\n\t\t\tthis.getAttendanceRules();\r\n\t\t\t\r\n\t\t\t// 获取今日打卡记录\r\n\t\t\tthis.getTodayClockRecord();\r\n\t\t\t\r\n\t\t\t// 获取最近打卡记录\r\n\t\t\tthis.getRecentRecords();\r\n\t\t\t\r\n\t\t\t// 在后台尝试获取位置权限和位置信息，但不阻塞页面加载\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.checkLocationPermission().then(hasPermission => {\r\n\t\t\t\t\tif (hasPermission) {\r\n\t\t\t\t\t\t// 已有权限，尝试获取位置\r\n\t\t\t\t\t\tthis.tryGetLocation();\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 没有权限，静默请求授权\r\n\t\t\t\t\t\tthis.requestLocationPermission();\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}, 500);\r\n\t\t},\r\n\t\tonUnload() {\r\n\t\t\t// 清除定时器\r\n\t\t\tif (this.timer) {\r\n\t\t\t\tclearInterval(this.timer);\r\n\t\t\t\tthis.timer = null;\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShow()\r\n\t\t{\r\n\t\t\t// 获取考勤规则（先获取规则）\r\n\t\t\tthis.getAttendanceRules();\r\n\t\t\t\r\n\t\t\t// 获取今日打卡记录\r\n\t\t\tthis.getTodayClockRecord();\r\n\t\t\t\r\n\t\t\t// 获取最近打卡记录\r\n\t\t\tthis.getRecentRecords();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 启动定位上传\r\n\t\t\tstartLocationUpload() {\r\n\t\t\t\tthis.$store.dispatch('attendance/startLocationUpload');\r\n\t\t\t\tconsole.log('启动全局位置上传');\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 停止定位上传\r\n\t\t\tstopLocationUpload() {\r\n\t\t\t\tthis.$store.dispatch('attendance/stopLocationUpload');\r\n\t\t\t\tconsole.log('停止全局位置上传');\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 更新日期时间\r\n\t\t\tupdateDateTime() {\r\n\t\t\t\tconst now = new Date();\r\n\t\t\t\t\r\n\t\t\t\t// 格式化日期: 2023年01月01日 星期一\r\n\t\t\t\tconst year = now.getFullYear();\r\n\t\t\t\tconst month = String(now.getMonth() + 1).padStart(2, '0');\r\n\t\t\t\tconst day = String(now.getDate()).padStart(2, '0');\r\n\t\t\t\tconst weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];\r\n\t\t\t\tconst weekDay = weekDays[now.getDay()];\r\n\t\t\t\t\r\n\t\t\t\tthis.currentDate = `${year}年${month}月${day}日 ${weekDay}`;\r\n\t\t\t\t\r\n\t\t\t\t// 格式化时间: 08:00:00\r\n\t\t\t\tconst hours = String(now.getHours()).padStart(2, '0');\r\n\t\t\t\tconst minutes = String(now.getMinutes()).padStart(2, '0');\r\n\t\t\t\tconst seconds = String(now.getSeconds()).padStart(2, '0');\r\n\t\t\t\t\r\n\t\t\t\tthis.currentTime = `${hours}:${minutes}:${seconds}`;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取位置信息\r\n\t\t\tgetLocation() {\r\n\t\t\t\t// 如果已经在获取位置中，不要重复请求\r\n\t\t\t\tif (this.isGettingLocation) {\r\n\t\t\t\t\tconsole.log('正在获取位置中，跳过重复请求');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthis.isGettingLocation = true;\r\n\t\t\t\tthis.clockStatus = 'normal'; // 确保按钮不会变灰\r\n\t\t\t\t\r\n\t\t\t\t// 设置超时，确保即使获取位置失败也能继续\r\n\t\t\t\tconst locationTimeout = setTimeout(() => {\r\n\t\t\t\t\tconsole.log('位置获取超时，停止尝试');\r\n\t\t\t\t\tthis.locationError = '位置获取超时';\r\n\t\t\t\t\tthis.clockStatus = 'normal';\r\n\t\t\t\t\tthis.isGettingLocation = false;\r\n\t\t\t\t\t// 超时时尝试直接请求权限\r\n\t\t\t\t\tthis.requestLocationPermission();\r\n\t\t\t\t}, 15000); // 15秒超时\r\n\t\t\t\t\r\n\t\t\t\t// 优先使用原生定位\r\n\t\t\t\tif (this.useNativeGeolocation) {\r\n\t\t\t\t\t// 尝试使用原生定位\r\n\t\t\t\t\tthis.getNativeLocation().then(() => {\r\n\t\t\t\t\t\tclearTimeout(locationTimeout);\r\n\t\t\t\t\t\tthis.isGettingLocation = false;\r\n\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\tconsole.error('原生定位失败，尝试使用uni定位:', err);\r\n\t\t\t\t\t\tthis.useNativeGeolocation = false;\r\n\t\t\t\t\t\t// 如果原生定位失败，回退到uni定位\r\n\t\t\t\t\t\tthis.getUniLocation().then(() => {\r\n\t\t\t\t\t\t\tclearTimeout(locationTimeout);\r\n\t\t\t\t\t\t\tthis.isGettingLocation = false;\r\n\t\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\t\tclearTimeout(locationTimeout);\r\n\t\t\t\t\t\t\tthis.handleLocationError(err);\r\n\t\t\t\t\t\t\tthis.isGettingLocation = false;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 直接使用uni定位\r\n\t\t\t\t\tthis.getUniLocation().then(() => {\r\n\t\t\t\t\t\tclearTimeout(locationTimeout);\r\n\t\t\t\t\t\tthis.isGettingLocation = false;\r\n\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\tclearTimeout(locationTimeout);\r\n\t\t\t\t\t\tthis.handleLocationError(err);\r\n\t\t\t\t\t\tthis.isGettingLocation = false;\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 使用原生定位API获取位置\r\n\t\t\tgetNativeLocation() {\r\n\t\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tplus.geolocation.getCurrentPosition(\r\n\t\t\t\t\t\t\t(position) => {\r\n\t\t\t\t\t\t\t\tconsole.log('原生定位成功:', position);\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t// 提取位置信息\r\n\t\t\t\t\t\t\t\tconst coords = position.coords;\r\n\t\t\t\t\t\t\t\tthis.latitude = coords.latitude;\r\n\t\t\t\t\t\t\t\tthis.longitude = coords.longitude;\r\n\t\t\t\t\t\t\t\tthis.locationError = ''; // 清除错误信息\r\n\t\t\t\t\t\t\t\tthis.locationPermissionDenied = false;\r\n\t\t\t\t\t\t\t\tthis.clockStatus = 'normal';\r\n\t\t\t\t\t\t\t\tthis.hasShownLocationError = false; // 重置提醒标志\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\tresolve(position);\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t(err) => {\r\n\t\t\t\t\t\t\t\tconsole.error('原生定位失败:', err);\r\n\t\t\t\t\t\t\t\treject({\r\n\t\t\t\t\t\t\t\t\terrMsg: err.message || '获取位置失败',\r\n\t\t\t\t\t\t\t\t\tdetail: err\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tenableHighAccuracy: true, // 高精度定位\r\n\t\t\t\t\t\t\t\ttimeout: 15000, // 超时时间\r\n\t\t\t\t\t\t\t\tmaximumAge: 0, // 不使用缓存\r\n\t\t\t\t\t\t\t\tprovider: 'system', // 使用系统定位\r\n\t\t\t\t\t\t\t\tgeocode: false // 不获取地理编码信息\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t);\r\n\t\t\t\t\t} catch (e) {\r\n\t\t\t\t\t\tconsole.error('调用原生定位API异常:', e);\r\n\t\t\t\t\t\treject({\r\n\t\t\t\t\t\t\terrMsg: '调用定位服务失败',\r\n\t\t\t\t\t\t\tdetail: e.message\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t\t// 非APP环境下，使用uni定位API\r\n\t\t\t\t\tthis.getUniLocation().then(resolve).catch(reject);\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 使用uni.getLocation获取位置\r\n\t\t\tgetUniLocation() {\r\n\t\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\t\tuni.getLocation({\r\n\t\t\t\t\t\ttype: 'gcj02',\r\n\t\t\t\t\t\tisHighAccuracy: true,\r\n\t\t\t\t\t\thighAccuracyExpireTime: 5000,\r\n\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\tconsole.log('uni位置获取成功:', res);\r\n\t\t\t\t\t\t\tthis.latitude = res.latitude;\r\n\t\t\t\t\t\t\tthis.longitude = res.longitude;\r\n\t\t\t\t\t\t\tthis.locationError = ''; // 清除错误信息\r\n\t\t\t\t\t\t\tthis.locationPermissionDenied = false;\r\n\t\t\t\t\t\t\tthis.clockStatus = 'normal';\r\n\t\t\t\t\t\t\tthis.hasShownLocationError = false; // 重置提醒标志\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tresolve(res);\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\tconsole.error('uni获取位置失败:', err);\r\n\t\t\t\t\t\t\treject(err);\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tcomplete: () => {\r\n\t\t\t\t\t\t\t// 完成位置获取，无论成功失败\r\n\t\t\t\t\t\t\tconsole.log('位置获取完成');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 检查位置权限\r\n\t\t\tcheckLocationPermission() {\r\n\t\t\t\treturn new Promise((resolve) => {\r\n\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\tif (plus.os.name.toLowerCase() === 'android') {\r\n\t\t\t\t\t\tconst mainActivity = plus.android.runtimeMainActivity();\r\n\t\t\t\t\t\tconst PackageManager = plus.android.importClass(\"android.content.pm.PackageManager\");\r\n\t\t\t\t\t\tconst ContextCompat = plus.android.importClass(\"androidx.core.content.ContextCompat\");\r\n\t\t\t\t\t\tconst Manifest = plus.android.importClass(\"android.Manifest\");\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 检查是否有精确位置权限\r\n\t\t\t\t\t\tconst hasFineLocationPermission = ContextCompat.checkSelfPermission(mainActivity, Manifest.permission.ACCESS_FINE_LOCATION) === PackageManager.PERMISSION_GRANTED;\r\n\t\t\t\t\t\t// 检查是否有粗略位置权限\r\n\t\t\t\t\t\tconst hasCoarseLocationPermission = ContextCompat.checkSelfPermission(mainActivity, Manifest.permission.ACCESS_COARSE_LOCATION) === PackageManager.PERMISSION_GRANTED;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 如果有任一位置权限，判断为有权限\r\n\t\t\t\t\t\tresolve(hasFineLocationPermission || hasCoarseLocationPermission);\r\n\t\t\t\t\t} else if (plus.os.name.toLowerCase() === 'ios') {\r\n\t\t\t\t\t\t// iOS平台\r\n\t\t\t\t\t\tconst cllocationMgr = plus.ios.import(\"CLLocationManager\");\r\n\t\t\t\t\t\tconst status = cllocationMgr.authorizationStatus();\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// kCLAuthorizationStatusAuthorizedAlways: 4\r\n\t\t\t\t\t\t// kCLAuthorizationStatusAuthorizedWhenInUse: 3\r\n\t\t\t\t\t\tconst hasPermission = (status === 4 || status === 3);\r\n\t\t\t\t\t\tplus.ios.deleteObject(cllocationMgr);\r\n\t\t\t\t\t\tresolve(hasPermission);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 其他平台，假设没有权限\r\n\t\t\t\t\t\tresolve(false);\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\t// H5环境\r\n\t\t\t\t\tif (navigator.geolocation) {\r\n\t\t\t\t\t\t// 尝试使用权限API查询权限状态\r\n\t\t\t\t\t\tif (navigator.permissions && navigator.permissions.query) {\r\n\t\t\t\t\t\t\tnavigator.permissions.query({name: 'geolocation'}).then(permissionStatus => {\r\n\t\t\t\t\t\t\t\tif (permissionStatus.state === 'granted') {\r\n\t\t\t\t\t\t\t\t\tresolve(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tresolve(false);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\t\t\t// 如果无法查询权限，假设没有权限\r\n\t\t\t\t\t\t\t\tresolve(false);\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 不支持权限API，假设没有权限\r\n\t\t\t\t\t\t\tresolve(false);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 浏览器不支持地理位置API\r\n\t\t\t\t\t\tresolve(false);\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t\r\n\t\t\t\t\t// #ifdef MP-WEIXIN || MP-ALIPAY\r\n\t\t\t\t\t// 微信小程序或支付宝小程序\r\n\t\t\t\t\tuni.getSetting({\r\n\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\tresolve(res.authSetting['scope.userLocation'] === true);\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\t\tresolve(false);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 其他平台，假设没有权限\r\n\t\t\t\t\t// #ifndef APP-PLUS || H5 || MP-WEIXIN || MP-ALIPAY\r\n\t\t\t\t\tresolve(false);\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 处理位置错误\r\n\t\t\thandleLocationError(err) {\r\n\t\t\t\tconsole.log('位置获取失败:', err);\r\n\t\t\t\tthis.clockStatus = 'outside';\r\n\t\t\t\tthis.locationError = '无法获取位置，请授权位置权限';\r\n\t\t\t\t\r\n\t\t\t\t// 检查位置权限\r\n\t\t\t\tthis.checkLocationPermission().then(hasPermission => {\r\n\t\t\t\t\tif (!hasPermission) {\r\n\t\t\t\t\t\t// 没有权限，请求授权\r\n\t\t\t\t\t\tthis.requestLocationPermission();\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 有权限但获取位置失败，可能是GPS未开启\r\n\t\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\t\tif (plus.os.name.toLowerCase() === 'android') {\r\n\t\t\t\t\t\t\t// 检查GPS是否开启\r\n\t\t\t\t\t\t\tconst mainActivity = plus.android.runtimeMainActivity();\r\n\t\t\t\t\t\t\tconst locationManager = plus.android.importClass(\"android.location.LocationManager\");\r\n\t\t\t\t\t\t\tconst locationService = mainActivity.getSystemService(locationManager);\r\n\t\t\t\t\t\t\tconst isGPSEnabled = locationService.isProviderEnabled(locationManager.GPS_PROVIDER);\r\n\t\t\t\t\t\t\tconst isNetworkEnabled = locationService.isProviderEnabled(locationManager.NETWORK_PROVIDER);\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tif (!isGPSEnabled && !isNetworkEnabled) {\r\n\t\t\t\t\t\t\t\t// GPS未开启，提示开启GPS\r\n\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\ttitle: '定位服务未开启',\r\n\t\t\t\t\t\t\t\t\tcontent: '请开启GPS定位服务',\r\n\t\t\t\t\t\t\t\t\tconfirmText: '去开启',\r\n\t\t\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\t\t\t// 跳转到位置设置页面\r\n\t\t\t\t\t\t\t\t\t\t\tconst Intent = plus.android.importClass('android.content.Intent');\r\n\t\t\t\t\t\t\t\t\t\t\tconst Settings = plus.android.importClass('android.provider.Settings');\r\n\t\t\t\t\t\t\t\t\t\t\tconst intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);\r\n\t\t\t\t\t\t\t\t\t\t\tmainActivity.startActivity(intent);\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\tthis.isGettingLocation = false;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 请求位置权限\r\n\t\t\trequestLocationPermission() {\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tif (plus.os.name.toLowerCase() === 'android') {\r\n\t\t\t\t\t// 静默申请权限，不显示任何提示\r\n\t\t\t\t\tconst Manifest = plus.android.importClass(\"android.Manifest\");\r\n\t\t\t\t\tplus.android.requestPermissions(\r\n\t\t\t\t\t\t[Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION],\r\n\t\t\t\t\t\t(resultObj) => {\r\n\t\t\t\t\t\t\tif (resultObj.granted.indexOf(Manifest.permission.ACCESS_FINE_LOCATION) > -1 ||\r\n\t\t\t\t\t\t\t\tresultObj.granted.indexOf(Manifest.permission.ACCESS_COARSE_LOCATION) > -1) {\r\n\t\t\t\t\t\t\t\t// 权限获取成功，尝试获取位置\r\n\t\t\t\t\t\t\t\tthis.tryGetLocation();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// 如果权限被拒绝，不做任何提示，使用默认位置\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t);\r\n\t\t\t\t} else if (plus.os.name.toLowerCase() === 'ios') {\r\n\t\t\t\t\t// iOS平台，直接请求权限\r\n\t\t\t\t\tconst cllocationMgr = plus.ios.import(\"CLLocationManager\");\r\n\t\t\t\t\tconst mgr = new cllocationMgr();\r\n\t\t\t\t\tmgr.requestWhenInUseAuthorization(); // 请求使用期间访问位置\r\n\t\t\t\t\tplus.ios.deleteObject(mgr);\r\n\t\t\t\t\tplus.ios.deleteObject(cllocationMgr);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 延迟后尝试获取位置\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.tryGetLocation();\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\t\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\t// H5环境\r\n\t\t\t\tif (navigator.geolocation) {\r\n\t\t\t\t\tnavigator.geolocation.getCurrentPosition(\r\n\t\t\t\t\t\t(position) => {\r\n\t\t\t\t\t\t\t// 成功获取位置，更新数据\r\n\t\t\t\t\t\t\tthis.latitude = position.coords.latitude;\r\n\t\t\t\t\t\t\tthis.longitude = position.coords.longitude;\r\n\t\t\t\t\t\t\tthis.locationError = '';\r\n\t\t\t\t\t\t\tthis.hasShownLocationError = false;\r\n\t\t\t\t\t\t\tthis.clockStatus = 'normal';\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t() => {\r\n\t\t\t\t\t\t\t// 如果用户拒绝，不做任何提示，使用默认位置\r\n\t\t\t\t\t\t\tconsole.log('H5环境获取位置失败，使用默认位置');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t);\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\t\r\n\t\t\t\t// #ifdef MP-WEIXIN || MP-ALIPAY\r\n\t\t\t\t// 微信小程序或支付宝小程序\r\n\t\t\t\tuni.authorize({\r\n\t\t\t\t\tscope: 'scope.userLocation',\r\n\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t// 授权成功，获取位置\r\n\t\t\t\t\t\tthis.tryGetLocation();\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\t// 授权失败，不做任何提示，使用默认位置\r\n\t\t\t\t\t\tconsole.log('小程序环境获取位置授权失败，使用默认位置');\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 打开系统位置设置\r\n\t\t\topenLocationSettings() {\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tif (plus.os.name.toLowerCase() === 'android') {\r\n\t\t\t\t\tconst Intent = plus.android.importClass('android.content.Intent');\r\n\t\t\t\t\tconst Settings = plus.android.importClass('android.provider.Settings');\r\n\t\t\t\t\tconst mainActivity = plus.android.runtimeMainActivity();\r\n\t\t\t\t\tconst intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);\r\n\t\t\t\t\tmainActivity.startActivity(intent);\r\n\t\t\t\t} else if (plus.os.name.toLowerCase() === 'ios') {\r\n\t\t\t\t\tconst UIApplication = plus.ios.import(\"UIApplication\");\r\n\t\t\t\t\tconst application = UIApplication.sharedApplication();\r\n\t\t\t\t\tconst NSURL = plus.ios.import(\"NSURL\");\r\n\t\t\t\t\tconst setting_url = NSURL.URLWithString(\"app-settings:\");\r\n\t\t\t\t\tapplication.openURL(setting_url);\r\n\t\t\t\t\tplus.ios.deleteObject(setting_url);\r\n\t\t\t\t\tplus.ios.deleteObject(application);\r\n\t\t\t\t\tplus.ios.deleteObject(UIApplication);\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 检查是否在考勤范围内\r\n\t\t\tcheckAttendanceArea(latitude, longitude) {\r\n\t\t\t\t// 如果后端API不可用，使用本地判断\r\n\t\t\t\tconst checkLocal = () => {\r\n\t\t\t\t\t// 假设公司位置\r\n\t\t\t\t\tconst companyLocation = {\r\n\t\t\t\t\t\tlatitude: 34.341576, // 根据实际情况修改\r\n\t\t\t\t\t\tlongitude: 108.940174 // 根据实际情况修改\r\n\t\t\t\t\t};\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 计算距离\r\n\t\t\t\t\tconst distance = this.calculateDistance(\r\n\t\t\t\t\t\tlatitude, longitude,\r\n\t\t\t\t\t\tcompanyLocation.latitude, companyLocation.longitude\r\n\t\t\t\t\t);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 判断是否在范围内（假设500米内可打卡）\r\n\t\t\t\t\tconst inArea = distance <= this.attendanceRules.allowedDistance;\r\n\t\t\t\t\tthis.clockStatus = inArea ? 'normal' : 'outside';\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (!inArea) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: `您距离考勤点${Math.round(distance)}米，不在打卡范围内`,\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t};\r\n\t\t\t\t\r\n\t\t\t\t// 调用后端API检查\r\n\t\t\t\tattendanceApi.checkAttendanceArea({\r\n\t\t\t\t\tlatitude,\r\n\t\t\t\t\tlongitude\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\t\tthis.clockStatus = res.data.inArea ? 'normal' : 'outside';\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 如果不在范围内，显示距离信息\r\n\t\t\t\t\t\tif (!res.data.inArea && res.data.distance) {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: `您距离考勤点${res.data.distance}米，不在打卡范围内`,\r\n\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 如果API返回错误，使用本地判断\r\n\t\t\t\t\t\tcheckLocal();\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tconsole.error('考勤范围校验异常:', err);\r\n\t\t\t\t\t// API调用失败，使用本地判断\r\n\t\t\t\t\tcheckLocal();\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 计算两点之间的距离（米）\r\n\t\t\tcalculateDistance(lat1, lon1, lat2, lon2) {\r\n\t\t\t\tconst R = 6371000; // 地球半径，单位米\r\n\t\t\t\tconst dLat = this.deg2rad(lat2 - lat1);\r\n\t\t\t\tconst dLon = this.deg2rad(lon2 - lon1);\r\n\t\t\t\tconst a = \r\n\t\t\t\t\tMath.sin(dLat/2) * Math.sin(dLat/2) +\r\n\t\t\t\t\tMath.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) * \r\n\t\t\t\t\tMath.sin(dLon/2) * Math.sin(dLon/2);\r\n\t\t\t\tconst c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));\r\n\t\t\t\tconst distance = R * c;\r\n\t\t\t\treturn distance;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 角度转弧度\r\n\t\t\tdeg2rad(deg) {\r\n\t\t\t\treturn deg * (Math.PI/180);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取考勤规则\r\n\t\t\tgetAttendanceRules() {\r\n\t\t\t\tattendanceApi.getClockRules()\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tif (res.code === 200 && res.data) {\r\n\t\t\t\t\t\t\tconsole.log('获取考勤规则成功:', res.data);\r\n\t\t\t\t\t\t\tthis.attendanceRules = {\r\n\t\t\t\t\t\t\t\t...this.attendanceRules,\r\n\t\t\t\t\t\t\t\t...res.data\r\n\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tconsole.error('获取考勤规则失败:', err);\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取今日打卡记录\r\n\t\t\tgetTodayClockRecord() {\r\n\t\t\t\tattendanceApi.getTodayRecord()\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tconsole.log('获取今日打卡记录:', res);\r\n\t\t\t\t\t\tif (res.code === 200 && res.data) {\r\n\t\t\t\t\t\t\tthis.clockInTime = res.data.clockInTime || '';\r\n\t\t\t\t\t\t\tthis.clockOutTime = res.data.clockOutTime || '';\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 如果已有上班打卡记录，显示打卡状态\r\n\t\t\t\t\t\t\tif (this.clockInTime && res.data.clockInStatus) {\r\n\t\t\t\t\t\t\t\tconst statusText = res.data.clockInStatus === 'late' ? '（迟到）' : '';\r\n\t\t\t\t\t\t\t\tthis.clockInTime = this.clockInTime + statusText;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 如果已有下班打卡记录，显示打卡状态\r\n\t\t\t\t\t\t\tif (this.clockOutTime && res.data.clockOutStatus) {\r\n\t\t\t\t\t\t\t\tconst statusText = res.data.clockOutStatus === 'early' ? '（早退）' : '';\r\n\t\t\t\t\t\t\t\tthis.clockOutTime = this.clockOutTime + statusText;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tconsole.error('获取今日打卡记录失败:', err);\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取最近打卡记录\r\n\t\t\tgetRecentRecords() {\r\n\t\t\t\tattendanceApi.getRecentRecords(7)  // 获取最近7天的记录\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tif (res.code === 200 && res.data && Array.isArray(res.data.records)) {\r\n\t\t\t\t\t\t\tthis.recentRecords = res.data.records.map(item => {\r\n\t\t\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t\t\tdate: item.date,\r\n\t\t\t\t\t\t\t\t\tweek: item.week,\r\n\t\t\t\t\t\t\t\t\tclockInTime: item.clockInTime || '未打卡',\r\n\t\t\t\t\t\t\t\t\tclockOutTime: item.clockOutTime || '未打卡',\r\n\t\t\t\t\t\t\t\t\tclockInStatus: item.clockInStatus || 'normal',\r\n\t\t\t\t\t\t\t\t\tclockOutStatus: item.clockOutStatus || 'normal'\r\n\t\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 处理返回数据格式不正确的情况\r\n\t\t\t\t\t\t\tconsole.warn('获取最近打卡记录返回数据格式异常:', res);\r\n\t\t\t\t\t\t\tthis.recentRecords = [];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tconsole.error('获取最近打卡记录失败:', err);\r\n\t\t\t\t\t\tthis.recentRecords = []; // 确保在错误时也将数组清空\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '获取打卡记录失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 处理打卡\r\n\t\t\thandleClockIn() {\r\n\t\t\t\t// 判断是上班打卡还是下班打卡\r\n\t\t\t\tconst isClockIn = !this.clockInTime;\r\n\t\t\t\tconst isClockOut = !!this.clockInTime && !this.clockOutTime;\r\n\t\t\t\t\r\n\t\t\t\tif (!isClockIn && !isClockOut) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '今日打卡已完成',\r\n\t\t\t\t\t\ticon: 'info'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 先尝试获取最新位置，但不阻止打卡流程\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '打卡中...'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 尝试获取位置，但不管成功与否都继续打卡\r\n\t\t\t\tthis.tryGetLocation().finally(() => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthis.submitAttendance();\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 尝试获取位置，但不影响打卡流程\r\n\t\t\ttryGetLocation() {\r\n\t\t\t\treturn new Promise((resolve) => {\r\n\t\t\t\t\t// 如果已经在获取位置中，直接返回\r\n\t\t\t\t\tif (this.isGettingLocation) {\r\n\t\t\t\t\t\tconsole.log('正在获取位置中，跳过重复请求');\r\n\t\t\t\t\t\tresolve();\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.isGettingLocation = true;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 设置超时，确保不会因为位置获取而阻塞\r\n\t\t\t\t\tconst locationTimeout = setTimeout(() => {\r\n\t\t\t\t\t\tconsole.log('位置获取超时，使用默认位置');\r\n\t\t\t\t\t\tthis.isGettingLocation = false;\r\n\t\t\t\t\t\tresolve();\r\n\t\t\t\t\t}, 3000); // 缩短到3秒超时\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 尝试使用原生定位\r\n\t\t\t\t\tif (this.useNativeGeolocation) {\r\n\t\t\t\t\t\tthis.getNativeLocation().then(() => {\r\n\t\t\t\t\t\t\tclearTimeout(locationTimeout);\r\n\t\t\t\t\t\t\tthis.isGettingLocation = false;\r\n\t\t\t\t\t\t\tresolve();\r\n\t\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\t\t// 如果原生定位失败，尝试uni定位\r\n\t\t\t\t\t\t\tthis.getUniLocation().then(() => {\r\n\t\t\t\t\t\t\t\tclearTimeout(locationTimeout);\r\n\t\t\t\t\t\t\t\tthis.isGettingLocation = false;\r\n\t\t\t\t\t\t\t\tresolve();\r\n\t\t\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\t\t\t// 如果都失败了，使用默认位置\r\n\t\t\t\t\t\t\t\tclearTimeout(locationTimeout);\r\n\t\t\t\t\t\t\t\tthis.isGettingLocation = false;\r\n\t\t\t\t\t\t\t\tresolve();\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 直接使用uni定位\r\n\t\t\t\t\t\tthis.getUniLocation().then(() => {\r\n\t\t\t\t\t\t\tclearTimeout(locationTimeout);\r\n\t\t\t\t\t\t\tthis.isGettingLocation = false;\r\n\t\t\t\t\t\t\tresolve();\r\n\t\t\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t\t\t// 如果失败了，使用默认位置\r\n\t\t\t\t\t\t\tclearTimeout(locationTimeout);\r\n\t\t\t\t\t\t\tthis.isGettingLocation = false;\r\n\t\t\t\t\t\t\tresolve();\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 同时尝试请求位置权限，但不阻塞流程\r\n\t\t\t\t\tthis.checkLocationPermission().then(hasPermission => {\r\n\t\t\t\t\t\tif (!hasPermission) {\r\n\t\t\t\t\t\t\tthis.requestLocationPermission();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 显示补卡申请弹窗\r\n\t\t\tshowSupplementModal() {\r\n\t\t\t\t// 重置表单数据\r\n\t\t\t\tthis.resetSupplementForm();\r\n\t\t\t\tthis.$refs.supplementPopup.open();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 重置补卡表单\r\n\t\t\tresetSupplementForm() {\r\n\t\t\t\tthis.supplementDate = this.formatDate(new Date());\r\n\t\t\t\tthis.supplementTypeIndex = 0;\r\n\t\t\t\tthis.supplementReason = '';\r\n\t\t\t\tthis.supplementImages = [];\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 选择补卡证明图片\r\n\t\t\tchooseSupplementImage() {\r\n\t\t\t\tuni.chooseImage({\r\n\t\t\t\t\tcount: 1,\r\n\t\t\t\t\tsizeType: ['compressed'],\r\n\t\t\t\t\tsourceType: ['album', 'camera'],\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t// 限制最多3张图片\r\n\t\t\t\t\t\tif (this.supplementImages.length < 3) {\r\n\t\t\t\t\t\t\tthis.supplementImages.push(res.tempFilePaths[0]);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '最多上传3张图片',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 删除补卡证明图片\r\n\t\t\tdeleteSupplementImage(index) {\r\n\t\t\t\tthis.supplementImages.splice(index, 1);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 提交补卡申请\r\n\t\t\tsubmitSupplementApplication() {\r\n\t\t\t\t// 表单验证\r\n\t\t\t\tif (!this.supplementDate) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请选择补卡日期',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (!this.supplementReason || this.supplementReason.trim() === '') {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入补卡原因',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 显示加载状态\r\n\t\t\t\tthis.loading = true;\r\n\t\t\t\t\r\n\t\t\t\t// 准备要提交的数据\r\n\t\t\t\tconst formData = {\r\n\t\t\t\t\tuserId: this.getUserId(),\r\n\t\t\t\t\tdate: this.supplementDate,\r\n\t\t\t\t\ttype: this.supplementTypeIndex + 1, // 1-上班打卡, 2-下班打卡\r\n\t\t\t\t\treason: this.supplementReason,\r\n\t\t\t\t\timages: []\r\n\t\t\t\t};\r\n\t\t\t\t\r\n\t\t\t\t// 先上传图片，再提交表单\r\n\t\t\t\tif (this.supplementImages.length > 0) {\r\n\t\t\t\t\tthis.uploadSupplementImages().then(imageUrls => {\r\n\t\t\t\t\t\tformData.images = imageUrls;\r\n\t\t\t\t\t\tthis.sendSupplementRequest(formData);\r\n\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '图片上传失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.sendSupplementRequest(formData);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 上传补卡证明图片\r\n\t\t\tuploadSupplementImages() {\r\n\t\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\t\tconst uploadTasks = this.supplementImages.map(imagePath => {\r\n\t\t\t\t\t\treturn new Promise((uploadResolve, uploadReject) => {\r\n\t\t\t\t\t\t\tuni.uploadFile({\r\n\t\t\t\t\t\t\t\turl: this.$api.baseUrl + '/attendance/upload',\r\n\t\t\t\t\t\t\t\tfilePath: imagePath,\r\n\t\t\t\t\t\t\t\tname: 'file',\r\n\t\t\t\t\t\t\t\theader: {\r\n\t\t\t\t\t\t\t\t\ttoken: uni.getStorageSync('token')\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\t\tif (res.statusCode === 200) {\r\n\t\t\t\t\t\t\t\t\t\tconst data = JSON.parse(res.data);\r\n\t\t\t\t\t\t\t\t\t\tif (data.code === 0 && data.data) {\r\n\t\t\t\t\t\t\t\t\t\t\tuploadResolve(data.data.url);\r\n\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\tuploadReject(new Error(data.msg || '上传失败'));\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tuploadReject(new Error('上传失败'));\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\t\tuploadReject(err);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\tPromise.all(uploadTasks).then(resolve).catch(reject);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 发送补卡申请请求\r\n\t\t\tsendSupplementRequest(formData) {\r\n\t\t\t\tattendanceApi.submitSupplementApplication(formData).then(res => {\r\n\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\tif (res.code === 0) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.msg || '补卡申请已提交',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthis.$refs.supplementPopup.close();\r\n\t\t\t\t\t\tthis.resetSupplementForm();\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 刷新打卡记录\r\n\t\t\t\t\t\tthis.getTodayClockRecord();\r\n\t\t\t\t\t\tthis.getRecentRecords();\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.msg || '补卡申请失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\tconsole.error('补卡申请失败:', err);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '补卡申请失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取打卡按钮文本\r\n\t\t\tgetClockButtonText() {\r\n\t\t\t\tif (!this.clockInTime) {\r\n\t\t\t\t\treturn '上班打卡';\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (!this.clockOutTime) {\r\n\t\t\t\t\treturn '下班打卡';\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\treturn '今日打卡已完成';\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取操作描述\r\n\t\t\tgetActionDescription() {\r\n\t\t\t\tconst now = new Date();\r\n\t\t\t\tconst currentHour = now.getHours();\r\n\t\t\t\tconst currentMinute = now.getMinutes();\r\n\t\t\t\t\r\n\t\t\t\tif (!this.clockInTime) {\r\n\t\t\t\t\t// 上班打卡判断\r\n\t\t\t\t\tconst clockInTimeArr = (this.attendanceRules.clockInTime || '08:30').split(':');\r\n\t\t\t\t\tconst clockInHour = parseInt(clockInTimeArr[0]);\r\n\t\t\t\t\tconst clockInMinute = parseInt(clockInTimeArr[1]);\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (currentHour > clockInHour || (currentHour === clockInHour && currentMinute > clockInMinute)) {\r\n\t\t\t\t\t\treturn `已超过上班时间${this.attendanceRules.clockInTime}，打卡将记为迟到`;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn `上班时间${this.attendanceRules.clockInTime}，请按时打卡`;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (!this.clockOutTime) {\r\n\t\t\t\t\t// 下班打卡判断\r\n\t\t\t\t\tconst clockOutTimeArr = (this.attendanceRules.clockOutTime || '17:30').split(':');\r\n\t\t\t\t\tconst clockOutHour = parseInt(clockOutTimeArr[0]);\r\n\t\t\t\t\tconst clockOutMinute = parseInt(clockOutTimeArr[1]);\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (currentHour < clockOutHour || (currentHour === clockOutHour && currentMinute < clockOutMinute)) {\r\n\t\t\t\t\t\treturn `下班时间${this.attendanceRules.clockOutTime}，提前打卡将记为早退`;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn `您已完成上班打卡，下班时间${this.attendanceRules.clockOutTime}`;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn '您已完成今日打卡';\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 格式化日期为YYYY-MM-DD\r\n\t\t\tformatDate(date) {\r\n\t\t\t\tconst year = date.getFullYear();\r\n\t\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\r\n\t\t\t\tconst day = String(date.getDate()).padStart(2, '0');\r\n\t\t\t\treturn `${year}-${month}-${day}`;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取用户ID\r\n\t\t\tgetUserId() {\r\n\t\t\t\tconst userId = uni.getStorageSync('userId');\r\n\t\t\t\tif (!userId) {\r\n\t\t\t\t\tconsole.warn('未找到用户ID，使用默认值1');\r\n\t\t\t\t\treturn 1; // 如果没有找到用户ID，返回默认值1\r\n\t\t\t\t}\r\n\t\t\t\treturn userId || 1; // 如果转换失败，返回默认值1\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 切换定位方式\r\n\t\t\ttoggleLocationMethod() {\r\n\t\t\t\tthis.useNativeGeolocation = !this.useNativeGeolocation;\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: `已切换为${this.useNativeGeolocation ? '原生' : '统一'}定位API`,\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 重新获取位置\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.getLocation();\r\n\t\t\t\t}, 500);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 提交考勤打卡\r\n\t\t\tsubmitAttendance() {\r\n\t\t\t\tconst now = new Date();\r\n\t\t\t\tconst clockType = !this.clockInTime ? 'checkin' : 'checkout';\r\n\t\t\t\t\r\n\t\t\t\t// 判断打卡状态\r\n\t\t\t\tlet status = 'normal';\r\n\t\t\t\t\r\n\t\t\t\tif (clockType === 'checkin') {\r\n\t\t\t\t\t// 上班打卡：判断是否迟到\r\n\t\t\t\t\tconst clockInTimeArr = this.attendanceRules.clockInTime.split(':');\r\n\t\t\t\t\tconst clockInHour = parseInt(clockInTimeArr[0]);\r\n\t\t\t\t\tconst clockInMinute = parseInt(clockInTimeArr[1]);\r\n\t\t\t\t\tconst clockInTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), clockInHour, clockInMinute);\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (now > clockInTime) {\r\n\t\t\t\t\t\tstatus = 'late';\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (clockType === 'checkout') {\r\n\t\t\t\t\t// 下班打卡：判断是否早退\r\n\t\t\t\t\tconst clockOutTimeArr = this.attendanceRules.clockOutTime.split(':');\r\n\t\t\t\t\tconst clockOutHour = parseInt(clockOutTimeArr[0]);\r\n\t\t\t\t\tconst clockOutMinute = parseInt(clockOutTimeArr[1]);\r\n\t\t\t\t\tconst clockOutTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), clockOutHour, clockOutMinute);\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (now < clockOutTime) {\r\n\t\t\t\t\t\tstatus = 'early';\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthis.loading = true;\r\n\t\t\t\t\r\n\t\t\t\t// 显示加载提示\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '打卡中...'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 保存当前经纬度，用于打卡成功后上传\r\n\t\t\t\tconst currentLocation = {\r\n\t\t\t\t\tlatitude: this.latitude,\r\n\t\t\t\t\tlongitude: this.longitude\r\n\t\t\t\t};\r\n\t\t\t\t\r\n\t\t\t\tattendanceApi.submitClock({\r\n\t\t\t\t\tclock_type: clockType,\r\n\t\t\t\t\tlatitude: this.latitude,\r\n\t\t\t\t\tlongitude: this.longitude,\r\n\t\t\t\t\tstatus: status, // 打卡状态\r\n\t\t\t\t\tuser_id: this.getUserId() // 添加用户ID\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: clockType === 'checkin' ? '上班打卡成功' : '下班打卡成功',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 更新打卡记录\r\n\t\t\t\t\t\tthis.getTodayClockRecord();\r\n\t\t\t\t\t\tthis.getRecentRecords();\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 尝试上传当前位置记录，但不阻塞流程\r\n\t\t\t\t\t\tthis.uploadTrajectoryRecord(currentLocation.latitude, currentLocation.longitude)\r\n\t\t\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\t\t\tconsole.error('打卡后上传位置失败:', err);\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 获取定位上传周期配置（单位：分钟）\r\n\t\t\t\t\t\tconst positionCycle = uni.getStorageSync('positionCycle') || 1;\r\n\r\n\t\t\t\t\t\t// 如果是上班打卡，则启动位置上传\r\n\t\t\t\t\t\tif (clockType === 'checkin') {\r\n\t\t\t\t\t\t\tthis.startLocationUpload();\r\n\t\t\t\t\t\t\tconsole.log('上班打卡成功，启动全局位置上传，上传周期:', positionCycle, '分钟');\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 如果是下班打卡，则停止位置上传\r\n\t\t\t\t\t\t\tthis.stopLocationUpload();\r\n\t\t\t\t\t\t\tconsole.log('下班打卡成功，停止全局位置上传');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.message || '打卡失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tconsole.error('打卡提交失败:', err);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '网络错误，请重试',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}).finally(() => {\r\n\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 切换管理员设置\r\n\t\t\tgoToAdminSettings() {\r\n\t\t\t\tconsole.log('正在跳转到考勤规则设置页面');\r\n\t\t\t\t// 导航到考勤规则设置页面\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/attendance/admin/rules',\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error('页面跳转失败:', err);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '页面跳转失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 上传位置轨迹记录\r\n\t\t\tuploadTrajectoryRecord(latitude, longitude) {\r\n\t\t\t\t// 检查经纬度是否有效\r\n\t\t\t\tif (!longitude || !latitude || longitude === 0 || latitude === 0) {\r\n\t\t\t\t\tconsole.error('经纬度无效，无法上传位置轨迹:', {\r\n\t\t\t\t\t\tlongitude: longitude,\r\n\t\t\t\t\t\tlatitude: latitude\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn Promise.reject({errMsg: '位置参数无效'});\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 获取用户ID\r\n\t\t\t\tconst userId = this.getUserId();\r\n\t\t\t\t\r\n\t\t\t\tconsole.log('上传位置轨迹:', {\r\n\t\t\t\t\tuserId: userId,\r\n\t\t\t\t\tlongitude: longitude,\r\n\t\t\t\t\tlatitude: latitude\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 调用API上传位置数据\r\n\t\t\t\treturn attendanceApi.uploadPersonTrajectory({\r\n\t\t\t\t\tuserId: userId, // 传递userId参数\r\n\t\t\t\t\temployeeId: userId, // 同时传递employeeId参数，值与userId相同\r\n\t\t\t\t\tlongitude: longitude,\r\n\t\t\t\t\tlatitude: latitude\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\t\tconsole.log('位置轨迹上传成功:', res.data);\r\n\t\t\t\t\t\treturn res.data;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.error('位置轨迹上传失败:', res.message || '未知错误', res);\r\n\t\t\t\t\t\treturn Promise.reject(res);\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tconsole.error('位置轨迹上传请求异常:', err);\r\n\t\t\t\t\treturn Promise.reject(err);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 重新获取位置\r\n\t\t\tretryGetLocation() {\r\n\t\t\t\t// 直接请求位置权限\r\n\t\t\t\tthis.requestLocationPermission();\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.clock-in-container {\r\n\t\tpadding: 30rpx;\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tmin-height: 100vh;\r\n\t}\r\n\t\r\n\t.status-header {\r\n\t\tmargin-bottom: 40rpx;\r\n\t\t\r\n\t\t.date-info {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\r\n\t\t\t.date {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.time {\r\n\t\t\t\tfont-size: 60rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.admin-settings {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 20rpx;\r\n\t\t\tright: 20rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t\t// background-color: rgba(255, 255, 255, 0.8);\r\n\t\t\tpadding: 10rpx;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t\t\r\n\t\t\t.settings-icon {\r\n\t\t\t\tfont-size: 40rpx;\r\n\t\t\t\tmargin-bottom: 5rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.settings-text {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.clock-status {\r\n\t\tmargin-bottom: 40rpx;\r\n\t\t\r\n\t\t.status-card {\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-radius: 16rpx;\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n\t\t\t\r\n\t\t\t.work-time {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tmargin-bottom: 30rpx;\r\n\t\t\t\t\r\n\t\t\t\t.time-item {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.time-label {\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.time-value {\r\n\t\t\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.time-divider {\r\n\t\t\t\t\twidth: 2rpx;\r\n\t\t\t\t\theight: 80rpx;\r\n\t\t\t\t\tbackground-color: #eee;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.clock-records {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tborder-top: 2rpx solid #f5f5f5;\r\n\t\t\t\tpadding-top: 30rpx;\r\n\t\t\t\t\r\n\t\t\t\t.record-item {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.record-label {\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.record-value {\r\n\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\tcolor: $uni-color-success;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&.not-clocked {\r\n\t\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.clock-action {\r\n\t\tmargin-bottom: 60rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\t\r\n\t\t.clock-circle {\r\n\t\t\twidth: 300rpx;\r\n\t\t\theight: 300rpx;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tbackground: linear-gradient(135deg, #4483e5, #6a9eef);\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\tbox-shadow: 0 10rpx 30rpx rgba(106, 158, 239, 0.3);\r\n\t\t\tmargin-bottom: 30rpx;\r\n\t\t\ttransition: all 0.3s ease;\r\n\t\t\t\r\n\t\t\t.circle-inner {\r\n\t\t\t\twidth: 260rpx;\r\n\t\t\t\theight: 260rpx;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.clock-text {\r\n\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tcolor: #4483e5;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.action-desc {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #666;\r\n\t\t\ttext-align: center;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.section-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tposition: relative;\r\n\t\tpadding-left: 20rpx;\r\n\t\t\r\n\t\t&::before {\r\n\t\t\tcontent: '';\r\n\t\t\tposition: absolute;\r\n\t\t\tleft: 0;\r\n\t\t\ttop: 8rpx;\r\n\t\t\twidth: 8rpx;\r\n\t\t\theight: 32rpx;\r\n\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\tborder-radius: 4rpx;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.recent-records {\r\n\t\t.record-list {\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-radius: 16rpx;\r\n\t\t\tpadding: 20rpx;\r\n\t\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n\t\t\t\r\n\t\t\t.record-empty {\r\n\t\t\t\tpadding: 40rpx 0;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tcolor: #999;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.record-item {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tpadding: 20rpx 0;\r\n\t\t\t\tborder-bottom: 2rpx solid #f5f5f5;\r\n\t\t\t\t\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tborder-bottom: none;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.record-date {\r\n\t\t\t\t\twidth: 180rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.date {\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\tmargin-bottom: 6rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.week {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.record-details {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.detail-item {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\t\tmargin-bottom: 0;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.detail-label {\r\n\t\t\t\t\t\t\twidth: 80rpx;\r\n\t\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.detail-value {\r\n\t\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\t\tcolor: $uni-color-success;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t&.abnormal {\r\n\t\t\t\t\t\t\t\tcolor: $uni-color-warning;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t.status-tag {\r\n\t\t\t\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\t\t\tpadding: 2rpx 10rpx;\r\n\t\t\t\t\t\t\t\tborder-radius: 4rpx;\r\n\t\t\t\t\t\t\t\tbackground-color: rgba(250, 173, 20, 0.1);\r\n\t\t\t\t\t\t\t\tcolor: $uni-color-warning;\r\n\t\t\t\t\t\t\t\tmargin-left: 10rpx;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.face-verify-popup {\r\n\t\twidth: 600rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\toverflow: hidden;\r\n\t\t\r\n\t\t.popup-title {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tpadding: 30rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tborder-bottom: 1rpx solid #eee;\r\n\t\t}\r\n\t\t\r\n\t\t.popup-content {\r\n\t\t\tpadding: 30rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.camera-container, .photo-preview {\r\n\t\t\twidth: 400rpx;\r\n\t\t\theight: 400rpx;\r\n\t\t\tmargin: 0 auto 30rpx;\r\n\t\t\tbackground-color: #f5f5f5;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t\toverflow: hidden;\r\n\t\t}\r\n\t\t\r\n\t\t.popup-footer {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tpadding-top: 20rpx;\r\n\t\t\t\r\n\t\t\tbutton {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tmargin: 0 20rpx;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tborder-radius: 40rpx;\r\n\t\t\t\t\r\n\t\t\t\t&.btn-cancel {\r\n\t\t\t\t\tbackground-color: #f5f5f5;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&.btn-confirm {\r\n\t\t\t\t\tbackground-color: #007AFF;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.supplement-popup {\r\n\t\twidth: 600rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\toverflow: hidden;\r\n\t\t\r\n\t\t.popup-title {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tpadding: 30rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tborder-bottom: 1rpx solid #eee;\r\n\t\t}\r\n\t\t\r\n\t\t.popup-content {\r\n\t\t\tpadding: 30rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.form-item {\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\r\n\t\t\t.form-label {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.form-input {\r\n\t\t\t\tbackground-color: #f5f5f5;\r\n\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\tpadding: 20rpx;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\r\n\t\t\t\ttextarea {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 150rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.popup-footer {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tpadding-top: 20rpx;\r\n\t\t\t\r\n\t\t\tbutton {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tmargin: 0 20rpx;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tborder-radius: 40rpx;\r\n\t\t\t\t\r\n\t\t\t\t&.btn-cancel {\r\n\t\t\t\t\tbackground-color: #f5f5f5;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&.btn-confirm {\r\n\t\t\t\t\tbackground-color: #007AFF;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.supplement-images {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tmargin-top: 10rpx;\r\n\t\t\r\n\t\t.image-item {\r\n\t\t\tposition: relative;\r\n\t\t\twidth: 150rpx;\r\n\t\t\theight: 150rpx;\r\n\t\t\tmargin-right: 20rpx;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t\toverflow: hidden;\r\n\t\t\t\r\n\t\t\t.supplement-image {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.image-delete {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: 0;\r\n\t\t\t\tright: 0;\r\n\t\t\t\twidth: 40rpx;\r\n\t\t\t\theight: 40rpx;\r\n\t\t\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.image-add {\r\n\t\t\twidth: 150rpx;\r\n\t\t\theight: 150rpx;\r\n\t\t\tbackground-color: #f5f5f5;\r\n\t\t\tborder: 1rpx dashed #ccc;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\t\r\n\t\t\t.image-add-icon {\r\n\t\t\t\tfont-size: 48rpx;\r\n\t\t\t\tcolor: #999;\r\n\t\t\t\tline-height: 1;\r\n\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.image-add-text {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #999;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.clock-btn {\r\n\t\tmargin-top: 40rpx;\r\n\t\twidth: 90%;\r\n\t\theight: 90rpx;\r\n\t\tbackground: linear-gradient(135deg, #4B79A1, #283E51);\r\n\t\tcolor: white;\r\n\t\tfont-size: 32rpx;\r\n\t\tborder-radius: 45rpx;\r\n\t\tbox-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.1);\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\t\r\n\t\t&.disabled {\r\n\t\t\tbackground: linear-gradient(135deg, #ccc, #999);\r\n\t\t\tbox-shadow: none;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.date-picker {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\t\r\n\t\t.icon-calendar {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tcolor: #999;\r\n\t\t}\r\n\t}\r\n</style> ", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/attendance/clock-in.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "err", "attendanceApi"], "mappings": ";;;AAyJC,MAAO,kBAAiB,MAAW;AAEnC,MAAK,YAAU;AAAA,EACd,YAAY;AAAA,IACV;AAAA;AAAA,EACD;AAAA,EACD,OAAO;AACN,WAAO;AAAA;AAAA,MAEN,OAAO;AAAA,MACP,aAAa;AAAA,MACb,aAAa;AAAA;AAAA,MAGb,UAAU;AAAA;AAAA,MACV,WAAW;AAAA;AAAA,MACX,aAAa;AAAA;AAAA,MACb,eAAe;AAAA;AAAA,MACf,sBAAsB;AAAA;AAAA,MACtB,0BAA0B;AAAA;AAAA,MAC1B,uBAAuB;AAAA;AAAA;AAAA,MAGvB,aAAa;AAAA,MACb,cAAc;AAAA;AAAA,MAGd,gBAAgB,KAAK,WAAW,oBAAI,KAAI,CAAE;AAAA,MAC1C,iBAAiB,CAAC,QAAQ,MAAM;AAAA,MAChC,qBAAqB;AAAA,MACrB,kBAAkB;AAAA,MAClB,kBAAkB,CAAE;AAAA;AAAA,MAGpB,eAAe,CAAE;AAAA;AAAA,MAGjB,iBAAiB;AAAA,QAChB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,iBAAiB;AAAA;AAAA,QACjB,eAAe;AAAA;AAAA,QACf,qBAAqB;AAAA;AAAA,QACrB,wBAAwB;AAAA;AAAA,MACxB;AAAA;AAAA,MAGD,SAAS;AAAA;AAAA,MAGT,SAAS;AAAA;AAAA,MAGT,qBAAqB;AAAA,MACrB,qBAAqB;AAAA;AAAA,MACrB,mBAAmB;AAAA;AAAA,IACpB;AAAA,EACA;AAAA,EACD,UAAU;AAAA;AAAA,IAET,aAAa;AACZ,aAAO;AAAA,IACP;AAAA,EACD;AAAA,EACD,SAAS;AAER,SAAK,eAAc;AAGnB,SAAK,QAAQ,YAAY,MAAM;AAC9B,WAAK,eAAc;AAAA,IACnB,GAAE,GAAI;AAGP,SAAK,wBAAwB;AAC7B,SAAK,oBAAoB;AAGzB,SAAK,mBAAkB;AAGvB,SAAK,oBAAmB;AAGxB,SAAK,iBAAgB;AAGrB,eAAW,MAAM;AAChB,WAAK,wBAAuB,EAAG,KAAK,mBAAiB;AACpD,YAAI,eAAe;AAElB,eAAK,eAAc;AAAA,eACb;AAEN,eAAK,0BAAyB;AAAA,QAC/B;AAAA,MACD,CAAC;AAAA,IACD,GAAE,GAAG;AAAA,EACN;AAAA,EACD,WAAW;AAEV,QAAI,KAAK,OAAO;AACf,oBAAc,KAAK,KAAK;AACxB,WAAK,QAAQ;AAAA,IACd;AAAA,EACA;AAAA,EACD,SACA;AAEC,SAAK,mBAAkB;AAGvB,SAAK,oBAAmB;AAGxB,SAAK,iBAAgB;AAAA,EACrB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,sBAAsB;AACrB,WAAK,OAAO,SAAS,gCAAgC;AACrDA,oBAAAA,MAAY,MAAA,OAAA,wCAAA,UAAU;AAAA,IACtB;AAAA;AAAA,IAGD,qBAAqB;AACpB,WAAK,OAAO,SAAS,+BAA+B;AACpDA,oBAAAA,MAAY,MAAA,OAAA,wCAAA,UAAU;AAAA,IACtB;AAAA;AAAA,IAGD,iBAAiB;AAChB,YAAM,MAAM,oBAAI;AAGhB,YAAM,OAAO,IAAI;AACjB,YAAM,QAAQ,OAAO,IAAI,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACxD,YAAM,MAAM,OAAO,IAAI,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AACjD,YAAM,WAAW,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AACjE,YAAM,UAAU,SAAS,IAAI,OAAQ,CAAA;AAErC,WAAK,cAAc,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO;AAGtD,YAAM,QAAQ,OAAO,IAAI,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AACpD,YAAM,UAAU,OAAO,IAAI,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACxD,YAAM,UAAU,OAAO,IAAI,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AAExD,WAAK,cAAc,GAAG,KAAK,IAAI,OAAO,IAAI,OAAO;AAAA,IACjD;AAAA;AAAA,IAGD,cAAc;AAEb,UAAI,KAAK,mBAAmB;AAC3BA,sBAAAA,2DAAY,gBAAgB;AAC5B;AAAA,MACD;AAEA,WAAK,oBAAoB;AACzB,WAAK,cAAc;AAGnB,YAAM,kBAAkB,WAAW,MAAM;AACxCA,sBAAAA,2DAAY,aAAa;AACzB,aAAK,gBAAgB;AACrB,aAAK,cAAc;AACnB,aAAK,oBAAoB;AAEzB,aAAK,0BAAyB;AAAA,MAC9B,GAAE,IAAK;AAGR,UAAI,KAAK,sBAAsB;AAE9B,aAAK,oBAAoB,KAAK,MAAM;AACnC,uBAAa,eAAe;AAC5B,eAAK,oBAAoB;AAAA,QAC1B,CAAC,EAAE,MAAM,SAAO;AACfA,wBAAc,MAAA,MAAA,SAAA,wCAAA,qBAAqB,GAAG;AACtC,eAAK,uBAAuB;AAE5B,eAAK,iBAAiB,KAAK,MAAM;AAChC,yBAAa,eAAe;AAC5B,iBAAK,oBAAoB;AAAA,UAC1B,CAAC,EAAE,MAAM,CAAAC,SAAO;AACf,yBAAa,eAAe;AAC5B,iBAAK,oBAAoBA,IAAG;AAC5B,iBAAK,oBAAoB;AAAA,UAC1B,CAAC;AAAA,QACF,CAAC;AAAA,aACK;AAEN,aAAK,iBAAiB,KAAK,MAAM;AAChC,uBAAa,eAAe;AAC5B,eAAK,oBAAoB;AAAA,QAC1B,CAAC,EAAE,MAAM,SAAO;AACf,uBAAa,eAAe;AAC5B,eAAK,oBAAoB,GAAG;AAC5B,eAAK,oBAAoB;AAAA,QAC1B,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,oBAAoB;AACnB,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAAA,MA4CxC,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB;AAChB,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvCD,sBAAAA,MAAI,YAAY;AAAA,UACf,MAAM;AAAA,UACN,gBAAgB;AAAA,UAChB,wBAAwB;AAAA,UACxB,SAAS,CAAC,QAAQ;AACjBA,0BAAA,MAAA,MAAA,OAAA,wCAAY,cAAc,GAAG;AAC7B,iBAAK,WAAW,IAAI;AACpB,iBAAK,YAAY,IAAI;AACrB,iBAAK,gBAAgB;AACrB,iBAAK,2BAA2B;AAChC,iBAAK,cAAc;AACnB,iBAAK,wBAAwB;AAE7B,oBAAQ,GAAG;AAAA,UACX;AAAA,UACD,MAAM,CAAC,QAAQ;AACdA,0BAAA,MAAA,MAAA,SAAA,wCAAc,cAAc,GAAG;AAC/B,mBAAO,GAAG;AAAA,UACV;AAAA,UACD,UAAU,MAAM;AAEfA,0BAAAA,MAAY,MAAA,OAAA,wCAAA,QAAQ;AAAA,UACrB;AAAA,QACD,CAAC;AAAA,MACF,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,0BAA0B;AACzB,aAAO,IAAI,QAAQ,CAAC,YAAY;AA0D/BA,sBAAAA,MAAI,WAAW;AAAA,UACd,SAAS,CAAC,QAAQ;AACjB,oBAAQ,IAAI,YAAY,oBAAoB,MAAM,IAAI;AAAA,UACtD;AAAA,UACD,MAAM,MAAM;AACX,oBAAQ,KAAK;AAAA,UACd;AAAA,QACD,CAAC;AAAA,MAOF,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB,KAAK;AACxBA,oBAAA,MAAA,MAAA,OAAA,wCAAY,WAAW,GAAG;AAC1B,WAAK,cAAc;AACnB,WAAK,gBAAgB;AAGrB,WAAK,wBAAuB,EAAG,KAAK,mBAAiB;AACpD,YAAI,CAAC,eAAe;AAEnB,eAAK,0BAAyB;AAAA,QA+B/B;AAAA,MACD,CAAC;AAED,WAAK,oBAAoB;AAAA,IACzB;AAAA;AAAA,IAGD,4BAA4B;AAqD3BA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS,MAAM;AAEd,eAAK,eAAc;AAAA,QACnB;AAAA,QACD,MAAM,MAAM;AAEXA,wBAAAA,MAAA,MAAA,OAAA,wCAAY,sBAAsB;AAAA,QACnC;AAAA,MACD,CAAC;AAAA,IAED;AAAA;AAAA,IAGD,uBAAuB;AAAA,IAmBtB;AAAA;AAAA,IAGD,oBAAoB,UAAU,WAAW;AAExC,YAAM,aAAa,MAAM;AAExB,cAAM,kBAAkB;AAAA,UACvB,UAAU;AAAA;AAAA,UACV,WAAW;AAAA;AAAA;AAIZ,cAAM,WAAW,KAAK;AAAA,UACrB;AAAA,UAAU;AAAA,UACV,gBAAgB;AAAA,UAAU,gBAAgB;AAAA;AAI3C,cAAM,SAAS,YAAY,KAAK,gBAAgB;AAChD,aAAK,cAAc,SAAS,WAAW;AAEvC,YAAI,CAAC,QAAQ;AACZA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,MAAM,QAAQ,CAAC;AAAA,YACpC,MAAM;AAAA,YACN,UAAU;AAAA,UACX,CAAC;AAAA,QACF;AAAA;AAIDE,gBAAAA,cAAc,oBAAoB;AAAA,QACjC;AAAA,QACA;AAAA,OACA,EAAE,KAAK,SAAO;AACd,YAAI,IAAI,SAAS,KAAK;AACrB,eAAK,cAAc,IAAI,KAAK,SAAS,WAAW;AAGhD,cAAI,CAAC,IAAI,KAAK,UAAU,IAAI,KAAK,UAAU;AAC1CF,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO,SAAS,IAAI,KAAK,QAAQ;AAAA,cACjC,MAAM;AAAA,cACN,UAAU;AAAA,YACX,CAAC;AAAA,UACF;AAAA,eACM;AAEN;QACD;AAAA,MACD,CAAC,EAAE,MAAM,SAAO;AACfA,mFAAc,aAAa,GAAG;AAE9B;MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB,MAAM,MAAM,MAAM,MAAM;AACzC,YAAM,IAAI;AACV,YAAM,OAAO,KAAK,QAAQ,OAAO,IAAI;AACrC,YAAM,OAAO,KAAK,QAAQ,OAAO,IAAI;AACrC,YAAM,IACL,KAAK,IAAI,OAAK,CAAC,IAAI,KAAK,IAAI,OAAK,CAAC,IAClC,KAAK,IAAI,KAAK,QAAQ,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,QAAQ,IAAI,CAAC,IAC1D,KAAK,IAAI,OAAK,CAAC,IAAI,KAAK,IAAI,OAAK,CAAC;AACnC,YAAM,IAAI,IAAI,KAAK,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,IAAE,CAAC,CAAC;AACrD,YAAM,WAAW,IAAI;AACrB,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,QAAQ,KAAK;AACZ,aAAO,OAAO,KAAK,KAAG;AAAA,IACtB;AAAA;AAAA,IAGD,qBAAqB;AACpBE,gBAAAA,cAAc,cAAc,EAC1B,KAAK,SAAO;AACZ,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AACjCF,wBAAY,MAAA,MAAA,OAAA,wCAAA,aAAa,IAAI,IAAI;AACjC,eAAK,kBAAkB;AAAA,YACtB,GAAG,KAAK;AAAA,YACR,GAAG,IAAI;AAAA;QAET;AAAA,OACA,EACA,MAAM,SAAO;AACbA,mFAAc,aAAa,GAAG;AAAA,MAC/B,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,sBAAsB;AACrBE,gBAAAA,cAAc,eAAe,EAC3B,KAAK,SAAO;AACZF,iFAAY,aAAa,GAAG;AAC5B,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AACjC,eAAK,cAAc,IAAI,KAAK,eAAe;AAC3C,eAAK,eAAe,IAAI,KAAK,gBAAgB;AAG7C,cAAI,KAAK,eAAe,IAAI,KAAK,eAAe;AAC/C,kBAAM,aAAa,IAAI,KAAK,kBAAkB,SAAS,SAAS;AAChE,iBAAK,cAAc,KAAK,cAAc;AAAA,UACvC;AAGA,cAAI,KAAK,gBAAgB,IAAI,KAAK,gBAAgB;AACjD,kBAAM,aAAa,IAAI,KAAK,mBAAmB,UAAU,SAAS;AAClE,iBAAK,eAAe,KAAK,eAAe;AAAA,UACzC;AAAA,QACD;AAAA,OACA,EACA,MAAM,SAAO;AACbA,sBAAc,MAAA,MAAA,SAAA,wCAAA,eAAe,GAAG;AAAA,MACjC,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,mBAAmB;AAClBE,gBAAa,cAAC,iBAAiB,CAAC,EAC9B,KAAK,SAAO;AACZ,YAAI,IAAI,SAAS,OAAO,IAAI,QAAQ,MAAM,QAAQ,IAAI,KAAK,OAAO,GAAG;AACpE,eAAK,gBAAgB,IAAI,KAAK,QAAQ,IAAI,UAAQ;AACjD,mBAAO;AAAA,cACN,MAAM,KAAK;AAAA,cACX,MAAM,KAAK;AAAA,cACX,aAAa,KAAK,eAAe;AAAA,cACjC,cAAc,KAAK,gBAAgB;AAAA,cACnC,eAAe,KAAK,iBAAiB;AAAA,cACrC,gBAAgB,KAAK,kBAAkB;AAAA;UAEzC,CAAC;AAAA,eACK;AAENF,wBAAA,MAAA,MAAA,QAAA,wCAAa,qBAAqB,GAAG;AACrC,eAAK,gBAAgB;QACtB;AAAA,OACA,EACA,MAAM,SAAO;AACbA,sBAAc,MAAA,MAAA,SAAA,wCAAA,eAAe,GAAG;AAChC,aAAK,gBAAgB;AACrBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB;AAEf,YAAM,YAAY,CAAC,KAAK;AACxB,YAAM,aAAa,CAAC,CAAC,KAAK,eAAe,CAAC,KAAK;AAE/C,UAAI,CAAC,aAAa,CAAC,YAAY;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAGAA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACR,CAAC;AAGD,WAAK,iBAAiB,QAAQ,MAAM;AACnCA,sBAAG,MAAC,YAAW;AACf,aAAK,iBAAgB;AAAA,MACtB,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB;AAChB,aAAO,IAAI,QAAQ,CAAC,YAAY;AAE/B,YAAI,KAAK,mBAAmB;AAC3BA,wBAAAA,2DAAY,gBAAgB;AAC5B;AACA;AAAA,QACD;AAEA,aAAK,oBAAoB;AAGzB,cAAM,kBAAkB,WAAW,MAAM;AACxCA,wBAAAA,MAAY,MAAA,OAAA,wCAAA,eAAe;AAC3B,eAAK,oBAAoB;AACzB;QACA,GAAE,GAAI;AAGP,YAAI,KAAK,sBAAsB;AAC9B,eAAK,oBAAoB,KAAK,MAAM;AACnC,yBAAa,eAAe;AAC5B,iBAAK,oBAAoB;AACzB;WACA,EAAE,MAAM,MAAM;AAEd,iBAAK,iBAAiB,KAAK,MAAM;AAChC,2BAAa,eAAe;AAC5B,mBAAK,oBAAoB;AACzB;aACA,EAAE,MAAM,MAAM;AAEd,2BAAa,eAAe;AAC5B,mBAAK,oBAAoB;AACzB;YACD,CAAC;AAAA,UACF,CAAC;AAAA,eACK;AAEN,eAAK,iBAAiB,KAAK,MAAM;AAChC,yBAAa,eAAe;AAC5B,iBAAK,oBAAoB;AACzB;WACA,EAAE,MAAM,MAAM;AAEd,yBAAa,eAAe;AAC5B,iBAAK,oBAAoB;AACzB;UACD,CAAC;AAAA,QACF;AAGA,aAAK,wBAAuB,EAAG,KAAK,mBAAiB;AACpD,cAAI,CAAC,eAAe;AACnB,iBAAK,0BAAyB;AAAA,UAC/B;AAAA,QACD,CAAC;AAAA,MACF,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,sBAAsB;AAErB,WAAK,oBAAmB;AACxB,WAAK,MAAM,gBAAgB;IAC3B;AAAA;AAAA,IAGD,sBAAsB;AACrB,WAAK,iBAAiB,KAAK,WAAW,oBAAI,KAAM,CAAA;AAChD,WAAK,sBAAsB;AAC3B,WAAK,mBAAmB;AACxB,WAAK,mBAAmB;IACxB;AAAA;AAAA,IAGD,wBAAwB;AACvBA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AAEjB,cAAI,KAAK,iBAAiB,SAAS,GAAG;AACrC,iBAAK,iBAAiB,KAAK,IAAI,cAAc,CAAC,CAAC;AAAA,iBACzC;AACNA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACP,CAAC;AAAA,UACF;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,sBAAsB,OAAO;AAC5B,WAAK,iBAAiB,OAAO,OAAO,CAAC;AAAA,IACrC;AAAA;AAAA,IAGD,8BAA8B;AAE7B,UAAI,CAAC,KAAK,gBAAgB;AACzBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,UAAI,CAAC,KAAK,oBAAoB,KAAK,iBAAiB,KAAO,MAAI,IAAI;AAClEA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAGA,WAAK,UAAU;AAGf,YAAM,WAAW;AAAA,QAChB,QAAQ,KAAK,UAAW;AAAA,QACxB,MAAM,KAAK;AAAA,QACX,MAAM,KAAK,sBAAsB;AAAA;AAAA,QACjC,QAAQ,KAAK;AAAA,QACb,QAAQ,CAAC;AAAA;AAIV,UAAI,KAAK,iBAAiB,SAAS,GAAG;AACrC,aAAK,uBAAsB,EAAG,KAAK,eAAa;AAC/C,mBAAS,SAAS;AAClB,eAAK,sBAAsB,QAAQ;AAAA,QACpC,CAAC,EAAE,MAAM,SAAO;AACf,eAAK,UAAU;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF,CAAC;AAAA,aACK;AACN,aAAK,sBAAsB,QAAQ;AAAA,MACpC;AAAA,IACA;AAAA;AAAA,IAGD,yBAAyB;AACxB,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,cAAM,cAAc,KAAK,iBAAiB,IAAI,eAAa;AAC1D,iBAAO,IAAI,QAAQ,CAAC,eAAe,iBAAiB;AACnDA,0BAAAA,MAAI,WAAW;AAAA,cACd,KAAK,KAAK,KAAK,UAAU;AAAA,cACzB,UAAU;AAAA,cACV,MAAM;AAAA,cACN,QAAQ;AAAA,gBACP,OAAOA,cAAAA,MAAI,eAAe,OAAO;AAAA,cACjC;AAAA,cACD,SAAS,CAAC,QAAQ;AACjB,oBAAI,IAAI,eAAe,KAAK;AAC3B,wBAAM,OAAO,KAAK,MAAM,IAAI,IAAI;AAChC,sBAAI,KAAK,SAAS,KAAK,KAAK,MAAM;AACjC,kCAAc,KAAK,KAAK,GAAG;AAAA,yBACrB;AACN,iCAAa,IAAI,MAAM,KAAK,OAAO,MAAM,CAAC;AAAA,kBAC3C;AAAA,uBACM;AACN,+BAAa,IAAI,MAAM,MAAM,CAAC;AAAA,gBAC/B;AAAA,cACA;AAAA,cACD,MAAM,CAAC,QAAQ;AACd,6BAAa,GAAG;AAAA,cACjB;AAAA,YACD,CAAC;AAAA,UACF,CAAC;AAAA,QACF,CAAC;AAED,gBAAQ,IAAI,WAAW,EAAE,KAAK,OAAO,EAAE,MAAM,MAAM;AAAA,MACpD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,sBAAsB,UAAU;AAC/BE,gBAAAA,cAAc,4BAA4B,QAAQ,EAAE,KAAK,SAAO;AAC/D,aAAK,UAAU;AACf,YAAI,IAAI,SAAS,GAAG;AACnBF,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,IAAI,OAAO;AAAA,YAClB,MAAM;AAAA,UACP,CAAC;AACD,eAAK,MAAM,gBAAgB;AAC3B,eAAK,oBAAmB;AAGxB,eAAK,oBAAmB;AACxB,eAAK,iBAAgB;AAAA,eACf;AACNA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,IAAI,OAAO;AAAA,YAClB,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACD,CAAC,EAAE,MAAM,SAAO;AACf,aAAK,UAAU;AACfA,oFAAc,WAAW,GAAG;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,qBAAqB;AACpB,UAAI,CAAC,KAAK,aAAa;AACtB,eAAO;AAAA,MACR;AAEA,UAAI,CAAC,KAAK,cAAc;AACvB,eAAO;AAAA,MACR;AAEA,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,uBAAuB;AACtB,YAAM,MAAM,oBAAI;AAChB,YAAM,cAAc,IAAI;AACxB,YAAM,gBAAgB,IAAI;AAE1B,UAAI,CAAC,KAAK,aAAa;AAEtB,cAAM,kBAAkB,KAAK,gBAAgB,eAAe,SAAS,MAAM,GAAG;AAC9E,cAAM,cAAc,SAAS,eAAe,CAAC,CAAC;AAC9C,cAAM,gBAAgB,SAAS,eAAe,CAAC,CAAC;AAEhD,YAAI,cAAc,eAAgB,gBAAgB,eAAe,gBAAgB,eAAgB;AAChG,iBAAO,UAAU,KAAK,gBAAgB,WAAW;AAAA,eAC3C;AACN,iBAAO,OAAO,KAAK,gBAAgB,WAAW;AAAA,QAC/C;AAAA,MACD;AAEA,UAAI,CAAC,KAAK,cAAc;AAEvB,cAAM,mBAAmB,KAAK,gBAAgB,gBAAgB,SAAS,MAAM,GAAG;AAChF,cAAM,eAAe,SAAS,gBAAgB,CAAC,CAAC;AAChD,cAAM,iBAAiB,SAAS,gBAAgB,CAAC,CAAC;AAElD,YAAI,cAAc,gBAAiB,gBAAgB,gBAAgB,gBAAgB,gBAAiB;AACnG,iBAAO,OAAO,KAAK,gBAAgB,YAAY;AAAA,eACzC;AACN,iBAAO,gBAAgB,KAAK,gBAAgB,YAAY;AAAA,QACzD;AAAA,MACD;AACA,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,WAAW,MAAM;AAChB,YAAM,OAAO,KAAK;AAClB,YAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,YAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,IAC9B;AAAA;AAAA,IAGD,YAAY;AACX,YAAM,SAASA,cAAAA,MAAI,eAAe,QAAQ;AAC1C,UAAI,CAAC,QAAQ;AACZA,sBAAAA,6DAAa,gBAAgB;AAC7B,eAAO;AAAA,MACR;AACA,aAAO,UAAU;AAAA,IACjB;AAAA;AAAA,IAGD,uBAAuB;AACtB,WAAK,uBAAuB,CAAC,KAAK;AAClCA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,OAAO,KAAK,uBAAuB,OAAO,IAAI;AAAA,QACrD,MAAM;AAAA,QACN,UAAU;AAAA,MACX,CAAC;AAGD,iBAAW,MAAM;AAChB,aAAK,YAAW;AAAA,MAChB,GAAE,GAAG;AAAA,IACN;AAAA;AAAA,IAGD,mBAAmB;AAClB,YAAM,MAAM,oBAAI;AAChB,YAAM,YAAY,CAAC,KAAK,cAAc,YAAY;AAGlD,UAAI,SAAS;AAEb,UAAI,cAAc,WAAW;AAE5B,cAAM,iBAAiB,KAAK,gBAAgB,YAAY,MAAM,GAAG;AACjE,cAAM,cAAc,SAAS,eAAe,CAAC,CAAC;AAC9C,cAAM,gBAAgB,SAAS,eAAe,CAAC,CAAC;AAChD,cAAM,cAAc,IAAI,KAAK,IAAI,YAAW,GAAI,IAAI,SAAQ,GAAI,IAAI,QAAS,GAAE,aAAa,aAAa;AAEzG,YAAI,MAAM,aAAa;AACtB,mBAAS;AAAA,QACV;AAAA,MACD,WAAW,cAAc,YAAY;AAEpC,cAAM,kBAAkB,KAAK,gBAAgB,aAAa,MAAM,GAAG;AACnE,cAAM,eAAe,SAAS,gBAAgB,CAAC,CAAC;AAChD,cAAM,iBAAiB,SAAS,gBAAgB,CAAC,CAAC;AAClD,cAAM,eAAe,IAAI,KAAK,IAAI,YAAW,GAAI,IAAI,SAAQ,GAAI,IAAI,QAAS,GAAE,cAAc,cAAc;AAE5G,YAAI,MAAM,cAAc;AACvB,mBAAS;AAAA,QACV;AAAA,MACD;AAEA,WAAK,UAAU;AAGfA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACR,CAAC;AAGD,YAAM,kBAAkB;AAAA,QACvB,UAAU,KAAK;AAAA,QACf,WAAW,KAAK;AAAA;AAGjBE,gBAAAA,cAAc,YAAY;AAAA,QACzB,YAAY;AAAA,QACZ,UAAU,KAAK;AAAA,QACf,WAAW,KAAK;AAAA,QAChB;AAAA;AAAA,QACA,SAAS,KAAK,UAAY;AAAA;AAAA,OAC1B,EAAE,KAAK,SAAO;AACdF,sBAAG,MAAC,YAAW;AACf,YAAI,IAAI,SAAS,KAAK;AACrBA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,cAAc,YAAY,WAAW;AAAA,YAC5C,MAAM;AAAA,UACP,CAAC;AAGD,eAAK,oBAAmB;AACxB,eAAK,iBAAgB;AAGrB,eAAK,uBAAuB,gBAAgB,UAAU,gBAAgB,SAAS,EAC7E,MAAM,SAAO;AACbA,0BAAc,MAAA,MAAA,SAAA,yCAAA,cAAc,GAAG;AAAA,UAChC,CAAC;AAGF,gBAAM,gBAAgBA,cAAG,MAAC,eAAe,eAAe,KAAK;AAG7D,cAAI,cAAc,WAAW;AAC5B,iBAAK,oBAAmB;AACxBA,0BAAY,MAAA,MAAA,OAAA,yCAAA,yBAAyB,eAAe,IAAI;AAAA,iBAClD;AAEN,iBAAK,mBAAkB;AACvBA,0BAAAA,MAAA,MAAA,OAAA,yCAAY,iBAAiB;AAAA,UAC9B;AAAA,eACM;AACNA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,IAAI,WAAW;AAAA,YACtB,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACD,CAAC,EAAE,MAAM,SAAO;AACfA,sBAAG,MAAC,YAAW;AACfA,oFAAc,WAAW,GAAG;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF,CAAC,EAAE,QAAQ,MAAM;AAChB,aAAK,UAAU;AAAA,MAChB,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB;AACnBA,oBAAAA,MAAA,MAAA,OAAA,yCAAY,eAAe;AAE3BA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,QACL,MAAM,CAAC,QAAQ;AACdA,wBAAc,MAAA,MAAA,SAAA,yCAAA,WAAW,GAAG;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,uBAAuB,UAAU,WAAW;AAE3C,UAAI,CAAC,aAAa,CAAC,YAAY,cAAc,KAAK,aAAa,GAAG;AACjEA,sBAAAA,MAAA,MAAA,SAAA,yCAAc,mBAAmB;AAAA,UAChC;AAAA,UACA;AAAA,QACD,CAAC;AACD,eAAO,QAAQ,OAAO,EAAC,QAAQ,SAAQ,CAAC;AAAA,MACzC;AAGA,YAAM,SAAS,KAAK;AAEpBA,oBAAAA,MAAY,MAAA,OAAA,yCAAA,WAAW;AAAA,QACtB;AAAA,QACA;AAAA,QACA;AAAA,MACD,CAAC;AAGD,aAAOE,UAAAA,cAAc,uBAAuB;AAAA,QAC3C;AAAA;AAAA,QACA,YAAY;AAAA;AAAA,QACZ;AAAA,QACA;AAAA,OACA,EAAE,KAAK,SAAO;AACd,YAAI,IAAI,SAAS,KAAK;AACrBF,wBAAA,MAAA,MAAA,OAAA,yCAAY,aAAa,IAAI,IAAI;AACjC,iBAAO,IAAI;AAAA,eACL;AACNA,8BAAc,MAAA,SAAA,yCAAA,aAAa,IAAI,WAAW,QAAQ,GAAG;AACrD,iBAAO,QAAQ,OAAO,GAAG;AAAA,QAC1B;AAAA,MACD,CAAC,EAAE,MAAM,SAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,yCAAc,eAAe,GAAG;AAChC,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC1B,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,mBAAmB;AAElB,WAAK,0BAAyB;AAAA,IAC9B;AAAA,EACF;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/vCD,GAAG,WAAW,eAAe;"}