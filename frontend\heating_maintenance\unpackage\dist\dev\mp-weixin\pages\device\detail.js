"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const _sfc_main = {
  data() {
    return {
      deviceId: "",
      deviceInfo: {
        deviceId: "",
        name: "",
        type: "",
        model: "",
        status: "offline",
        location: {
          building: "",
          floor: "",
          room: "",
          coordinates: {
            lat: 0,
            lng: 0
          }
        },
        manufacturer: "",
        installTime: "",
        lastMaintenance: "",
        nextMaintenance: ""
      },
      deviceMetrics: [],
      alarmList: [],
      maintenanceRecords: [],
      isLoading: false
    };
  },
  onLoad(options) {
    this.deviceId = options.id;
    this.loadDeviceDetails();
    this.loadDeviceMetrics();
    this.loadDeviceAlarms();
    this.loadMaintenanceRecords();
  },
  methods: {
    // 加载设备详情
    loadDeviceDetails() {
      this.isLoading = true;
      utils_api.deviceApi.getDetail(this.deviceId).then((res) => {
        common_vendor.index.hideLoading();
        if (res.code === 200) {
          this.deviceInfo = res.data;
        } else {
          this.showError(res.message || "加载失败");
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        this.showError("网络异常，请稍后重试");
        common_vendor.index.__f__("error", "at pages/device/detail.vue:185", "获取设备详情失败:", err);
      });
    },
    // 加载设备运行指标
    loadDeviceMetrics() {
      setTimeout(() => {
        switch (this.deviceInfo.type) {
          case "pump":
            this.deviceMetrics = [
              { label: "流量", value: "200 m³/h", isAlert: false },
              { label: "温度", value: "65 °C", isAlert: false },
              { label: "压力", value: "0.4 MPa", isAlert: false },
              { label: "功率", value: "5.5 kW", isAlert: false }
            ];
            break;
          case "valve":
            this.deviceMetrics = [
              { label: "开度", value: "80%", isAlert: false },
              { label: "压差", value: "0.2 MPa", isAlert: false }
            ];
            break;
          case "sensor":
            this.deviceMetrics = [
              { label: "温度", value: "72 °C", isAlert: true },
              { label: "信号强度", value: "95%", isAlert: false }
            ];
            break;
          case "controller":
            this.deviceMetrics = [
              { label: "CPU使用率", value: "45%", isAlert: false },
              { label: "内存使用率", value: "60%", isAlert: false },
              { label: "网络状态", value: "正常", isAlert: false }
            ];
            break;
          default:
            this.deviceMetrics = [];
        }
      }, 600);
    },
    // 加载设备告警信息
    loadDeviceAlarms() {
      setTimeout(() => {
        this.alarmList = [
          {
            id: "al001",
            level: "warning",
            title: "温度过高警告",
            description: "设备温度达到75°C，接近阈值（80°C）",
            time: "2023-12-05 14:30:25"
          },
          {
            id: "al002",
            level: "error",
            title: "压力异常",
            description: "压力低于正常运行范围，可能存在泄漏",
            time: "2023-12-04 08:15:10"
          }
        ];
      }, 700);
    },
    // 加载维护记录
    loadMaintenanceRecords() {
      setTimeout(() => {
        this.maintenanceRecords = [
          {
            id: "mr001",
            title: "例行检查",
            description: "检查设备运行状态，更换润滑油，无异常",
            time: "2023-12-01 10:15:00",
            operator: "张工"
          },
          {
            id: "mr002",
            title: "故障维修",
            description: "更换损坏的轴承，恢复设备运行",
            time: "2023-11-15 14:30:00",
            operator: "李工"
          }
        ];
      }, 800);
    },
    // 刷新数据
    refreshData() {
      common_vendor.index.showLoading({
        title: "刷新中..."
      });
      this.loadDeviceMetrics();
      this.loadDeviceAlarms();
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "数据已更新",
          icon: "success"
        });
      }, 800);
    },
    // 上报故障
    reportFault() {
      common_vendor.index.navigateTo({
        url: `/pages/fault/report?deviceId=${this.deviceId}`
      });
    },
    // 添加维护记录
    createMaintenance() {
      common_vendor.index.navigateTo({
        url: `/pages/maintenance/add?deviceId=${this.deviceId}`
      });
    },
    // 跳转到告警列表
    navigateToAlarmList() {
      common_vendor.index.navigateTo({
        url: `/pages/alarm/list?deviceId=${this.deviceId}`
      });
    },
    // 跳转到维护记录列表
    navigateToMaintenanceList() {
      common_vendor.index.navigateTo({
        url: `/pages/maintenance/list?deviceId=${this.deviceId}`
      });
    },
    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case "online":
          return "在线";
        case "offline":
          return "离线";
        case "fault":
          return "故障";
        default:
          return "未知";
      }
    },
    // 获取位置文本
    getLocation() {
      const loc = this.deviceInfo.location;
      if (!loc)
        return "未知位置";
      return `${loc.building || ""} ${loc.floor || ""} ${loc.room || ""}`.trim();
    },
    // 格式化日期
    formatDate(dateString) {
      if (!dateString)
        return "未知";
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
    },
    // 格式化维护日期
    formatMaintenanceDate(dateString) {
      if (!dateString)
        return "";
      const date = new Date(dateString);
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    },
    // 格式化维护时间
    formatMaintenanceTime(dateString) {
      if (!dateString)
        return "";
      const date = new Date(dateString);
      return `${String(date.getHours()).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($options.getStatusText($data.deviceInfo.status)),
    b: common_vendor.n($data.deviceInfo.status),
    c: common_vendor.t($data.deviceInfo.name),
    d: common_vendor.t($data.deviceInfo.type),
    e: common_vendor.t($data.deviceInfo.model),
    f: common_vendor.t($data.deviceInfo.deviceId),
    g: common_vendor.t($options.getLocation()),
    h: common_vendor.t($data.deviceInfo.manufacturer || "未知"),
    i: common_vendor.t($options.formatDate($data.deviceInfo.installTime)),
    j: common_vendor.o((...args) => $options.refreshData && $options.refreshData(...args)),
    k: common_vendor.f($data.deviceMetrics, (metric, index, i0) => {
      return {
        a: common_vendor.t(metric.value),
        b: metric.isAlert ? 1 : "",
        c: common_vendor.t(metric.label),
        d: index
      };
    }),
    l: $data.deviceInfo.type === "pump" || $data.deviceInfo.type === "sensor"
  }, $data.deviceInfo.type === "pump" || $data.deviceInfo.type === "sensor" ? {} : {}, {
    m: $data.alarmList.length > 0
  }, $data.alarmList.length > 0 ? {
    n: common_vendor.o((...args) => $options.navigateToAlarmList && $options.navigateToAlarmList(...args)),
    o: common_vendor.f($data.alarmList, (alarm, index, i0) => {
      return {
        a: common_vendor.n(alarm.level),
        b: common_vendor.t(alarm.title),
        c: common_vendor.t(alarm.description),
        d: common_vendor.t(alarm.time),
        e: index
      };
    })
  } : {}, {
    p: common_vendor.o((...args) => $options.navigateToMaintenanceList && $options.navigateToMaintenanceList(...args)),
    q: common_vendor.f($data.maintenanceRecords, (record, index, i0) => {
      return {
        a: common_vendor.t($options.formatMaintenanceDate(record.time)),
        b: common_vendor.t($options.formatMaintenanceTime(record.time)),
        c: common_vendor.t(record.title),
        d: common_vendor.t(record.description),
        e: common_vendor.t(record.operator),
        f: index
      };
    }),
    r: $data.maintenanceRecords.length === 0
  }, $data.maintenanceRecords.length === 0 ? {} : {}, {
    s: common_vendor.o((...args) => $options.reportFault && $options.reportFault(...args)),
    t: common_vendor.o((...args) => $options.createMaintenance && $options.createMaintenance(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/device/detail.js.map
