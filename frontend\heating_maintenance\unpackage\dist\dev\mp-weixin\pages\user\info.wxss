/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.user-info-container {
  padding-bottom: 50rpx;
  background-color: #f5f7fa;
}
.user-header {
  background-image: linear-gradient(135deg, #1890ff, #0076e4);
  padding: 120rpx 30rpx 80rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  overflow: hidden;
}
.user-header .user-header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><circle cx="20" cy="20" r="15" fill="rgba(255,255,255,0.05)"/><circle cx="70" cy="70" r="25" fill="rgba(255,255,255,0.05)"/><circle cx="100" cy="30" r="20" fill="rgba(255,255,255,0.05)"/></svg>');
  background-size: 200rpx;
  opacity: 0.8;
}
.user-header .avatar-container {
  position: relative;
  margin-bottom: 20rpx;
  z-index: 1;
}
.user-header .avatar-container .avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  border: 6rpx solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
}
.user-header .avatar-container .edit-avatar {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 56rpx;
  height: 56rpx;
  background-color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);
}
.user-header .avatar-container .edit-avatar .iconfont {
  font-size: 30rpx;
  color: #1890ff;
}
.user-header .user-details {
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  z-index: 1;
}
.user-header .user-details .user-name {
  font-size: 44rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
.user-header .user-details .user-role {
  margin-bottom: 12rpx;
}
.user-header .user-details .user-role .role-badge {
  background-color: rgba(255, 255, 255, 0.25);
  padding: 8rpx 24rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);
}
.user-header .user-details .user-id {
  font-size: 26rpx;
  opacity: 0.9;
  letter-spacing: 1rpx;
}
.work-stats {
  margin-top: -50rpx;
  margin-left: 30rpx;
  margin-right: 30rpx;
  margin-bottom: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 10rpx 30rpx rgba(24, 144, 255, 0.1);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 30rpx 0;
  position: relative;
  z-index: 10;
}
.work-stats .stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx 0;
}
.work-stats .stat-item .stat-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 12rpx;
  line-height: 1;
}
.work-stats .stat-item .stat-label {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}
.work-stats .stat-divider {
  width: 2rpx;
  height: 70rpx;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.02), rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.02));
}
.quick-actions {
  margin: 0 30rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.05);
}
.quick-actions .quick-grid {
  display: flex;
  justify-content: space-around;
}
.quick-actions .quick-grid .quick-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx 0;
  transition: transform 0.2s;
}
.quick-actions .quick-grid .quick-item:active {
  transform: scale(0.95);
}
.quick-actions .quick-grid .quick-item .quick-icon-bg {
  width: 110rpx;
  height: 110rpx;
  border-radius: 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15rpx;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.1), rgba(24, 144, 255, 0.2));
  box-shadow: 0 6rpx 15rpx rgba(24, 144, 255, 0.1);
}
.quick-actions .quick-grid .quick-item .quick-icon-bg .iconfont {
  font-size: 52rpx;
  color: #1890ff;
}
.quick-actions .quick-grid .quick-item .quick-icon-bg:nth-child(1) {
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.1), rgba(24, 144, 255, 0.2));
}
.quick-actions .quick-grid .quick-item:nth-child(1) .quick-icon-bg {
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.1), rgba(24, 144, 255, 0.2));
}
.quick-actions .quick-grid .quick-item:nth-child(2) .quick-icon-bg {
  background: linear-gradient(135deg, rgba(82, 196, 26, 0.1), rgba(82, 196, 26, 0.2));
}
.quick-actions .quick-grid .quick-item:nth-child(2) .quick-icon-bg .iconfont {
  color: #52c41a;
}
.quick-actions .quick-grid .quick-item:nth-child(3) .quick-icon-bg {
  background: linear-gradient(135deg, rgba(250, 140, 22, 0.1), rgba(250, 140, 22, 0.2));
}
.quick-actions .quick-grid .quick-item:nth-child(3) .quick-icon-bg .iconfont {
  color: #fa8c16;
}
.quick-actions .quick-grid .quick-item:nth-child(4) .quick-icon-bg {
  background: linear-gradient(135deg, rgba(245, 34, 45, 0.1), rgba(245, 34, 45, 0.2));
}
.quick-actions .quick-grid .quick-item:nth-child(4) .quick-icon-bg .iconfont {
  color: #f5222d;
}
.quick-actions .quick-grid .quick-item .item-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.feature-list {
  margin-top: 20rpx;
}
.feature-list .feature-section {
  margin: 0 30rpx 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.05);
}
.feature-list .feature-section .section-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
}
.feature-list .feature-section .section-title .section-icon {
  font-size: 32rpx;
  color: #1890ff;
  margin-right: 10rpx;
}
.feature-list .feature-section .menu-item {
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: background-color 0.2s;
}
.feature-list .feature-section .menu-item:active {
  background-color: rgba(0, 0, 0, 0.02);
}
.feature-list .feature-section .menu-item:last-child {
  border-bottom: none;
}
.feature-list .feature-section .menu-item .menu-icon-container {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.08);
}
.feature-list .feature-section .menu-item .menu-icon-container .iconfont {
  font-size: 36rpx;
  color: #fff;
}
.feature-list .feature-section .menu-item .menu-icon-container.profile-icon {
  background: linear-gradient(135deg, #1890ff, #36b3ff);
}
.feature-list .feature-section .menu-item .menu-icon-container.message-icon {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}
.feature-list .feature-section .menu-item .menu-icon-container.security-icon {
  background: linear-gradient(135deg, #fa8c16, #ffa940);
}
.feature-list .feature-section .menu-item .menu-icon-container.binding-icon {
  background: linear-gradient(135deg, #722ed1, #9254de);
}
.feature-list .feature-section .menu-item .menu-icon-container.help-icon {
  background: linear-gradient(135deg, #13c2c2, #36cfc9);
}
.feature-list .feature-section .menu-item .menu-icon-container.about-icon {
  background: linear-gradient(135deg, #eb2f96, #f759ab);
}
.feature-list .feature-section .menu-item .menu-icon-container.message-test-icon {
  background: linear-gradient(135deg, #13c2c2, #36cfc9);
}
.feature-list .feature-section .menu-item .menu-content {
  flex: 1;
  font-size: 30rpx;
}
.feature-list .feature-section .menu-item .iconfont.icon-arrow-right {
  font-size: 28rpx;
  color: #ccc;
}
.logout-btn {
  margin: 50rpx 30rpx;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #fff;
  color: #f5222d;
  text-align: center;
  border-radius: 12rpx;
  font-size: 32rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.05);
  font-weight: bold;
}
.logout-btn:active {
  background-color: rgba(255, 255, 255, 0.9);
  transform: translateY(2rpx);
}