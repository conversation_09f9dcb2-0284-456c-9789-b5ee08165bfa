<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="f11b85d5-9d5e-4665-b7b5-20653ad93531" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="userSettingsFile" value="D:\maven\apache-maven-3.8.6-bin\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2xNwKqu62CHhpNDBQYMcLv5bHIR" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.heating-system [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.heating-system [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.heating-system [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.heating-system [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.heating-system [test].executor&quot;: &quot;Run&quot;,
    &quot;Maven.heating-system [validate].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.HeatingApplication.executor&quot;: &quot;Debug&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/tb_project/tbkj_java_framework/4-Source/src/hcms&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;SDK&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.254023&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.heating.entity.attendance" />
    </key>
  </component>
  <component name="RunManager">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="backend" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="backend" />
      <option name="filePath" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="HeatingApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="heating-system" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.heating.HeatingApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="f11b85d5-9d5e-4665-b7b5-20653ad93531" name="更改" comment="" />
      <created>1747791777892</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1747791777892</updated>
      <workItem from="1747791779104" duration="12450000" />
      <workItem from="1747873735291" duration="56535000" />
      <workItem from="1748391924327" duration="16140000" />
      <workItem from="1748478286885" duration="24356000" />
      <workItem from="1749178960040" duration="16344000" />
      <workItem from="1749689389898" duration="17862000" />
      <workItem from="1749778631112" duration="50618000" />
      <workItem from="1750120214281" duration="15287000" />
      <workItem from="1750152995959" duration="2263000" />
      <workItem from="1750205889088" duration="13122000" />
      <workItem from="1750235994715" duration="139000" />
      <workItem from="1750237147150" duration="4019000" />
      <workItem from="1750293018694" duration="13706000" />
      <workItem from="1750379602914" duration="10265000" />
      <workItem from="1750724763831" duration="5357000" />
      <workItem from="1750811846068" duration="1848000" />
      <workItem from="1751349659766" duration="8031000" />
      <workItem from="1751416061653" duration="10895000" />
      <workItem from="1751502372974" duration="15783000" />
      <workItem from="1751589048340" duration="14358000" />
      <workItem from="1753063320768" duration="4296000" />
      <workItem from="1754302556040" duration="255000" />
      <workItem from="1754302920983" duration="492000" />
      <workItem from="1754353701788" duration="51000" />
      <workItem from="1754531951779" duration="5968000" />
      <workItem from="1755756896026" duration="4440000" />
      <workItem from="1755822029859" duration="9308000" />
      <workItem from="1756081102901" duration="8388000" />
      <workItem from="1756167679624" duration="1208000" />
      <workItem from="1756255312174" duration="1230000" />
      <workItem from="1756686976788" duration="13049000" />
      <workItem from="1756773964921" duration="870000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/heating/controller/AuthController.java</url>
          <line>36</line>
          <option name="timeStamp" value="14" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>