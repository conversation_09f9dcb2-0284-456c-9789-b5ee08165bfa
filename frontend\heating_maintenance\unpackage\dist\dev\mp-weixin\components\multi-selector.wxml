<view class="container"><view class="header"><view class="header-left" bindtap="{{a}}"><text class="header-icon">×</text><text class="header-cancel">取消</text></view><text class="header-title">{{b}}</text><view class="header-right" bindtap="{{c}}"><text class="header-confirm">确定</text></view></view><view class="search-box"><view class="search-input-wrapper"><text class="search-icon">🔍</text><input type="text" placeholder="搜索" class="search-input" bindinput="{{d}}" value="{{e}}"/><text wx:if="{{f}}" class="clear-icon" bindtap="{{g}}">×</text></view></view><view wx:if="{{h}}" class="selected-area"><view class="selected-header"><text class="selected-title">已选择 ({{i}})</text><text class="selected-clear" bindtap="{{j}}">清空</text></view><scroll-view class="selected-list" scroll-x><view wx:for="{{k}}" wx:for-item="item" wx:key="b" class="selected-item" bindtap="{{item.c}}"><text class="selected-item-name">{{item.a}}</text><text class="selected-item-remove">×</text></view></scroll-view></view><scroll-view class="items-list" scroll-y style="{{'height:' + o}}"><view wx:for="{{l}}" wx:for-item="item" wx:key="d" class="list-item" bindtap="{{item.e}}"><view class="item-content"><text class="item-name">{{item.a}}</text><view class="{{['checkbox', item.c && 'checked']}}"><text wx:if="{{item.b}}" class="checkbox-inner">✓</text></view></view></view><view wx:if="{{m}}" class="empty-tip"><text class="empty-text">{{n}}</text></view></scroll-view></view>