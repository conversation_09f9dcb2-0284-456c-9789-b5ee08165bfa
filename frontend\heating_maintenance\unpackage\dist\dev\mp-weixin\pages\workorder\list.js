"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      // 当前用户ID
      userId: common_vendor.index.getStorageSync("userId") || "",
      // 用户项目权限
      heatUnitId: common_vendor.index.getStorageSync("heatUnitId") || "",
      // 当前激活的标签页
      activeTab: "my",
      // 'my' 或 'pending'
      // 列表数据
      workorderList: [],
      currentPage: 1,
      pageSize: 10,
      hasNoMore: false,
      isLoadingMore: false,
      isRefreshing: false,
      isLoading: false,
      loadError: false,
      // 筛选条件显示控制
      showFilterCondition: false,
      // 筛选条件
      filterDate: "",
      // 筛选条件
      filters: {
        startDate: "",
        endDate: ""
      }
    };
  },
  onLoad() {
    this.getWorkorderList();
  },
  // 下拉刷新
  onPullDownRefresh() {
    this.refreshData().then(() => {
      common_vendor.index.stopPullDownRefresh();
    });
  },
  // 上拉加载更多
  onReachBottom() {
    common_vendor.index.__f__("log", "at pages/workorder/list.vue:197", "执行上拉加载");
    this.loadMoreData();
  },
  methods: {
    // 切换标签页
    switchTab(tab) {
      if (this.activeTab !== tab) {
        this.activeTab = tab;
        this.currentPage = 1;
        this.workorderList = [];
        this.hasNoMore = false;
        this.getWorkorderList();
      }
    },
    // 加载工单列表
    async getWorkorderList(isLoadMore = false) {
      if (this.isLoading)
        return;
      this.isLoading = true;
      if (!isLoadMore) {
        this.loadError = false;
      }
      try {
        const params = {
          page: this.currentPage,
          pageSize: this.pageSize,
          uid: this.userId,
          heatUnitId: this.heatUnitId
          // 添加用户项目权限参数
        };
        if (this.filterDate) {
          params.date = this.filterDate;
        }
        if (this.activeTab === "my") {
          params.type = "my";
        } else if (this.activeTab === "pending") {
          params.type = "pending";
          params.status = "待接单";
        }
        const res = await utils_api.workOrderApi.getList(params);
        if (res.code === 200) {
          const { list, total, totalPages } = res.data;
          if (isLoadMore) {
            this.workorderList = [...this.workorderList, ...list];
          } else {
            this.workorderList = list;
          }
          this.hasNoMore = this.currentPage >= totalPages;
        } else {
          this.loadError = true;
          common_vendor.index.showToast({
            title: res.message || "获取工单列表失败",
            icon: "none"
          });
        }
      } catch (err) {
        common_vendor.index.__f__("error", "at pages/workorder/list.vue:268", "获取工单列表异常:", err);
        this.loadError = true;
        common_vendor.index.showToast({
          title: "网络异常，请稍后重试",
          icon: "none"
        });
      } finally {
        this.isLoading = false;
        this.isLoadingMore = false;
        this.isRefreshing = false;
      }
    },
    // 加载更多数据
    loadMoreData() {
      if (!this.isLoadingMore && !this.hasNoMore && !this.isLoading) {
        this.isLoadingMore = true;
        this.currentPage++;
        this.getWorkorderList(true);
      }
    },
    // 下拉刷新
    async refreshData() {
      this.isRefreshing = true;
      this.currentPage = 1;
      this.hasNoMore = false;
      this.workorderList = [];
      this.filterDate = "";
      await this.getWorkorderList(false);
    },
    handleComplete() {
      common_vendor.index.navigateTo({
        url: `/pages/workorder/complete?id=${this.orderId}`
      });
    },
    // 日期选择变更
    onDateChange(e) {
      this.filterDate = e.detail.value;
      this.currentPage = 1;
      this.workorderList = [];
      this.hasNoMore = false;
      this.getWorkorderList();
    },
    // 获取状态对应的样式类
    getStatusClass(status) {
      const statusClassMap = {
        "待接单": "pending",
        "处理中": "processing",
        "已完成": "completed",
        "已转派": "transferred",
        "已取消": "cancelled"
      };
      return statusClassMap[status] || "default";
    },
    // 获取显示的工单状态
    getDisplayStatus(item) {
      if (item.transferUserId && item.transferUserId === this.userId) {
        return "已转派";
      }
      return item.orderStatus;
    },
    // 获取故障等级对应的样式类
    getFaultLevelClass(level) {
      const levelClassMap = {
        提示: "notice",
        一般: "normal",
        重要: "important",
        严重: "critical"
      };
      return levelClassMap[level] || "default";
    },
    // 查看工单详情
    viewOrderDetail(orderId) {
      common_vendor.index.navigateTo({
        url: `/pages/workorder/detail?id=${orderId}`
      });
    },
    // 处理工单操作
    handleAction(item, action) {
      switch (action) {
        case "detail":
          this.viewOrderDetail(item.orderId);
          break;
        case "accept":
          this.acceptOrder(item);
          break;
        case "complete":
          common_vendor.index.navigateTo({
            url: `/pages/workorder/complete?id=${item.orderId}`
          });
          break;
      }
    },
    // 接单操作
    acceptOrder(item) {
      common_vendor.index.showModal({
        title: "接单确认",
        content: "确认接单？接单后您将负责处理此工单",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({
              title: "处理中..."
            });
            const requestData = {
              order_id: item.orderId,
              repair_user_id: this.userId,
              order_status: "处理中"
            };
            utils_api.workOrderApi.updateStatus(requestData).then((res2) => {
              if (res2.code === 200) {
                common_vendor.index.showToast({
                  title: "接单成功",
                  icon: "success"
                });
                this.refreshData();
              } else {
                common_vendor.index.showToast({
                  title: res2.message || "接单失败",
                  icon: "none"
                });
              }
            }).catch((err) => {
              common_vendor.index.__f__("error", "at pages/workorder/list.vue:408", "接单失败:", err);
              common_vendor.index.showToast({
                title: "网络异常，请稍后重试",
                icon: "none"
              });
            }).finally(() => {
              common_vendor.index.hideLoading();
            });
          }
        }
      });
    },
    // 切换筛选条件区域显示/隐藏
    toggleFilterCondition() {
      this.showFilterCondition = !this.showFilterCondition;
    },
    // 日期选择
    onStartDateChange(e) {
      this.filters.startDate = e.detail.value;
    },
    // 重置筛选
    resetFilters() {
      this.filters = {
        startDate: ""
      };
    },
    // 应用筛选
    applyFilters() {
      this.toggleFilterCondition();
      this.currentPage = 1;
      this.getWorkorderList();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.activeTab === "my" ? 1 : "",
    b: common_vendor.o(($event) => $options.switchTab("my")),
    c: $data.activeTab === "pending" ? 1 : "",
    d: common_vendor.o(($event) => $options.switchTab("pending")),
    e: common_vendor.t($data.filterDate || "选择日期"),
    f: $data.filterDate,
    g: common_vendor.o((...args) => $options.onDateChange && $options.onDateChange(...args)),
    h: common_vendor.o((...args) => $options.refreshData && $options.refreshData(...args)),
    i: $data.showFilterCondition
  }, $data.showFilterCondition ? {
    j: common_vendor.o((...args) => $options.toggleFilterCondition && $options.toggleFilterCondition(...args)),
    k: common_vendor.t($data.filters.startDate || "选择日期"),
    l: $data.filters.startDate,
    m: common_vendor.o((...args) => $options.onStartDateChange && $options.onStartDateChange(...args)),
    n: common_vendor.o((...args) => $options.resetFilters && $options.resetFilters(...args)),
    o: common_vendor.o((...args) => $options.applyFilters && $options.applyFilters(...args))
  } : {}, {
    p: $data.workorderList.length > 0
  }, $data.workorderList.length > 0 ? common_vendor.e({
    q: common_vendor.f($data.workorderList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.orderNo),
        b: common_vendor.t($options.getDisplayStatus(item)),
        c: common_vendor.n($options.getStatusClass($options.getDisplayStatus(item))),
        d: common_vendor.t(item.heatUnitName),
        e: common_vendor.t(item.faultType),
        f: common_vendor.t(item.faultLevel),
        g: common_vendor.n($options.getFaultLevelClass(item.faultLevel)),
        h: common_vendor.t(item.createdTime),
        i: common_vendor.o(($event) => $options.handleAction(item, "detail"), index),
        j: $data.activeTab === "pending" && item.orderStatus === "待接单"
      }, $data.activeTab === "pending" && item.orderStatus === "待接单" ? {
        k: common_vendor.o(($event) => $options.handleAction(item, "accept"), index)
      } : {}, {
        l: $data.activeTab === "my" && item.orderStatus === "处理中"
      }, $data.activeTab === "my" && item.orderStatus === "处理中" ? {
        m: common_vendor.o((...args) => $options.handleComplete && $options.handleComplete(...args), index)
      } : {}, {
        n: index,
        o: common_vendor.o(($event) => $options.viewOrderDetail(item.orderId), index)
      });
    }),
    r: $data.isLoadingMore
  }, $data.isLoadingMore ? {} : {}, {
    s: $data.hasNoMore
  }, $data.hasNoMore ? {} : {}) : {
    t: common_assets._imports_0$4,
    v: common_vendor.t($data.activeTab === "my" ? "暂无我的工单" : "暂无待接单工单")
  }, {
    w: $data.showFilterCondition ? "calc(100vh - 480rpx)" : "calc(100vh - 290rpx)",
    x: common_vendor.o((...args) => $options.loadMoreData && $options.loadMoreData(...args)),
    y: $data.isRefreshing,
    z: common_vendor.o((...args) => $options.refreshData && $options.refreshData(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/workorder/list.js.map
