<base-tab-bar u-s="{{['d']}}" u-i="6d746b73-0" bind:__l="__l"><view class="message-center-container"><view class="message-tabs"><block wx:for="{{a}}" wx:for-item="tab"><view wx:if="{{tab.a}}" key="{{tab.e}}" class="{{['message-tab', tab.f && 'active']}}" bindtap="{{tab.g}}">{{tab.b}} <text wx:if="{{tab.c}}" class="badge">{{tab.d}}</text></view><permission-check wx:else u-s="{{['d']}}" key="{{tab.m}}" u-i="{{tab.n}}" bind:__l="__l" u-p="{{tab.o||''}}"><view class="{{['message-tab', tab.k && 'active']}}" bindtap="{{tab.l}}">{{tab.h}} <text wx:if="{{tab.i}}" class="badge">{{tab.j}}</text></view></permission-check></block></view><view wx:if="{{b}}" class="filter-panel"><view wx:for="{{c}}" wx:for-item="item" wx:key="b" class="{{['filter-item', item.c && 'active']}}" bindtap="{{item.d}}">{{item.a}}</view></view><view wx:if="{{d}}" class="filter-panel"><view wx:for="{{e}}" wx:for-item="item" wx:key="b" class="{{['filter-item', item.c && 'active']}}" bindtap="{{item.d}}">{{item.a}}</view></view><view class="message-list"><view wx:for="{{f}}" wx:for-item="message" wx:key="i" class="{{['message-card', message.j && 'unread', message.k]}}" bindtap="{{message.l}}"><view class="message-header"><view class="message-title"><text class="{{['priority-dot', message.a]}}"></text><text>{{message.b}}</text></view><view class="message-time">{{message.c}}</view></view><view class="message-content">{{message.d}}</view><view class="message-footer"><view class="message-type">{{message.e}}</view><view class="message-actions"><block wx:if="{{message.f}}"><view class="action-item ignore-btn" catchtap="{{message.g}}"> 忽略 </view></block><block wx:else><view class="action-item" catchtap="{{message.h}}"> 查看 </view></block></view></view></view></view><view wx:if="{{g}}" class="empty-state"><image class="empty-icon" src="{{h}}"></image><text class="empty-text">暂无消息</text></view><view wx:if="{{i}}" class="load-more" bindtap="{{j}}">加载更多</view><view wx:if="{{k}}" class="no-more">没有更多消息了</view></view></base-tab-bar>