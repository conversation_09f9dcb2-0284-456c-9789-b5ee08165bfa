/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.all-records-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.filter-bar {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
}
.filter-bar .date-range-selector, .filter-bar .staff-selector, .filter-bar .status-selector {
  flex: 1;
  background-color: #fff;
  padding: 15rpx 20rpx;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 26rpx;
  margin-right: 10rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);
}
.filter-bar .date-range-selector:last-child, .filter-bar .staff-selector:last-child, .filter-bar .status-selector:last-child {
  margin-right: 0;
}
.filter-bar .date-range-selector text, .filter-bar .staff-selector text, .filter-bar .status-selector text {
  width: 80%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.search-bar {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 15rpx 20rpx;
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);
}
.search-bar input {
  flex: 1;
  height: 60rpx;
  margin: 0 10rpx;
  font-size: 28rpx;
}
.search-bar .clear-btn {
  font-size: 26rpx;
  color: #999;
}
.statistics-info {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);
}
.statistics-info text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}
.statistics-info .status-count {
  display: flex;
  flex-wrap: wrap;
}
.statistics-info .status-count .status-item {
  margin-right: 20rpx;
}
.statistics-info .status-count .status-item text {
  font-size: 24rpx;
}
.statistics-info .status-count .status-item text.normal {
  color: #4cd964;
}
.statistics-info .status-count .status-item text.late {
  color: #f0ad4e;
}
.statistics-info .status-count .status-item text.early {
  color: #5bc0de;
}
.statistics-info .status-count .status-item text.absent {
  color: #dd524d;
}
.records-list {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);
}
.records-list .record-group {
  margin-bottom: 30rpx;
}
.records-list .record-group .group-header {
  display: flex;
  align-items: center;
  padding: 15rpx 10rpx;
  border-bottom: 1rpx solid #f5f5f5;
  background-color: #f9f9f9;
  border-radius: 8rpx 8rpx 0 0;
}
.records-list .record-group .group-header .date {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}
.records-list .record-group .group-header .week {
  font-size: 24rpx;
  color: #666;
  margin-left: 10rpx;
  background-color: #eaeaea;
  padding: 2rpx 10rpx;
  border-radius: 4rpx;
}
.records-list .record-group .record-item {
  display: flex;
  flex-direction: column;
  padding: 20rpx 10rpx;
  border-bottom: 1rpx solid #f5f5f5;
}
.records-list .record-group .record-item:last-child {
  border-bottom: none;
}
.records-list .record-group .record-item .staff-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.records-list .record-group .record-item .staff-info .avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 2rpx solid #eaeaea;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}
.records-list .record-group .record-item .staff-info .staff-details {
  margin-left: 20rpx;
}
.records-list .record-group .record-item .staff-info .staff-details .staff-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}
.records-list .record-group .record-item .staff-info .staff-details .staff-dept {
  font-size: 24rpx;
  color: #666;
  margin-top: 5rpx;
  background-color: #f5f5f5;
  display: inline-block;
  padding: 2rpx 10rpx;
  border-radius: 4rpx;
}
.records-list .record-group .record-item .record-details {
  padding-left: 100rpx;
}
.records-list .record-group .record-item .record-details .time-record {
  display: flex;
  flex-wrap: wrap;
}
.records-list .record-group .record-item .record-details .time-record .time-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  margin-bottom: 15rpx;
}
.records-list .record-group .record-item .record-details .time-record .time-item .time-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
  background-color: #f9f9f9;
  padding: 4rpx 10rpx;
  border-radius: 4rpx;
}
.records-list .record-group .record-item .record-details .time-record .time-item .time-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.records-list .record-group .record-item .record-details .time-record .time-item .time-value.abnormal {
  color: #f0ad4e;
}
.records-list .record-group .record-item .record-details .time-record .time-item .status-tag {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-left: 10rpx;
}
.records-list .record-group .record-item .record-details .time-record .time-item .status-tag.late {
  background-color: #fef0e5;
  color: #f0ad4e;
  border: 1rpx solid #f0ad4e;
}
.records-list .record-group .record-item .record-details .time-record .time-item .status-tag.early {
  background-color: #e5f5fa;
  color: #5bc0de;
  border: 1rpx solid #5bc0de;
}
.records-list .record-group .record-item .record-details .location-info {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-top: 5rpx;
}
.records-list .record-group .record-item .record-details .location-info .location-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
  background-color: #f9f9f9;
  padding: 4rpx 10rpx;
  border-radius: 4rpx;
}
.records-list .record-group .record-item .record-details .location-info .location-value {
  font-size: 26rpx;
  color: #666;
  flex: 1;
  word-break: break-all;
}
.records-list .load-more, .records-list .no-more {
  text-align: center;
  padding: 30rpx 0;
}
.records-list .load-more text, .records-list .no-more text {
  font-size: 26rpx;
  color: #999;
}
.records-list .empty-records {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}
.records-list .empty-records image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.records-list .empty-records text {
  font-size: 28rpx;
  color: #999;
}
.date-popup, .staff-popup, .status-popup {
  background-color: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
}
.date-popup .popup-header, .staff-popup .popup-header, .status-popup .popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}
.date-popup .popup-header text, .staff-popup .popup-header text, .status-popup .popup-header text {
  font-size: 32rpx;
  font-weight: bold;
}
.date-popup .popup-header .close-btn, .staff-popup .popup-header .close-btn, .status-popup .popup-header .close-btn {
  font-size: 28rpx;
  color: #007AFF;
}
.date-popup .date-options {
  padding: 0 30rpx 30rpx;
}
.date-popup .date-options .date-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}
.date-popup .date-options .date-option text {
  font-size: 30rpx;
  color: #333;
}
.date-popup .date-options .date-option.active text {
  color: #007AFF;
}
.date-popup .date-options .custom-date-range {
  padding: 20rpx 0;
}
.date-popup .date-options .custom-date-range text {
  font-size: 30rpx;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}
.date-popup .date-options .custom-date-range .date-inputs {
  display: flex;
  align-items: center;
}
.date-popup .date-options .custom-date-range .date-inputs .date-picker {
  flex: 1;
}
.date-popup .date-options .custom-date-range .date-inputs .date-input {
  background-color: #f5f5f5;
  padding: 15rpx 20rpx;
  border-radius: 10rpx;
}
.date-popup .date-options .custom-date-range .date-inputs .date-input text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 0;
}
.date-popup .date-options .custom-date-range .date-inputs .date-separator {
  margin: 0 20rpx;
  font-size: 28rpx;
  color: #999;
}
.date-popup .date-options .custom-date-range .custom-date-actions {
  margin-top: 20rpx;
  display: flex;
  justify-content: flex-end;
}
.date-popup .date-options .custom-date-range .custom-date-actions .btn-apply {
  background-color: #007AFF;
  color: #fff;
  font-size: 28rpx;
  padding: 10rpx 30rpx;
  border-radius: 8rpx;
}
.date-popup .date-options .custom-date-range .custom-date-actions .btn-apply:disabled {
  background-color: #cccccc;
}
.staff-popup .search-box {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  margin: 20rpx 30rpx;
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
}
.staff-popup .search-box input {
  flex: 1;
  height: 60rpx;
  margin-left: 10rpx;
  font-size: 28rpx;
}
.staff-popup .staff-list {
  max-height: 600rpx;
  padding: 0 15rpx;
}
.staff-popup .staff-list .staff-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 15rpx;
  border-bottom: 1rpx solid #f5f5f5;
  margin-bottom: 5rpx;
  border-radius: 8rpx;
}
.staff-popup .staff-list .staff-item:active {
  background-color: #f9f9f9;
}
.staff-popup .staff-list .staff-item .staff-info {
  display: flex;
  flex-direction: column;
}
.staff-popup .staff-list .staff-item .staff-info .staff-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}
.staff-popup .staff-list .staff-item .staff-info .staff-dept {
  font-size: 24rpx;
  color: #666;
  margin-top: 6rpx;
  background-color: #f5f5f5;
  display: inline-block;
  padding: 2rpx 10rpx;
  border-radius: 4rpx;
  max-width: 400rpx;
}
.status-popup .status-list {
  padding-bottom: 30rpx;
}
.status-popup .status-list .status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}
.status-popup .status-list .status-item text {
  font-size: 30rpx;
  color: #333;
}