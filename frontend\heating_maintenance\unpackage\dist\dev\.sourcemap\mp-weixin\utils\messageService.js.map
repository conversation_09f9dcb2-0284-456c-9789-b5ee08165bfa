{"version": 3, "file": "messageService.js", "sources": ["utils/messageService.js"], "sourcesContent": ["// utils/messageService.js\r\n\r\n// 引入 uni-app 的请求模块\r\nimport { request } from './api.js'; // 修改：使用相对路径导入同目录的 api.js\r\n// 或者使用 uni-app 自带的请求\r\n// const request = uni.request; \r\n\r\n// 定时器ID\r\nlet intervalId = null;\r\n// 服务运行状态标志\r\nlet isRunning = false;\r\n// 轮询周期（毫秒）\r\nconst POLLING_INTERVAL = 10000; // 10秒\r\n// 轮询执行锁标志\r\nlet isPolling = false; \r\n\r\n// 调用后端消息接口的通用函数\r\nasync function fetchMessages(apiPath, messageType) {\r\n  console.log(`消息服务：正在调用 ${messageType} 消息接口 (${apiPath})...`);\r\n  try {\r\n    // 注意：根据你的 request 封装调整参数\r\n    // 如果直接使用 uni.request，结构类似\r\n    const res = await request({\r\n      url: apiPath,  \r\n      method: 'GET' \r\n    }); \r\n    // 假设你的 request 封装返回了完整的响应对象\r\n    // 如果直接用 uni.request, res 是 [error, responseData]\r\n    // 需要根据你的封装或 uni.request 的返回格式处理 res\r\n    if (res && res.code === 200 && res.data) {\r\n      console.log(`消息服务：成功获取 ${messageType} 消息`, res.data);\r\n      // 在这里可以处理获取到的消息，例如触发事件、存入状态管理等\r\n      if (res.data.length > 0) {\r\n          console.warn(`消息服务：收到 ${res.data.length} 条新的 ${messageType} 消息！`);\r\n          // 可选：触发全局事件或通知\r\n          uni.$emit('newMessageReceived', { type: messageType, data: res.data });\r\n      }\r\n    } else {\r\n      console.error(`消息服务：获取 ${messageType} 消息失败`, res);\r\n    }\r\n  } catch (error) {\r\n    // 只记录错误，不中断轮询\r\n    console.error(`消息服务：调用 ${messageType} 接口 (${apiPath}) 时发生网络错误或异常`, error);\r\n  }\r\n}\r\n\r\n// 轮询执行的任务\r\nasync function pollTasks() {\r\n  // 检查服务是否仍在运行且当前没有其他轮询在执行\r\n  if (!isRunning || isPolling) { \r\n    if(isPolling) console.log(\"消息服务：上一次轮询仍在进行中，跳过本次执行。\");\r\n    return; \r\n  }\r\n\r\n  console.log(\"消息服务：开始执行轮询任务...\");\r\n  isPolling = true; // 设置轮询锁\r\n\r\n  try {\r\n    // 使用 Promise.allSettled 来并发调用接口，并等待所有接口完成（无论成功或失败）\r\n    await Promise.allSettled([\r\n      fetchMessages('/api/patrols/plans/messages', '巡检'), // 3.14 巡检消息\r\n      fetchMessages('/api/alarm/messages', '告警'),        // 4.5 告警消息\r\n      fetchMessages('/api/faults/messages', '故障'),       // 4.6 故障消息 \r\n      fetchMessages('/api/WorkOrders/messages', '工单')    // 5.6 工单消息\r\n    ]);\r\n    console.log(\"消息服务：轮询任务执行完毕。\");\r\n  } catch (error) {\r\n    // Promise.allSettled 不会走到 catch, 但保留以防万一\r\n    console.error(\"消息服务：轮询任务中发生未预料的错误\", error);\r\n  } finally {\r\n    isPolling = false; // 释放轮询锁\r\n  }\r\n}\r\n\r\n// 启动消息服务\r\nfunction startService() {\r\n  // 强制清除可能存在的旧定时器\r\n  if (intervalId) {\r\n    console.log(\"消息服务：检测到可能残留的定时器，正在清除...\");\r\n    clearInterval(intervalId);\r\n    intervalId = null;\r\n  }\r\n  // 如果服务已启动，则退出\r\n  if (isRunning) {\r\n    console.log(\"消息服务：服务已经在运行中。\");\r\n    return;\r\n  }\r\n  console.log(\"消息服务：正在启动...\");\r\n  isRunning = true;\r\n  isPolling = false; // 重置轮询锁状态\r\n  // 立即执行一次\r\n  pollTasks(); \r\n  // 设置定时器\r\n  intervalId = setInterval(pollTasks, POLLING_INTERVAL);\r\n  console.log(\"消息服务：已启动，轮询周期\", POLLING_INTERVAL / 1000, \"秒\");\r\n}\r\n\r\n// 停止消息服务\r\nfunction stopService() {\r\n  if (!isRunning) {\r\n    console.log(\"消息服务：服务尚未运行。\");\r\n    return;\r\n  }\r\n  console.log(\"消息服务：正在停止...\");\r\n  clearInterval(intervalId);\r\n  intervalId = null;\r\n  isRunning = false;\r\n  isPolling = false; // 停止时也重置轮询锁\r\n  console.log(\"消息服务：已停止。\");\r\n}\r\n\r\n// 获取服务运行状态\r\nfunction getServiceStatus() {\r\n  return isRunning;\r\n}\r\n\r\n// 导出服务控制函数\r\nexport const messageService = {\r\n  start: startService,\r\n  stop: stopService,\r\n  getStatus: getServiceStatus\r\n}; "], "names": ["uni", "request"], "mappings": ";;;AAQA,IAAI,aAAa;AAEjB,IAAI,YAAY;AAEhB,MAAM,mBAAmB;AAEzB,IAAI,YAAY;AAGhB,eAAe,cAAc,SAAS,aAAa;AACjDA,gBAAAA,oDAAY,aAAa,WAAW,UAAU,OAAO,MAAM;AAC3D,MAAI;AAGF,UAAM,MAAM,MAAMC,kBAAQ;AAAA,MACxB,KAAK;AAAA,MACL,QAAQ;AAAA,IACd,CAAK;AAID,QAAI,OAAO,IAAI,SAAS,OAAO,IAAI,MAAM;AACvCD,oBAAAA,MAAY,MAAA,OAAA,iCAAA,aAAa,WAAW,OAAO,IAAI,IAAI;AAEnD,UAAI,IAAI,KAAK,SAAS,GAAG;AACrBA,sBAAAA,MAAA,MAAA,QAAA,iCAAa,WAAW,IAAI,KAAK,MAAM,QAAQ,WAAW,MAAM;AAEhEA,4BAAI,MAAM,sBAAsB,EAAE,MAAM,aAAa,MAAM,IAAI,KAAI,CAAE;AAAA,MACxE;AAAA,IACP,OAAW;AACLA,0BAAA,MAAA,SAAA,iCAAc,WAAW,WAAW,SAAS,GAAG;AAAA,IACjD;AAAA,EACF,SAAQ,OAAO;AAEdA,kBAAAA,MAAA,MAAA,SAAA,iCAAc,WAAW,WAAW,QAAQ,OAAO,gBAAgB,KAAK;AAAA,EACzE;AACH;AAGA,eAAe,YAAY;AAEzB,MAAI,CAAC,aAAa,WAAW;AAC3B,QAAG;AAAWA,oBAAAA,MAAY,MAAA,OAAA,iCAAA,yBAAyB;AACnD;AAAA,EACD;AAEDA,gBAAAA,oDAAY,kBAAkB;AAC9B,cAAY;AAEZ,MAAI;AAEF,UAAM,QAAQ,WAAW;AAAA,MACvB,cAAc,+BAA+B,IAAI;AAAA;AAAA,MACjD,cAAc,uBAAuB,IAAI;AAAA;AAAA,MACzC,cAAc,wBAAwB,IAAI;AAAA;AAAA,MAC1C,cAAc,4BAA4B,IAAI;AAAA;AAAA,IACpD,CAAK;AACDA,kBAAAA,MAAA,MAAA,OAAA,iCAAY,gBAAgB;AAAA,EAC7B,SAAQ,OAAO;AAEdA,kBAAA,MAAA,MAAA,SAAA,iCAAc,sBAAsB,KAAK;AAAA,EAC7C,UAAY;AACR,gBAAY;AAAA,EACb;AACH;AAGA,SAAS,eAAe;AAEtB,MAAI,YAAY;AACdA,kBAAAA,MAAA,MAAA,OAAA,iCAAY,0BAA0B;AACtC,kBAAc,UAAU;AACxB,iBAAa;AAAA,EACd;AAED,MAAI,WAAW;AACbA,kBAAAA,MAAA,MAAA,OAAA,iCAAY,gBAAgB;AAC5B;AAAA,EACD;AACDA,gBAAAA,MAAY,MAAA,OAAA,iCAAA,cAAc;AAC1B,cAAY;AACZ,cAAY;AAEZ;AAEA,eAAa,YAAY,WAAW,gBAAgB;AACpDA,oEAAY,iBAAiB,mBAAmB,KAAM,GAAG;AAC3D;AAGA,SAAS,cAAc;AACrB,MAAI,CAAC,WAAW;AACdA,kBAAAA,MAAA,MAAA,OAAA,kCAAY,cAAc;AAC1B;AAAA,EACD;AACDA,gBAAAA,MAAY,MAAA,OAAA,kCAAA,cAAc;AAC1B,gBAAc,UAAU;AACxB,eAAa;AACb,cAAY;AACZ,cAAY;AACZA,gBAAAA,MAAA,MAAA,OAAA,kCAAY,WAAW;AACzB;AAGA,SAAS,mBAAmB;AAC1B,SAAO;AACT;AAGY,MAAC,iBAAiB;AAAA,EAC5B,OAAO;AAAA,EACP,MAAM;AAAA,EACN,WAAW;AACb;;"}