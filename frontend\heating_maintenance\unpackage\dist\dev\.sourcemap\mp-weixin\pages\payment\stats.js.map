{"version": 3, "file": "stats.js", "sources": ["pages/payment/stats.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcGF5bWVudC9zdGF0cy52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"payment-stats-container\">\r\n\t\t<!-- 页面标题 -->\r\n\t\t<view class=\"page-header\">\r\n\t\t\t<text class=\"page-title\">缴费统计</text>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 查询条件 -->\r\n\t\t<view class=\"query-form\">\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"form-label\">热力单元</text>\r\n\t\t\t\t<picker class=\"form-picker\" @change=\"handleHeatUnitChange\" :value=\"heatUnitIndex\" :range=\"heatUnitOptions\">\r\n\t\t\t\t\t<view class=\"picker-text\">{{ heatUnitOptions[heatUnitIndex] }}</view>\r\n\t\t\t\t</picker>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"form-label\">时间范围</text>\r\n\t\t\t\t<view class=\"date-range\">\r\n\t\t\t\t\t<picker class=\"date-picker\" mode=\"date\" :value=\"startDate\" @change=\"handleStartDateChange\">\r\n\t\t\t\t\t\t<view class=\"picker-text\">{{ startDate }}</view>\r\n\t\t\t\t\t</picker>\r\n\t\t\t\t\t<text class=\"date-separator\">至</text>\r\n\t\t\t\t\t<picker class=\"date-picker\" mode=\"date\" :value=\"endDate\" @change=\"handleEndDateChange\">\r\n\t\t\t\t\t\t<view class=\"picker-text\">{{ endDate }}</view>\r\n\t\t\t\t\t</picker>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"form-label\">缴费状态</text>\r\n\t\t\t\t<picker class=\"form-picker\" @change=\"handleStatusChange\" :value=\"statusIndex\" :range=\"statusOptions\">\r\n\t\t\t\t\t<view class=\"picker-text\">{{ statusOptions[statusIndex] }}</view>\r\n\t\t\t\t</picker>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<button class=\"query-btn\" @click=\"queryStats\">查询</button>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 统计卡片 -->\r\n\t\t<view class=\"stats-cards\" v-if=\"!loading\">\r\n\t\t\t<view class=\"stats-card\">\r\n\t\t\t\t<text class=\"stats-value\">¥{{ statsData.total.toLocaleString() }}</text>\r\n\t\t\t\t<text class=\"stats-label\">总金额</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"stats-card\">\r\n\t\t\t\t<text class=\"stats-value\">¥{{ statsData.paid.toLocaleString() }}</text>\r\n\t\t\t\t<text class=\"stats-label\">已缴费</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"stats-card\">\r\n\t\t\t\t<text class=\"stats-value\">¥{{ statsData.unpaid.toLocaleString() }}</text>\r\n\t\t\t\t<text class=\"stats-label\">未缴费</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"stats-card\">\r\n\t\t\t\t<text class=\"stats-value\">{{ (statsData.paid / statsData.total * 100).toFixed(1) }}%</text>\r\n\t\t\t\t<text class=\"stats-label\">缴费率</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 加载提示 -->\r\n\t\t<view class=\"loading-container\" v-if=\"loading\">\r\n\t\t\t<uni-load-more status=\"loading\" :content-text=\"loadingText\"></uni-load-more>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 图表区域 -->\r\n\t\t<view class=\"chart-container\" v-if=\"!loading\">\r\n\t\t\t<view class=\"chart-header\">\r\n\t\t\t\t<text class=\"chart-title\">缴费趋势分析</text>\r\n\t\t\t\t<view class=\"chart-tabs\">\r\n\t\t\t\t\t<text class=\"chart-tab\" :class=\"{ active: activeChart === 'trend' }\" @click=\"activeChart = 'trend'\">趋势图</text>\r\n\t\t\t\t\t<text class=\"chart-tab\" :class=\"{ active: activeChart === 'proportion' }\" @click=\"activeChart = 'proportion'\">占比图</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 趋势图 -->\r\n\t\t\t<view class=\"chart-content\" v-if=\"activeChart === 'trend'\">\r\n\t\t\t\t<view class=\"chart-placeholder\">\r\n\t\t\t\t\t<text>趋势图展示区域</text>\r\n\t\t\t\t\t<!-- 实际项目中，这里应该使用支持的图表组件 -->\r\n\t\t\t\t\t<!-- 例如: <qiun-data-charts type=\"line\" :chartData=\"trendChartData\"></qiun-data-charts> -->\r\n\t\t\t\t\t<view class=\"trend-chart-mock\">\r\n\t\t\t\t\t\t<view class=\"chart-legend\">\r\n\t\t\t\t\t\t\t<view class=\"legend-item\">\r\n\t\t\t\t\t\t\t\t<view class=\"legend-color\" style=\"background-color: #1890ff;\"></view>\r\n\t\t\t\t\t\t\t\t<text class=\"legend-text\">已缴费</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"legend-item\">\r\n\t\t\t\t\t\t\t\t<view class=\"legend-color\" style=\"background-color: #faad14;\"></view>\r\n\t\t\t\t\t\t\t\t<text class=\"legend-text\">未缴费</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"chart-bars\">\r\n\t\t\t\t\t\t\t<view class=\"chart-bar\" v-for=\"(item, index) in mockTrendData\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<view class=\"bar-label\">{{ item.date }}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"bar-wrapper\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"bar-value paid\" :style=\"{ height: item.paid + '%' }\"></view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"bar-value unpaid\" :style=\"{ height: item.unpaid + '%' }\"></view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 占比图 -->\r\n\t\t\t<view class=\"chart-content\" v-if=\"activeChart === 'proportion'\">\r\n\t\t\t\t<view class=\"chart-placeholder\">\r\n\t\t\t\t\t<text>占比图展示区域</text>\r\n\t\t\t\t\t<!-- 实际项目中，这里应该使用支持的图表组件 -->\r\n\t\t\t\t\t<!-- 例如: <qiun-data-charts type=\"pie\" :chartData=\"pieChartData\"></qiun-data-charts> -->\r\n\t\t\t\t\t<view class=\"pie-chart-mock\">\r\n\t\t\t\t\t\t<view class=\"pie-chart\">\r\n\t\t\t\t\t\t\t<view class=\"pie-slice paid\" :style=\"{ transform: 'rotate(0deg)', 'clip-path': 'polygon(50% 50%, 50% 0%, ' + (50 + 50 * Math.cos(Math.PI * 2 * statsData.paid / statsData.total)) + '% ' + (50 - 50 * Math.sin(Math.PI * 2 * statsData.paid / statsData.total)) + '%)' }\"></view>\r\n\t\t\t\t\t\t\t<view class=\"pie-slice unpaid\" :style=\"{ transform: 'rotate(' + (360 * statsData.paid / statsData.total) + 'deg)', 'clip-path': 'polygon(50% 50%, 50% 0%, ' + (50 + 50 * Math.cos(Math.PI * 2 * statsData.unpaid / statsData.total)) + '% ' + (50 - 50 * Math.sin(Math.PI * 2 * statsData.unpaid / statsData.total)) + '%)' }\"></view>\r\n\t\t\t\t\t\t\t<view class=\"pie-center\">\r\n\t\t\t\t\t\t\t\t<text class=\"pie-percent\">{{ (statsData.paid / statsData.total * 100).toFixed(0) }}%</text>\r\n\t\t\t\t\t\t\t\t<text class=\"pie-label\">缴费率</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"pie-legend\">\r\n\t\t\t\t\t\t\t<view class=\"legend-item\">\r\n\t\t\t\t\t\t\t\t<view class=\"legend-color\" style=\"background-color: #1890ff;\"></view>\r\n\t\t\t\t\t\t\t\t<view class=\"legend-info\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"legend-text\">已缴费</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"legend-value\">¥{{ statsData.paid.toLocaleString() }}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"legend-item\">\r\n\t\t\t\t\t\t\t\t<view class=\"legend-color\" style=\"background-color: #faad14;\"></view>\r\n\t\t\t\t\t\t\t\t<view class=\"legend-info\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"legend-text\">未缴费</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"legend-value\">¥{{ statsData.unpaid.toLocaleString() }}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 数据为空提示 -->\r\n\t\t<view class=\"empty-container\" v-if=\"!loading && isEmpty\">\r\n\t\t\t<image class=\"empty-icon\" src=\"/static/icons/empty.png\" mode=\"aspectFit\"></image>\r\n\t\t\t<text class=\"empty-text\">暂无缴费数据</text>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\t// 获取当前日期\r\n\t\t\tconst now = new Date();\r\n\t\t\tconst year = now.getFullYear();\r\n\t\t\tconst month = now.getMonth() + 1;\r\n\t\t\tconst day = now.getDate();\r\n\t\t\t\r\n\t\t\t// 格式化日期为 YYYY-MM-DD\r\n\t\t\tconst formatDate = (y, m, d) => {\r\n\t\t\t\treturn `${y}-${m < 10 ? '0' + m : m}-${d < 10 ? '0' + d : d}`;\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 默认开始日期为当前月份第一天\r\n\t\t\tconst startDate = formatDate(year, month, 1);\r\n\t\t\t// 默认结束日期为当前日期\r\n\t\t\tconst endDate = formatDate(year, month, day);\r\n\t\t\t\r\n\t\t\treturn {\r\n\t\t\t\t// 查询条件\r\n\t\t\t\theatUnitOptions: ['全部', '金色家园', '翠湖花园', '阳光小区', '幸福家园', '和平广场'],\r\n\t\t\t\theatUnitIndex: 0,\r\n\t\t\t\tstatusOptions: ['全部', '已缴费', '未缴费'],\r\n\t\t\t\tstatusIndex: 0,\r\n\t\t\t\tstartDate: startDate,\r\n\t\t\t\tendDate: endDate,\r\n\t\t\t\t\r\n\t\t\t\t// 加载状态\r\n\t\t\t\tloading: false,\r\n\t\t\t\tloadingText: {\r\n\t\t\t\t\tcontentdown: '正在加载...',\r\n\t\t\t\t\tcontentrefresh: '加载中...',\r\n\t\t\t\t\tcontentnomore: '没有更多数据了'\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\t// 统计数据\r\n\t\t\t\tstatsData: {\r\n\t\t\t\t\ttotal: 1000000,\r\n\t\t\t\t\tpaid: 800000,\r\n\t\t\t\t\tunpaid: 200000\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\t// 图表控制\r\n\t\t\t\tactiveChart: 'trend',\r\n\t\t\t\t\r\n\t\t\t\t// 模拟趋势数据\r\n\t\t\t\tmockTrendData: [\r\n\t\t\t\t\t{ date: '1月', paid: 65, unpaid: 35 },\r\n\t\t\t\t\t{ date: '2月', paid: 70, unpaid: 30 },\r\n\t\t\t\t\t{ date: '3月', paid: 75, unpaid: 25 },\r\n\t\t\t\t\t{ date: '4月', paid: 80, unpaid: 20 },\r\n\t\t\t\t\t{ date: '5月', paid: 85, unpaid: 15 },\r\n\t\t\t\t\t{ date: '6月', paid: 90, unpaid: 10 }\r\n\t\t\t\t],\r\n\t\t\t\t\r\n\t\t\t\tisEmpty: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t// 页面加载时获取缴费统计数据\r\n\t\t\tthis.getPaymentStats();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 选择热力单元变化\r\n\t\t\thandleHeatUnitChange(e) {\r\n\t\t\t\tthis.heatUnitIndex = e.detail.value;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 开始日期变化\r\n\t\t\thandleStartDateChange(e) {\r\n\t\t\t\tthis.startDate = e.detail.value;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 结束日期变化\r\n\t\t\thandleEndDateChange(e) {\r\n\t\t\t\tthis.endDate = e.detail.value;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 缴费状态变化\r\n\t\t\thandleStatusChange(e) {\r\n\t\t\t\tthis.statusIndex = e.detail.value;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 查询按钮点击\r\n\t\t\tqueryStats() {\r\n\t\t\t\tthis.getPaymentStats();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取缴费统计数据\r\n\t\t\tgetPaymentStats() {\r\n\t\t\t\tthis.loading = true;\r\n\t\t\t\t\r\n\t\t\t\t// 构建请求参数\r\n\t\t\t\tconst params = {\r\n\t\t\t\t\theat_unit_name: this.heatUnitIndex === 0 ? '' : this.heatUnitOptions[this.heatUnitIndex],\r\n\t\t\t\t\tstart_time: this.startDate + ' 00:00:00',\r\n\t\t\t\t\tend_time: this.endDate + ' 23:59:59',\r\n\t\t\t\t\tstatus: this.statusIndex === 0 ? '' : (this.statusIndex === 1 ? 'paid' : 'unpaid')\r\n\t\t\t\t};\r\n\t\t\t\t\r\n\t\t\t\tconsole.log('查询参数:', params);\r\n\t\t\t\t\r\n\t\t\t\t// 实际应用中这里会调用API获取统计数据\r\n\t\t\t\t// 模拟API调用\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t// 模拟响应数据\r\n\t\t\t\t\tconst mockResponse = {\r\n\t\t\t\t\t\tcode: 200,\r\n\t\t\t\t\t\tmessage: \"缴费统计获取成功\",\r\n\t\t\t\t\t\tdata: {\r\n\t\t\t\t\t\t\ttotal: Math.floor(Math.random() * 1000000) + 500000,\r\n\t\t\t\t\t\t\tpaid: Math.floor(Math.random() * 800000) + 200000,\r\n\t\t\t\t\t\t\tunpaid: 0\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t};\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 计算未缴费金额\r\n\t\t\t\t\tmockResponse.data.unpaid = mockResponse.data.total - mockResponse.data.paid;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 更新统计数据\r\n\t\t\t\t\tthis.statsData = mockResponse.data;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 更新图表数据\r\n\t\t\t\t\tthis.updateChartData();\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 判断是否为空数据\r\n\t\t\t\t\tthis.isEmpty = mockResponse.data.total === 0;\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t}, 1000);\r\n\t\t\t\t\r\n\t\t\t\t// 实际API调用示例\r\n\t\t\t\t/*\r\n\t\t\t\tuni.request({\r\n\t\t\t\t\turl: '/api/payments/stats',\r\n\t\t\t\t\tmethod: 'POST',\r\n\t\t\t\t\tdata: params,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.data.code === 200) {\r\n\t\t\t\t\t\t\tthis.statsData = res.data.data;\r\n\t\t\t\t\t\t\tthis.updateChartData();\r\n\t\t\t\t\t\t\tthis.isEmpty = this.statsData.total === 0;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: res.data.message || '获取统计数据失败',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '网络请求失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tconsole.error('获取缴费统计失败:', err);\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcomplete: () => {\r\n\t\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t*/\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 更新图表数据\r\n\t\t\tupdateChartData() {\r\n\t\t\t\t// 实际项目中，这里应该根据API返回的数据更新图表数据\r\n\t\t\t\t// 这里仅作示例，使用随机数据\r\n\t\t\t\tconst mockDates = ['1月', '2月', '3月', '4月', '5月', '6月'];\r\n\t\t\t\tthis.mockTrendData = mockDates.map(date => {\r\n\t\t\t\t\tconst paidPercent = Math.floor(Math.random() * 30) + 60; // 60%-90%\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tdate: date,\r\n\t\t\t\t\t\tpaid: paidPercent,\r\n\t\t\t\t\t\tunpaid: 100 - paidPercent\r\n\t\t\t\t\t};\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.payment-stats-container {\r\n\t\tpadding: 30rpx;\r\n\t\tbackground-color: #f8f8f8;\r\n\t\tmin-height: 100vh;\r\n\t}\r\n\t\r\n\t.page-header {\r\n\t\tmargin-bottom: 30rpx;\r\n\t\t\r\n\t\t.page-title {\r\n\t\t\tfont-size: 36rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tcolor: #333;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.query-form {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 12rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\t\t\r\n\t\t.form-item {\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\r\n\t\t\t.form-label {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.form-picker {\r\n\t\t\t\theight: 80rpx;\r\n\t\t\t\tborder: 1px solid #e8e8e8;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\tpadding: 0 20rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tbackground-color: #f8f8f8;\r\n\t\t\t\t\r\n\t\t\t\t.picker-text {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.date-range {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\t\r\n\t\t\t\t.date-picker {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\theight: 80rpx;\r\n\t\t\t\t\tborder: 1px solid #e8e8e8;\r\n\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\tpadding: 0 20rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tbackground-color: #f8f8f8;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.picker-text {\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.date-separator {\r\n\t\t\t\t\tmargin: 0 20rpx;\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.query-btn {\r\n\t\t\tbackground-color: #1890ff;\r\n\t\t\tcolor: #fff;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\theight: 80rpx;\r\n\t\t\tline-height: 80rpx;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tmargin-top: 20rpx;\r\n\t\t\t\r\n\t\t\t&:active {\r\n\t\t\t\tbackground-color: #0e80eb;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.stats-cards {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tmargin: 0 -10rpx 30rpx;\r\n\t\t\r\n\t\t.stats-card {\r\n\t\t\twidth: calc(50% - 20rpx);\r\n\t\t\tmargin: 0 10rpx 20rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-radius: 12rpx;\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\t\r\n\t\t\t.stats-value {\r\n\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tcolor: #1890ff;\r\n\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.stats-label {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #666;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&:nth-child(3) {\r\n\t\t\t\t.stats-value {\r\n\t\t\t\t\tcolor: #faad14;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&:nth-child(4) {\r\n\t\t\t\t.stats-value {\r\n\t\t\t\t\tcolor: #52c41a;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.loading-container {\r\n\t\tpadding: 40rpx 0;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t}\r\n\t\r\n\t.chart-container {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 12rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\t\t\r\n\t\t.chart-header {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin-bottom: 30rpx;\r\n\t\t\t\r\n\t\t\t.chart-title {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.chart-tabs {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\t\r\n\t\t\t\t.chart-tab {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\tmargin-left: 30rpx;\r\n\t\t\t\t\tpadding-bottom: 10rpx;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.active {\r\n\t\t\t\t\t\tcolor: #1890ff;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&:after {\r\n\t\t\t\t\t\t\tcontent: \"\";\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\t\t\tleft: 0;\r\n\t\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\t\theight: 4rpx;\r\n\t\t\t\t\t\t\tbackground-color: #1890ff;\r\n\t\t\t\t\t\t\tborder-radius: 2rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.chart-content {\r\n\t\t\tmin-height: 500rpx;\r\n\t\t\t\r\n\t\t\t.chart-placeholder {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\t\r\n\t\t\t\t.trend-chart-mock {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 500rpx;\r\n\t\t\t\t\tpadding: 20rpx 0;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.chart-legend {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.legend-item {\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\tmargin: 0 20rpx;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t.legend-color {\r\n\t\t\t\t\t\t\t\twidth: 24rpx;\r\n\t\t\t\t\t\t\t\theight: 24rpx;\r\n\t\t\t\t\t\t\t\tborder-radius: 4rpx;\r\n\t\t\t\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t.legend-text {\r\n\t\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.chart-bars {\r\n\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tjustify-content: space-around;\r\n\t\t\t\t\t\talign-items: flex-end;\r\n\t\t\t\t\t\theight: 400rpx;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.chart-bar {\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\twidth: calc(100% / 6);\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t.bar-label {\r\n\t\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\t\t\tmargin-top: 10rpx;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t.bar-wrapper {\r\n\t\t\t\t\t\t\t\twidth: 60rpx;\r\n\t\t\t\t\t\t\t\theight: 300rpx;\r\n\t\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\t\tflex-direction: column-reverse;\r\n\t\t\t\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t.bar-value {\r\n\t\t\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\t\t\t\ttransition: height 0.5s ease;\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t&.paid {\r\n\t\t\t\t\t\t\t\t\t\tbackground-color: #1890ff;\r\n\t\t\t\t\t\t\t\t\t\tborder-radius: 8rpx 8rpx 0 0;\r\n\t\t\t\t\t\t\t\t\t\tz-index: 2;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t&.unpaid {\r\n\t\t\t\t\t\t\t\t\t\tbackground-color: #faad14;\r\n\t\t\t\t\t\t\t\t\t\tborder-radius: 0 0 8rpx 8rpx;\r\n\t\t\t\t\t\t\t\t\t\tz-index: 1;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.pie-chart-mock {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 500rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.pie-chart {\r\n\t\t\t\t\t\twidth: 300rpx;\r\n\t\t\t\t\t\theight: 300rpx;\r\n\t\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\tmargin-bottom: 30rpx;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.pie-slice {\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t&.paid {\r\n\t\t\t\t\t\t\t\tbackground-color: #1890ff;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t&.unpaid {\r\n\t\t\t\t\t\t\t\tbackground-color: #faad14;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.pie-center {\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\ttop: 50%;\r\n\t\t\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\t\t\ttransform: translate(-50%, -50%);\r\n\t\t\t\t\t\t\twidth: 200rpx;\r\n\t\t\t\t\t\t\theight: 200rpx;\r\n\t\t\t\t\t\t\tbackground-color: white;\r\n\t\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\t\tbox-shadow: 0 0 10rpx rgba(0, 0, 0, 0.05);\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t.pie-percent {\r\n\t\t\t\t\t\t\t\tfont-size: 40rpx;\r\n\t\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\t\tcolor: #1890ff;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t.pie-label {\r\n\t\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.pie-legend {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.legend-item {\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\tmargin: 0 30rpx;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t.legend-color {\r\n\t\t\t\t\t\t\t\twidth: 24rpx;\r\n\t\t\t\t\t\t\t\theight: 24rpx;\r\n\t\t\t\t\t\t\t\tborder-radius: 4rpx;\r\n\t\t\t\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t.legend-info {\r\n\t\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t.legend-text {\r\n\t\t\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t.legend-value {\r\n\t\t\t\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.empty-container {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tpadding: 100rpx 0;\r\n\t\t\r\n\t\t.empty-icon {\r\n\t\t\twidth: 200rpx;\r\n\t\t\theight: 200rpx;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.empty-text {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #999;\r\n\t\t}\r\n\t}\r\n</style> ", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/payment/stats.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAqJC,MAAK,YAAU;AAAA,EACd,OAAO;AAEN,UAAM,MAAM,oBAAI;AAChB,UAAM,OAAO,IAAI;AACjB,UAAM,QAAQ,IAAI,SAAQ,IAAK;AAC/B,UAAM,MAAM,IAAI;AAGhB,UAAM,aAAa,CAAC,GAAG,GAAG,MAAM;AAC/B,aAAO,GAAG,CAAC,IAAI,IAAI,KAAK,MAAM,IAAI,CAAC,IAAI,IAAI,KAAK,MAAM,IAAI,CAAC;AAAA;AAI5D,UAAM,YAAY,WAAW,MAAM,OAAO,CAAC;AAE3C,UAAM,UAAU,WAAW,MAAM,OAAO,GAAG;AAE3C,WAAO;AAAA;AAAA,MAEN,iBAAiB,CAAC,MAAM,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,MAC9D,eAAe;AAAA,MACf,eAAe,CAAC,MAAM,OAAO,KAAK;AAAA,MAClC,aAAa;AAAA,MACb;AAAA,MACA;AAAA;AAAA,MAGA,SAAS;AAAA,MACT,aAAa;AAAA,QACZ,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,eAAe;AAAA,MACf;AAAA;AAAA,MAGD,WAAW;AAAA,QACV,OAAO;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,MACR;AAAA;AAAA,MAGD,aAAa;AAAA;AAAA,MAGb,eAAe;AAAA,QACd,EAAE,MAAM,MAAM,MAAM,IAAI,QAAQ,GAAI;AAAA,QACpC,EAAE,MAAM,MAAM,MAAM,IAAI,QAAQ,GAAI;AAAA,QACpC,EAAE,MAAM,MAAM,MAAM,IAAI,QAAQ,GAAI;AAAA,QACpC,EAAE,MAAM,MAAM,MAAM,IAAI,QAAQ,GAAI;AAAA,QACpC,EAAE,MAAM,MAAM,MAAM,IAAI,QAAQ,GAAI;AAAA,QACpC,EAAE,MAAM,MAAM,MAAM,IAAI,QAAQ,GAAG;AAAA,MACnC;AAAA,MAED,SAAS;AAAA,IACV;AAAA,EACA;AAAA,EACD,SAAS;AAER,SAAK,gBAAe;AAAA,EACpB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,qBAAqB,GAAG;AACvB,WAAK,gBAAgB,EAAE,OAAO;AAAA,IAC9B;AAAA;AAAA,IAGD,sBAAsB,GAAG;AACxB,WAAK,YAAY,EAAE,OAAO;AAAA,IAC1B;AAAA;AAAA,IAGD,oBAAoB,GAAG;AACtB,WAAK,UAAU,EAAE,OAAO;AAAA,IACxB;AAAA;AAAA,IAGD,mBAAmB,GAAG;AACrB,WAAK,cAAc,EAAE,OAAO;AAAA,IAC5B;AAAA;AAAA,IAGD,aAAa;AACZ,WAAK,gBAAe;AAAA,IACpB;AAAA;AAAA,IAGD,kBAAkB;AACjB,WAAK,UAAU;AAGf,YAAM,SAAS;AAAA,QACd,gBAAgB,KAAK,kBAAkB,IAAI,KAAK,KAAK,gBAAgB,KAAK,aAAa;AAAA,QACvF,YAAY,KAAK,YAAY;AAAA,QAC7B,UAAU,KAAK,UAAU;AAAA,QACzB,QAAQ,KAAK,gBAAgB,IAAI,KAAM,KAAK,gBAAgB,IAAI,SAAS;AAAA;AAG1EA,oBAAA,MAAA,MAAA,OAAA,kCAAY,SAAS,MAAM;AAI3B,iBAAW,MAAM;AAEhB,cAAM,eAAe;AAAA,UACpB,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA,YACL,OAAO,KAAK,MAAM,KAAK,WAAW,GAAO,IAAI;AAAA,YAC7C,MAAM,KAAK,MAAM,KAAK,OAAS,IAAE,GAAM,IAAI;AAAA,YAC3C,QAAQ;AAAA,UACT;AAAA;AAID,qBAAa,KAAK,SAAS,aAAa,KAAK,QAAQ,aAAa,KAAK;AAGvE,aAAK,YAAY,aAAa;AAG9B,aAAK,gBAAe;AAGpB,aAAK,UAAU,aAAa,KAAK,UAAU;AAE3C,aAAK,UAAU;AAAA,MACf,GAAE,GAAI;AAAA,IAgCP;AAAA;AAAA,IAGD,kBAAkB;AAGjB,YAAM,YAAY,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AACrD,WAAK,gBAAgB,UAAU,IAAI,UAAQ;AAC1C,cAAM,cAAc,KAAK,MAAM,KAAK,WAAW,EAAE,IAAI;AACrD,eAAO;AAAA,UACN;AAAA,UACA,MAAM;AAAA,UACN,QAAQ,MAAM;AAAA;MAEhB,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtUD,GAAG,WAAW,eAAe;"}