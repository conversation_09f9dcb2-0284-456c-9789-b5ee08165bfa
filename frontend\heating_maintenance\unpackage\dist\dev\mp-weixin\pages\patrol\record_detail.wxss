/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  box-sizing: border-box;
}
.order-info-card {
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  margin: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding: 30rpx;
}
.order-info-card .order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.order-info-card .order-header .order-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.order-info-card .order-header .order-status {
  padding: 6rpx 16rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  text-align: center;
  min-width: 80rpx;
}
.order-info-card .order-header .order-status.status-pending {
  background-color: rgba(250, 173, 20, 0.1);
  color: #faad14;
}
.order-info-card .order-header .order-status.status-processing {
  background-color: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}
.order-info-card .order-header .order-status.status-completed {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}
.order-info-card .order-header .order-status.status-overdue {
  background-color: rgba(245, 34, 45, 0.1);
  color: #f5222d;
}
.order-info-card .order-location {
  margin-bottom: 20rpx;
}
.order-info-card .order-location .location-info {
  font-size: 28rpx;
  color: #666;
}
.order-info-card .order-location .location-info .location-name {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}
.order-info-card .patrol-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
}
.order-info-card .patrol-info .patrol-executor .patrol-label, .order-info-card .patrol-info .patrol-date .patrol-label {
  font-size: 24rpx;
  color: #999;
  margin-right: 10rpx;
}
.order-info-card .patrol-info .patrol-executor .patrol-value, .order-info-card .patrol-info .patrol-date .patrol-value {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}
.detail-tabs {
  display: flex;
  background-color: #fff;
  margin: 0 20rpx 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: -webkit-sticky;
  position: sticky;
  /* 使选项卡固定在顶部 */
  top: 0;
  z-index: 10;
}
.detail-tabs .tab-item {
  flex: 1;
  text-align: center;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}
.detail-tabs .tab-item.active {
  color: #1890ff;
  font-weight: bold;
}
.detail-tabs .tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40%;
  height: 4rpx;
  background-color: #1890ff;
  border-radius: 2rpx;
}
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 40vh;
}
.loading-container .loading-spinner {
  width: 70rpx;
  height: 70rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}
.loading-container .loading-text {
  font-size: 28rpx;
  color: #666;
}
@keyframes spin {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  width: 100%;
  box-sizing: border-box;
}
.error-container .error-icon {
  width: 80rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #fff2f0;
  border-radius: 50%;
  color: #f5222d;
  font-size: 50rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.error-container .error-message {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 30rpx;
}
.error-container .retry-button {
  background-color: #1890ff;
  color: #fff;
  font-size: 28rpx;
  padding: 16rpx 40rpx;
  border-radius: 8rpx;
  border: none;
}
.content-container {
  flex: 1;
  overflow: visible;
  /* 改为visible，允许内容自然流动 */
  padding-bottom: 120rpx;
  /* 为底部按钮留出空间 */
}
.tab-content {
  height: auto;
  min-height: 60vh;
  /* 确保内容区域有足够的高度 */
  padding: 0 20rpx;
  box-sizing: border-box;
}
.scroll-view-content {
  height: auto;
  /* 移除固定高度限制，改为自适应内容高度 */
  max-height: none;
  /* 移除最大高度限制 */
  overflow: visible;
  /* 确保内容可见 */
}
.info-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.info-section .section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 20rpx;
}
.info-section .section-content .info-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 2rpx solid #f5f5f5;
}
.info-section .section-content .info-item:last-child {
  border-bottom: none;
}
.info-section .section-content .info-item .info-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #666;
}
.info-section .section-content .info-item .info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.info-section .section-content .info-item .info-value.normal-count {
  color: #52c41a;
  font-weight: bold;
}
.info-section .section-content .info-item .info-value.abnormal-count {
  color: #f5222d;
  font-weight: bold;
}
.info-section .section-content .info-item .info-value.status-value {
  font-weight: bold;
}
.info-section .section-content .info-item .info-value.status-value.pending {
  color: #faad14;
}
.info-section .section-content .info-item .info-value.status-value.processing {
  color: #1890ff;
}
.info-section .section-content .info-item .info-value.status-value.completed {
  color: #52c41a;
}
.info-section .section-content .info-item .info-value.status-value.overdue {
  color: #f5222d;
}
.patrol-results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.patrol-results-header .results-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.patrol-results-header .results-summary {
  display: flex;
  align-items: center;
}
.patrol-results-header .results-summary .results-count {
  font-size: 26rpx;
  color: #999;
  margin-right: 16rpx;
}
.patrol-results-header .results-summary .abnormal-count {
  font-size: 26rpx;
  color: #f5222d;
  font-weight: bold;
}
.empty-results {
  padding: 0;
  text-align: center;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  min-height: 500rpx;
  /* 增加最小高度，确保有足够空间 */
  display: flex;
  align-items: center;
  justify-content: center;
}
.empty-results .empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}
.empty-results image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-results text {
  font-size: 28rpx;
  color: #999;
  display: block;
  width: 100%;
  text-align: center;
  line-height: 1.5;
}
.task-list .task-item {
  padding: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.task-list .task-item .task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.task-list .task-item .task-header .task-left {
  display: flex;
  align-items: center;
}
.task-list .task-item .task-header .task-left .task-status {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  margin-right: 10rpx;
}
.task-list .task-item .task-header .task-left .task-status.completed {
  background-color: #52c41a;
}
.task-list .task-item .task-header .task-left .task-status.abnormal {
  background-color: #f5222d;
}
.task-list .task-item .task-header .task-left .task-status.pending {
  background-color: #faad14;
}
.task-list .task-item .task-header .task-left .task-status.overdue {
  background-color: #f5222d;
}
.task-list .task-item .task-header .task-left .task-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}
.task-list .task-item .task-header .task-status-text {
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}
.task-list .task-item .task-header .task-status-text.status-normal {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}
.task-list .task-item .task-header .task-status-text.status-abnormal {
  background-color: rgba(245, 34, 45, 0.1);
  color: #f5222d;
}
.task-list .task-item .task-header .task-status-text.status-pending {
  background-color: rgba(250, 173, 20, 0.1);
  color: #faad14;
}
.task-list .task-item .task-header .task-status-text.status-overdue {
  background-color: rgba(245, 34, 45, 0.1);
  color: #f5222d;
}
.task-list .task-item .task-details {
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 16rpx;
  margin-top: 16rpx;
}
.task-list .task-item .task-details .detail-row {
  display: flex;
  margin-bottom: 16rpx;
}
.task-list .task-item .task-details .detail-row .detail-label {
  width: 120rpx;
  font-size: 26rpx;
  color: #666;
}
.task-list .task-item .task-details .detail-row .detail-value {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  word-break: break-all;
}
.task-list .task-item .task-details .detail-row .detail-value.value-abnormal {
  color: #f5222d;
}
.task-list .task-item .task-details .task-images {
  margin-top: 20rpx;
}
.task-list .task-item .task-details .task-images .images-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}
.task-list .task-item .task-details .task-images .image-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}
.task-list .task-item .task-details .task-images .image-list image {
  width: 33.33%;
  height: 200rpx;
  padding: 10rpx;
  box-sizing: border-box;
  flex-shrink: 0;
  border-radius: 8rpx;
}
.all-images {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.all-images .image-card {
  width: 33.33%;
  padding: 10rpx;
  box-sizing: border-box;
  position: relative;
  height: 220rpx;
}
.all-images .image-card image {
  width: 100%;
  height: 180rpx;
  object-fit: cover;
  border-radius: 8rpx;
}
.all-images .image-card .image-item-name {
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.action-buttons {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx;
  background-color: #fff;
  display: flex;
  justify-content: center;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.action-buttons .action-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 30rpx;
  margin: 0 10rpx;
}
.action-buttons .action-btn.start {
  background-color: #1890ff;
  color: #fff;
}
.action-buttons .action-btn.continue {
  background-color: #52c41a;
  color: #fff;
}
.action-buttons .action-btn.complete {
  background-color: #faad14;
  color: #fff;
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin: 20rpx 0;
  padding-left: 20rpx;
  border-left: 6rpx solid #1890ff;
}