/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 修改页面样式以消除顶部的白色间隙 */
page {
  background-color: #000000;
  height: 100%;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

/* 隐藏默认导航栏的可能空间 */
.uni-page-head {
  display: none;
}
.uni-page-wrapper,
.uni-page-body {
  height: 100%;
  background-color: #000000 !important;
  overflow: hidden;
  position: relative;
  z-index: 1;
}
.video-player-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  background-color: #000;
  overflow: hidden;
  z-index: 10;
}
.video-player-container .video-wrapper {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}
.video-player-container .video-wrapper video {
  width: 100%;
  height: 100%;
  max-height: 100%;
  object-fit: contain;
  z-index: 1;
}
.video-player-container .controls {
  display: flex;
  padding: 30rpx;
  background-color: #222;
  gap: 30rpx;
}
.video-player-container .controls button {
  flex: 1;
  margin: 0;
  padding: 20rpx 0;
  border-radius: 8rpx;
  font-size: 30rpx;
}
.video-player-container .controls button.btn-back {
  background-color: #444;
  color: #fff;
}
.video-player-container .controls button.btn-open-browser {
  background-color: #1890ff;
  color: #fff;
}
.video-player-container .error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
  text-align: center;
  z-index: 2;
}
.video-player-container .error-message text {
  color: #fff;
  font-size: 28rpx;
}