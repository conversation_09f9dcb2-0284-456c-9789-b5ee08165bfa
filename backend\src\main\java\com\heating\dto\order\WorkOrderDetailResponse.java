package com.heating.dto.order;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public record WorkOrderDetailResponse(
    String orderNo,
    long faultId,
    String heatUnitName,
    long repairUserId,
    String repairUserName,
    Integer transferUserId,
    String transferUserName,
    String transferReason,
    String transferTime,
    String repairContent,
    String repairResult,
    String address,
    String orderStatus,
    String faultType,
    String faultLevel,
    String faultDesc,
    String repairTime,
    String  occurTime,
    Map<String, Integer> repairMaterialsQuantity,
    String createdTime,
    String updatedTime,
    List<AttachmentDto> faultAttachments,
    List<AttachmentDto> workOrderAttachments,
    List<OperationLogDto> operationLogs
) {

    public record AttachmentDto(
        String fileType,
        String filePath
    ) {
    }

    public record OperationLogDto(
        String operationType,
        String operationDesc,
        String operatorName,
        String createdAt
    ) {
    }
    
    /**
     * 工单耗材DTO
     */
    public record MaterialDto(
        Long id,
        Long materialId,
        String materialName,
        BigDecimal quantityUsed,
        String unit,
        String recordedAt
    ) {
    }
}