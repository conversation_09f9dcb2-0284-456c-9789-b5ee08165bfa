{"version": 3, "file": "complete.js", "sources": ["pages/workorder/complete.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvd29ya29yZGVyL2NvbXBsZXRlLnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"page-wrapper\">\n\t\t<view class=\"complete-container\">\n\t\t\t<view class=\"form-card\">\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<view class=\"form-label required\">维修内容</view>\n\t\t\t\t\t<textarea class=\"form-textarea\" v-model=\"formData.repairContent\" placeholder=\"请输入维修内容和过程描述\"></textarea>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<view class=\"form-label required\">维修结果</view>\n\t\t\t\t\t<textarea class=\"form-textarea\" v-model=\"formData.repairResult\" placeholder=\"请输入维修结果和效果\"></textarea>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<view class=\"form-label\">维修耗材及数量</view>\n\t\t\t\t\t<view class=\"materials-container\">\n\t\t\t\t\t\t<view v-for=\"(item, index) in repairMaterialsList\" :key=\"index\" class=\"material-item\">\n\t\t\t\t\t\t\t<view class=\"material-row\">\n\t\t\t\t\t\t\t\t<view class=\"material-selector\" @click=\"showMaterialSelectorPopup(index)\">\n\t\t\t\t\t\t\t\t\t<view class=\"form-input material-input\">\n\t\t\t\t\t\t\t\t\t\t<text>{{ item.name || '请选择耗材' }}</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"arrow-down\">▼</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<input type=\"number\" class=\"quantity-input\" v-model=\"item.quantity\" placeholder=\"数量\" />\n\t\t\t\t\t\t\t\t<text class=\"delete-btn\" @click=\"removeMaterial(index)\">×</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"add-material-btn\" @click=\"addMaterial\">\n\t\t\t\t\t\t\t<text class=\"iconfont icon-add\"></text>\n\t\t\t\t\t\t\t<text>添加耗材</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<view class=\"form-label required\">维修时间</view>\n\t\t\t\t\t<picker mode=\"multiSelector\" @change=\"onDateTimeChange\" @columnchange=\"onColumnChange\" :value=\"dateTimeIndex\" :range=\"dateTimeArray\">\n\t\t\t\t\t\t<view class=\"picker-view\">\n\t\t\t\t\t\t\t<text>{{ formData.repairDate }} {{ formData.repairTime }}</text>\n\t\t\t\t\t\t\t<text class=\"iconfont icon-calendar\"></text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</picker>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<view class=\"form-label\">附件上传</view>\n\t\t\t\t\t<view class=\"upload-area\">\n\t\t\t\t\t\t<!-- 图片上传部分 -->\n\t\t\t\t\t\t<view class=\"form-header\">\n\t\t\t\t\t\t\t<text class=\"form-subtitle\">上传维修现场图片（最多6张）</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"image-grid\">\n\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\tv-for=\"(image, index) in imageList\" \n\t\t\t\t\t\t\t\t:key=\"index\" \n\t\t\t\t\t\t\t\tclass=\"image-item\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<image :src=\"image\" mode=\"aspectFill\" @click=\"previewImage(index)\"></image>\n\t\t\t\t\t\t\t\t<text class=\"delete-icon\" @click=\"deleteImage(index)\">×</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<view class=\"upload-item\" v-if=\"imageList.length < 6\" @click=\"chooseImage\">\n\t\t\t\t\t\t\t\t<text class=\"iconfont icon-camera\"></text>\n\t\t\t\t\t\t\t\t<text>上传图片</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 视频上传部分 -->\n\t\t\t\t\t\t<view class=\"form-header\" style=\"margin-top: 30rpx;\">\n\t\t\t\t\t\t\t<text class=\"form-subtitle\">上传维修现场视频（最多1个）</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"upload-area\">\n\t\t\t\t\t\t\t<view class=\"video-container\" v-if=\"videoPath\">\n\t\t\t\t\t\t\t\t<video \n\t\t\t\t\t\t\t\t\t:src=\"getVideoUrl()\" \n\t\t\t\t\t\t\t\t\tcontrols\n\t\t\t\t\t\t\t\t\tobject-fit=\"fill\"\n\t\t\t\t\t\t\t\t\tinitial-time=\"0\"\n\t\t\t\t\t\t\t\t\tshow-fullscreen-btn=\"true\"\n\t\t\t\t\t\t\t\t\tshow-play-btn=\"true\"\n\t\t\t\t\t\t\t\t\tshow-center-play-btn=\"true\"\n\t\t\t\t\t\t\t\t\tenable-progress-gesture=\"true\"\n\t\t\t\t\t\t\t\t\tauto-pause-if-navigate=\"true\"\n\t\t\t\t\t\t\t\t\tauto-pause-if-open-native=\"true\"\n\t\t\t\t\t\t\t\t\tcodec=\"h264\"\n\t\t\t\t\t\t\t\t\t@error=\"onVideoError\"\n\t\t\t\t\t\t\t\t></video>\n\t\t\t\t\t\t\t\t<text class=\"delete-icon\" @click=\"deleteVideo\">×</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<view class=\"upload-item\" v-if=\"!videoPath\" @click=\"chooseVideo\">\n\t\t\t\t\t\t\t\t<text class=\"iconfont icon-video\"></text>\n\t\t\t\t\t\t\t\t<text>上传视频</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"action-buttons\">\n\t\t\t\t<button class=\"btn-cancel\" @click=\"cancelComplete\">取消</button>\n\t\t\t\t<button class=\"btn-submit\" @click=\"submitComplete\">提交</button>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 材料选择弹窗 -->\n\t\t<uni-popup ref=\"materialSelector\" type=\"bottom\">\n\t\t\t<view class=\"selector material-selector-popup\">\n\t\t\t\t<view class=\"selector-header\">\n\t\t\t\t\t<text class=\"selector-title\">选择耗材</text>\n\t\t\t\t\t<view class=\"header-actions\">\n\t\t\t\t\t\t<text class=\"confirm-button-header\" @click=\"confirmMaterialSelection\">确定</text>\n\t\t\t\t\t\t<text class=\"close-button\" @click=\"hideMaterialSelector\">关闭</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"selector-content\">\n\t\t\t\t\t<view class=\"template-search\">\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\ttype=\"text\" \n\t\t\t\t\t\t\tv-model=\"materialSearchKeyword\" \n\t\t\t\t\t\t\tplaceholder=\"搜索耗材\" \n\t\t\t\t\t\t\tconfirm-type=\"search\"\n\t\t\t\t\t\t\t@input=\"handleMaterialSearch\"\n\t\t\t\t\t\t\tclass=\"search-input\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t\t<text \n\t\t\t\t\t\t\tclass=\"search-clear\" \n\t\t\t\t\t\t\tv-if=\"materialSearchKeyword\" \n\t\t\t\t\t\t\t@click=\"clearMaterialSearch\"\n\t\t\t\t\t\t>×</text>\n\t\t\t\t\t\t<view class=\"search-btn\" @click=\"handleMaterialSearch\">搜索</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"material-list\">\n\t\t\t\t\t\t<view \n\t\t\t\t\t\t\tclass=\"material-item-popup\" \n\t\t\t\t\t\t\tv-for=\"material in filteredMaterials\" \n\t\t\t\t\t\t\t:key=\"material.id\"\n\t\t\t\t\t\t\t@click=\"selectMaterial(material)\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<view class=\"material-info\">\n\t\t\t\t\t\t\t\t<text class=\"material-name\">{{ material.name }}</text>\n\t\t\t\t\t\t\t\t<text class=\"material-type\">{{ material.material_type }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"checkbox\" :class=\"{ checked: tempSelectedMaterial && tempSelectedMaterial.id === material.id }\">\n\t\t\t\t\t\t\t\t<text class=\"iconfont icon-check\" v-if=\"tempSelectedMaterial && tempSelectedMaterial.id === material.id\"></text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"empty-tip\" v-if=\"filteredMaterials.length === 0\">\n\t\t\t\t\t\t<text>没有找到相关耗材</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t</view>\n</template>\n\n<script>\n\timport { workOrderApi, materialsApi } from '../../utils/api';\n\timport uploadUtils from '@/utils/upload.js';\n\t\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\torderId: null,\n\t\t\t\tformData: {\n\t\t\t\t\trepairContent: '',\n\t\t\t\t\trepairResult: '',\n\t\t\t\t\trepairDate: '',\n\t\t\t\t\trepairTime: '',\n\t\t\t\t\trepairDateTime: '',\n\t\t\t\t\tattachment: []\n\t\t\t\t},\n\t\t\t\trepairMaterialsList: [\n\t\t\t\t\t{ name: '', quantity: '' }\n\t\t\t\t],\n\t\t\t\trules: {\n\t\t\t\t\trepairContent: [\n\t\t\t\t\t\t{ required: true, message: '请输入维修内容' }\n\t\t\t\t\t],\n\t\t\t\t\trepairResult: [\n\t\t\t\t\t\t{ required: true, message: '请输入维修结果' }\n\t\t\t\t\t],\n\t\t\t\t\trepairDate: [\n\t\t\t\t\t\t{ required: true, message: '请选择维修日期' }\n\t\t\t\t\t],\n\t\t\t\t\trepairTime: [\n\t\t\t\t\t\t{ required: true, message: '请选择维修时间' }\n\t\t\t\t\t]\n\t\t\t\t},\n\t\t\t\tdateTimeArray: [[], [], [], [], [], []],\n\t\t\t\tdateTimeIndex: [0, 0, 0, 0, 0, 0],\n\t\t\t\timageList: [], // 本地临时路径\n\t\t\t\tserverImageList: [], // 服务器路径\n\t\t\t\tvideoPath: '', // 本地临时路径\n\t\t\t\tserverVideoPath: '', // 服务器路径\n\t\t\t\tisUploading: false,\n\t\t\t\t// 新增材料相关数据\n\t\t\t\tmaterialOptions: [], // 材料选项列表\n\t\t\t\tmaterialSearchKeyword: '', // 材料搜索关键词\n\t\t\t\tfilteredMaterials: [], // 过滤后的材料列表\n\t\t\t\tcurrentMaterialIndex: -1, // 当前正在编辑的材料索引\n\t\t\t\ttempSelectedMaterial: null, // 临时选中的材料\n\t\t\t\tisPopupOpen: false // 弹窗状态\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\tif (options.id) {\n\t\t\t\tthis.orderId = options.id;\n\t\t\t} else {\n\t\t\t\tthis.showError('缺少工单ID');\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.navigateBack();\n\t\t\t\t}, 1500);\n\t\t\t}\n\t\t\t\n\t\t\t// 初始化日期时间选择器\n\t\t\tthis.initDateTimePicker();\n\t\t\t\n\t\t\t// 默认设置当前时间\n\t\t\tconst now = new Date();\n\t\t\tconst year = now.getFullYear();\n\t\t\tconst month = String(now.getMonth() + 1).padStart(2, '0');\n\t\t\tconst day = String(now.getDate()).padStart(2, '0');\n\t\t\tconst hours = String(now.getHours()).padStart(2, '0');\n\t\t\tconst minutes = String(now.getMinutes()).padStart(2, '0');\n\t\t\t\n\t\t\tthis.formData.repairDate = `${year}-${month}-${day}`;\n\t\t\tthis.formData.repairTime = `${hours}:${minutes}:00`;\n\t\t\tthis.updateRepairDateTime();\n\t\t\t\n\t\t\t// 加载材料列表\n\t\t\tthis.loadMaterialsList();\n\t\t},\n\t\tmethods: {\n\t\t\t// 添加维修耗材\n\t\t\taddMaterial() {\n\t\t\t\tthis.repairMaterialsList.push({ name: '', quantity: '' });\n\t\t\t},\n\t\t\t\n\t\t\t// 删除维修耗材\n\t\t\tremoveMaterial(index) {\n\t\t\t\tif (this.repairMaterialsList.length > 1) {\n\t\t\t\t\tthis.repairMaterialsList.splice(index, 1);\n\t\t\t\t} else {\n\t\t\t\t\t// 至少保留一个输入框\n\t\t\t\t\tthis.repairMaterialsList = [{ name: '', quantity: '' }];\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 获取耗材数据（JSON格式）\n\t\t\tgetRepairMaterialsQuantity() {\n\t\t\t\tconst result = {};\n\t\t\t\tthis.repairMaterialsList.forEach(item => {\n\t\t\t\t\tif (item.name && item.name.trim()) {\n\t\t\t\t\t\tresult[item.name.trim()] = item.quantity || 0;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\treturn result;\n\t\t\t},\n\t\t\t\n\t\t\t// 加载材料列表\n\t\t\tloadMaterialsList() {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '加载材料列表...'\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tmaterialsApi.getMaterialsList()\n\t\t\t\t\t.then(res => {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tif (res.code === 200 && res.data) {\n\t\t\t\t\t\t\tthis.materialOptions = res.data;\n\t\t\t\t\t\t\tthis.filteredMaterials = [...res.data];\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.showError('获取材料列表失败');\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tconsole.error('获取材料列表失败:', err);\n\t\t\t\t\t\tthis.showError('获取材料列表失败，请重试');\n\t\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 显示材料选择器\n\t\t\tshowMaterialSelectorPopup(index) {\n\t\t\t\tthis.currentMaterialIndex = index;\n\t\t\t\tthis.tempSelectedMaterial = null;\n\t\t\t\t\n\t\t\t\t// 如果当前行已经选择了材料，则预选中\n\t\t\t\tconst currentMaterial = this.repairMaterialsList[index];\n\t\t\t\tif (currentMaterial && currentMaterial.name) {\n\t\t\t\t\tconst foundMaterial = this.materialOptions.find(m => m.name === currentMaterial.name);\n\t\t\t\t\tif (foundMaterial) {\n\t\t\t\t\t\tthis.tempSelectedMaterial = foundMaterial;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 重置搜索\n\t\t\t\tthis.materialSearchKeyword = '';\n\t\t\t\tthis.filteredMaterials = [...this.materialOptions];\n\t\t\t\t\n\t\t\t\tthis.isPopupOpen = true;\n\t\t\t\tthis.$refs.materialSelector.open();\n\t\t\t},\n\t\t\t\n\t\t\t// 隐藏材料选择器\n\t\t\thideMaterialSelector() {\n\t\t\t\tthis.isPopupOpen = false;\n\t\t\t\tthis.$refs.materialSelector.close();\n\t\t\t},\n\t\t\t\n\t\t\t// 选择材料\n\t\t\tselectMaterial(material) {\n\t\t\t\tthis.tempSelectedMaterial = material;\n\t\t\t},\n\t\t\t\n\t\t\t// 确认材料选择\n\t\t\tconfirmMaterialSelection() {\n\t\t\t\tif (this.tempSelectedMaterial && this.currentMaterialIndex !== -1) {\n\t\t\t\t\tthis.repairMaterialsList[this.currentMaterialIndex].name = this.tempSelectedMaterial.name;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.hideMaterialSelector();\n\t\t\t},\n\t\t\t\n\t\t\t// 搜索材料\n\t\t\thandleMaterialSearch() {\n\t\t\t\tif (!this.materialSearchKeyword || this.materialSearchKeyword.trim() === '') {\n\t\t\t\t\tthis.filteredMaterials = [...this.materialOptions];\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconst keyword = this.materialSearchKeyword.toLowerCase().trim();\n\t\t\t\tthis.filteredMaterials = this.materialOptions.filter(material => \n\t\t\t\t\tmaterial.name.toLowerCase().includes(keyword) || \n\t\t\t\t\t(material.material_type && material.material_type.toLowerCase().includes(keyword))\n\t\t\t\t);\n\t\t\t},\n\t\t\t\n\t\t\t// 清空材料搜索\n\t\t\tclearMaterialSearch() {\n\t\t\t\tthis.materialSearchKeyword = '';\n\t\t\t\tthis.filteredMaterials = [...this.materialOptions];\n\t\t\t},\n\t\t\t\n\t\t\t// 初始化日期时间选择器\n\t\t\tinitDateTimePicker() {\n\t\t\t\t// 生成年份数组，前后各5年\n\t\t\t\tconst date = new Date();\n\t\t\t\tconst currentYear = date.getFullYear();\n\t\t\t\tconst years = [];\n\t\t\t\tfor (let i = currentYear - 2; i <= currentYear + 2; i++) {\n\t\t\t\t\tyears.push(i + '年');\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 生成月份数组\n\t\t\t\tconst months = [];\n\t\t\t\tfor (let i = 1; i <= 12; i++) {\n\t\t\t\t\tmonths.push(i + '月');\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 生成日期数组\n\t\t\t\tconst days = [];\n\t\t\t\tfor (let i = 1; i <= 31; i++) {\n\t\t\t\t\tdays.push(i + '日');\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 生成小时数组\n\t\t\t\tconst hours = [];\n\t\t\t\tfor (let i = 0; i <= 23; i++) {\n\t\t\t\t\thours.push(i + '时');\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 生成分钟数组\n\t\t\t\tconst minutes = [];\n\t\t\t\tfor (let i = 0; i <= 59; i++) {\n\t\t\t\t\tminutes.push(i + '分');\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 生成秒数组\n\t\t\t\tconst seconds = [];\n\t\t\t\tfor (let i = 0; i <= 59; i++) {\n\t\t\t\t\tseconds.push(i + '秒');\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 设置到data中\n\t\t\t\tthis.dateTimeArray = [years, months, days, hours, minutes, seconds];\n\t\t\t\t\n\t\t\t\t// 设置默认选中值\n\t\t\t\tconst yearIndex = 2; // 默认选中当前年份\n\t\t\t\tconst monthIndex = date.getMonth(); // 1月对应索引0\n\t\t\t\tconst dayIndex = date.getDate() - 1; // 1日对应索引0\n\t\t\t\tconst hourIndex = date.getHours();\n\t\t\t\tconst minuteIndex = date.getMinutes();\n\t\t\t\tconst secondIndex = date.getSeconds();\n\t\t\t\t\n\t\t\t\tthis.dateTimeIndex = [yearIndex, monthIndex, dayIndex, hourIndex, minuteIndex, secondIndex];\n\t\t\t},\n\t\t\t\n\t\t\t// 处理日期时间选择器变化\n\t\t\tonDateTimeChange(e) {\n\t\t\t\tconst values = e.detail.value;\n\t\t\t\tthis.dateTimeIndex = values;\n\t\t\t\t\n\t\t\t\t// 更新日期和时间\n\t\t\t\tconst year = parseInt(this.dateTimeArray[0][values[0]]);\n\t\t\t\tconst month = String(parseInt(this.dateTimeArray[1][values[1]])).padStart(2, '0');\n\t\t\t\tconst day = String(parseInt(this.dateTimeArray[2][values[2]])).padStart(2, '0');\n\t\t\t\tconst hour = String(parseInt(this.dateTimeArray[3][values[3]])).padStart(2, '0');\n\t\t\t\tconst minute = String(parseInt(this.dateTimeArray[4][values[4]])).padStart(2, '0');\n\t\t\t\tconst second = String(parseInt(this.dateTimeArray[5][values[5]])).padStart(2, '0');\n\t\t\t\t\n\t\t\t\tthis.formData.repairDate = `${year}-${month}-${day}`;\n\t\t\t\tthis.formData.repairTime = `${hour}:${minute}:${second}`;\n\t\t\t\tthis.updateRepairDateTime();\n\t\t\t},\n\t\t\t\n\t\t\t// 处理日期时间选择器列变化\n\t\t\tonColumnChange(e) {\n\t\t\t\tlet column = e.detail.column;\n\t\t\t\tlet value = e.detail.value;\n\t\t\t\t\n\t\t\t\t// 当年份或月份变化时，需要调整天数\n\t\t\t\tif (column === 0 || column === 1) {\n\t\t\t\t\tconst year = parseInt(this.dateTimeArray[0][this.dateTimeIndex[0]]);\n\t\t\t\t\tconst month = parseInt(this.dateTimeArray[1][value === undefined ? this.dateTimeIndex[1] : value]);\n\t\t\t\t\t\n\t\t\t\t\t// 获取当月的天数\n\t\t\t\t\tconst daysInMonth = new Date(year, month, 0).getDate();\n\t\t\t\t\t\n\t\t\t\t\t// 更新天数数组\n\t\t\t\t\tconst days = [];\n\t\t\t\t\tfor (let i = 1; i <= daysInMonth; i++) {\n\t\t\t\t\t\tdays.push(i + '日');\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 更新天数数组\n\t\t\t\t\tthis.dateTimeArray[2] = days;\n\t\t\t\t\t\n\t\t\t\t\t// 如果当前选中的日期超过了当月的最大天数，则调整为当月的最后一天\n\t\t\t\t\tif (this.dateTimeIndex[2] >= daysInMonth) {\n\t\t\t\t\t\tthis.dateTimeIndex[2] = daysInMonth - 1;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 更新合并后的日期时间\n\t\t\tupdateRepairDateTime() {\n\t\t\t\tif (this.formData.repairDate && this.formData.repairTime) {\n\t\t\t\t\tconst dateTimeStr = `${this.formData.repairDate} ${this.formData.repairTime}`;\n\t\t\t\t\tthis.formData.repairDateTime = dateTimeStr;\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 获取一年前的日期\n\t\t\tgetOneYearAgo() {\n\t\t\t\tconst date = new Date();\n\t\t\t\tdate.setFullYear(date.getFullYear() - 1);\n\t\t\t\tconst year = date.getFullYear();\n\t\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\n\t\t\t\tconst day = String(date.getDate()).padStart(2, '0');\n\t\t\t\treturn `${year}-${month}-${day}`;\n\t\t\t},\n\t\t\t\n\t\t\t// 获取当前日期\n\t\t\tgetCurrentDate() {\n\t\t\t\tconst date = new Date();\n\t\t\t\tconst year = date.getFullYear();\n\t\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\n\t\t\t\tconst day = String(date.getDate()).padStart(2, '0');\n\t\t\t\treturn `${year}-${month}-${day}`;\n\t\t\t},\n\t\t\t\n\t\t\t// 选择图片\n\t\t\tchooseImage() {\n\t\t\t\tuni.chooseImage({\n\t\t\t\t\tcount: 6 - this.imageList.length,\n\t\t\t\t\tsizeType: ['compressed'],\n\t\t\t\t\tsourceType: ['album', 'camera'],\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t// 显示上传中提示\n\t\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\t\ttitle: '上传中...',\n\t\t\t\t\t\t\tmask: true\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 检查token是否存在，不存在则提示用户登录\n\t\t\t\t\t\tconst token = uni.getStorageSync('token');\n\t\t\t\t\t\tif (!token) {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '请先登录',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 逐个上传图片\n\t\t\t\t\t\tconst uploadPromises = res.tempFilePaths.map(path => {\n\t\t\t\t\t\t\treturn uploadUtils.uploadImage(path)\n\t\t\t\t\t\t\t\t.then(serverPath => {\n\t\t\t\t\t\t\t\t\t// 保存本地路径和服务器路径\n\t\t\t\t\t\t\t\t\tthis.imageList.push(path);\n\t\t\t\t\t\t\t\t\tthis.serverImageList.push(serverPath);\n\t\t\t\t\t\t\t\t\treturn serverPath;\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 等待所有图片上传完成\n\t\t\t\t\t\tPromise.all(uploadPromises)\n\t\t\t\t\t\t\t.then(() => {\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '上传成功',\n\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: err.message || '上传失败',\n\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 预览图片\n\t\t\tpreviewImage(index) {\n\t\t\t\t// 将本地路径转换为可访问的URL\n\t\t\t\tconst previewUrls = this.imageList.map((path, i) => {\n\t\t\t\t\t// 如果有对应的服务器路径，使用服务器路径构建完整URL\n\t\t\t\t\tif (this.serverImageList[i]) {\n\t\t\t\t\t\treturn uploadUtils.getFileUrl(this.serverImageList[i]);\n\t\t\t\t\t}\n\t\t\t\t\treturn path;\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tuni.previewImage({\n\t\t\t\t\turls: previewUrls,\n\t\t\t\t\tcurrent: previewUrls[index]\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 删除图片\n\t\t\tdeleteImage(index) {\n\t\t\t\tthis.imageList.splice(index, 1);\n\t\t\t\tthis.serverImageList.splice(index, 1);\n\t\t\t},\n\t\t\t\n\t\t\t// 选择视频\n\t\t\tchooseVideo() {\n\t\t\t\tuni.chooseVideo({\n\t\t\t\t\tsourceType: ['album', 'camera'],\n\t\t\t\t\tmaxDuration: 60,\n\t\t\t\t\tcamera: 'back',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t// 显示上传中提示\n\t\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\t\ttitle: '上传中...',\n\t\t\t\t\t\t\tmask: true\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 检查token是否存在，不存在则提示用户登录\n\t\t\t\t\t\tconst token = uni.getStorageSync('token');\n\t\t\t\t\t\tif (!token) {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '请先登录',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 上传视频到服务器\n\t\t\t\t\t\tuploadUtils.uploadVideo(res.tempFilePath)\n\t\t\t\t\t\t\t.then(serverPath => {\n\t\t\t\t\t\t\t\t// 保存本地路径和服务器路径\n\t\t\t\t\t\t\t\tthis.videoPath = res.tempFilePath;\n\t\t\t\t\t\t\t\tthis.serverVideoPath = serverPath;\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '上传成功',\n\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: err.message || '上传失败',\n\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 获取视频URL\n\t\t\tgetVideoUrl() {\n\t\t\t\t// 特殊处理以@开头的URL\n\t\t\t\tif (this.serverVideoPath && this.serverVideoPath.startsWith('@http')) {\n\t\t\t\t\tconst cleanUrl = this.serverVideoPath.substring(1); // 去除@符号\n\t\t\t\t\tconsole.log('视频路径以@http开头，清理后:', cleanUrl);\n\t\t\t\t\treturn cleanUrl;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 如果是服务器路径，使用工具方法获取完整URL\n\t\t\t\tif (this.serverVideoPath) {\n\t\t\t\t\t// 确保它真的是一个http URL\n\t\t\t\t\tif (this.serverVideoPath.startsWith('http')) {\n\t\t\t\t\t\treturn this.serverVideoPath;\n\t\t\t\t\t}\n\t\t\t\t\tconst url = uploadUtils.getFileUrl(this.serverVideoPath);\n\t\t\t\t\tconsole.log('处理后的视频URL:', url);\n\t\t\t\t\treturn url;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 否则返回本地临时路径\n\t\t\t\tconsole.log('使用本地视频路径:', this.videoPath);\n\t\t\t\treturn this.videoPath;\n\t\t\t},\n\t\t\t\n\t\t\t// 视频加载错误处理\n\t\t\tonVideoError(e) {\n\t\t\t\tconsole.error('视频加载失败:', e.detail);\n\t\t\t\tconsole.error('视频路径:', this.videoPath);\n\t\t\t\tconsole.error('服务器视频路径:', this.serverVideoPath);\n\t\t\t\tconsole.error('处理后URL:', this.getVideoUrl());\n\t\t\t\t\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '视频加载失败，请尝试其他方式查看',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 删除视频\n\t\t\tdeleteVideo() {\n\t\t\t\tthis.videoPath = '';\n\t\t\t\tthis.serverVideoPath = '';\n\t\t\t},\n\t\t\t\n\t\t\t// 取消完成\n\t\t\tcancelComplete() {\n\t\t\t\tuni.navigateBack();\n\t\t\t},\n\t\t\t\n\t\t\t// 验证表单\n\t\t\tvalidateForm() {\n\t\t\t\tlet isValid = true;\n\t\t\t\t\n\t\t\t\tif (!this.formData.repairDate) {\n\t\t\t\t\tthis.showError('请选择维修日期');\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (!this.formData.repairTime) {\n\t\t\t\t\tthis.showError('请选择维修时间');\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tfor (const key in this.rules) {\n\t\t\t\t\tif (key === 'repairDate' || key === 'repairTime') continue;\n\t\t\t\t\t\n\t\t\t\t\tconst value = this.formData[key];\n\t\t\t\t\tconst rules = this.rules[key];\n\t\t\t\t\t\n\t\t\t\t\tfor (const rule of rules) {\n\t\t\t\t\t\tif (rule.required && !value) {\n\t\t\t\t\t\t\tthis.showError(rule.message);\n\t\t\t\t\t\t\tisValid = false;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tif (!isValid) break;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn isValid;\n\t\t\t},\n\t\t\t\n\t\t\t// 上传附件\n\t\t\tuploadAttachments() {\n\t\t\t\treturn new Promise((resolve) => {\n\t\t\t\t\tconst attachments = [];\n\t\t\t\t\t\n\t\t\t\t\t// 添加已上传的图片\n\t\t\t\t\tthis.serverImageList.forEach(serverPath => {\n\t\t\t\t\t\tattachments.push({\n\t\t\t\t\t\t\tfile_type: 'image',\n\t\t\t\t\t\t\tfile_path: serverPath\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 添加已上传的视频\n\t\t\t\t\tif (this.serverVideoPath) {\n\t\t\t\t\t\tattachments.push({\n\t\t\t\t\t\t\tfile_type: 'video',\n\t\t\t\t\t\t\tfile_path: this.serverVideoPath\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 直接返回已上传的附件列表\n\t\t\t\t\tresolve(attachments);\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 提交表单\n\t\t\tsubmitComplete() {\n\t\t\t\tif (!this.validateForm()) return;\n\t\t\t\t\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '确认完成',\n\t\t\t\t\tcontent: '是否确认完成工单？完成后将无法修改',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\t\t\ttitle: '提交中...'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 上传附件\n\t\t\t\t\t\t\tthis.uploadAttachments().then(attachments => {\n\t\t\t\t\t\t\t\tthis.submitOrderComplete(attachments);\n\t\t\t\t\t\t\t}).catch(err => {\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\tthis.showError('附件处理失败: ' + err);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 提交工单完成\n\t\t\tsubmitOrderComplete(attachments) {\n\t\t\t\t// 处理耗材数量\n\t\t\t\tconst repairMaterialsQuantity = this.getRepairMaterialsQuantity();\n\t\t\t\t\n\t\t\t\tconst params = {\n\t\t\t\t\torder_id: this.orderId,\n\t\t\t\t\trepair_user_id: this.getCurrentUserId(),\n\t\t\t\t\torder_status: '已完成',\n\t\t\t\t\trepair_content: this.formData.repairContent,\n\t\t\t\t\trepair_result: this.formData.repairResult,\n\t\t\t\t\trepair_materials_quantity: repairMaterialsQuantity,\n\t\t\t\t\trepair_time: this.formData.repairDateTime,\n\t\t\t\t\tattachment: attachments\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\tconsole.log('提交参数:', params);\n\t\t\t\t\n\t\t\t\tworkOrderApi.completeOrder(params)\n\t\t\t\t\t.then(res => {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tif (res.code === 200) {\n\t\t\t\t\t\t\tthis.showSuccess('工单已完成');\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tuni.navigateBack({\n\t\t\t\t\t\t\t\t\tdelta: 1\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.showError(res.message || '提交失败');\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tthis.showError('网络异常，请稍后重试');\n\t\t\t\t\t\tconsole.error('完成工单失败:', err);\n\t\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 获取当前用户ID\n\t\t\tgetCurrentUserId() {\n\t\t\t\tconst userId = uni.getStorageSync('userId');\n\t\t\t\treturn userId || 1;\n\t\t\t},\n\t\t\t\n\t\t\t// 显示成功提示\n\t\t\tshowSuccess(message) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: message,\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 显示错误提示\n\t\t\tshowError(message) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: message,\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t.complete-container {\n\t\tmin-height: 100vh;\n\t\tbackground-color: #f5f5f5;\n\t\tpadding: 20rpx;\n\t}\n\t\n\t.form-card {\n\t\tbackground-color: #fff;\n\t\tborder-radius: 12rpx;\n\t\tmargin-bottom: 20rpx;\n\t\tpadding: 30rpx;\n\t\t\n\t\t.form-title {\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: bold;\n\t\t\tcolor: $uni-text-color;\n\t\t\tmargin-bottom: 30rpx;\n\t\t\tpadding-bottom: 20rpx;\n\t\t\tborder-bottom: 1rpx solid #eee;\n\t\t}\n\t\t\n\t\t.form-item {\n\t\t\tmargin-bottom: 30rpx;\n\t\t\t\n\t\t\t.form-label {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tcolor: $uni-text-color;\n\t\t\t\tmargin-bottom: 16rpx;\n\t\t\t\t\n\t\t\t\t&.required::before {\n\t\t\t\t\tcontent: '*';\n\t\t\t\t\tcolor: #f5222d;\n\t\t\t\t\tmargin-right: 6rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.form-textarea {\n\t\t\t\twidth: 100%;\n\t\t\t\theight: 200rpx;\n\t\t\t\tpadding: 20rpx;\n\t\t\t\tbackground-color: #f5f5f5;\n\t\t\t\tborder-radius: 8rpx;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tcolor: $uni-text-color;\n\t\t\t\tbox-sizing: border-box;\n\t\t\t}\n\t\t\t\n\t\t\t.picker-view {\n\t\t\t\twidth: 100%;\n\t\t\t\theight: 80rpx;\n\t\t\t\tpadding: 0 20rpx;\n\t\t\t\tbackground-color: #f5f5f5;\n\t\t\t\tborder-radius: 8rpx;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tcolor: $uni-text-color;\n\t\t\t\tbox-sizing: border-box;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\tmargin-bottom: 10rpx;\n\t\t\t\t\n\t\t\t\t.iconfont {\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tcolor: #999;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.upload-area {\n\t\t\t\t.form-header {\n\t\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t\t\n\t\t\t\t\t.form-subtitle {\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tcolor: $uni-text-color-grey;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.image-grid {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex-wrap: wrap;\n\t\t\t\t\t\n\t\t\t\t\t.image-item, .upload-item {\n\t\t\t\t\t\twidth: 200rpx;\n\t\t\t\t\t\theight: 200rpx;\n\t\t\t\t\t\tmargin-right: 20rpx;\n\t\t\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t\t\tposition: relative;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.image-item {\n\t\t\t\t\t\timage {\n\t\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\t\theight: 100%;\n\t\t\t\t\t\t\tborder-radius: 8rpx;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t.delete-icon {\n\t\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\t\ttop: -16rpx;\n\t\t\t\t\t\t\tright: -16rpx;\n\t\t\t\t\t\t\twidth: 40rpx;\n\t\t\t\t\t\t\theight: 40rpx;\n\t\t\t\t\t\t\tbackground-color: rgba(0, 0, 0, 0.5);\n\t\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.upload-item {\n\t\t\t\t\t\tborder: 2rpx dashed #ddd;\n\t\t\t\t\t\tborder-radius: 8rpx;\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\tflex-direction: column;\n\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\n\t\t\t\t\t\t.iconfont {\n\t\t\t\t\t\t\tfont-size: 60rpx;\n\t\t\t\t\t\t\tcolor: $uni-text-color-grey;\n\t\t\t\t\t\t\tmargin-bottom: 16rpx;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\ttext {\n\t\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\t\tcolor: $uni-text-color-grey;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.video-container {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\theight: 400rpx;\n\t\t\t\t\tposition: relative;\n\t\t\t\t\t\n\t\t\t\t\tvideo {\n\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\theight: 100%;\n\t\t\t\t\t\tborder-radius: 8rpx;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.delete-icon {\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\ttop: 16rpx;\n\t\t\t\t\t\tright: 16rpx;\n\t\t\t\t\t\twidth: 40rpx;\n\t\t\t\t\t\theight: 40rpx;\n\t\t\t\t\t\tbackground-color: rgba(0, 0, 0, 0.5);\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.action-buttons {\n\t\tdisplay: flex;\n\t\tpadding: 20rpx 0;\n\t\t\n\t\tbutton {\n\t\t\tflex: 1;\n\t\t\theight: 80rpx;\n\t\t\tline-height: 80rpx;\n\t\t\ttext-align: center;\n\t\t\tborder-radius: 40rpx;\n\t\t\tfont-size: 28rpx;\n\t\t\tmargin: 0 10rpx;\n\t\t\t\n\t\t\t&::after {\n\t\t\t\tborder: none;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.btn-cancel {\n\t\t\tbackground-color: #f5f5f5;\n\t\t\tcolor: $uni-text-color;\n\t\t\tborder: 1px solid #ddd;\n\t\t}\n\t\t\n\t\t.btn-submit {\n\t\t\tbackground-color: $uni-color-primary;\n\t\t\tcolor: #fff;\n\t\t}\n\t}\n\t\n\t// 耗材相关样式\n\t.materials-container {\n\t\t.material-item {\n\t\t\tmargin-bottom: 15rpx;\n\t\t\t\n\t\t\t.material-row {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\t\n\t\t\t\t.material-selector {\n\t\t\t\t\tflex: 2;\n\t\t\t\t\tmargin-right: 15rpx;\n\t\t\t\t\t\n\t\t\t\t\t.material-input {\n\t\t\t\t\t\theight: 70rpx;\n\t\t\t\t\t\tbackground-color: #f5f5f5;\n\t\t\t\t\t\tborder-radius: 8rpx;\n\t\t\t\t\t\tpadding: 0 20rpx;\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\n\t\t\t\t\t\t.arrow-down {\n\t\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\t\tcolor: #999;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.quantity-input {\n\t\t\t\t\tflex: 1;\n\t\t\t\t\theight: 70rpx;\n\t\t\t\t\tbackground-color: #f5f5f5;\n\t\t\t\t\tborder-radius: 8rpx;\n\t\t\t\t\tpadding: 0 20rpx;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.delete-btn {\n\t\t\t\t\twidth: 60rpx;\n\t\t\t\t\theight: 60rpx;\n\t\t\t\t\tline-height: 60rpx;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tfont-size: 40rpx;\n\t\t\t\t\tcolor: #999;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.add-material-btn {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\theight: 70rpx;\n\t\t\tbackground-color: #f5f5f5;\n\t\t\tborder-radius: 8rpx;\n\t\t\tcolor: $uni-color-primary;\n\t\t\tfont-size: 28rpx;\n\t\t\tborder: 1px dashed #ccc;\n\t\t\t\n\t\t\t.iconfont {\n\t\t\t\tmargin-right: 10rpx;\n\t\t\t}\n\t\t}\n\t}\n\t\n\t// 材料选择器弹窗样式\n\t.material-selector-popup {\n\t\tbackground-color: #fff;\n\t\tborder-radius: 20rpx 20rpx 0 0;\n\t\tmax-height: 80vh;\n\t\theight: 60vh;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tz-index: 100;\n\t\t\n\t\t.selector-header {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\tpadding: 30rpx;\n\t\t\tborder-bottom: 1rpx solid #e5e5e5;\n\t\t\t\n\t\t\t.selector-title {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: bold;\n\t\t\t\tcolor: $uni-text-color;\n\t\t\t}\n\t\t\t\n\t\t\t.header-actions {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t}\n\t\t\t\n\t\t\t.close-button {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tcolor: $uni-text-color-grey;\n\t\t\t}\n\t\t\t\n\t\t\t.confirm-button-header {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tcolor: $uni-color-primary;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tmargin-right: 20rpx;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.selector-content {\n\t\t\tflex: 1;\n\t\t\toverflow-y: auto;\n\t\t\tpadding: 0 30rpx;\n\t\t\t-webkit-overflow-scrolling: touch;\n\t\t\t\n\t\t\t.template-search {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tpadding: 20rpx 0;\n\t\t\t\tborder-bottom: 1rpx solid #f0f0f0;\n\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\tposition: relative;\n\t\t\t\t\n\t\t\t\t.search-input {\n\t\t\t\t\tflex: 1;\n\t\t\t\t\theight: 70rpx;\n\t\t\t\t\tborder-radius: 35rpx;\n\t\t\t\t\tbackground: #f5f5f5;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tpadding: 0 70rpx 0 30rpx;\n\t\t\t\t\tcolor: $uni-text-color;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.search-clear {\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\tright: 130rpx;\n\t\t\t\t\ttop: 50%;\n\t\t\t\t\ttransform: translateY(-50%);\n\t\t\t\t\twidth: 40rpx;\n\t\t\t\t\theight: 40rpx;\n\t\t\t\t\tline-height: 40rpx;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\tbackground: #ccc;\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.search-btn {\n\t\t\t\t\tpadding: 0 30rpx;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: $uni-color-primary;\n\t\t\t\t\theight: 70rpx;\n\t\t\t\t\tline-height: 70rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.material-list {\n\t\t\t\t.material-item-popup {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tpadding: 25rpx 0;\n\t\t\t\t\tborder-bottom: 1rpx solid #f0f0f0;\n\t\t\t\t\t\n\t\t\t\t\t&:last-child {\n\t\t\t\t\t\tborder-bottom: none;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.material-info {\n\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\t\n\t\t\t\t\t\t.material-name {\n\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\tcolor: $uni-text-color;\n\t\t\t\t\t\t\tmargin-bottom: 6rpx;\n\t\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t.material-type {\n\t\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\t\tcolor: $uni-text-color-grey;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.checkbox {\n\t\t\t\t\t\twidth: 40rpx;\n\t\t\t\t\t\theight: 40rpx;\n\t\t\t\t\t\tborder: 1rpx solid #e5e5e5;\n\t\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t\t\n\t\t\t\t\t\t&.checked {\n\t\t\t\t\t\t\tbackground-color: $uni-color-primary;\n\t\t\t\t\t\t\tborder-color: $uni-color-primary;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t.iconfont {\n\t\t\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.empty-tip {\n\t\t\t\ttext-align: center;\n\t\t\t\tpadding: 40rpx 0;\n\t\t\t\tcolor: $uni-text-color-grey;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t}\n\t\t}\n\t}\n\t\n\t// 图标样式\n\t.iconfont {\n\t\tfont-size: 16px;\n\t\tfont-style: normal;\n\t\t-webkit-font-smoothing: antialiased;\n\t\t-moz-osx-font-smoothing: grayscale;\n\t}\n\t\n\t.icon-add:before {\n\t\tcontent: \"+\";\n\t}\n\t\n\t.icon-check:before {\n\t\tcontent: \"✓\";\n\t}\n</style> ", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/workorder/complete.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "materialsApi", "uploadUtils", "workOrderApi"], "mappings": ";;;;AAuKC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,SAAS;AAAA,MACT,UAAU;AAAA,QACT,eAAe;AAAA,QACf,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,gBAAgB;AAAA,QAChB,YAAY,CAAC;AAAA,MACb;AAAA,MACD,qBAAqB;AAAA,QACpB,EAAE,MAAM,IAAI,UAAU,GAAG;AAAA,MACzB;AAAA,MACD,OAAO;AAAA,QACN,eAAe;AAAA,UACd,EAAE,UAAU,MAAM,SAAS,UAAU;AAAA,QACrC;AAAA,QACD,cAAc;AAAA,UACb,EAAE,UAAU,MAAM,SAAS,UAAU;AAAA,QACrC;AAAA,QACD,YAAY;AAAA,UACX,EAAE,UAAU,MAAM,SAAS,UAAU;AAAA,QACrC;AAAA,QACD,YAAY;AAAA,UACX,EAAE,UAAU,MAAM,SAAS,UAAU;AAAA,QACtC;AAAA,MACA;AAAA,MACD,eAAe,CAAC,CAAA,GAAI,CAAE,GAAE,CAAE,GAAE,CAAE,GAAE,CAAE,GAAE,EAAE;AAAA,MACtC,eAAe,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,MAChC,WAAW,CAAE;AAAA;AAAA,MACb,iBAAiB,CAAE;AAAA;AAAA,MACnB,WAAW;AAAA;AAAA,MACX,iBAAiB;AAAA;AAAA,MACjB,aAAa;AAAA;AAAA,MAEb,iBAAiB,CAAE;AAAA;AAAA,MACnB,uBAAuB;AAAA;AAAA,MACvB,mBAAmB,CAAE;AAAA;AAAA,MACrB,sBAAsB;AAAA;AAAA,MACtB,sBAAsB;AAAA;AAAA,MACtB,aAAa;AAAA;AAAA,IACd;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AACf,QAAI,QAAQ,IAAI;AACf,WAAK,UAAU,QAAQ;AAAA,WACjB;AACN,WAAK,UAAU,QAAQ;AACvB,iBAAW,MAAM;AAChBA,sBAAG,MAAC,aAAY;AAAA,MAChB,GAAE,IAAI;AAAA,IACR;AAGA,SAAK,mBAAkB;AAGvB,UAAM,MAAM,oBAAI;AAChB,UAAM,OAAO,IAAI;AACjB,UAAM,QAAQ,OAAO,IAAI,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACxD,UAAM,MAAM,OAAO,IAAI,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AACjD,UAAM,QAAQ,OAAO,IAAI,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AACpD,UAAM,UAAU,OAAO,IAAI,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AAExD,SAAK,SAAS,aAAa,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAClD,SAAK,SAAS,aAAa,GAAG,KAAK,IAAI,OAAO;AAC9C,SAAK,qBAAoB;AAGzB,SAAK,kBAAiB;AAAA,EACtB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,cAAc;AACb,WAAK,oBAAoB,KAAK,EAAE,MAAM,IAAI,UAAU,GAAG,CAAC;AAAA,IACxD;AAAA;AAAA,IAGD,eAAe,OAAO;AACrB,UAAI,KAAK,oBAAoB,SAAS,GAAG;AACxC,aAAK,oBAAoB,OAAO,OAAO,CAAC;AAAA,aAClC;AAEN,aAAK,sBAAsB,CAAC,EAAE,MAAM,IAAI,UAAU,GAAG,CAAC;AAAA,MACvD;AAAA,IACA;AAAA;AAAA,IAGD,6BAA6B;AAC5B,YAAM,SAAS,CAAA;AACf,WAAK,oBAAoB,QAAQ,UAAQ;AACxC,YAAI,KAAK,QAAQ,KAAK,KAAK,KAAI,GAAI;AAClC,iBAAO,KAAK,KAAK,KAAM,CAAA,IAAI,KAAK,YAAY;AAAA,QAC7C;AAAA,MACD,CAAC;AACD,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,oBAAoB;AACnBA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACR,CAAC;AAEDC,gBAAAA,aAAa,iBAAiB,EAC5B,KAAK,SAAO;AACZD,sBAAG,MAAC,YAAW;AACf,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AACjC,eAAK,kBAAkB,IAAI;AAC3B,eAAK,oBAAoB,CAAC,GAAG,IAAI,IAAI;AAAA,eAC/B;AACN,eAAK,UAAU,UAAU;AAAA,QAC1B;AAAA,OACA,EACA,MAAM,SAAO;AACbA,sBAAG,MAAC,YAAW;AACfA,sBAAc,MAAA,MAAA,SAAA,uCAAA,aAAa,GAAG;AAC9B,aAAK,UAAU,cAAc;AAAA,MAC9B,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,0BAA0B,OAAO;AAChC,WAAK,uBAAuB;AAC5B,WAAK,uBAAuB;AAG5B,YAAM,kBAAkB,KAAK,oBAAoB,KAAK;AACtD,UAAI,mBAAmB,gBAAgB,MAAM;AAC5C,cAAM,gBAAgB,KAAK,gBAAgB,KAAK,OAAK,EAAE,SAAS,gBAAgB,IAAI;AACpF,YAAI,eAAe;AAClB,eAAK,uBAAuB;AAAA,QAC7B;AAAA,MACD;AAGA,WAAK,wBAAwB;AAC7B,WAAK,oBAAoB,CAAC,GAAG,KAAK,eAAe;AAEjD,WAAK,cAAc;AACnB,WAAK,MAAM,iBAAiB;IAC5B;AAAA;AAAA,IAGD,uBAAuB;AACtB,WAAK,cAAc;AACnB,WAAK,MAAM,iBAAiB;IAC5B;AAAA;AAAA,IAGD,eAAe,UAAU;AACxB,WAAK,uBAAuB;AAAA,IAC5B;AAAA;AAAA,IAGD,2BAA2B;AAC1B,UAAI,KAAK,wBAAwB,KAAK,yBAAyB,IAAI;AAClE,aAAK,oBAAoB,KAAK,oBAAoB,EAAE,OAAO,KAAK,qBAAqB;AAAA,MACtF;AAEA,WAAK,qBAAoB;AAAA,IACzB;AAAA;AAAA,IAGD,uBAAuB;AACtB,UAAI,CAAC,KAAK,yBAAyB,KAAK,sBAAsB,WAAW,IAAI;AAC5E,aAAK,oBAAoB,CAAC,GAAG,KAAK,eAAe;AACjD;AAAA,MACD;AAEA,YAAM,UAAU,KAAK,sBAAsB,YAAa,EAAC,KAAI;AAC7D,WAAK,oBAAoB,KAAK,gBAAgB;AAAA,QAAO,cACpD,SAAS,KAAK,cAAc,SAAS,OAAO,KAC3C,SAAS,iBAAiB,SAAS,cAAc,cAAc,SAAS,OAAO;AAAA;IAEjF;AAAA;AAAA,IAGD,sBAAsB;AACrB,WAAK,wBAAwB;AAC7B,WAAK,oBAAoB,CAAC,GAAG,KAAK,eAAe;AAAA,IACjD;AAAA;AAAA,IAGD,qBAAqB;AAEpB,YAAM,OAAO,oBAAI;AACjB,YAAM,cAAc,KAAK;AACzB,YAAM,QAAQ,CAAA;AACd,eAAS,IAAI,cAAc,GAAG,KAAK,cAAc,GAAG,KAAK;AACxD,cAAM,KAAK,IAAI,GAAG;AAAA,MACnB;AAGA,YAAM,SAAS,CAAA;AACf,eAAS,IAAI,GAAG,KAAK,IAAI,KAAK;AAC7B,eAAO,KAAK,IAAI,GAAG;AAAA,MACpB;AAGA,YAAM,OAAO,CAAA;AACb,eAAS,IAAI,GAAG,KAAK,IAAI,KAAK;AAC7B,aAAK,KAAK,IAAI,GAAG;AAAA,MAClB;AAGA,YAAM,QAAQ,CAAA;AACd,eAAS,IAAI,GAAG,KAAK,IAAI,KAAK;AAC7B,cAAM,KAAK,IAAI,GAAG;AAAA,MACnB;AAGA,YAAM,UAAU,CAAA;AAChB,eAAS,IAAI,GAAG,KAAK,IAAI,KAAK;AAC7B,gBAAQ,KAAK,IAAI,GAAG;AAAA,MACrB;AAGA,YAAM,UAAU,CAAA;AAChB,eAAS,IAAI,GAAG,KAAK,IAAI,KAAK;AAC7B,gBAAQ,KAAK,IAAI,GAAG;AAAA,MACrB;AAGA,WAAK,gBAAgB,CAAC,OAAO,QAAQ,MAAM,OAAO,SAAS,OAAO;AAGlE,YAAM,YAAY;AAClB,YAAM,aAAa,KAAK;AACxB,YAAM,WAAW,KAAK,QAAO,IAAK;AAClC,YAAM,YAAY,KAAK;AACvB,YAAM,cAAc,KAAK;AACzB,YAAM,cAAc,KAAK;AAEzB,WAAK,gBAAgB,CAAC,WAAW,YAAY,UAAU,WAAW,aAAa,WAAW;AAAA,IAC1F;AAAA;AAAA,IAGD,iBAAiB,GAAG;AACnB,YAAM,SAAS,EAAE,OAAO;AACxB,WAAK,gBAAgB;AAGrB,YAAM,OAAO,SAAS,KAAK,cAAc,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;AACtD,YAAM,QAAQ,OAAO,SAAS,KAAK,cAAc,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,SAAS,GAAG,GAAG;AAChF,YAAM,MAAM,OAAO,SAAS,KAAK,cAAc,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,SAAS,GAAG,GAAG;AAC9E,YAAM,OAAO,OAAO,SAAS,KAAK,cAAc,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,SAAS,GAAG,GAAG;AAC/E,YAAM,SAAS,OAAO,SAAS,KAAK,cAAc,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,SAAS,GAAG,GAAG;AACjF,YAAM,SAAS,OAAO,SAAS,KAAK,cAAc,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,SAAS,GAAG,GAAG;AAEjF,WAAK,SAAS,aAAa,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAClD,WAAK,SAAS,aAAa,GAAG,IAAI,IAAI,MAAM,IAAI,MAAM;AACtD,WAAK,qBAAoB;AAAA,IACzB;AAAA;AAAA,IAGD,eAAe,GAAG;AACjB,UAAI,SAAS,EAAE,OAAO;AACtB,UAAI,QAAQ,EAAE,OAAO;AAGrB,UAAI,WAAW,KAAK,WAAW,GAAG;AACjC,cAAM,OAAO,SAAS,KAAK,cAAc,CAAC,EAAE,KAAK,cAAc,CAAC,CAAC,CAAC;AAClE,cAAM,QAAQ,SAAS,KAAK,cAAc,CAAC,EAAE,UAAU,SAAY,KAAK,cAAc,CAAC,IAAI,KAAK,CAAC;AAGjG,cAAM,cAAc,IAAI,KAAK,MAAM,OAAO,CAAC,EAAE;AAG7C,cAAM,OAAO,CAAA;AACb,iBAAS,IAAI,GAAG,KAAK,aAAa,KAAK;AACtC,eAAK,KAAK,IAAI,GAAG;AAAA,QAClB;AAGA,aAAK,cAAc,CAAC,IAAI;AAGxB,YAAI,KAAK,cAAc,CAAC,KAAK,aAAa;AACzC,eAAK,cAAc,CAAC,IAAI,cAAc;AAAA,QACvC;AAAA,MACD;AAAA,IACA;AAAA;AAAA,IAGD,uBAAuB;AACtB,UAAI,KAAK,SAAS,cAAc,KAAK,SAAS,YAAY;AACzD,cAAM,cAAc,GAAG,KAAK,SAAS,UAAU,IAAI,KAAK,SAAS,UAAU;AAC3E,aAAK,SAAS,iBAAiB;AAAA,MAChC;AAAA,IACA;AAAA;AAAA,IAGD,gBAAgB;AACf,YAAM,OAAO,oBAAI;AACjB,WAAK,YAAY,KAAK,YAAc,IAAE,CAAC;AACvC,YAAM,OAAO,KAAK;AAClB,YAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,YAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,IAC9B;AAAA;AAAA,IAGD,iBAAiB;AAChB,YAAM,OAAO,oBAAI;AACjB,YAAM,OAAO,KAAK;AAClB,YAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,YAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,IAC9B;AAAA;AAAA,IAGD,cAAc;AACbA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO,IAAI,KAAK,UAAU;AAAA,QAC1B,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AAEjBA,wBAAAA,MAAI,YAAY;AAAA,YACf,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAGD,gBAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,cAAI,CAAC,OAAO;AACXA,0BAAG,MAAC,YAAW;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACP,CAAC;AACD;AAAA,UACD;AAGA,gBAAM,iBAAiB,IAAI,cAAc,IAAI,UAAQ;AACpD,mBAAOE,aAAW,YAAC,YAAY,IAAI,EACjC,KAAK,gBAAc;AAEnB,mBAAK,UAAU,KAAK,IAAI;AACxB,mBAAK,gBAAgB,KAAK,UAAU;AACpC,qBAAO;AAAA,YACR,CAAC;AAAA,UACH,CAAC;AAGD,kBAAQ,IAAI,cAAc,EACxB,KAAK,MAAM;AACXF,0BAAG,MAAC,YAAW;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACP,CAAC;AAAA,WACD,EACA,MAAM,SAAO;AACbA,0BAAG,MAAC,YAAW;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO,IAAI,WAAW;AAAA,cACtB,MAAM;AAAA,YACP,CAAC;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,aAAa,OAAO;AAEnB,YAAM,cAAc,KAAK,UAAU,IAAI,CAAC,MAAM,MAAM;AAEnD,YAAI,KAAK,gBAAgB,CAAC,GAAG;AAC5B,iBAAOE,aAAAA,YAAY,WAAW,KAAK,gBAAgB,CAAC,CAAC;AAAA,QACtD;AACA,eAAO;AAAA,MACR,CAAC;AAEDF,oBAAAA,MAAI,aAAa;AAAA,QAChB,MAAM;AAAA,QACN,SAAS,YAAY,KAAK;AAAA,MAC3B,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,YAAY,OAAO;AAClB,WAAK,UAAU,OAAO,OAAO,CAAC;AAC9B,WAAK,gBAAgB,OAAO,OAAO,CAAC;AAAA,IACpC;AAAA;AAAA,IAGD,cAAc;AACbA,oBAAAA,MAAI,YAAY;AAAA,QACf,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,SAAS,CAAC,QAAQ;AAEjBA,wBAAAA,MAAI,YAAY;AAAA,YACf,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAGD,gBAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,cAAI,CAAC,OAAO;AACXA,0BAAG,MAAC,YAAW;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACP,CAAC;AACD;AAAA,UACD;AAGAE,mCAAY,YAAY,IAAI,YAAY,EACtC,KAAK,gBAAc;AAEnB,iBAAK,YAAY,IAAI;AACrB,iBAAK,kBAAkB;AAEvBF,0BAAG,MAAC,YAAW;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACP,CAAC;AAAA,WACD,EACA,MAAM,SAAO;AACbA,0BAAG,MAAC,YAAW;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO,IAAI,WAAW;AAAA,cACtB,MAAM;AAAA,YACP,CAAC;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AAEb,UAAI,KAAK,mBAAmB,KAAK,gBAAgB,WAAW,OAAO,GAAG;AACrE,cAAM,WAAW,KAAK,gBAAgB,UAAU,CAAC;AACjDA,gFAAY,qBAAqB,QAAQ;AACzC,eAAO;AAAA,MACR;AAGA,UAAI,KAAK,iBAAiB;AAEzB,YAAI,KAAK,gBAAgB,WAAW,MAAM,GAAG;AAC5C,iBAAO,KAAK;AAAA,QACb;AACA,cAAM,MAAME,aAAW,YAAC,WAAW,KAAK,eAAe;AACvDF,gFAAY,cAAc,GAAG;AAC7B,eAAO;AAAA,MACR;AAGAA,oBAAA,MAAA,MAAA,OAAA,uCAAY,aAAa,KAAK,SAAS;AACvC,aAAO,KAAK;AAAA,IACZ;AAAA;AAAA,IAGD,aAAa,GAAG;AACfA,oBAAc,MAAA,MAAA,SAAA,uCAAA,WAAW,EAAE,MAAM;AACjCA,oBAAA,MAAA,MAAA,SAAA,uCAAc,SAAS,KAAK,SAAS;AACrCA,oBAAc,MAAA,MAAA,SAAA,uCAAA,YAAY,KAAK,eAAe;AAC9CA,oBAAA,MAAA,MAAA,SAAA,uCAAc,WAAW,KAAK,YAAW,CAAE;AAE3CA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACX,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AACb,WAAK,YAAY;AACjB,WAAK,kBAAkB;AAAA,IACvB;AAAA;AAAA,IAGD,iBAAiB;AAChBA,oBAAG,MAAC,aAAY;AAAA,IAChB;AAAA;AAAA,IAGD,eAAe;AACd,UAAI,UAAU;AAEd,UAAI,CAAC,KAAK,SAAS,YAAY;AAC9B,aAAK,UAAU,SAAS;AACxB,eAAO;AAAA,MACR;AAEA,UAAI,CAAC,KAAK,SAAS,YAAY;AAC9B,aAAK,UAAU,SAAS;AACxB,eAAO;AAAA,MACR;AAEA,iBAAW,OAAO,KAAK,OAAO;AAC7B,YAAI,QAAQ,gBAAgB,QAAQ;AAAc;AAElD,cAAM,QAAQ,KAAK,SAAS,GAAG;AAC/B,cAAM,QAAQ,KAAK,MAAM,GAAG;AAE5B,mBAAW,QAAQ,OAAO;AACzB,cAAI,KAAK,YAAY,CAAC,OAAO;AAC5B,iBAAK,UAAU,KAAK,OAAO;AAC3B,sBAAU;AACV;AAAA,UACD;AAAA,QACD;AAEA,YAAI,CAAC;AAAS;AAAA,MACf;AAEA,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,oBAAoB;AACnB,aAAO,IAAI,QAAQ,CAAC,YAAY;AAC/B,cAAM,cAAc,CAAA;AAGpB,aAAK,gBAAgB,QAAQ,gBAAc;AAC1C,sBAAY,KAAK;AAAA,YAChB,WAAW;AAAA,YACX,WAAW;AAAA,UACZ,CAAC;AAAA,QACF,CAAC;AAGD,YAAI,KAAK,iBAAiB;AACzB,sBAAY,KAAK;AAAA,YAChB,WAAW;AAAA,YACX,WAAW,KAAK;AAAA,UACjB,CAAC;AAAA,QACF;AAGA,gBAAQ,WAAW;AAAA,MACpB,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB;AAChB,UAAI,CAAC,KAAK,aAAY;AAAI;AAE1BA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAChBA,0BAAAA,MAAI,YAAY;AAAA,cACf,OAAO;AAAA,YACR,CAAC;AAGD,iBAAK,kBAAiB,EAAG,KAAK,iBAAe;AAC5C,mBAAK,oBAAoB,WAAW;AAAA,YACrC,CAAC,EAAE,MAAM,SAAO;AACfA,4BAAG,MAAC,YAAW;AACf,mBAAK,UAAU,aAAa,GAAG;AAAA,YAChC,CAAC;AAAA,UACF;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB,aAAa;AAEhC,YAAM,0BAA0B,KAAK;AAErC,YAAM,SAAS;AAAA,QACd,UAAU,KAAK;AAAA,QACf,gBAAgB,KAAK,iBAAkB;AAAA,QACvC,cAAc;AAAA,QACd,gBAAgB,KAAK,SAAS;AAAA,QAC9B,eAAe,KAAK,SAAS;AAAA,QAC7B,2BAA2B;AAAA,QAC3B,aAAa,KAAK,SAAS;AAAA,QAC3B,YAAY;AAAA;AAGbA,oBAAA,MAAA,MAAA,OAAA,uCAAY,SAAS,MAAM;AAE3BG,gBAAY,aAAC,cAAc,MAAM,EAC/B,KAAK,SAAO;AACZH,sBAAG,MAAC,YAAW;AACf,YAAI,IAAI,SAAS,KAAK;AACrB,eAAK,YAAY,OAAO;AACxB,qBAAW,MAAM;AAChBA,0BAAAA,MAAI,aAAa;AAAA,cAChB,OAAO;AAAA,YACR,CAAC;AAAA,UACD,GAAE,IAAI;AAAA,eACD;AACN,eAAK,UAAU,IAAI,WAAW,MAAM;AAAA,QACrC;AAAA,OACA,EACA,MAAM,SAAO;AACbA,sBAAG,MAAC,YAAW;AACf,aAAK,UAAU,YAAY;AAC3BA,sBAAc,MAAA,MAAA,SAAA,uCAAA,WAAW,GAAG;AAAA,MAC7B,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,mBAAmB;AAClB,YAAM,SAASA,cAAAA,MAAI,eAAe,QAAQ;AAC1C,aAAO,UAAU;AAAA,IACjB;AAAA;AAAA,IAGD,YAAY,SAAS;AACpBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,UAAU,SAAS;AAClBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjyBD,GAAG,WAAW,eAAe;"}