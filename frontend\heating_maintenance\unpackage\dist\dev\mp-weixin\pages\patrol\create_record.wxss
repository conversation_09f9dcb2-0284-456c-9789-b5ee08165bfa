/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  width: 100%;
}
.page-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  padding: 40rpx 30rpx 20rpx;
  text-align: center;
  background-color: #fff;
  position: relative;
}
.page-title::after {
  content: "";
  position: absolute;
  bottom: 5rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: #1890ff;
  border-radius: 3rpx;
}
.form-container {
  flex: 1;
  padding: 30rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}
.form-group {
  margin-bottom: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  width: 90%;
  max-width: 680rpx;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
  border-left: 4rpx solid #1890ff;
}
.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}
.required:after {
  content: "*";
  color: #f56c6c;
  margin-left: 6rpx;
}
.form-input {
  width: 100%;
  height: 80rpx;
  border: 1px solid #dcdfe6;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.select-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.placeholder {
  color: #999;
}
.select-arrow {
  font-size: 24rpx;
  color: #999;
}
.form-textarea {
  width: 100%;
  height: 200rpx;
  border: 1px solid #dcdfe6;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}
.status-group {
  display: flex;
  flex-direction: row;
  gap: 30rpx;
}
.status-option {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 20rpx;
  border: 1px solid #dcdfe6;
  border-radius: 8rpx;
  transition: all 0.3s;
  cursor: pointer;
}
.status-option.active {
  border-color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
}
.status-dot {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: #dcdfe6;
}
.status-option.active .status-dot {
  background-color: #1890ff;
}
.status-text {
  font-size: 28rpx;
  color: #333;
}
.status-option.active .status-text {
  color: #1890ff;
  font-weight: 500;
}
.results-list {
  margin-top: 20rpx;
  width: 100%;
}
.result-item {
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 16rpx;
  border-left: 4rpx solid #1890ff;
  transition: all 0.3s ease;
  cursor: pointer;
}
.result-item:nth-child(3n+1) {
  border-left-color: #1890ff;
}
.result-item:nth-child(3n+2) {
  border-left-color: #52c41a;
}
.result-item:nth-child(3n+3) {
  border-left-color: #faad14;
}
.result-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
}
.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.result-title {
  font-size: 28rpx;
  font-weight: 500;
}
.result-status {
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
}
.result-status.normal {
  background-color: #f0f9eb;
  color: #67c23a;
}
.result-status.abnormal {
  background-color: #fef0f0;
  color: #f56c6c;
}
.result-body {
  font-size: 26rpx;
  color: #666;
}
.result-row {
  margin-bottom: 8rpx;
}
.result-label {
  color: #999;
  margin-right: 8rpx;
}
.submit-section {
  margin: 40rpx 0;
  width: 90%;
  max-width: 680rpx;
  display: flex;
  justify-content: center;
  margin-left: auto;
  margin-right: auto;
}
.submit-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background-color: #1890ff;
  color: #fff;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.3);
}
.submit-btn.disabled {
  background-color: #a0cfff;
}

/* 弹窗样式 */
.result-popup {
  background-color: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  padding-bottom: env(safe-area-inset-bottom);
  max-height: 80vh;
}
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #eee;
}
.popup-title {
  font-size: 32rpx;
  font-weight: 500;
}
.popup-close {
  font-size: 28rpx;
  color: #999;
}
.popup-content {
  padding: 30rpx;
  max-height: calc(80vh - 100rpx);
  overflow-y: auto;
}
.popup-form-group {
  margin-bottom: 24rpx;
  position: relative;
}
.popup-form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}
.popup-input {
  width: 100%;
  height: 80rpx;
  border: 1px solid #dcdfe6;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.popup-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
.popup-form-textarea {
  width: 100%;
  height: 160rpx;
  border: 1px solid #dcdfe6;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.result-radio-group {
  display: flex;
  gap: 20rpx;
}
.result-radio {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx;
  border: 1px solid #dcdfe6;
  border-radius: 8rpx;
  transition: all 0.3s;
}
.result-radio.result-radio-selected {
  border-color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
}
.result-image-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}
.result-image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  margin: 10rpx;
  border-radius: 8rpx;
  overflow: hidden;
}
.result-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.result-image-delete {
  position: absolute;
  top: 6rpx;
  right: 6rpx;
  width: 36rpx;
  height: 36rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.icon-delete {
  font-size: 24rpx;
  color: #ffffff;
  line-height: 24rpx;
}
.result-image-add {
  width: 160rpx;
  height: 160rpx;
  margin: 10rpx;
  background-color: #f5f5f5;
  border: 1px dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #999;
}
.icon-add {
  font-size: 48rpx;
  line-height: 48rpx;
  color: #999;
}
.result-image-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}
.location-btns {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.location-btn {
  background-color: #1890ff;
  color: #fff;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
  border-radius: 8rpx;
}
.popup-save-btn {
  margin-top: 40rpx;
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background-color: #1890ff;
  color: #fff;
  border-radius: 8rpx;
  font-size: 32rpx;
}
.device-info-box {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.device-name {
  font-size: 28rpx;
  font-weight: 500;
}
.device-category {
  font-size: 24rpx;
  color: #999;
}
.standard-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.normal-range {
  font-size: 24rpx;
  color: #999;
}
.check-method {
  font-size: 24rpx;
  color: #999;
}
.parameter-unit {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  color: #666;
}

/* 时间选择器样式 */
.datetime-popup {
  background-color: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  padding-bottom: env(safe-area-inset-bottom);
}
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1px solid #eee;
}
.popup-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}
.popup-close {
  font-size: 28rpx;
  color: #999;
  padding: 10rpx;
}
.picker-body {
  padding: 30rpx 20rpx;
}
.picker {
  width: 100%;
  height: 400rpx;
}
.picker-item {
  height: 50px;
  line-height: 50px;
  text-align: center;
  font-size: 28rpx;
  color: #333;
}
.btn-area {
  padding: 20rpx 30rpx 40rpx;
}
.confirm-btn {
  width: 100%;
  background-color: #1890ff;
  color: #fff;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  border-radius: 44rpx;
  font-weight: 500;
}

/* 执行人选择器样式 */
.executor-popup {
  background-color: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  padding-bottom: env(safe-area-inset-bottom);
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}
.executor-body {
  padding: 20rpx 30rpx;
  flex: 1;
}
.executor-search {
  margin-bottom: 20rpx;
}
.search-input {
  width: 100%;
  height: 80rpx;
  border: 1px solid #dcdfe6;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  background-color: #f5f5f5;
}
.executor-list {
  height: 600rpx;
  margin-bottom: 20rpx;
}
.executor-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 10rpx;
  border-bottom: 1px solid #eaeaea;
  transition: background-color 0.3s ease;
}
.executor-item:active {
  background-color: rgba(24, 144, 255, 0.05);
}
.executor-info {
  display: flex;
  align-items: center;
}
.executor-name {
  font-size: 28rpx;
  margin-left: 16rpx;
  color: #333;
}
.executor-dept {
  font-size: 24rpx;
  color: #999;
}
.btn-area {
  padding: 20rpx 30rpx 40rpx;
}
.confirm-btn {
  width: 100%;
  background: linear-gradient(135deg, #1890ff, #096dd9);
  color: #fff;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  border-radius: 44rpx;
  font-weight: 500;
  box-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.3);
}