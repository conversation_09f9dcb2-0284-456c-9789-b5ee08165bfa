"use strict";
const common_vendor = require("../common/vendor.js");
const utils_api = require("./api.js");
let intervalId = null;
let isRunning = false;
const POLLING_INTERVAL = 1e4;
let isPolling = false;
async function fetchMessages(apiPath, messageType) {
  common_vendor.index.__f__("log", "at utils/messageService.js:19", `消息服务：正在调用 ${messageType} 消息接口 (${apiPath})...`);
  try {
    const res = await utils_api.request({
      url: apiPath,
      method: "GET"
    });
    if (res && res.code === 200 && res.data) {
      common_vendor.index.__f__("log", "at utils/messageService.js:31", `消息服务：成功获取 ${messageType} 消息`, res.data);
      if (res.data.length > 0) {
        common_vendor.index.__f__("warn", "at utils/messageService.js:34", `消息服务：收到 ${res.data.length} 条新的 ${messageType} 消息！`);
        common_vendor.index.$emit("newMessageReceived", { type: messageType, data: res.data });
      }
    } else {
      common_vendor.index.__f__("error", "at utils/messageService.js:39", `消息服务：获取 ${messageType} 消息失败`, res);
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/messageService.js:43", `消息服务：调用 ${messageType} 接口 (${apiPath}) 时发生网络错误或异常`, error);
  }
}
async function pollTasks() {
  if (!isRunning || isPolling) {
    if (isPolling)
      common_vendor.index.__f__("log", "at utils/messageService.js:51", "消息服务：上一次轮询仍在进行中，跳过本次执行。");
    return;
  }
  common_vendor.index.__f__("log", "at utils/messageService.js:55", "消息服务：开始执行轮询任务...");
  isPolling = true;
  try {
    await Promise.allSettled([
      fetchMessages("/api/patrols/plans/messages", "巡检"),
      // 3.14 巡检消息
      fetchMessages("/api/alarm/messages", "告警"),
      // 4.5 告警消息
      fetchMessages("/api/faults/messages", "故障"),
      // 4.6 故障消息 
      fetchMessages("/api/WorkOrders/messages", "工单")
      // 5.6 工单消息
    ]);
    common_vendor.index.__f__("log", "at utils/messageService.js:66", "消息服务：轮询任务执行完毕。");
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/messageService.js:69", "消息服务：轮询任务中发生未预料的错误", error);
  } finally {
    isPolling = false;
  }
}
function startService() {
  if (intervalId) {
    common_vendor.index.__f__("log", "at utils/messageService.js:79", "消息服务：检测到可能残留的定时器，正在清除...");
    clearInterval(intervalId);
    intervalId = null;
  }
  if (isRunning) {
    common_vendor.index.__f__("log", "at utils/messageService.js:85", "消息服务：服务已经在运行中。");
    return;
  }
  common_vendor.index.__f__("log", "at utils/messageService.js:88", "消息服务：正在启动...");
  isRunning = true;
  isPolling = false;
  pollTasks();
  intervalId = setInterval(pollTasks, POLLING_INTERVAL);
  common_vendor.index.__f__("log", "at utils/messageService.js:95", "消息服务：已启动，轮询周期", POLLING_INTERVAL / 1e3, "秒");
}
function stopService() {
  if (!isRunning) {
    common_vendor.index.__f__("log", "at utils/messageService.js:101", "消息服务：服务尚未运行。");
    return;
  }
  common_vendor.index.__f__("log", "at utils/messageService.js:104", "消息服务：正在停止...");
  clearInterval(intervalId);
  intervalId = null;
  isRunning = false;
  isPolling = false;
  common_vendor.index.__f__("log", "at utils/messageService.js:109", "消息服务：已停止。");
}
function getServiceStatus() {
  return isRunning;
}
const messageService = {
  start: startService,
  stop: stopService,
  getStatus: getServiceStatus
};
exports.messageService = messageService;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/messageService.js.map
