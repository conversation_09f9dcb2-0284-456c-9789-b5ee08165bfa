{"version": 3, "file": "rules.js", "sources": ["pages/attendance/admin/rules.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYXR0ZW5kYW5jZS9hZG1pbi9ydWxlcy52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"rules-container\">\r\n\t<!-- \t<view class=\"page-header\">\r\n\t\t\t<view class=\"page-title\">考勤规则设置</view>\r\n\t\t</view> -->\r\n\t\t\r\n\t\t<view class=\"rules-form\">\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<view class=\"form-label\">上班时间</view>\r\n\t\t\t\t<view class=\"form-input\">\r\n\t\t\t\t\t<picker mode=\"time\" :value=\"clockInTime\" @change=\"clockInTime = $event.detail.value\">\r\n\t\t\t\t\t\t<view class=\"picker-value\">{{ clockInTime }}</view>\r\n\t\t\t\t\t</picker>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<view class=\"form-label\">下班时间</view>\r\n\t\t\t\t<view class=\"form-input\">\r\n\t\t\t\t\t<picker mode=\"time\" :value=\"clockOutTime\" @change=\"clockOutTime = $event.detail.value\">\r\n\t\t\t\t\t\t<view class=\"picker-value\">{{ clockOutTime }}</view>\r\n\t\t\t\t\t</picker>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-actions\">\r\n\t\t\t\t<button class=\"btn-save\" @click=\"saveRules\" :disabled=\"loading\">保存设置</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { attendanceApi } from '@/utils/api.js';\r\n\t\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tclockInTime: '09:00',\r\n\t\t\t\tclockOutTime: '18:00',\r\n\t\t\t\tallowedDistance: 500,\r\n\t\t\t\tlateThreshold: 15,\r\n\t\t\t\tearlyLeaveThreshold: 15,\r\n\t\t\t\tloading: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t// 获取当前规则设置\r\n\t\t\tthis.getRules();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 获取规则\r\n\t\t\tgetRules() {\r\n\t\t\t\tthis.loading = true;\r\n\t\t\t\tattendanceApi.getClockRules()\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tif (res.code === 200 && res.data) {\r\n\t\t\t\t\t\t\tthis.clockInTime = res.data.clockInTime || '09:00';\r\n\t\t\t\t\t\t\tthis.clockOutTime = res.data.clockOutTime || '18:00';\r\n\t\t\t\t\t\t\tthis.allowedDistance = res.data.allowedDistance || 500;\r\n\t\t\t\t\t\t\tthis.lateThreshold = res.data.lateThreshold || 15;\r\n\t\t\t\t\t\t\tthis.earlyLeaveThreshold = res.data.earlyLeaveThreshold || 15;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tconsole.error('获取考勤规则失败:', err);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '获取规则失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.finally(() => {\r\n\t\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 保存规则\r\n\t\t\tsaveRules() {\r\n\t\t\t\t// 表单验证\r\n\t\t\t\tif (!this.clockInTime) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请设置上班时间',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (!this.clockOutTime) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请设置下班时间',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthis.loading = true;\r\n\t\t\t\t\r\n\t\t\t\t// 调用API保存规则\r\n\t\t\t\tattendanceApi.updateClockRules(this.clockInTime, this.clockOutTime)\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '保存成功',\r\n\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\tuni.navigateBack();\r\n\t\t\t\t\t\t\t}, 1500);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: res.message || '保存失败',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tconsole.error('保存考勤规则失败:', err);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '保存失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.finally(() => {\r\n\t\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.rules-container {\r\n\t\tpadding: 30rpx;\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tmin-height: 100vh;\r\n\t}\r\n\t\r\n\t.page-header {\r\n\t\tmargin-bottom: 40rpx;\r\n\t\t\r\n\t\t.page-title {\r\n\t\t\tfont-size: 36rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tcolor: #333;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.rules-form {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 16rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n\t\t\r\n\t\t.form-item {\r\n\t\t\tmargin-bottom: 30rpx;\r\n\t\t\t\r\n\t\t\t.form-label {\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tmargin-bottom: 16rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.form-input {\r\n\t\t\t\tbackground-color: #f5f7fa;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\tpadding: 20rpx;\r\n\t\t\t\t\r\n\t\t\t\tinput {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 60rpx;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.picker-value {\r\n\t\t\t\t\theight: 60rpx;\r\n\t\t\t\t\tline-height: 60rpx;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.form-desc {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #999;\r\n\t\t\t\tmargin-top: 10rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.form-actions {\r\n\t\t\tmargin-top: 50rpx;\r\n\t\t\t\r\n\t\t\t.btn-save {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 90rpx;\r\n\t\t\t\tbackground: linear-gradient(135deg, #4483e5, #6a9eef);\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tborder-radius: 45rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\t\r\n\t\t\t\t&:disabled {\r\n\t\t\t\t\tbackground: linear-gradient(135deg, #ccc, #999);\r\n\t\t\t\t\topacity: 0.7;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style> ", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/attendance/admin/rules.vue'\nwx.createPage(MiniProgramPage)"], "names": ["attendanceApi", "uni"], "mappings": ";;;AAkCC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,aAAa;AAAA,MACb,cAAc;AAAA,MACd,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,qBAAqB;AAAA,MACrB,SAAS;AAAA,IACV;AAAA,EACA;AAAA,EACD,SAAS;AAER,SAAK,SAAQ;AAAA,EACb;AAAA,EACD,SAAS;AAAA;AAAA,IAER,WAAW;AACV,WAAK,UAAU;AACfA,gBAAAA,cAAc,cAAc,EAC1B,KAAK,SAAO;AACZ,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AACjC,eAAK,cAAc,IAAI,KAAK,eAAe;AAC3C,eAAK,eAAe,IAAI,KAAK,gBAAgB;AAC7C,eAAK,kBAAkB,IAAI,KAAK,mBAAmB;AACnD,eAAK,gBAAgB,IAAI,KAAK,iBAAiB;AAC/C,eAAK,sBAAsB,IAAI,KAAK,uBAAuB;AAAA,QAC5D;AAAA,OACA,EACA,MAAM,SAAO;AACbC,qFAAc,aAAa,GAAG;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,OACD,EACA,QAAQ,MAAM;AACd,aAAK,UAAU;AAAA,MAChB,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,YAAY;AAEX,UAAI,CAAC,KAAK,aAAa;AACtBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,UAAI,CAAC,KAAK,cAAc;AACvBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,WAAK,UAAU;AAGfD,gBAAAA,cAAc,iBAAiB,KAAK,aAAa,KAAK,YAAY,EAChE,KAAK,SAAO;AACZ,YAAI,IAAI,SAAS,KAAK;AACrBC,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AACD,qBAAW,MAAM;AAChBA,0BAAG,MAAC,aAAY;AAAA,UAChB,GAAE,IAAI;AAAA,eACD;AACNA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,IAAI,WAAW;AAAA,YACtB,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,OACA,EACA,MAAM,SAAO;AACbA,sFAAc,aAAa,GAAG;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,OACD,EACA,QAAQ,MAAM;AACd,aAAK,UAAU;AAAA,MAChB,CAAC;AAAA,IACH;AAAA,EACD;AACD;;;;;;;;;;;;;;AC7HD,GAAG,WAAW,eAAe;"}