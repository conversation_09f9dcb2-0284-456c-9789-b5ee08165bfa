/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.fault-report-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}
.form-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.form-header {
  margin-bottom: 20rpx;
}
.form-header .form-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}
.form-header .form-subtitle {
  font-size: 24rpx;
  color: #666;
}
.form-group .form-item {
  margin-bottom: 24rpx;
}
.form-group .form-item:last-child {
  margin-bottom: 0;
}
.form-group .form-item .form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}
.form-group .form-item .form-label.required::before {
  content: "*";
  color: #f5222d;
  margin-right: 4rpx;
}
.form-group .form-item .form-input-container {
  position: relative;
  width: 100%;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  border: 1rpx solid #e5e5e5;
  transition: background-color 0.2s;
}
.form-group .form-item .form-input-container:active {
  background-color: #f0f0f0;
}
.form-group .form-item .form-picker-container {
  background-color: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
  border: 1rpx solid #e5e5e5;
  position: relative;
  overflow: hidden;
  transition: background-color 0.2s;
}
.form-group .form-item .form-picker-container:active {
  background-color: #f0f0f0;
}
.form-group .form-item .form-picker-container::after {
  content: "";
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 12rpx solid transparent;
  border-right: 12rpx solid transparent;
  border-top: 12rpx solid #999;
  pointer-events: none;
}
.form-group .form-item .form-picker {
  width: 100%;
  height: 100%;
  border: none;
  background: transparent;
}
.form-group .form-item .form-picker .picker-text {
  font-size: 28rpx;
  color: #333;
  padding-right: 40rpx;
}
.form-group .form-item .form-picker .picker-text.placeholder {
  color: #808080;
}
.form-group .form-item .form-value {
  font-size: 28rpx;
  color: #333;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}
.form-group .form-item .level-options {
  display: flex;
  justify-content: space-between;
}
.form-group .form-item .level-options .level-option {
  flex: 1;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 10rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
}
.form-group .form-item .level-options .level-option:first-child {
  margin-left: 0;
}
.form-group .form-item .level-options .level-option:last-child {
  margin-right: 0;
}
.form-group .form-item .level-options .level-option.active {
  background-color: #1890ff;
  color: #fff;
}
.form-group .form-item .form-textarea {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  box-sizing: border-box;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  font-size: 28rpx;
  position: relative;
}
.form-group .form-item .textarea-counter {
  position: absolute;
  right: 30rpx;
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #666;
}
.upload-area .image-list {
  display: flex;
  flex-wrap: wrap;
}
.upload-area .image-list .image-item, .upload-area .image-list .upload-item {
  width: 200rpx;
  height: 200rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
}
.upload-area .image-list .image-item image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}
.upload-area .image-list .image-item .delete-icon {
  position: absolute;
  top: -16rpx;
  right: -16rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
}
.upload-area .image-list .upload-item {
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.upload-area .image-list .upload-item .iconfont {
  font-size: 60rpx;
  color: #666;
  margin-bottom: 16rpx;
}
.upload-area .image-list .upload-item text {
  font-size: 24rpx;
  color: #666;
}
.upload-area .video-container {
  width: 100%;
  height: 400rpx;
  position: relative;
}
.upload-area .video-container video {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}
.upload-area .video-container .delete-icon {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
}
.upload-area .video-container .video-actions {
  margin-top: 20rpx;
  display: flex;
  justify-content: center;
}
.upload-area .video-container .video-actions .video-action-btn {
  background-color: #f5f5f5;
  color: #333;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
}
.submit-btn-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.submit-btn-container .submit-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background-color: #1890ff;
  color: #fff;
  font-size: 32rpx;
  border-radius: 8rpx;
}
.form-input {
  background-color: transparent;
  padding: 20rpx;
  width: 100%;
  height: 80rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #333;
  border: none;
}
.form-input-container {
  position: relative;
  width: 100%;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  border: 1rpx solid #e5e5e5;
  transition: background-color 0.2s;
}
.form-input-container:active {
  background-color: #f0f0f0;
}
.address-inputs {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.address-inputs.error {
  border-color: #fa436a;
  background-color: #fff0f0;
  animation: shake 0.5s ease-in-out;
}
.address-input-item {
  position: relative;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  border: 1rpx solid #e5e5e5;
  flex: 1;
  padding-right: 60rpx;
}
.address-input-item.error {
  border-color: #fa436a;
  background-color: #fff0f0;
  animation: shake 0.5s ease-in-out;
}
.address-input-item .form-input {
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}
.address-input-item .address-input-label {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  color: #909399;
}
.room-input {
  flex: 1.5;
}
.address-separator {
  padding: 0 10rpx;
  color: #909399;
  font-size: 30rpx;
  font-weight: bold;
}
.address-hint {
  font-size: 24rpx;
  color: #909399;
  margin-top: 6rpx;
  margin-left: 10rpx;
}
@keyframes shake {
0%, 100% {
    transform: translateX(0);
}
20%, 60% {
    transform: translateX(-5px);
}
40%, 80% {
    transform: translateX(5px);
}
}