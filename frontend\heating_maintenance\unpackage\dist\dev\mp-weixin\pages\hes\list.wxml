<base-tab-bar u-s="{{['d']}}" u-i="584ae98d-0" bind:__l="__l"><view class="hes-list-container"><view class="search-bar"><view class="search-input-wrapper"><text class="iconfont icon-search"></text><input type="text" placeholder="搜索换热站名称/编号" confirm-type="search" bindconfirm="{{a}}" value="{{b}}" bindinput="{{c}}"/><text wx:if="{{d}}" class="iconfont icon-clear" bindtap="{{e}}"></text></view></view><view class="filter-tabs"><view wx:for="{{f}}" wx:for-item="tab" wx:key="b" class="{{['filter-tab', tab.c && 'active']}}" bindtap="{{tab.d}}">{{tab.a}}</view></view><view class="hes-modern-list"><view wx:for="{{g}}" wx:for-item="station" wx:key="m" class="{{['hes-card', station.n]}}" bindtap="{{station.o}}"><view class="card-header"><view class="station-name">{{station.a}} [{{station.b}}]</view><view class="connection-time">{{station.c}}</view></view><view class="operation-mode"> 控制模式：{{station.d}}</view><view class="temperature-info"><view class="network-row"><text class="network-label">一次网:</text><text class="temp-value">{{station.e}}/{{station.f}}°C</text><text class="network-label second-label">二次网:</text><text class="temp-value">{{station.g}}/{{station.h}}°C</text></view></view><view class="card-actions"><view class="{{['status-badge', station.j]}}">{{station.i}}</view><view class="action-buttons"><view class="action-btn control" catchtap="{{station.k}}"> 控制 </view><view class="action-btn detail" catchtap="{{station.l}}"> 详情 </view></view></view></view></view><view wx:if="{{h}}" class="load-more" bindtap="{{i}}">加载更多</view><view wx:else class="no-more">没有更多数据了</view></view></base-tab-bar>