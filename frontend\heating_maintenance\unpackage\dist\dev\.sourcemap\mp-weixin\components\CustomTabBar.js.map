{"version": 3, "file": "CustomTabBar.js", "sources": ["components/CustomTabBar.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RTovdGFpYm9fY29tcGFueS90Yl9wcm9qZWN0L3NoYWFueGlfamllbWluZ19uZXdfZW5lcmd5X2NvbXBhbnkvNC1Tb3VyY2UvYXBwL2Zyb250ZW5kL2hlYXRpbmdfbWFpbnRlbmFuY2UvY29tcG9uZW50cy9DdXN0b21UYWJCYXIudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"custom-tabbar\">\r\n    <view\r\n      v-for=\"(item, index) in filteredTabList\"\r\n      :key=\"index\"\r\n      class=\"tab-item\"\r\n      :class=\"{ active: computedCurrentPath === item.pagePath }\"\r\n      @click=\"switchTab(item.pagePath)\"\r\n    >\r\n      <view class=\"icon-container\">\r\n        <image\r\n          :src=\"\r\n            computedCurrentPath === item.pagePath ? item.selectedIconPath : item.iconPath\r\n          \"\r\n          mode=\"aspectFit\"\r\n          class=\"tab-icon\"\r\n        ></image>\r\n      </view>\r\n      <text class=\"tab-text\">{{ item.text }}</text>\r\n      <view v-if=\"computedCurrentPath === item.pagePath\" class=\"active-indicator\"></view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { hasPermission } from \"@/utils/auth.js\";\r\n\r\nexport default {\r\n  name: \"CustomTabBar\",\r\n  data() {\r\n    return {\r\n      tabList: [\r\n        {\r\n          pagePath: \"/pages/home/<USER>\",\r\n          text: \"首页\",\r\n          iconPath: \"/static/tab/home.png\",\r\n          selectedIconPath: \"/static/tab/home-active.png\",\r\n        },\r\n        {\r\n          pagePath: \"/pages/hes/list\",\r\n          text: \"换热站\",\r\n          iconPath: \"/static/tab/hes.png\",\r\n          selectedIconPath: \"/static/tab/hes-active.png\",\r\n          permissionCode: \"home:hes\",\r\n        },\r\n        {\r\n          pagePath: \"/pages/message/center\",\r\n          text: \"消息\",\r\n          iconPath: \"/static/tab/notification.png\",\r\n          selectedIconPath: \"/static/tab/notification-active.png\",\r\n        },\r\n        {\r\n          pagePath: \"/pages/user/info\",\r\n          text: \"我的\",\r\n          iconPath: \"/static/tab/profile.png\",\r\n          selectedIconPath: \"/static/tab/profile-active.png\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  computed: {\r\n    computedCurrentPath() {\r\n      const pages = getCurrentPages();\r\n      if (pages.length) {\r\n        const currentPage = pages[pages.length - 1];\r\n        return `/${currentPage.route}`;\r\n      }\r\n      return \"\";\r\n    },\r\n    filteredTabList() {\r\n      return this.tabList.filter((item) => {\r\n        return !item.permissionCode || hasPermission(item.permissionCode);\r\n      });\r\n    },\r\n  },\r\n  methods: {\r\n    switchTab(path) {\r\n      if (this.computedCurrentPath === path) return;\r\n      uni.switchTab({\r\n        url: path,\r\n        fail: (err) => {\r\n          console.error(`switchTab 失败: ${JSON.stringify(err)}`);\r\n          uni.navigateTo({ url: path });\r\n        },\r\n      });\r\n    },\r\n  },\r\n  mounted() {\r\n    uni.hideTabBar();\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.custom-tabbar {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 110rpx;\r\n  background: #ffffff;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-around;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.06);\r\n  z-index: 999;\r\n\r\n  .tab-item {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 12rpx 0;\r\n    position: relative;\r\n    transition: all 0.3s;\r\n\r\n    &.active {\r\n      transform: translateY(-6rpx);\r\n\r\n      .tab-text {\r\n        color: #1890ff;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n\r\n    .icon-container {\r\n      width: 60rpx;\r\n      height: 60rpx;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      margin-bottom: 6rpx;\r\n\r\n      .tab-icon {\r\n        width: 48rpx;\r\n        height: 48rpx;\r\n        transition: all 0.3s;\r\n      }\r\n    }\r\n\r\n    .tab-text {\r\n      font-size: 24rpx;\r\n      color: #999;\r\n      line-height: 1;\r\n      transition: all 0.3s;\r\n    }\r\n\r\n    .active-indicator {\r\n      position: absolute;\r\n      bottom: -3rpx;\r\n      left: 50%;\r\n      transform: translateX(-50%);\r\n      width: 16rpx;\r\n      height: 6rpx;\r\n      background: #1890ff;\r\n      border-radius: 6rpx;\r\n    }\r\n  }\r\n}\r\n\r\npage {\r\n  padding-bottom: calc(110rpx + env(safe-area-inset-bottom));\r\n}\r\n</style>\r\n", "import Component from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/components/CustomTabBar.vue'\nwx.createComponent(Component)"], "names": ["hasPermission", "uni"], "mappings": ";;;;AA2BA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA,MACL,SAAS;AAAA,QACP;AAAA,UACE,UAAU;AAAA,UACV,MAAM;AAAA,UACN,UAAU;AAAA,UACV,kBAAkB;AAAA,QACnB;AAAA,QACD;AAAA,UACE,UAAU;AAAA,UACV,MAAM;AAAA,UACN,UAAU;AAAA,UACV,kBAAkB;AAAA,UAClB,gBAAgB;AAAA,QACjB;AAAA,QACD;AAAA,UACE,UAAU;AAAA,UACV,MAAM;AAAA,UACN,UAAU;AAAA,UACV,kBAAkB;AAAA,QACnB;AAAA,QACD;AAAA,UACE,UAAU;AAAA,UACV,MAAM;AAAA,UACN,UAAU;AAAA,UACV,kBAAkB;AAAA,QACnB;AAAA,MACF;AAAA;EAEJ;AAAA,EACD,UAAU;AAAA,IACR,sBAAsB;AACpB,YAAM,QAAQ;AACd,UAAI,MAAM,QAAQ;AAChB,cAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,eAAO,IAAI,YAAY,KAAK;AAAA,MAC9B;AACA,aAAO;AAAA,IACR;AAAA,IACD,kBAAkB;AAChB,aAAO,KAAK,QAAQ,OAAO,CAAC,SAAS;AACnC,eAAO,CAAC,KAAK,kBAAkBA,WAAa,cAAC,KAAK,cAAc;AAAA,MAClE,CAAC;AAAA,IACF;AAAA,EACF;AAAA,EACD,SAAS;AAAA,IACP,UAAU,MAAM;AACd,UAAI,KAAK,wBAAwB;AAAM;AACvCC,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,QACL,MAAM,CAAC,QAAQ;AACbA,wBAAAA,MAAc,MAAA,SAAA,qCAAA,iBAAiB,KAAK,UAAU,GAAG,CAAC,EAAE;AACpDA,wBAAAA,MAAI,WAAW,EAAE,KAAK,KAAM,CAAA;AAAA,QAC7B;AAAA,MACH,CAAC;AAAA,IACF;AAAA,EACF;AAAA,EACD,UAAU;AACRA,kBAAG,MAAC,WAAU;AAAA,EACf;AACH;;;;;;;;;;;;;;;;;ACzFA,GAAG,gBAAgB,SAAS;"}