{"version": 3, "file": "list.js", "sources": ["pages/device/list.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZGV2aWNlL2xpc3QudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"device-container\">\r\n\t\t<!-- 搜索和筛选区域 -->\r\n\t\t<view class=\"search-filter-bar\">\r\n\t\t\t<view class=\"search-box\">\r\n\t\t\t\t<text class=\"iconfont icon-search\"></text>\r\n\t\t\t\t<input type=\"text\" placeholder=\"搜索设备名称或型号\" v-model=\"searchKeyword\" @input=\"handleSearch\" />\r\n\t\t\t\t<text class=\"iconfont icon-clear\" v-if=\"searchKeyword\" @click=\"clearSearch\"></text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"filter-btn\" @click=\"showFilterModal\">\r\n\t\t\t\t<text class=\"iconfont icon-filter\"></text>\r\n\t\t\t\t<text>筛选</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 状态切换区域 -->\r\n\t\t<view class=\"status-tabs\">\r\n\t\t\t<view \r\n\t\t\t\tv-for=\"(tab, index) in statusTabs\" \r\n\t\t\t\t:key=\"index\" \r\n\t\t\t\tclass=\"status-tab\" \r\n\t\t\t\t:class=\"{ active: currentStatus === tab.value }\"\r\n\t\t\t\t@click=\"switchStatus(tab.value)\"\r\n\t\t\t>\r\n\t\t\t\t<text>{{ tab.name }}</text>\r\n\t\t\t\t<text class=\"tab-count\">({{ tab.count }})</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 设备列表 -->\r\n\t\t<scroll-view scroll-y=\"true\" class=\"device-list\" @scrolltolower=\"loadMoreDevices\">\r\n\t\t\t<view v-if=\"deviceList.length > 0\">\r\n\t\t\t\t<view \r\n\t\t\t\t\tv-for=\"device in deviceList\" \r\n\t\t\t\t\t:key=\"device.deviceId\" \r\n\t\t\t\t\tclass=\"device-card\"\r\n\t\t\t\t\t@click=\"navigateToDetail(device.deviceId)\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<view class=\"device-status\" :class=\"device.status\"></view>\r\n\t\t\t\t\t<view class=\"device-info\">\r\n\t\t\t\t\t\t<view class=\"device-name-type\">\r\n\t\t\t\t\t\t\t<text class=\"device-name\">{{ device.name }}</text>\r\n\t\t\t\t\t\t\t<text class=\"device-type\">{{ device.type }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"device-model\">{{ device.model || '未知型号' }}</view>\r\n\t\t\t\t\t\t<view class=\"device-location\">\r\n\t\t\t\t\t\t\t<text class=\"iconfont icon-location\"></text>\r\n\t\t\t\t\t\t\t<text>{{ device.location ? `${device.location.building} ${device.location.room}` : '位置未知' }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"device-metrics\">\r\n\t\t\t\t\t\t<view class=\"metric-item\">\r\n\t\t\t\t\t\t\t<text class=\"metric-label\">上次维护</text>\r\n\t\t\t\t\t\t\t<text class=\"metric-value\">{{ formatDate(device.lastMaintenance) }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"metric-item\">\r\n\t\t\t\t\t\t\t<text class=\"metric-label\">下次维护</text>\r\n\t\t\t\t\t\t\t<text class=\"metric-value\">{{ formatDate(device.nextMaintenance) }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"metric-item\" v-if=\"device.alarmCount > 0\">\r\n\t\t\t\t\t\t\t<text class=\"metric-label\">告警数</text>\r\n\t\t\t\t\t\t\t<text class=\"metric-value alarm\">{{ device.alarmCount }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"device-action\">\r\n\t\t\t\t\t\t<text class=\"iconfont icon-detail\"></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 加载更多 -->\r\n\t\t\t\t<view class=\"loading-more\" v-if=\"isLoading\">\r\n\t\t\t\t\t<text>加载中...</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"no-more\" v-if=\"noMoreData && !isLoading\">\r\n\t\t\t\t\t<text>没有更多数据了</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 无数据提示 -->\r\n\t\t\t<view class=\"empty-list\" v-else>\r\n\t\t\t\t<image src=\"/static/images/empty.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t<text>暂无设备数据</text>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\t\t\r\n\t\t<!-- 筛选弹窗 -->\r\n\t\t<uni-popup ref=\"filterPopup\" type=\"bottom\">\r\n\t\t\t<view class=\"filter-modal\">\r\n\t\t\t\t<view class=\"filter-header\">\r\n\t\t\t\t\t<text class=\"filter-title\">筛选条件</text>\r\n\t\t\t\t\t<text class=\"filter-reset\" @click=\"resetFilter\">重置</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"filter-content\">\r\n\t\t\t\t\t<!-- 设备类型筛选 -->\r\n\t\t\t\t\t<view class=\"filter-section\">\r\n\t\t\t\t\t\t<text class=\"filter-section-title\">设备类型</text>\r\n\t\t\t\t\t\t<view class=\"filter-options\">\r\n\t\t\t\t\t\t\t<view \r\n\t\t\t\t\t\t\t\tv-for=\"(type, index) in deviceTypes\" \r\n\t\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\t\tclass=\"filter-option\" \r\n\t\t\t\t\t\t\t\t:class=\"{ selected: filterOptions.type === type.value }\"\r\n\t\t\t\t\t\t\t\t@click=\"selectFilterOption('type', type.value)\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{{ type.name }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 设备区域筛选 -->\r\n\t\t\t\t\t<view class=\"filter-section\">\r\n\t\t\t\t\t\t<text class=\"filter-section-title\">设备区域</text>\r\n\t\t\t\t\t\t<view class=\"filter-options\">\r\n\t\t\t\t\t\t\t<view \r\n\t\t\t\t\t\t\t\tv-for=\"(area, index) in deviceAreas\" \r\n\t\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\t\tclass=\"filter-option\" \r\n\t\t\t\t\t\t\t\t:class=\"{ selected: filterOptions.area === area.value }\"\r\n\t\t\t\t\t\t\t\t@click=\"selectFilterOption('area', area.value)\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{{ area.name }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 排序方式 -->\r\n\t\t\t\t\t<view class=\"filter-section\">\r\n\t\t\t\t\t\t<text class=\"filter-section-title\">排序方式</text>\r\n\t\t\t\t\t\t<view class=\"filter-options\">\r\n\t\t\t\t\t\t\t<view \r\n\t\t\t\t\t\t\t\tv-for=\"(sort, index) in sortOptions\" \r\n\t\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\t\tclass=\"filter-option\" \r\n\t\t\t\t\t\t\t\t:class=\"{ selected: filterOptions.sortBy === sort.value }\"\r\n\t\t\t\t\t\t\t\t@click=\"selectFilterOption('sortBy', sort.value)\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{{ sort.name }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"filter-footer\">\r\n\t\t\t\t\t<button class=\"btn-cancel\" @click=\"cancelFilter\">取消</button>\r\n\t\t\t\t\t<button class=\"btn-apply\" @click=\"applyFilter\">确认</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t\t\r\n\t\t<!-- 悬浮添加按钮 -->\r\n\t\t<view class=\"fab-button\" @click=\"navigateToAdd\">\r\n\t\t\t<text class=\"iconfont icon-add\"></text>\r\n\t\t</view> \r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport CustomTabBar from '@/components/CustomTabBar.vue';\r\n\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tCustomTabBar\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tsearchKeyword: '',\r\n\t\t\t\tcurrentStatus: 'all',\r\n\t\t\t\tstatusTabs: [\r\n\t\t\t\t\t{ name: '全部', value: 'all', count: 0 },\r\n\t\t\t\t\t{ name: '在线', value: 'online', count: 0 },\r\n\t\t\t\t\t{ name: '离线', value: 'offline', count: 0 },\r\n\t\t\t\t\t{ name: '故障', value: 'fault', count: 0 }\r\n\t\t\t\t],\r\n\t\t\t\tdeviceList: [],\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tpageSize: 10,\r\n\t\t\t\tisLoading: false,\r\n\t\t\t\tnoMoreData: false,\r\n\t\t\t\tfilterOptions: {\r\n\t\t\t\t\ttype: '',\r\n\t\t\t\t\tarea: '',\r\n\t\t\t\t\tsortBy: 'installTime',\r\n\t\t\t\t\tsortOrder: 'desc'\r\n\t\t\t\t},\r\n\t\t\t\tdeviceTypes: [\r\n\t\t\t\t\t{ name: '全部', value: '' },\r\n\t\t\t\t\t{ name: '泵类', value: 'pump' },\r\n\t\t\t\t\t{ name: '阀门', value: 'valve' },\r\n\t\t\t\t\t{ name: '传感器', value: 'sensor' },\r\n\t\t\t\t\t{ name: '控制器', value: 'controller' }\r\n\t\t\t\t],\r\n\t\t\t\tdeviceAreas: [\r\n\t\t\t\t\t{ name: '全部', value: '' },\r\n\t\t\t\t\t{ name: '东区', value: 'east' },\r\n\t\t\t\t\t{ name: '西区', value: 'west' },\r\n\t\t\t\t\t{ name: '南区', value: 'south' },\r\n\t\t\t\t\t{ name: '北区', value: 'north' }\r\n\t\t\t\t],\r\n\t\t\t\tsortOptions: [\r\n\t\t\t\t\t{ name: '安装时间', value: 'installTime' },\r\n\t\t\t\t\t{ name: '维护周期', value: 'maintainPeriod' },\r\n\t\t\t\t\t{ name: '告警数量', value: 'alarmCount' }\r\n\t\t\t\t]\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.loadDeviceList();\r\n\t\t\tthis.loadDeviceStats();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 加载设备列表\r\n\t\t\tloadDeviceList(append = false) {\r\n\t\t\t\tif (this.isLoading) return;\r\n\t\t\t\t\r\n\t\t\t\tthis.isLoading = true;\r\n\t\t\t\t\r\n\t\t\t\t// 构造请求参数\r\n\t\t\t\tconst params = {\r\n\t\t\t\t\tpage: this.page,\r\n\t\t\t\t\tpageSize: this.pageSize,\r\n\t\t\t\t\tstatus: this.currentStatus === 'all' ? '' : this.currentStatus,\r\n\t\t\t\t\tkeyword: this.searchKeyword,\r\n\t\t\t\t\t...this.filterOptions\r\n\t\t\t\t};\r\n\t\t\t\t\r\n\t\t\t\t// 模拟API调用\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t// 实际项目中应该调用API\r\n\t\t\t\t\t// uni.request({\r\n\t\t\t\t\t//     url: '/api/devices/list',\r\n\t\t\t\t\t//     method: 'POST',\r\n\t\t\t\t\t//     data: params,\r\n\t\t\t\t\t//     success: (res) => {\r\n\t\t\t\t\t//         // 处理返回的数据\r\n\t\t\t\t\t//     }\r\n\t\t\t\t\t// });\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 模拟数据\r\n\t\t\t\t\tconst mockData = this.getMockDeviceList();\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 更新设备列表\r\n\t\t\t\t\tif (append) {\r\n\t\t\t\t\t\tthis.deviceList = [...this.deviceList, ...mockData];\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.deviceList = mockData;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 是否还有更多数据\r\n\t\t\t\t\tthis.noMoreData = mockData.length < this.pageSize;\r\n\t\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t}, 500);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 加载设备统计\r\n\t\t\tloadDeviceStats() {\r\n\t\t\t\t// 模拟API调用\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t// 实际项目中应该调用API\r\n\t\t\t\t\t// uni.request({\r\n\t\t\t\t\t//     url: '/api/devices/stats',\r\n\t\t\t\t\t//     method: 'GET',\r\n\t\t\t\t\t//     success: (res) => {\r\n\t\t\t\t\t//         // 处理返回的数据\r\n\t\t\t\t\t//     }\r\n\t\t\t\t\t// });\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 模拟数据\r\n\t\t\t\t\tconst stats = {\r\n\t\t\t\t\t\ttotal: 10,\r\n\t\t\t\t\t\tonline: 5,\r\n\t\t\t\t\t\toffline: 3,\r\n\t\t\t\t\t\tfault: 2\r\n\t\t\t\t\t};\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 更新选项卡数量\r\n\t\t\t\t\tthis.statusTabs[0].count = stats.total;\r\n\t\t\t\t\tthis.statusTabs[1].count = stats.online;\r\n\t\t\t\t\tthis.statusTabs[2].count = stats.offline;\r\n\t\t\t\t\tthis.statusTabs[3].count = stats.fault;\r\n\t\t\t\t}, 300);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 搜索设备\r\n\t\t\thandleSearch() {\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.noMoreData = false;\r\n\t\t\t\tthis.loadDeviceList();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 清除搜索\r\n\t\t\tclearSearch() {\r\n\t\t\t\tthis.searchKeyword = '';\r\n\t\t\t\tthis.handleSearch();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 切换状态选项卡\r\n\t\t\tswitchStatus(status) {\r\n\t\t\t\tif (this.currentStatus === status) return;\r\n\t\t\t\t\r\n\t\t\t\tthis.currentStatus = status;\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.noMoreData = false;\r\n\t\t\t\tthis.loadDeviceList();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 加载更多设备\r\n\t\t\tloadMoreDevices() {\r\n\t\t\t\tif (this.isLoading || this.noMoreData) return;\r\n\t\t\t\t\r\n\t\t\t\tthis.page++;\r\n\t\t\t\tthis.loadDeviceList(true);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 显示筛选弹窗\r\n\t\t\tshowFilterModal() {\r\n\t\t\t\tthis.$refs.filterPopup.open();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 选择筛选选项\r\n\t\t\tselectFilterOption(field, value) {\r\n\t\t\t\tthis.filterOptions[field] = value;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 重置筛选条件\r\n\t\t\tresetFilter() {\r\n\t\t\t\tthis.filterOptions = {\r\n\t\t\t\t\ttype: '',\r\n\t\t\t\t\tarea: '',\r\n\t\t\t\t\tsortBy: 'installTime',\r\n\t\t\t\t\tsortOrder: 'desc'\r\n\t\t\t\t};\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 取消筛选\r\n\t\t\tcancelFilter() {\r\n\t\t\t\tthis.$refs.filterPopup.close();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 应用筛选\r\n\t\t\tapplyFilter() {\r\n\t\t\t\tthis.page = 1;\r\n\t\t\t\tthis.noMoreData = false;\r\n\t\t\t\tthis.loadDeviceList();\r\n\t\t\t\tthis.$refs.filterPopup.close();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 跳转到设备详情\r\n\t\t\tnavigateToDetail(deviceId) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/device/detail?id=${deviceId}`\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 跳转到添加设备\r\n\t\t\tnavigateToAdd() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/device/add'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 格式化日期\r\n\t\t\tformatDate(dateString) {\r\n\t\t\t\tif (!dateString) return '未知';\r\n\t\t\t\t\r\n\t\t\t\tconst date = new Date(dateString);\r\n\t\t\t\treturn `${date.getMonth() + 1}月${date.getDate()}日`;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取模拟设备列表\r\n\t\t\tgetMockDeviceList() {\r\n\t\t\t\treturn [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tdeviceId: \"dev_001\",\r\n\t\t\t\t\t\tname: \"1号循环泵\",\r\n\t\t\t\t\t\ttype: \"pump\",\r\n\t\t\t\t\t\tmodel: \"XGZ-2023\",\r\n\t\t\t\t\t\tstatus: \"online\",\r\n\t\t\t\t\t\tlocation: {\r\n\t\t\t\t\t\t\tbuilding: \"3号楼\",\r\n\t\t\t\t\t\t\tfloor: \"1层\",\r\n\t\t\t\t\t\t\troom: \"泵房\",\r\n\t\t\t\t\t\t\tcoordinates: {\r\n\t\t\t\t\t\t\t\tlat: 39.904200,\r\n\t\t\t\t\t\t\t\tlng: 116.407400\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tlastMaintenance: \"2023-12-01 10:15:00\",\r\n\t\t\t\t\t\tnextMaintenance: \"2023-12-16 10:15:00\",\r\n\t\t\t\t\t\talarmCount: 0\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tdeviceId: \"dev_002\",\r\n\t\t\t\t\t\tname: \"2号阀门\",\r\n\t\t\t\t\t\ttype: \"valve\",\r\n\t\t\t\t\t\tmodel: \"DFV-2022\",\r\n\t\t\t\t\t\tstatus: \"online\",\r\n\t\t\t\t\t\tlocation: {\r\n\t\t\t\t\t\t\tbuilding: \"3号楼\",\r\n\t\t\t\t\t\t\tfloor: \"1层\",\r\n\t\t\t\t\t\t\troom: \"阀门间\",\r\n\t\t\t\t\t\t\tcoordinates: {\r\n\t\t\t\t\t\t\t\tlat: 39.904300,\r\n\t\t\t\t\t\t\t\tlng: 116.407500\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tlastMaintenance: \"2023-12-02 22:30:00\",\r\n\t\t\t\t\t\tnextMaintenance: \"2023-12-16 22:30:00\",\r\n\t\t\t\t\t\talarmCount: 0\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tdeviceId: \"dev_003\",\r\n\t\t\t\t\t\tname: \"3号控制器\",\r\n\t\t\t\t\t\ttype: \"controller\",\r\n\t\t\t\t\t\tmodel: \"CTL-2023\",\r\n\t\t\t\t\t\tstatus: \"fault\",\r\n\t\t\t\t\t\tlocation: {\r\n\t\t\t\t\t\t\tbuilding: \"4号楼\",\r\n\t\t\t\t\t\t\tfloor: \"1层\",\r\n\t\t\t\t\t\t\troom: \"控制室\",\r\n\t\t\t\t\t\t\tcoordinates: {\r\n\t\t\t\t\t\t\t\tlat: 39.904400,\r\n\t\t\t\t\t\t\t\tlng: 116.407600\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tlastMaintenance: \"2023-12-03 14:20:00\",\r\n\t\t\t\t\t\tnextMaintenance: \"2023-12-17 14:20:00\",\r\n\t\t\t\t\t\talarmCount: 2\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tdeviceId: \"dev_004\",\r\n\t\t\t\t\t\tname: \"温度传感器\",\r\n\t\t\t\t\t\ttype: \"sensor\",\r\n\t\t\t\t\t\tmodel: \"TSR-100\",\r\n\t\t\t\t\t\tstatus: \"offline\",\r\n\t\t\t\t\t\tlocation: {\r\n\t\t\t\t\t\t\tbuilding: \"4号楼\",\r\n\t\t\t\t\t\t\tfloor: \"2层\",\r\n\t\t\t\t\t\t\troom: \"机房\",\r\n\t\t\t\t\t\t\tcoordinates: {\r\n\t\t\t\t\t\t\t\tlat: 39.904500,\r\n\t\t\t\t\t\t\t\tlng: 116.407700\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tlastMaintenance: \"2023-12-04 09:10:00\",\r\n\t\t\t\t\t\tnextMaintenance: \"2023-12-18 09:10:00\",\r\n\t\t\t\t\t\talarmCount: 0\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tdeviceId: \"dev_005\",\r\n\t\t\t\t\t\tname: \"3号阀门\",\r\n\t\t\t\t\t\ttype: \"valve\",\r\n\t\t\t\t\t\tmodel: \"DFV-2022\",\r\n\t\t\t\t\t\tstatus: \"online\",\r\n\t\t\t\t\t\tlocation: {\r\n\t\t\t\t\t\t\tbuilding: \"5号楼\",\r\n\t\t\t\t\t\t\tfloor: \"1层\",\r\n\t\t\t\t\t\t\troom: \"阀门间\",\r\n\t\t\t\t\t\t\tcoordinates: {\r\n\t\t\t\t\t\t\t\tlat: 39.904600,\r\n\t\t\t\t\t\t\t\tlng: 116.407800\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tlastMaintenance: \"2023-12-05 19:20:00\",\r\n\t\t\t\t\t\tnextMaintenance: \"2023-12-19 19:20:00\",\r\n\t\t\t\t\t\talarmCount: 0\r\n\t\t\t\t\t}\r\n\t\t\t\t];\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.device-container {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\theight: 100vh;\r\n\t\tbackground-color: #f5f5f5;\r\n\t}\r\n\t\r\n\t.search-filter-bar {\r\n\t\tdisplay: flex;\r\n\t\tpadding: 20rpx 30rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\r\n\t\t\r\n\t\t.search-box {\r\n\t\t\tflex: 1;\r\n\t\t\theight: 70rpx;\r\n\t\t\tbackground-color: #f5f5f5;\r\n\t\t\tborder-radius: 35rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 0 20rpx;\r\n\t\t\tmargin-right: 20rpx;\r\n\t\t\t\r\n\t\t\t.iconfont {\r\n\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\tcolor: #999;\r\n\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tinput {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\theight: 70rpx;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.filter-btn {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 0 20rpx;\r\n\t\t\t\r\n\t\t\t.iconfont {\r\n\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t\tmargin-right: 6rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\ttext {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.status-tabs {\r\n\t\tdisplay: flex;\r\n\t\tbackground-color: #fff;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\t\r\n\t\t.status-tab {\r\n\t\t\tflex: 1;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\theight: 80rpx;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: $uni-text-color;\r\n\t\t\tposition: relative;\r\n\t\t\t\r\n\t\t\t&.active {\r\n\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\r\n\t\t\t\t&::after {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\ttransform: translateX(-50%);\r\n\t\t\t\t\twidth: 40rpx;\r\n\t\t\t\t\theight: 4rpx;\r\n\t\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\t\tborder-radius: 2rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.tab-count {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\tmargin-left: 4rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.device-list {\r\n\t\tflex: 1;\r\n\t\tpadding: 0 30rpx;\r\n\t\t\r\n\t\t.device-card {\r\n\t\t\tdisplay: flex;\r\n\t\t\tposition: relative;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t\tpadding: 20rpx;\r\n\t\t\tbox-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\r\n\t\t\t\r\n\t\t\t.device-status {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: 20rpx;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\twidth: 8rpx;\r\n\t\t\t\theight: 36rpx;\r\n\t\t\t\tborder-radius: 0 4rpx 4rpx 0;\r\n\t\t\t\t\r\n\t\t\t\t&.online {\r\n\t\t\t\t\tbackground-color: $uni-color-success;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&.offline {\r\n\t\t\t\t\tbackground-color: $uni-text-color-grey;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&.fault {\r\n\t\t\t\t\tbackground-color: $uni-color-error;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.device-info {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tpadding-left: 16rpx;\r\n\t\t\t\t\r\n\t\t\t\t.device-name-type {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.device-name {\r\n\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.device-type {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\t\t\tpadding: 4rpx 12rpx;\r\n\t\t\t\t\t\tborder-radius: 4rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.device-model {\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.device-location {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.iconfont {\r\n\t\t\t\t\t\tmargin-right: 6rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.device-metrics {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\r\n\t\t\t\t.metric-item {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tmargin-bottom: 6rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.metric-label {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.metric-value {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&.alarm {\r\n\t\t\t\t\t\t\tcolor: $uni-color-error;\r\n\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.device-action {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\t\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 40rpx;\r\n\t\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.loading-more, .no-more {\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\tpadding: 20rpx 0;\r\n\t\t}\r\n\t\t\r\n\t\t.empty-list {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding-top: 200rpx;\r\n\t\t\t\r\n\t\t\timage {\r\n\t\t\t\twidth: 200rpx;\r\n\t\t\t\theight: 200rpx;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\ttext {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.filter-modal {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 16rpx 16rpx 0 0;\r\n\t\toverflow: hidden;\r\n\t\t\r\n\t\t.filter-header {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tborder-bottom: 1rpx solid #eee;\r\n\t\t\t\r\n\t\t\t.filter-title {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.filter-reset {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.filter-content {\r\n\t\t\tpadding: 20rpx 30rpx;\r\n\t\t\tmax-height: 60vh;\r\n\t\t\toverflow-y: auto;\r\n\t\t\t\r\n\t\t\t.filter-section {\r\n\t\t\t\tmargin-bottom: 30rpx;\r\n\t\t\t\t\r\n\t\t\t\t.filter-section-title {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.filter-options {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-wrap: wrap;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.filter-option {\r\n\t\t\t\t\t\tpadding: 10rpx 30rpx;\r\n\t\t\t\t\t\tbackground-color: #f5f5f5;\r\n\t\t\t\t\t\tborder-radius: 30rpx;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\t\tmargin-bottom: 16rpx;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&.selected {\r\n\t\t\t\t\t\t\tbackground-color: rgba(24, 144, 255, 0.1);\r\n\t\t\t\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.filter-footer {\r\n\t\t\tdisplay: flex;\r\n\t\t\tpadding: 20rpx;\r\n\t\t\tborder-top: 1rpx solid #eee;\r\n\t\t\t\r\n\t\t\tbutton {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\theight: 80rpx;\r\n\t\t\t\tline-height: 80rpx;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tborder-radius: 40rpx;\r\n\t\t\t\t\r\n\t\t\t\t&::after {\r\n\t\t\t\t\tborder: none;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.btn-cancel {\r\n\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\tbackground-color: #f5f5f5;\r\n\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.btn-apply {\r\n\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.fab-button {\r\n\t\tposition: fixed;\r\n\t\tright: 30rpx;\r\n\t\tbottom: 30rpx;\r\n\t\twidth: 100rpx;\r\n\t\theight: 100rpx;\r\n\t\tbackground-color: $uni-color-primary;\r\n\t\tborder-radius: 50%;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tbox-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.3);\r\n\t\t\r\n\t\t.iconfont {\r\n\t\t\tfont-size: 48rpx;\r\n\t\t\tcolor: #fff;\r\n\t\t}\r\n\t}\r\n</style> ", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/device/list.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AA8JC,qBAAqB,MAAW;AAEhC,MAAK,YAAU;AAAA,EACd,YAAY;AAAA,IACX;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,eAAe;AAAA,MACf,eAAe;AAAA,MACf,YAAY;AAAA,QACX,EAAE,MAAM,MAAM,OAAO,OAAO,OAAO,EAAG;AAAA,QACtC,EAAE,MAAM,MAAM,OAAO,UAAU,OAAO,EAAG;AAAA,QACzC,EAAE,MAAM,MAAM,OAAO,WAAW,OAAO,EAAG;AAAA,QAC1C,EAAE,MAAM,MAAM,OAAO,SAAS,OAAO,EAAE;AAAA,MACvC;AAAA,MACD,YAAY,CAAE;AAAA,MACd,MAAM;AAAA,MACN,UAAU;AAAA,MACV,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,eAAe;AAAA,QACd,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,WAAW;AAAA,MACX;AAAA,MACD,aAAa;AAAA,QACZ,EAAE,MAAM,MAAM,OAAO,GAAI;AAAA,QACzB,EAAE,MAAM,MAAM,OAAO,OAAQ;AAAA,QAC7B,EAAE,MAAM,MAAM,OAAO,QAAS;AAAA,QAC9B,EAAE,MAAM,OAAO,OAAO,SAAU;AAAA,QAChC,EAAE,MAAM,OAAO,OAAO,aAAa;AAAA,MACnC;AAAA,MACD,aAAa;AAAA,QACZ,EAAE,MAAM,MAAM,OAAO,GAAI;AAAA,QACzB,EAAE,MAAM,MAAM,OAAO,OAAQ;AAAA,QAC7B,EAAE,MAAM,MAAM,OAAO,OAAQ;AAAA,QAC7B,EAAE,MAAM,MAAM,OAAO,QAAS;AAAA,QAC9B,EAAE,MAAM,MAAM,OAAO,QAAQ;AAAA,MAC7B;AAAA,MACD,aAAa;AAAA,QACZ,EAAE,MAAM,QAAQ,OAAO,cAAe;AAAA,QACtC,EAAE,MAAM,QAAQ,OAAO,iBAAkB;AAAA,QACzC,EAAE,MAAM,QAAQ,OAAO,aAAa;AAAA,MACrC;AAAA,IACD;AAAA,EACA;AAAA,EACD,SAAS;AACR,SAAK,eAAc;AACnB,SAAK,gBAAe;AAAA,EACpB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,eAAe,SAAS,OAAO;AAC9B,UAAI,KAAK;AAAW;AAEpB,WAAK,YAAY;AAGF,OAAA;AAAA,QACd,MAAM,KAAK;AAAA,QACX,UAAU,KAAK;AAAA,QACf,QAAQ,KAAK,kBAAkB,QAAQ,KAAK,KAAK;AAAA,QACjD,SAAS,KAAK;AAAA,QACd,GAAG,KAAK;AAAA,MACR;AAGD,iBAAW,MAAM;AAYhB,cAAM,WAAW,KAAK;AAGtB,YAAI,QAAQ;AACX,eAAK,aAAa,CAAC,GAAG,KAAK,YAAY,GAAG,QAAQ;AAAA,eAC5C;AACN,eAAK,aAAa;AAAA,QACnB;AAGA,aAAK,aAAa,SAAS,SAAS,KAAK;AACzC,aAAK,YAAY;AAAA,MACjB,GAAE,GAAG;AAAA,IACN;AAAA;AAAA,IAGD,kBAAkB;AAEjB,iBAAW,MAAM;AAWhB,cAAM,QAAQ;AAAA,UACb,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,OAAO;AAAA;AAIR,aAAK,WAAW,CAAC,EAAE,QAAQ,MAAM;AACjC,aAAK,WAAW,CAAC,EAAE,QAAQ,MAAM;AACjC,aAAK,WAAW,CAAC,EAAE,QAAQ,MAAM;AACjC,aAAK,WAAW,CAAC,EAAE,QAAQ,MAAM;AAAA,MACjC,GAAE,GAAG;AAAA,IACN;AAAA;AAAA,IAGD,eAAe;AACd,WAAK,OAAO;AACZ,WAAK,aAAa;AAClB,WAAK,eAAc;AAAA,IACnB;AAAA;AAAA,IAGD,cAAc;AACb,WAAK,gBAAgB;AACrB,WAAK,aAAY;AAAA,IACjB;AAAA;AAAA,IAGD,aAAa,QAAQ;AACpB,UAAI,KAAK,kBAAkB;AAAQ;AAEnC,WAAK,gBAAgB;AACrB,WAAK,OAAO;AACZ,WAAK,aAAa;AAClB,WAAK,eAAc;AAAA,IACnB;AAAA;AAAA,IAGD,kBAAkB;AACjB,UAAI,KAAK,aAAa,KAAK;AAAY;AAEvC,WAAK;AACL,WAAK,eAAe,IAAI;AAAA,IACxB;AAAA;AAAA,IAGD,kBAAkB;AACjB,WAAK,MAAM,YAAY;IACvB;AAAA;AAAA,IAGD,mBAAmB,OAAO,OAAO;AAChC,WAAK,cAAc,KAAK,IAAI;AAAA,IAC5B;AAAA;AAAA,IAGD,cAAc;AACb,WAAK,gBAAgB;AAAA,QACpB,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,WAAW;AAAA;IAEZ;AAAA;AAAA,IAGD,eAAe;AACd,WAAK,MAAM,YAAY;IACvB;AAAA;AAAA,IAGD,cAAc;AACb,WAAK,OAAO;AACZ,WAAK,aAAa;AAClB,WAAK,eAAc;AACnB,WAAK,MAAM,YAAY;IACvB;AAAA;AAAA,IAGD,iBAAiB,UAAU;AAC1BA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,2BAA2B,QAAQ;AAAA,MACzC,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB;AACfA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,YAAY;AACtB,UAAI,CAAC;AAAY,eAAO;AAExB,YAAM,OAAO,IAAI,KAAK,UAAU;AAChC,aAAO,GAAG,KAAK,aAAa,CAAC,IAAI,KAAK,SAAS;AAAA,IAC/C;AAAA;AAAA,IAGD,oBAAoB;AACnB,aAAO;AAAA,QACN;AAAA,UACC,UAAU;AAAA,UACV,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,UAAU;AAAA,YACT,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,YACN,aAAa;AAAA,cACZ,KAAK;AAAA,cACL,KAAK;AAAA,YACN;AAAA,UACA;AAAA,UACD,iBAAiB;AAAA,UACjB,iBAAiB;AAAA,UACjB,YAAY;AAAA,QACZ;AAAA,QACD;AAAA,UACC,UAAU;AAAA,UACV,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,UAAU;AAAA,YACT,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,YACN,aAAa;AAAA,cACZ,KAAK;AAAA,cACL,KAAK;AAAA,YACN;AAAA,UACA;AAAA,UACD,iBAAiB;AAAA,UACjB,iBAAiB;AAAA,UACjB,YAAY;AAAA,QACZ;AAAA,QACD;AAAA,UACC,UAAU;AAAA,UACV,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,UAAU;AAAA,YACT,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,YACN,aAAa;AAAA,cACZ,KAAK;AAAA,cACL,KAAK;AAAA,YACN;AAAA,UACA;AAAA,UACD,iBAAiB;AAAA,UACjB,iBAAiB;AAAA,UACjB,YAAY;AAAA,QACZ;AAAA,QACD;AAAA,UACC,UAAU;AAAA,UACV,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,UAAU;AAAA,YACT,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,YACN,aAAa;AAAA,cACZ,KAAK;AAAA,cACL,KAAK;AAAA,YACN;AAAA,UACA;AAAA,UACD,iBAAiB;AAAA,UACjB,iBAAiB;AAAA,UACjB,YAAY;AAAA,QACZ;AAAA,QACD;AAAA,UACC,UAAU;AAAA,UACV,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,UAAU;AAAA,YACT,UAAU;AAAA,YACV,OAAO;AAAA,YACP,MAAM;AAAA,YACN,aAAa;AAAA,cACZ,KAAK;AAAA,cACL,KAAK;AAAA,YACN;AAAA,UACA;AAAA,UACD,iBAAiB;AAAA,UACjB,iBAAiB;AAAA,UACjB,YAAY;AAAA,QACb;AAAA;IAEF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrdD,GAAG,WAAW,eAAe;"}