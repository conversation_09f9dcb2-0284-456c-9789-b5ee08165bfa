"use strict";
const common_vendor = require("../common/vendor.js");
function getStoredUserPermissions() {
  const permissions = common_vendor.index.getStorageSync("userPermissions");
  if (Array.isArray(permissions)) {
    return permissions;
  }
  return [];
}
function getToken() {
  return common_vendor.index.getStorageSync("token") || "";
}
function hasPermission(requiredPermissionCode) {
  if (!requiredPermissionCode) {
    return true;
  }
  const userPermissions = getStoredUserPermissions();
  return userPermissions.includes(requiredPermissionCode);
}
function checkAccess(requiredPermissionCode, redirectUnauthorized = true) {
  if (!hasPermission(requiredPermissionCode)) {
    const userPermissions = getStoredUserPermissions();
    common_vendor.index.__f__("warn", "at utils/auth.js:103", `权限不足: 需要权限 '${requiredPermissionCode}', 用户当前权限: ${JSON.stringify(userPermissions)}`);
    common_vendor.index.showToast({
      title: "您没有足够的权限访问此页面或执行此操作。",
      icon: "none",
      duration: 2500
    });
    if (redirectUnauthorized) {
      setTimeout(() => {
        common_vendor.index.reLaunch({
          url: "/pages/user/login"
          // 修改为您的登录页路径
          // 或者跳转到自定义的无权限页面: url: '/pages/common/no-permission'
        });
      }, 2500);
    }
    return false;
  }
  return true;
}
function guardPageAccess(requiredPermissionCode) {
  if (!checkAccess(requiredPermissionCode, true)) {
    return false;
  }
  return true;
}
exports.getToken = getToken;
exports.guardPageAccess = guardPageAccess;
exports.hasPermission = hasPermission;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/auth.js.map
