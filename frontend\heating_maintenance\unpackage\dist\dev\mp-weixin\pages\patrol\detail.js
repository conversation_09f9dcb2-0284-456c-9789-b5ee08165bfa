"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const _sfc_main = {
  data() {
    return {
      planId: "",
      planInfo: {
        id: "",
        title: "",
        code: "",
        type: "",
        startTime: "",
        endTime: "",
        location: "",
        manager: "",
        progress: "",
        description: "",
        status: "pending"
      },
      planTasks: [],
      // stats: {
      // 	totalTasks: 0,
      // 	completedTasks: 0,
      // 	abnormalCount: 0,
      // 	completionRate: 0
      // },
      isLoading: false,
      // 是否为巡检记录详情
      isRecord: false
    };
  },
  computed: {
    progressPercent() {
      if (!this.planInfo.progress)
        return 0;
      const progressParts = this.planInfo.progress.split("/");
      if (progressParts.length !== 2)
        return 0;
      const completed = parseInt(progressParts[0]);
      const total = parseInt(progressParts[1]);
      if (isNaN(completed) || isNaN(total) || total === 0)
        return 0;
      return Math.floor(completed / total * 100);
    }
  },
  onLoad(options) {
    this.planId = options.id;
    this.isRecord = options.type === "record";
    if (this.isRecord) {
      this.loadPatrolResultDetail();
    } else {
      this.loadPlanDetail();
    }
  },
  methods: {
    // 加载巡检结果详情
    loadPatrolResultDetail() {
      this.isLoading = true;
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      utils_api.patrolApi.getPatrolResultDetail(this.planId).then((res) => {
        if (res.code === 200 && res.data) {
          const data = res.data;
          const recordInfo = data.recordInfo;
          this.planInfo = {
            id: recordInfo.id,
            title: recordInfo.planName,
            code: `REC-${recordInfo.id}`,
            type: "巡检记录",
            startTime: recordInfo.startTime,
            endTime: recordInfo.endTime,
            location: recordInfo.locations,
            manager: recordInfo.executorName || "未分配",
            description: recordInfo.remark || "",
            status: recordInfo.status,
            scheduleType: recordInfo.scheduleType,
            scheduleWeekDays: recordInfo.scheduleWeekDays,
            scheduleMonthDays: recordInfo.scheduleMonthDays
          };
          if (data.resultList && data.resultList.length > 0) {
            this.planTasks = data.resultList.map((result) => ({
              id: result.id,
              title: result.itemName,
              target: result.deviceName || "",
              point: result.categoryName || "",
              content: result.description || "",
              standard: result.normalRange || "",
              unit: result.unit || "",
              actualValue: result.paramValue || "",
              isAbnormal: result.checkResult === "abnormal",
              completedTime: this.formatDateTime(result.createTime),
              remark: result.description || "",
              status: "completed",
              // 巡检记录中的项目都是已完成的
              images: result.images || [],
              showDetails: false,
              latitude: result.latitude,
              longitude: result.longitude
            }));
          }
        } else {
          common_vendor.index.showToast({
            title: res.message || "获取巡检结果详情失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/patrol/detail.vue:264", "获取巡检结果详情失败:", err);
        common_vendor.index.showToast({
          title: "获取巡检结果详情失败",
          icon: "none"
        });
      }).finally(() => {
        this.isLoading = false;
        common_vendor.index.hideLoading();
      });
    },
    // 加载巡检计划详情
    loadPlanDetail() {
      this.isLoading = true;
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      const apiCall = this.isRecord ? utils_api.patrolApi.getPatrolRecordDetail(this.planId) : utils_api.patrolApi.getPlanDetail(this.planId);
      apiCall.then((res) => {
        var _a, _b;
        if (res.code === 200 && res.data) {
          const data = res.data;
          this.planInfo = {
            id: this.planId,
            title: data.name,
            code: data.planNo,
            type: data.patrolType,
            startTime: data.startDate,
            endTime: data.endDate,
            location: data.locations,
            manager: ((_a = data.executorNames) == null ? void 0 : _a.join("、")) || "未分配",
            progress: `0/${((_b = data.patrolItems) == null ? void 0 : _b.length) || 0}`,
            // 进度信息需要计算
            description: data.description || "",
            status: data.status,
            scheduleType: data.scheduleType,
            scheduleWeekDays: data.scheduleWeekDays,
            scheduleMonthDays: data.scheduleMonthDays
          };
          if (data.patrolItems && data.patrolItems.length > 0) {
            this.planTasks = data.patrolItems.map((item) => ({
              id: item.id,
              title: item.itemName,
              target: item.deviceName || "",
              point: item.checkMethod || "",
              content: item.description,
              standard: item.normalRange || "",
              unit: item.unit,
              actualValue: "",
              isAbnormal: false,
              completedTime: "",
              remark: "",
              status: "pending",
              // 默认为待执行
              images: [],
              showDetails: false
            }));
            const completedTasks = this.isRecord ? this.planTasks.length : 0;
            this.planInfo.progress = `${completedTasks}/${this.planTasks.length}`;
          }
          common_vendor.index.hideLoading();
        } else {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: res.message || "获取巡检计划详情失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/patrol/detail.vue:344", "获取巡检计划详情失败:", err);
        common_vendor.index.showToast({
          title: "获取巡检计划详情失败",
          icon: "none"
        });
      }).finally(() => {
        this.isLoading = false;
        common_vendor.index.hideLoading();
      });
    },
    // 计算统计信息
    calculateStats() {
      if (!this.planTasks || this.planTasks.length === 0) {
        this.stats = {
          totalTasks: 0,
          completedTasks: 0,
          abnormalCount: 0,
          completionRate: 0
        };
        return;
      }
      const totalTasks = this.planTasks.length;
      const completedTasks = this.planTasks.filter((task) => task.status === "completed").length;
      const abnormalCount = this.planTasks.filter((task) => task.isAbnormal).length;
      const completionRate = totalTasks > 0 ? Math.floor(completedTasks / totalTasks * 100) : 0;
      this.stats = {
        totalTasks,
        completedTasks,
        abnormalCount,
        completionRate
      };
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        "pending": "待执行",
        "processing": "进行中",
        "completed": "已完成",
        "overdue": "已超时",
        "canceled": "已取消"
      };
      return statusMap[status] || "未知";
    },
    // 切换任务详情显示/隐藏
    toggleTaskDetails(index) {
      this.$set(this.planTasks[index], "showDetails", !this.planTasks[index].showDetails);
    },
    // 预览图片
    previewImage(images, current) {
      common_vendor.index.previewImage({
        urls: images,
        current: images[current]
      });
    },
    // 开始巡检
    startPatrol() {
      common_vendor.index.navigateTo({
        url: `/pages/patrol/execute?id=${this.planId}`
      });
    },
    // 继续巡检
    continuePatrol() {
      common_vendor.index.navigateTo({
        url: `/pages/patrol/execute?id=${this.planId}`
      });
    },
    // 完成巡检
    completePatrol() {
      common_vendor.index.showModal({
        title: "确认完成",
        content: "是否确认完成本次巡检计划？未完成的巡检项将被标记为已跳过。",
        confirmText: "确认",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "巡检计划已完成",
              icon: "success"
            });
            setTimeout(() => {
              this.planInfo.status = "completed";
              this.planInfo.progress = "12/12";
              this.planTasks.forEach((task) => {
                if (task.status === "pending") {
                  task.status = "skipped";
                }
              });
              this.calculateStats();
            }, 500);
          }
        }
      });
    },
    // 导出报告
    exportReport() {
      common_vendor.index.showToast({
        title: "报告已导出",
        icon: "success"
      });
    },
    // 格式化日期为yyyy-MM-dd
    formatDate(dateStr) {
      if (!dateStr)
        return "";
      const date = new Date(dateStr);
      if (isNaN(date.getTime()))
        return dateStr;
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    // 格式化日期时间为yyyy-MM-dd HH:mm:ss
    formatDateTime(dateStr) {
      if (!dateStr)
        return "";
      const date = new Date(dateStr);
      if (isNaN(date.getTime()))
        return dateStr;
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      const hour = date.getHours().toString().padStart(2, "0");
      const minute = date.getMinutes().toString().padStart(2, "0");
      const second = date.getSeconds().toString().padStart(2, "0");
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    },
    // 获取巡检周期文本
    getScheduleText(scheduleType, scheduleWeekDays, scheduleMonthDays) {
      const scheduleMap = {
        "daily": "每日",
        "weekly": "每周",
        "monthly": "每月"
      };
      const scheduleText = scheduleMap[scheduleType] || "未知";
      if (scheduleType === "daily") {
        return scheduleText;
      } else if (scheduleType === "weekly" && scheduleWeekDays && scheduleWeekDays.length > 0) {
        const weekDayMap = {
          1: "周一",
          2: "周二",
          3: "周三",
          4: "周四",
          5: "周五",
          6: "周六",
          7: "周日"
        };
        const weekDays = scheduleWeekDays.map((day) => weekDayMap[day] || `周${day}`).join("、");
        return `${scheduleText}（${weekDays}）`;
      } else if (scheduleType === "monthly" && scheduleMonthDays && scheduleMonthDays.length > 0) {
        const monthDays = scheduleMonthDays.map((day) => `${day}日`).join("、");
        return `${scheduleText}（${monthDays}）`;
      }
      return scheduleText;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.isRecord ? "巡检记录详情" : "巡检计划详情"),
    b: common_vendor.t($options.getStatusText($data.planInfo.status)),
    c: common_vendor.n($data.planInfo.status),
    d: common_vendor.t($data.planInfo.title),
    e: common_vendor.t($data.planInfo.code),
    f: common_vendor.t($data.planInfo.type),
    g: common_vendor.t($options.formatDate($data.planInfo.startTime)),
    h: common_vendor.t($options.formatDate($data.planInfo.endTime)),
    i: $data.planInfo.scheduleType
  }, $data.planInfo.scheduleType ? {
    j: common_vendor.t($options.getScheduleText($data.planInfo.scheduleType, $data.planInfo.scheduleWeekDays, $data.planInfo.scheduleMonthDays))
  } : {}, {
    k: common_vendor.t($data.planInfo.location),
    l: common_vendor.t($data.planInfo.manager),
    m: $data.planInfo.description
  }, $data.planInfo.description ? {
    n: common_vendor.t($data.planInfo.description)
  } : {}, {
    o: common_vendor.f($data.planTasks, (task, index, i0) => {
      return common_vendor.e({
        a: common_vendor.n(task.status),
        b: common_vendor.t(task.title),
        c: common_vendor.n(task.showDetails ? "icon-arrow-up" : "icon-arrow-down"),
        d: task.showDetails
      }, task.showDetails ? common_vendor.e({
        e: common_vendor.t(task.target),
        f: common_vendor.t(task.point),
        g: common_vendor.t(task.content),
        h: common_vendor.t(task.standard),
        i: common_vendor.t(task.unit),
        j: task.status === "completed"
      }, task.status === "completed" ? {
        k: common_vendor.t(task.actualValue),
        l: task.isAbnormal ? 1 : ""
      } : {}, {
        m: task.status === "completed" && task.completedTime
      }, task.status === "completed" && task.completedTime ? {
        n: common_vendor.t(task.completedTime)
      } : {}, {
        o: task.status === "completed" && task.remark
      }, task.status === "completed" && task.remark ? {
        p: common_vendor.t(task.remark)
      } : {}, {
        q: task.status === "completed" && task.images && task.images.length > 0
      }, task.status === "completed" && task.images && task.images.length > 0 ? {
        r: common_vendor.f(task.images, (img, imgIndex, i1) => {
          return {
            a: imgIndex,
            b: img,
            c: common_vendor.o(($event) => $options.previewImage(task.images, imgIndex), imgIndex)
          };
        })
      } : {}) : {}, {
        s: index,
        t: common_vendor.o(($event) => $options.toggleTaskDetails(index), index)
      });
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/patrol/detail.js.map
