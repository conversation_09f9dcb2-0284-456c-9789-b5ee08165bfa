<view class="patrol-records-container"><view wx:if="{{a}}" class="error-box"><view class="error-content"><text class="error-icon iconfont icon-warn"></text><text class="error-text">{{b}}</text><button class="retry-btn" bindtap="{{c}}">重试</button></view></view><view class="page-header"><view class="search-bar"><view class="filter-options"><view class="filter-option" bindtap="{{e}}"><text class="option-text">时间</text><text class="option-value">{{d}}</text></view><view class="filter-option" bindtap="{{g}}"><text class="option-text">状态</text><text class="option-value">{{f}}</text></view></view></view></view><view wx:if="{{h}}" class="record-list"><view wx:for="{{i}}" wx:for-item="record" wx:key="o" class="record-item" bindtap="{{record.p}}"><view class="record-header"><text class="record-title">{{record.a}}</text><view class="{{['record-status', record.c]}}">{{record.b}}</view></view><view class="record-info"><view class="info-row date-location"><view class="info-item"><text class="item-label">执行日期:</text><text class="item-value">{{record.d}}</text></view><view class="info-item"><text class="item-label">巡检类型:</text><text class="item-value">{{record.e}}</text></view></view><view class="info-row time-row"><view class="info-item"><text class="item-label">执行时间:</text><text class="item-value">{{record.f}} ~{{record.g}}</text></view></view><view class="info-row executor-abnormal"><view class="info-item"><text class="item-label">执行人:</text><text class="item-value">{{record.h}}</text></view></view></view><view class="record-footer"><text class="record-time">{{record.i}}</text><permission-check wx:if="{{j}}" u-s="{{['d']}}" u-i="{{record.l}}" bind:__l="__l" u-p="{{j}}"><view wx:if="{{record.j}}" class="view-detail start" catchtap="{{record.k}}"><text class="btn-text">开始巡检</text></view></permission-check><permission-check wx:if="{{k}}" u-s="{{['d']}}" u-i="{{record.n}}" bind:__l="__l" u-p="{{k}}"><view class="view-detail view" catchtap="{{record.m}}"><text class="btn-text">查看详情</text></view></permission-check></view></view></view><view wx:if="{{l}}" class="error-container"><text class="error-text">{{m}}</text><button class="retry-btn" bindtap="{{n}}">重新加载</button></view><view wx:if="{{o}}" class="empty-state"><image class="empty-image" src="{{p}}" mode="aspectFit"></image><text class="empty-text">{{q}}</text></view><uni-popup wx:if="{{v}}" class="r" u-s="{{['d']}}" u-r="timeFilterPopup" u-i="17c31495-2" bind:__l="__l" u-p="{{v}}"><view class="filter-popup"><view class="popup-header"><text class="popup-title">选择时间范围</text><text class="popup-close" bindtap="{{r}}">关闭</text></view><view class="popup-content"><view wx:for="{{s}}" wx:for-item="option" wx:key="c" class="{{['filter-option', option.d && 'active']}}" bindtap="{{option.e}}"><text class="option-text">{{option.a}}</text><text wx:if="{{option.b}}" class="iconfont icon-check"></text></view></view></view></uni-popup><uni-popup wx:if="{{z}}" class="r" u-s="{{['d']}}" u-r="statusFilterPopup" u-i="17c31495-3" bind:__l="__l" u-p="{{z}}"><view class="filter-popup"><view class="popup-header"><text class="popup-title">选择巡检状态</text><text class="popup-close" bindtap="{{w}}">关闭</text></view><view class="popup-content"><view wx:for="{{x}}" wx:for-item="option" wx:key="c" class="{{['filter-option', option.d && 'active']}}" bindtap="{{option.e}}"><text class="option-text">{{option.a}}</text><text wx:if="{{option.b}}" class="iconfont icon-check"></text></view></view></view></uni-popup></view>