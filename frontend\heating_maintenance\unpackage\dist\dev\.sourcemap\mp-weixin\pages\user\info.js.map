{"version": 3, "file": "info.js", "sources": ["pages/user/info.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlci9pbmZvLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"user-info-container\">\n    <!-- 用户信息头部 -->\n    <view class=\"user-header\">\n      <view class=\"user-header-bg\"></view>\n      <view class=\"avatar-container\">\n        <image class=\"avatar\" :src=\"getFullImageUrl(userInfo.avatar)\"></image>\n        <view class=\"edit-avatar\" @click=\"updateAvatar\">\n          <text class=\"iconfont icon-camera\"></text>\n        </view>\n      </view>\n\n      <view class=\"user-details\">\n        <view class=\"user-name\">{{ userInfo.name }}</view>\n     <!--   <view class=\"user-role\">\n          <text class=\"role-badge\">{{ userInfo.role }}</text>\n        </view> -->\n      </view>\n    </view>\n\n<!-- 工作统计 -->\n<view class=\"work-stats\">\n      <view class=\"stat-item\">\n        <text class=\"stat-value\">{{ workStats.myWorkOrders }}</text>\n        <text class=\"stat-label\">我的工单</text>\n      </view>\n     <view class=\"stat-divider\"></view>\n      <view class=\"stat-item\">\n        <text class=\"stat-value\">{{ workStats.processingOrders }}</text>\n        <text class=\"stat-label\">{{ isAdminOrManager ? '已接单' : '待处理' }}</text>\n      </view>\n      <view class=\"stat-divider\"></view>\n      <view class=\"stat-item\">\n        <text class=\"stat-value\">{{ workStats.pendingOrders }}</text>\n        <text class=\"stat-label\">待接单</text>\n      </view>\n </view>\n\n  <!-- 快速入口 -->\n  <!--  <view class=\"quick-actions\">\n      <view class=\"quick-grid\">\n        <view class=\"quick-item\" @click=\"navigateTo('/pages/workorder/list')\">\n          <view class=\"quick-icon-bg\">\n            <text class=\"iconfont icon-task\"></text>\n          </view>\n          <text class=\"item-text\">我的工单</text>\n        </view>\n        <view class=\"quick-item\" @click=\"navigateTo('/pages/patrol/records')\">\n          <view class=\"quick-icon-bg\">\n            <text class=\"iconfont icon-patrol\"></text>\n          </view>\n          <text class=\"item-text\">巡检记录</text>\n        </view>\n        <view class=\"quick-item\" @click=\"navigateTo('/pages/device/list')\">\n          <view class=\"quick-icon-bg\">\n            <text class=\"iconfont icon-device\"></text>\n          </view>\n          <text class=\"item-text\">设备管理</text>\n        </view>\n        <view class=\"quick-item\" @click=\"navigateTo('/pages/fault/list')\">\n          <view class=\"quick-icon-bg\">\n            <text class=\"iconfont icon-fault\"></text>\n          </view>\n          <text class=\"item-text\">故障列表</text>\n        </view>\n      </view>\n    </view> -->\n\n    <!-- 功能列表 -->\n    <view class=\"feature-list\">\n      <!-- 个人设置 -->\n      <view class=\"feature-section\">\n        <view class=\"section-title\">\n          <text class=\"section-icon iconfont icon-settings\"></text>\n          个人设置\n        </view>\n\n        <view class=\"menu-item\" @click=\"navigateTo('/pages/user/profile')\">\n          <view class=\"menu-icon-container profile-icon\">\n            <text class=\"iconfont icon-user\"></text>\n          </view>\n          <view class=\"menu-content\">个人资料</view>\n          <text class=\"iconfont icon-arrow-right\"></text>\n        </view>\n\n        <view class=\"menu-item\" @click=\"navigateTo('/pages/user/message-settings')\">\n          <view class=\"menu-icon-container message-icon\">\n            <text class=\"iconfont icon-notification\"></text>\n          </view>\n          <view class=\"menu-content\">消息设置</view>\n          <text class=\"iconfont icon-arrow-right\"></text>\n        </view>\n      </view>\n\n      <!-- 安全设置 -->\n      <view class=\"feature-section\">\n        <view class=\"section-title\">\n          <text class=\"section-icon iconfont icon-shield\"></text>\n          安全设置\n        </view>\n\n        <view class=\"menu-item\" @click=\"navigateTo('/pages/user/change-password')\">\n          <view class=\"menu-icon-container security-icon\">\n            <text class=\"iconfont icon-lock\"></text>\n          </view>\n          <view class=\"menu-content\">密码修改</view>\n          <text class=\"iconfont icon-arrow-right\"></text>\n        </view>\n\n     <!--   <view class=\"menu-item\" @click=\"navigateTo('/pages/user/account-binding')\">\n          <view class=\"menu-icon-container binding-icon\">\n            <text class=\"iconfont icon-phone\"></text>\n          </view>\n          <view class=\"menu-content\">账号绑定</view>\n          <text class=\"iconfont icon-arrow-right\"></text>\n        </view> -->\n      </view>\n\n      <!-- 帮助与支持 -->\n      <view class=\"feature-section\">\n        <view class=\"section-title\">\n          <text class=\"section-icon iconfont icon-support\"></text>\n          帮助与支持\n        </view>\n\n        <view class=\"menu-item\" @click=\"navigateTo('/pages/user/faq')\">\n          <view class=\"menu-icon-container help-icon\">\n            <text class=\"iconfont icon-help\"></text>\n          </view>\n          <view class=\"menu-content\">常见问题</view>\n          <text class=\"iconfont icon-arrow-right\"></text>\n        </view>\n\n        <view class=\"menu-item\" @click=\"navigateTo('/pages/user/about')\">\n          <view class=\"menu-icon-container about-icon\">\n            <text class=\"iconfont icon-info\"></text>\n          </view>\n          <view class=\"menu-content\">关于系统</view>\n          <text class=\"iconfont icon-arrow-right\"></text>\n        </view>\n\n        <!-- 新增：消息服务测试按钮 -->\n   <!--     <view class=\"menu-item\" @click=\"navigateTo('/pages/messageTest/messageTest')\">\n          <view class=\"menu-icon-container message-test-icon\">\n            <text class=\"iconfont icon-message\"></text>\n          </view>\n          <view class=\"menu-content\">消息服务测试</view>\n          <text class=\"iconfont icon-arrow-right\"></text>\n        </view> -->\n        <!-- 新增结束 -->\n      </view>\n    </view>\n\n    <!-- 退出登录 -->\n    <view class=\"logout-btn\" @click=\"handleLogout\">退出登录</view>\n\n    <!-- 添加自定义TabBar -->\n    <BaseTabBar></BaseTabBar>\n  </view>\n</template>\n\n<script>\nimport { userApi, workOrderApi } from \"../../utils/api\";\nimport BaseTabBar from \"@/components/BaseTabBar.vue\";\nimport uploadUtils from \"@/utils/upload.js\";\n\nexport default {\n  components: {\n    BaseTabBar,\n  },\n  data() {\n    return {\n      userInfo: {},\n      workStats: {\n        myWorkOrders: 0,\n        processingOrders: 0,\n        pendingOrders: 0,\n      },\n      isUploading: false,\n    };\n  },\n  computed: {\n    // 判断用户是否为管理员或主管角色\n    isAdminOrManager() {\n      const userRole = uni.getStorageSync(\"userRole\");\n      return userRole === 'admin' || userRole === 'manager';\n    }\n  },\n  onShow() {\n    // 每次显示页面时都加载用户信息，确保数据最新\n    this.loadUserInfo();\n    // 加载工单统计数据\n    this.loadWorkOrderStats();\n  },\n  methods: {\n    // 加载用户信息\n    loadUserInfo() {\n      // 从本地存储获取用户信息\n      const userInfo = uni.getStorageSync(\"userInfo\");\n      const userId = uni.getStorageSync(\"userId\");\n      const userRole = uni.getStorageSync(\"userRole\");\n\n      if (userInfo) {\n        console.log(\"本地存储的用户信息:\", userInfo);\n        // 更新数据\n        this.userInfo = {\n          name: userInfo.user.name || userInfo.user.username || \"用户\",\n          avatar: userInfo.user.avatar ? uploadUtils.getFileUrl(userInfo.user.avatar) : \"/static/user/avatar.png\",\n          role: this.getRoleName(userRole) || \"普通用户\",\n          id: userId || \"USER-\" + Date.now(),\n        };\n      } else {\n        // 如果本地没有用户信息，从服务器获取\n        this.fetchUserInfoFromServer();\n      }\n    },\n\n    // 从服务器获取用户信息\n    fetchUserInfoFromServer() {\n      uni.showLoading({\n        title: \"加载中...\",\n      });\n\n      userApi\n        .getUserInfo()\n        .then((res) => {\n          uni.hideLoading();\n          if (res.code === 200) {\n            // 存储用户信息\n            uni.setStorageSync(\"userInfo\", res.data);\n             \n            // 更新数据\n            this.userInfo = {\n              name: res.data.name || res.data.username || \"用户\",\n              avatar: res.data.avatar ? uploadUtils.getFileUrl(res.data.avatar) : \"/static/user/avatar.png\",\n              role: this.getRoleName(res.data.role) || \"普通用户\",\n              id: res.data.userId || \"USER-\" + Date.now(),\n            };\n          } else {\n            this.showError(\"获取用户信息失败\");\n          }\n        })\n        .catch((err) => {\n          uni.hideLoading();\n          console.error(\"获取用户信息失败:\", err);\n          this.showError(\"网络错误，请稍后重试\");\n        });\n    },\n\n    // 角色名称转换\n    getRoleName(role) {\n      const roleMap = {\n        admin: \"系统管理员\",\n        manager: \"主管\",\n        engineer: \"维修工程师\",\n        operator: \"操作员\",\n        user: \"普通用户\",\n      };\n\n      return roleMap[role] || role || \"普通用户\";\n    },\n\n    // 显示错误提示\n    showError(message) {\n      uni.showToast({\n        title: message,\n        icon: \"none\",\n      });\n    },\n\n    // 更新头像\n    updateAvatar() {\n      uni.chooseImage({\n        count: 1,\n        sizeType: ['compressed'],\n        sourceType: ['album', 'camera'],\n        success: (res) => {\n          const tempFilePaths = res.tempFilePaths;\n          // 显示上传中提示\n          uni.showLoading({\n            title: '上传中...',\n            mask: true\n          });\n          \n          this.isUploading = true;\n          \n          // 检查token是否存在，不存在则提示用户登录\n          const token = uni.getStorageSync('token');\n          if (!token) {\n            uni.hideLoading();\n            uni.showToast({\n              title: '请先登录',\n              icon: 'none'\n            });\n            return;\n          }\n          \n          // 使用uploadUtils上传头像\n          uploadUtils.uploadImage(tempFilePaths[0])\n            .then(serverPath => {\n              console.log('头像上传成功，服务器路径:', serverPath);\n              \n              // 更新本地显示 - 使用临时文件路径以立即显示效果\n              this.userInfo.avatar = tempFilePaths[0];\n              \n              // 调用API更新用户头像信息\n              return userApi.updateUserInfo({\n                avatar: \"/uploads\"+serverPath\n              });\n            })\n            .then(res => {\n              if (res.code === 200) {\n                // 更新本地存储的用户信息 - 使用服务器路径\n                const userInfo = uni.getStorageSync('userInfo') || {};\n                userInfo.avatar = res.data?.avatar || userInfo.avatar;\n                uni.setStorageSync('userInfo', userInfo);\n                \n                uni.showToast({\n                  title: '头像更新成功',\n                  icon: 'success'\n                });\n\t\t\t\t\n\t\t\t\t\n              } else {\n                uni.showToast({\n                  title: res.message || '更新头像失败',\n                  icon: 'none'\n                });\n              }\n            })\n            .catch(err => {\n              console.error('上传头像失败:', err);\n              uni.showToast({\n                title: '上传失败，请重试',\n                icon: 'none'\n              });\n            })\n            .finally(() => {\n              uni.hideLoading();\n              this.isUploading = false;\n            });\n        },\n      });\n    },\n\n    // 页面导航\n    navigateTo(url) {\n      uni.navigateTo({\n        url: url,\n      });\n    },\n\n    // 退出登录\n    handleLogout() {\n      uni.showModal({\n        title: \"确认退出\",\n        content: \"确定要退出登录吗？\",\n        success: (res) => {\n          if (res.confirm) {\n            // 清除本地存储的登录信息\n            uni.removeStorageSync(\"token\");\n            uni.removeStorageSync(\"userInfo\");\n            uni.removeStorageSync(\"userId\");\n            uni.removeStorageSync(\"userRole\");\n            uni.removeStorageSync(\"userPermissions\");\n\n            // 重定向到登录页\n            uni.reLaunch({\n              url: \"/pages/user/login\",\n            });\n          }\n        },\n      });\n    },\n\n    // 获取完整的图片URL\n    getFullImageUrl(url) {\n      if (!url) return '/static/user/avatar.png';\n      \n      // 如果已经是完整URL，直接返回\n      if (url.startsWith('http') || url.startsWith('/static/')) {\n        return url;\n      }\n      \n      // 使用uploadUtils获取完整URL\n      return uploadUtils.getFileUrl(url);\n    },\n\n    // 加载工单统计数据\n    loadWorkOrderStats() {\n      console.log(\"开始加载工单统计数据\");\n      const userRole = uni.getStorageSync(\"userRole\");\n      console.log(\"用户角色:\", userRole, \"是否管理员或主管:\", this.isAdminOrManager);\n      \n      workOrderApi.getWorkOrderStats()\n        .then(res => {\n          if (res.code === 200 && res.data) {\n            console.log(\"工单统计数据:\", res.data);\n            this.workStats = {\n              myWorkOrders: res.data.myWorkOrders || 0,\n              processingOrders: res.data.processingOrders || 0,\n              pendingOrders: res.data.pendingOrders || 0\n            };\n            \n            // 根据角色显示不同的提示信息\n            if (this.isAdminOrManager) {\n              console.log(\"管理员/主管视图: 我的工单、已接单工单、待接单工单\");\n            } else {\n              console.log(\"普通用户视图: 我的工单、待处理工单、待接单工单\");\n            }\n          } else {\n            console.warn(\"获取工单统计数据失败:\", res.message);\n          }\n        })\n        .catch(err => {\n          console.error(\"获取工单统计数据异常:\", err);\n        });\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\">\n.user-info-container {\n  padding-bottom: 50rpx;\n  background-color: #f5f7fa;\n}\n\n.user-header {\n  background-image: linear-gradient(135deg, #1890ff, #0076e4);\n  padding: 120rpx 30rpx 80rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  position: relative;\n  overflow: hidden;\n\n  .user-header-bg {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"100\" height=\"100\" viewBox=\"0 0 100 100\"><circle cx=\"20\" cy=\"20\" r=\"15\" fill=\"rgba(255,255,255,0.05)\"/><circle cx=\"70\" cy=\"70\" r=\"25\" fill=\"rgba(255,255,255,0.05)\"/><circle cx=\"100\" cy=\"30\" r=\"20\" fill=\"rgba(255,255,255,0.05)\"/></svg>');\n    background-size: 200rpx;\n    opacity: 0.8;\n  }\n\n  .avatar-container {\n    position: relative;\n    margin-bottom: 20rpx;\n    z-index: 1;\n\n    .avatar {\n      width: 160rpx;\n      height: 160rpx;\n      border-radius: 50%;\n      border: 6rpx solid rgba(255, 255, 255, 0.8);\n      box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);\n    }\n\n    .edit-avatar {\n      position: absolute;\n      right: 0;\n      bottom: 0;\n      width: 56rpx;\n      height: 56rpx;\n      background-color: #fff;\n      border-radius: 50%;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);\n\n      .iconfont {\n        font-size: 30rpx;\n        color: $uni-color-primary;\n      }\n    }\n  }\n\n  .user-details {\n    color: #fff;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    text-align: center;\n    z-index: 1;\n\n    .user-name {\n      font-size: 44rpx;\n      font-weight: bold;\n      margin-bottom: 16rpx;\n      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);\n    }\n\n    .user-role {\n      margin-bottom: 12rpx;\n\n      .role-badge {\n        background-color: rgba(255, 255, 255, 0.25);\n        padding: 8rpx 24rpx;\n        border-radius: 30rpx;\n        font-size: 26rpx;\n        box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);\n      }\n    }\n\n    .user-id {\n      font-size: 26rpx;\n      opacity: 0.9;\n      letter-spacing: 1rpx;\n    }\n  }\n}\n\n.work-stats {\n  margin-top: -50rpx;\n  margin-left: 30rpx;\n  margin-right: 30rpx;\n  margin-bottom: 30rpx;\n  background-color: #fff;\n  border-radius: 16rpx;\n  box-shadow: 0 10rpx 30rpx rgba(24, 144, 255, 0.1);\n  display: flex;\n  justify-content: space-around;\n  align-items: center;\n  padding: 30rpx 0;\n  position: relative;\n  z-index: 10;\n\n  .stat-item {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    padding: 10rpx 0;\n\n    .stat-value {\n      font-size: 48rpx;\n      font-weight: bold;\n      color: $uni-color-primary;\n      margin-bottom: 12rpx;\n      line-height: 1;\n    }\n\n    .stat-label {\n      font-size: 26rpx;\n      color: #666;\n      font-weight: 500;\n    }\n  }\n\n  .stat-divider {\n    width: 2rpx;\n    height: 70rpx;\n    background: linear-gradient(\n      to bottom,\n      rgba(0, 0, 0, 0.02),\n      rgba(0, 0, 0, 0.1),\n      rgba(0, 0, 0, 0.02)\n    );\n  }\n}\n\n.quick-actions {\n  margin: 0 30rpx 30rpx;\n  background-color: #fff;\n  border-radius: 16rpx;\n  padding: 30rpx 20rpx;\n  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.05);\n\n  .quick-grid {\n    display: flex;\n    justify-content: space-around;\n\n    .quick-item {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      padding: 15rpx 0;\n      transition: transform 0.2s;\n\n      &:active {\n        transform: scale(0.95);\n      }\n\n      .quick-icon-bg {\n        width: 110rpx;\n        height: 110rpx;\n        border-radius: 30rpx;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        margin-bottom: 15rpx;\n        background: linear-gradient(\n          135deg,\n          rgba(24, 144, 255, 0.1),\n          rgba(24, 144, 255, 0.2)\n        );\n        box-shadow: 0 6rpx 15rpx rgba(24, 144, 255, 0.1);\n\n        .iconfont {\n          font-size: 52rpx;\n          color: $uni-color-primary;\n        }\n\n        &:nth-child(1) {\n          background: linear-gradient(\n            135deg,\n            rgba(24, 144, 255, 0.1),\n            rgba(24, 144, 255, 0.2)\n          );\n        }\n      }\n\n      &:nth-child(1) .quick-icon-bg {\n        background: linear-gradient(\n          135deg,\n          rgba(24, 144, 255, 0.1),\n          rgba(24, 144, 255, 0.2)\n        );\n      }\n\n      &:nth-child(2) .quick-icon-bg {\n        background: linear-gradient(\n          135deg,\n          rgba(82, 196, 26, 0.1),\n          rgba(82, 196, 26, 0.2)\n        );\n\n        .iconfont {\n          color: #52c41a;\n        }\n      }\n\n      &:nth-child(3) .quick-icon-bg {\n        background: linear-gradient(\n          135deg,\n          rgba(250, 140, 22, 0.1),\n          rgba(250, 140, 22, 0.2)\n        );\n\n        .iconfont {\n          color: #fa8c16;\n        }\n      }\n\n      &:nth-child(4) .quick-icon-bg {\n        background: linear-gradient(\n          135deg,\n          rgba(245, 34, 45, 0.1),\n          rgba(245, 34, 45, 0.2)\n        );\n\n        .iconfont {\n          color: #f5222d;\n        }\n      }\n\n      .item-text {\n        font-size: 28rpx;\n        color: #333;\n        font-weight: 500;\n      }\n    }\n  }\n}\n\n.feature-list {\n\tmargin-top: 20rpx;;\n  .feature-section {\n    margin: 0 30rpx 30rpx;\n    background-color: #fff;\n    border-radius: 12rpx;\n    padding: 20rpx;\n    box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.05);\n\n    .section-title {\n      font-size: 30rpx;\n      font-weight: bold;\n      margin-bottom: 20rpx;\n      padding: 0 20rpx;\n      display: flex;\n      align-items: center;\n\n      .section-icon {\n        font-size: 32rpx;\n        color: $uni-color-primary;\n        margin-right: 10rpx;\n      }\n    }\n\n    .menu-item {\n      display: flex;\n      align-items: center;\n      padding: 24rpx 20rpx;\n      border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);\n      transition: background-color 0.2s;\n\n      &:active {\n        background-color: rgba(0, 0, 0, 0.02);\n      }\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      .menu-icon-container {\n        width: 70rpx;\n        height: 70rpx;\n        border-radius: 50%;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        margin-right: 20rpx;\n        box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.08);\n\n        .iconfont {\n          font-size: 36rpx;\n          color: #fff;\n        }\n\n        &.profile-icon {\n          background: linear-gradient(135deg, #1890ff, #36b3ff);\n        }\n\n        &.message-icon {\n          background: linear-gradient(135deg, #52c41a, #73d13d);\n        }\n\n        &.security-icon {\n          background: linear-gradient(135deg, #fa8c16, #ffa940);\n        }\n\n        &.binding-icon {\n          background: linear-gradient(135deg, #722ed1, #9254de);\n        }\n\n        &.help-icon {\n          background: linear-gradient(135deg, #13c2c2, #36cfc9);\n        }\n\n        &.about-icon {\n          background: linear-gradient(135deg, #eb2f96, #f759ab);\n        }\n\n        &.message-test-icon {\n          background: linear-gradient(135deg, #13c2c2, #36cfc9);\n        }\n      }\n\n      .menu-content {\n        flex: 1;\n        font-size: 30rpx;\n      }\n\n      .iconfont.icon-arrow-right {\n        font-size: 28rpx;\n        color: #ccc;\n      }\n    }\n  }\n}\n\n.logout-btn {\n  margin: 50rpx 30rpx;\n  height: 90rpx;\n  line-height: 90rpx;\n  background-color: #fff;\n  color: $uni-color-error;\n  text-align: center;\n  border-radius: 12rpx;\n  font-size: 32rpx;\n  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.05);\n  font-weight: bold;\n  &:active {\n    background-color: rgba(255, 255, 255, 0.9);\n    transform: translateY(2rpx);\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaan<PERSON>_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/user/info.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "uploadUtils", "userApi", "res", "workOrderApi"], "mappings": ";;;;AAmKA,mBAAmB,MAAW;AAG9B,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,UAAU,CAAE;AAAA,MACZ,WAAW;AAAA,QACT,cAAc;AAAA,QACd,kBAAkB;AAAA,QAClB,eAAe;AAAA,MAChB;AAAA,MACD,aAAa;AAAA;EAEhB;AAAA,EACD,UAAU;AAAA;AAAA,IAER,mBAAmB;AACjB,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9C,aAAO,aAAa,WAAW,aAAa;AAAA,IAC9C;AAAA,EACD;AAAA,EACD,SAAS;AAEP,SAAK,aAAY;AAEjB,SAAK,mBAAkB;AAAA,EACxB;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,eAAe;AAEb,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9C,YAAM,SAASA,cAAAA,MAAI,eAAe,QAAQ;AAC1C,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAE9C,UAAI,UAAU;AACZA,sBAAA,MAAA,MAAA,OAAA,8BAAY,cAAc,QAAQ;AAElC,aAAK,WAAW;AAAA,UACd,MAAM,SAAS,KAAK,QAAQ,SAAS,KAAK,YAAY;AAAA,UACtD,QAAQ,SAAS,KAAK,SAASC,aAAAA,YAAY,WAAW,SAAS,KAAK,MAAM,IAAI;AAAA,UAC9E,MAAM,KAAK,YAAY,QAAQ,KAAK;AAAA,UACpC,IAAI,UAAU,UAAU,KAAK,IAAK;AAAA;aAE/B;AAEL,aAAK,wBAAuB;AAAA,MAC9B;AAAA,IACD;AAAA;AAAA,IAGD,0BAA0B;AACxBD,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAEDE,gBAAM,QACH,YAAY,EACZ,KAAK,CAAC,QAAQ;AACbF,sBAAG,MAAC,YAAW;AACf,YAAI,IAAI,SAAS,KAAK;AAEpBA,wBAAAA,MAAI,eAAe,YAAY,IAAI,IAAI;AAGvC,eAAK,WAAW;AAAA,YACd,MAAM,IAAI,KAAK,QAAQ,IAAI,KAAK,YAAY;AAAA,YAC5C,QAAQ,IAAI,KAAK,SAASC,aAAAA,YAAY,WAAW,IAAI,KAAK,MAAM,IAAI;AAAA,YACpE,MAAM,KAAK,YAAY,IAAI,KAAK,IAAI,KAAK;AAAA,YACzC,IAAI,IAAI,KAAK,UAAU,UAAU,KAAK,IAAK;AAAA;eAExC;AACL,eAAK,UAAU,UAAU;AAAA,QAC3B;AAAA,OACD,EACA,MAAM,CAAC,QAAQ;AACdD,sBAAG,MAAC,YAAW;AACfA,sBAAc,MAAA,MAAA,SAAA,8BAAA,aAAa,GAAG;AAC9B,aAAK,UAAU,YAAY;AAAA,MAC7B,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,YAAY,MAAM;AAChB,YAAM,UAAU;AAAA,QACd,OAAO;AAAA,QACP,SAAS;AAAA,QACT,UAAU;AAAA,QACV,UAAU;AAAA,QACV,MAAM;AAAA;AAGR,aAAO,QAAQ,IAAI,KAAK,QAAQ;AAAA,IACjC;AAAA;AAAA,IAGD,UAAU,SAAS;AACjBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,eAAe;AACbA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AAChB,gBAAM,gBAAgB,IAAI;AAE1BA,wBAAAA,MAAI,YAAY;AAAA,YACd,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAED,eAAK,cAAc;AAGnB,gBAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,cAAI,CAAC,OAAO;AACVA,0BAAG,MAAC,YAAW;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AACD;AAAA,UACF;AAGAC,uBAAAA,YAAY,YAAY,cAAc,CAAC,CAAC,EACrC,KAAK,gBAAc;AAClBD,0BAAY,MAAA,MAAA,OAAA,8BAAA,iBAAiB,UAAU;AAGvC,iBAAK,SAAS,SAAS,cAAc,CAAC;AAGtC,mBAAOE,UAAAA,QAAQ,eAAe;AAAA,cAC5B,QAAQ,aAAW;AAAA,YACrB,CAAC;AAAA,WACF,EACA,KAAK,CAAAC,SAAO;;AACX,gBAAIA,KAAI,SAAS,KAAK;AAEpB,oBAAM,WAAWH,cAAG,MAAC,eAAe,UAAU,KAAK,CAAA;AACnD,uBAAS,WAAS,KAAAG,KAAI,SAAJ,mBAAU,WAAU,SAAS;AAC/CH,4BAAAA,MAAI,eAAe,YAAY,QAAQ;AAEvCA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AAAA,mBAGI;AACLA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAOG,KAAI,WAAW;AAAA,gBACtB,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,WACD,EACA,MAAM,SAAO;AACZH,0BAAA,MAAA,MAAA,SAAA,8BAAc,WAAW,GAAG;AAC5BA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,WACF,EACA,QAAQ,MAAM;AACbA,0BAAG,MAAC,YAAW;AACf,iBAAK,cAAc;AAAA,UACrB,CAAC;AAAA,QACJ;AAAA,MACH,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,WAAW,KAAK;AACdA,oBAAAA,MAAI,WAAW;AAAA,QACb;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,eAAe;AACbA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEfA,gCAAI,kBAAkB,OAAO;AAC7BA,gCAAI,kBAAkB,UAAU;AAChCA,gCAAI,kBAAkB,QAAQ;AAC9BA,gCAAI,kBAAkB,UAAU;AAChCA,gCAAI,kBAAkB,iBAAiB;AAGvCA,0BAAAA,MAAI,SAAS;AAAA,cACX,KAAK;AAAA,YACP,CAAC;AAAA,UACH;AAAA,QACD;AAAA,MACH,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB,KAAK;AACnB,UAAI,CAAC;AAAK,eAAO;AAGjB,UAAI,IAAI,WAAW,MAAM,KAAK,IAAI,WAAW,UAAU,GAAG;AACxD,eAAO;AAAA,MACT;AAGA,aAAOC,aAAW,YAAC,WAAW,GAAG;AAAA,IAClC;AAAA;AAAA,IAGD,qBAAqB;AACnBD,oBAAAA,MAAA,MAAA,OAAA,8BAAY,YAAY;AACxB,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9CA,0BAAA,MAAA,OAAA,8BAAY,SAAS,UAAU,aAAa,KAAK,gBAAgB;AAEjEI,gBAAAA,aAAa,kBAAkB,EAC5B,KAAK,SAAO;AACX,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAChCJ,wBAAY,MAAA,MAAA,OAAA,8BAAA,WAAW,IAAI,IAAI;AAC/B,eAAK,YAAY;AAAA,YACf,cAAc,IAAI,KAAK,gBAAgB;AAAA,YACvC,kBAAkB,IAAI,KAAK,oBAAoB;AAAA,YAC/C,eAAe,IAAI,KAAK,iBAAiB;AAAA;AAI3C,cAAI,KAAK,kBAAkB;AACzBA,0BAAAA,MAAY,MAAA,OAAA,8BAAA,4BAA4B;AAAA,iBACnC;AACLA,0BAAAA,MAAA,MAAA,OAAA,8BAAY,0BAA0B;AAAA,UACxC;AAAA,eACK;AACLA,wBAAa,MAAA,MAAA,QAAA,8BAAA,eAAe,IAAI,OAAO;AAAA,QACzC;AAAA,OACD,EACA,MAAM,SAAO;AACZA,sBAAA,MAAA,MAAA,SAAA,8BAAc,eAAe,GAAG;AAAA,MAClC,CAAC;AAAA,IACJ;AAAA,EACF;AACH;;;;;;;;;;;;;;;;;;;;;;;AClaA,GAAG,WAAW,eAAe;"}