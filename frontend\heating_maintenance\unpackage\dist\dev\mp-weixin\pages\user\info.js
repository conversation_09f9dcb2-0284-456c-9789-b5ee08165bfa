"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const utils_upload = require("../../utils/upload.js");
const BaseTabBar = () => "../../components/BaseTabBar.js";
const _sfc_main = {
  components: {
    BaseTabBar
  },
  data() {
    return {
      userInfo: {},
      workStats: {
        myWorkOrders: 0,
        processingOrders: 0,
        pendingOrders: 0
      },
      isUploading: false
    };
  },
  computed: {
    // 判断用户是否为管理员或主管角色
    isAdminOrManager() {
      const userRole = common_vendor.index.getStorageSync("userRole");
      return userRole === "admin" || userRole === "manager";
    }
  },
  onShow() {
    this.loadUserInfo();
    this.loadWorkOrderStats();
  },
  methods: {
    // 加载用户信息
    loadUserInfo() {
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      const userId = common_vendor.index.getStorageSync("userId");
      const userRole = common_vendor.index.getStorageSync("userRole");
      if (userInfo) {
        common_vendor.index.__f__("log", "at pages/user/info.vue:204", "本地存储的用户信息:", userInfo);
        this.userInfo = {
          name: userInfo.user.name || userInfo.user.username || "用户",
          avatar: userInfo.user.avatar ? utils_upload.uploadUtils.getFileUrl(userInfo.user.avatar) : "/static/user/avatar.png",
          role: this.getRoleName(userRole) || "普通用户",
          id: userId || "USER-" + Date.now()
        };
      } else {
        this.fetchUserInfoFromServer();
      }
    },
    // 从服务器获取用户信息
    fetchUserInfoFromServer() {
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      utils_api.userApi.getUserInfo().then((res) => {
        common_vendor.index.hideLoading();
        if (res.code === 200) {
          common_vendor.index.setStorageSync("userInfo", res.data);
          this.userInfo = {
            name: res.data.name || res.data.username || "用户",
            avatar: res.data.avatar ? utils_upload.uploadUtils.getFileUrl(res.data.avatar) : "/static/user/avatar.png",
            role: this.getRoleName(res.data.role) || "普通用户",
            id: res.data.userId || "USER-" + Date.now()
          };
        } else {
          this.showError("获取用户信息失败");
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/user/info.vue:245", "获取用户信息失败:", err);
        this.showError("网络错误，请稍后重试");
      });
    },
    // 角色名称转换
    getRoleName(role) {
      const roleMap = {
        admin: "系统管理员",
        manager: "主管",
        engineer: "维修工程师",
        operator: "操作员",
        user: "普通用户"
      };
      return roleMap[role] || role || "普通用户";
    },
    // 显示错误提示
    showError(message) {
      common_vendor.index.showToast({
        title: message,
        icon: "none"
      });
    },
    // 更新头像
    updateAvatar() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFilePaths = res.tempFilePaths;
          common_vendor.index.showLoading({
            title: "上传中...",
            mask: true
          });
          this.isUploading = true;
          const token = common_vendor.index.getStorageSync("token");
          if (!token) {
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "请先登录",
              icon: "none"
            });
            return;
          }
          utils_upload.uploadUtils.uploadImage(tempFilePaths[0]).then((serverPath) => {
            common_vendor.index.__f__("log", "at pages/user/info.vue:301", "头像上传成功，服务器路径:", serverPath);
            this.userInfo.avatar = tempFilePaths[0];
            return utils_api.userApi.updateUserInfo({
              avatar: "/uploads" + serverPath
            });
          }).then((res2) => {
            var _a;
            if (res2.code === 200) {
              const userInfo = common_vendor.index.getStorageSync("userInfo") || {};
              userInfo.avatar = ((_a = res2.data) == null ? void 0 : _a.avatar) || userInfo.avatar;
              common_vendor.index.setStorageSync("userInfo", userInfo);
              common_vendor.index.showToast({
                title: "头像更新成功",
                icon: "success"
              });
            } else {
              common_vendor.index.showToast({
                title: res2.message || "更新头像失败",
                icon: "none"
              });
            }
          }).catch((err) => {
            common_vendor.index.__f__("error", "at pages/user/info.vue:332", "上传头像失败:", err);
            common_vendor.index.showToast({
              title: "上传失败，请重试",
              icon: "none"
            });
          }).finally(() => {
            common_vendor.index.hideLoading();
            this.isUploading = false;
          });
        }
      });
    },
    // 页面导航
    navigateTo(url) {
      common_vendor.index.navigateTo({
        url
      });
    },
    // 退出登录
    handleLogout() {
      common_vendor.index.showModal({
        title: "确认退出",
        content: "确定要退出登录吗？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.removeStorageSync("token");
            common_vendor.index.removeStorageSync("userInfo");
            common_vendor.index.removeStorageSync("userId");
            common_vendor.index.removeStorageSync("userRole");
            common_vendor.index.removeStorageSync("userPermissions");
            common_vendor.index.reLaunch({
              url: "/pages/user/login"
            });
          }
        }
      });
    },
    // 获取完整的图片URL
    getFullImageUrl(url) {
      if (!url)
        return "/static/user/avatar.png";
      if (url.startsWith("http") || url.startsWith("/static/")) {
        return url;
      }
      return utils_upload.uploadUtils.getFileUrl(url);
    },
    // 加载工单统计数据
    loadWorkOrderStats() {
      common_vendor.index.__f__("log", "at pages/user/info.vue:391", "开始加载工单统计数据");
      const userRole = common_vendor.index.getStorageSync("userRole");
      common_vendor.index.__f__("log", "at pages/user/info.vue:393", "用户角色:", userRole, "是否管理员或主管:", this.isAdminOrManager);
      utils_api.workOrderApi.getWorkOrderStats().then((res) => {
        if (res.code === 200 && res.data) {
          common_vendor.index.__f__("log", "at pages/user/info.vue:398", "工单统计数据:", res.data);
          this.workStats = {
            myWorkOrders: res.data.myWorkOrders || 0,
            processingOrders: res.data.processingOrders || 0,
            pendingOrders: res.data.pendingOrders || 0
          };
          if (this.isAdminOrManager) {
            common_vendor.index.__f__("log", "at pages/user/info.vue:407", "管理员/主管视图: 我的工单、已接单工单、待接单工单");
          } else {
            common_vendor.index.__f__("log", "at pages/user/info.vue:409", "普通用户视图: 我的工单、待处理工单、待接单工单");
          }
        } else {
          common_vendor.index.__f__("warn", "at pages/user/info.vue:412", "获取工单统计数据失败:", res.message);
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/user/info.vue:416", "获取工单统计数据异常:", err);
      });
    }
  }
};
if (!Array) {
  const _component_BaseTabBar = common_vendor.resolveComponent("BaseTabBar");
  _component_BaseTabBar();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $options.getFullImageUrl($data.userInfo.avatar),
    b: common_vendor.o((...args) => $options.updateAvatar && $options.updateAvatar(...args)),
    c: common_vendor.t($data.userInfo.name),
    d: common_vendor.t($data.workStats.myWorkOrders),
    e: common_vendor.t($data.workStats.processingOrders),
    f: common_vendor.t($options.isAdminOrManager ? "已接单" : "待处理"),
    g: common_vendor.t($data.workStats.pendingOrders),
    h: common_vendor.o(($event) => $options.navigateTo("/pages/user/profile")),
    i: common_vendor.o(($event) => $options.navigateTo("/pages/user/message-settings")),
    j: common_vendor.o(($event) => $options.navigateTo("/pages/user/change-password")),
    k: common_vendor.o(($event) => $options.navigateTo("/pages/user/faq")),
    l: common_vendor.o(($event) => $options.navigateTo("/pages/user/about")),
    m: common_vendor.o((...args) => $options.handleLogout && $options.handleLogout(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/user/info.js.map
