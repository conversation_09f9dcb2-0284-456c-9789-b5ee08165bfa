{"version": 3, "file": "detail.js", "sources": ["pages/hes/detail.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaGVzL2RldGFpbC52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"hes-detail-container\">\r\n\t\t<!-- 基本信息 -->\r\n\t\t<view class=\"detail-card\">\r\n\t\t\t<view class=\"card-header\">\r\n\t\t\t\t<text class=\"card-title\">基本信息</text>\r\n\t\t\t\t<view class=\"hes-status\" :class=\"stationInfo.status\">{{ getStatusText(stationInfo.status) }}</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"info-item\">\r\n\t\t\t\t<text class=\"info-label\">换热站名称</text>\r\n\t\t\t\t<text class=\"info-value\">{{ stationInfo.name }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\">\r\n\t\t\t\t<text class=\"info-label\">换热站编号</text>\r\n\t\t\t\t<text class=\"info-value\">{{ stationInfo.id }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\">\r\n\t\t\t\t<text class=\"info-label\">所在地址</text>\r\n\t\t\t\t<text class=\"info-value\">{{ stationInfo.address }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\">\r\n\t\t\t\t<text class=\"info-label\">管理人员</text>\r\n\t\t\t\t<text class=\"info-value\">{{ stationInfo.manager || '未分配' }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\">\r\n\t\t\t\t<text class=\"info-label\">联系电话</text>\r\n\t\t\t\t<text class=\"info-value\">{{ stationInfo.phone || '未设置' }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\">\r\n\t\t\t\t<text class=\"info-label\">投入使用</text>\r\n\t\t\t\t<text class=\"info-value\">{{ formatDate(stationInfo.startDate) }}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 运行数据 -->\r\n\t\t<view class=\"detail-card\">\r\n\t\t\t<view class=\"card-header\">\r\n\t\t\t\t<text class=\"card-title\">运行数据</text>\r\n\t\t\t\t<text class=\"refresh-btn\" @click=\"refreshData\">刷新</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"data-grid\">\r\n\t\t\t\t<view class=\"data-item\" v-for=\"(item, index) in runningData\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"data-value\" :class=\"{ warning: item.isWarning, danger: item.isDanger }\">\r\n\t\t\t\t\t\t{{ item.value }}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"data-label\">{{ item.label }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"data-chart\">\r\n\t\t\t\t<view class=\"chart-header\">\r\n\t\t\t\t\t<text class=\"chart-title\">温度趋势 (24小时)</text>\r\n\t\t\t\t\t<view class=\"chart-legend\">\r\n\t\t\t\t\t\t<view class=\"legend-item\">\r\n\t\t\t\t\t\t\t<view class=\"legend-color supply\"></view>\r\n\t\t\t\t\t\t\t<text class=\"legend-text\">供水温度</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"legend-item\">\r\n\t\t\t\t\t\t\t<view class=\"legend-color return\"></view>\r\n\t\t\t\t\t\t\t<text class=\"legend-text\">回水温度</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 图表区域，实际项目中使用echart等图表库 -->\r\n\t\t\t\t<view class=\"chart-placeholder\">\r\n\t\t\t\t\t<text>温度趋势图</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 供热范围 -->\r\n\t\t<view class=\"detail-card\">\r\n\t\t\t<view class=\"card-header\">\r\n\t\t\t\t<text class=\"card-title\">供热范围</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"heating-area\">\r\n\t\t\t\t<view class=\"area-info\">\r\n\t\t\t\t\t<view class=\"area-item\">\r\n\t\t\t\t\t\t<text class=\"area-label\">供热面积</text>\r\n\t\t\t\t\t\t<text class=\"area-value\">{{ stationInfo.heatingArea || 0 }} m²</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"area-item\">\r\n\t\t\t\t\t\t<text class=\"area-label\">覆盖小区</text>\r\n\t\t\t\t\t\t<text class=\"area-value\">{{ stationInfo.coverCommunities || 0 }} 个</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"area-item\">\r\n\t\t\t\t\t\t<text class=\"area-label\">覆盖用户</text>\r\n\t\t\t\t\t\t<text class=\"area-value\">{{ stationInfo.coverUsers || 0 }} 户</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"community-list\">\r\n\t\t\t\t\t<view class=\"community-item\" v-for=\"(item, index) in communities\" :key=\"index\">\r\n\t\t\t\t\t\t<text class=\"community-name\">{{ item.name }}</text>\r\n\t\t\t\t\t\t<text class=\"community-address\">{{ item.address }}</text>\r\n\t\t\t\t\t\t<text class=\"community-units\">{{ item.buildingCount }}栋楼 {{ item.userCount }}户</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 设备列表 -->\r\n\t\t<view class=\"detail-card\">\r\n\t\t\t<view class=\"card-header\">\r\n\t\t\t\t<text class=\"card-title\">关联设备</text>\r\n\t\t\t\t<text class=\"view-all\" @click=\"navigateToDeviceList\">查看全部</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"device-list\">\r\n\t\t\t\t<view class=\"device-item\" v-for=\"(device, index) in devices\" :key=\"index\" @click=\"navigateToDeviceDetail(device.deviceId)\">\r\n\t\t\t\t\t<view class=\"device-status\" :class=\"device.status\"></view>\r\n\t\t\t\t\t<view class=\"device-info\">\r\n\t\t\t\t\t\t<view class=\"device-name\">{{ device.name }}</view>\r\n\t\t\t\t\t\t<view class=\"device-type\">{{ device.type }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"device-alarm\" v-if=\"device.alarmCount > 0\">\r\n\t\t\t\t\t\t<text class=\"alarm-count\">{{ device.alarmCount }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"iconfont icon-arrow-right\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"empty-tip\" v-if=\"devices.length === 0\">\r\n\t\t\t\t\t<text>暂无关联设备</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 告警记录 -->\r\n\t\t<view class=\"detail-card\" v-if=\"alarms.length > 0\">\r\n\t\t\t<view class=\"card-header\">\r\n\t\t\t\t<text class=\"card-title\">最近告警</text>\r\n\t\t\t\t<text class=\"view-all\" @click=\"navigateToAlarmList\">查看全部</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"alarm-list\">\r\n\t\t\t\t<view class=\"alarm-item\" v-for=\"(alarm, index) in alarms\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"alarm-level\" :class=\"alarm.level\"></view>\r\n\t\t\t\t\t<view class=\"alarm-content\">\r\n\t\t\t\t\t\t<view class=\"alarm-title\">{{ alarm.title }}</view>\r\n\t\t\t\t\t\t<view class=\"alarm-time\">{{ alarm.time }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 操作按钮 -->\r\n\t\t<view class=\"action-buttons\">\r\n\t\t\t<view class=\"action-btn\" @click=\"navigateToControl\">\r\n\t\t\t\t<text class=\"iconfont icon-control\"></text>\r\n\t\t\t\t<text>远程控制</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"action-btn\" @click=\"navigateToReport\">\r\n\t\t\t\t<text class=\"iconfont icon-chart\"></text>\r\n\t\t\t\t<text>运行报表</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"action-btn\" @click=\"navigateToMaintenance\">\r\n\t\t\t\t<text class=\"iconfont icon-maintain\"></text>\r\n\t\t\t\t<text>维护记录</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { heatingStationApi } from '@/utils/api.js';\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tstationId: '',\r\n\t\t\tstationDetail: {}, // 原始API返回的详情数据\r\n\t\t\tstationInfo: {\r\n\t\t\t\tid: '',\r\n\t\t\t\tname: '',\r\n\t\t\t\tstatus: 'normal',\r\n\t\t\t\taddress: '',\r\n\t\t\t\tmanager: '',\r\n\t\t\t\tphone: '',\r\n\t\t\t\tstartDate: '',\r\n\t\t\t\theatingArea: 0,\r\n\t\t\t\tcoverCommunities: 0,\r\n\t\t\t\tcoverUsers: 0\r\n\t\t\t},\r\n\t\t\trunningData: [],\r\n\t\t\tcommunities: [],\r\n\t\t\tdevices: [],\r\n\t\t\talarms: [],\r\n\t\t\tisLoading: false\r\n\t\t}\r\n\t},\r\n\tonLoad(options) {\r\n\t\t// 获取路由参数中的站点ID\r\n\t\tthis.stationId = options.id;\r\n\t\t\r\n\t\t// 从上一个页面获取详情数据（如果有）\r\n\t\tconst eventChannel = this.getOpenerEventChannel();\r\n\t\teventChannel.on('acceptStationDetail', (data) => {\r\n\t\t\tconsole.log('从列表页接收到详情数据:', data);\r\n\t\t\tthis.stationDetail = data;\r\n\t\t\tthis.parseStationDetail();\r\n\t\t});\r\n\t\t\r\n\t\t// 如果没有通过事件通道传递数据，则主动加载\r\n\t\tif (!this.stationDetail || !this.stationDetail.basic_info) {\r\n\t\t\tthis.loadStationDetail();\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t// 加载换热站详情\r\n\t\tloadStationDetail() {\r\n\t\t\tthis.isLoading = true;\r\n\t\t\tuni.showLoading({ title: '加载中...' });\r\n\t\t\t\r\n\t\t\theatingStationApi.getDetail(this.stationId)\r\n\t\t\t\t.then(res => {\r\n\t\t\t\t\tconsole.log('换热站详情API返回:', res);\r\n\t\t\t\t\tif (res.code === 200 && res.data) {\r\n\t\t\t\t\t\tthis.stationDetail = res.data;\r\n\t\t\t\t\t\tthis.parseStationDetail();\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.message || '获取详情失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tconsole.error('获取换热站详情失败:', err);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '获取详情失败，请重试',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t\t.finally(() => {\r\n\t\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 解析站点详情数据\r\n\t\tparseStationDetail() {\r\n\t\t\tif (!this.stationDetail) return;\r\n\t\t\t\r\n\t\t\tconst basicInfo = this.stationDetail.basic_info || {};\r\n\t\t\tconst realtimeData = this.stationDetail.realtime_data || {};\r\n\t\t\t\r\n\t\t\t// 更新基本信息\r\n\t\t\tthis.stationInfo = {\r\n\t\t\t\tid: basicInfo.id || this.stationId,\r\n\t\t\t\tname: basicInfo.name || '',\r\n\t\t\t\tstatus: basicInfo.status || 'normal',\r\n\t\t\t\taddress: basicInfo.address || '',\r\n\t\t\t\tmanager: basicInfo.manager_name || '',\r\n\t\t\t\tphone: basicInfo.contact_phone || '',\r\n\t\t\t\tstartDate: basicInfo.installation_date || '',\r\n\t\t\t\theatingArea: basicInfo.heating_area || 0,\r\n\t\t\t\tcoverCommunities: basicInfo.community_count || 0,\r\n\t\t\t\tcoverUsers: basicInfo.user_count || 0\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 更新运行数据\r\n\t\t\tthis.parseRunningData(realtimeData);\r\n\t\t\t\r\n\t\t\t// 解析设备状态\r\n\t\t\tthis.parseDevices(realtimeData?.equipment_status);\r\n\t\t\t\r\n\t\t\t// 解析告警信息\r\n\t\t\tthis.loadAlarms();\r\n\t\t\t\r\n\t\t\t// 解析小区信息\r\n\t\t\tthis.loadCommunities();\r\n\t\t},\r\n\t\t\r\n\t\t// 解析运行数据\r\n\t\tparseRunningData(realtimeData) {\r\n\t\t\tif (!realtimeData) return;\r\n\t\t\t\r\n\t\t\tconst primary = realtimeData.primary_system || {};\r\n\t\t\tconst secondary = realtimeData.secondary_system || {};\r\n\t\t\t\r\n\t\t\tthis.runningData = [\r\n\t\t\t\t{\r\n\t\t\t\t\tlabel: '一次供水温度',\r\n\t\t\t\t\tvalue: primary.supply_temp ? `${primary.supply_temp}°C` : '--',\r\n\t\t\t\t\tisWarning: primary.supply_temp > 95,\r\n\t\t\t\t\tisDanger: primary.supply_temp > 105\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tlabel: '一次回水温度',\r\n\t\t\t\t\tvalue: primary.return_temp ? `${primary.return_temp}°C` : '--',\r\n\t\t\t\t\tisWarning: primary.return_temp > 80,\r\n\t\t\t\t\tisDanger: primary.return_temp > 90\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tlabel: '二次供水温度',\r\n\t\t\t\t\tvalue: secondary.supply_temp ? `${secondary.supply_temp}°C` : '--',\r\n\t\t\t\t\tisWarning: secondary.supply_temp > 75,\r\n\t\t\t\t\tisDanger: secondary.supply_temp > 85\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tlabel: '二次回水温度',\r\n\t\t\t\t\tvalue: secondary.return_temp ? `${secondary.return_temp}°C` : '--',\r\n\t\t\t\t\tisWarning: secondary.return_temp > 65,\r\n\t\t\t\t\tisDanger: secondary.return_temp > 75\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tlabel: '一次供水压力',\r\n\t\t\t\t\tvalue: primary.supply_pressure ? `${primary.supply_pressure}MPa` : '--',\r\n\t\t\t\t\tisWarning: primary.supply_pressure > 0.8,\r\n\t\t\t\t\tisDanger: primary.supply_pressure > 1.0\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tlabel: '一次回水压力',\r\n\t\t\t\t\tvalue: primary.return_pressure ? `${primary.return_pressure}MPa` : '--',\r\n\t\t\t\t\tisWarning: primary.return_pressure > 0.5,\r\n\t\t\t\t\tisDanger: primary.return_pressure > 0.7\r\n\t\t\t\t}\r\n\t\t\t];\r\n\t\t\t\r\n\t\t\t// 可以继续添加其他运行数据\r\n\t\t\tif (primary.flow_rate) {\r\n\t\t\t\tthis.runningData.push({\r\n\t\t\t\t\tlabel: '流量',\r\n\t\t\t\t\tvalue: `${primary.flow_rate}m³/h`,\r\n\t\t\t\t\tisWarning: false,\r\n\t\t\t\t\tisDanger: false\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (primary.power) {\r\n\t\t\t\tthis.runningData.push({\r\n\t\t\t\t\tlabel: '热负荷',\r\n\t\t\t\t\tvalue: `${(primary.power / 1000).toFixed(2)}MW`,\r\n\t\t\t\t\tisWarning: primary.power > 1800,\r\n\t\t\t\t\tisDanger: primary.power > 2000\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 解析设备数据\r\n\t\tparseDevices(equipmentStatus) {\r\n\t\t\tif (!equipmentStatus) return;\r\n\t\t\t\r\n\t\t\tthis.devices = [];\r\n\t\t\t\r\n\t\t\t// 处理泵设备\r\n\t\t\tif (equipmentStatus.pumps && equipmentStatus.pumps.length > 0) {\r\n\t\t\t\tequipmentStatus.pumps.forEach((pump, index) => {\r\n\t\t\t\t\tthis.devices.push({\r\n\t\t\t\t\t\tdeviceId: pump.id || `pump_${index + 1}`,\r\n\t\t\t\t\t\tname: pump.name || `水泵 ${index + 1}`,\r\n\t\t\t\t\t\ttype: '泵',\r\n\t\t\t\t\t\tstatus: pump.status === 'running' ? 'online' : (pump.status === 'fault' ? 'fault' : 'offline'),\r\n\t\t\t\t\t\talarmCount: 0 // 告警数可以从另外的接口获取\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 处理阀门设备\r\n\t\t\tif (equipmentStatus.valves && equipmentStatus.valves.length > 0) {\r\n\t\t\t\tequipmentStatus.valves.forEach((valve, index) => {\r\n\t\t\t\t\tthis.devices.push({\r\n\t\t\t\t\t\tdeviceId: valve.id || `valve_${index + 1}`,\r\n\t\t\t\t\t\tname: valve.name || `阀门 ${index + 1}`,\r\n\t\t\t\t\t\ttype: '阀门',\r\n\t\t\t\t\t\tstatus: valve.status === 'normal' ? 'online' : 'fault',\r\n\t\t\t\t\t\talarmCount: 0\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 加载社区列表\r\n\t\tloadCommunities() {\r\n\t\t\t// 如果API中包含社区信息，可以从API数据中解析\r\n\t\t\t// 暂时使用模拟数据，实际项目中应该调用API\r\n\t\t\tif (this.stationDetail && this.stationDetail.communities) {\r\n\t\t\t\tthis.communities = this.stationDetail.communities.map(item => ({\r\n\t\t\t\t\tid: item.id,\r\n\t\t\t\t\tname: item.name,\r\n\t\t\t\t\taddress: item.address,\r\n\t\t\t\t\tbuildingCount: item.building_count,\r\n\t\t\t\t\tuserCount: item.user_count\r\n\t\t\t\t}));\r\n\t\t\t} else {\r\n\t\t\t\t// 如果需要单独获取社区数据，可以在这里调用相关API\r\n\t\t\t\t// 例如: heatingStationApi.getCommunities(this.stationId)\r\n\t\t\t\t//       .then(res => { this.communities = res.data; })\r\n\t\t\t\tthis.communities = [];\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 加载告警记录\r\n\t\tloadAlarms() {\r\n\t\t\t// 调用告警API获取最新告警\r\n\t\t\theatingStationApi.getAlarmList({ hes_id: this.stationId, limit: 5 })\r\n\t\t\t\t.then(res => {\r\n\t\t\t\t\tif (res.code === 200 && res.data && res.data.list) {\r\n\t\t\t\t\t\tthis.alarms = res.data.list.map(alarm => ({\r\n\t\t\t\t\t\t\tid: alarm.id,\r\n\t\t\t\t\t\t\ttitle: alarm.alarm_name,\r\n\t\t\t\t\t\t\tlevel: this.getAlarmLevel(alarm.level),\r\n\t\t\t\t\t\t\ttime: alarm.start_time,\r\n\t\t\t\t\t\t\tdeviceId: alarm.device_id\r\n\t\t\t\t\t\t}));\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tconsole.error('获取告警列表失败:', err);\r\n\t\t\t\t\tthis.alarms = [];\r\n\t\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 获取告警级别\r\n\t\tgetAlarmLevel(level) {\r\n\t\t\tif (level === 'urgent' || level === 'critical') return 'error';\r\n\t\t\tif (level === 'warning' || level === 'notice') return 'warning';\r\n\t\t\treturn 'warning'; // 默认警告级别\r\n\t\t},\r\n\t\t\r\n\t\t// 刷新数据\r\n\t\trefreshData() {\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '刷新中...'\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\tthis.loadStationDetail();\r\n\t\t},\r\n\t\t\r\n\t\t// 获取状态文本\r\n\t\tgetStatusText(status) {\r\n\t\t\tconst statusMap = {\r\n\t\t\t\t'online': '在线',\r\n\t\t\t\t'offline': '离线',\r\n\t\t\t\t'normal': '正常',\r\n\t\t\t\t'warning': '异常',\r\n\t\t\t\t'fault': '故障'\r\n\t\t\t};\r\n\t\t\treturn statusMap[status] || '未知';\r\n\t\t},\r\n\t\t\r\n\t\t// 格式化日期\r\n\t\tformatDate(dateString) {\r\n\t\t\tif (!dateString) return '未知';\r\n\t\t\t\r\n\t\t\ttry {\r\n\t\t\t\tconst date = new Date(dateString);\r\n\t\t\t\treturn `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.error('日期格式化错误:', e);\r\n\t\t\t\treturn dateString;\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 导航到设备列表\r\n\t\tnavigateToDeviceList() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/device/list?stationId=${this.stationId}`\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 导航到设备详情\r\n\t\tnavigateToDeviceDetail(deviceId) {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/device/detail?id=${deviceId}`\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 导航到告警列表\r\n\t\tnavigateToAlarmList() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/hes/alarms?id=${this.stationId}`\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 导航到远程控制页面\r\n\t\tnavigateToControl() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/hes/control?id=${this.stationId}`\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 导航到运行报表页面\r\n\t\tnavigateToReport() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/hes/report?id=${this.stationId}`\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 导航到维护记录页面\r\n\t\tnavigateToMaintenance() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/hes/maintenance?id=${this.stationId}`\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.hes-detail-container {\r\n\t\tpadding: 20rpx;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tmin-height: 100vh;\r\n\t}\r\n\t\r\n\t.detail-card {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 12rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\r\n\t\t\r\n\t\t.card-header {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin-bottom: 24rpx;\r\n\t\t\t\r\n\t\t\t.card-title {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tpadding-left: 20rpx;\r\n\t\t\t\t\r\n\t\t\t\t&::before {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t\ttop: 8rpx;\r\n\t\t\t\t\twidth: 6rpx;\r\n\t\t\t\t\theight: 28rpx;\r\n\t\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\t\tborder-radius: 3rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.hes-status {\r\n\t\t\t\tpadding: 4rpx 16rpx;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\r\n\t\t\t\t&.normal {\r\n\t\t\t\t\tbackground-color: rgba(82, 196, 26, 0.1);\r\n\t\t\t\t\tcolor: $uni-color-success;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&.warning {\r\n\t\t\t\t\tbackground-color: rgba(250, 173, 20, 0.1);\r\n\t\t\t\t\tcolor: $uni-color-warning;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&.fault {\r\n\t\t\t\t\tbackground-color: rgba(245, 34, 45, 0.1);\r\n\t\t\t\t\tcolor: $uni-color-error;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.refresh-btn, .view-all {\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.info-item {\r\n\t\tdisplay: flex;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\t\r\n\t\t.info-label {\r\n\t\t\twidth: 180rpx;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: $uni-text-color-grey;\r\n\t\t}\r\n\t\t\r\n\t\t.info-value {\r\n\t\t\tflex: 1;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: $uni-text-color;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.data-grid {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\t\r\n\t\t.data-item {\r\n\t\t\twidth: 33.33%;\r\n\t\t\ttext-align: center;\r\n\t\t\tmargin-bottom: 30rpx;\r\n\t\t\t\r\n\t\t\t.data-value {\r\n\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t\r\n\t\t\t\t&.warning {\r\n\t\t\t\t\tcolor: $uni-color-warning;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&.danger {\r\n\t\t\t\t\tcolor: $uni-color-error;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.data-label {\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.data-chart {\r\n\t\tborder-top: 1rpx solid #eee;\r\n\t\tpadding-top: 20rpx;\r\n\t\t\r\n\t\t.chart-header {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\r\n\t\t\t.chart-title {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.chart-legend {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\t\r\n\t\t\t\t.legend-item {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.legend-color {\r\n\t\t\t\t\t\twidth: 20rpx;\r\n\t\t\t\t\t\theight: 6rpx;\r\n\t\t\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&.supply {\r\n\t\t\t\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&.return {\r\n\t\t\t\t\t\t\tbackground-color: $uni-color-warning;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.legend-text {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.chart-placeholder {\r\n\t\t\theight: 300rpx;\r\n\t\t\tbackground-color: #f8f8f8;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\t\r\n\t\t\ttext {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.heating-area {\r\n\t\t.area-info {\r\n\t\t\tdisplay: flex;\r\n\t\t\tmargin-bottom: 30rpx;\r\n\t\t\t\r\n\t\t\t.area-item {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\t\r\n\t\t\t\t.area-label {\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.area-value {\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.community-list {\r\n\t\t\tborder-top: 1rpx solid #eee;\r\n\t\t\tpadding-top: 20rpx;\r\n\t\t\t\r\n\t\t\t.community-item {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tpadding: 20rpx 0;\r\n\t\t\t\tborder-bottom: 1rpx solid #eee;\r\n\t\t\t\t\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tborder-bottom: none;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.community-name {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.community-address {\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.community-units {\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.device-list {\r\n\t\t.device-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 20rpx 0;\r\n\t\t\tborder-bottom: 1rpx solid #eee;\r\n\t\t\t\r\n\t\t\t&:last-child {\r\n\t\t\t\tborder-bottom: none;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.device-status {\r\n\t\t\t\twidth: 12rpx;\r\n\t\t\t\theight: 12rpx;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tmargin-right: 16rpx;\r\n\t\t\t\t\r\n\t\t\t\t&.online {\r\n\t\t\t\t\tbackground-color: $uni-color-success;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&.offline {\r\n\t\t\t\t\tbackground-color: $uni-text-color-grey;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&.fault {\r\n\t\t\t\t\tbackground-color: $uni-color-error;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.device-info {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\t\r\n\t\t\t\t.device-name {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t\tmargin-bottom: 4rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.device-type {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.device-alarm {\r\n\t\t\t\tbackground-color: $uni-color-error;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\tpadding: 0 10rpx;\r\n\t\t\t\tmargin-right: 16rpx;\r\n\t\t\t\t\r\n\t\t\t\t.alarm-count {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.iconfont {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.empty-tip {\r\n\t\t\tpadding: 40rpx 0;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: $uni-text-color-grey;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.alarm-list {\r\n\t\t.alarm-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 16rpx 0;\r\n\t\t\tborder-bottom: 1rpx solid #eee;\r\n\t\t\t\r\n\t\t\t&:last-child {\r\n\t\t\t\tborder-bottom: none;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.alarm-level {\r\n\t\t\t\twidth: 12rpx;\r\n\t\t\t\theight: 12rpx;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tmargin-right: 16rpx;\r\n\t\t\t\t\r\n\t\t\t\t&.warning {\r\n\t\t\t\t\tbackground-color: $uni-color-warning;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&.error {\r\n\t\t\t\t\tbackground-color: $uni-color-error;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.alarm-content {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\t\r\n\t\t\t\t.alarm-title {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t\tmargin-bottom: 4rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.alarm-time {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.action-buttons {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: #fff;\r\n\t\tdisplay: flex;\r\n\t\tpadding: 20rpx 30rpx;\r\n\t\tbox-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);\r\n\t\t\r\n\t\t.action-btn {\r\n\t\t\tflex: 1;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t\tcolor: $uni-color-primary;\r\n\t\t\t\r\n\t\t\t.iconfont {\r\n\t\t\t\tfont-size: 48rpx;\r\n\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\ttext {\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style> ", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/hes/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "heatingStationApi"], "mappings": ";;;AA0KA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,WAAW;AAAA,MACX,eAAe,CAAE;AAAA;AAAA,MACjB,aAAa;AAAA,QACZ,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,SAAS;AAAA,QACT,OAAO;AAAA,QACP,WAAW;AAAA,QACX,aAAa;AAAA,QACb,kBAAkB;AAAA,QAClB,YAAY;AAAA,MACZ;AAAA,MACD,aAAa,CAAE;AAAA,MACf,aAAa,CAAE;AAAA,MACf,SAAS,CAAE;AAAA,MACX,QAAQ,CAAE;AAAA,MACV,WAAW;AAAA,IACZ;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AAEf,SAAK,YAAY,QAAQ;AAGzB,UAAM,eAAe,KAAK;AAC1B,iBAAa,GAAG,uBAAuB,CAAC,SAAS;AAChDA,oBAAA,MAAA,MAAA,OAAA,+BAAY,gBAAgB,IAAI;AAChC,WAAK,gBAAgB;AACrB,WAAK,mBAAkB;AAAA,IACxB,CAAC;AAGD,QAAI,CAAC,KAAK,iBAAiB,CAAC,KAAK,cAAc,YAAY;AAC1D,WAAK,kBAAiB;AAAA,IACvB;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,oBAAoB;AACnB,WAAK,YAAY;AACjBA,oBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AAEnCC,kCAAkB,UAAU,KAAK,SAAS,EACxC,KAAK,SAAO;AACZD,sBAAA,MAAA,MAAA,OAAA,+BAAY,eAAe,GAAG;AAC9B,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AACjC,eAAK,gBAAgB,IAAI;AACzB,eAAK,mBAAkB;AAAA,eACjB;AACNA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,IAAI,WAAW;AAAA,YACtB,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,OACA,EACA,MAAM,SAAO;AACbA,sBAAA,MAAA,MAAA,SAAA,+BAAc,cAAc,GAAG;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,OACD,EACA,QAAQ,MAAM;AACd,aAAK,YAAY;AACjBA,sBAAG,MAAC,YAAW;AAAA,MAChB,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,qBAAqB;AACpB,UAAI,CAAC,KAAK;AAAe;AAEzB,YAAM,YAAY,KAAK,cAAc,cAAc,CAAA;AACnD,YAAM,eAAe,KAAK,cAAc,iBAAiB,CAAA;AAGzD,WAAK,cAAc;AAAA,QAClB,IAAI,UAAU,MAAM,KAAK;AAAA,QACzB,MAAM,UAAU,QAAQ;AAAA,QACxB,QAAQ,UAAU,UAAU;AAAA,QAC5B,SAAS,UAAU,WAAW;AAAA,QAC9B,SAAS,UAAU,gBAAgB;AAAA,QACnC,OAAO,UAAU,iBAAiB;AAAA,QAClC,WAAW,UAAU,qBAAqB;AAAA,QAC1C,aAAa,UAAU,gBAAgB;AAAA,QACvC,kBAAkB,UAAU,mBAAmB;AAAA,QAC/C,YAAY,UAAU,cAAc;AAAA;AAIrC,WAAK,iBAAiB,YAAY;AAGlC,WAAK,aAAa,6CAAc,gBAAgB;AAGhD,WAAK,WAAU;AAGf,WAAK,gBAAe;AAAA,IACpB;AAAA;AAAA,IAGD,iBAAiB,cAAc;AAC9B,UAAI,CAAC;AAAc;AAEnB,YAAM,UAAU,aAAa,kBAAkB;AAC/C,YAAM,YAAY,aAAa,oBAAoB;AAEnD,WAAK,cAAc;AAAA,QAClB;AAAA,UACC,OAAO;AAAA,UACP,OAAO,QAAQ,cAAc,GAAG,QAAQ,WAAW,OAAO;AAAA,UAC1D,WAAW,QAAQ,cAAc;AAAA,UACjC,UAAU,QAAQ,cAAc;AAAA,QAChC;AAAA,QACD;AAAA,UACC,OAAO;AAAA,UACP,OAAO,QAAQ,cAAc,GAAG,QAAQ,WAAW,OAAO;AAAA,UAC1D,WAAW,QAAQ,cAAc;AAAA,UACjC,UAAU,QAAQ,cAAc;AAAA,QAChC;AAAA,QACD;AAAA,UACC,OAAO;AAAA,UACP,OAAO,UAAU,cAAc,GAAG,UAAU,WAAW,OAAO;AAAA,UAC9D,WAAW,UAAU,cAAc;AAAA,UACnC,UAAU,UAAU,cAAc;AAAA,QAClC;AAAA,QACD;AAAA,UACC,OAAO;AAAA,UACP,OAAO,UAAU,cAAc,GAAG,UAAU,WAAW,OAAO;AAAA,UAC9D,WAAW,UAAU,cAAc;AAAA,UACnC,UAAU,UAAU,cAAc;AAAA,QAClC;AAAA,QACD;AAAA,UACC,OAAO;AAAA,UACP,OAAO,QAAQ,kBAAkB,GAAG,QAAQ,eAAe,QAAQ;AAAA,UACnE,WAAW,QAAQ,kBAAkB;AAAA,UACrC,UAAU,QAAQ,kBAAkB;AAAA,QACpC;AAAA,QACD;AAAA,UACC,OAAO;AAAA,UACP,OAAO,QAAQ,kBAAkB,GAAG,QAAQ,eAAe,QAAQ;AAAA,UACnE,WAAW,QAAQ,kBAAkB;AAAA,UACrC,UAAU,QAAQ,kBAAkB;AAAA,QACrC;AAAA;AAID,UAAI,QAAQ,WAAW;AACtB,aAAK,YAAY,KAAK;AAAA,UACrB,OAAO;AAAA,UACP,OAAO,GAAG,QAAQ,SAAS;AAAA,UAC3B,WAAW;AAAA,UACX,UAAU;AAAA,QACX,CAAC;AAAA,MACF;AAEA,UAAI,QAAQ,OAAO;AAClB,aAAK,YAAY,KAAK;AAAA,UACrB,OAAO;AAAA,UACP,OAAO,IAAI,QAAQ,QAAQ,KAAM,QAAQ,CAAC,CAAC;AAAA,UAC3C,WAAW,QAAQ,QAAQ;AAAA,UAC3B,UAAU,QAAQ,QAAQ;AAAA,QAC3B,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,aAAa,iBAAiB;AAC7B,UAAI,CAAC;AAAiB;AAEtB,WAAK,UAAU;AAGf,UAAI,gBAAgB,SAAS,gBAAgB,MAAM,SAAS,GAAG;AAC9D,wBAAgB,MAAM,QAAQ,CAAC,MAAM,UAAU;AAC9C,eAAK,QAAQ,KAAK;AAAA,YACjB,UAAU,KAAK,MAAM,QAAQ,QAAQ,CAAC;AAAA,YACtC,MAAM,KAAK,QAAQ,MAAM,QAAQ,CAAC;AAAA,YAClC,MAAM;AAAA,YACN,QAAQ,KAAK,WAAW,YAAY,WAAY,KAAK,WAAW,UAAU,UAAU;AAAA,YACpF,YAAY;AAAA;AAAA,UACb,CAAC;AAAA,QACF,CAAC;AAAA,MACF;AAGA,UAAI,gBAAgB,UAAU,gBAAgB,OAAO,SAAS,GAAG;AAChE,wBAAgB,OAAO,QAAQ,CAAC,OAAO,UAAU;AAChD,eAAK,QAAQ,KAAK;AAAA,YACjB,UAAU,MAAM,MAAM,SAAS,QAAQ,CAAC;AAAA,YACxC,MAAM,MAAM,QAAQ,MAAM,QAAQ,CAAC;AAAA,YACnC,MAAM;AAAA,YACN,QAAQ,MAAM,WAAW,WAAW,WAAW;AAAA,YAC/C,YAAY;AAAA,UACb,CAAC;AAAA,QACF,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,kBAAkB;AAGjB,UAAI,KAAK,iBAAiB,KAAK,cAAc,aAAa;AACzD,aAAK,cAAc,KAAK,cAAc,YAAY,IAAI,WAAS;AAAA,UAC9D,IAAI,KAAK;AAAA,UACT,MAAM,KAAK;AAAA,UACX,SAAS,KAAK;AAAA,UACd,eAAe,KAAK;AAAA,UACpB,WAAW,KAAK;AAAA,QAChB,EAAC;AAAA,aACI;AAIN,aAAK,cAAc;MACpB;AAAA,IACA;AAAA;AAAA,IAGD,aAAa;AAEZC,gBAAiB,kBAAC,aAAa,EAAE,QAAQ,KAAK,WAAW,OAAO,GAAG,EACjE,KAAK,SAAO;AACZ,YAAI,IAAI,SAAS,OAAO,IAAI,QAAQ,IAAI,KAAK,MAAM;AAClD,eAAK,SAAS,IAAI,KAAK,KAAK,IAAI,YAAU;AAAA,YACzC,IAAI,MAAM;AAAA,YACV,OAAO,MAAM;AAAA,YACb,OAAO,KAAK,cAAc,MAAM,KAAK;AAAA,YACrC,MAAM,MAAM;AAAA,YACZ,UAAU,MAAM;AAAA,UAChB,EAAC;AAAA,QACH;AAAA,OACA,EACA,MAAM,SAAO;AACbD,0EAAc,aAAa,GAAG;AAC9B,aAAK,SAAS;MACf,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,cAAc,OAAO;AACpB,UAAI,UAAU,YAAY,UAAU;AAAY,eAAO;AACvD,UAAI,UAAU,aAAa,UAAU;AAAU,eAAO;AACtD,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,cAAc;AACbA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACR,CAAC;AAED,WAAK,kBAAiB;AAAA,IACtB;AAAA;AAAA,IAGD,cAAc,QAAQ;AACrB,YAAM,YAAY;AAAA,QACjB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW;AAAA,QACX,SAAS;AAAA;AAEV,aAAO,UAAU,MAAM,KAAK;AAAA,IAC5B;AAAA;AAAA,IAGD,WAAW,YAAY;AACtB,UAAI,CAAC;AAAY,eAAO;AAExB,UAAI;AACH,cAAM,OAAO,IAAI,KAAK,UAAU;AAChC,eAAO,GAAG,KAAK,YAAW,CAAE,IAAI,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,KAAK,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,MACxH,SAAS,GAAG;AACXA,0EAAc,YAAY,CAAC;AAC3B,eAAO;AAAA,MACR;AAAA,IACA;AAAA;AAAA,IAGD,uBAAuB;AACtBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,gCAAgC,KAAK,SAAS;AAAA,MACpD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,uBAAuB,UAAU;AAChCA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,2BAA2B,QAAQ;AAAA,MACzC,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,sBAAsB;AACrBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,wBAAwB,KAAK,SAAS;AAAA,MAC5C,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB;AACnBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,yBAAyB,KAAK,SAAS;AAAA,MAC7C,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,mBAAmB;AAClBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,wBAAwB,KAAK,SAAS;AAAA,MAC5C,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,wBAAwB;AACvBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,6BAA6B,KAAK,SAAS;AAAA,MACjD,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnfA,GAAG,WAAW,eAAe;"}