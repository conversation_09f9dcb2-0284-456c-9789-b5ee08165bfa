<view class="account-binding-container"><view class="page-header"><text class="page-title">账号绑定</text></view><view class="binding-card"><view class="binding-item"><view class="binding-icon phone-icon"><text class="iconfont icon-phone"></text></view><view class="binding-info"><text class="binding-name">手机号</text><text wx:if="{{a}}" class="binding-status">已绑定 ({{b}})</text><text wx:else class="binding-status not-bound">未绑定</text></view><view class="binding-action"><text class="action-btn" bindtap="{{d}}">{{c}}</text></view></view><view class="binding-item"><view class="binding-icon email-icon"><text class="iconfont icon-email"></text></view><view class="binding-info"><text class="binding-name">邮箱</text><text wx:if="{{e}}" class="binding-status">已绑定 ({{f}})</text><text wx:else class="binding-status not-bound">未绑定</text></view><view class="binding-action"><text class="action-btn" bindtap="{{h}}">{{g}}</text></view></view><view class="binding-item"><view class="binding-icon wechat-icon"><text class="iconfont icon-wechat"></text></view><view class="binding-info"><text class="binding-name">微信</text><text wx:if="{{i}}" class="binding-status">已绑定 ({{j}})</text><text wx:else class="binding-status not-bound">未绑定</text></view><view class="binding-action"><text class="action-btn" bindtap="{{l}}">{{k}}</text></view></view><view class="binding-item"><view class="binding-icon dingtalk-icon"><text class="iconfont icon-dingtalk"></text></view><view class="binding-info"><text class="binding-name">钉钉</text><text wx:if="{{m}}" class="binding-status">已绑定 ({{n}})</text><text wx:else class="binding-status not-bound">未绑定</text></view><view class="binding-action"><text class="action-btn" bindtap="{{p}}">{{o}}</text></view></view></view><view class="security-section"><view class="security-title"><text class="title-text">安全提示</text></view><view class="security-tips"><view class="tip-item"><text class="tip-marker">•</text><text class="tip-text">绑定手机号和邮箱可用于接收通知和找回密码</text></view><view class="tip-item"><text class="tip-marker">•</text><text class="tip-text">绑定第三方账号可快速登录系统</text></view><view class="tip-item"><text class="tip-marker">•</text><text class="tip-text">为保障账号安全，建议至少绑定手机号</text></view></view></view><uni-popup wx:if="{{z}}" class="r" u-s="{{['d']}}" u-r="phonePopup" u-i="628d1c37-0" bind:__l="__l" u-p="{{z}}"><view class="popup-content"><view class="popup-title">绑定手机号</view><view class="popup-form"><view class="form-item"><input type="number" placeholder="请输入手机号码" maxlength="11" value="{{q}}" bindinput="{{r}}"/></view><view class="form-item code-item"><input type="number" placeholder="请输入验证码" maxlength="6" value="{{s}}" bindinput="{{t}}"/><text class="send-code-btn" bindtap="{{v}}">获取验证码</text></view></view><view class="popup-actions"><text class="cancel-btn" bindtap="{{w}}">取消</text><text class="confirm-btn" bindtap="{{x}}">确定</text></view></view></uni-popup><uni-popup wx:if="{{I}}" class="r" u-s="{{['d']}}" u-r="emailPopup" u-i="628d1c37-1" bind:__l="__l" u-p="{{I}}"><view class="popup-content"><view class="popup-title">绑定邮箱</view><view class="popup-form"><view class="form-item"><input type="text" placeholder="请输入邮箱地址" value="{{A}}" bindinput="{{B}}"/></view><view class="form-item code-item"><input type="number" placeholder="请输入验证码" maxlength="6" value="{{C}}" bindinput="{{D}}"/><text class="send-code-btn" bindtap="{{E}}">获取验证码</text></view></view><view class="popup-actions"><text class="cancel-btn" bindtap="{{F}}">取消</text><text class="confirm-btn" bindtap="{{G}}">确定</text></view></view></uni-popup></view>