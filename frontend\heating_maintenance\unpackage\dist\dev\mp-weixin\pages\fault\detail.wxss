/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.fault-detail-container {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.detail-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.detail-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.detail-card .card-header .card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  position: relative;
  padding-left: 20rpx;
}
.detail-card .card-header .card-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background-color: #1890ff;
  border-radius: 4rpx;
}
.detail-card .card-header .fault-status, .detail-card .card-header .work-order-status {
  padding: 6rpx 20rpx;
  border-radius: 4rpx;
  font-size: 24rpx;
}
.detail-card .card-header .fault-status.pending, .detail-card .card-header .work-order-status.pending {
  background-color: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}
.detail-card .card-header .fault-status.confirmed, .detail-card .card-header .work-order-status.confirmed {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}
.detail-card .card-header .fault-status.rejected, .detail-card .card-header .work-order-status.rejected {
  background-color: rgba(245, 34, 45, 0.1);
  color: #f5222d;
}
.detail-card .card-header .fault-status.in_progress, .detail-card .card-header .work-order-status.in_progress {
  background-color: rgba(250, 173, 20, 0.1);
  color: #faad14;
}
.detail-card .card-header .fault-status.completed, .detail-card .card-header .work-order-status.completed {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}
.detail-card .info-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.detail-card .info-group .info-item {
  display: flex;
  align-items: flex-start;
}
.detail-card .info-group .info-item .info-label {
  min-width: 160rpx;
  font-size: 28rpx;
  color: #666;
}
.detail-card .info-group .info-item .info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
}
.detail-card .info-group .info-item .info-value.level-tag {
  display: inline-block;
  padding: 4rpx 16rpx;
  border-radius: 4rpx;
}
.detail-card .info-group .info-item .info-value.level-tag.minor {
  background-color: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}
.detail-card .info-group .info-item .info-value.level-tag.normal {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}
.detail-card .info-group .info-item .info-value.level-tag.critical {
  background-color: rgba(250, 173, 20, 0.1);
  color: #faad14;
}
.detail-card .info-group .info-item .info-value.level-tag.emergency {
  background-color: rgba(245, 34, 45, 0.1);
  color: #f5222d;
}
.detail-card .fault-desc {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}
.detail-card .image-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}
.detail-card .image-grid .image-item {
  width: 33.33%;
  padding: 10rpx;
  box-sizing: border-box;
}
.detail-card .image-grid .image-item image {
  width: 100%;
  height: 200rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
}
.detail-card .video-container {
  margin-top: 20rpx;
}
.detail-card .video-container video {
  width: 100%;
  height: 400rpx;
  border-radius: 8rpx;
  background-color: #000;
}
.detail-card .video-container .video-fallback {
  width: 100%;
  height: 400rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.detail-card .video-container .video-fallback .play-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}
.detail-card .video-container .video-fallback .fallback-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}
.detail-card .video-container .video-fallback .btn-open-browser {
  background-color: #1890ff;
  color: #fff;
  font-size: 28rpx;
  padding: 10rpx 30rpx;
  border-radius: 8rpx;
  margin-top: 20rpx;
}
.detail-card .video-debug-info {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  font-size: 24rpx;
}
.detail-card .video-debug-info .debug-title {
  font-weight: bold;
  margin-bottom: 10rpx;
  display: block;
}
.detail-card .video-debug-info .debug-text {
  color: #666;
  word-break: break-all;
  display: block;
  margin-bottom: 6rpx;
}
.detail-card .btn-view-order {
  margin-top: 20rpx;
  background-color: #f0f8ff;
  border: 1rpx solid #1890ff;
  border-radius: 8rpx;
  padding: 16rpx 0;
  text-align: center;
}
.detail-card .btn-view-order text {
  color: #1890ff;
  font-size: 28rpx;
}
.action-buttons {
  display: flex;
  gap: 30rpx;
  margin-top: 50rpx;
  margin-bottom: 30rpx;
}
.action-buttons button {
  flex: 1;
  margin: 0;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 30rpx;
  border-radius: 8rpx;
}
.action-buttons button.btn-confirm {
  background-color: #1890ff;
  color: #fff;
}
.action-buttons button.btn-reject {
  background-color: #fff;
  color: #f5222d;
  border: 1rpx solid #f5222d;
}
.status-tip {
  background-color: rgba(245, 34, 45, 0.1);
  padding: 20rpx 30rpx;
  border-radius: 8rpx;
  margin: 30rpx 0;
}
.status-tip text {
  font-size: 28rpx;
  color: #f5222d;
}

/* 操作日志样式 - 时间轴效果 */
.timeline {
  position: relative;
  padding: 20rpx 0;
}
.timeline .log-item {
  position: relative;
  padding-left: 40rpx;
  margin-bottom: 30rpx;
}
.timeline .log-item:last-child {
  margin-bottom: 0;
}
.timeline .log-item .timeline-dot {
  position: absolute;
  left: 0;
  top: 16rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #666;
  z-index: 2;
}
.timeline .log-item .timeline-dot.dot-report {
  background-color: #1890ff;
}
.timeline .log-item .timeline-dot.dot-confirm {
  background-color: #52c41a;
}
.timeline .log-item .timeline-dot.dot-reject {
  background-color: #f5222d;
}
.timeline .log-item .timeline-dot.dot-repair {
  background-color: #faad14;
}
.timeline .log-item .timeline-dot.dot-complete {
  background-color: #52c41a;
}
.timeline .log-item .timeline-dot.dot-assign {
  background-color: #8a2be2;
}
.timeline .log-item .timeline-line {
  position: absolute;
  left: 9rpx;
  top: 36rpx;
  width: 2rpx;
  height: calc(100% + 10rpx);
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 1;
}
.timeline .log-item .log-content {
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 8rpx;
  padding: 16rpx;
}
.timeline .log-item .log-content .log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}
.timeline .log-item .log-content .log-header .log-type {
  font-size: 28rpx;
  font-weight: 500;
  color: #1890ff;
}
.timeline .log-item .log-content .log-header .log-time {
  font-size: 24rpx;
  color: #666;
}
.timeline .log-item .log-content .log-body {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}
.timeline .log-item .log-content .log-body .log-desc {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
}
.timeline .log-item .log-content .log-body .log-operator {
  font-size: 24rpx;
  color: #666;
  margin-top: 4rpx;
}