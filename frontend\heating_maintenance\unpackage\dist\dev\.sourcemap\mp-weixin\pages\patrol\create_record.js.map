{"version": 3, "file": "create_record.js", "sources": ["pages/patrol/create_record.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcGF0cm9sL2NyZWF0ZV9yZWNvcmQudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"container\"> \r\n    <!-- 添加页面标题 -->\r\n    <view class=\"page-title\">\r\n      <text>创建巡检记录</text>\r\n    </view>\r\n    \r\n    <!-- 表单内容区域 -->\r\n    <scroll-view scroll-y class=\"form-container\">\r\n      <!-- 巡检计划选择 -->\r\n      <view class=\"form-group\">\r\n        <text class=\"form-label required\">巡检计划</text>\r\n        <view class=\"form-input select-box\" @click=\"showPlanSelector\">\r\n          <text v-if=\"formData.patrol_plan_id\">{{ selectedPlanName }}</text>\r\n          <text v-else class=\"placeholder\">请选择巡检计划</text>\r\n          <text class=\"select-arrow\">▼</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 执行人选择 -->\r\n      <view class=\"form-group\">\r\n        <text class=\"form-label required\">执行人</text>\r\n        <view class=\"form-input select-box\" @click=\"showExecutorSelector\">\r\n          <text v-if=\"formData.executor_ids && formData.executor_ids.length > 0\">\r\n            已选择 {{ formData.executor_ids.length }} 人{{ selectedExecutorNames ? '：' + selectedExecutorNames : '' }}\r\n          </text>\r\n          <text v-else class=\"placeholder\">请选择执行人</text>\r\n          <text class=\"select-arrow\">▼</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 开始时间 -->\r\n      <view class=\"form-group\">\r\n        <text class=\"form-label required\">开始时间</text>\r\n        <view class=\"form-input select-box\" @click=\"showStartTimePicker\">\r\n          <text v-if=\"formData.start_time\">{{ formData.start_time }}</text>\r\n          <text v-else class=\"placeholder\">请选择开始时间</text>\r\n          <text class=\"select-arrow\">▼</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 结束时间 -->\r\n      <view class=\"form-group\">\r\n        <text class=\"form-label required\">结束时间</text>\r\n        <view class=\"form-input select-box\" @click=\"showEndTimePicker\">\r\n          <text v-if=\"formData.end_time\">{{ formData.end_time }}</text>\r\n          <text v-else class=\"placeholder\">请选择结束时间</text>\r\n          <text class=\"select-arrow\">▼</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 记录状态 -->\r\n      <view class=\"form-group\">\r\n        <text class=\"form-label required\">记录状态</text>\r\n        <view class=\"status-group\">\r\n          <view \r\n            v-for=\"status in statusOptions\" \r\n            :key=\"status.value\" \r\n            class=\"status-option\" \r\n            :class=\"{ active: formData.status === status.value }\"\r\n            @click=\"formData.status = status.value\"\r\n          >\r\n            <text class=\"status-dot\"></text>\r\n            <text class=\"status-text\">{{ status.label }}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 备注信息 -->\r\n      <view class=\"form-group\">\r\n        <text class=\"form-label\">备注信息</text>\r\n        <textarea \r\n          class=\"form-textarea\" \r\n          v-model=\"formData.remark\" \r\n          placeholder=\"请输入巡检记录备注信息\" \r\n          maxlength=\"200\"\r\n        ></textarea>\r\n        <text class=\"char-count\">{{ formData.remark.length }}/200</text>\r\n      </view>\r\n      \r\n      <!-- 巡检结果 -->\r\n      <view class=\"form-group\" v-if=\"formData.patrol_results.length > 0\">\r\n        <text class=\"form-label\">巡检结果</text>\r\n        <view class=\"results-list\">\r\n          <view \r\n            class=\"result-item\" \r\n            v-for=\"(result, index) in formData.patrol_results\" \r\n            :key=\"index\"\r\n            @click=\"editPatrolResult(index)\"\r\n          >\r\n            <view class=\"result-header\">\r\n              <text class=\"result-title\">{{ result.item_info.itemName }}</text>\r\n              <view class=\"result-status\" :class=\"result.check_result\">\r\n                {{ result.check_result === 'normal' ? '正常' : '异常' }}\r\n              </view>\r\n            </view>\r\n            <view class=\"result-body\">\r\n              <view class=\"result-row device-info\">\r\n                <text class=\"result-label\">设备:</text>\r\n                <text class=\"result-value\">{{ result.item_info.deviceName || '未知设备' }}</text>\r\n              </view>\r\n              <view class=\"result-row\" v-if=\"result.item_info.normalRange\">\r\n                <text class=\"result-label\">正常范围:</text>\r\n                <text class=\"result-value\">{{ result.item_info.normalRange }}</text>\r\n              </view>\r\n              <view class=\"result-row\" v-if=\"result.param_value\">\r\n                <text class=\"result-label\">参数值:</text>\r\n                <text class=\"result-value\">{{ result.param_value }}{{ result.item_info.unit ? ' ' + result.item_info.unit : '' }}</text>\r\n              </view>\r\n              <view class=\"result-row\" v-if=\"result.description\">\r\n                <text class=\"result-label\">描述:</text>\r\n                <text class=\"result-value\">{{ result.description }}</text>\r\n              </view>\r\n              <view class=\"result-row\" v-if=\"result.images && result.images.length > 0\">\r\n                <text class=\"result-label\">照片:</text>\r\n                <text class=\"result-value\">已上传 {{ result.images.length }} 张</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 提交按钮 -->\r\n      <view class=\"submit-section\">\r\n        <button \r\n          class=\"submit-btn\" \r\n          :class=\"{ disabled: submitting }\" \r\n          :disabled=\"submitting\"\r\n          @click=\"submitRecord\"\r\n        >\r\n          {{ submitting ? '提交中...' : '提交巡检记录' }}\r\n        </button>\r\n      </view>\r\n    </scroll-view>\r\n    \r\n    <!-- 巡检结果编辑弹窗 -->\r\n    <uni-popup ref=\"resultPopup\" type=\"bottom\">\r\n      <view class=\"result-popup\">\r\n        <view class=\"popup-header\">\r\n          <text class=\"popup-title\">巡检结果 - {{ currentResult.item_info ? currentResult.item_info.itemName : '' }}</text>\r\n          <text class=\"popup-close\" @click=\"closeResultPopup\">关闭</text>\r\n        </view>\r\n        <view class=\"popup-content\">\r\n          <!-- 设备信息 -->\r\n          <view class=\"popup-form-group\" v-if=\"currentResult.item_info\">\r\n            <text class=\"popup-form-label\">设备信息</text>\r\n            <view class=\"device-info-box\">\r\n              <text class=\"device-name\">{{ currentResult.item_info.deviceName || '未知设备' }}</text>\r\n              <text class=\"device-category\" v-if=\"currentResult.item_info.categoryName\">分类: {{ currentResult.item_info.categoryName }}</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 检查标准 -->\r\n          <view class=\"popup-form-group\" v-if=\"currentResult.item_info && currentResult.item_info.normalRange\">\r\n            <text class=\"popup-form-label\">检查标准</text>\r\n            <view class=\"standard-info\">\r\n              <text class=\"normal-range\">正常范围: {{ currentResult.item_info.normalRange }}</text>\r\n              <text class=\"check-method\" v-if=\"currentResult.item_info.checkMethod\">检查方法: {{ currentResult.item_info.checkMethod }}</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 参数值输入区域 -->\r\n          <view class=\"popup-form-group\" v-if=\"currentResult.item_info && currentResult.item_info.normalRange\">\r\n            <text class=\"popup-form-label required\">当前值</text>\r\n            <input \r\n              class=\"popup-input\" \r\n              type=\"text\" \r\n              v-model=\"currentResult.param_value\" \r\n              placeholder=\"请输入当前测量值\"\r\n            />\r\n            <text class=\"parameter-unit\" v-if=\"currentResult.item_info.unit\">{{ currentResult.item_info.unit }}</text>\r\n          </view>\r\n          \r\n          <view class=\"popup-form-group\">\r\n            <text class=\"popup-form-label\">检查结果</text>\r\n            <view class=\"result-radio-group\">\r\n              <view \r\n                class=\"result-radio\" \r\n                :class=\"{'result-radio-selected': currentResult.check_result === 'normal'}\"\r\n                @click=\"currentResult.check_result = 'normal'\"\r\n              >\r\n                <text>正常</text>\r\n              </view>\r\n              <view \r\n                class=\"result-radio\" \r\n                :class=\"{'result-radio-selected': currentResult.check_result === 'abnormal'}\"\r\n                @click=\"currentResult.check_result = 'abnormal'\"\r\n              >\r\n                <text>异常</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 参数值输入区域 (用于没有正常范围的项目) -->\r\n          <view class=\"popup-form-group\" v-if=\"currentResult.item_info && !currentResult.item_info.normalRange && currentResult.item_info.hasValueInput\">\r\n            <text class=\"popup-form-label\">参数值</text>\r\n            <input \r\n              class=\"popup-input\" \r\n              type=\"text\" \r\n              v-model=\"currentResult.param_value\" \r\n              placeholder=\"请输入参数值\"\r\n            />\r\n            <text class=\"parameter-unit\" v-if=\"currentResult.item_info.unit\">{{ currentResult.item_info.unit }}</text>\r\n          </view>\r\n          \r\n          <view class=\"popup-form-group\">\r\n            <text class=\"popup-form-label\">描述</text>\r\n            <textarea \r\n              class=\"popup-form-textarea\" \r\n              v-model=\"currentResult.description\" \r\n              placeholder=\"请输入描述信息\"\r\n            ></textarea>\r\n          </view>\r\n          \r\n          <!-- 图片上传区域 -->\r\n          <view class=\"popup-form-group\">\r\n            <text class=\"popup-form-label\">照片</text>\r\n            <view class=\"result-image-list\" v-if=\"currentResult\">\r\n              <view class=\"result-image-item\" v-for=\"(image, index) in currentResult.images\" :key=\"index\">\r\n                <image class=\"result-image\" :src=\"image\" mode=\"aspectFill\"></image>\r\n                <view class=\"result-image-delete\" @click=\"removeImage(index)\">\r\n                  <text class=\"icon-delete\">×</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"result-image-add\" v-if=\"currentResult.images.length < 4\" @click=\"chooseImage\">\r\n                <text class=\"icon-add\">+</text>\r\n                <text class=\"result-image-tip\">添加图片</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"popup-form-group\">\r\n            <text class=\"popup-form-label\">位置信息</text>\r\n            <view class=\"location-btns\">\r\n              <button class=\"location-btn\" @click=\"getLocation\">获取当前位置</button>\r\n              <text v-if=\"currentResult.latitude && currentResult.longitude\">\r\n                已获取位置 ({{ currentResult.latitude.toFixed(4) }}, {{ currentResult.longitude.toFixed(4) }})\r\n              </text>\r\n            </view>\r\n          </view>\r\n          \r\n          <button class=\"popup-save-btn\" @click=\"saveResult\">保存</button>\r\n        </view>\r\n      </view>\r\n    </uni-popup>\r\n    \r\n    <!-- 时间选择器弹窗 -->\r\n    <uni-popup ref=\"datetimePopup\" type=\"bottom\">\r\n      <view class=\"datetime-popup\">\r\n        <view class=\"popup-header\">\r\n          <text class=\"popup-title\">{{ currentDatetimeType === 'start' ? '选择开始时间' : '选择结束时间' }}</text>\r\n          <text class=\"popup-close\" @click=\"closeDatetimePicker\">关闭</text>\r\n        </view>\r\n        <view class=\"picker-body\">\r\n          <picker-view class=\"picker\" :indicator-style=\"indicatorStyle\" :value=\"pickerValue\" @change=\"dateTimePickerChange\">\r\n            <picker-view-column>\r\n              <view class=\"picker-item\" v-for=\"(item, index) in years\" :key=\"'year-'+index\">{{ item }}年</view>\r\n            </picker-view-column>\r\n            <picker-view-column>\r\n              <view class=\"picker-item\" v-for=\"(item, index) in months\" :key=\"'month-'+index\">{{ item }}月</view>\r\n            </picker-view-column>\r\n            <picker-view-column>\r\n              <view class=\"picker-item\" v-for=\"(item, index) in days\" :key=\"'day-'+index\">{{ item }}日</view>\r\n            </picker-view-column>\r\n            <picker-view-column>\r\n              <view class=\"picker-item\" v-for=\"(item, index) in hours\" :key=\"'hour-'+index\">{{ item }}时</view>\r\n            </picker-view-column>\r\n            <picker-view-column>\r\n              <view class=\"picker-item\" v-for=\"(item, index) in minutes\" :key=\"'minute-'+index\">{{ item }}分</view>\r\n            </picker-view-column>\r\n            <picker-view-column>\r\n              <view class=\"picker-item\" v-for=\"(item, index) in seconds\" :key=\"'second-'+index\">{{ item }}秒</view>\r\n            </picker-view-column>\r\n          </picker-view>\r\n        </view>\r\n        <view class=\"btn-area\">\r\n          <button class=\"confirm-btn\" @click=\"confirmDateTime\">确定</button>\r\n        </view>\r\n      </view>\r\n    </uni-popup>\r\n    \r\n    <!-- 执行人选择器弹窗 -->\r\n    <uni-popup ref=\"executorPopup\" type=\"bottom\">\r\n      <view class=\"executor-popup\">\r\n        <view class=\"popup-header\">\r\n          <text class=\"popup-title\">选择执行人</text>\r\n          <text class=\"popup-close\" @click=\"closeExecutorSelector\">关闭</text>\r\n        </view>\r\n        <view class=\"executor-body\">\r\n          <view class=\"executor-search\">\r\n            <input type=\"text\" v-model=\"executorSearchKey\" placeholder=\"搜索执行人\" class=\"search-input\" />\r\n          </view>\r\n          <scroll-view scroll-y class=\"executor-list\">\r\n            <checkbox-group @change=\"handleExecutorChange\">\r\n              <label class=\"executor-item\" v-for=\"(executor, index) in filteredExecutors\" :key=\"index\">\r\n                <view class=\"executor-info\">\r\n                  <checkbox :value=\"executor.id.toString()\" :checked=\"isExecutorSelected(executor.id)\" />\r\n                  <text class=\"executor-name\">{{ executor.name }}</text>\r\n                </view>\r\n                <text class=\"executor-dept\" v-if=\"executor.departmentName\">{{ executor.departmentName }}</text>\r\n              </label>\r\n            </checkbox-group>\r\n          </scroll-view>\r\n        </view>\r\n        <view class=\"btn-area\">\r\n          <button class=\"confirm-btn\" @click=\"confirmExecutorSelection\">确定 (已选 {{ formData.executor_ids.length }} 人)</button>\r\n        </view>\r\n      </view>\r\n    </uni-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { patrolApi } from '@/utils/api.js';\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      formData: {\r\n        patrol_plan_id: '', // 巡检计划ID\r\n        executor_ids: [], // 执行人ID数组\r\n        start_time: '', // 开始时间\r\n        end_time: '', // 结束时间\r\n        status: 'completed', // 状态：pending/processing/completed\r\n        remark: '', // 备注\r\n        patrol_results: [] // 巡检结果\r\n      },\r\n      statusOptions: [\r\n        { label: '待执行', value: 'pending' },\r\n        { label: '执行中', value: 'processing' },\r\n        { label: '已完成', value: 'completed' }\r\n      ],\r\n      selectedPlanName: '',\r\n      selectedExecutorNames: '',\r\n      plans: [],\r\n      executors: [],\r\n      loading: false,\r\n      submitting: false,\r\n      currentResultIndex: -1,\r\n      currentResult: {\r\n        patrol_item_id: '',\r\n        check_result: 'normal',\r\n        param_value: '',\r\n        description: '',\r\n        images: [],\r\n        latitude: 0,\r\n        longitude: 0\r\n      },\r\n      \r\n      // 日期时间选择器相关数据\r\n      currentDatetimeType: 'start', // 当前正在选择的时间类型：start/end\r\n      indicatorStyle: 'height: 50px;',\r\n      pickerValue: [0, 0, 0, 0, 0, 0],\r\n      years: [],\r\n      months: [],\r\n      days: [],\r\n      hours: [],\r\n      minutes: [],\r\n      seconds: [],\r\n      year: 2020,\r\n      month: 1,\r\n      day: 1,\r\n      hour: 0,\r\n      minute: 0,\r\n      second: 0,\r\n      \r\n      // 执行人选择器相关数据\r\n      executorSearchKey: '',\r\n      selectedExecutorIds: []\r\n    };\r\n  },\r\n  computed: {\r\n    // 获取经过搜索过滤的执行人列表\r\n    filteredExecutors() {\r\n      if (!this.executorSearchKey) {\r\n        return this.executors;\r\n      }\r\n      \r\n      const keyword = this.executorSearchKey.toLowerCase();\r\n      return this.executors.filter(exec => \r\n        exec.name.toLowerCase().includes(keyword) || \r\n        (exec.departmentName && exec.departmentName.toLowerCase().includes(keyword))\r\n      );\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    // 从路由参数中获取数据\r\n    if (options.plan_id) {\r\n      this.formData.patrol_plan_id = options.plan_id;\r\n      this.loadPlanDetails(options.plan_id);\r\n    }\r\n    \r\n    // 加载巡检计划列表\r\n    this.loadPatrolPlans();\r\n    \r\n    // 加载执行人列表\r\n    this.loadExecutors();\r\n    \r\n    // 设置默认开始和结束时间为当前时间\r\n    const now = new Date();\r\n    this.formData.start_time = this.formatDateTime(now);\r\n    this.formData.end_time = this.formatDateTime(now);\r\n  },\r\n  methods: {\r\n    // 格式化日期时间\r\n    formatDateTime(date) {\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0');\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n      const seconds = String(date.getSeconds()).padStart(2, '0');\r\n      \r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n    },\r\n    \r\n    // 加载巡检计划列表\r\n    loadPatrolPlans() {\r\n      this.loading = true;\r\n      patrolApi.getPatrolPlans({})\r\n        .then(res => {\r\n          if (res.code === 200 && res.data) {\r\n            this.plans = res.data || [];\r\n            \r\n            // 如果已有选择的计划ID，设置对应的计划名称\r\n            if (this.formData.patrol_plan_id) {\r\n              const selectedPlan = this.plans.find(plan => plan.id == this.formData.patrol_plan_id);\r\n              if (selectedPlan) {\r\n                this.selectedPlanName = selectedPlan.name;\r\n              }\r\n            }\r\n          } else {\r\n            uni.showToast({\r\n              title: '获取巡检计划列表失败',\r\n              icon: 'none'\r\n            });\r\n          }\r\n        })\r\n        .catch(err => {\r\n          console.error('获取巡检计划列表错误:', err);\r\n          uni.showToast({\r\n            title: '获取巡检计划列表失败',\r\n            icon: 'none'\r\n          });\r\n        })\r\n        .finally(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    \r\n    // 加载巡检计划详情\r\n    loadPlanDetails(planId) {\r\n      patrolApi.getPlanDetail(planId)\r\n        .then(res => {\r\n          if (res.code === 200 && res.data) {\r\n            this.selectedPlanName = res.data.name;\r\n            \r\n            // 获取计划中的执行人\r\n            if (res.data.executorIds && res.data.executorIds.length > 0) {\r\n              // 加载执行人列表\r\n              this.loadPlanExecutors(planId);\r\n            }\r\n            \r\n            // 加载巡检项目，从API获取巡检项目列表\r\n            this.loadPatrolItems(planId);\r\n          }\r\n        })\r\n        .catch(err => {\r\n          console.error('获取巡检计划详情错误:', err);\r\n        });\r\n    },\r\n    \r\n    // 加载巡检项目列表\r\n    loadPatrolItems(planId) {\r\n      patrolApi.getItemList({ planId: planId })\r\n        .then(res => {\r\n          if (res.code === 200 && res.data) {\r\n            // 根据API返回的巡检项列表初始化巡检结果\r\n            if (res.data && res.data.length > 0) {\r\n              this.formData.patrol_results = res.data.map(item => ({\r\n                patrol_item_id: item.id,\r\n                check_result: 'normal',\r\n                param_value: '',\r\n                description: '',\r\n                images: [],\r\n                latitude: 0,\r\n                longitude: 0,\r\n                // 存储巡检项的详细信息，用于展示\r\n                item_info: {\r\n                  deviceId: item.deviceId,\r\n                  deviceName: item.deviceName,\r\n                  itemName: item.itemName || `巡检项 ${item.id}`,\r\n                  categoryName: item.categoryName || '',\r\n                  paramType: item.paramType || '',\r\n                  unit: item.unit || '',\r\n                  normalRange: item.normalRange || '',\r\n                  checkMethod: item.checkMethod || '',\r\n                  importance: item.importance || '',\r\n                  description: item.description || ''\r\n                }\r\n              }));\r\n            } else {\r\n              // 如果没有巡检项，显示提示\r\n              uni.showToast({\r\n                title: '该巡检计划没有巡检项',\r\n                icon: 'none'\r\n              });\r\n              this.formData.patrol_results = [];\r\n            }\r\n          } else {\r\n            uni.showToast({\r\n              title: '获取巡检项目失败',\r\n              icon: 'none'\r\n            });\r\n          }\r\n        })\r\n        .catch(err => {\r\n          console.error('获取巡检项目列表错误:', err);\r\n          uni.showToast({\r\n            title: '获取巡检项目失败',\r\n            icon: 'none'\r\n          });\r\n        });\r\n    },\r\n    \r\n    // 加载计划指定的执行人\r\n    loadPlanExecutors(planId) {\r\n      patrolApi.getPlanExecutors(planId)\r\n        .then(res => {\r\n          if (res.code === 200 && res.data) {\r\n            this.executors = res.data || [];\r\n            \r\n            // 更新已选择执行人的显示\r\n            this.updateSelectedExecutorNames();\r\n          }\r\n        })\r\n        .catch(err => {\r\n          console.error('获取计划执行人列表错误:', err);\r\n        });\r\n    },\r\n    \r\n    // 更新已选择执行人的显示\r\n    updateSelectedExecutorNames() {\r\n      if (this.formData.executor_ids && this.formData.executor_ids.length > 0 && this.executors.length > 0) {\r\n        const selectedUsers = this.executors.filter(exec => \r\n          this.formData.executor_ids.includes(exec.id)\r\n        );\r\n        \r\n        if (selectedUsers.length > 0) {\r\n          this.selectedExecutorNames = selectedUsers.map(user => user.name).join('，');\r\n          \r\n          // 如果名称太长，只显示人数\r\n          if (this.selectedExecutorNames.length > 15) {\r\n            this.selectedExecutorNames = `共${selectedUsers.length}人`;\r\n          }\r\n        } else {\r\n          this.selectedExecutorNames = '';\r\n        }\r\n      } else {\r\n        this.selectedExecutorNames = '';\r\n      }\r\n    },\r\n    \r\n    // 加载执行人列表\r\n    loadExecutors() {\r\n      patrolApi.getPlanExecutors()\r\n        .then(res => {\r\n          if (res.code === 200 && res.data) {\r\n            this.executors = res.data || [];\r\n            \r\n            // 默认选择当前用户作为执行人\r\n            const userInfo = uni.getStorageSync('userInfo');\r\n            if (userInfo && userInfo.userId) {\r\n              this.formData.executor_ids = [userInfo.userId];\r\n              this.updateSelectedExecutorNames();\r\n            }\r\n          }\r\n        })\r\n        .catch(err => {\r\n          console.error('获取执行人列表错误:', err);\r\n        });\r\n    },\r\n    \r\n    // 显示巡检计划选择器\r\n    showPlanSelector() {\r\n      if (this.plans.length === 0) {\r\n        uni.showToast({\r\n          title: '暂无可选巡检计划',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      const planItems = this.plans.map(plan => plan.name);\r\n      uni.showActionSheet({\r\n        itemList: planItems,\r\n        success: res => {\r\n          const index = res.tapIndex;\r\n          this.formData.patrol_plan_id = this.plans[index].id;\r\n          this.selectedPlanName = this.plans[index].name;\r\n          \r\n          // 加载计划详情\r\n          this.loadPlanDetails(this.formData.patrol_plan_id);\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 显示执行人选择器\r\n    showExecutorSelector() {\r\n      if (this.executors.length === 0) {\r\n        uni.showToast({\r\n          title: '暂无可选执行人',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      // 初始化已选择的执行人ID\r\n      this.selectedExecutorIds = [...this.formData.executor_ids];\r\n      \r\n      // 显示选择器\r\n      this.$refs.executorPopup.open();\r\n    },\r\n    \r\n    // 显示开始时间选择器\r\n    showStartTimePicker() {\r\n      this.currentDatetimeType = 'start';\r\n      this.initDateTimePicker(this.formData.start_time);\r\n      this.$refs.datetimePopup.open();\r\n    },\r\n    \r\n    // 显示结束时间选择器\r\n    showEndTimePicker() {\r\n      this.currentDatetimeType = 'end';\r\n      this.initDateTimePicker(this.formData.end_time);\r\n      this.$refs.datetimePopup.open();\r\n    },\r\n    \r\n    // 关闭时间选择器\r\n    closeDatetimePicker() {\r\n      this.$refs.datetimePopup.close();\r\n    },\r\n    \r\n    // 初始化时间选择器\r\n    initDateTimePicker(dateTimeStr) {\r\n      let date;\r\n      \r\n      if (dateTimeStr) {\r\n        date = new Date(dateTimeStr);\r\n        if (isNaN(date.getTime())) {\r\n          date = new Date();\r\n        }\r\n      } else {\r\n        date = new Date();\r\n      }\r\n      \r\n      this.year = date.getFullYear();\r\n      this.month = date.getMonth() + 1;\r\n      this.day = date.getDate();\r\n      this.hour = date.getHours();\r\n      this.minute = date.getMinutes();\r\n      this.second = date.getSeconds();\r\n      \r\n      // 生成年份数据，当前年份前后10年\r\n      this.years = [];\r\n      for (let i = this.year - 10; i <= this.year + 10; i++) {\r\n        this.years.push(i);\r\n      }\r\n      \r\n      // 生成月份数据\r\n      this.months = [];\r\n      for (let i = 1; i <= 12; i++) {\r\n        this.months.push(i);\r\n      }\r\n      \r\n      this.updateDays();\r\n      \r\n      // 生成小时数据\r\n      this.hours = [];\r\n      for (let i = 0; i <= 23; i++) {\r\n        this.hours.push(i);\r\n      }\r\n      \r\n      // 生成分钟和秒数据\r\n      this.minutes = [];\r\n      this.seconds = [];\r\n      for (let i = 0; i <= 59; i++) {\r\n        this.minutes.push(i);\r\n        this.seconds.push(i);\r\n      }\r\n      \r\n      // 更新pickerValue\r\n      this.updatePickerValue();\r\n    },\r\n    \r\n    // 更新天数，根据年月确定\r\n    updateDays() {\r\n      let daysInMonth = 31;\r\n      \r\n      // 计算当月天数\r\n      if (this.month === 2) {\r\n        // 闰年2月29天，平年28天\r\n        daysInMonth = (this.year % 4 === 0 && this.year % 100 !== 0) || this.year % 400 === 0 ? 29 : 28;\r\n      } else if ([4, 6, 9, 11].includes(this.month)) {\r\n        daysInMonth = 30;\r\n      }\r\n      \r\n      this.days = [];\r\n      for (let i = 1; i <= daysInMonth; i++) {\r\n        this.days.push(i);\r\n      }\r\n      \r\n      // 如果当前选择的日期超出了当月的最大天数，则调整为当月最后一天\r\n      if (this.day > daysInMonth) {\r\n        this.day = daysInMonth;\r\n      }\r\n    },\r\n    \r\n    // 更新pickerValue\r\n    updatePickerValue() {\r\n      const yearIndex = this.years.indexOf(this.year);\r\n      const monthIndex = this.months.indexOf(this.month);\r\n      const dayIndex = this.days.indexOf(this.day);\r\n      const hourIndex = this.hours.indexOf(this.hour);\r\n      const minuteIndex = this.minutes.indexOf(this.minute);\r\n      const secondIndex = this.seconds.indexOf(this.second);\r\n      \r\n      this.pickerValue = [\r\n        yearIndex !== -1 ? yearIndex : 0,\r\n        monthIndex !== -1 ? monthIndex : 0,\r\n        dayIndex !== -1 ? dayIndex : 0,\r\n        hourIndex !== -1 ? hourIndex : 0,\r\n        minuteIndex !== -1 ? minuteIndex : 0,\r\n        secondIndex !== -1 ? secondIndex : 0\r\n      ];\r\n    },\r\n    \r\n    // 日期时间选择器变化处理\r\n    dateTimePickerChange(e) {\r\n      const val = e.detail.value;\r\n      \r\n      // 更新选中的值\r\n      this.year = this.years[val[0]];\r\n      this.month = this.months[val[1]];\r\n      \r\n      // 更新天数列表\r\n      this.updateDays();\r\n      \r\n      // 如果当前选中的日期超出了更新后的天数范围，则重置为1号\r\n      if (val[2] >= this.days.length) {\r\n        val[2] = 0;\r\n      }\r\n      \r\n      this.day = this.days[val[2]];\r\n      this.hour = this.hours[val[3]];\r\n      this.minute = this.minutes[val[4]];\r\n      this.second = this.seconds[val[5]];\r\n    },\r\n    \r\n    // 确认日期时间选择\r\n    confirmDateTime() {\r\n      // 格式化日期时间为字符串\r\n      const datetimeStr = this.formatPickerDateTime();\r\n      \r\n      // 更新表单数据\r\n      if (this.currentDatetimeType === 'start') {\r\n        this.formData.start_time = datetimeStr;\r\n        \r\n        // 如果开始时间晚于结束时间，则更新结束时间\r\n        if (this.formData.end_time && new Date(this.formData.start_time) > new Date(this.formData.end_time)) {\r\n          this.formData.end_time = datetimeStr;\r\n        }\r\n      } else {\r\n        this.formData.end_time = datetimeStr;\r\n        \r\n        // 如果结束时间早于开始时间，显示提示\r\n        if (this.formData.start_time && new Date(this.formData.end_time) < new Date(this.formData.start_time)) {\r\n          uni.showToast({\r\n            title: '结束时间不能早于开始时间',\r\n            icon: 'none'\r\n          });\r\n          this.formData.end_time = this.formData.start_time;\r\n        }\r\n      }\r\n      \r\n      // 关闭选择器\r\n      this.closeDatetimePicker();\r\n    },\r\n    \r\n    // 格式化选择器日期时间\r\n    formatPickerDateTime() {\r\n      const year = this.year;\r\n      const month = String(this.month).padStart(2, '0');\r\n      const day = String(this.day).padStart(2, '0');\r\n      const hour = String(this.hour).padStart(2, '0');\r\n      const minute = String(this.minute).padStart(2, '0');\r\n      const second = String(this.second).padStart(2, '0');\r\n      \r\n      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;\r\n    },\r\n    \r\n    // 编辑巡检结果\r\n    editPatrolResult(index) {\r\n      this.currentResultIndex = index;\r\n      \r\n      // 深拷贝以避免直接修改原对象\r\n      const resultCopy = JSON.parse(JSON.stringify(this.formData.patrol_results[index]));\r\n      \r\n      // 确保images字段存在\r\n      if (!resultCopy.images) {\r\n        resultCopy.images = [];\r\n      }\r\n      \r\n      this.currentResult = resultCopy;\r\n      this.$refs.resultPopup.open();\r\n    },\r\n    \r\n    // 关闭结果弹窗\r\n    closeResultPopup() {\r\n      this.$refs.resultPopup.close();\r\n    },\r\n    \r\n    // 保存结果\r\n    saveResult() {\r\n      // 对于有正常范围的项目，检查参数值是否已填写\r\n      if (this.currentResult.item_info && this.currentResult.item_info.normalRange && !this.currentResult.param_value) {\r\n        uni.showToast({\r\n          title: '请输入当前测量值',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      // 保存当前编辑的结果到表单数据\r\n      if (this.currentResultIndex >= 0) {\r\n        // 确保使用images字段\r\n        if (!this.currentResult.images) {\r\n          this.currentResult.images = [];\r\n        }\r\n        \r\n        this.formData.patrol_results[this.currentResultIndex] = JSON.parse(JSON.stringify(this.currentResult));\r\n      }\r\n      \r\n      this.closeResultPopup();\r\n    },\r\n    \r\n    // 选择图片\r\n    chooseImage() {\r\n      uni.chooseImage({\r\n        count: 4 - (this.currentResult.images?.length || 0),\r\n        success: (res) => {\r\n          const tempFilePaths = res.tempFilePaths;\r\n          const newImages = tempFilePaths.map(path => ({\r\n            url: path,\r\n            file: path\r\n          }));\r\n          \r\n          if (!this.currentResult.images) {\r\n            this.currentResult.images = [];\r\n          }\r\n          \r\n          this.currentResult.images = [...this.currentResult.images, ...newImages];\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 删除图片\r\n    removeImage(index) {\r\n      this.currentResult.images.splice(index, 1);\r\n    },\r\n    \r\n    // 获取位置信息\r\n    getLocation() {\r\n      uni.getLocation({\r\n        type: 'gcj02',\r\n        success: (res) => {\r\n          this.currentResult.latitude = res.latitude;\r\n          this.currentResult.longitude = res.longitude;\r\n        },\r\n        fail: (err) => {\r\n          console.error('获取位置失败:', err);\r\n          uni.showToast({\r\n            title: '获取位置失败，请检查位置权限',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 表单验证\r\n    validateForm() {\r\n      if (!this.formData.patrol_plan_id) {\r\n        uni.showToast({\r\n          title: '请选择巡检计划',\r\n          icon: 'none'\r\n        });\r\n        return false;\r\n      }\r\n      \r\n      if (this.formData.executor_ids.length === 0) {\r\n        uni.showToast({\r\n          title: '请选择执行人',\r\n          icon: 'none'\r\n        });\r\n        return false;\r\n      }\r\n      \r\n      if (!this.formData.start_time) {\r\n        uni.showToast({\r\n          title: '请选择开始时间',\r\n          icon: 'none'\r\n        });\r\n        return false;\r\n      }\r\n      \r\n      if (!this.formData.end_time) {\r\n        uni.showToast({\r\n          title: '请选择结束时间',\r\n          icon: 'none'\r\n        });\r\n        return false;\r\n      }\r\n      \r\n      if (new Date(this.formData.end_time) < new Date(this.formData.start_time)) {\r\n        uni.showToast({\r\n          title: '结束时间不能早于开始时间',\r\n          icon: 'none'\r\n        });\r\n        return false;\r\n      }\r\n      \r\n      return true;\r\n    },\r\n    \r\n    // 提交巡检记录\r\n    submitRecord() {\r\n      if (!this.validateForm()) {\r\n        return;\r\n      }\r\n      \r\n      // 确认提交弹窗\r\n      uni.showModal({\r\n        title: '提交确认',\r\n        content: '确定要提交巡检记录吗？',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            this.doSubmit();\r\n          }\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 执行提交操作\r\n    doSubmit() {\r\n      this.submitting = true;\r\n      \r\n      // 根据最新接口文档严格构造请求数据\r\n      const submitData = {\r\n        patrol_plan_id: this.formData.patrol_plan_id, // 巡检计划ID\r\n        executor_id: this.formData.executor_ids[0] || '', // 执行人ID\r\n        start_time: this.formData.start_time, // 开始时间\r\n        end_time: this.formData.end_time, // 结束时间\r\n        status: this.formData.status, // 状态:pending,processing,completed\r\n        remark: this.formData.remark || '', // 备注\r\n        patrol_results: [] // 巡检结果数组\r\n      };\r\n      \r\n      // 处理巡检项目数组，严格按照最新接口文档的字段要求\r\n      if (this.formData.patrol_results && this.formData.patrol_results.length > 0) {\r\n        submitData.patrol_results = this.formData.patrol_results.map(result => {\r\n          // 从结果中提取接口需要的确切字段\r\n          const cleanResult = {\r\n            patrol_item_id: result.patrol_item_id, // 巡检项ID\r\n            check_result: result.check_result, // 检查结果:normal-正常,abnormal-异常\r\n            param_value: result.param_value || result.parameter_value || '', // 参数值\r\n            description: result.description || '', // 描述\r\n            latitude: result.latitude ? Number(result.latitude) : 0, // 经度，确保为数字类型\r\n            longitude: result.longitude ? Number(result.longitude) : 0 // 纬度，确保为数字类型\r\n          };\r\n          \r\n          // 处理图片字段，按最新文档使用images\r\n          try {\r\n            // 仅使用images字段\r\n            if (result.images && result.images.length) {\r\n              const imagesArray = Array.from(result.images);\r\n              cleanResult.images = imagesArray.filter(Boolean).map(img => \r\n                typeof img === 'string' ? img : \r\n                (img && img.url ? img.url : '')\r\n              ).filter(url => url);\r\n            } else {\r\n              cleanResult.images = []; // 确保有空数组\r\n            }\r\n          } catch (e) {\r\n            console.error('处理图片字段出错:', e);\r\n            cleanResult.images = []; // 出错时提供默认空数组\r\n          }\r\n          \r\n          return cleanResult;\r\n        });\r\n      }\r\n      \r\n      console.log('提交数据:', JSON.stringify(submitData));\r\n      \r\n      patrolApi.submitPatrolRecord(submitData)\r\n        .then(res => {\r\n          if (res.code === 200) {\r\n            uni.showToast({\r\n              title: '巡检记录提交成功',\r\n              icon: 'success'\r\n            });\r\n            \r\n            // 延迟跳转\r\n            setTimeout(() => {\r\n              uni.navigateBack();\r\n            }, 1500);\r\n          } else {\r\n            uni.showToast({\r\n              title: res.message || '提交失败',\r\n              icon: 'none'\r\n            });\r\n          }\r\n        })\r\n        .catch(err => {\r\n          console.error('提交巡检记录错误:', err);\r\n          uni.showToast({\r\n            title: '提交失败，请重试',\r\n            icon: 'none'\r\n          });\r\n        })\r\n        .finally(() => {\r\n          this.submitting = false;\r\n        });\r\n    },\r\n    \r\n    // 关闭执行人选择器\r\n    closeExecutorSelector() {\r\n      this.$refs.executorPopup.close();\r\n      this.executorSearchKey = ''; // 清空搜索关键字\r\n    },\r\n    \r\n    // 处理执行人选择变化\r\n    handleExecutorChange(e) {\r\n      // 更新选中的执行人ID\r\n      this.selectedExecutorIds = e.detail.value.map(id => parseInt(id));\r\n    },\r\n    \r\n    // 确认执行人选择\r\n    confirmExecutorSelection() {\r\n      // 更新表单数据中的执行人ID\r\n      this.formData.executor_ids = [...this.selectedExecutorIds];\r\n      \r\n      // 更新显示的执行人名称\r\n      this.updateSelectedExecutorNames();\r\n      \r\n      // 关闭选择器\r\n      this.closeExecutorSelector();\r\n    },\r\n    \r\n    // 检查执行人是否被选中\r\n    isExecutorSelected(executorId) {\r\n      return this.selectedExecutorIds.includes(executorId);\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 100vh;\r\n  background-color: #f5f5f5;\r\n  width: 100%;\r\n}\r\n\r\n.page-title {\r\n  font-size: 40rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n  padding: 40rpx 30rpx 20rpx;\r\n  text-align: center;\r\n  background-color: #fff;\r\n  position: relative;\r\n  \r\n  &::after {\r\n    content: '';\r\n    position: absolute;\r\n    bottom: 5rpx;\r\n    left: 50%;\r\n    transform: translateX(-50%);\r\n    width: 60rpx;\r\n    height: 6rpx;\r\n    background-color: #1890ff;\r\n    border-radius: 3rpx;\r\n  }\r\n}\r\n\r\n.form-container {\r\n  flex: 1;\r\n  padding: 30rpx 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  width: 100%;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 30rpx;\r\n  background-color: #fff;\r\n  border-radius: 12rpx;\r\n  padding: 24rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n  width: 90%;\r\n  max-width: 680rpx;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n  box-sizing: border-box;\r\n  border-left: 4rpx solid #1890ff;\r\n}\r\n\r\n.form-label {\r\n  display: block;\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  margin-bottom: 16rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.required:after {\r\n  content: '*';\r\n  color: #f56c6c;\r\n  margin-left: 6rpx;\r\n}\r\n\r\n.form-input {\r\n  width: 100%;\r\n  height: 80rpx;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 8rpx;\r\n  padding: 0 20rpx;\r\n  font-size: 28rpx;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.select-box {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n\r\n.placeholder {\r\n  color: #999;\r\n}\r\n\r\n.select-arrow {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.form-textarea {\r\n  width: 100%;\r\n  height: 200rpx;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 8rpx;\r\n  padding: 20rpx;\r\n  font-size: 28rpx;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.char-count {\r\n  text-align: right;\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  margin-top: 8rpx;\r\n}\r\n\r\n.status-group {\r\n  display: flex;\r\n  flex-direction: row;\r\n  gap: 30rpx;\r\n}\r\n\r\n.status-option {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10rpx;\r\n  padding: 20rpx;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 8rpx;\r\n  transition: all 0.3s;\r\n  cursor: pointer;\r\n}\r\n\r\n.status-option.active {\r\n  border-color: #1890ff;\r\n  background-color: rgba(24, 144, 255, 0.1);\r\n}\r\n\r\n.status-dot {\r\n  width: 24rpx;\r\n  height: 24rpx;\r\n  border-radius: 50%;\r\n  background-color: #dcdfe6;\r\n}\r\n\r\n.status-option.active .status-dot {\r\n  background-color: #1890ff;\r\n}\r\n\r\n.status-text {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n}\r\n\r\n.status-option.active .status-text {\r\n  color: #1890ff;\r\n  font-weight: 500;\r\n}\r\n\r\n.results-list {\r\n  margin-top: 20rpx;\r\n  width: 100%;\r\n}\r\n\r\n.result-item {\r\n  background-color: #f9f9f9;\r\n  border-radius: 12rpx;\r\n  padding: 20rpx;\r\n  margin-bottom: 16rpx;\r\n  border-left: 4rpx solid #1890ff;\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n  \r\n  &:nth-child(3n+1) {\r\n    border-left-color: #1890ff;\r\n  }\r\n  \r\n  &:nth-child(3n+2) {\r\n    border-left-color: #52c41a;\r\n  }\r\n  \r\n  &:nth-child(3n+3) {\r\n    border-left-color: #faad14;\r\n  }\r\n  \r\n  &:active {\r\n    transform: scale(0.98);\r\n    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);\r\n  }\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.result-title {\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.result-status {\r\n  padding: 6rpx 16rpx;\r\n  border-radius: 30rpx;\r\n  font-size: 24rpx;\r\n}\r\n\r\n.result-status.normal {\r\n  background-color: #f0f9eb;\r\n  color: #67c23a;\r\n}\r\n\r\n.result-status.abnormal {\r\n  background-color: #fef0f0;\r\n  color: #f56c6c;\r\n}\r\n\r\n.result-body {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n}\r\n\r\n.result-row {\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.result-label {\r\n  color: #999;\r\n  margin-right: 8rpx;\r\n}\r\n\r\n.submit-section {\r\n  margin: 40rpx 0;\r\n  width: 90%;\r\n  max-width: 680rpx;\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n}\r\n\r\n.submit-btn {\r\n  width: 100%;\r\n  height: 88rpx;\r\n  line-height: 88rpx;\r\n  text-align: center;\r\n  background-color: #1890ff;\r\n  color: #fff;\r\n  border-radius: 44rpx;\r\n  font-size: 32rpx;\r\n  font-weight: 500;\r\n  box-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.3);\r\n}\r\n\r\n.submit-btn.disabled {\r\n  background-color: #a0cfff;\r\n}\r\n\r\n/* 弹窗样式 */\r\n.result-popup {\r\n  background-color: #fff;\r\n  border-top-left-radius: 20rpx;\r\n  border-top-right-radius: 20rpx;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n  max-height: 80vh;\r\n}\r\n\r\n.popup-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 30rpx;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.popup-title {\r\n  font-size: 32rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.popup-close {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n}\r\n\r\n.popup-content {\r\n  padding: 30rpx;\r\n  max-height: calc(80vh - 100rpx);\r\n  overflow-y: auto;\r\n}\r\n\r\n.popup-form-group {\r\n  margin-bottom: 24rpx;\r\n  position: relative;\r\n}\r\n\r\n.popup-form-label {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  margin-bottom: 12rpx;\r\n  display: block;\r\n}\r\n\r\n.popup-input {\r\n  width: 100%;\r\n  height: 80rpx;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 8rpx;\r\n  padding: 0 20rpx;\r\n  font-size: 28rpx;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.popup-input:focus {\r\n  border-color: #1890ff;\r\n  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\r\n}\r\n\r\n.popup-form-textarea {\r\n  width: 100%;\r\n  height: 160rpx;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 8rpx;\r\n  padding: 20rpx;\r\n  font-size: 28rpx;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.result-radio-group {\r\n  display: flex;\r\n  gap: 20rpx;\r\n}\r\n\r\n.result-radio {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 16rpx;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 8rpx;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.result-radio.result-radio-selected {\r\n  border-color: #1890ff;\r\n  background-color: rgba(24, 144, 255, 0.1);\r\n}\r\n\r\n.result-image-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: 0 -10rpx;\r\n}\r\n\r\n.result-image-item {\r\n  position: relative;\r\n  width: 160rpx;\r\n  height: 160rpx;\r\n  margin: 10rpx;\r\n  border-radius: 8rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n.result-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.result-image-delete {\r\n  position: absolute;\r\n  top: 6rpx;\r\n  right: 6rpx;\r\n  width: 36rpx;\r\n  height: 36rpx;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.icon-delete {\r\n  font-size: 24rpx;\r\n  color: #ffffff;\r\n  line-height: 24rpx;\r\n}\r\n\r\n.result-image-add {\r\n  width: 160rpx;\r\n  height: 160rpx;\r\n  margin: 10rpx;\r\n  background-color: #f5f5f5;\r\n  border: 1px dashed #ddd;\r\n  border-radius: 8rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  color: #999;\r\n}\r\n\r\n.icon-add {\r\n  font-size: 48rpx;\r\n  line-height: 48rpx;\r\n  color: #999;\r\n}\r\n\r\n.result-image-tip {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  margin-top: 8rpx;\r\n}\r\n\r\n.location-btns {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16rpx;\r\n}\r\n\r\n.location-btn {\r\n  background-color: #1890ff;\r\n  color: #fff;\r\n  height: 70rpx;\r\n  line-height: 70rpx;\r\n  font-size: 28rpx;\r\n  border-radius: 8rpx;\r\n}\r\n\r\n.popup-save-btn {\r\n  margin-top: 40rpx;\r\n  width: 100%;\r\n  height: 88rpx;\r\n  line-height: 88rpx;\r\n  text-align: center;\r\n  background-color: #1890ff;\r\n  color: #fff;\r\n  border-radius: 8rpx;\r\n  font-size: 32rpx;\r\n}\r\n\r\n.device-info-box {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8rpx;\r\n}\r\n\r\n.device-name {\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.device-category {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.standard-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8rpx;\r\n}\r\n\r\n.normal-range {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.check-method {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.parameter-unit {\r\n  position: absolute;\r\n  right: 20rpx;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  font-size: 24rpx;\r\n  color: #666;\r\n}\r\n\r\n/* 时间选择器样式 */\r\n.datetime-popup {\r\n  background-color: #fff;\r\n  border-top-left-radius: 20rpx;\r\n  border-top-right-radius: 20rpx;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n\r\n.popup-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 24rpx 30rpx;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.popup-title {\r\n  font-size: 32rpx;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n.popup-close {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n  padding: 10rpx;\r\n}\r\n\r\n.picker-body {\r\n  padding: 30rpx 20rpx;\r\n}\r\n\r\n.picker {\r\n  width: 100%;\r\n  height: 400rpx;\r\n}\r\n\r\n.picker-item {\r\n  height: 50px;\r\n  line-height: 50px;\r\n  text-align: center;\r\n  font-size: 28rpx;\r\n  color: #333;\r\n}\r\n\r\n.btn-area {\r\n  padding: 20rpx 30rpx 40rpx;\r\n}\r\n\r\n.confirm-btn {\r\n  width: 100%;\r\n  background-color: #1890ff;\r\n  color: #fff;\r\n  height: 88rpx;\r\n  line-height: 88rpx;\r\n  font-size: 32rpx;\r\n  border-radius: 44rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 执行人选择器样式 */\r\n.executor-popup {\r\n  background-color: #fff;\r\n  border-top-left-radius: 20rpx;\r\n  border-top-right-radius: 20rpx;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n  max-height: 80vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.executor-body {\r\n  padding: 20rpx 30rpx;\r\n  flex: 1;\r\n}\r\n\r\n.executor-search {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n  height: 80rpx;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 8rpx;\r\n  padding: 0 20rpx;\r\n  font-size: 28rpx;\r\n  box-sizing: border-box;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.executor-list {\r\n  height: 600rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.executor-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 24rpx 10rpx;\r\n  border-bottom: 1px solid #eaeaea;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.executor-item:active {\r\n  background-color: rgba(24, 144, 255, 0.05);\r\n}\r\n\r\n.executor-info {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.executor-name {\r\n  font-size: 28rpx;\r\n  margin-left: 16rpx;\r\n  color: #333;\r\n}\r\n\r\n.executor-dept {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.btn-area {\r\n  padding: 20rpx 30rpx 40rpx;\r\n}\r\n\r\n.confirm-btn {\r\n  width: 100%;\r\n  background: linear-gradient(135deg, #1890ff, #096dd9);\r\n  color: #fff;\r\n  height: 88rpx;\r\n  line-height: 88rpx;\r\n  font-size: 32rpx;\r\n  border-radius: 44rpx;\r\n  font-weight: 500;\r\n  box-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.3);\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/patrol/create_record.vue'\nwx.createPage(MiniProgramPage)"], "names": ["patrolApi", "uni"], "mappings": ";;;AA2TA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,UAAU;AAAA,QACR,gBAAgB;AAAA;AAAA,QAChB,cAAc,CAAE;AAAA;AAAA,QAChB,YAAY;AAAA;AAAA,QACZ,UAAU;AAAA;AAAA,QACV,QAAQ;AAAA;AAAA,QACR,QAAQ;AAAA;AAAA,QACR,gBAAgB,CAAC;AAAA;AAAA,MAClB;AAAA,MACD,eAAe;AAAA,QACb,EAAE,OAAO,OAAO,OAAO,UAAW;AAAA,QAClC,EAAE,OAAO,OAAO,OAAO,aAAc;AAAA,QACrC,EAAE,OAAO,OAAO,OAAO,YAAY;AAAA,MACpC;AAAA,MACD,kBAAkB;AAAA,MAClB,uBAAuB;AAAA,MACvB,OAAO,CAAE;AAAA,MACT,WAAW,CAAE;AAAA,MACb,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,oBAAoB;AAAA,MACpB,eAAe;AAAA,QACb,gBAAgB;AAAA,QAChB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,aAAa;AAAA,QACb,QAAQ,CAAE;AAAA,QACV,UAAU;AAAA,QACV,WAAW;AAAA,MACZ;AAAA;AAAA,MAGD,qBAAqB;AAAA;AAAA,MACrB,gBAAgB;AAAA,MAChB,aAAa,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,MAC9B,OAAO,CAAE;AAAA,MACT,QAAQ,CAAE;AAAA,MACV,MAAM,CAAE;AAAA,MACR,OAAO,CAAE;AAAA,MACT,SAAS,CAAE;AAAA,MACX,SAAS,CAAE;AAAA,MACX,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,MACL,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ;AAAA;AAAA,MAGR,mBAAmB;AAAA,MACnB,qBAAqB,CAAC;AAAA;EAEzB;AAAA,EACD,UAAU;AAAA;AAAA,IAER,oBAAoB;AAClB,UAAI,CAAC,KAAK,mBAAmB;AAC3B,eAAO,KAAK;AAAA,MACd;AAEA,YAAM,UAAU,KAAK,kBAAkB,YAAW;AAClD,aAAO,KAAK,UAAU;AAAA,QAAO,UAC3B,KAAK,KAAK,cAAc,SAAS,OAAO,KACvC,KAAK,kBAAkB,KAAK,eAAe,cAAc,SAAS,OAAO;AAAA;IAE9E;AAAA,EACD;AAAA,EACD,OAAO,SAAS;AAEd,QAAI,QAAQ,SAAS;AACnB,WAAK,SAAS,iBAAiB,QAAQ;AACvC,WAAK,gBAAgB,QAAQ,OAAO;AAAA,IACtC;AAGA,SAAK,gBAAe;AAGpB,SAAK,cAAa;AAGlB,UAAM,MAAM,oBAAI;AAChB,SAAK,SAAS,aAAa,KAAK,eAAe,GAAG;AAClD,SAAK,SAAS,WAAW,KAAK,eAAe,GAAG;AAAA,EACjD;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,eAAe,MAAM;AACnB,YAAM,OAAO,KAAK;AAClB,YAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,YAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,YAAM,QAAQ,OAAO,KAAK,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AACrD,YAAM,UAAU,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACzD,YAAM,UAAU,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AAEzD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,OAAO,IAAI,OAAO;AAAA,IAC9D;AAAA;AAAA,IAGD,kBAAkB;AAChB,WAAK,UAAU;AACfA,gBAAS,UAAC,eAAe,EAAE,EACxB,KAAK,SAAO;AACX,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAChC,eAAK,QAAQ,IAAI,QAAQ,CAAA;AAGzB,cAAI,KAAK,SAAS,gBAAgB;AAChC,kBAAM,eAAe,KAAK,MAAM,KAAK,UAAQ,KAAK,MAAM,KAAK,SAAS,cAAc;AACpF,gBAAI,cAAc;AAChB,mBAAK,mBAAmB,aAAa;AAAA,YACvC;AAAA,UACF;AAAA,eACK;AACLC,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,OACD,EACA,MAAM,SAAO;AACZA,sBAAA,MAAA,MAAA,SAAA,yCAAc,eAAe,GAAG;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,OACF,EACA,QAAQ,MAAM;AACb,aAAK,UAAU;AAAA,MACjB,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,gBAAgB,QAAQ;AACtBD,gBAAS,UAAC,cAAc,MAAM,EAC3B,KAAK,SAAO;AACX,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAChC,eAAK,mBAAmB,IAAI,KAAK;AAGjC,cAAI,IAAI,KAAK,eAAe,IAAI,KAAK,YAAY,SAAS,GAAG;AAE3D,iBAAK,kBAAkB,MAAM;AAAA,UAC/B;AAGA,eAAK,gBAAgB,MAAM;AAAA,QAC7B;AAAA,OACD,EACA,MAAM,SAAO;AACZC,sBAAA,MAAA,MAAA,SAAA,yCAAc,eAAe,GAAG;AAAA,MAClC,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,gBAAgB,QAAQ;AACtBD,gBAAAA,UAAU,YAAY,EAAE,QAAgB,EACrC,KAAK,SAAO;AACX,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAEhC,cAAI,IAAI,QAAQ,IAAI,KAAK,SAAS,GAAG;AACnC,iBAAK,SAAS,iBAAiB,IAAI,KAAK,IAAI,WAAS;AAAA,cACnD,gBAAgB,KAAK;AAAA,cACrB,cAAc;AAAA,cACd,aAAa;AAAA,cACb,aAAa;AAAA,cACb,QAAQ,CAAE;AAAA,cACV,UAAU;AAAA,cACV,WAAW;AAAA;AAAA,cAEX,WAAW;AAAA,gBACT,UAAU,KAAK;AAAA,gBACf,YAAY,KAAK;AAAA,gBACjB,UAAU,KAAK,YAAY,OAAO,KAAK,EAAE;AAAA,gBACzC,cAAc,KAAK,gBAAgB;AAAA,gBACnC,WAAW,KAAK,aAAa;AAAA,gBAC7B,MAAM,KAAK,QAAQ;AAAA,gBACnB,aAAa,KAAK,eAAe;AAAA,gBACjC,aAAa,KAAK,eAAe;AAAA,gBACjC,YAAY,KAAK,cAAc;AAAA,gBAC/B,aAAa,KAAK,eAAe;AAAA,cACnC;AAAA,YACD,EAAC;AAAA,iBACG;AAELC,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AACD,iBAAK,SAAS,iBAAiB;UACjC;AAAA,eACK;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,OACD,EACA,MAAM,SAAO;AACZA,sBAAA,MAAA,MAAA,SAAA,yCAAc,eAAe,GAAG;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,kBAAkB,QAAQ;AACxBD,gBAAS,UAAC,iBAAiB,MAAM,EAC9B,KAAK,SAAO;AACX,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAChC,eAAK,YAAY,IAAI,QAAQ,CAAA;AAG7B,eAAK,4BAA2B;AAAA,QAClC;AAAA,OACD,EACA,MAAM,SAAO;AACZC,sBAAA,MAAA,MAAA,SAAA,yCAAc,gBAAgB,GAAG;AAAA,MACnC,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,8BAA8B;AAC5B,UAAI,KAAK,SAAS,gBAAgB,KAAK,SAAS,aAAa,SAAS,KAAK,KAAK,UAAU,SAAS,GAAG;AACpG,cAAM,gBAAgB,KAAK,UAAU;AAAA,UAAO,UAC1C,KAAK,SAAS,aAAa,SAAS,KAAK,EAAE;AAAA;AAG7C,YAAI,cAAc,SAAS,GAAG;AAC5B,eAAK,wBAAwB,cAAc,IAAI,UAAQ,KAAK,IAAI,EAAE,KAAK,GAAG;AAG1E,cAAI,KAAK,sBAAsB,SAAS,IAAI;AAC1C,iBAAK,wBAAwB,IAAI,cAAc,MAAM;AAAA,UACvD;AAAA,eACK;AACL,eAAK,wBAAwB;AAAA,QAC/B;AAAA,aACK;AACL,aAAK,wBAAwB;AAAA,MAC/B;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB;AACdD,gBAAAA,UAAU,iBAAiB,EACxB,KAAK,SAAO;AACX,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAChC,eAAK,YAAY,IAAI,QAAQ,CAAA;AAG7B,gBAAM,WAAWC,cAAAA,MAAI,eAAe,UAAU;AAC9C,cAAI,YAAY,SAAS,QAAQ;AAC/B,iBAAK,SAAS,eAAe,CAAC,SAAS,MAAM;AAC7C,iBAAK,4BAA2B;AAAA,UAClC;AAAA,QACF;AAAA,OACD,EACA,MAAM,SAAO;AACZA,sBAAA,MAAA,MAAA,SAAA,yCAAc,cAAc,GAAG;AAAA,MACjC,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,mBAAmB;AACjB,UAAI,KAAK,MAAM,WAAW,GAAG;AAC3BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,YAAM,YAAY,KAAK,MAAM,IAAI,UAAQ,KAAK,IAAI;AAClDA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU;AAAA,QACV,SAAS,SAAO;AACd,gBAAM,QAAQ,IAAI;AAClB,eAAK,SAAS,iBAAiB,KAAK,MAAM,KAAK,EAAE;AACjD,eAAK,mBAAmB,KAAK,MAAM,KAAK,EAAE;AAG1C,eAAK,gBAAgB,KAAK,SAAS,cAAc;AAAA,QACnD;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,uBAAuB;AACrB,UAAI,KAAK,UAAU,WAAW,GAAG;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAGA,WAAK,sBAAsB,CAAC,GAAG,KAAK,SAAS,YAAY;AAGzD,WAAK,MAAM,cAAc;IAC1B;AAAA;AAAA,IAGD,sBAAsB;AACpB,WAAK,sBAAsB;AAC3B,WAAK,mBAAmB,KAAK,SAAS,UAAU;AAChD,WAAK,MAAM,cAAc;IAC1B;AAAA;AAAA,IAGD,oBAAoB;AAClB,WAAK,sBAAsB;AAC3B,WAAK,mBAAmB,KAAK,SAAS,QAAQ;AAC9C,WAAK,MAAM,cAAc;IAC1B;AAAA;AAAA,IAGD,sBAAsB;AACpB,WAAK,MAAM,cAAc;IAC1B;AAAA;AAAA,IAGD,mBAAmB,aAAa;AAC9B,UAAI;AAEJ,UAAI,aAAa;AACf,eAAO,IAAI,KAAK,WAAW;AAC3B,YAAI,MAAM,KAAK,QAAO,CAAE,GAAG;AACzB,iBAAO,oBAAI;QACb;AAAA,aACK;AACL,eAAO,oBAAI;MACb;AAEA,WAAK,OAAO,KAAK;AACjB,WAAK,QAAQ,KAAK,SAAQ,IAAK;AAC/B,WAAK,MAAM,KAAK;AAChB,WAAK,OAAO,KAAK;AACjB,WAAK,SAAS,KAAK;AACnB,WAAK,SAAS,KAAK;AAGnB,WAAK,QAAQ;AACb,eAAS,IAAI,KAAK,OAAO,IAAI,KAAK,KAAK,OAAO,IAAI,KAAK;AACrD,aAAK,MAAM,KAAK,CAAC;AAAA,MACnB;AAGA,WAAK,SAAS;AACd,eAAS,IAAI,GAAG,KAAK,IAAI,KAAK;AAC5B,aAAK,OAAO,KAAK,CAAC;AAAA,MACpB;AAEA,WAAK,WAAU;AAGf,WAAK,QAAQ;AACb,eAAS,IAAI,GAAG,KAAK,IAAI,KAAK;AAC5B,aAAK,MAAM,KAAK,CAAC;AAAA,MACnB;AAGA,WAAK,UAAU;AACf,WAAK,UAAU;AACf,eAAS,IAAI,GAAG,KAAK,IAAI,KAAK;AAC5B,aAAK,QAAQ,KAAK,CAAC;AACnB,aAAK,QAAQ,KAAK,CAAC;AAAA,MACrB;AAGA,WAAK,kBAAiB;AAAA,IACvB;AAAA;AAAA,IAGD,aAAa;AACX,UAAI,cAAc;AAGlB,UAAI,KAAK,UAAU,GAAG;AAEpB,sBAAe,KAAK,OAAO,MAAM,KAAK,KAAK,OAAO,QAAQ,KAAM,KAAK,OAAO,QAAQ,IAAI,KAAK;AAAA,MAC/F,WAAW,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,SAAS,KAAK,KAAK,GAAG;AAC7C,sBAAc;AAAA,MAChB;AAEA,WAAK,OAAO;AACZ,eAAS,IAAI,GAAG,KAAK,aAAa,KAAK;AACrC,aAAK,KAAK,KAAK,CAAC;AAAA,MAClB;AAGA,UAAI,KAAK,MAAM,aAAa;AAC1B,aAAK,MAAM;AAAA,MACb;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB;AAClB,YAAM,YAAY,KAAK,MAAM,QAAQ,KAAK,IAAI;AAC9C,YAAM,aAAa,KAAK,OAAO,QAAQ,KAAK,KAAK;AACjD,YAAM,WAAW,KAAK,KAAK,QAAQ,KAAK,GAAG;AAC3C,YAAM,YAAY,KAAK,MAAM,QAAQ,KAAK,IAAI;AAC9C,YAAM,cAAc,KAAK,QAAQ,QAAQ,KAAK,MAAM;AACpD,YAAM,cAAc,KAAK,QAAQ,QAAQ,KAAK,MAAM;AAEpD,WAAK,cAAc;AAAA,QACjB,cAAc,KAAK,YAAY;AAAA,QAC/B,eAAe,KAAK,aAAa;AAAA,QACjC,aAAa,KAAK,WAAW;AAAA,QAC7B,cAAc,KAAK,YAAY;AAAA,QAC/B,gBAAgB,KAAK,cAAc;AAAA,QACnC,gBAAgB,KAAK,cAAc;AAAA;IAEtC;AAAA;AAAA,IAGD,qBAAqB,GAAG;AACtB,YAAM,MAAM,EAAE,OAAO;AAGrB,WAAK,OAAO,KAAK,MAAM,IAAI,CAAC,CAAC;AAC7B,WAAK,QAAQ,KAAK,OAAO,IAAI,CAAC,CAAC;AAG/B,WAAK,WAAU;AAGf,UAAI,IAAI,CAAC,KAAK,KAAK,KAAK,QAAQ;AAC9B,YAAI,CAAC,IAAI;AAAA,MACX;AAEA,WAAK,MAAM,KAAK,KAAK,IAAI,CAAC,CAAC;AAC3B,WAAK,OAAO,KAAK,MAAM,IAAI,CAAC,CAAC;AAC7B,WAAK,SAAS,KAAK,QAAQ,IAAI,CAAC,CAAC;AACjC,WAAK,SAAS,KAAK,QAAQ,IAAI,CAAC,CAAC;AAAA,IAClC;AAAA;AAAA,IAGD,kBAAkB;AAEhB,YAAM,cAAc,KAAK;AAGzB,UAAI,KAAK,wBAAwB,SAAS;AACxC,aAAK,SAAS,aAAa;AAG3B,YAAI,KAAK,SAAS,YAAY,IAAI,KAAK,KAAK,SAAS,UAAU,IAAI,IAAI,KAAK,KAAK,SAAS,QAAQ,GAAG;AACnG,eAAK,SAAS,WAAW;AAAA,QAC3B;AAAA,aACK;AACL,aAAK,SAAS,WAAW;AAGzB,YAAI,KAAK,SAAS,cAAc,IAAI,KAAK,KAAK,SAAS,QAAQ,IAAI,IAAI,KAAK,KAAK,SAAS,UAAU,GAAG;AACrGA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AACD,eAAK,SAAS,WAAW,KAAK,SAAS;AAAA,QACzC;AAAA,MACF;AAGA,WAAK,oBAAmB;AAAA,IACzB;AAAA;AAAA,IAGD,uBAAuB;AACrB,YAAM,OAAO,KAAK;AAClB,YAAM,QAAQ,OAAO,KAAK,KAAK,EAAE,SAAS,GAAG,GAAG;AAChD,YAAM,MAAM,OAAO,KAAK,GAAG,EAAE,SAAS,GAAG,GAAG;AAC5C,YAAM,OAAO,OAAO,KAAK,IAAI,EAAE,SAAS,GAAG,GAAG;AAC9C,YAAM,SAAS,OAAO,KAAK,MAAM,EAAE,SAAS,GAAG,GAAG;AAClD,YAAM,SAAS,OAAO,KAAK,MAAM,EAAE,SAAS,GAAG,GAAG;AAElD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM,IAAI,MAAM;AAAA,IAC3D;AAAA;AAAA,IAGD,iBAAiB,OAAO;AACtB,WAAK,qBAAqB;AAG1B,YAAM,aAAa,KAAK,MAAM,KAAK,UAAU,KAAK,SAAS,eAAe,KAAK,CAAC,CAAC;AAGjF,UAAI,CAAC,WAAW,QAAQ;AACtB,mBAAW,SAAS;MACtB;AAEA,WAAK,gBAAgB;AACrB,WAAK,MAAM,YAAY;IACxB;AAAA;AAAA,IAGD,mBAAmB;AACjB,WAAK,MAAM,YAAY;IACxB;AAAA;AAAA,IAGD,aAAa;AAEX,UAAI,KAAK,cAAc,aAAa,KAAK,cAAc,UAAU,eAAe,CAAC,KAAK,cAAc,aAAa;AAC/GA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAGA,UAAI,KAAK,sBAAsB,GAAG;AAEhC,YAAI,CAAC,KAAK,cAAc,QAAQ;AAC9B,eAAK,cAAc,SAAS;QAC9B;AAEA,aAAK,SAAS,eAAe,KAAK,kBAAkB,IAAI,KAAK,MAAM,KAAK,UAAU,KAAK,aAAa,CAAC;AAAA,MACvG;AAEA,WAAK,iBAAgB;AAAA,IACtB;AAAA;AAAA,IAGD,cAAc;;AACZA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO,OAAK,UAAK,cAAc,WAAnB,mBAA2B,WAAU;AAAA,QACjD,SAAS,CAAC,QAAQ;AAChB,gBAAM,gBAAgB,IAAI;AAC1B,gBAAM,YAAY,cAAc,IAAI,WAAS;AAAA,YAC3C,KAAK;AAAA,YACL,MAAM;AAAA,UACP,EAAC;AAEF,cAAI,CAAC,KAAK,cAAc,QAAQ;AAC9B,iBAAK,cAAc,SAAS;UAC9B;AAEA,eAAK,cAAc,SAAS,CAAC,GAAG,KAAK,cAAc,QAAQ,GAAG,SAAS;AAAA,QACzE;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,YAAY,OAAO;AACjB,WAAK,cAAc,OAAO,OAAO,OAAO,CAAC;AAAA,IAC1C;AAAA;AAAA,IAGD,cAAc;AACZA,oBAAAA,MAAI,YAAY;AAAA,QACd,MAAM;AAAA,QACN,SAAS,CAAC,QAAQ;AAChB,eAAK,cAAc,WAAW,IAAI;AAClC,eAAK,cAAc,YAAY,IAAI;AAAA,QACpC;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAc,MAAA,MAAA,SAAA,yCAAA,WAAW,GAAG;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,eAAe;AACb,UAAI,CAAC,KAAK,SAAS,gBAAgB;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,UAAI,KAAK,SAAS,aAAa,WAAW,GAAG;AAC3CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,KAAK,SAAS,YAAY;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,KAAK,SAAS,UAAU;AAC3BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,UAAI,IAAI,KAAK,KAAK,SAAS,QAAQ,IAAI,IAAI,KAAK,KAAK,SAAS,UAAU,GAAG;AACzEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,eAAe;AACb,UAAI,CAAC,KAAK,gBAAgB;AACxB;AAAA,MACF;AAGAA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,iBAAK,SAAQ;AAAA,UACf;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,WAAW;AACT,WAAK,aAAa;AAGlB,YAAM,aAAa;AAAA,QACjB,gBAAgB,KAAK,SAAS;AAAA;AAAA,QAC9B,aAAa,KAAK,SAAS,aAAa,CAAC,KAAK;AAAA;AAAA,QAC9C,YAAY,KAAK,SAAS;AAAA;AAAA,QAC1B,UAAU,KAAK,SAAS;AAAA;AAAA,QACxB,QAAQ,KAAK,SAAS;AAAA;AAAA,QACtB,QAAQ,KAAK,SAAS,UAAU;AAAA;AAAA,QAChC,gBAAgB,CAAG;AAAA;AAAA;AAIrB,UAAI,KAAK,SAAS,kBAAkB,KAAK,SAAS,eAAe,SAAS,GAAG;AAC3E,mBAAW,iBAAiB,KAAK,SAAS,eAAe,IAAI,YAAU;AAErE,gBAAM,cAAc;AAAA,YAClB,gBAAgB,OAAO;AAAA;AAAA,YACvB,cAAc,OAAO;AAAA;AAAA,YACrB,aAAa,OAAO,eAAe,OAAO,mBAAmB;AAAA;AAAA,YAC7D,aAAa,OAAO,eAAe;AAAA;AAAA,YACnC,UAAU,OAAO,WAAW,OAAO,OAAO,QAAQ,IAAI;AAAA;AAAA,YACtD,WAAW,OAAO,YAAY,OAAO,OAAO,SAAS,IAAI;AAAA;AAAA;AAI3D,cAAI;AAEF,gBAAI,OAAO,UAAU,OAAO,OAAO,QAAQ;AACzC,oBAAM,cAAc,MAAM,KAAK,OAAO,MAAM;AAC5C,0BAAY,SAAS,YAAY,OAAO,OAAO,EAAE;AAAA,gBAAI,SACnD,OAAO,QAAQ,WAAW,MACzB,OAAO,IAAI,MAAM,IAAI,MAAM;AAAA,cAC9B,EAAE,OAAO,SAAO,GAAG;AAAA,mBACd;AACL,0BAAY,SAAS;YACvB;AAAA,UACF,SAAS,GAAG;AACVA,0BAAA,MAAA,MAAA,SAAA,yCAAc,aAAa,CAAC;AAC5B,wBAAY,SAAS;UACvB;AAEA,iBAAO;AAAA,QACT,CAAC;AAAA,MACH;AAEAA,0BAAA,MAAA,OAAA,0CAAY,SAAS,KAAK,UAAU,UAAU,CAAC;AAE/CD,gBAAS,UAAC,mBAAmB,UAAU,EACpC,KAAK,SAAO;AACX,YAAI,IAAI,SAAS,KAAK;AACpBC,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAGD,qBAAW,MAAM;AACfA,0BAAG,MAAC,aAAY;AAAA,UACjB,GAAE,IAAI;AAAA,eACF;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,IAAI,WAAW;AAAA,YACtB,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,OACD,EACA,MAAM,SAAO;AACZA,sBAAA,MAAA,MAAA,SAAA,0CAAc,aAAa,GAAG;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,OACF,EACA,QAAQ,MAAM;AACb,aAAK,aAAa;AAAA,MACpB,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,wBAAwB;AACtB,WAAK,MAAM,cAAc;AACzB,WAAK,oBAAoB;AAAA,IAC1B;AAAA;AAAA,IAGD,qBAAqB,GAAG;AAEtB,WAAK,sBAAsB,EAAE,OAAO,MAAM,IAAI,QAAM,SAAS,EAAE,CAAC;AAAA,IACjE;AAAA;AAAA,IAGD,2BAA2B;AAEzB,WAAK,SAAS,eAAe,CAAC,GAAG,KAAK,mBAAmB;AAGzD,WAAK,4BAA2B;AAGhC,WAAK,sBAAqB;AAAA,IAC3B;AAAA;AAAA,IAGD,mBAAmB,YAAY;AAC7B,aAAO,KAAK,oBAAoB,SAAS,UAAU;AAAA,IACrD;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtiCA,GAAG,WAAW,eAAe;"}