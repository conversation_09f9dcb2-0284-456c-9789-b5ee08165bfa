/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.valve-detail-page {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 30rpx;
}
.header {
  display: flex;
  align-items: center;
  height: 88rpx;
  background-color: #fff;
  padding: 0 30rpx;
  position: relative;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}
.header .back-button {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.header .page-title {
  flex: 1;
  text-align: center;
  font-size: 34rpx;
  font-weight: 500;
  padding-right: 60rpx;
}
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.valve-content {
  padding: 20rpx 30rpx;
}
.status-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
}
.status-card .status-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.status-card .status-icon.open {
  background-color: rgba(46, 204, 113, 0.1);
}
.status-card .status-icon.open .iconfont {
  color: #2ecc71;
}
.status-card .status-icon.closed {
  background-color: rgba(149, 165, 166, 0.1);
}
.status-card .status-icon.closed .iconfont {
  color: #95a5a6;
}
.status-card .status-icon.error {
  background-color: rgba(231, 76, 60, 0.1);
}
.status-card .status-icon.error .iconfont {
  color: #e74c3c;
}
.status-card .status-icon .iconfont {
  font-size: 60rpx;
}
.status-card .status-info {
  flex: 1;
}
.status-card .status-info .status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.status-card .status-info .status-header .valve-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  margin-right: 20rpx;
}
.status-card .status-info .status-detail .detail-item {
  margin-bottom: 12rpx;
}
.status-card .status-info .status-detail .detail-item .detail-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}
.status-card .status-info .status-detail .detail-item .detail-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}
.status-tag {
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #fff;
}
.status-tag.status-open {
  background-color: #2ecc71;
}
.status-tag.status-closed {
  background-color: #95a5a6;
}
.status-tag.status-error {
  background-color: #e74c3c;
}
.control-panel {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.control-panel .panel-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}
.control-panel .panel-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 8rpx;
  width: 6rpx;
  height: 28rpx;
  background-color: #1989fa;
  border-radius: 3rpx;
}
.control-panel .opening-control {
  margin-bottom: 30rpx;
}
.control-panel .opening-control .control-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}
.control-panel .opening-control .opening-slider {
  margin: 20rpx 0;
}
.control-panel .control-buttons {
  display: flex;
  justify-content: space-between;
}
.control-panel .control-buttons .control-btn {
  width: 30%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 28rpx;
}
.control-panel .control-buttons .control-btn.set-btn {
  background-color: #1989fa;
  color: #fff;
}
.control-panel .control-buttons .control-btn.open-btn {
  background-color: #2ecc71;
  color: #fff;
}
.control-panel .control-buttons .control-btn.close-btn {
  background-color: #e74c3c;
  color: #fff;
}
.operation-records {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.operation-records .records-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}
.operation-records .records-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 8rpx;
  width: 6rpx;
  height: 28rpx;
  background-color: #1989fa;
  border-radius: 3rpx;
}
.operation-records .timeline {
  position: relative;
  padding-left: 30rpx;
}
.operation-records .timeline::before {
  content: "";
  position: absolute;
  left: 10rpx;
  top: 0;
  bottom: 0;
  width: 2rpx;
  background-color: #e0e0e0;
}
.operation-records .timeline .timeline-item {
  position: relative;
  padding-bottom: 30rpx;
}
.operation-records .timeline .timeline-item:last-child {
  padding-bottom: 0;
}
.operation-records .timeline .timeline-item .timeline-dot {
  position: absolute;
  left: -30rpx;
  top: 10rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #1989fa;
  z-index: 1;
}
.operation-records .timeline .timeline-item .timeline-content .timeline-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
  display: block;
}
.operation-records .timeline .timeline-item .timeline-content .timeline-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}
.operation-records .timeline .timeline-item .timeline-content .timeline-operator {
  font-size: 24rpx;
  color: #666;
  display: block;
}