"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const utils_api = require("./utils/api.js");
const store_attendance = require("./store/attendance.js");
if (!Math) {
  "./pages/user/login.js";
  "./pages/user/agreement.js";
  "./pages/home/<USER>";
  "./pages/device/list.js";
  "./pages/device/detail.js";
  "./pages/hes/list.js";
  "./pages/hes/detail.js";
  "./pages/hes/control.js";
  "./pages/valves/control.js";
  "./pages/valves/detail.js";
  "./pages/payment/stats.js";
  "./pages/message/center.js";
  "./pages/user/info.js";
  "./pages/user/profile.js";
  "./pages/user/message-settings.js";
  "./pages/user/change-password.js";
  "./pages/user/account-binding.js";
  "./pages/user/faq.js";
  "./pages/user/about.js";
  "./pages/workorder/detail.js";
  "./pages/workorder/list.js";
  "./pages/workorder/complete.js";
  "./pages/workorder/transfer.js";
  "./pages/patrol/plans.js";
  "./pages/patrol/detail.js";
  "./pages/patrol/execute.js";
  "./pages/patrol/create.js";
  "./pages/patrol/records.js";
  "./pages/patrol/record_detail.js";
  "./pages/patrol/create_record.js";
  "./pages/fault/report.js";
  "./pages/fault/createfault.js";
  "./pages/fault/list.js";
  "./pages/fault/detail.js";
  "./components/multi-selector.js";
  "./components/datetime-picker.js";
  "./pages/attendance/clock-in.js";
  "./pages/attendance/statistics.js";
  "./pages/attendance/all-records.js";
  "./pages/attendance/admin/rules.js";
  "./pages/messageTest/messageTest.js";
  "./pages/common/video-player.js";
}
const _sfc_main = {
  onLaunch: function() {
    common_vendor.index.__f__("log", "at App.vue:10", "App Launch");
    this.checkAppUpdate();
    this.preloadResources();
  },
  onShow: function() {
  },
  onHide: function() {
  },
  methods: {
    checkAppUpdate() {
      common_vendor.index.__f__("log", "at App.vue:27", "检查应用更新");
    },
    preloadResources() {
      common_vendor.index.__f__("log", "at App.vue:32", "预加载资源");
    }
  },
  globalData: {
    useCustomTabBar: true
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {};
}
const App = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
function createApp() {
  const app = common_vendor.createSSRApp(App);
  const store = common_vendor.createStore({
    modules: {
      attendance: store_attendance.attendanceModule
    }
  });
  app.use(store);
  app.config.globalProperties.$baseUrl = utils_api.BASE_URL;
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
