"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      loading: false,
      loadingText: {
        contentdown: "加载中...",
        contentrefresh: "加载中...",
        contentnomore: "没有更多数据"
      },
      // 筛选条件
      heatUnitOptions: ["东区小区", "西区小区", "南区小区"],
      heatUnitIndex: 0,
      heatUnitId: null,
      buildingOptions: [],
      buildingIndex: 0,
      buildingId: null,
      unitOptions: [],
      unitIndex: 0,
      unitId: null,
      houseNumber: "",
      // 阀门数据
      valvesList: [],
      currentValve: null,
      // 阀门控制
      targetOpenDegree: 0
    };
  },
  onLoad() {
    this.loadHeatUnits();
  },
  methods: {
    // 加载小区列表
    loadHeatUnits() {
      this.heatUnitOptions = ["东区小区", "西区小区", "南区小区"];
      this.heatUnitIndex = 0;
      this.heatUnitId = 1;
    },
    // 小区选择变化
    handleHeatUnitChange(e) {
      this.heatUnitIndex = e.detail.value;
      this.heatUnitId = this.heatUnitIndex + 1;
      this.buildingOptions = [];
      this.buildingIndex = 0;
      this.buildingId = null;
      this.unitOptions = [];
      this.unitIndex = 0;
      this.unitId = null;
      this.loadBuildings();
    },
    // 加载楼栋列表
    loadBuildings() {
      setTimeout(() => {
        this.buildingOptions = ["1号楼", "2号楼", "3号楼"];
        this.buildingIndex = 0;
        this.buildingId = 1;
      }, 500);
    },
    // 楼栋选择变化
    handleBuildingChange(e) {
      this.buildingIndex = e.detail.value;
      this.buildingId = this.buildingIndex + 1;
      this.unitOptions = [];
      this.unitIndex = 0;
      this.unitId = null;
      this.loadUnits();
    },
    // 加载单元列表
    loadUnits() {
      setTimeout(() => {
        this.unitOptions = ["1单元", "2单元", "3单元"];
        this.unitIndex = 0;
        this.unitId = 1;
      }, 500);
    },
    // 单元选择变化
    handleUnitChange(e) {
      this.unitIndex = e.detail.value;
      this.unitId = this.unitIndex + 1;
    },
    // 搜索阀门
    searchValves() {
      this.loading = true;
      this.valvesList = [];
      ({
        heat_unit_id: this.heatUnitId
      });
      if (this.buildingId) {
        this.buildingId;
      }
      if (this.unitId) {
        this.unitId;
      }
      if (this.houseNumber.trim()) {
        this.houseNumber.trim();
      }
      setTimeout(() => {
        this.valvesList = [
          {
            id: 1,
            name: this.heatUnitOptions[this.heatUnitIndex] + " " + this.buildingOptions[this.buildingIndex] + " " + this.unitOptions[this.unitIndex] + " " + (this.houseNumber || "101") + " 入户阀门",
            status: "open",
            openDegree: 80,
            lastOperationTime: "2025-04-03 14:35:20",
            operator: "系统",
            operationType: "自动开启(缴费成功)"
          }
        ];
        this.loading = false;
      }, 1e3);
    },
    // 选择阀门
    selectValve(valve) {
      common_vendor.index.navigateTo({
        url: `/pages/valves/detail?id=${valve.id}`
      });
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        "open": "已开启",
        "closed": "已关闭",
        "error": "异常"
      };
      return statusMap[status] || status;
    },
    // 返回上一页
    navigateBack() {
      common_vendor.index.navigateBack();
    }
  }
};
if (!Array) {
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  _easycom_uni_load_more2();
}
const _easycom_uni_load_more = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-load-more/uni-load-more.js";
if (!Math) {
  _easycom_uni_load_more();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.heatUnitOptions[$data.heatUnitIndex] || "请选择"),
    b: common_vendor.o((...args) => $options.handleHeatUnitChange && $options.handleHeatUnitChange(...args)),
    c: $data.heatUnitIndex,
    d: $data.heatUnitOptions,
    e: common_vendor.t($data.buildingOptions[$data.buildingIndex] || "请选择"),
    f: common_vendor.o((...args) => $options.handleBuildingChange && $options.handleBuildingChange(...args)),
    g: $data.buildingIndex,
    h: $data.buildingOptions,
    i: !$data.heatUnitId,
    j: common_vendor.t($data.unitOptions[$data.unitIndex] || "请选择"),
    k: common_vendor.o((...args) => $options.handleUnitChange && $options.handleUnitChange(...args)),
    l: $data.unitIndex,
    m: $data.unitOptions,
    n: !$data.buildingId,
    o: $data.houseNumber,
    p: common_vendor.o(($event) => $data.houseNumber = $event.detail.value),
    q: common_vendor.o((...args) => $options.searchValves && $options.searchValves(...args)),
    r: $data.loading
  }, $data.loading ? {
    s: common_vendor.p({
      status: "loading",
      ["content-text"]: $data.loadingText
    })
  } : $data.valvesList.length === 0 && !$data.loading ? {
    v: common_assets._imports_0$1
  } : {
    w: common_vendor.f($data.valvesList, (valve, index, i0) => {
      return {
        a: common_vendor.t(valve.name),
        b: common_vendor.t($options.getStatusText(valve.status)),
        c: common_vendor.n("status-" + valve.status),
        d: common_vendor.t(valve.openDegree || 0),
        e: common_vendor.t(valve.lastOperationTime || "无"),
        f: index,
        g: common_vendor.o(($event) => $options.selectValve(valve), index)
      };
    })
  }, {
    t: $data.valvesList.length === 0 && !$data.loading
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/valves/control.js.map
