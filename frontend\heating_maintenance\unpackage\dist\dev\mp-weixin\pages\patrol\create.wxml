<view class="patrol-create-container data-v-778b6e1a"><form class="data-v-778b6e1a" bindsubmit="{{S}}"><view class="form-card data-v-778b6e1a"><view class="card-title data-v-778b6e1a">基本信息</view><view class="form-item data-v-778b6e1a"><text class="form-label required data-v-778b6e1a">计划名称</text><input class="form-input plan-name-input data-v-778b6e1a" type="text" placeholder="请输入计划名称" maxlength="{{50}}" value="{{a}}" bindinput="{{b}}"/></view><view class="form-item data-v-778b6e1a"><text class="form-label required data-v-778b6e1a">巡检周期</text><picker class="form-picker data-v-778b6e1a" range="{{d}}" range-key="label" value="{{e}}" bindchange="{{f}}"><view class="picker-value placeholder-style data-v-778b6e1a" style="padding-left:20rpx">{{c}}</view></picker></view><view class="form-item data-v-778b6e1a"><text class="form-label required data-v-778b6e1a">巡检类型</text><picker class="form-picker data-v-778b6e1a" range="{{h}}" range-key="label" value="{{i}}" bindchange="{{j}}"><view class="picker-value placeholder-style data-v-778b6e1a" style="padding-left:20rpx">{{g}}</view></picker></view><view class="form-item data-v-778b6e1a"><text class="form-label required data-v-778b6e1a">开始日期</text><picker class="form-picker data-v-778b6e1a" mode="date" value="{{l}}" start="2023-01-01" end="2030-12-31" bindchange="{{m}}"><view class="picker-value placeholder-style data-v-778b6e1a" style="padding-left:20rpx">{{k}}</view></picker></view><view class="form-item data-v-778b6e1a"><text class="form-label required data-v-778b6e1a">结束日期</text><picker class="form-picker data-v-778b6e1a" mode="date" value="{{o}}" start="{{p}}" end="2030-12-31" bindchange="{{q}}"><view class="picker-value placeholder-style data-v-778b6e1a" style="padding-left:20rpx">{{n}}</view></picker></view><view class="form-item data-v-778b6e1a"><text class="form-label required data-v-778b6e1a">巡检地点</text><view class="heatunit-selector data-v-778b6e1a" bindtap="{{v}}"><view class="form-input location-input data-v-778b6e1a"><text wx:if="{{r}}" class="data-v-778b6e1a">{{s}}</text><text wx:else class="data-v-778b6e1a">{{t}}</text><text class="arrow-down data-v-778b6e1a">▼</text></view></view></view><view class="form-item data-v-778b6e1a"><text class="form-label data-v-778b6e1a">巡检人</text><view class="staff-selector data-v-778b6e1a" bindtap="{{x}}"><view class="form-input location-input data-v-778b6e1a"><text class="data-v-778b6e1a">{{w}}</text><text class="arrow-down data-v-778b6e1a">▼</text></view></view><view wx:if="{{y}}" class="selected-items data-v-778b6e1a"><view wx:for="{{z}}" wx:for-item="executor" wx:key="c" class="selected-item data-v-778b6e1a"><text class="item-text data-v-778b6e1a">{{executor.a}}</text><text class="remove-icon data-v-778b6e1a" catchtap="{{executor.b}}">×</text></view></view></view><view class="form-item data-v-778b6e1a"><text class="form-label required data-v-778b6e1a">巡检设备</text><view class="device-selector data-v-778b6e1a" bindtap="{{B}}"><view class="form-input location-input data-v-778b6e1a"><text class="data-v-778b6e1a">{{A}}</text><text class="arrow-down data-v-778b6e1a">▼</text></view></view><view wx:if="{{C}}" class="selected-items data-v-778b6e1a"><view wx:for="{{D}}" wx:for-item="device" wx:key="c" class="selected-item data-v-778b6e1a"><text class="item-text data-v-778b6e1a">{{device.a}}</text><text class="remove-icon data-v-778b6e1a" catchtap="{{device.b}}">×</text></view></view></view><view class="form-item data-v-778b6e1a"><text class="form-label data-v-778b6e1a">计划描述</text><block wx:if="{{r0}}"><textarea class="form-textarea data-v-778b6e1a" placeholder="请输入巡检计划描述（选填）" maxlength="{{500}}" value="{{E}}" bindinput="{{F}}"/></block></view><view wx:if="{{G}}" class="form-item data-v-778b6e1a"><text class="form-label required data-v-778b6e1a">巡检间隔(天)</text><input class="form-input data-v-778b6e1a" type="number" placeholder="请输入巡检间隔天数" value="{{H}}" bindinput="{{I}}"/></view><view wx:if="{{J}}" class="form-item data-v-778b6e1a"><text class="form-label required data-v-778b6e1a">巡检星期</text><view class="tag-group data-v-778b6e1a"><view wx:for="{{K}}" wx:for-item="day" wx:key="b" class="{{['tag', 'data-v-778b6e1a', day.c && 'active']}}" bindtap="{{day.d}}">{{day.a}}</view></view></view><view wx:if="{{L}}" class="form-item data-v-778b6e1a"><text class="form-label required data-v-778b6e1a">巡检日期</text><view class="tag-group data-v-778b6e1a"><view wx:for="{{M}}" wx:for-item="day" wx:key="b" class="{{['tag', 'data-v-778b6e1a', day.c && 'active']}}" bindtap="{{day.d}}">{{day.a}}</view></view></view></view><view class="form-card data-v-778b6e1a"><view class="card-header data-v-778b6e1a"><view class="card-title data-v-778b6e1a">巡检任务</view><view bindtap="{{N}}" class="{{['add-button', 'data-v-778b6e1a', O && 'disabled']}}"><text class="iconfont icon-add data-v-778b6e1a"></text><text class="data-v-778b6e1a">添加巡检项目</text></view></view><view wx:if="{{P}}" class="task-list data-v-778b6e1a"><view wx:for="{{Q}}" wx:for-item="task" wx:key="p" class="task-item data-v-778b6e1a"><view class="task-content data-v-778b6e1a"><text class="task-title data-v-778b6e1a">{{task.a}}</text><view class="task-info-row data-v-778b6e1a"><text class="task-info data-v-778b6e1a">所属设备: {{task.b}}</text><text class="{{['importance-tag', 'data-v-778b6e1a', task.d]}}">{{task.c}}</text></view><text class="task-info data-v-778b6e1a">所属类型: {{task.e}}</text><text class="task-info data-v-778b6e1a">检测标准: {{task.f}}</text><text class="task-info data-v-778b6e1a">检测方法: {{task.g}}</text><text wx:if="{{task.h}}" class="task-info data-v-778b6e1a">检测范围: {{task.i}} {{task.j}}</text><text wx:elif="{{task.k}}" class="task-info data-v-778b6e1a">可选值: {{task.l}}</text><text wx:elif="{{task.m}}" class="task-info data-v-778b6e1a">期望值: {{task.n}}</text></view><view class="task-actions data-v-778b6e1a"><text class="delete-icon data-v-778b6e1a" bindtap="{{task.o}}">×</text></view></view></view><view wx:else class="empty-tip data-v-778b6e1a"><text class="data-v-778b6e1a">请添加巡检任务</text></view></view><button class="submit-button data-v-778b6e1a" form-type="submit" style="{{'display:' + R}}">创建巡检计划</button></form><uni-popup wx:if="{{ad}}" class="r data-v-778b6e1a" u-s="{{['d']}}" u-r="heatUnitSelector" u-i="778b6e1a-0" bind:__l="__l" u-p="{{ad}}"><view class="selector heat-unit-selector data-v-778b6e1a"><view class="selector-header data-v-778b6e1a"><text class="selector-title data-v-778b6e1a">选择热用户</text><view class="header-actions data-v-778b6e1a"><text class="confirm-button-header data-v-778b6e1a" bindtap="{{T}}">确定</text><text class="close-button data-v-778b6e1a" bindtap="{{U}}">关闭</text></view></view><view class="selector-content data-v-778b6e1a"><view class="template-search data-v-778b6e1a"><input type="text" placeholder="搜索热用户" confirm-type="search" bindinput="{{V}}" class="search-input data-v-778b6e1a" value="{{W}}"/><text wx:if="{{X}}" class="icon-clear data-v-778b6e1a" bindtap="{{Y}}"></text><view class="search-btn data-v-778b6e1a" bindtap="{{Z}}">搜索</view></view><view class="heatunit-list data-v-778b6e1a"><view wx:for="{{aa}}" wx:for-item="heatUnit" wx:key="d" class="heatunit-item data-v-778b6e1a" bindtap="{{heatUnit.e}}"><text class="heatunit-name data-v-778b6e1a">{{heatUnit.a}}</text><view class="{{['checkbox', 'data-v-778b6e1a', heatUnit.c && 'checked']}}"><text wx:if="{{heatUnit.b}}" class="iconfont icon-check data-v-778b6e1a"></text></view></view></view><view wx:if="{{ab}}" class="empty-tip data-v-778b6e1a"><text class="data-v-778b6e1a">没有找到相关热用户</text></view></view></view></uni-popup><uni-popup wx:if="{{aq}}" class="r data-v-778b6e1a" u-s="{{['d']}}" u-r="deviceSelector" u-i="778b6e1a-1" bind:__l="__l" u-p="{{aq}}"><view class="selector device-selector-popup data-v-778b6e1a"><view class="selector-header data-v-778b6e1a"><text class="selector-title data-v-778b6e1a">选择巡检设备</text><view class="header-actions data-v-778b6e1a"><text class="confirm-button-header data-v-778b6e1a" bindtap="{{ae}}">确定</text><text class="close-button data-v-778b6e1a" bindtap="{{af}}">关闭</text></view></view><view class="selector-content data-v-778b6e1a"><view wx:if="{{ag}}" class="device-select-all-wrapper data-v-778b6e1a"><view class="device-select-all-card data-v-778b6e1a" bindtap="{{am}}"><view class="device-select-all-content data-v-778b6e1a"><view class="{{['device-select-checkbox', 'data-v-778b6e1a', ai && 'checked']}}"><text wx:if="{{ah}}" class="data-v-778b6e1a">✓</text></view><view class="device-select-info data-v-778b6e1a"><view class="device-select-title data-v-778b6e1a"><text class="select-title-text data-v-778b6e1a">全选设备</text><view class="{{['select-status-badge', 'data-v-778b6e1a', al && 'active']}}"><text class="badge-text data-v-778b6e1a">{{aj}}/{{ak}}</text></view></view></view></view></view></view><view class="device-list data-v-778b6e1a"><view wx:for="{{an}}" wx:for-item="device" wx:key="d" class="device-item data-v-778b6e1a" bindtap="{{device.e}}"><text class="device-name data-v-778b6e1a">{{device.a}}</text><view class="{{['checkbox', 'data-v-778b6e1a', device.c && 'checked']}}"><text wx:if="{{device.b}}" class="iconfont icon-check data-v-778b6e1a"></text></view></view></view><view wx:if="{{ao}}" class="empty-tip data-v-778b6e1a"><text class="data-v-778b6e1a">暂无可选设备</text></view></view></view></uni-popup><uni-popup wx:if="{{az}}" class="r data-v-778b6e1a" u-s="{{['d']}}" u-r="staffSelector" u-i="778b6e1a-2" bind:__l="__l" u-p="{{az}}"><view class="selector staff-selector-popup data-v-778b6e1a"><view class="selector-header data-v-778b6e1a"><text class="selector-title data-v-778b6e1a">选择巡检人员</text><view class="header-actions data-v-778b6e1a"><text class="confirm-button-header data-v-778b6e1a" bindtap="{{ar}}">确定</text><text class="close-button data-v-778b6e1a" bindtap="{{as}}">关闭</text></view></view><view class="selector-content data-v-778b6e1a"><view class="staff-list data-v-778b6e1a"><view wx:for="{{at}}" wx:for-item="staff" wx:key="d" class="staff-item data-v-778b6e1a" bindtap="{{staff.e}}"><text class="staff-name data-v-778b6e1a">{{staff.a}}</text><view class="{{['checkbox', 'data-v-778b6e1a', staff.c && 'checked']}}"><text wx:if="{{staff.b}}" class="iconfont icon-check data-v-778b6e1a"></text></view></view></view><view wx:if="{{av}}" class="empty-tip data-v-778b6e1a"><text wx:if="{{aw}}" class="data-v-778b6e1a">当前热用户"{{ax}}"暂无有权限的巡检人员</text><text wx:else class="data-v-778b6e1a">暂无可选人员</text></view></view></view></uni-popup><uni-popup wx:if="{{aY}}" class="r data-v-778b6e1a" u-s="{{['d']}}" u-r="taskSelector" u-i="778b6e1a-3" bind:__l="__l" u-p="{{aY}}"><view class="task-selector task-item-selector data-v-778b6e1a"><view class="selector-header data-v-778b6e1a"><text class="selector-title data-v-778b6e1a">选择巡检项目</text><view class="header-actions data-v-778b6e1a"><text class="confirm-button-header data-v-778b6e1a" bindtap="{{aA}}">确定</text><text class="close-button data-v-778b6e1a" bindtap="{{aB}}">关闭</text></view></view><view class="selector-content data-v-778b6e1a"><view class="data-v-778b6e1a" style="display:flex;align-items:center;padding:0 10rpx 20rpx;position:relative"><picker class="data-v-778b6e1a" style="flex:1;height:80rpx" range="{{aD}}" range-key="name" value="{{aE}}" bindchange="{{aF}}"><view class="data-v-778b6e1a" style="background:#f5f5f5;border-radius:40rpx;padding:0 30rpx;height:80rpx;font-size:28rpx;color:#333;display:flex;justify-content:space-between;align-items:center"><text class="data-v-778b6e1a">{{aC}}</text><text class="data-v-778b6e1a" style="color:#999;font-size:24rpx">▼</text></view></picker></view><view class="template-search data-v-778b6e1a"><input type="text" placeholder="搜索巡检项" confirm-type="search" bindinput="{{aG}}" class="search-input data-v-778b6e1a" value="{{aH}}"/><text wx:if="{{aI}}" class="search-clear data-v-778b6e1a" bindtap="{{aJ}}">×</text><view class="search-btn data-v-778b6e1a" bindtap="{{aK}}">搜索</view></view><view wx:if="{{aL}}" class="data-v-778b6e1a" bindtap="{{aT}}" style="background:linear-gradient(135deg, #f0f8ff 0%, #ffffff 100%);border:2px solid #007aff;border-radius:8px;padding:16px;margin-bottom:16px;box-shadow:0 1px 4px rgba(0, 122, 255, 0.1);display:flex;align-items:center"><view class="data-v-778b6e1a" style="{{'width:' + '20px' + ';' + ('height:' + '20px') + ';' + ('border:' + '2px solid #007aff') + ';' + ('border-radius:' + '6px') + ';' + ('display:' + 'flex') + ';' + ('align-items:' + 'center') + ';' + ('justify-content:' + 'center') + ';' + ('margin-right:' + '12px') + ';' + ('flex-shrink:' + '0') + ';' + ('background-color:' + aN)}}"><text wx:if="{{aM}}" class="data-v-778b6e1a" style="color:#fff;font-size:18px;font-weight:bold">✓</text></view><view class="data-v-778b6e1a" style="flex:1"><view class="data-v-778b6e1a" style="display:flex;justify-content:space-between;align-items:center"><text class="data-v-778b6e1a" style="font-size:14px;font-weight:bold;color:#007aff">全选巡检项</text><view class="data-v-778b6e1a" style="{{'background-color:' + aQ + ';' + ('color:' + aR) + ';' + ('font-size:' + '14px') + ';' + ('padding:' + '2px 8px') + ';' + ('border-radius:' + '14px') + ';' + ('font-weight:' + '600') + ';' + ('border:' + aS)}}"><text class="data-v-778b6e1a">{{aO}}/{{aP}}</text></view></view></view></view><view class="task-templates data-v-778b6e1a"><view wx:for="{{aU}}" wx:for-item="template" wx:key="q" class="{{['template-item', 'data-v-778b6e1a', template.r && 'active']}}" bindtap="{{template.s}}"><view class="{{['checkbox', 'data-v-778b6e1a', template.b && 'checked']}}"><text wx:if="{{template.a}}" class="iconfont icon-check data-v-778b6e1a"></text></view><view class="template-left data-v-778b6e1a"><view class="template-header data-v-778b6e1a"><text class="template-title data-v-778b6e1a">{{template.c}}</text><text class="{{['importance-tag', 'data-v-778b6e1a', template.e]}}">{{template.d}}</text></view><text class="template-info data-v-778b6e1a">所属设备: {{template.f}}</text><text class="template-info data-v-778b6e1a">所属类型: {{template.g}}</text><text class="template-info template-description data-v-778b6e1a">检测标准: {{template.h}}</text><text class="template-info data-v-778b6e1a">检测方法: {{template.i}}</text><text wx:if="{{template.j}}" class="template-info data-v-778b6e1a">检测范围: {{template.k}} {{template.l}}</text><text wx:elif="{{template.m}}" class="template-info data-v-778b6e1a">可选值: {{template.n}}</text><text wx:elif="{{template.o}}" class="template-info data-v-778b6e1a">期望值: {{template.p}}</text></view></view></view><view wx:if="{{aV}}" class="empty-tip data-v-778b6e1a"><text class="data-v-778b6e1a">没有找到相关巡检项模板</text></view></view><view class="selector-content-footer data-v-778b6e1a"><view class="data-v-778b6e1a" style="padding:15rpx 0;border-top:1rpx solid #e5e5e5;background-color:#007aff;box-shadow:0 -4rpx 10rpx rgba(0, 0, 0, 0.1);position:fixed;bottom:0;left:0;right:0;z-index:999"><view class="data-v-778b6e1a" style="display:flex;align-items:center;justify-content:center;padding:15rpx 0"><view class="data-v-778b6e1a" style="width:48rpx;height:48rpx;background-color:white;border-radius:50%;margin-right:16rpx;display:flex;align-items:center;justify-content:center"><text class="data-v-778b6e1a" style="color:#007aff;font-size:28rpx;font-weight:bold">{{aW}}</text></view><text class="data-v-778b6e1a" style="color:white;font-size:30rpx;font-weight:bold">已选择项目</text></view></view></view></view></uni-popup></view>