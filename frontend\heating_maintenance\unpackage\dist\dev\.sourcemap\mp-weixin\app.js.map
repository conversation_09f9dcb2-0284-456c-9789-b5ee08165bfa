{"version": 3, "file": "app.js", "sources": ["App.vue", "main.js"], "sourcesContent": ["<template>\r\n  <div></div>\r\n</template>\r\n\r\n<script>\r\nimport CustomTabBar from \"./components/CustomTabBar.vue\";\r\n\r\nexport default {\r\n  onLaunch: function () {\r\n    console.log(\"App Launch\");\r\n\r\n    // 检查是否有更新\r\n    this.checkAppUpdate();\r\n\r\n    // 预加载一些必要的资源\r\n    this.preloadResources();\r\n  },\r\n  onShow: function () {\r\n    //console.log(\"App Show\");\r\n  },\r\n  onHide: function () {\r\n   // console.log(\"App Hide\");\r\n  },\r\n  methods: {\r\n    checkAppUpdate() {\r\n      // 版本检查逻辑\r\n      console.log(\"检查应用更新\");\r\n    },\r\n\r\n    preloadResources() {\r\n      // 预加载一些资源\r\n      console.log(\"预加载资源\");\r\n    },\r\n  },\r\n  globalData: {\r\n    useCustomTabBar: true,\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n/*每个页面公共css */\r\n@import \"./uni.scss\";\r\n\r\n/* 全局基础样式 */\r\npage {\r\n  background-color: $uni-bg-color-grey;\r\n  font-size: 28rpx;\r\n  color: $uni-text-color;\r\n  font-family: -apple-system, BlinkMacSystemFont, \"Helvetica Neue\", Helvetica, Segoe UI,\r\n    Arial, Roboto, \"PingFang SC\", \"miui\", \"Hiragino Sans GB\", \"Microsoft Yahei\",\r\n    sans-serif;\r\n  padding-bottom: 110rpx;\r\n}\r\n\r\n/* 通用卡片样式 */\r\n.card {\r\n  background-color: #fff;\r\n  border-radius: 12rpx;\r\n  padding: 24rpx;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n/* 通用标题样式 */\r\n.section-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 20rpx;\r\n  position: relative;\r\n  padding-left: 20rpx;\r\n\r\n  &::before {\r\n    content: \"\";\r\n    position: absolute;\r\n    left: 0;\r\n    top: 8rpx;\r\n    width: 8rpx;\r\n    height: 32rpx;\r\n    background-color: $uni-color-primary;\r\n    border-radius: 4rpx;\r\n  }\r\n}\r\n\r\n/* 通用按钮样式 */\r\n.btn-primary {\r\n  background-color: $uni-color-primary;\r\n  color: #fff;\r\n  border-radius: 8rpx;\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n  width: 686rpx;\r\n  text-align: center;\r\n  font-size: 32rpx;\r\n  box-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.3);\r\n}\r\n\r\n.btn-secondary {\r\n  background-color: transparent;\r\n  color: $uni-color-primary;\r\n  border: 1rpx solid $uni-color-primary;\r\n  border-radius: 8rpx;\r\n  height: 64rpx;\r\n  line-height: 64rpx;\r\n  width: 320rpx;\r\n  text-align: center;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.btn-warning {\r\n  background-color: $uni-color-warning;\r\n  color: #fff;\r\n  border-radius: 8rpx;\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n  width: 686rpx;\r\n  text-align: center;\r\n  font-size: 32rpx;\r\n  box-shadow: 0 6rpx 16rpx rgba(250, 173, 20, 0.3);\r\n}\r\n\r\n/* 状态标签样式 */\r\n.status-tag {\r\n  display: inline-block;\r\n  padding: 6rpx 16rpx;\r\n  font-size: 24rpx;\r\n  border-radius: 6rpx;\r\n\r\n  &.primary {\r\n    background-color: rgba(24, 144, 255, 0.1);\r\n    color: $uni-color-primary;\r\n  }\r\n\r\n  &.success {\r\n    background-color: rgba(82, 196, 26, 0.1);\r\n    color: $uni-color-success;\r\n  }\r\n\r\n  &.warning {\r\n    background-color: rgba(250, 173, 20, 0.1);\r\n    color: $uni-color-warning;\r\n  }\r\n\r\n  &.error {\r\n    background-color: rgba(245, 34, 45, 0.1);\r\n    color: $uni-color-error;\r\n  }\r\n}\r\n\r\n/* 列表项样式 */\r\n.list-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 24rpx 0;\r\n  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);\r\n\r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n}\r\n\r\n/* 修复页面底部内边距，避免被TabBar遮挡 */\r\n.has-tabbar-padding {\r\n  padding-bottom: calc(110rpx + env(safe-area-inset-bottom));\r\n}\r\n</style>\r\n", "import App from './App.vue'\n\n// #ifndef VUE3\nimport Vue from 'vue'\nimport './uni.promisify.adaptor'\n\n// 导入Vuex状态管理\nimport store from './store'\n\n// 导入图标字体样式\nimport './static/styles/iconfont.scss'\n\n// 导入全局样式\nimport './static/styles/global.scss'\n\n// 导入API配置\nimport { BASE_URL } from './utils/api'\n\n// // 导入并注册权限指令\n// import permissionDirective from './utils/permission-directive'\n// Vue.use(permissionDirective)\n\n// 导入并全局注册权限检查组件\nimport PermissionCheck from './components/PermissionCheck.vue' \nVue.component('PermissionCheck', PermissionCheck) \n \n// 页面拦截器，用于登录拦截\nconst whiteList = ['/pages/user/login', '/pages/user/agreement']; // 不需要登录拦截的页面\n\n// 添加请求拦截器\nuni.addInterceptor('request', {\n  invoke(args) {\n    // 不在这里处理请求头，由api.js模块处理\n    \n    // 开发环境打印请求信息\n    if (process.env.NODE_ENV !== 'production') {\n      console.log('Request:', args);\n    }\n    \n    return args;\n  },\n  success(args) {\n    // 开发环境打印响应信息\n    if (process.env.NODE_ENV !== 'production') {\n      console.log('Response:', args);\n    }\n    \n    // 特殊错误码处理\n    if (args.data?.code === 401) {\n      // token 失效\n      uni.showToast({\n        title: '登录已过期，请重新登录',\n        icon: 'none'\n      });\n      \n      // 清除登录信息\n      uni.removeStorageSync('token');\n      uni.removeStorageSync('userInfo');\n      uni.removeStorageSync('userId');\n      uni.removeStorageSync('userRole');\n      uni.removeStorageSync('userPermissions');\n      \n      // 跳转到登录页\n      setTimeout(() => {\n        uni.reLaunch({\n          url: '/pages/user/login'\n        });\n      }, 1500);\n    }\n    \n    return args;\n  },\n  fail(err) {\n    console.error('Request failed:', err);\n    return err;\n  }\n});\n\n// 路由拦截器\nconst routerInterceptor = {\n  invoke(params) {\n    // 获取目标页面路径\n    const url = params.url.split('?')[0];\n    \n    // 检查是否在白名单中\n    if (whiteList.includes(url)) {\n      return true;\n    }\n    \n    // 检查用户是否已登录\n    const token = uni.getStorageSync('token');\n    \n    if (!token) {\n      // 未登录，跳转到登录页面\n      uni.navigateTo({\n        url: '/pages/user/login'\n      });\n      return false;\n    }\n    \n    // 已登录，允许继续\n    return true;\n  }\n};\n\n// 添加页面跳转拦截器\nuni.addInterceptor('navigateTo', routerInterceptor);\nuni.addInterceptor('redirectTo', routerInterceptor);\nuni.addInterceptor('reLaunch', routerInterceptor);\nuni.addInterceptor('switchTab', routerInterceptor);\n\n// 全局混入，添加BASE_URL到所有组件\nVue.mixin({\n  data() {\n    return {\n      $baseUrl: BASE_URL\n    }\n  }\n});\n\nVue.config.productionTip = false\nApp.mpType = 'app'\nconst app = new Vue({\n  store,\n  ...App\n})\napp.$mount()\n// #endif\n\n// #ifdef VUE3\nimport { createSSRApp } from 'vue'\n// 在Vue3环境下使用不同的变量名避免命名冲突\nimport { BASE_URL as API_BASE_URL } from './utils/api'\nimport { createStore } from 'vuex'\nimport attendanceModule from './store/attendance'\n\nexport function createApp() {\n  const app = createSSRApp(App)\n  \n  // 创建Vue3版本的store\n  const store = createStore({\n    modules: {\n      attendance: attendanceModule\n    }\n  })\n  \n  // 注册store\n  app.use(store)\n  \n  // 为Vue3应用提供全局属性\n  app.config.globalProperties.$baseUrl = API_BASE_URL\n  \n  return {\n    app\n  }\n}\n// #endif"], "names": ["uni", "createSSRApp", "createStore", "attendanceModule", "API_BASE_URL"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,MAAK,YAAU;AAAA,EACb,UAAU,WAAY;AACpBA,kBAAAA,oCAAY,YAAY;AAGxB,SAAK,eAAc;AAGnB,SAAK,iBAAgB;AAAA,EACtB;AAAA,EACD,QAAQ,WAAY;AAAA,EAEnB;AAAA,EACD,QAAQ,WAAY;AAAA,EAEnB;AAAA,EACD,SAAS;AAAA,IACP,iBAAiB;AAEfA,oBAAAA,oCAAY,QAAQ;AAAA,IACrB;AAAA,IAED,mBAAmB;AAEjBA,oBAAAA,MAAY,MAAA,OAAA,iBAAA,OAAO;AAAA,IACpB;AAAA,EACF;AAAA,EACD,YAAY;AAAA,IACV,iBAAiB;AAAA,EAClB;AACH;;;;;ACmGO,SAAS,YAAY;AAC1B,QAAM,MAAMC,cAAY,aAAC,GAAG;AAG5B,QAAM,QAAQC,cAAAA,YAAY;AAAA,IACxB,SAAS;AAAA,MACP,YAAYC,iBAAgB;AAAA,IAC7B;AAAA,EACL,CAAG;AAGD,MAAI,IAAI,KAAK;AAGb,MAAI,OAAO,iBAAiB,WAAWC,UAAY;AAEnD,SAAO;AAAA,IACL;AAAA,EACD;AACH;;;"}