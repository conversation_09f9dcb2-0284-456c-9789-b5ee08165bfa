{"version": 3, "file": "change-password.js", "sources": ["pages/user/change-password.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlci9jaGFuZ2UtcGFzc3dvcmQudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"change-password-container\">\r\n\t<!-- \t<view class=\"page-header\">\r\n\t\t\t<text class=\"page-title\">密码修改</text>\r\n\t\t</view> -->\r\n\t\t\r\n\t\t<view class=\"form-card\">\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"form-label\">当前密码</text>\r\n\t\t\t\t<view class=\"form-input\">\r\n\t\t\t\t\t<input type=\"password\" v-model=\"password.current\" placeholder=\"请输入当前密码\" password />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"form-label\">新密码</text>\r\n\t\t\t\t<view class=\"form-input\">\r\n\t\t\t\t\t<input type=\"password\" v-model=\"password.new\" placeholder=\"请输入新密码\" password />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"password-rules\">\r\n\t\t\t\t\t<text class=\"rule-item\" :class=\"{ 'rule-passed': checkLength }\">至少8个字符</text>\r\n\t\t\t\t\t<text class=\"rule-item\" :class=\"{ 'rule-passed': checkNumber }\">包含数字</text>\r\n\t\t\t\t\t<text class=\"rule-item\" :class=\"{ 'rule-passed': checkLetter }\">包含字母</text>\r\n\t\t\t\t\t<text class=\"rule-item\" :class=\"{ 'rule-passed': checkSpecial }\">包含特殊字符</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"form-label\">确认新密码</text>\r\n\t\t\t\t<view class=\"form-input\">\r\n\t\t\t\t\t<input type=\"password\" v-model=\"password.confirm\" placeholder=\"请再次输入新密码\" password />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"confirm-tip\" v-if=\"password.new && password.confirm && password.new !== password.confirm\">\r\n\t\t\t\t\t<text class=\"tip-error\">两次输入的密码不一致</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"submit-btn\" :class=\"{ 'btn-disabled': !isFormValid }\" @click=\"changePassword\">修改密码</view>\r\n\t\t\r\n\t\t<view class=\"help-tip\">\r\n\t\t\t<text class=\"tip-title\">注意事项：</text>\r\n\t\t\t<text class=\"tip-content\">1. 为保证账号安全，建议使用强密码</text>\r\n\t\t\t<text class=\"tip-content\">2. 密码修改成功后，需要重新登录</text>\r\n\t\t\t<text class=\"tip-content\">3. 如忘记当前密码，请联系管理员重置</text>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { userApi } from '../../utils/api';\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tpassword: {\r\n\t\t\t\tcurrent: '',\r\n\t\t\t\tnew: '',\r\n\t\t\t\tconfirm: ''\r\n\t\t\t},\r\n\t\t\tisSubmitting: false\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\t// 检查密码长度\r\n\t\tcheckLength() {\r\n\t\t\treturn this.password.new.length >= 8;\r\n\t\t},\r\n\t\t\r\n\t\t// 检查是否包含数字\r\n\t\tcheckNumber() {\r\n\t\t\treturn /\\d/.test(this.password.new);\r\n\t\t},\r\n\t\t\r\n\t\t// 检查是否包含字母\r\n\t\tcheckLetter() {\r\n\t\t\treturn /[a-zA-Z]/.test(this.password.new);\r\n\t\t},\r\n\t\t\r\n\t\t// 检查是否包含特殊字符\r\n\t\tcheckSpecial() {\r\n\t\t\treturn /[^a-zA-Z0-9]/.test(this.password.new);\r\n\t\t},\r\n\t\t\r\n\t\t// 表单是否有效\r\n\t\tisFormValid() {\r\n\t\t\treturn this.password.current && \r\n\t\t\t\tthis.password.new && \r\n\t\t\t\tthis.password.confirm && \r\n\t\t\t\tthis.password.new === this.password.confirm &&\r\n\t\t\t\tthis.checkLength &&\r\n\t\t\t\tthis.checkNumber &&\r\n\t\t\t\tthis.checkLetter;\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t// 修改密码\r\n\t\tchangePassword() {\r\n\t\t\tif (!this.isFormValid) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 防止重复提交\r\n\t\t\tif (this.isSubmitting) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tthis.isSubmitting = true;\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '处理中...'\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 准备请求数据\r\n\t\t\tconst passwordData = {\r\n\t\t\t\toldPassword: this.password.current,\r\n\t\t\t\tnewPassword: this.password.new\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 调用API进行密码修改\r\n\t\t\tuserApi.changePassword(passwordData)\r\n\t\t\t\t.then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthis.isSubmitting = false;\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\t\t// 密码修改成功\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle: '修改成功',\r\n\t\t\t\t\t\t\tcontent: '密码修改成功，请使用新密码重新登录',\r\n\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t// 清除登录状态，跳转到登录页\r\n\t\t\t\t\t\t\t\tuni.removeStorageSync('token');\r\n\t\t\t\t\t\t\t\tuni.removeStorageSync('userInfo');\r\n\t\t\t\t\t\t\t\tuni.removeStorageSync('userId');\r\n\t\t\t\t\t\t\t\tuni.removeStorageSync('userRole');\r\n\t\t\t\t\t\t\t\tuni.removeStorageSync('userPermissions');\r\n\t\t\t\t\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/user/login'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 密码修改失败\r\n\t\t\t\t\t\tthis.showError(res.message || '密码修改失败');\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthis.isSubmitting = false;\r\n\t\t\t\t\tconsole.error('密码修改请求失败:', err);\r\n\t\t\t\t\tthis.showError('网络错误，请稍后重试');\r\n\t\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 显示错误提示\r\n\t\tshowError(message) {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: message,\r\n\t\t\t\ticon: 'none'\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.change-password-container {\r\n\tbackground-color: #f5f7fa;\r\n\tmin-height: 100vh;\r\n\tpadding-bottom: 40rpx;\r\n}\r\n\r\n.page-header {\r\n\tbackground-color: #fff;\r\n\tpadding: 30rpx;\r\n\tborder-bottom: 1rpx solid #eee;\r\n\t\r\n\t.page-title {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t}\r\n}\r\n\r\n.form-card {\r\n\tmargin: 30rpx;\r\n\tbackground-color: #fff;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 20rpx;\r\n\tbox-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.05);\r\n\t\r\n\t.form-item {\r\n\t\tpadding: 24rpx 20rpx;\r\n\t\tborder-bottom: 1rpx solid rgba(0, 0, 0, 0.05);\r\n\t\t\r\n\t\t&:last-child {\r\n\t\t\tborder-bottom: none;\r\n\t\t}\r\n\t\t\r\n\t\t.form-label {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #666;\r\n\t\t\tmargin-bottom: 16rpx;\r\n\t\t\tdisplay: block;\r\n\t\t}\r\n\t\t\r\n\t\t.form-input {\r\n\t\t\tinput {\r\n\t\t\t\theight: 80rpx;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.password-rules {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-wrap: wrap;\r\n\t\t\tmargin-top: 16rpx;\r\n\t\t\t\r\n\t\t\t.rule-item {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #999;\r\n\t\t\t\tmargin-right: 30rpx;\r\n\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\t\r\n\t\t\t\t&::before {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\twidth: 16rpx;\r\n\t\t\t\t\theight: 16rpx;\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\tbackground-color: #e8e8e8;\r\n\t\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&.rule-passed {\r\n\t\t\t\t\tcolor: #52c41a;\r\n\t\t\t\t\t\r\n\t\t\t\t\t&::before {\r\n\t\t\t\t\t\tbackground-color: #52c41a;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.confirm-tip {\r\n\t\t\tmargin-top: 16rpx;\r\n\t\t\t\r\n\t\t\t.tip-error {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #f5222d;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.submit-btn {\r\n\tmargin: 60rpx 30rpx;\r\n\theight: 90rpx;\r\n\tline-height: 90rpx;\r\n\tbackground-color: $uni-color-primary;\r\n\tcolor: #fff;\r\n\ttext-align: center;\r\n\tborder-radius: 12rpx;\r\n\tfont-size: 32rpx;\r\n\tbox-shadow: 0 6rpx 20rpx rgba(24, 144, 255, 0.3);\r\n\tfont-weight: bold;\r\n\t\r\n\t&:active {\r\n\t\topacity: 0.9;\r\n\t\ttransform: translateY(2rpx);\r\n\t}\r\n\t\r\n\t&.btn-disabled {\r\n\t\tbackground-color: #ccc;\r\n\t\tbox-shadow: none;\r\n\t}\r\n}\r\n\r\n.help-tip {\r\n\tpadding: 0 40rpx;\r\n\tmargin-top: 40rpx;\r\n\t\r\n\t.tip-title {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666;\r\n\t\tmargin-bottom: 16rpx;\r\n\t\tdisplay: block;\r\n\t}\r\n\t\r\n\t.tip-content {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #999;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tdisplay: block;\r\n\t\tline-height: 1.6;\r\n\t}\r\n}\r\n</style> ", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaan<PERSON>_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/user/change-password.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "userApi"], "mappings": ";;;AAoDA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,UAAU;AAAA,QACT,SAAS;AAAA,QACT,KAAK;AAAA,QACL,SAAS;AAAA,MACT;AAAA,MACD,cAAc;AAAA,IACf;AAAA,EACA;AAAA,EACD,UAAU;AAAA;AAAA,IAET,cAAc;AACb,aAAO,KAAK,SAAS,IAAI,UAAU;AAAA,IACnC;AAAA;AAAA,IAGD,cAAc;AACb,aAAO,KAAK,KAAK,KAAK,SAAS,GAAG;AAAA,IAClC;AAAA;AAAA,IAGD,cAAc;AACb,aAAO,WAAW,KAAK,KAAK,SAAS,GAAG;AAAA,IACxC;AAAA;AAAA,IAGD,eAAe;AACd,aAAO,eAAe,KAAK,KAAK,SAAS,GAAG;AAAA,IAC5C;AAAA;AAAA,IAGD,cAAc;AACb,aAAO,KAAK,SAAS,WACpB,KAAK,SAAS,OACd,KAAK,SAAS,WACd,KAAK,SAAS,QAAQ,KAAK,SAAS,WACpC,KAAK,eACL,KAAK,eACL,KAAK;AAAA,IACP;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,iBAAiB;AAChB,UAAI,CAAC,KAAK,aAAa;AACtB;AAAA,MACD;AAGA,UAAI,KAAK,cAAc;AACtB;AAAA,MACD;AAEA,WAAK,eAAe;AACpBA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACR,CAAC;AAGD,YAAM,eAAe;AAAA,QACpB,aAAa,KAAK,SAAS;AAAA,QAC3B,aAAa,KAAK,SAAS;AAAA;AAI5BC,gBAAO,QAAC,eAAe,YAAY,EACjC,KAAK,SAAO;AACZD,sBAAG,MAAC,YAAW;AACf,aAAK,eAAe;AAEpB,YAAI,IAAI,SAAS,KAAK;AAErBA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,SAAS,MAAM;AAEdA,kCAAI,kBAAkB,OAAO;AAC7BA,kCAAI,kBAAkB,UAAU;AAChCA,kCAAI,kBAAkB,QAAQ;AAC9BA,kCAAI,kBAAkB,UAAU;AAChCA,kCAAI,kBAAkB,iBAAiB;AACvCA,4BAAAA,MAAI,SAAS;AAAA,gBACZ,KAAK;AAAA,cACN,CAAC;AAAA,YACF;AAAA,UACD,CAAC;AAAA,eACK;AAEN,eAAK,UAAU,IAAI,WAAW,QAAQ;AAAA,QACvC;AAAA,OACA,EACA,MAAM,SAAO;AACbA,sBAAG,MAAC,YAAW;AACf,aAAK,eAAe;AACpBA,oFAAc,aAAa,GAAG;AAC9B,aAAK,UAAU,YAAY;AAAA,MAC5B,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,UAAU,SAAS;AAClBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;AClKA,GAAG,WAAW,eAAe;"}