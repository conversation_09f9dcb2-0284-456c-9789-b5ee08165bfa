{"version": 3, "file": "control.js", "sources": ["pages/hes/control.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaGVzL2NvbnRyb2wudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"station-control\"> \r\n\r\n    <!-- 标签切换栏 -->\r\n    <view class=\"tab-section\">\r\n      <view class=\"tab-bar\">\r\n        <view \r\n          class=\"tab-item\" \r\n          :class=\"{ active: activeTab === 'control' }\"\r\n          @tap=\"activeTab = 'control'\"\r\n        >\r\n          控制视图\r\n        </view>\r\n        <view \r\n          class=\"tab-item\" \r\n          :class=\"{ active: activeTab === 'process' }\"\r\n          @tap=\"activeTab = 'process'\"\r\n        >\r\n          工艺视图\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 站点选择区域 -->\r\n    <view class=\"station-selector\" @tap=\"showStationSelect\">\r\n      <view class=\"station-label\">换热站：</view>\r\n      <view class=\"station-value-wrapper\">\r\n        <text class=\"station-value-text\" :class=\"{'placeholder': !selectedStation}\">\r\n          {{ selectedStation ? selectedStation.name : '请选择换热站' }}\r\n        </text>\r\n        <text class=\"station-arrow\">></text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 站点内容区域 -->\r\n    <view class=\"station-content\" v-if=\"selectedStation\">\r\n      <!-- 根据当前tab显示不同内容 -->\r\n      <view v-if=\"activeTab === 'control'\" class=\"control-view\">\r\n        <view class=\"card basic-info\">\r\n          <view class=\"card-title\">基本信息</view>\r\n          <view class=\"info-row\">\r\n            <text class=\"label\">站点名称：</text>\r\n            <text class=\"value\">{{ selectedStation.name }}</text>\r\n          </view>\r\n          <view class=\"info-row\">\r\n            <text class=\"label\">站点地址：</text>\r\n            <text class=\"value\">{{ selectedStation.address || selectedStation.heat_unit_name }}</text>\r\n          </view>\r\n          <view class=\"info-row\">\r\n            <text class=\"label\">运行状态：</text>\r\n            <text class=\"value\" :class=\"selectedStation.status\">\r\n              {{ selectedStation.status === 'online' ? '在线' : selectedStation.status === 'fault' ? '故障' : '离线' }}\r\n            </text>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 实时数据卡片 -->\r\n        <view class=\"card real-time-data\" v-if=\"stationData\">\r\n          <view class=\"card-title\">实时数据</view>\r\n          <view class=\"data-grid\">\r\n            <view class=\"data-item\">\r\n              <text class=\"data-label\">一次供水温度</text>\r\n              <text class=\"data-value\">{{ stationData.realtime_data?.primary_system?.supply_temp || '-' }}℃</text>\r\n            </view>\r\n            <view class=\"data-item\">\r\n              <text class=\"data-label\">一次回水温度</text>\r\n              <text class=\"data-value\">{{ stationData.realtime_data?.primary_system?.return_temp || '-' }}℃</text>\r\n            </view>\r\n            <view class=\"data-item\">\r\n              <text class=\"data-label\">一次供水压力</text>\r\n              <text class=\"data-value\">{{ stationData.realtime_data?.primary_system?.supply_pressure || '-' }}MPa</text>\r\n            </view>\r\n            <view class=\"data-item\">\r\n              <text class=\"data-label\">一次回水压力</text>\r\n              <text class=\"data-value\">{{ stationData.realtime_data?.primary_system?.return_pressure || '-' }}MPa</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        <view class=\"card real-time-data\" v-else>\r\n          <view class=\"card-title\">实时数据</view>\r\n          <view class=\"data-grid\">\r\n            <view class=\"data-item\">\r\n              <text class=\"data-label\">供水温度</text>\r\n              <text class=\"data-value\">{{ selectedStation.supply_temp || '-' }}℃</text>\r\n            </view>\r\n            <view class=\"data-item\">\r\n              <text class=\"data-label\">回水温度</text>\r\n              <text class=\"data-value\">{{ selectedStation.return_temp || '-' }}℃</text>\r\n            </view>\r\n            <view class=\"data-item\">\r\n              <text class=\"data-label\">供水压力</text>\r\n              <text class=\"data-value\">{{ selectedStation.supply_pressure || '-' }}MPa</text>\r\n            </view>\r\n            <view class=\"data-item\">\r\n              <text class=\"data-label\">回水压力</text>\r\n              <text class=\"data-value\">{{ selectedStation.return_pressure || '-' }}MPa</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 工作模式卡片 -->\r\n        <view class=\"card work-mode\">\r\n          <view class=\"mode-header\">\r\n            <text class=\"mode-title\">运行模式:</text>\r\n            <view class=\"mode-buttons\">\r\n              <view \r\n                class=\"mode-button\" \r\n                :class=\"{ active: workMode === 'auto' }\"\r\n                @tap=\"handleWorkModeChange('auto')\"\r\n              >自动</view>\r\n              <view \r\n                class=\"mode-button\" \r\n                :class=\"{ active: workMode === 'manual' }\"\r\n                @tap=\"handleWorkModeChange('manual')\"\r\n              >手动</view>\r\n            </view>\r\n          </view>\r\n          <view class=\"mode-desc\">\r\n            {{ workMode === 'auto' ? '自动模式: 系统将根据预设算法自动调节设备运行' : '手动模式: 您可以手动调节设备运行参数' }}\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 设备控制卡片 -->\r\n        <view class=\"card device-control\">\r\n          <view class=\"card-title\">设备控制</view>\r\n          \r\n          <!-- 设备类型切换标签 -->\r\n          <view class=\"device-tabs\">\r\n            <view \r\n              class=\"device-tab\" \r\n              :class=\"{ active: activeDeviceTab === 'valve' }\"\r\n              @tap=\"activeDeviceTab = 'valve'\"\r\n            >阀门控制</view>\r\n            <view \r\n              class=\"device-tab\" \r\n              :class=\"{ active: activeDeviceTab === 'pump' }\"\r\n              @tap=\"activeDeviceTab = 'pump'\"\r\n            >水泵控制</view>\r\n          </view>\r\n          \r\n          <!-- 阀门控制 -->\r\n          <view v-if=\"activeDeviceTab === 'valve'\" class=\"device-section\">\r\n            <view class=\"device-control-block\">\r\n              <picker \r\n                @change=\"handleValveSelect\" \r\n                :value=\"selectedValveIndex\" \r\n                :range=\"deviceControlData.valves\" \r\n                range-key=\"name\"\r\n                :disabled=\"workMode === 'auto' || selectedStation.status !== 'online'\"\r\n              >\r\n                <view class=\"device-selector\">\r\n                  <view class=\"selector-wrap\">\r\n                    <text class=\"dropdown-label\">选择阀门:</text>\r\n                    <view class=\"dropdown-value\">\r\n                      <text class=\"device-name\">{{ deviceControlData.valves[selectedValveIndex].name }}</text>\r\n                      <text class=\"dropdown-arrow\">▼</text>\r\n                    </view>\r\n                  </view>\r\n                  <switch \r\n                    :checked=\"deviceControlData.valves[selectedValveIndex].isOpen\" \r\n                    @change=\"(e) => handleValveSwitch(deviceControlData.valves[selectedValveIndex].id, e)\" \r\n                    :disabled=\"workMode === 'auto' || selectedStation.status !== 'online'\"\r\n                    color=\"#0088ff\"\r\n                  />\r\n                </view>\r\n              </picker>\r\n              \r\n              <view class=\"device-control-params\">\r\n                <text class=\"param-label\">开度: {{ deviceControlData.valves[selectedValveIndex].openDegree }}%</text>\r\n                <view class=\"slider-row\">\r\n                  <slider \r\n                    :value=\"tempValveOpenDegree\" \r\n                    min=\"0\" \r\n                    max=\"100\" \r\n                    @change=\"(e) => updateTempValveOpenDegree(e)\"\r\n                    :disabled=\"workMode === 'auto' || !deviceControlData.valves[selectedValveIndex].isOpen || selectedStation.status !== 'online'\"\r\n                    activeColor=\"#0088ff\"\r\n                    block-size=\"20\"\r\n                    block-color=\"#ffffff\"\r\n                  />\r\n                  <text class=\"param-value\">{{ tempValveOpenDegree }}</text>\r\n                </view>\r\n                <view class=\"confirm-button-row\">\r\n                  <button \r\n                    class=\"confirm-button\" \r\n                    @tap=\"confirmValveSettings\"\r\n                    :disabled=\"workMode === 'auto' || !deviceControlData.valves[selectedValveIndex].isOpen || selectedStation.status !== 'online'\"\r\n                  >确认</button>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 水泵控制 -->\r\n          <view v-if=\"activeDeviceTab === 'pump'\" class=\"device-section\">\r\n            <view class=\"device-control-block\">\r\n              <picker \r\n                @change=\"handlePumpSelect\" \r\n                :value=\"selectedPumpIndex\" \r\n                :range=\"deviceControlData.pumps\" \r\n                range-key=\"name\"\r\n                :disabled=\"workMode === 'auto' || selectedStation.status !== 'online'\"\r\n              >\r\n                <view class=\"device-selector\">\r\n                  <view class=\"selector-wrap\">\r\n                    <text class=\"dropdown-label\">选择水泵:</text>\r\n                    <view class=\"dropdown-value\">\r\n                      <text class=\"device-name\">{{ deviceControlData.pumps[selectedPumpIndex].name }}</text>\r\n                      <text class=\"dropdown-arrow\">▼</text>\r\n                    </view>\r\n                  </view>\r\n                  <switch \r\n                    :checked=\"deviceControlData.pumps[selectedPumpIndex].isRunning\" \r\n                    @change=\"(e) => handlePumpSwitch(deviceControlData.pumps[selectedPumpIndex].id, e)\" \r\n                    :disabled=\"workMode === 'auto' || selectedStation.status !== 'online'\"\r\n                    color=\"#0088ff\"\r\n                  />\r\n                </view>\r\n              </picker>\r\n              \r\n              <view class=\"device-control-params\">\r\n                <text class=\"param-label\">频率: {{ deviceControlData.pumps[selectedPumpIndex].frequency }}Hz</text>\r\n                \r\n                <!-- 工业变频器风格的频率控制 -->\r\n                <view class=\"frequency-control\" :class=\"{'disabled': workMode === 'auto' || !deviceControlData.pumps[selectedPumpIndex].isRunning || selectedStation.status !== 'online'}\">\r\n                  <view class=\"frequency-display\">\r\n                    <input \r\n                      type=\"digit\" \r\n                      v-model=\"tempPumpFrequency\" \r\n                      class=\"frequency-input\"\r\n                      :disabled=\"workMode === 'auto' || !deviceControlData.pumps[selectedPumpIndex].isRunning || selectedStation.status !== 'online'\"\r\n                    />\r\n                    <text class=\"frequency-unit\">Hz</text>\r\n                  </view>\r\n                  <view class=\"frequency-buttons\">\r\n                    <view \r\n                      class=\"frequency-button increase\" \r\n                      @tap=\"increasePumpFrequency\"\r\n                      :class=\"{'disabled': workMode === 'auto' || !deviceControlData.pumps[selectedPumpIndex].isRunning || selectedStation.status !== 'online'}\"\r\n                    >▲</view>\r\n                    <view \r\n                      class=\"frequency-button decrease\" \r\n                      @tap=\"decreasePumpFrequency\"\r\n                      :class=\"{'disabled': workMode === 'auto' || !deviceControlData.pumps[selectedPumpIndex].isRunning || selectedStation.status !== 'online'}\"\r\n                    >▼</view>\r\n                  </view>\r\n                </view>\r\n                \r\n                <view class=\"confirm-button-row\">\r\n                  <button \r\n                    class=\"confirm-button\" \r\n                    @tap=\"confirmPumpSettings\"\r\n                    :disabled=\"workMode === 'auto' || !deviceControlData.pumps[selectedPumpIndex].isRunning || selectedStation.status !== 'online'\"\r\n                  >确认</button>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 控制算法卡片 -->\r\n        <view class=\"card control-algorithm\">\r\n          <view class=\"card-title\">控制算法</view>\r\n          <view class=\"algorithm-tip\" v-if=\"workMode === 'manual'\">\r\n            <text class=\"tip-text\">注意：算法设置仅在自动模式下生效，请先切换到自动模式</text>\r\n          </view>\r\n          <view class=\"algorithm-tip\" v-if=\"selectedStation.status !== 'online'\">\r\n            <text class=\"tip-text\">注意：该站点当前不在线，无法应用算法设置</text>\r\n          </view>\r\n          <view class=\"algorithm-selector\">\r\n            <radio-group @change=\"handleAlgorithmChange\">\r\n              <label class=\"algorithm-item\" v-for=\"(algo, index) in algorithms\" :key=\"index\">\r\n                <view class=\"algorithm-radio\">\r\n                  <radio \r\n                    :value=\"algo.value\" \r\n                    :checked=\"selectedAlgorithm === algo.value\"\r\n                    color=\"#0088ff\"\r\n                    :disabled=\"selectedStation.status !== 'online'\"\r\n                  />\r\n                </view>\r\n                <text class=\"algorithm-label\">{{ algo.label }}</text>\r\n              </label>\r\n            </radio-group>\r\n          </view>\r\n          <view class=\"algorithm-desc\">\r\n            {{ getAlgorithmDescription() }}\r\n          </view>\r\n          <!-- 显示目标值设定区域，但根据条件禁用输入和按钮 -->\r\n          <view class=\"algorithm-params\">\r\n            <view class=\"param-item\">\r\n              <text class=\"param-label\">目标值设定:</text>\r\n              <input \r\n                type=\"digit\" \r\n                v-model=\"algorithmTargetValue\" \r\n                class=\"param-input\"\r\n                :disabled=\"workMode === 'manual' || selectedStation.status !== 'online'\"\r\n              />\r\n              <text class=\"param-unit\">{{ getAlgorithmUnit() }}</text>\r\n            </view>\r\n            <button \r\n              class=\"apply-button\" \r\n              @tap=\"applyAlgorithmSettings\"\r\n              :disabled=\"workMode === 'manual' || selectedStation.status !== 'online'\"\r\n            >应用</button>\r\n            <view class=\"button-tip\" v-if=\"workMode === 'manual'\">\r\n              <text class=\"tip-text\">需要在自动模式下才能应用算法</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view v-else-if=\"activeTab === 'process'\" class=\"process-view\">\r\n        <view class=\"process-image\">\r\n          <image src=\"/static/images/process_flow.png\" mode=\"widthFix\"></image>\r\n        </view>\r\n        <view class=\"process-legend\">\r\n          <view class=\"legend-item\">\r\n            <view class=\"legend-color\" style=\"background-color: #f00;\"></view>\r\n            <text class=\"legend-text\">供水</text>\r\n          </view>\r\n          <view class=\"legend-item\">\r\n            <view class=\"legend-color\" style=\"background-color: #00f;\"></view>\r\n            <text class=\"legend-text\">回水</text>\r\n          </view>\r\n          <view class=\"legend-item\">\r\n            <view class=\"legend-color\" style=\"background-color: #0c0;\"></view>\r\n            <text class=\"legend-text\">运行设备</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 未选择站点时的提示 -->\r\n    <view class=\"no-station\" v-else>\r\n      <text class=\"tip\">请选择换热站进行控制</text>\r\n    </view>\r\n    \r\n    <!-- 站点选择弹窗 -->\r\n    <uni-popup ref=\"stationPopup\" type=\"bottom\" background-color=\"#f5f5f5\">\r\n      <view class=\"station-picker\">\r\n        <view class=\"picker-header\">\r\n          <text class=\"picker-title\">选择换热站</text>\r\n          <view class=\"picker-close\" @tap=\"closeStationPopup\">关闭</view>\r\n        </view>\r\n        \r\n        <!-- 搜索栏 -->\r\n        <view class=\"search-bar\">\r\n          <view class=\"search-input-wrap\">\r\n            <view class=\"search-icon\">🔍</view>\r\n            <input \r\n              type=\"text\" \r\n              v-model=\"stationFilter.keyword\" \r\n              placeholder=\"搜索换热站\" \r\n              confirm-type=\"search\"\r\n              @confirm=\"searchStations\"\r\n              class=\"search-input\"\r\n            />\r\n            <view class=\"search-clear\" v-if=\"stationFilter.keyword\" @tap=\"stationFilter.keyword = ''\">×</view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 状态筛选 -->\r\n        <view class=\"filter-tabs\">\r\n          <view \r\n            class=\"filter-tab\" \r\n            :class=\"{ active: stationFilter.status === 'all' }\"\r\n            @tap=\"setStatusFilter('all')\"\r\n          >全部</view>\r\n          <view \r\n            class=\"filter-tab\" \r\n            :class=\"{ active: stationFilter.status === 'online' }\"\r\n            @tap=\"setStatusFilter('online')\"\r\n          >在线</view>\r\n          <view \r\n            class=\"filter-tab\" \r\n            :class=\"{ active: stationFilter.status === 'fault' }\"\r\n            @tap=\"setStatusFilter('fault')\"\r\n          >故障</view>\r\n          <view \r\n            class=\"filter-tab\" \r\n            :class=\"{ active: stationFilter.status === 'offline' }\"\r\n            @tap=\"setStatusFilter('offline')\"\r\n          >离线</view>\r\n        </view>\r\n        \r\n        <!-- 站点列表 -->\r\n        <scroll-view scroll-y=\"true\" class=\"station-list\">\r\n          <view v-if=\"filteredStations.length === 0\" class=\"empty-tip\">\r\n            <text>暂无符合条件的换热站</text>\r\n          </view>\r\n          <view \r\n            class=\"station-item\" \r\n            v-for=\"station in filteredStations\" \r\n            :key=\"station.id\"\r\n            @tap=\"handleSelectStation(station)\"\r\n          >\r\n            <view class=\"station-item-content\">\r\n              <view class=\"station-main-info\">\r\n                <text class=\"station-name\">{{ station.name }}</text>\r\n                <text class=\"station-address\">{{ station.heat_unit_name }}</text>\r\n              </view>\r\n              <view class=\"station-temp-info\">\r\n                <text class=\"temp-item\">供水温度: {{ station.supply_temp ? station.supply_temp.toFixed(2) + '℃' : '-' }}</text>\r\n                <text class=\"temp-item\">回水温度: {{ station.return_temp ? station.return_temp.toFixed(2) + '℃' : '-' }}</text>\r\n              </view>\r\n              <text class=\"station-status-tag\" :class=\"station.status\">\r\n                {{ station.status === 'online' ? '在线' : station.status === 'fault' ? '故障' : '离线' }}\r\n              </text>\r\n            </view>\r\n          </view>\r\n        </scroll-view>\r\n      </view>\r\n    </uni-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { heatingStationApi } from '@/utils/api.js';\r\n\r\nexport default {\r\n  components: {\r\n    // 确保组件正确引入\r\n  },\r\n  data() {\r\n    return {\r\n      activeTab: 'control',\r\n      selectedStation: null,\r\n      stationList: [],\r\n      stationData: null,\r\n      tempSliderValue: 80,\r\n      pressureSliderValue: 60,\r\n      loading: false,\r\n      // 站点筛选条件\r\n      stationFilter: {\r\n        status: 'all',\r\n        keyword: ''\r\n      },\r\n      // 工作模式\r\n      workMode: 'auto',\r\n      // 设备控制标签\r\n      activeDeviceTab: 'valve',\r\n      // 设备控制数据\r\n      deviceControlData: {\r\n        valves: [\r\n          { id: 1, name: '一次供水电动阀', isOpen: false, openDegree: 0 },\r\n          { id: 2, name: '一次回水电动阀', isOpen: false, openDegree: 0 },\r\n          { id: 3, name: '二次供水电动阀', isOpen: false, openDegree: 0 }\r\n        ],\r\n        pumps: [\r\n          { id: 1, name: '一次循环泵', isRunning: false, frequency: 0 },\r\n          { id: 2, name: '二次循环泵', isRunning: false, frequency: 0 }\r\n        ]\r\n      },\r\n      // 当前选中的阀门和水泵索引\r\n      selectedValveIndex: 0,\r\n      selectedPumpIndex: 0,\r\n      // 控制算法\r\n      algorithms: [\r\n        { value: 'primary_flow', label: '一次流量控制算法' },\r\n        { value: 'primary_supply_temp', label: '一次供水温度控制算法' },\r\n        { value: 'secondary_supply_temp', label: '二次供水温度控制算法' },\r\n        { value: 'secondary_return_temp', label: '二次回水温度控制算法' },\r\n        { value: 'ai_prediction', label: 'AI预算算法' }\r\n      ],\r\n      selectedAlgorithm: 'primary_flow',\r\n      algorithmTargetValue: 75,\r\n      tempValveOpenDegree: 0,\r\n      tempPumpFrequency: 0\r\n    };\r\n  },\r\n  computed: {\r\n    // 过滤后的站点列表\r\n    filteredStations() {\r\n      if (!this.stationList || this.stationList.length === 0) return [];\r\n      \r\n      return this.stationList.filter(station => {\r\n        // 状态筛选\r\n        if (this.stationFilter.status !== 'all' && station.status !== this.stationFilter.status) {\r\n          return false;\r\n        }\r\n        \r\n        // 关键词筛选\r\n        if (this.stationFilter.keyword && !station.name.includes(this.stationFilter.keyword)) {\r\n          return false;\r\n        }\r\n        \r\n        return true;\r\n      });\r\n    }\r\n  },\r\n  onReady() {\r\n    // 确保popup组件已加载\r\n    if (this.$refs.stationPopup) {\r\n      console.log('Popup component loaded');\r\n    } else {\r\n      console.error('Popup component not found');\r\n    }\r\n  },\r\n  onLoad() {\r\n    // 加载站点列表\r\n    this.loadStationList();\r\n    \r\n    // 检查是否有缓存的站点信息\r\n    const cachedStation = uni.getStorageSync('selectedStation');\r\n    if (cachedStation) {\r\n      try {\r\n        this.selectedStation = JSON.parse(cachedStation);\r\n        this.loadStationData();\r\n      } catch (e) {\r\n        console.error('解析缓存站点数据失败', e);\r\n      }\r\n    }\r\n    \r\n    // 初始化算法默认值\r\n    this.initAlgorithmDefaults();\r\n  },\r\n  onShow() {\r\n    // 获取全局组件引用\r\n    this.$nextTick(() => {\r\n      // 如果页面上还没有popup对象，尝试延迟获取\r\n      if (!this.$refs.stationPopup) {\r\n        setTimeout(() => {\r\n          this.initPopupComponents();\r\n        }, 300);\r\n      }\r\n      \r\n      // 如果已有选择的站点，刷新数据以获取最新的控制状态\r\n      if (this.selectedStation && this.selectedStation.id) {\r\n        console.log('页面显示，刷新站点数据');\r\n        this.loadStationData();\r\n      }\r\n    });\r\n  },\r\n  methods: {\r\n    // 初始化popup组件\r\n    initPopupComponents() {\r\n      if (!this.$refs.stationPopup) {\r\n        console.error('Popup component not found, trying alternative access');\r\n        // 尝试通过uni API创建\r\n        uni.showToast({\r\n          title: '请重新点击选择换热站',\r\n          icon: 'none',\r\n          duration: 2000\r\n        });\r\n      }\r\n    },\r\n    // 加载换热站列表\r\n    async loadStationList() {\r\n      uni.showLoading({\r\n        title: '加载站点列表...'\r\n      });\r\n      \r\n      try {\r\n        const res = await heatingStationApi.getList({\r\n          \"status\": \"\",   \r\n          \"keyword\": \"\"\r\n        });\r\n        if (res.code === 200 && res.data && res.data.list) {\r\n          this.stationList = res.data.list;\r\n        } else {\r\n          uni.showToast({\r\n            title: '获取站点列表失败',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error('获取站点列表出错:', error);\r\n        uni.showToast({\r\n          title: '网络异常，请重试',\r\n          icon: 'none'\r\n        });\r\n      } finally {\r\n        uni.hideLoading();\r\n      }\r\n    },\r\n    \r\n    // 显示站点选择\r\n    showStationSelect() {\r\n      if (this.stationList.length === 0) {\r\n        this.loadStationList().then(() => {\r\n          this.openStationPopup();\r\n        });\r\n      } else {\r\n        this.openStationPopup();\r\n      }\r\n    },\r\n    \r\n    // 打开站点选择弹窗\r\n    openStationPopup() {\r\n      // 重置筛选条件\r\n      this.stationFilter.status = 'all';\r\n      this.stationFilter.keyword = '';\r\n      \r\n      // 打开弹窗\r\n      if (this.$refs.stationPopup) {\r\n        this.$refs.stationPopup.open();\r\n      } else {\r\n        console.error('站点选择弹窗组件未找到');\r\n        uni.showToast({\r\n          title: '组件加载失败，请重试',\r\n          icon: 'none'\r\n        });\r\n      }\r\n    },\r\n    \r\n    // 关闭站点选择弹窗\r\n    closeStationPopup() {\r\n      if (this.$refs.stationPopup) {\r\n        this.$refs.stationPopup.close();\r\n      }\r\n    },\r\n    \r\n    // 设置状态筛选\r\n    setStatusFilter(status) {\r\n      this.stationFilter.status = status;\r\n    },\r\n    \r\n    // 搜索站点\r\n    searchStations() {\r\n      console.log('搜索关键词:', this.stationFilter.keyword);\r\n      // 关键词搜索已经通过计算属性filteredStations自动实现\r\n    },\r\n    \r\n    // 处理站点选择\r\n    handleSelectStation(station) {\r\n      this.selectedStation = station;\r\n      // 缓存选择的站点\r\n      uni.setStorageSync('selectedStation', JSON.stringify(station));\r\n      this.loadStationData();\r\n      this.closeStationPopup();\r\n    },\r\n    \r\n    // 加载站点详细数据\r\n    async loadStationData() {\r\n      if (!this.selectedStation) return;\r\n      \r\n      uni.showLoading({\r\n        title: '加载站点数据...'\r\n      });\r\n      \r\n      try {\r\n        const res = await heatingStationApi.getDetail(this.selectedStation.id);\r\n        console.log('站点详细数据:', JSON.stringify(res));\r\n        \r\n        if (res.code === 200 && res.data) {\r\n          this.stationData = res.data;\r\n          \r\n          // 设置滑块初始值\r\n          if (this.stationData.realtime_data && this.stationData.realtime_data.primary_system) {\r\n            // 温度滑块值\r\n            const supplyTemp = this.stationData.realtime_data.primary_system.supply_temp;\r\n            if (supplyTemp) {\r\n              this.tempSliderValue = Math.max(70, Math.min(95, Math.round(supplyTemp)));\r\n            }\r\n            \r\n            // 压力滑块值 (转换为0.4-0.8MPa对应的40-80)\r\n            const supplyPressure = this.stationData.realtime_data.primary_system.supply_pressure;\r\n            if (supplyPressure) {\r\n              this.pressureSliderValue = Math.max(40, Math.min(80, Math.round(supplyPressure * 100)));\r\n            }\r\n          } else {\r\n            console.warn('一次系统数据缺失，使用默认温度和压力值');\r\n          }\r\n          \r\n          // 获取当前工作模式（添加默认值处理）\r\n          if (this.stationData.operation_mode) {\r\n            console.log('服务器返回的工作模式:', this.stationData.operation_mode);\r\n            this.workMode = this.stationData.operation_mode === 'automatic' ? 'auto' : 'manual';\r\n          } else {\r\n            // 如果服务器未返回工作模式，默认设为手动模式以便操作设备\r\n            console.warn('未获取到工作模式信息，默认设为手动模式');\r\n            this.workMode = 'manual';\r\n          }\r\n          \r\n          // 获取当前控制算法\r\n          if (this.stationData.control_algorithm) {\r\n            this.selectedAlgorithm = this.stationData.control_algorithm.type || 'primary_flow';\r\n            if (this.stationData.control_algorithm.target_value) {\r\n              this.algorithmTargetValue = this.stationData.control_algorithm.target_value;\r\n            }\r\n          } else {\r\n            console.warn('未获取到控制算法信息，使用默认算法');\r\n            this.selectedAlgorithm = 'primary_flow';\r\n            this.algorithmTargetValue = 75;\r\n          }\r\n          \r\n          // 如果有设备数据，更新设备控制状态\r\n          if (this.stationData.realtime_data) {\r\n            console.log('开始更新设备控制数据');\r\n            this.updateDeviceControlData();\r\n          } else {\r\n            console.error('站点实时数据缺失，无法更新设备状态');\r\n            // 确保控制参数有默认值\r\n            this.resetControlParameters();\r\n          }\r\n          \r\n        } else {\r\n          console.error('获取站点数据响应异常:', res.message || '未知错误');\r\n          uni.showToast({\r\n            title: '获取站点数据失败',\r\n            icon: 'none'\r\n          });\r\n          // 确保控制参数有默认值\r\n          this.resetControlParameters();\r\n        }\r\n      } catch (error) {\r\n        console.error('获取站点数据出错:', error);\r\n        uni.showToast({\r\n          title: '网络异常，请重试',\r\n          icon: 'none'\r\n        });\r\n        // 确保控制参数有默认值\r\n        this.resetControlParameters();\r\n      } finally {\r\n        uni.hideLoading();\r\n      }\r\n    },\r\n    \r\n    // 重置控制参数到默认值\r\n    resetControlParameters() {\r\n      try {\r\n        this.tempValveOpenDegree = this.deviceControlData.valves[this.selectedValveIndex]?.openDegree || 0;\r\n        this.tempPumpFrequency = this.deviceControlData.pumps[this.selectedPumpIndex]?.frequency || 0;\r\n      } catch (e) {\r\n        console.error('重置控制参数失败:', e);\r\n        // 强制设置安全默认值\r\n        this.tempValveOpenDegree = 0;\r\n        this.tempPumpFrequency = 0;\r\n        // 确保索引在有效范围内\r\n        this.selectedValveIndex = 0;\r\n        this.selectedPumpIndex = 0;\r\n      }\r\n      console.log('重置后的控制参数:', {\r\n        tempValveOpenDegree: this.tempValveOpenDegree,\r\n        tempPumpFrequency: this.tempPumpFrequency,\r\n        selectedValveIndex: this.selectedValveIndex,\r\n        selectedPumpIndex: this.selectedPumpIndex\r\n      });\r\n    },\r\n    \r\n    // 更新设备控制数据\r\n    updateDeviceControlData() {\r\n      console.log('更新设备控制数据', JSON.stringify(this.stationData));\r\n      \r\n      // 检查是否有设备状态数据\r\n      if (!this.stationData.realtime_data || !this.stationData.realtime_data.equipment_status) {\r\n        console.error('设备数据缺失，使用默认设置');\r\n        // 先确保索引在合法范围内\r\n        this.selectedValveIndex = Math.min(this.selectedValveIndex, this.deviceControlData.valves.length - 1);\r\n        this.selectedPumpIndex = Math.min(this.selectedPumpIndex, this.deviceControlData.pumps.length - 1);\r\n        \r\n        // 确保默认值被设置\r\n        this.tempValveOpenDegree = this.deviceControlData.valves[this.selectedValveIndex].openDegree || 0;\r\n        this.tempPumpFrequency = this.deviceControlData.pumps[this.selectedPumpIndex].frequency || 0;\r\n        return;\r\n      }\r\n      \r\n      const equipStatus = this.stationData.realtime_data.equipment_status;\r\n      console.log('设备状态数据:', JSON.stringify(equipStatus));\r\n      \r\n      // 更新阀门状态\r\n      if (equipStatus.valves && equipStatus.valves.length > 0) {\r\n        console.log('更新阀门数据:', JSON.stringify(equipStatus.valves));\r\n        this.deviceControlData.valves = equipStatus.valves.map(valve => {\r\n          return {\r\n            id: valve.id,\r\n            name: valve.name || `阀门${valve.id}`,\r\n            isOpen: valve.status === 'open',\r\n            openDegree: valve.opening_degree || 0\r\n          };\r\n        });\r\n        \r\n        // 确保选中的索引有效\r\n        if (this.selectedValveIndex >= this.deviceControlData.valves.length) {\r\n          this.selectedValveIndex = 0;\r\n        }\r\n      } else {\r\n        console.warn('阀门数据缺失，保留默认数据');\r\n      }\r\n      \r\n      // 同步更新临时变量\r\n      if (this.deviceControlData.valves.length > 0) {\r\n        // 先检查所选阀门的开度是否存在\r\n        if (this.deviceControlData.valves[this.selectedValveIndex]) {\r\n          const currentDegree = this.deviceControlData.valves[this.selectedValveIndex].openDegree;\r\n          console.log(`阀门[${this.selectedValveIndex}]当前开度:`, currentDegree);\r\n          this.tempValveOpenDegree = currentDegree;\r\n        } else {\r\n          console.warn('选中的阀门索引无效，重置为0');\r\n          this.selectedValveIndex = 0;\r\n          this.tempValveOpenDegree = this.deviceControlData.valves[0].openDegree || 0;\r\n        }\r\n      }\r\n      \r\n      // 更新水泵状态\r\n      if (equipStatus.pumps && equipStatus.pumps.length > 0) {\r\n        console.log('更新水泵数据:', JSON.stringify(equipStatus.pumps));\r\n        this.deviceControlData.pumps = equipStatus.pumps.map(pump => {\r\n          return {\r\n            id: pump.id,\r\n            name: pump.name || `水泵${pump.id}`,\r\n            isRunning: pump.status === 'running',\r\n            frequency: pump.frequency || 0\r\n          };\r\n        });\r\n        \r\n        // 确保选中的索引有效\r\n        if (this.selectedPumpIndex >= this.deviceControlData.pumps.length) {\r\n          this.selectedPumpIndex = 0;\r\n        }\r\n      } else {\r\n        console.warn('水泵数据缺失，保留默认数据');\r\n      }\r\n      \r\n      // 同步更新临时变量\r\n      if (this.deviceControlData.pumps.length > 0) {\r\n        // 先检查所选水泵的频率是否存在\r\n        if (this.deviceControlData.pumps[this.selectedPumpIndex]) {\r\n          const currentFreq = this.deviceControlData.pumps[this.selectedPumpIndex].frequency;\r\n          console.log(`水泵[${this.selectedPumpIndex}]当前频率:`, currentFreq);\r\n          this.tempPumpFrequency = currentFreq;\r\n        } else {\r\n          console.warn('选中的水泵索引无效，重置为0');\r\n          this.selectedPumpIndex = 0;\r\n          this.tempPumpFrequency = this.deviceControlData.pumps[0].frequency || 0;\r\n        }\r\n      }\r\n      \r\n      // 记录当前工作模式和站点状态，用于调试\r\n      console.log(`当前工作模式:${this.workMode}, 站点状态:${this.selectedStation.status}, 临时开度值:${this.tempValveOpenDegree}, 临时频率值:${this.tempPumpFrequency}`);\r\n    },\r\n    \r\n    // 工作模式切换\r\n    async handleWorkModeChange(mode) {\r\n      console.log('尝试切换工作模式:', mode);\r\n      if (this.workMode === mode || !this.selectedStation) return;\r\n      \r\n      // 确认切换前先检查站点状态\r\n      if (this.selectedStation.status !== 'online') {\r\n        console.warn('站点不在线，无法切换工作模式');\r\n        uni.showToast({\r\n          title: '站点不在线，无法切换模式',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      // 如果从自动切换到手动，提醒用户算法将不再生效\r\n      if (mode === 'manual' && this.workMode === 'auto') {\r\n        uni.showModal({\r\n          title: '确认切换',\r\n          content: '切换到手动模式后，控制算法将不再生效。是否继续？',\r\n          success: res => {\r\n            if (res.confirm) {\r\n              this.doWorkModeChange(mode);\r\n            }\r\n          }\r\n        });\r\n      } \r\n      // 如果从手动切换到自动，提醒用户算法将会生效\r\n      else if (mode === 'auto' && this.workMode === 'manual') {\r\n        uni.showModal({\r\n          title: '确认切换',\r\n          content: '切换到自动模式后，系统将根据选择的控制算法自动调节设备。是否继续？',\r\n          success: res => {\r\n            if (res.confirm) {\r\n              this.doWorkModeChange(mode);\r\n            }\r\n          }\r\n        });\r\n      }\r\n    },\r\n    \r\n    // 执行工作模式切换\r\n    async doWorkModeChange(mode) {\r\n      try {\r\n        // 显示加载中提示\r\n        uni.showLoading({\r\n          title: '正在切换模式...',\r\n          mask: true\r\n        });\r\n        \r\n        const params = {\r\n          hes_id: this.selectedStation.id,\r\n          mode: mode === 'auto' ? 'automatic' : 'manual',\r\n          operator_id: uni.getStorageSync('userId') || 1\r\n        };\r\n        \r\n        console.log('发送模式切换请求:', JSON.stringify(params));\r\n        const result = await heatingStationApi.setOperationMode(params);\r\n        console.log('模式切换结果:', JSON.stringify(result));\r\n        \r\n        if (result.code === 200) {\r\n          // 先更新本地模式，便于UI响应\r\n          this.workMode = mode;\r\n          \r\n          // 模式相关提示\r\n          let successMessage = '';\r\n          if (mode === 'auto') {\r\n            successMessage = '已切换到自动模式，控制算法已生效';\r\n          } else {\r\n            successMessage = '已切换到手动模式，可手动控制设备';\r\n          }\r\n          \r\n          uni.showToast({\r\n            title: successMessage,\r\n            icon: 'success',\r\n            duration: 2000\r\n          });\r\n          \r\n          // 提示用户切换成功\r\n          console.log(`成功将工作模式切换为: ${mode}`);\r\n          \r\n          // 重新加载站点数据以刷新UI状态\r\n          setTimeout(() => {\r\n            console.log('模式切换成功，重新加载站点数据...');\r\n            this.loadStationData();\r\n          }, 1000);\r\n        } else {\r\n          console.error('模式切换失败:', result.message || '未知错误');\r\n          uni.showToast({\r\n            title: result.message || '模式切换失败',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error('模式切换异常:', error);\r\n        uni.showToast({\r\n          title: error.message || '操作失败，请重试',\r\n          icon: 'none'\r\n        });\r\n      } finally {\r\n        // 确保隐藏加载提示\r\n        uni.hideLoading();\r\n      }\r\n    },\r\n    \r\n    // 处理阀门选择\r\n    handleValveSelect(e) {\r\n      this.selectedValveIndex = e.detail.value;\r\n      // 更新临时存储的开度值\r\n      this.tempValveOpenDegree = this.deviceControlData.valves[this.selectedValveIndex].openDegree;\r\n    },\r\n    \r\n    // 处理水泵选择\r\n    handlePumpSelect(e) {\r\n      this.selectedPumpIndex = e.detail.value;\r\n      // 更新临时存储的频率值\r\n      this.tempPumpFrequency = this.deviceControlData.pumps[this.selectedPumpIndex].frequency;\r\n    },\r\n    \r\n    // 更新阀门开度\r\n    updateTempValveOpenDegree(event) {\r\n      // 只更新临时值，不直接修改设备状态\r\n      const newValue = parseInt(event.detail.value) || 0;\r\n      console.log(`阀门开度临时值从 ${this.tempValveOpenDegree} 更新为 ${newValue}`);\r\n      this.tempValveOpenDegree = newValue;\r\n    },\r\n    \r\n    // 确认阀门设置\r\n    confirmValveSettings() {\r\n      // 检查当前阀门状态\r\n      const currentValve = this.deviceControlData.valves[this.selectedValveIndex];\r\n      if (!currentValve) {\r\n        console.error('当前选中的阀门不存在');\r\n        uni.showToast({\r\n          title: '阀门数据错误',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      if (!currentValve.isOpen) {\r\n        uni.showToast({\r\n          title: '请先打开阀门',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      // 显示确认弹窗\r\n      uni.showModal({\r\n        title: '确认设置',\r\n        content: `确认将\"${currentValve.name}\"开度设置为${this.tempValveOpenDegree}%吗？`,\r\n        success: async (res) => {\r\n          if (res.confirm) {\r\n            // 更新本地数据\r\n            this.updateValveState(currentValve.id, { openDegree: this.tempValveOpenDegree });\r\n            // 发送控制命令\r\n            await this.sendValveControlCommand(currentValve.id);\r\n          }\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 更新水泵频率\r\n    updateTempPumpFrequency(event) {\r\n      // 只更新临时值，不直接修改设备状态\r\n      const newValue = parseInt(event.detail.value) || 0;\r\n      console.log(`水泵频率临时值从 ${this.tempPumpFrequency} 更新为 ${newValue}`);\r\n      this.tempPumpFrequency = newValue;\r\n    },\r\n    \r\n    // 确认水泵设置\r\n    confirmPumpSettings() {\r\n      // 检查当前水泵状态\r\n      const currentPump = this.deviceControlData.pumps[this.selectedPumpIndex];\r\n      if (!currentPump) {\r\n        console.error('当前选中的水泵不存在');\r\n        uni.showToast({\r\n          title: '水泵数据错误',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      if (!currentPump.isRunning) {\r\n        uni.showToast({\r\n          title: '请先启动水泵',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      // 显示确认弹窗\r\n      uni.showModal({\r\n        title: '确认设置',\r\n        content: `确认将\"${currentPump.name}\"频率设置为${this.tempPumpFrequency}Hz吗？`,\r\n        success: async (res) => {\r\n          if (res.confirm) {\r\n            // 更新本地数据\r\n            this.updatePumpState(currentPump.id, { frequency: this.tempPumpFrequency });\r\n            // 发送控制命令\r\n            await this.sendPumpControlCommand(currentPump.id);\r\n          }\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 阀门开关控制\r\n    async handleValveSwitch(valveId, event) {\r\n      const isOpen = event.detail.value;\r\n      \r\n      // 获取当前阀门\r\n      const valveIndex = this.deviceControlData.valves.findIndex(v => v.id === valveId);\r\n      if (valveIndex === -1) {\r\n        console.error('找不到ID为', valveId, '的阀门');\r\n        uni.showToast({\r\n          title: '操作失败，设备数据错误',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      const valve = this.deviceControlData.valves[valveIndex];\r\n      // 显示确认弹窗\r\n      uni.showModal({\r\n        title: '确认操作',\r\n        content: `确认${isOpen ? '打开' : '关闭'}\"${valve.name}\"吗？`,\r\n        success: async (res) => {\r\n          if (res.confirm) {\r\n            // 更新本地数据\r\n            this.updateValveState(valveId, { isOpen });\r\n            \r\n            // 如果关闭阀门，自动设置开度为0\r\n            if (!isOpen) {\r\n              this.updateValveState(valveId, { openDegree: 0 });\r\n              this.tempValveOpenDegree = 0;\r\n            } else {\r\n              // 如果打开阀门，更新临时开度值\r\n              this.tempValveOpenDegree = valve.openDegree;\r\n            }\r\n            \r\n            // 发送控制命令\r\n            await this.sendValveControlCommand(valveId);\r\n          } else {\r\n            // 用户取消，还原开关状态\r\n            // 通过下一个渲染周期更新UI\r\n            this.$nextTick(() => {\r\n              this.deviceControlData.valves[valveIndex].isOpen = !isOpen;\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 水泵开关控制\r\n    async handlePumpSwitch(pumpId, event) {\r\n      const isRunning = event.detail.value;\r\n      \r\n      // 获取当前水泵\r\n      const pumpIndex = this.deviceControlData.pumps.findIndex(p => p.id === pumpId);\r\n      if (pumpIndex === -1) {\r\n        console.error('找不到ID为', pumpId, '的水泵');\r\n        uni.showToast({\r\n          title: '操作失败，设备数据错误',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      const pump = this.deviceControlData.pumps[pumpIndex];\r\n      // 显示确认弹窗\r\n      uni.showModal({\r\n        title: '确认操作',\r\n        content: `确认${isRunning ? '启动' : '停止'}\"${pump.name}\"吗？`,\r\n        success: async (res) => {\r\n          if (res.confirm) {\r\n            // 更新本地数据\r\n            this.updatePumpState(pumpId, { isRunning });\r\n            \r\n            // 如果关闭水泵，自动设置频率为0\r\n            if (!isRunning) {\r\n              this.updatePumpState(pumpId, { frequency: 0 });\r\n              this.tempPumpFrequency = 0;\r\n            } else {\r\n              // 如果启动水泵，更新临时频率值\r\n              this.tempPumpFrequency = pump.frequency;\r\n            }\r\n            \r\n            // 发送控制命令\r\n            await this.sendPumpControlCommand(pumpId);\r\n          } else {\r\n            // 用户取消，还原开关状态\r\n            // 通过下一个渲染周期更新UI\r\n            this.$nextTick(() => {\r\n              this.deviceControlData.pumps[pumpIndex].isRunning = !isRunning;\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 更新阀门状态\r\n    updateValveState(valveId, newState) {\r\n      const valveIndex = this.deviceControlData.valves.findIndex(v => v.id === valveId);\r\n      if (valveIndex !== -1) {\r\n        this.deviceControlData.valves[valveIndex] = {\r\n          ...this.deviceControlData.valves[valveIndex],\r\n          ...newState\r\n        };\r\n      }\r\n    },\r\n    \r\n    // 更新水泵状态\r\n    updatePumpState(pumpId, newState) {\r\n      const pumpIndex = this.deviceControlData.pumps.findIndex(p => p.id === pumpId);\r\n      if (pumpIndex !== -1) {\r\n        this.deviceControlData.pumps[pumpIndex] = {\r\n          ...this.deviceControlData.pumps[pumpIndex],\r\n          ...newState\r\n        };\r\n      }\r\n    },\r\n    \r\n    // 发送阀门控制命令\r\n    async sendValveControlCommand(valveId) {\r\n      const valve = this.deviceControlData.valves.find(v => v.id === valveId);\r\n      if (!valve) return;\r\n      \r\n      try {\r\n        const controlParams = {\r\n          hes_id: this.selectedStation.id,\r\n          control_type: 'valve',\r\n          device_id: valveId,\r\n          action: valve.isOpen ? 'open' : 'close',\r\n          value: valve.openDegree, // 阀门开度\r\n          operator_id: uni.getStorageSync('userId') || 1,\r\n          remark: `${valve.isOpen ? '打开' : '关闭'}阀门，开度设为${valve.openDegree}%`\r\n        };\r\n        \r\n        const result = await heatingStationApi.controlDevice(controlParams);\r\n        \r\n        if (result.code === 200) {\r\n          uni.showToast({\r\n            title: '阀门控制成功',\r\n            icon: 'success'\r\n          });\r\n        } else {\r\n          uni.showToast({\r\n            title: result.message || '阀门控制失败',\r\n            icon: 'none'\r\n          });\r\n          // 失败时恢复原状态\r\n          this.loadStationData();\r\n        }\r\n      } catch (error) {\r\n        console.error('阀门控制错误:', error);\r\n        uni.showToast({\r\n          title: error.message || '操作失败，请重试',\r\n          icon: 'none'\r\n        });\r\n      }\r\n    },\r\n    \r\n    // 发送水泵控制命令\r\n    async sendPumpControlCommand(pumpId) {\r\n      const pump = this.deviceControlData.pumps.find(p => p.id === pumpId);\r\n      if (!pump) return;\r\n      \r\n      try {\r\n        const controlParams = {\r\n          hes_id: this.selectedStation.id,\r\n          control_type: 'pump',\r\n          device_id: pumpId,\r\n          action: pump.isRunning ? 'start' : 'stop',\r\n          value: pump.frequency, // 水泵频率\r\n          operator_id: uni.getStorageSync('userId') || 1,\r\n          remark: `${pump.isRunning ? '启动' : '停止'}水泵，频率设为${pump.frequency}Hz`\r\n        };\r\n        \r\n        const result = await heatingStationApi.controlDevice(controlParams);\r\n        \r\n        if (result.code === 200) {\r\n          uni.showToast({\r\n            title: '水泵控制成功',\r\n            icon: 'success'\r\n          });\r\n        } else {\r\n          uni.showToast({\r\n            title: result.message || '水泵控制失败',\r\n            icon: 'none'\r\n          });\r\n          // 失败时恢复原状态\r\n          this.loadStationData();\r\n        }\r\n      } catch (error) {\r\n        console.error('水泵控制错误:', error);\r\n        uni.showToast({\r\n          title: error.message || '操作失败，请重试',\r\n          icon: 'none'\r\n        });\r\n      }\r\n    },\r\n    \r\n    // 处理算法选择变更\r\n    handleAlgorithmChange(e) {\r\n      const oldAlgorithm = this.selectedAlgorithm;\r\n      this.selectedAlgorithm = e.detail.value;\r\n      console.log(`算法选择从 ${oldAlgorithm} 变更为 ${this.selectedAlgorithm}`);\r\n      \r\n      // 如果在手动模式下，提示用户需要切换到自动模式\r\n      if (this.workMode === 'manual') {\r\n        uni.showToast({\r\n          title: '请切换到自动模式以应用算法',\r\n          icon: 'none',\r\n          duration: 2000\r\n        });\r\n      }\r\n      \r\n      // 根据选择的算法类型设置默认的目标值\r\n      if (this.selectedAlgorithm !== oldAlgorithm) {\r\n        const defaultValues = {\r\n          'primary_flow': 80,\r\n          'primary_supply_temp': 85,\r\n          'secondary_supply_temp': 60,\r\n          'secondary_return_temp': 45,\r\n          'ai_prediction': 75\r\n        };\r\n        \r\n        if (defaultValues[this.selectedAlgorithm]) {\r\n          this.algorithmTargetValue = defaultValues[this.selectedAlgorithm];\r\n          console.log(`设置默认目标值: ${this.algorithmTargetValue} ${this.getAlgorithmUnit()}`);\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 获取算法描述\r\n    getAlgorithmDescription() {\r\n      const algoMap = {\r\n        'primary_flow': '通过控制一次侧流量来调节换热效果',\r\n        'primary_supply_temp': '通过调节一次供水温度来控制整体热量输出',\r\n        'secondary_supply_temp': '通过精确控制二次供水温度来满足用户需求',\r\n        'secondary_return_temp': '通过监控和调整二次回水温度优化系统效率',\r\n        'ai_prediction': '利用AI技术预测负荷变化并提前调整系统参数'\r\n      };\r\n      \r\n      return algoMap[this.selectedAlgorithm] || '请选择一种控制算法';\r\n    },\r\n    \r\n    // 获取算法目标值单位\r\n    getAlgorithmUnit() {\r\n      const unitMap = {\r\n        'primary_flow': 'm³/h',\r\n        'primary_supply_temp': '℃',\r\n        'secondary_supply_temp': '℃',\r\n        'secondary_return_temp': '℃',\r\n        'ai_prediction': ''\r\n      };\r\n      \r\n      return unitMap[this.selectedAlgorithm] || '';\r\n    },\r\n    \r\n    // 应用算法设置\r\n    async applyAlgorithmSettings() {\r\n      if (!this.selectedStation || !this.selectedAlgorithm) {\r\n        console.error('缺少站点或算法信息');\r\n        uni.showToast({\r\n          title: '请选择算法和设置目标值',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      // 验证站点状态\r\n      if (this.selectedStation.status !== 'online') {\r\n        console.warn('站点不在线，无法应用算法设置');\r\n        uni.showToast({\r\n          title: '站点不在线，无法设置算法',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      // 验证工作模式\r\n      if (this.workMode !== 'auto') {\r\n        console.warn('当前为手动模式，无法应用算法设置');\r\n        uni.showToast({\r\n          title: '自动模式下才能应用算法',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      // 验证目标值\r\n      const targetValue = parseFloat(this.algorithmTargetValue);\r\n      if (isNaN(targetValue) || targetValue <= 0) {\r\n        console.error('目标值无效:', this.algorithmTargetValue);\r\n        uni.showToast({\r\n          title: '请输入有效的目标值',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      try {\r\n        // 显示加载中提示\r\n        uni.showLoading({\r\n          title: '正在应用算法设置...',\r\n          mask: true\r\n        });\r\n        \r\n        const params = {\r\n          hes_id: this.selectedStation.id,\r\n          algorithm_type: this.selectedAlgorithm,\r\n          target_value: targetValue,\r\n          operator_id: uni.getStorageSync('userId') || 1\r\n        };\r\n        \r\n        console.log('发送算法设置请求:', JSON.stringify(params));\r\n        const result = await heatingStationApi.setControlAlgorithm(params);\r\n        console.log('算法设置结果:', JSON.stringify(result));\r\n        \r\n        if (result.code === 200) {\r\n          uni.showToast({\r\n            title: '算法设置成功',\r\n            icon: 'success'\r\n          });\r\n          \r\n          // 成功后可以重新加载数据以确认设置已生效\r\n          setTimeout(() => {\r\n            console.log('算法设置成功，重新加载站点数据...');\r\n            this.loadStationData();\r\n          }, 1000);\r\n        } else {\r\n          console.error('应用算法设置失败:', result.message || '未知错误');\r\n          uni.showToast({\r\n            title: result.message || '算法设置失败',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error('算法设置异常:', error);\r\n        uni.showToast({\r\n          title: error.message || '操作失败，请重试',\r\n          icon: 'none'\r\n        });\r\n      } finally {\r\n        // 确保隐藏加载提示\r\n        uni.hideLoading();\r\n      }\r\n    },\r\n    \r\n    // 初始化算法默认值\r\n    initAlgorithmDefaults() {\r\n      // 设置默认算法选项\r\n      if (!this.selectedAlgorithm) {\r\n        this.selectedAlgorithm = 'primary_flow';\r\n      }\r\n      \r\n      // 设置默认目标值\r\n      const defaultValues = {\r\n        'primary_flow': 80,\r\n        'primary_supply_temp': 85,\r\n        'secondary_supply_temp': 60,\r\n        'secondary_return_temp': 45,\r\n        'ai_prediction': 75\r\n      };\r\n      \r\n      // 如果没有设置目标值或目标值无效，设置默认值\r\n      if (!this.algorithmTargetValue || isNaN(parseFloat(this.algorithmTargetValue))) {\r\n        this.algorithmTargetValue = defaultValues[this.selectedAlgorithm] || 75;\r\n        console.log(`初始化算法默认值: ${this.selectedAlgorithm}, 目标值: ${this.algorithmTargetValue}`);\r\n      }\r\n    },\r\n    \r\n    // 增加频率方法\r\n    increasePumpFrequency() {\r\n      if (this.workMode === 'auto' || !this.deviceControlData.pumps[this.selectedPumpIndex].isRunning || this.selectedStation.status !== 'online') {\r\n        return;\r\n      }\r\n      \r\n      // 获取当前值\r\n      let currentValue = parseFloat(this.tempPumpFrequency) || 0;\r\n      // 增加0.5，最大为50\r\n      let newValue = Math.min(50, currentValue + 0.5);\r\n      // 格式化为一位小数\r\n      newValue = parseFloat(newValue.toFixed(1));\r\n      console.log(`增加水泵频率: ${currentValue} -> ${newValue}`);\r\n      // 更新临时值\r\n      this.tempPumpFrequency = newValue;\r\n    },\r\n    \r\n    // 执行水泵频率减少\r\n    decreasePumpFrequency() {\r\n      if (this.workMode === 'auto' || !this.deviceControlData.pumps[this.selectedPumpIndex].isRunning || this.selectedStation.status !== 'online') {\r\n        return;\r\n      }\r\n      \r\n      // 获取当前值\r\n      let currentValue = parseFloat(this.tempPumpFrequency) || 0;\r\n      // 减少0.5，最小为0\r\n      let newValue = Math.max(0, currentValue - 0.5);\r\n      // 格式化为一位小数\r\n      newValue = parseFloat(newValue.toFixed(1));\r\n      console.log(`减少水泵频率: ${currentValue} -> ${newValue}`);\r\n      // 更新临时值\r\n      this.tempPumpFrequency = newValue;\r\n    },\r\n    \r\n    // 返回上一页\r\n    navigateBack() {\r\n      uni.navigateBack();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style>\r\n.station-control {\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.page-title {\r\n  background-color: #0088ff;\r\n  color: #fff;\r\n  padding: 20rpx 30rpx;\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 40rpx;\r\n  font-weight: bold;\r\n}\r\n\r\n.back-button {\r\n  position: absolute;\r\n  left: 30rpx;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 1;\r\n}\r\n\r\n.tab-section {\r\n  background-color: #0088ff;\r\n  padding: 0 20rpx 16rpx;\r\n}\r\n\r\n.tab-bar {\r\n  display: flex;\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  border-radius: 8rpx;\r\n  padding: 6rpx;\r\n}\r\n\r\n.tab-item {\r\n  flex: 1;\r\n  text-align: center;\r\n  padding: 16rpx 0;\r\n  font-size: 28rpx;\r\n  border-radius: 6rpx;\r\n  color: #fff;\r\n}\r\n\r\n.tab-item.active {\r\n  background-color: rgba(255, 255, 255, 0.9);\r\n  color: #0088ff;\r\n  font-weight: bold;\r\n}\r\n\r\n.station-selector {\r\n  background-color: #fff;\r\n  padding: 30rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  border-bottom: 1rpx solid #eee;\r\n}\r\n\r\n.station-label {\r\n  font-size: 30rpx;\r\n  color: #666;\r\n  min-width: 150rpx;\r\n}\r\n\r\n.station-value-wrapper {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border: 1px solid #e5e5e5;\r\n  padding: 12rpx 20rpx;\r\n  border-radius: 6rpx;\r\n}\r\n\r\n.station-value-text {\r\n  font-size: 30rpx;\r\n  color: #333;\r\n  font-weight: bold;\r\n}\r\n\r\n.station-value-text.placeholder {\r\n  color: #999;\r\n  font-weight: normal;\r\n}\r\n\r\n.station-arrow {\r\n  font-size: 30rpx;\r\n  color: #999;\r\n}\r\n\r\n.station-content {\r\n  flex: 1;\r\n  padding: 20rpx;\r\n}\r\n\r\n.card {\r\n  background-color: #fff;\r\n  border-radius: 12rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 20rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.card-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n  margin-bottom: 20rpx;\r\n  border-bottom: 1px solid #f5f5f5;\r\n  padding-bottom: 16rpx;\r\n}\r\n\r\n.info-row {\r\n  display: flex;\r\n  padding: 16rpx 0;\r\n}\r\n\r\n.label {\r\n  color: #666;\r\n  width: 180rpx;\r\n}\r\n\r\n.value {\r\n  color: #333;\r\n  flex: 1;\r\n}\r\n\r\n.value.online {\r\n  color: #00c853;\r\n}\r\n\r\n.value.offline {\r\n  color: #ef5350;\r\n}\r\n\r\n.value.fault {\r\n  color: #ff9800;\r\n}\r\n\r\n.data-grid {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.data-item {\r\n  width: 50%;\r\n  padding: 20rpx 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.data-label {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.data-value {\r\n  font-size: 36rpx;\r\n  color: #0088ff;\r\n  font-weight: bold;\r\n}\r\n\r\n.control-grid {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.control-item {\r\n  width: 50%;\r\n  padding: 20rpx 0;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.control-label {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n}\r\n\r\n.slider-control {\r\n  margin: 30rpx 0;\r\n}\r\n\r\n.slider-label {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  margin-bottom: 20rpx;\r\n  display: block;\r\n}\r\n\r\n.slider-box {\r\n  padding: 0 20rpx;\r\n}\r\n\r\n.process-view {\r\n  background-color: #fff;\r\n  border-radius: 12rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.process-image {\r\n  width: 100%;\r\n  text-align: center;\r\n}\r\n\r\n.process-image image {\r\n  width: 100%;\r\n  max-width: 690rpx;\r\n}\r\n\r\n.process-legend {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 30rpx;\r\n}\r\n\r\n.legend-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin: 0 20rpx;\r\n}\r\n\r\n.legend-color {\r\n  width: 30rpx;\r\n  height: 16rpx;\r\n  margin-right: 10rpx;\r\n}\r\n\r\n.legend-text {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n}\r\n\r\n.no-station {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  color: #999;\r\n  font-size: 32rpx;\r\n}\r\n\r\n/* 站点选择器样式 */\r\n.station-picker {\r\n  background-color: #f5f5f5;\r\n  border-top-left-radius: 20rpx;\r\n  border-top-right-radius: 20rpx;\r\n  max-height: 80vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.picker-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 30rpx;\r\n  background-color: #fff;\r\n}\r\n\r\n.picker-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n}\r\n\r\n.picker-close {\r\n  font-size: 28rpx;\r\n  color: #666;\r\n}\r\n\r\n/* 搜索栏样式 */\r\n.search-bar {\r\n  padding: 20rpx 30rpx;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.search-input-wrap {\r\n  display: flex;\r\n  align-items: center;\r\n  background-color: #fff;\r\n  border-radius: 8rpx;\r\n  padding: 0 20rpx;\r\n  height: 80rpx;\r\n}\r\n\r\n.search-icon {\r\n  margin-right: 10rpx;\r\n  font-size: 28rpx;\r\n  color: #999;\r\n}\r\n\r\n.search-input {\r\n  flex: 1;\r\n  height: 80rpx;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.search-clear {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  line-height: 40rpx;\r\n  text-align: center;\r\n  color: #999;\r\n  font-size: 30rpx;\r\n}\r\n\r\n/* 状态筛选标签样式 */\r\n.filter-tabs {\r\n  display: flex;\r\n  padding: 20rpx 20rpx;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.filter-tab {\r\n  padding: 12rpx 24rpx;\r\n  margin: 0 10rpx;\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  background-color: #fff;\r\n  border-radius: 50rpx;\r\n}\r\n\r\n.filter-tab.active {\r\n  color: #fff;\r\n  background-color: #0088ff;\r\n}\r\n\r\n/* 站点列表样式 */\r\n.station-list {\r\n  flex: 1;\r\n  max-height: 60vh;\r\n}\r\n\r\n.station-item {\r\n  margin: 20rpx;\r\n  background-color: #fff;\r\n  border-radius: 8rpx;\r\n  overflow: hidden;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.station-item-content {\r\n  position: relative;\r\n  padding: 24rpx;\r\n}\r\n\r\n.station-main-info {\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.station-name {\r\n  font-size: 30rpx;\r\n  color: #333;\r\n  font-weight: bold;\r\n  margin-bottom: 8rpx;\r\n  display: block;\r\n}\r\n\r\n.station-address {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  display: block;\r\n}\r\n\r\n.station-temp-info {\r\n  margin-top: 12rpx;\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.temp-item {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n  flex: 1;\r\n}\r\n\r\n.station-status-tag {\r\n  position: absolute;\r\n  right: 24rpx;\r\n  bottom: 24rpx;\r\n  font-size: 24rpx;\r\n  padding: 4rpx 12rpx;\r\n  border-radius: 4rpx;\r\n}\r\n\r\n.station-status-tag.online {\r\n  color: #00c853;\r\n  background-color: rgba(0, 200, 83, 0.1);\r\n}\r\n\r\n.station-status-tag.offline {\r\n  color: #ff5252;\r\n  background-color: rgba(255, 82, 82, 0.1);\r\n}\r\n\r\n.station-status-tag.fault {\r\n  color: #ff9800;\r\n  background-color: rgba(255, 152, 0, 0.1);\r\n}\r\n\r\n.empty-tip {\r\n  padding: 60rpx 0;\r\n  text-align: center;\r\n  color: #999;\r\n  font-size: 28rpx;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n/* 工作模式卡片样式 */\r\n.mode-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.mode-title {\r\n  font-size: 30rpx;\r\n  color: #333;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.mode-buttons {\r\n  display: flex;\r\n  background-color: #f0f0f0;\r\n  border-radius: 8rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n.mode-button {\r\n  padding: 12rpx 40rpx;\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  text-align: center;\r\n}\r\n\r\n.mode-button.active {\r\n  background-color: #0088ff;\r\n  color: #fff;\r\n}\r\n\r\n.mode-desc {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  margin-top: 16rpx;\r\n}\r\n\r\n/* 设备控制卡片样式 */\r\n.device-section {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.blue-indicator {\r\n  width: 8rpx;\r\n  height: 32rpx;\r\n  background-color: #0088ff;\r\n  margin-right: 10rpx;\r\n}\r\n\r\n.section-title-text {\r\n  font-size: 30rpx;\r\n  color: #333;\r\n  font-weight: bold;\r\n}\r\n\r\n.device-control-block {\r\n  background-color: #fff;\r\n  border: 1rpx solid #eeeeee;\r\n  border-radius: 8rpx;\r\n  overflow: hidden;\r\n  padding: 20rpx;\r\n}\r\n\r\n.device-selector {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.selector-wrap {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.dropdown-label {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  margin-bottom: 6rpx;\r\n}\r\n\r\n.dropdown-value {\r\n  display: flex;\r\n  align-items: center;\r\n  border-bottom: 1px dashed #e0e0e0;\r\n  padding-bottom: 8rpx;\r\n}\r\n\r\n.device-name {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  font-weight: bold;\r\n}\r\n\r\n.dropdown-arrow {\r\n  font-size: 20rpx;\r\n  color: #0088ff;\r\n  margin-left: 10rpx;\r\n}\r\n\r\n.device-control-params {\r\n  padding-top: 10rpx;\r\n}\r\n\r\n.param-label {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  margin-bottom: 16rpx;\r\n  display: block;\r\n}\r\n\r\n.slider-row {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.param-value {\r\n  width: 50rpx;\r\n  text-align: right;\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  margin-left: 10rpx;\r\n}\r\n\r\n/* 控制算法卡片样式 */\r\n.algorithm-selector {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.algorithm-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 16rpx 0;\r\n}\r\n\r\n.algorithm-radio {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.algorithm-label {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  margin-left: 5rpx;\r\n}\r\n\r\n.algorithm-desc {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n  padding: 10rpx;\r\n  background-color: #f9f9f9;\r\n  border-radius: 6rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.algorithm-params {\r\n  margin-top: 20rpx;\r\n}\r\n\r\n.param-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.param-input {\r\n  flex: 1;\r\n  height: 70rpx;\r\n  border: 1rpx solid #e5e5e5;\r\n  border-radius: 6rpx;\r\n  padding: 0 20rpx;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.param-unit {\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  margin-left: 10rpx;\r\n}\r\n\r\n.apply-button {\r\n  background-color: #0088ff;\r\n  color: #fff;\r\n  font-size: 28rpx;\r\n  padding: 16rpx 0;\r\n  border-radius: 6rpx;\r\n  margin-top: 20rpx;\r\n}\r\n\r\n.apply-button[disabled] {\r\n  background-color: #cccccc;\r\n  color: #999;\r\n}\r\n\r\n/* 新增样式 */\r\n.confirm-button-row {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 20rpx;\r\n}\r\n\r\n.confirm-button {\r\n  background-color: #0088ff;\r\n  color: #fff;\r\n  font-size: 28rpx;\r\n  padding: 12rpx 24rpx;\r\n  border-radius: 6rpx;\r\n}\r\n\r\n.confirm-button[disabled] {\r\n  background-color: #cccccc;\r\n  color: #999;\r\n}\r\n\r\n/* 设备控制标签页样式 */\r\n.device-tabs {\r\n  display: flex;\r\n  margin-bottom: 20rpx;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.device-tab {\r\n  flex: 1;\r\n  text-align: center;\r\n  padding: 20rpx 0;\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  position: relative;\r\n}\r\n\r\n.device-tab.active {\r\n  color: #0088ff;\r\n  font-weight: bold;\r\n}\r\n\r\n.device-tab.active:after {\r\n  content: \"\";\r\n  position: absolute;\r\n  bottom: -2rpx;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 40%;\r\n  height: 4rpx;\r\n  background-color: #0088ff;\r\n  border-radius: 2rpx;\r\n}\r\n\r\n/* 工业变频器风格的频率控制样式 */\r\n.frequency-control {\r\n  display: flex;\r\n  margin: 20rpx 0;\r\n  border: 2rpx solid #e0e0e0;\r\n  border-radius: 8rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n.frequency-control.disabled {\r\n  opacity: 0.6;\r\n}\r\n\r\n.frequency-display {\r\n  flex: 1;\r\n  background: #fff;\r\n  padding: 0 20rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  border: 1rpx solid #e0e0e0;\r\n  height: 80rpx;\r\n  border-top-left-radius: 8rpx;\r\n  border-bottom-left-radius: 8rpx;\r\n}\r\n\r\n.frequency-input {\r\n  flex: 1;\r\n  height: 80rpx;\r\n  font-size: 40rpx;\r\n  color: #333;\r\n  font-weight: bold;\r\n  text-align: right;\r\n  background-color: transparent;\r\n}\r\n\r\n.frequency-unit {\r\n  font-size: 30rpx;\r\n  color: #666;\r\n  margin-left: 10rpx;\r\n  width: 40rpx;\r\n}\r\n\r\n.frequency-buttons {\r\n  width: 80rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.frequency-button {\r\n  height: 40rpx;\r\n  line-height: 40rpx;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #0088ff;\r\n  color: white;\r\n  font-size: 24rpx;\r\n}\r\n\r\n.frequency-button.disabled {\r\n  background-color: #cccccc;\r\n}\r\n\r\n.frequency-button.increase {\r\n  border-bottom: 1rpx solid rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n</style> ", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/hes/control.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "heatingStationApi"], "mappings": ";;;;AAmaA,MAAK,YAAU;AAAA,EACb,YAAY;AAAA;AAAA,EAEX;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,aAAa,CAAE;AAAA,MACf,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,qBAAqB;AAAA,MACrB,SAAS;AAAA;AAAA,MAET,eAAe;AAAA,QACb,QAAQ;AAAA,QACR,SAAS;AAAA,MACV;AAAA;AAAA,MAED,UAAU;AAAA;AAAA,MAEV,iBAAiB;AAAA;AAAA,MAEjB,mBAAmB;AAAA,QACjB,QAAQ;AAAA,UACN,EAAE,IAAI,GAAG,MAAM,WAAW,QAAQ,OAAO,YAAY,EAAG;AAAA,UACxD,EAAE,IAAI,GAAG,MAAM,WAAW,QAAQ,OAAO,YAAY,EAAG;AAAA,UACxD,EAAE,IAAI,GAAG,MAAM,WAAW,QAAQ,OAAO,YAAY,EAAE;AAAA,QACxD;AAAA,QACD,OAAO;AAAA,UACL,EAAE,IAAI,GAAG,MAAM,SAAS,WAAW,OAAO,WAAW,EAAG;AAAA,UACxD,EAAE,IAAI,GAAG,MAAM,SAAS,WAAW,OAAO,WAAW,EAAE;AAAA,QACzD;AAAA,MACD;AAAA;AAAA,MAED,oBAAoB;AAAA,MACpB,mBAAmB;AAAA;AAAA,MAEnB,YAAY;AAAA,QACV,EAAE,OAAO,gBAAgB,OAAO,WAAY;AAAA,QAC5C,EAAE,OAAO,uBAAuB,OAAO,aAAc;AAAA,QACrD,EAAE,OAAO,yBAAyB,OAAO,aAAc;AAAA,QACvD,EAAE,OAAO,yBAAyB,OAAO,aAAc;AAAA,QACvD,EAAE,OAAO,iBAAiB,OAAO,SAAS;AAAA,MAC3C;AAAA,MACD,mBAAmB;AAAA,MACnB,sBAAsB;AAAA,MACtB,qBAAqB;AAAA,MACrB,mBAAmB;AAAA;EAEtB;AAAA,EACD,UAAU;AAAA;AAAA,IAER,mBAAmB;AACjB,UAAI,CAAC,KAAK,eAAe,KAAK,YAAY,WAAW;AAAG,eAAO;AAE/D,aAAO,KAAK,YAAY,OAAO,aAAW;AAExC,YAAI,KAAK,cAAc,WAAW,SAAS,QAAQ,WAAW,KAAK,cAAc,QAAQ;AACvF,iBAAO;AAAA,QACT;AAGA,YAAI,KAAK,cAAc,WAAW,CAAC,QAAQ,KAAK,SAAS,KAAK,cAAc,OAAO,GAAG;AACpF,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACD;AAAA,EACD,UAAU;AAER,QAAI,KAAK,MAAM,cAAc;AAC3BA,oBAAAA,MAAY,MAAA,OAAA,gCAAA,wBAAwB;AAAA,WAC/B;AACLA,oBAAAA,MAAc,MAAA,SAAA,gCAAA,2BAA2B;AAAA,IAC3C;AAAA,EACD;AAAA,EACD,SAAS;AAEP,SAAK,gBAAe;AAGpB,UAAM,gBAAgBA,cAAAA,MAAI,eAAe,iBAAiB;AAC1D,QAAI,eAAe;AACjB,UAAI;AACF,aAAK,kBAAkB,KAAK,MAAM,aAAa;AAC/C,aAAK,gBAAe;AAAA,MACtB,SAAS,GAAG;AACVA,sBAAA,MAAA,MAAA,SAAA,gCAAc,cAAc,CAAC;AAAA,MAC/B;AAAA,IACF;AAGA,SAAK,sBAAqB;AAAA,EAC3B;AAAA,EACD,SAAS;AAEP,SAAK,UAAU,MAAM;AAEnB,UAAI,CAAC,KAAK,MAAM,cAAc;AAC5B,mBAAW,MAAM;AACf,eAAK,oBAAmB;AAAA,QACzB,GAAE,GAAG;AAAA,MACR;AAGA,UAAI,KAAK,mBAAmB,KAAK,gBAAgB,IAAI;AACnDA,sBAAAA,MAAY,MAAA,OAAA,gCAAA,aAAa;AACzB,aAAK,gBAAe;AAAA,MACtB;AAAA,IACF,CAAC;AAAA,EACF;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,sBAAsB;AACpB,UAAI,CAAC,KAAK,MAAM,cAAc;AAC5BA,sBAAAA,MAAA,MAAA,SAAA,gCAAc,sDAAsD;AAEpEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACZ,CAAC;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAED,MAAM,kBAAkB;AACtBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAED,UAAI;AACF,cAAM,MAAM,MAAMC,UAAiB,kBAAC,QAAQ;AAAA,UAC1C,UAAU;AAAA,UACV,WAAW;AAAA,QACb,CAAC;AACD,YAAI,IAAI,SAAS,OAAO,IAAI,QAAQ,IAAI,KAAK,MAAM;AACjD,eAAK,cAAc,IAAI,KAAK;AAAA,eACvB;AACLD,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,gCAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH,UAAU;AACRA,sBAAG,MAAC,YAAW;AAAA,MACjB;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB;AAClB,UAAI,KAAK,YAAY,WAAW,GAAG;AACjC,aAAK,kBAAkB,KAAK,MAAM;AAChC,eAAK,iBAAgB;AAAA,QACvB,CAAC;AAAA,aACI;AACL,aAAK,iBAAgB;AAAA,MACvB;AAAA,IACD;AAAA;AAAA,IAGD,mBAAmB;AAEjB,WAAK,cAAc,SAAS;AAC5B,WAAK,cAAc,UAAU;AAG7B,UAAI,KAAK,MAAM,cAAc;AAC3B,aAAK,MAAM,aAAa;aACnB;AACLA,sBAAAA,MAAc,MAAA,SAAA,gCAAA,aAAa;AAC3BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB;AAClB,UAAI,KAAK,MAAM,cAAc;AAC3B,aAAK,MAAM,aAAa;MAC1B;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB,QAAQ;AACtB,WAAK,cAAc,SAAS;AAAA,IAC7B;AAAA;AAAA,IAGD,iBAAiB;AACfA,0BAAY,MAAA,OAAA,gCAAA,UAAU,KAAK,cAAc,OAAO;AAAA,IAEjD;AAAA;AAAA,IAGD,oBAAoB,SAAS;AAC3B,WAAK,kBAAkB;AAEvBA,oBAAG,MAAC,eAAe,mBAAmB,KAAK,UAAU,OAAO,CAAC;AAC7D,WAAK,gBAAe;AACpB,WAAK,kBAAiB;AAAA,IACvB;AAAA;AAAA,IAGD,MAAM,kBAAkB;AACtB,UAAI,CAAC,KAAK;AAAiB;AAE3BA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAED,UAAI;AACF,cAAM,MAAM,MAAMC,4BAAkB,UAAU,KAAK,gBAAgB,EAAE;AACrED,4BAAY,MAAA,OAAA,gCAAA,WAAW,KAAK,UAAU,GAAG,CAAC;AAE1C,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAChC,eAAK,cAAc,IAAI;AAGvB,cAAI,KAAK,YAAY,iBAAiB,KAAK,YAAY,cAAc,gBAAgB;AAEnF,kBAAM,aAAa,KAAK,YAAY,cAAc,eAAe;AACjE,gBAAI,YAAY;AACd,mBAAK,kBAAkB,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,UAAU,CAAC,CAAC;AAAA,YAC1E;AAGA,kBAAM,iBAAiB,KAAK,YAAY,cAAc,eAAe;AACrE,gBAAI,gBAAgB;AAClB,mBAAK,sBAAsB,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,iBAAiB,GAAG,CAAC,CAAC;AAAA,YACxF;AAAA,iBACK;AACLA,0BAAAA,MAAA,MAAA,QAAA,gCAAa,qBAAqB;AAAA,UACpC;AAGA,cAAI,KAAK,YAAY,gBAAgB;AACnCA,gCAAA,MAAA,OAAA,gCAAY,eAAe,KAAK,YAAY,cAAc;AAC1D,iBAAK,WAAW,KAAK,YAAY,mBAAmB,cAAc,SAAS;AAAA,iBACtE;AAELA,0BAAAA,MAAA,MAAA,QAAA,gCAAa,qBAAqB;AAClC,iBAAK,WAAW;AAAA,UAClB;AAGA,cAAI,KAAK,YAAY,mBAAmB;AACtC,iBAAK,oBAAoB,KAAK,YAAY,kBAAkB,QAAQ;AACpE,gBAAI,KAAK,YAAY,kBAAkB,cAAc;AACnD,mBAAK,uBAAuB,KAAK,YAAY,kBAAkB;AAAA,YACjE;AAAA,iBACK;AACLA,0BAAAA,MAAa,MAAA,QAAA,gCAAA,mBAAmB;AAChC,iBAAK,oBAAoB;AACzB,iBAAK,uBAAuB;AAAA,UAC9B;AAGA,cAAI,KAAK,YAAY,eAAe;AAClCA,0BAAAA,MAAY,MAAA,OAAA,gCAAA,YAAY;AACxB,iBAAK,wBAAuB;AAAA,iBACvB;AACLA,0BAAAA,MAAc,MAAA,SAAA,gCAAA,mBAAmB;AAEjC,iBAAK,uBAAsB;AAAA,UAC7B;AAAA,eAEK;AACLA,8BAAc,MAAA,SAAA,gCAAA,eAAe,IAAI,WAAW,MAAM;AAClDA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAED,eAAK,uBAAsB;AAAA,QAC7B;AAAA,MACA,SAAO,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,gCAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAED,aAAK,uBAAsB;AAAA,MAC7B,UAAU;AACRA,sBAAG,MAAC,YAAW;AAAA,MACjB;AAAA,IACD;AAAA;AAAA,IAGD,yBAAyB;;AACvB,UAAI;AACF,aAAK,wBAAsB,UAAK,kBAAkB,OAAO,KAAK,kBAAkB,MAArD,mBAAwD,eAAc;AACjG,aAAK,sBAAoB,UAAK,kBAAkB,MAAM,KAAK,iBAAiB,MAAnD,mBAAsD,cAAa;AAAA,MAC9F,SAAS,GAAG;AACVA,sBAAA,MAAA,MAAA,SAAA,gCAAc,aAAa,CAAC;AAE5B,aAAK,sBAAsB;AAC3B,aAAK,oBAAoB;AAEzB,aAAK,qBAAqB;AAC1B,aAAK,oBAAoB;AAAA,MAC3B;AACAA,oBAAAA,MAAY,MAAA,OAAA,gCAAA,aAAa;AAAA,QACvB,qBAAqB,KAAK;AAAA,QAC1B,mBAAmB,KAAK;AAAA,QACxB,oBAAoB,KAAK;AAAA,QACzB,mBAAmB,KAAK;AAAA,MAC1B,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,0BAA0B;AACxBA,uEAAY,YAAY,KAAK,UAAU,KAAK,WAAW,CAAC;AAGxD,UAAI,CAAC,KAAK,YAAY,iBAAiB,CAAC,KAAK,YAAY,cAAc,kBAAkB;AACvFA,sBAAAA,MAAA,MAAA,SAAA,gCAAc,eAAe;AAE7B,aAAK,qBAAqB,KAAK,IAAI,KAAK,oBAAoB,KAAK,kBAAkB,OAAO,SAAS,CAAC;AACpG,aAAK,oBAAoB,KAAK,IAAI,KAAK,mBAAmB,KAAK,kBAAkB,MAAM,SAAS,CAAC;AAGjG,aAAK,sBAAsB,KAAK,kBAAkB,OAAO,KAAK,kBAAkB,EAAE,cAAc;AAChG,aAAK,oBAAoB,KAAK,kBAAkB,MAAM,KAAK,iBAAiB,EAAE,aAAa;AAC3F;AAAA,MACF;AAEA,YAAM,cAAc,KAAK,YAAY,cAAc;AACnDA,0BAAY,MAAA,OAAA,gCAAA,WAAW,KAAK,UAAU,WAAW,CAAC;AAGlD,UAAI,YAAY,UAAU,YAAY,OAAO,SAAS,GAAG;AACvDA,4BAAA,MAAA,OAAA,gCAAY,WAAW,KAAK,UAAU,YAAY,MAAM,CAAC;AACzD,aAAK,kBAAkB,SAAS,YAAY,OAAO,IAAI,WAAS;AAC9D,iBAAO;AAAA,YACL,IAAI,MAAM;AAAA,YACV,MAAM,MAAM,QAAQ,KAAK,MAAM,EAAE;AAAA,YACjC,QAAQ,MAAM,WAAW;AAAA,YACzB,YAAY,MAAM,kBAAkB;AAAA;QAExC,CAAC;AAGD,YAAI,KAAK,sBAAsB,KAAK,kBAAkB,OAAO,QAAQ;AACnE,eAAK,qBAAqB;AAAA,QAC5B;AAAA,aACK;AACLA,sBAAAA,MAAA,MAAA,QAAA,gCAAa,eAAe;AAAA,MAC9B;AAGA,UAAI,KAAK,kBAAkB,OAAO,SAAS,GAAG;AAE5C,YAAI,KAAK,kBAAkB,OAAO,KAAK,kBAAkB,GAAG;AAC1D,gBAAM,gBAAgB,KAAK,kBAAkB,OAAO,KAAK,kBAAkB,EAAE;AAC7EA,wBAAAA,MAAA,MAAA,OAAA,gCAAY,MAAM,KAAK,kBAAkB,UAAU,aAAa;AAChE,eAAK,sBAAsB;AAAA,eACtB;AACLA,wBAAAA,MAAA,MAAA,QAAA,gCAAa,gBAAgB;AAC7B,eAAK,qBAAqB;AAC1B,eAAK,sBAAsB,KAAK,kBAAkB,OAAO,CAAC,EAAE,cAAc;AAAA,QAC5E;AAAA,MACF;AAGA,UAAI,YAAY,SAAS,YAAY,MAAM,SAAS,GAAG;AACrDA,4BAAA,MAAA,OAAA,gCAAY,WAAW,KAAK,UAAU,YAAY,KAAK,CAAC;AACxD,aAAK,kBAAkB,QAAQ,YAAY,MAAM,IAAI,UAAQ;AAC3D,iBAAO;AAAA,YACL,IAAI,KAAK;AAAA,YACT,MAAM,KAAK,QAAQ,KAAK,KAAK,EAAE;AAAA,YAC/B,WAAW,KAAK,WAAW;AAAA,YAC3B,WAAW,KAAK,aAAa;AAAA;QAEjC,CAAC;AAGD,YAAI,KAAK,qBAAqB,KAAK,kBAAkB,MAAM,QAAQ;AACjE,eAAK,oBAAoB;AAAA,QAC3B;AAAA,aACK;AACLA,sBAAAA,MAAA,MAAA,QAAA,gCAAa,eAAe;AAAA,MAC9B;AAGA,UAAI,KAAK,kBAAkB,MAAM,SAAS,GAAG;AAE3C,YAAI,KAAK,kBAAkB,MAAM,KAAK,iBAAiB,GAAG;AACxD,gBAAM,cAAc,KAAK,kBAAkB,MAAM,KAAK,iBAAiB,EAAE;AACzEA,wBAAAA,MAAA,MAAA,OAAA,gCAAY,MAAM,KAAK,iBAAiB,UAAU,WAAW;AAC7D,eAAK,oBAAoB;AAAA,eACpB;AACLA,wBAAAA,MAAA,MAAA,QAAA,gCAAa,gBAAgB;AAC7B,eAAK,oBAAoB;AACzB,eAAK,oBAAoB,KAAK,kBAAkB,MAAM,CAAC,EAAE,aAAa;AAAA,QACxE;AAAA,MACF;AAGAA,0BAAA,MAAA,OAAA,gCAAY,UAAU,KAAK,QAAQ,UAAU,KAAK,gBAAgB,MAAM,WAAW,KAAK,mBAAmB,WAAW,KAAK,iBAAiB,EAAE;AAAA,IAC/I;AAAA;AAAA,IAGD,MAAM,qBAAqB,MAAM;AAC/BA,uEAAY,aAAa,IAAI;AAC7B,UAAI,KAAK,aAAa,QAAQ,CAAC,KAAK;AAAiB;AAGrD,UAAI,KAAK,gBAAgB,WAAW,UAAU;AAC5CA,sBAAAA,MAAa,MAAA,QAAA,gCAAA,gBAAgB;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAGA,UAAI,SAAS,YAAY,KAAK,aAAa,QAAQ;AACjDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,SAAS,SAAO;AACd,gBAAI,IAAI,SAAS;AACf,mBAAK,iBAAiB,IAAI;AAAA,YAC5B;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH,WAES,SAAS,UAAU,KAAK,aAAa,UAAU;AACtDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,SAAS,SAAO;AACd,gBAAI,IAAI,SAAS;AACf,mBAAK,iBAAiB,IAAI;AAAA,YAC5B;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,iBAAiB,MAAM;AAC3B,UAAI;AAEFA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAED,cAAM,SAAS;AAAA,UACb,QAAQ,KAAK,gBAAgB;AAAA,UAC7B,MAAM,SAAS,SAAS,cAAc;AAAA,UACtC,aAAaA,cAAG,MAAC,eAAe,QAAQ,KAAK;AAAA;AAG/CA,yEAAY,aAAa,KAAK,UAAU,MAAM,CAAC;AAC/C,cAAM,SAAS,MAAMC,UAAAA,kBAAkB,iBAAiB,MAAM;AAC9DD,4BAAY,MAAA,OAAA,gCAAA,WAAW,KAAK,UAAU,MAAM,CAAC;AAE7C,YAAI,OAAO,SAAS,KAAK;AAEvB,eAAK,WAAW;AAGhB,cAAI,iBAAiB;AACrB,cAAI,SAAS,QAAQ;AACnB,6BAAiB;AAAA,iBACZ;AACL,6BAAiB;AAAA,UACnB;AAEAA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACZ,CAAC;AAGDA,wBAAY,MAAA,MAAA,OAAA,gCAAA,eAAe,IAAI,EAAE;AAGjC,qBAAW,MAAM;AACfA,0BAAAA,MAAA,MAAA,OAAA,gCAAY,oBAAoB;AAChC,iBAAK,gBAAe;AAAA,UACrB,GAAE,GAAI;AAAA,eACF;AACLA,8BAAA,MAAA,SAAA,gCAAc,WAAW,OAAO,WAAW,MAAM;AACjDA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,OAAO,WAAW;AAAA,YACzB,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,gCAAA,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,QACR,CAAC;AAAA,MACH,UAAU;AAERA,sBAAG,MAAC,YAAW;AAAA,MACjB;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB,GAAG;AACnB,WAAK,qBAAqB,EAAE,OAAO;AAEnC,WAAK,sBAAsB,KAAK,kBAAkB,OAAO,KAAK,kBAAkB,EAAE;AAAA,IACnF;AAAA;AAAA,IAGD,iBAAiB,GAAG;AAClB,WAAK,oBAAoB,EAAE,OAAO;AAElC,WAAK,oBAAoB,KAAK,kBAAkB,MAAM,KAAK,iBAAiB,EAAE;AAAA,IAC/E;AAAA;AAAA,IAGD,0BAA0B,OAAO;AAE/B,YAAM,WAAW,SAAS,MAAM,OAAO,KAAK,KAAK;AACjDA,oBAAAA,MAAA,MAAA,OAAA,gCAAY,YAAY,KAAK,mBAAmB,QAAQ,QAAQ,EAAE;AAClE,WAAK,sBAAsB;AAAA,IAC5B;AAAA;AAAA,IAGD,uBAAuB;AAErB,YAAM,eAAe,KAAK,kBAAkB,OAAO,KAAK,kBAAkB;AAC1E,UAAI,CAAC,cAAc;AACjBA,sBAAAA,MAAc,MAAA,SAAA,gCAAA,YAAY;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,aAAa,QAAQ;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAGAA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,OAAO,aAAa,IAAI,SAAS,KAAK,mBAAmB;AAAA,QAClE,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,SAAS;AAEf,iBAAK,iBAAiB,aAAa,IAAI,EAAE,YAAY,KAAK,oBAAkB,CAAG;AAE/E,kBAAM,KAAK,wBAAwB,aAAa,EAAE;AAAA,UACpD;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,wBAAwB,OAAO;AAE7B,YAAM,WAAW,SAAS,MAAM,OAAO,KAAK,KAAK;AACjDA,oBAAAA,MAAA,MAAA,OAAA,iCAAY,YAAY,KAAK,iBAAiB,QAAQ,QAAQ,EAAE;AAChE,WAAK,oBAAoB;AAAA,IAC1B;AAAA;AAAA,IAGD,sBAAsB;AAEpB,YAAM,cAAc,KAAK,kBAAkB,MAAM,KAAK,iBAAiB;AACvE,UAAI,CAAC,aAAa;AAChBA,sBAAAA,MAAc,MAAA,SAAA,iCAAA,YAAY;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,YAAY,WAAW;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAGAA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,OAAO,YAAY,IAAI,SAAS,KAAK,iBAAiB;AAAA,QAC/D,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,SAAS;AAEf,iBAAK,gBAAgB,YAAY,IAAI,EAAE,WAAW,KAAK,kBAAgB,CAAG;AAE1E,kBAAM,KAAK,uBAAuB,YAAY,EAAE;AAAA,UAClD;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,kBAAkB,SAAS,OAAO;AACtC,YAAM,SAAS,MAAM,OAAO;AAG5B,YAAM,aAAa,KAAK,kBAAkB,OAAO,UAAU,OAAK,EAAE,OAAO,OAAO;AAChF,UAAI,eAAe,IAAI;AACrBA,sBAAc,MAAA,MAAA,SAAA,iCAAA,UAAU,SAAS,KAAK;AACtCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,YAAM,QAAQ,KAAK,kBAAkB,OAAO,UAAU;AAEtDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,KAAK,SAAS,OAAO,IAAI,IAAI,MAAM,IAAI;AAAA,QAChD,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,SAAS;AAEf,iBAAK,iBAAiB,SAAS,EAAE,OAAQ,CAAA;AAGzC,gBAAI,CAAC,QAAQ;AACX,mBAAK,iBAAiB,SAAS,EAAE,YAAY,EAAG,CAAA;AAChD,mBAAK,sBAAsB;AAAA,mBACtB;AAEL,mBAAK,sBAAsB,MAAM;AAAA,YACnC;AAGA,kBAAM,KAAK,wBAAwB,OAAO;AAAA,iBACrC;AAGL,iBAAK,UAAU,MAAM;AACnB,mBAAK,kBAAkB,OAAO,UAAU,EAAE,SAAS,CAAC;AAAA,YACtD,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,iBAAiB,QAAQ,OAAO;AACpC,YAAM,YAAY,MAAM,OAAO;AAG/B,YAAM,YAAY,KAAK,kBAAkB,MAAM,UAAU,OAAK,EAAE,OAAO,MAAM;AAC7E,UAAI,cAAc,IAAI;AACpBA,sBAAc,MAAA,MAAA,SAAA,iCAAA,UAAU,QAAQ,KAAK;AACrCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,YAAM,OAAO,KAAK,kBAAkB,MAAM,SAAS;AAEnDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,KAAK,YAAY,OAAO,IAAI,IAAI,KAAK,IAAI;AAAA,QAClD,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,SAAS;AAEf,iBAAK,gBAAgB,QAAQ,EAAE,UAAW,CAAA;AAG1C,gBAAI,CAAC,WAAW;AACd,mBAAK,gBAAgB,QAAQ,EAAE,WAAW,EAAG,CAAA;AAC7C,mBAAK,oBAAoB;AAAA,mBACpB;AAEL,mBAAK,oBAAoB,KAAK;AAAA,YAChC;AAGA,kBAAM,KAAK,uBAAuB,MAAM;AAAA,iBACnC;AAGL,iBAAK,UAAU,MAAM;AACnB,mBAAK,kBAAkB,MAAM,SAAS,EAAE,YAAY,CAAC;AAAA,YACvD,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB,SAAS,UAAU;AAClC,YAAM,aAAa,KAAK,kBAAkB,OAAO,UAAU,OAAK,EAAE,OAAO,OAAO;AAChF,UAAI,eAAe,IAAI;AACrB,aAAK,kBAAkB,OAAO,UAAU,IAAI;AAAA,UAC1C,GAAG,KAAK,kBAAkB,OAAO,UAAU;AAAA,UAC3C,GAAG;AAAA;MAEP;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB,QAAQ,UAAU;AAChC,YAAM,YAAY,KAAK,kBAAkB,MAAM,UAAU,OAAK,EAAE,OAAO,MAAM;AAC7E,UAAI,cAAc,IAAI;AACpB,aAAK,kBAAkB,MAAM,SAAS,IAAI;AAAA,UACxC,GAAG,KAAK,kBAAkB,MAAM,SAAS;AAAA,UACzC,GAAG;AAAA;MAEP;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,wBAAwB,SAAS;AACrC,YAAM,QAAQ,KAAK,kBAAkB,OAAO,KAAK,OAAK,EAAE,OAAO,OAAO;AACtE,UAAI,CAAC;AAAO;AAEZ,UAAI;AACF,cAAM,gBAAgB;AAAA,UACpB,QAAQ,KAAK,gBAAgB;AAAA,UAC7B,cAAc;AAAA,UACd,WAAW;AAAA,UACX,QAAQ,MAAM,SAAS,SAAS;AAAA,UAChC,OAAO,MAAM;AAAA;AAAA,UACb,aAAaA,cAAG,MAAC,eAAe,QAAQ,KAAK;AAAA,UAC7C,QAAQ,GAAG,MAAM,SAAS,OAAO,IAAI,UAAU,MAAM,UAAU;AAAA;AAGjE,cAAM,SAAS,MAAMC,UAAAA,kBAAkB,cAAc,aAAa;AAElE,YAAI,OAAO,SAAS,KAAK;AACvBD,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,eACI;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,OAAO,WAAW;AAAA,YACzB,MAAM;AAAA,UACR,CAAC;AAED,eAAK,gBAAe;AAAA,QACtB;AAAA,MACA,SAAO,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,iCAAA,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,uBAAuB,QAAQ;AACnC,YAAM,OAAO,KAAK,kBAAkB,MAAM,KAAK,OAAK,EAAE,OAAO,MAAM;AACnE,UAAI,CAAC;AAAM;AAEX,UAAI;AACF,cAAM,gBAAgB;AAAA,UACpB,QAAQ,KAAK,gBAAgB;AAAA,UAC7B,cAAc;AAAA,UACd,WAAW;AAAA,UACX,QAAQ,KAAK,YAAY,UAAU;AAAA,UACnC,OAAO,KAAK;AAAA;AAAA,UACZ,aAAaA,cAAG,MAAC,eAAe,QAAQ,KAAK;AAAA,UAC7C,QAAQ,GAAG,KAAK,YAAY,OAAO,IAAI,UAAU,KAAK,SAAS;AAAA;AAGjE,cAAM,SAAS,MAAMC,UAAAA,kBAAkB,cAAc,aAAa;AAElE,YAAI,OAAO,SAAS,KAAK;AACvBD,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,eACI;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,OAAO,WAAW;AAAA,YACzB,MAAM;AAAA,UACR,CAAC;AAED,eAAK,gBAAe;AAAA,QACtB;AAAA,MACA,SAAO,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,iCAAA,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,sBAAsB,GAAG;AACvB,YAAM,eAAe,KAAK;AAC1B,WAAK,oBAAoB,EAAE,OAAO;AAClCA,oBAAAA,MAAA,MAAA,OAAA,iCAAY,SAAS,YAAY,QAAQ,KAAK,iBAAiB,EAAE;AAGjE,UAAI,KAAK,aAAa,UAAU;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACZ,CAAC;AAAA,MACH;AAGA,UAAI,KAAK,sBAAsB,cAAc;AAC3C,cAAM,gBAAgB;AAAA,UACpB,gBAAgB;AAAA,UAChB,uBAAuB;AAAA,UACvB,yBAAyB;AAAA,UACzB,yBAAyB;AAAA,UACzB,iBAAiB;AAAA;AAGnB,YAAI,cAAc,KAAK,iBAAiB,GAAG;AACzC,eAAK,uBAAuB,cAAc,KAAK,iBAAiB;AAChEA,wBAAAA,MAAA,MAAA,OAAA,iCAAY,YAAY,KAAK,oBAAoB,IAAI,KAAK,iBAAkB,CAAA,EAAE;AAAA,QAChF;AAAA,MACF;AAAA,IACD;AAAA;AAAA,IAGD,0BAA0B;AACxB,YAAM,UAAU;AAAA,QACd,gBAAgB;AAAA,QAChB,uBAAuB;AAAA,QACvB,yBAAyB;AAAA,QACzB,yBAAyB;AAAA,QACzB,iBAAiB;AAAA;AAGnB,aAAO,QAAQ,KAAK,iBAAiB,KAAK;AAAA,IAC3C;AAAA;AAAA,IAGD,mBAAmB;AACjB,YAAM,UAAU;AAAA,QACd,gBAAgB;AAAA,QAChB,uBAAuB;AAAA,QACvB,yBAAyB;AAAA,QACzB,yBAAyB;AAAA,QACzB,iBAAiB;AAAA;AAGnB,aAAO,QAAQ,KAAK,iBAAiB,KAAK;AAAA,IAC3C;AAAA;AAAA,IAGD,MAAM,yBAAyB;AAC7B,UAAI,CAAC,KAAK,mBAAmB,CAAC,KAAK,mBAAmB;AACpDA,sBAAAA,sDAAc,WAAW;AACzBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAGA,UAAI,KAAK,gBAAgB,WAAW,UAAU;AAC5CA,sBAAAA,MAAa,MAAA,QAAA,iCAAA,gBAAgB;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAGA,UAAI,KAAK,aAAa,QAAQ;AAC5BA,sBAAAA,MAAA,MAAA,QAAA,iCAAa,kBAAkB;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAGA,YAAM,cAAc,WAAW,KAAK,oBAAoB;AACxD,UAAI,MAAM,WAAW,KAAK,eAAe,GAAG;AAC1CA,4EAAc,UAAU,KAAK,oBAAoB;AACjDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI;AAEFA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAED,cAAM,SAAS;AAAA,UACb,QAAQ,KAAK,gBAAgB;AAAA,UAC7B,gBAAgB,KAAK;AAAA,UACrB,cAAc;AAAA,UACd,aAAaA,cAAG,MAAC,eAAe,QAAQ,KAAK;AAAA;AAG/CA,0EAAY,aAAa,KAAK,UAAU,MAAM,CAAC;AAC/C,cAAM,SAAS,MAAMC,UAAAA,kBAAkB,oBAAoB,MAAM;AACjED,4BAAY,MAAA,OAAA,iCAAA,WAAW,KAAK,UAAU,MAAM,CAAC;AAE7C,YAAI,OAAO,SAAS,KAAK;AACvBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAGD,qBAAW,MAAM;AACfA,0BAAAA,MAAA,MAAA,OAAA,iCAAY,oBAAoB;AAChC,iBAAK,gBAAe;AAAA,UACrB,GAAE,GAAI;AAAA,eACF;AACLA,8BAAc,MAAA,SAAA,iCAAA,aAAa,OAAO,WAAW,MAAM;AACnDA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,OAAO,WAAW;AAAA,YACzB,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,iCAAA,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,QACR,CAAC;AAAA,MACH,UAAU;AAERA,sBAAG,MAAC,YAAW;AAAA,MACjB;AAAA,IACD;AAAA;AAAA,IAGD,wBAAwB;AAEtB,UAAI,CAAC,KAAK,mBAAmB;AAC3B,aAAK,oBAAoB;AAAA,MAC3B;AAGA,YAAM,gBAAgB;AAAA,QACpB,gBAAgB;AAAA,QAChB,uBAAuB;AAAA,QACvB,yBAAyB;AAAA,QACzB,yBAAyB;AAAA,QACzB,iBAAiB;AAAA;AAInB,UAAI,CAAC,KAAK,wBAAwB,MAAM,WAAW,KAAK,oBAAoB,CAAC,GAAG;AAC9E,aAAK,uBAAuB,cAAc,KAAK,iBAAiB,KAAK;AACrEA,sBAAAA,MAAA,MAAA,OAAA,iCAAY,aAAa,KAAK,iBAAiB,UAAU,KAAK,oBAAoB,EAAE;AAAA,MACtF;AAAA,IACD;AAAA;AAAA,IAGD,wBAAwB;AACtB,UAAI,KAAK,aAAa,UAAU,CAAC,KAAK,kBAAkB,MAAM,KAAK,iBAAiB,EAAE,aAAa,KAAK,gBAAgB,WAAW,UAAU;AAC3I;AAAA,MACF;AAGA,UAAI,eAAe,WAAW,KAAK,iBAAiB,KAAK;AAEzD,UAAI,WAAW,KAAK,IAAI,IAAI,eAAe,GAAG;AAE9C,iBAAW,WAAW,SAAS,QAAQ,CAAC,CAAC;AACzCA,0BAAY,MAAA,OAAA,iCAAA,WAAW,YAAY,OAAO,QAAQ,EAAE;AAEpD,WAAK,oBAAoB;AAAA,IAC1B;AAAA;AAAA,IAGD,wBAAwB;AACtB,UAAI,KAAK,aAAa,UAAU,CAAC,KAAK,kBAAkB,MAAM,KAAK,iBAAiB,EAAE,aAAa,KAAK,gBAAgB,WAAW,UAAU;AAC3I;AAAA,MACF;AAGA,UAAI,eAAe,WAAW,KAAK,iBAAiB,KAAK;AAEzD,UAAI,WAAW,KAAK,IAAI,GAAG,eAAe,GAAG;AAE7C,iBAAW,WAAW,SAAS,QAAQ,CAAC,CAAC;AACzCA,0BAAY,MAAA,OAAA,iCAAA,WAAW,YAAY,OAAO,QAAQ,EAAE;AAEpD,WAAK,oBAAoB;AAAA,IAC1B;AAAA;AAAA,IAGD,eAAe;AACbA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACp6CA,GAAG,WAAW,eAAe;"}