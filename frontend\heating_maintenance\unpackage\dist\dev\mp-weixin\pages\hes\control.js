"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  components: {
    // 确保组件正确引入
  },
  data() {
    return {
      activeTab: "control",
      selectedStation: null,
      stationList: [],
      stationData: null,
      tempSliderValue: 80,
      pressureSliderValue: 60,
      loading: false,
      // 站点筛选条件
      stationFilter: {
        status: "all",
        keyword: ""
      },
      // 工作模式
      workMode: "auto",
      // 设备控制标签
      activeDeviceTab: "valve",
      // 设备控制数据
      deviceControlData: {
        valves: [
          { id: 1, name: "一次供水电动阀", isOpen: false, openDegree: 0 },
          { id: 2, name: "一次回水电动阀", isOpen: false, openDegree: 0 },
          { id: 3, name: "二次供水电动阀", isOpen: false, openDegree: 0 }
        ],
        pumps: [
          { id: 1, name: "一次循环泵", isRunning: false, frequency: 0 },
          { id: 2, name: "二次循环泵", isRunning: false, frequency: 0 }
        ]
      },
      // 当前选中的阀门和水泵索引
      selectedValveIndex: 0,
      selectedPumpIndex: 0,
      // 控制算法
      algorithms: [
        { value: "primary_flow", label: "一次流量控制算法" },
        { value: "primary_supply_temp", label: "一次供水温度控制算法" },
        { value: "secondary_supply_temp", label: "二次供水温度控制算法" },
        { value: "secondary_return_temp", label: "二次回水温度控制算法" },
        { value: "ai_prediction", label: "AI预算算法" }
      ],
      selectedAlgorithm: "primary_flow",
      algorithmTargetValue: 75,
      tempValveOpenDegree: 0,
      tempPumpFrequency: 0
    };
  },
  computed: {
    // 过滤后的站点列表
    filteredStations() {
      if (!this.stationList || this.stationList.length === 0)
        return [];
      return this.stationList.filter((station) => {
        if (this.stationFilter.status !== "all" && station.status !== this.stationFilter.status) {
          return false;
        }
        if (this.stationFilter.keyword && !station.name.includes(this.stationFilter.keyword)) {
          return false;
        }
        return true;
      });
    }
  },
  onReady() {
    if (this.$refs.stationPopup) {
      common_vendor.index.__f__("log", "at pages/hes/control.vue:494", "Popup component loaded");
    } else {
      common_vendor.index.__f__("error", "at pages/hes/control.vue:496", "Popup component not found");
    }
  },
  onLoad() {
    this.loadStationList();
    const cachedStation = common_vendor.index.getStorageSync("selectedStation");
    if (cachedStation) {
      try {
        this.selectedStation = JSON.parse(cachedStation);
        this.loadStationData();
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/hes/control.vue:510", "解析缓存站点数据失败", e);
      }
    }
    this.initAlgorithmDefaults();
  },
  onShow() {
    this.$nextTick(() => {
      if (!this.$refs.stationPopup) {
        setTimeout(() => {
          this.initPopupComponents();
        }, 300);
      }
      if (this.selectedStation && this.selectedStation.id) {
        common_vendor.index.__f__("log", "at pages/hes/control.vue:529", "页面显示，刷新站点数据");
        this.loadStationData();
      }
    });
  },
  methods: {
    // 初始化popup组件
    initPopupComponents() {
      if (!this.$refs.stationPopup) {
        common_vendor.index.__f__("error", "at pages/hes/control.vue:538", "Popup component not found, trying alternative access");
        common_vendor.index.showToast({
          title: "请重新点击选择换热站",
          icon: "none",
          duration: 2e3
        });
      }
    },
    // 加载换热站列表
    async loadStationList() {
      common_vendor.index.showLoading({
        title: "加载站点列表..."
      });
      try {
        const res = await utils_api.heatingStationApi.getList({
          "status": "",
          "keyword": ""
        });
        if (res.code === 200 && res.data && res.data.list) {
          this.stationList = res.data.list;
        } else {
          common_vendor.index.showToast({
            title: "获取站点列表失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/hes/control.vue:567", "获取站点列表出错:", error);
        common_vendor.index.showToast({
          title: "网络异常，请重试",
          icon: "none"
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    // 显示站点选择
    showStationSelect() {
      if (this.stationList.length === 0) {
        this.loadStationList().then(() => {
          this.openStationPopup();
        });
      } else {
        this.openStationPopup();
      }
    },
    // 打开站点选择弹窗
    openStationPopup() {
      this.stationFilter.status = "all";
      this.stationFilter.keyword = "";
      if (this.$refs.stationPopup) {
        this.$refs.stationPopup.open();
      } else {
        common_vendor.index.__f__("error", "at pages/hes/control.vue:598", "站点选择弹窗组件未找到");
        common_vendor.index.showToast({
          title: "组件加载失败，请重试",
          icon: "none"
        });
      }
    },
    // 关闭站点选择弹窗
    closeStationPopup() {
      if (this.$refs.stationPopup) {
        this.$refs.stationPopup.close();
      }
    },
    // 设置状态筛选
    setStatusFilter(status) {
      this.stationFilter.status = status;
    },
    // 搜索站点
    searchStations() {
      common_vendor.index.__f__("log", "at pages/hes/control.vue:620", "搜索关键词:", this.stationFilter.keyword);
    },
    // 处理站点选择
    handleSelectStation(station) {
      this.selectedStation = station;
      common_vendor.index.setStorageSync("selectedStation", JSON.stringify(station));
      this.loadStationData();
      this.closeStationPopup();
    },
    // 加载站点详细数据
    async loadStationData() {
      if (!this.selectedStation)
        return;
      common_vendor.index.showLoading({
        title: "加载站点数据..."
      });
      try {
        const res = await utils_api.heatingStationApi.getDetail(this.selectedStation.id);
        common_vendor.index.__f__("log", "at pages/hes/control.vue:643", "站点详细数据:", JSON.stringify(res));
        if (res.code === 200 && res.data) {
          this.stationData = res.data;
          if (this.stationData.realtime_data && this.stationData.realtime_data.primary_system) {
            const supplyTemp = this.stationData.realtime_data.primary_system.supply_temp;
            if (supplyTemp) {
              this.tempSliderValue = Math.max(70, Math.min(95, Math.round(supplyTemp)));
            }
            const supplyPressure = this.stationData.realtime_data.primary_system.supply_pressure;
            if (supplyPressure) {
              this.pressureSliderValue = Math.max(40, Math.min(80, Math.round(supplyPressure * 100)));
            }
          } else {
            common_vendor.index.__f__("warn", "at pages/hes/control.vue:662", "一次系统数据缺失，使用默认温度和压力值");
          }
          if (this.stationData.operation_mode) {
            common_vendor.index.__f__("log", "at pages/hes/control.vue:667", "服务器返回的工作模式:", this.stationData.operation_mode);
            this.workMode = this.stationData.operation_mode === "automatic" ? "auto" : "manual";
          } else {
            common_vendor.index.__f__("warn", "at pages/hes/control.vue:671", "未获取到工作模式信息，默认设为手动模式");
            this.workMode = "manual";
          }
          if (this.stationData.control_algorithm) {
            this.selectedAlgorithm = this.stationData.control_algorithm.type || "primary_flow";
            if (this.stationData.control_algorithm.target_value) {
              this.algorithmTargetValue = this.stationData.control_algorithm.target_value;
            }
          } else {
            common_vendor.index.__f__("warn", "at pages/hes/control.vue:682", "未获取到控制算法信息，使用默认算法");
            this.selectedAlgorithm = "primary_flow";
            this.algorithmTargetValue = 75;
          }
          if (this.stationData.realtime_data) {
            common_vendor.index.__f__("log", "at pages/hes/control.vue:689", "开始更新设备控制数据");
            this.updateDeviceControlData();
          } else {
            common_vendor.index.__f__("error", "at pages/hes/control.vue:692", "站点实时数据缺失，无法更新设备状态");
            this.resetControlParameters();
          }
        } else {
          common_vendor.index.__f__("error", "at pages/hes/control.vue:698", "获取站点数据响应异常:", res.message || "未知错误");
          common_vendor.index.showToast({
            title: "获取站点数据失败",
            icon: "none"
          });
          this.resetControlParameters();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/hes/control.vue:707", "获取站点数据出错:", error);
        common_vendor.index.showToast({
          title: "网络异常，请重试",
          icon: "none"
        });
        this.resetControlParameters();
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    // 重置控制参数到默认值
    resetControlParameters() {
      var _a, _b;
      try {
        this.tempValveOpenDegree = ((_a = this.deviceControlData.valves[this.selectedValveIndex]) == null ? void 0 : _a.openDegree) || 0;
        this.tempPumpFrequency = ((_b = this.deviceControlData.pumps[this.selectedPumpIndex]) == null ? void 0 : _b.frequency) || 0;
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/hes/control.vue:725", "重置控制参数失败:", e);
        this.tempValveOpenDegree = 0;
        this.tempPumpFrequency = 0;
        this.selectedValveIndex = 0;
        this.selectedPumpIndex = 0;
      }
      common_vendor.index.__f__("log", "at pages/hes/control.vue:733", "重置后的控制参数:", {
        tempValveOpenDegree: this.tempValveOpenDegree,
        tempPumpFrequency: this.tempPumpFrequency,
        selectedValveIndex: this.selectedValveIndex,
        selectedPumpIndex: this.selectedPumpIndex
      });
    },
    // 更新设备控制数据
    updateDeviceControlData() {
      common_vendor.index.__f__("log", "at pages/hes/control.vue:743", "更新设备控制数据", JSON.stringify(this.stationData));
      if (!this.stationData.realtime_data || !this.stationData.realtime_data.equipment_status) {
        common_vendor.index.__f__("error", "at pages/hes/control.vue:747", "设备数据缺失，使用默认设置");
        this.selectedValveIndex = Math.min(this.selectedValveIndex, this.deviceControlData.valves.length - 1);
        this.selectedPumpIndex = Math.min(this.selectedPumpIndex, this.deviceControlData.pumps.length - 1);
        this.tempValveOpenDegree = this.deviceControlData.valves[this.selectedValveIndex].openDegree || 0;
        this.tempPumpFrequency = this.deviceControlData.pumps[this.selectedPumpIndex].frequency || 0;
        return;
      }
      const equipStatus = this.stationData.realtime_data.equipment_status;
      common_vendor.index.__f__("log", "at pages/hes/control.vue:759", "设备状态数据:", JSON.stringify(equipStatus));
      if (equipStatus.valves && equipStatus.valves.length > 0) {
        common_vendor.index.__f__("log", "at pages/hes/control.vue:763", "更新阀门数据:", JSON.stringify(equipStatus.valves));
        this.deviceControlData.valves = equipStatus.valves.map((valve) => {
          return {
            id: valve.id,
            name: valve.name || `阀门${valve.id}`,
            isOpen: valve.status === "open",
            openDegree: valve.opening_degree || 0
          };
        });
        if (this.selectedValveIndex >= this.deviceControlData.valves.length) {
          this.selectedValveIndex = 0;
        }
      } else {
        common_vendor.index.__f__("warn", "at pages/hes/control.vue:778", "阀门数据缺失，保留默认数据");
      }
      if (this.deviceControlData.valves.length > 0) {
        if (this.deviceControlData.valves[this.selectedValveIndex]) {
          const currentDegree = this.deviceControlData.valves[this.selectedValveIndex].openDegree;
          common_vendor.index.__f__("log", "at pages/hes/control.vue:786", `阀门[${this.selectedValveIndex}]当前开度:`, currentDegree);
          this.tempValveOpenDegree = currentDegree;
        } else {
          common_vendor.index.__f__("warn", "at pages/hes/control.vue:789", "选中的阀门索引无效，重置为0");
          this.selectedValveIndex = 0;
          this.tempValveOpenDegree = this.deviceControlData.valves[0].openDegree || 0;
        }
      }
      if (equipStatus.pumps && equipStatus.pumps.length > 0) {
        common_vendor.index.__f__("log", "at pages/hes/control.vue:797", "更新水泵数据:", JSON.stringify(equipStatus.pumps));
        this.deviceControlData.pumps = equipStatus.pumps.map((pump) => {
          return {
            id: pump.id,
            name: pump.name || `水泵${pump.id}`,
            isRunning: pump.status === "running",
            frequency: pump.frequency || 0
          };
        });
        if (this.selectedPumpIndex >= this.deviceControlData.pumps.length) {
          this.selectedPumpIndex = 0;
        }
      } else {
        common_vendor.index.__f__("warn", "at pages/hes/control.vue:812", "水泵数据缺失，保留默认数据");
      }
      if (this.deviceControlData.pumps.length > 0) {
        if (this.deviceControlData.pumps[this.selectedPumpIndex]) {
          const currentFreq = this.deviceControlData.pumps[this.selectedPumpIndex].frequency;
          common_vendor.index.__f__("log", "at pages/hes/control.vue:820", `水泵[${this.selectedPumpIndex}]当前频率:`, currentFreq);
          this.tempPumpFrequency = currentFreq;
        } else {
          common_vendor.index.__f__("warn", "at pages/hes/control.vue:823", "选中的水泵索引无效，重置为0");
          this.selectedPumpIndex = 0;
          this.tempPumpFrequency = this.deviceControlData.pumps[0].frequency || 0;
        }
      }
      common_vendor.index.__f__("log", "at pages/hes/control.vue:830", `当前工作模式:${this.workMode}, 站点状态:${this.selectedStation.status}, 临时开度值:${this.tempValveOpenDegree}, 临时频率值:${this.tempPumpFrequency}`);
    },
    // 工作模式切换
    async handleWorkModeChange(mode) {
      common_vendor.index.__f__("log", "at pages/hes/control.vue:835", "尝试切换工作模式:", mode);
      if (this.workMode === mode || !this.selectedStation)
        return;
      if (this.selectedStation.status !== "online") {
        common_vendor.index.__f__("warn", "at pages/hes/control.vue:840", "站点不在线，无法切换工作模式");
        common_vendor.index.showToast({
          title: "站点不在线，无法切换模式",
          icon: "none"
        });
        return;
      }
      if (mode === "manual" && this.workMode === "auto") {
        common_vendor.index.showModal({
          title: "确认切换",
          content: "切换到手动模式后，控制算法将不再生效。是否继续？",
          success: (res) => {
            if (res.confirm) {
              this.doWorkModeChange(mode);
            }
          }
        });
      } else if (mode === "auto" && this.workMode === "manual") {
        common_vendor.index.showModal({
          title: "确认切换",
          content: "切换到自动模式后，系统将根据选择的控制算法自动调节设备。是否继续？",
          success: (res) => {
            if (res.confirm) {
              this.doWorkModeChange(mode);
            }
          }
        });
      }
    },
    // 执行工作模式切换
    async doWorkModeChange(mode) {
      try {
        common_vendor.index.showLoading({
          title: "正在切换模式...",
          mask: true
        });
        const params = {
          hes_id: this.selectedStation.id,
          mode: mode === "auto" ? "automatic" : "manual",
          operator_id: common_vendor.index.getStorageSync("userId") || 1
        };
        common_vendor.index.__f__("log", "at pages/hes/control.vue:889", "发送模式切换请求:", JSON.stringify(params));
        const result = await utils_api.heatingStationApi.setOperationMode(params);
        common_vendor.index.__f__("log", "at pages/hes/control.vue:891", "模式切换结果:", JSON.stringify(result));
        if (result.code === 200) {
          this.workMode = mode;
          let successMessage = "";
          if (mode === "auto") {
            successMessage = "已切换到自动模式，控制算法已生效";
          } else {
            successMessage = "已切换到手动模式，可手动控制设备";
          }
          common_vendor.index.showToast({
            title: successMessage,
            icon: "success",
            duration: 2e3
          });
          common_vendor.index.__f__("log", "at pages/hes/control.vue:912", `成功将工作模式切换为: ${mode}`);
          setTimeout(() => {
            common_vendor.index.__f__("log", "at pages/hes/control.vue:916", "模式切换成功，重新加载站点数据...");
            this.loadStationData();
          }, 1e3);
        } else {
          common_vendor.index.__f__("error", "at pages/hes/control.vue:920", "模式切换失败:", result.message || "未知错误");
          common_vendor.index.showToast({
            title: result.message || "模式切换失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/hes/control.vue:927", "模式切换异常:", error);
        common_vendor.index.showToast({
          title: error.message || "操作失败，请重试",
          icon: "none"
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    // 处理阀门选择
    handleValveSelect(e) {
      this.selectedValveIndex = e.detail.value;
      this.tempValveOpenDegree = this.deviceControlData.valves[this.selectedValveIndex].openDegree;
    },
    // 处理水泵选择
    handlePumpSelect(e) {
      this.selectedPumpIndex = e.detail.value;
      this.tempPumpFrequency = this.deviceControlData.pumps[this.selectedPumpIndex].frequency;
    },
    // 更新阀门开度
    updateTempValveOpenDegree(event) {
      const newValue = parseInt(event.detail.value) || 0;
      common_vendor.index.__f__("log", "at pages/hes/control.vue:956", `阀门开度临时值从 ${this.tempValveOpenDegree} 更新为 ${newValue}`);
      this.tempValveOpenDegree = newValue;
    },
    // 确认阀门设置
    confirmValveSettings() {
      const currentValve = this.deviceControlData.valves[this.selectedValveIndex];
      if (!currentValve) {
        common_vendor.index.__f__("error", "at pages/hes/control.vue:965", "当前选中的阀门不存在");
        common_vendor.index.showToast({
          title: "阀门数据错误",
          icon: "none"
        });
        return;
      }
      if (!currentValve.isOpen) {
        common_vendor.index.showToast({
          title: "请先打开阀门",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "确认设置",
        content: `确认将"${currentValve.name}"开度设置为${this.tempValveOpenDegree}%吗？`,
        success: async (res) => {
          if (res.confirm) {
            this.updateValveState(currentValve.id, { openDegree: this.tempValveOpenDegree });
            await this.sendValveControlCommand(currentValve.id);
          }
        }
      });
    },
    // 更新水泵频率
    updateTempPumpFrequency(event) {
      const newValue = parseInt(event.detail.value) || 0;
      common_vendor.index.__f__("log", "at pages/hes/control.vue:1000", `水泵频率临时值从 ${this.tempPumpFrequency} 更新为 ${newValue}`);
      this.tempPumpFrequency = newValue;
    },
    // 确认水泵设置
    confirmPumpSettings() {
      const currentPump = this.deviceControlData.pumps[this.selectedPumpIndex];
      if (!currentPump) {
        common_vendor.index.__f__("error", "at pages/hes/control.vue:1009", "当前选中的水泵不存在");
        common_vendor.index.showToast({
          title: "水泵数据错误",
          icon: "none"
        });
        return;
      }
      if (!currentPump.isRunning) {
        common_vendor.index.showToast({
          title: "请先启动水泵",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "确认设置",
        content: `确认将"${currentPump.name}"频率设置为${this.tempPumpFrequency}Hz吗？`,
        success: async (res) => {
          if (res.confirm) {
            this.updatePumpState(currentPump.id, { frequency: this.tempPumpFrequency });
            await this.sendPumpControlCommand(currentPump.id);
          }
        }
      });
    },
    // 阀门开关控制
    async handleValveSwitch(valveId, event) {
      const isOpen = event.detail.value;
      const valveIndex = this.deviceControlData.valves.findIndex((v) => v.id === valveId);
      if (valveIndex === -1) {
        common_vendor.index.__f__("error", "at pages/hes/control.vue:1047", "找不到ID为", valveId, "的阀门");
        common_vendor.index.showToast({
          title: "操作失败，设备数据错误",
          icon: "none"
        });
        return;
      }
      const valve = this.deviceControlData.valves[valveIndex];
      common_vendor.index.showModal({
        title: "确认操作",
        content: `确认${isOpen ? "打开" : "关闭"}"${valve.name}"吗？`,
        success: async (res) => {
          if (res.confirm) {
            this.updateValveState(valveId, { isOpen });
            if (!isOpen) {
              this.updateValveState(valveId, { openDegree: 0 });
              this.tempValveOpenDegree = 0;
            } else {
              this.tempValveOpenDegree = valve.openDegree;
            }
            await this.sendValveControlCommand(valveId);
          } else {
            this.$nextTick(() => {
              this.deviceControlData.valves[valveIndex].isOpen = !isOpen;
            });
          }
        }
      });
    },
    // 水泵开关控制
    async handlePumpSwitch(pumpId, event) {
      const isRunning = event.detail.value;
      const pumpIndex = this.deviceControlData.pumps.findIndex((p) => p.id === pumpId);
      if (pumpIndex === -1) {
        common_vendor.index.__f__("error", "at pages/hes/control.vue:1094", "找不到ID为", pumpId, "的水泵");
        common_vendor.index.showToast({
          title: "操作失败，设备数据错误",
          icon: "none"
        });
        return;
      }
      const pump = this.deviceControlData.pumps[pumpIndex];
      common_vendor.index.showModal({
        title: "确认操作",
        content: `确认${isRunning ? "启动" : "停止"}"${pump.name}"吗？`,
        success: async (res) => {
          if (res.confirm) {
            this.updatePumpState(pumpId, { isRunning });
            if (!isRunning) {
              this.updatePumpState(pumpId, { frequency: 0 });
              this.tempPumpFrequency = 0;
            } else {
              this.tempPumpFrequency = pump.frequency;
            }
            await this.sendPumpControlCommand(pumpId);
          } else {
            this.$nextTick(() => {
              this.deviceControlData.pumps[pumpIndex].isRunning = !isRunning;
            });
          }
        }
      });
    },
    // 更新阀门状态
    updateValveState(valveId, newState) {
      const valveIndex = this.deviceControlData.valves.findIndex((v) => v.id === valveId);
      if (valveIndex !== -1) {
        this.deviceControlData.valves[valveIndex] = {
          ...this.deviceControlData.valves[valveIndex],
          ...newState
        };
      }
    },
    // 更新水泵状态
    updatePumpState(pumpId, newState) {
      const pumpIndex = this.deviceControlData.pumps.findIndex((p) => p.id === pumpId);
      if (pumpIndex !== -1) {
        this.deviceControlData.pumps[pumpIndex] = {
          ...this.deviceControlData.pumps[pumpIndex],
          ...newState
        };
      }
    },
    // 发送阀门控制命令
    async sendValveControlCommand(valveId) {
      const valve = this.deviceControlData.valves.find((v) => v.id === valveId);
      if (!valve)
        return;
      try {
        const controlParams = {
          hes_id: this.selectedStation.id,
          control_type: "valve",
          device_id: valveId,
          action: valve.isOpen ? "open" : "close",
          value: valve.openDegree,
          // 阀门开度
          operator_id: common_vendor.index.getStorageSync("userId") || 1,
          remark: `${valve.isOpen ? "打开" : "关闭"}阀门，开度设为${valve.openDegree}%`
        };
        const result = await utils_api.heatingStationApi.controlDevice(controlParams);
        if (result.code === 200) {
          common_vendor.index.showToast({
            title: "阀门控制成功",
            icon: "success"
          });
        } else {
          common_vendor.index.showToast({
            title: result.message || "阀门控制失败",
            icon: "none"
          });
          this.loadStationData();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/hes/control.vue:1188", "阀门控制错误:", error);
        common_vendor.index.showToast({
          title: error.message || "操作失败，请重试",
          icon: "none"
        });
      }
    },
    // 发送水泵控制命令
    async sendPumpControlCommand(pumpId) {
      const pump = this.deviceControlData.pumps.find((p) => p.id === pumpId);
      if (!pump)
        return;
      try {
        const controlParams = {
          hes_id: this.selectedStation.id,
          control_type: "pump",
          device_id: pumpId,
          action: pump.isRunning ? "start" : "stop",
          value: pump.frequency,
          // 水泵频率
          operator_id: common_vendor.index.getStorageSync("userId") || 1,
          remark: `${pump.isRunning ? "启动" : "停止"}水泵，频率设为${pump.frequency}Hz`
        };
        const result = await utils_api.heatingStationApi.controlDevice(controlParams);
        if (result.code === 200) {
          common_vendor.index.showToast({
            title: "水泵控制成功",
            icon: "success"
          });
        } else {
          common_vendor.index.showToast({
            title: result.message || "水泵控制失败",
            icon: "none"
          });
          this.loadStationData();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/hes/control.vue:1228", "水泵控制错误:", error);
        common_vendor.index.showToast({
          title: error.message || "操作失败，请重试",
          icon: "none"
        });
      }
    },
    // 处理算法选择变更
    handleAlgorithmChange(e) {
      const oldAlgorithm = this.selectedAlgorithm;
      this.selectedAlgorithm = e.detail.value;
      common_vendor.index.__f__("log", "at pages/hes/control.vue:1240", `算法选择从 ${oldAlgorithm} 变更为 ${this.selectedAlgorithm}`);
      if (this.workMode === "manual") {
        common_vendor.index.showToast({
          title: "请切换到自动模式以应用算法",
          icon: "none",
          duration: 2e3
        });
      }
      if (this.selectedAlgorithm !== oldAlgorithm) {
        const defaultValues = {
          "primary_flow": 80,
          "primary_supply_temp": 85,
          "secondary_supply_temp": 60,
          "secondary_return_temp": 45,
          "ai_prediction": 75
        };
        if (defaultValues[this.selectedAlgorithm]) {
          this.algorithmTargetValue = defaultValues[this.selectedAlgorithm];
          common_vendor.index.__f__("log", "at pages/hes/control.vue:1263", `设置默认目标值: ${this.algorithmTargetValue} ${this.getAlgorithmUnit()}`);
        }
      }
    },
    // 获取算法描述
    getAlgorithmDescription() {
      const algoMap = {
        "primary_flow": "通过控制一次侧流量来调节换热效果",
        "primary_supply_temp": "通过调节一次供水温度来控制整体热量输出",
        "secondary_supply_temp": "通过精确控制二次供水温度来满足用户需求",
        "secondary_return_temp": "通过监控和调整二次回水温度优化系统效率",
        "ai_prediction": "利用AI技术预测负荷变化并提前调整系统参数"
      };
      return algoMap[this.selectedAlgorithm] || "请选择一种控制算法";
    },
    // 获取算法目标值单位
    getAlgorithmUnit() {
      const unitMap = {
        "primary_flow": "m³/h",
        "primary_supply_temp": "℃",
        "secondary_supply_temp": "℃",
        "secondary_return_temp": "℃",
        "ai_prediction": ""
      };
      return unitMap[this.selectedAlgorithm] || "";
    },
    // 应用算法设置
    async applyAlgorithmSettings() {
      if (!this.selectedStation || !this.selectedAlgorithm) {
        common_vendor.index.__f__("error", "at pages/hes/control.vue:1297", "缺少站点或算法信息");
        common_vendor.index.showToast({
          title: "请选择算法和设置目标值",
          icon: "none"
        });
        return;
      }
      if (this.selectedStation.status !== "online") {
        common_vendor.index.__f__("warn", "at pages/hes/control.vue:1307", "站点不在线，无法应用算法设置");
        common_vendor.index.showToast({
          title: "站点不在线，无法设置算法",
          icon: "none"
        });
        return;
      }
      if (this.workMode !== "auto") {
        common_vendor.index.__f__("warn", "at pages/hes/control.vue:1317", "当前为手动模式，无法应用算法设置");
        common_vendor.index.showToast({
          title: "自动模式下才能应用算法",
          icon: "none"
        });
        return;
      }
      const targetValue = parseFloat(this.algorithmTargetValue);
      if (isNaN(targetValue) || targetValue <= 0) {
        common_vendor.index.__f__("error", "at pages/hes/control.vue:1328", "目标值无效:", this.algorithmTargetValue);
        common_vendor.index.showToast({
          title: "请输入有效的目标值",
          icon: "none"
        });
        return;
      }
      try {
        common_vendor.index.showLoading({
          title: "正在应用算法设置...",
          mask: true
        });
        const params = {
          hes_id: this.selectedStation.id,
          algorithm_type: this.selectedAlgorithm,
          target_value: targetValue,
          operator_id: common_vendor.index.getStorageSync("userId") || 1
        };
        common_vendor.index.__f__("log", "at pages/hes/control.vue:1350", "发送算法设置请求:", JSON.stringify(params));
        const result = await utils_api.heatingStationApi.setControlAlgorithm(params);
        common_vendor.index.__f__("log", "at pages/hes/control.vue:1352", "算法设置结果:", JSON.stringify(result));
        if (result.code === 200) {
          common_vendor.index.showToast({
            title: "算法设置成功",
            icon: "success"
          });
          setTimeout(() => {
            common_vendor.index.__f__("log", "at pages/hes/control.vue:1362", "算法设置成功，重新加载站点数据...");
            this.loadStationData();
          }, 1e3);
        } else {
          common_vendor.index.__f__("error", "at pages/hes/control.vue:1366", "应用算法设置失败:", result.message || "未知错误");
          common_vendor.index.showToast({
            title: result.message || "算法设置失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/hes/control.vue:1373", "算法设置异常:", error);
        common_vendor.index.showToast({
          title: error.message || "操作失败，请重试",
          icon: "none"
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    // 初始化算法默认值
    initAlgorithmDefaults() {
      if (!this.selectedAlgorithm) {
        this.selectedAlgorithm = "primary_flow";
      }
      const defaultValues = {
        "primary_flow": 80,
        "primary_supply_temp": 85,
        "secondary_supply_temp": 60,
        "secondary_return_temp": 45,
        "ai_prediction": 75
      };
      if (!this.algorithmTargetValue || isNaN(parseFloat(this.algorithmTargetValue))) {
        this.algorithmTargetValue = defaultValues[this.selectedAlgorithm] || 75;
        common_vendor.index.__f__("log", "at pages/hes/control.vue:1403", `初始化算法默认值: ${this.selectedAlgorithm}, 目标值: ${this.algorithmTargetValue}`);
      }
    },
    // 增加频率方法
    increasePumpFrequency() {
      if (this.workMode === "auto" || !this.deviceControlData.pumps[this.selectedPumpIndex].isRunning || this.selectedStation.status !== "online") {
        return;
      }
      let currentValue = parseFloat(this.tempPumpFrequency) || 0;
      let newValue = Math.min(50, currentValue + 0.5);
      newValue = parseFloat(newValue.toFixed(1));
      common_vendor.index.__f__("log", "at pages/hes/control.vue:1419", `增加水泵频率: ${currentValue} -> ${newValue}`);
      this.tempPumpFrequency = newValue;
    },
    // 执行水泵频率减少
    decreasePumpFrequency() {
      if (this.workMode === "auto" || !this.deviceControlData.pumps[this.selectedPumpIndex].isRunning || this.selectedStation.status !== "online") {
        return;
      }
      let currentValue = parseFloat(this.tempPumpFrequency) || 0;
      let newValue = Math.max(0, currentValue - 0.5);
      newValue = parseFloat(newValue.toFixed(1));
      common_vendor.index.__f__("log", "at pages/hes/control.vue:1436", `减少水泵频率: ${currentValue} -> ${newValue}`);
      this.tempPumpFrequency = newValue;
    },
    // 返回上一页
    navigateBack() {
      common_vendor.index.navigateBack();
    }
  }
};
if (!Array) {
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  _easycom_uni_popup2();
}
const _easycom_uni_popup = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a, _b, _c, _d, _e, _f, _g, _h;
  return common_vendor.e({
    a: $data.activeTab === "control" ? 1 : "",
    b: common_vendor.o(($event) => $data.activeTab = "control"),
    c: $data.activeTab === "process" ? 1 : "",
    d: common_vendor.o(($event) => $data.activeTab = "process"),
    e: common_vendor.t($data.selectedStation ? $data.selectedStation.name : "请选择换热站"),
    f: !$data.selectedStation ? 1 : "",
    g: common_vendor.o((...args) => $options.showStationSelect && $options.showStationSelect(...args)),
    h: $data.selectedStation
  }, $data.selectedStation ? common_vendor.e({
    i: $data.activeTab === "control"
  }, $data.activeTab === "control" ? common_vendor.e({
    j: common_vendor.t($data.selectedStation.name),
    k: common_vendor.t($data.selectedStation.address || $data.selectedStation.heat_unit_name),
    l: common_vendor.t($data.selectedStation.status === "online" ? "在线" : $data.selectedStation.status === "fault" ? "故障" : "离线"),
    m: common_vendor.n($data.selectedStation.status),
    n: $data.stationData
  }, $data.stationData ? {
    o: common_vendor.t(((_b = (_a = $data.stationData.realtime_data) == null ? void 0 : _a.primary_system) == null ? void 0 : _b.supply_temp) || "-"),
    p: common_vendor.t(((_d = (_c = $data.stationData.realtime_data) == null ? void 0 : _c.primary_system) == null ? void 0 : _d.return_temp) || "-"),
    q: common_vendor.t(((_f = (_e = $data.stationData.realtime_data) == null ? void 0 : _e.primary_system) == null ? void 0 : _f.supply_pressure) || "-"),
    r: common_vendor.t(((_h = (_g = $data.stationData.realtime_data) == null ? void 0 : _g.primary_system) == null ? void 0 : _h.return_pressure) || "-")
  } : {
    s: common_vendor.t($data.selectedStation.supply_temp || "-"),
    t: common_vendor.t($data.selectedStation.return_temp || "-"),
    v: common_vendor.t($data.selectedStation.supply_pressure || "-"),
    w: common_vendor.t($data.selectedStation.return_pressure || "-")
  }, {
    x: $data.workMode === "auto" ? 1 : "",
    y: common_vendor.o(($event) => $options.handleWorkModeChange("auto")),
    z: $data.workMode === "manual" ? 1 : "",
    A: common_vendor.o(($event) => $options.handleWorkModeChange("manual")),
    B: common_vendor.t($data.workMode === "auto" ? "自动模式: 系统将根据预设算法自动调节设备运行" : "手动模式: 您可以手动调节设备运行参数"),
    C: $data.activeDeviceTab === "valve" ? 1 : "",
    D: common_vendor.o(($event) => $data.activeDeviceTab = "valve"),
    E: $data.activeDeviceTab === "pump" ? 1 : "",
    F: common_vendor.o(($event) => $data.activeDeviceTab = "pump"),
    G: $data.activeDeviceTab === "valve"
  }, $data.activeDeviceTab === "valve" ? {
    H: common_vendor.t($data.deviceControlData.valves[$data.selectedValveIndex].name),
    I: $data.deviceControlData.valves[$data.selectedValveIndex].isOpen,
    J: common_vendor.o((e) => $options.handleValveSwitch($data.deviceControlData.valves[$data.selectedValveIndex].id, e)),
    K: $data.workMode === "auto" || $data.selectedStation.status !== "online",
    L: common_vendor.o((...args) => $options.handleValveSelect && $options.handleValveSelect(...args)),
    M: $data.selectedValveIndex,
    N: $data.deviceControlData.valves,
    O: $data.workMode === "auto" || $data.selectedStation.status !== "online",
    P: common_vendor.t($data.deviceControlData.valves[$data.selectedValveIndex].openDegree),
    Q: $data.tempValveOpenDegree,
    R: common_vendor.o((e) => $options.updateTempValveOpenDegree(e)),
    S: $data.workMode === "auto" || !$data.deviceControlData.valves[$data.selectedValveIndex].isOpen || $data.selectedStation.status !== "online",
    T: common_vendor.t($data.tempValveOpenDegree),
    U: common_vendor.o((...args) => $options.confirmValveSettings && $options.confirmValveSettings(...args)),
    V: $data.workMode === "auto" || !$data.deviceControlData.valves[$data.selectedValveIndex].isOpen || $data.selectedStation.status !== "online"
  } : {}, {
    W: $data.activeDeviceTab === "pump"
  }, $data.activeDeviceTab === "pump" ? {
    X: common_vendor.t($data.deviceControlData.pumps[$data.selectedPumpIndex].name),
    Y: $data.deviceControlData.pumps[$data.selectedPumpIndex].isRunning,
    Z: common_vendor.o((e) => $options.handlePumpSwitch($data.deviceControlData.pumps[$data.selectedPumpIndex].id, e)),
    aa: $data.workMode === "auto" || $data.selectedStation.status !== "online",
    ab: common_vendor.o((...args) => $options.handlePumpSelect && $options.handlePumpSelect(...args)),
    ac: $data.selectedPumpIndex,
    ad: $data.deviceControlData.pumps,
    ae: $data.workMode === "auto" || $data.selectedStation.status !== "online",
    af: common_vendor.t($data.deviceControlData.pumps[$data.selectedPumpIndex].frequency),
    ag: $data.workMode === "auto" || !$data.deviceControlData.pumps[$data.selectedPumpIndex].isRunning || $data.selectedStation.status !== "online",
    ah: $data.tempPumpFrequency,
    ai: common_vendor.o(($event) => $data.tempPumpFrequency = $event.detail.value),
    aj: common_vendor.o((...args) => $options.increasePumpFrequency && $options.increasePumpFrequency(...args)),
    ak: $data.workMode === "auto" || !$data.deviceControlData.pumps[$data.selectedPumpIndex].isRunning || $data.selectedStation.status !== "online" ? 1 : "",
    al: common_vendor.o((...args) => $options.decreasePumpFrequency && $options.decreasePumpFrequency(...args)),
    am: $data.workMode === "auto" || !$data.deviceControlData.pumps[$data.selectedPumpIndex].isRunning || $data.selectedStation.status !== "online" ? 1 : "",
    an: $data.workMode === "auto" || !$data.deviceControlData.pumps[$data.selectedPumpIndex].isRunning || $data.selectedStation.status !== "online" ? 1 : "",
    ao: common_vendor.o((...args) => $options.confirmPumpSettings && $options.confirmPumpSettings(...args)),
    ap: $data.workMode === "auto" || !$data.deviceControlData.pumps[$data.selectedPumpIndex].isRunning || $data.selectedStation.status !== "online"
  } : {}, {
    aq: $data.workMode === "manual"
  }, $data.workMode === "manual" ? {} : {}, {
    ar: $data.selectedStation.status !== "online"
  }, $data.selectedStation.status !== "online" ? {} : {}, {
    as: common_vendor.f($data.algorithms, (algo, index, i0) => {
      return {
        a: algo.value,
        b: $data.selectedAlgorithm === algo.value,
        c: common_vendor.t(algo.label),
        d: index
      };
    }),
    at: $data.selectedStation.status !== "online",
    av: common_vendor.o((...args) => $options.handleAlgorithmChange && $options.handleAlgorithmChange(...args)),
    aw: common_vendor.t($options.getAlgorithmDescription()),
    ax: $data.workMode === "manual" || $data.selectedStation.status !== "online",
    ay: $data.algorithmTargetValue,
    az: common_vendor.o(($event) => $data.algorithmTargetValue = $event.detail.value),
    aA: common_vendor.t($options.getAlgorithmUnit()),
    aB: common_vendor.o((...args) => $options.applyAlgorithmSettings && $options.applyAlgorithmSettings(...args)),
    aC: $data.workMode === "manual" || $data.selectedStation.status !== "online",
    aD: $data.workMode === "manual"
  }, $data.workMode === "manual" ? {} : {}) : $data.activeTab === "process" ? {
    aF: common_assets._imports_0$2
  } : {}, {
    aE: $data.activeTab === "process"
  }) : {}, {
    aG: common_vendor.o((...args) => $options.closeStationPopup && $options.closeStationPopup(...args)),
    aH: common_vendor.o((...args) => $options.searchStations && $options.searchStations(...args)),
    aI: $data.stationFilter.keyword,
    aJ: common_vendor.o(($event) => $data.stationFilter.keyword = $event.detail.value),
    aK: $data.stationFilter.keyword
  }, $data.stationFilter.keyword ? {
    aL: common_vendor.o(($event) => $data.stationFilter.keyword = "")
  } : {}, {
    aM: $data.stationFilter.status === "all" ? 1 : "",
    aN: common_vendor.o(($event) => $options.setStatusFilter("all")),
    aO: $data.stationFilter.status === "online" ? 1 : "",
    aP: common_vendor.o(($event) => $options.setStatusFilter("online")),
    aQ: $data.stationFilter.status === "fault" ? 1 : "",
    aR: common_vendor.o(($event) => $options.setStatusFilter("fault")),
    aS: $data.stationFilter.status === "offline" ? 1 : "",
    aT: common_vendor.o(($event) => $options.setStatusFilter("offline")),
    aU: $options.filteredStations.length === 0
  }, $options.filteredStations.length === 0 ? {} : {}, {
    aV: common_vendor.f($options.filteredStations, (station, k0, i0) => {
      return {
        a: common_vendor.t(station.name),
        b: common_vendor.t(station.heat_unit_name),
        c: common_vendor.t(station.supply_temp ? station.supply_temp.toFixed(2) + "℃" : "-"),
        d: common_vendor.t(station.return_temp ? station.return_temp.toFixed(2) + "℃" : "-"),
        e: common_vendor.t(station.status === "online" ? "在线" : station.status === "fault" ? "故障" : "离线"),
        f: common_vendor.n(station.status),
        g: station.id,
        h: common_vendor.o(($event) => $options.handleSelectStation(station), station.id)
      };
    }),
    aW: common_vendor.sr("stationPopup", "4e9888be-0"),
    aX: common_vendor.p({
      type: "bottom",
      ["background-color"]: "#f5f5f5"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/hes/control.js.map
