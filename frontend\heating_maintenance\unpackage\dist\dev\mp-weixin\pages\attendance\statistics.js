"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      currentYear: (/* @__PURE__ */ new Date()).getFullYear(),
      currentMonth: (/* @__PURE__ */ new Date()).getMonth() + 1,
      weekDays: ["日", "一", "二", "三", "四", "五", "六"],
      calendarDays: [],
      selectedStaff: null,
      searchKeyword: "",
      staffList: [],
      isLoading: false,
      statistics: {
        normalDays: 0,
        lateDays: 0,
        earlyDays: 0,
        absentDays: 0
      },
      records: [],
      selectedDay: null,
      dayDetailRecord: null,
      exportForm: {
        startDate: "",
        endDate: "",
        type: "all",
        format: "excel"
      },
      selectedExportStaff: [],
      multiSearchKeyword: "",
      currentPickerType: "start",
      exportLoading: false,
      dayRecords: [],
      dayStats: {
        normal: 0,
        late: 0,
        early: 0,
        absent: 0
      },
      selectedDate: "",
      dayDetailPopupVisible: false,
      dayDetailData: [],
      selectedStaffDetail: null
    };
  },
  computed: {
    filteredStaffList() {
      if (!this.searchKeyword)
        return this.staffList;
      return this.staffList.filter(
        (staff) => {
          var _a;
          return staff.name.includes(this.searchKeyword) || ((_a = staff.department) == null ? void 0 : _a.includes(this.searchKeyword));
        }
      );
    },
    filteredMultiStaffList() {
      if (!this.multiSearchKeyword)
        return this.staffList;
      return this.staffList.filter(
        (staff) => {
          var _a;
          return staff.name.includes(this.multiSearchKeyword) || ((_a = staff.department) == null ? void 0 : _a.includes(this.multiSearchKeyword));
        }
      );
    },
    normalCount() {
      return this.dayDetailData.filter((item) => !item.isLate && !item.isEarly && !item.isAbsent).length;
    },
    lateCount() {
      return this.dayDetailData.filter((item) => item.isLate).length;
    },
    earlyCount() {
      return this.dayDetailData.filter((item) => item.isEarly).length;
    },
    absentCount() {
      return this.dayDetailData.filter((item) => item.isAbsent).length;
    },
    normalStaff() {
      return this.dayDetailData.filter((item) => !item.isLate && !item.isEarly && !item.isAbsent);
    },
    lateStaff() {
      return this.dayDetailData.filter((item) => item.isLate);
    },
    earlyStaff() {
      return this.dayDetailData.filter((item) => item.isEarly);
    },
    absentStaff() {
      return this.dayDetailData.filter((item) => item.isAbsent);
    }
  },
  onLoad() {
    this.generateCalendar();
    this.loadStaffList();
    this.loadAttendanceData();
  },
  methods: {
    // 生成日历数据
    generateCalendar() {
      const year = this.currentYear;
      const month = this.currentMonth;
      const firstDay = new Date(year, month - 1, 1).getDay();
      const daysInMonth = new Date(year, month, 0).getDate();
      const daysInPrevMonth = new Date(year, month - 1, 0).getDate();
      const days = [];
      for (let i = firstDay - 1; i >= 0; i--) {
        const prevMonth = month === 1 ? 12 : month - 1;
        const prevYear = month === 1 ? year - 1 : year;
        days.push({
          day: daysInPrevMonth - i,
          month: prevMonth,
          year: prevYear,
          isCurrentMonth: false,
          isToday: false,
          status: null
        });
      }
      const today = /* @__PURE__ */ new Date();
      const todayDate = today.getDate();
      const todayMonth = today.getMonth() + 1;
      const todayYear = today.getFullYear();
      for (let i = 1; i <= daysInMonth; i++) {
        days.push({
          day: i,
          month,
          year,
          isCurrentMonth: true,
          isToday: i === todayDate && month === todayMonth && year === todayYear,
          status: null
          // 初始无状态，将在加载数据后更新
        });
      }
      const remainingDays = 42 - days.length;
      for (let i = 1; i <= remainingDays; i++) {
        const nextMonth = month === 12 ? 1 : month + 1;
        const nextYear = month === 12 ? year + 1 : year;
        days.push({
          day: i,
          month: nextMonth,
          year: nextYear,
          isCurrentMonth: false,
          isToday: false,
          status: null
        });
      }
      this.calendarDays = days;
    },
    // 格式化日期为MM-DD格式
    formatDate(dateString) {
      if (!dateString)
        return "";
      const date = new Date(dateString);
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${month}-${day}`;
    },
    // 格式化星期为"周X"格式
    formatWeek(dateString) {
      if (!dateString)
        return "";
      const date = new Date(dateString);
      const weekDays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
      return weekDays[date.getDay()];
    },
    // 格式化位置信息
    formatLocation(record) {
      if (!record || !record.latitude || !record.longitude)
        return "未知位置";
      return `${record.latitude.toFixed(6)}, ${record.longitude.toFixed(6)}`;
    },
    // 加载员工列表
    loadStaffList() {
      utils_api.attendanceApi.getAllStaff().then((res) => {
        if (res.code === 200 && res.data) {
          if (res.data.data && Array.isArray(res.data.data.staffList)) {
            this.staffList = res.data.data.staffList;
          } else if (Array.isArray(res.data.staffList)) {
            this.staffList = res.data.staffList;
          } else if (Array.isArray(res.data)) {
            this.staffList = res.data;
          } else {
            common_vendor.index.__f__("warn", "at pages/attendance/statistics.vue:588", "员工列表数据格式异常:", res);
            this.loadInspectorList();
          }
        } else {
          this.loadInspectorList();
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/attendance/statistics.vue:597", "获取人员列表失败:", err);
        this.loadInspectorList();
      });
    },
    // 从用户API加载检查员列表（降级方案）
    loadInspectorList() {
      utils_api.userApi.getInspectorList().then((userRes) => {
        if (userRes.code === 200 && userRes.data) {
          if (Array.isArray(userRes.data)) {
            this.staffList = userRes.data;
          } else if (userRes.data.data && Array.isArray(userRes.data.data.staffList)) {
            this.staffList = userRes.data.data.staffList;
          } else {
            common_vendor.index.__f__("warn", "at pages/attendance/statistics.vue:612", "无法解析人员列表数据");
            this.staffList = [];
          }
        }
      }).catch((userErr) => {
        common_vendor.index.__f__("error", "at pages/attendance/statistics.vue:617", "获取人员列表失败:", userErr);
        common_vendor.index.showToast({
          title: "获取人员列表失败",
          icon: "none"
        });
      });
    },
    // 上个月
    prevMonth() {
      if (this.currentMonth === 1) {
        this.currentYear -= 1;
        this.currentMonth = 12;
      } else {
        this.currentMonth -= 1;
      }
      this.generateCalendar();
      this.loadAttendanceData();
    },
    // 下个月
    nextMonth() {
      if (this.currentMonth === 12) {
        this.currentYear += 1;
        this.currentMonth = 1;
      } else {
        this.currentMonth += 1;
      }
      this.generateCalendar();
      this.loadAttendanceData();
    },
    // 打开人员选择器
    openStaffSelector() {
      this.$refs.staffPopup.open();
    },
    // 关闭人员选择器
    closeStaffSelector() {
      this.$refs.staffPopup.close();
    },
    // 选择人员
    selectStaff(staff) {
      this.selectedStaff = staff;
      this.closeStaffSelector();
      this.loadAttendanceData();
    },
    // 加载考勤数据
    loadAttendanceData() {
      this.isLoading = true;
      common_vendor.index.showLoading({
        title: "加载数据中..."
      });
      const params = {
        year: this.currentYear,
        month: this.currentMonth,
        day: this.selectedDay ? this.selectedDay.day : void 0
      };
      if (this.selectedStaff) {
        params.userId = this.selectedStaff.id;
      }
      utils_api.attendanceApi.getStats(params).then((res) => {
        if (res.code === 200 && res.data) {
          let summaryData = res.data;
          if (res.data.data && typeof res.data.data === "object") {
            summaryData = res.data.data;
          }
          const summary = summaryData.summary || {};
          this.statistics = {
            normalDays: summary.normal || 0,
            lateDays: summary.late || 0,
            earlyDays: summary.early || 0,
            absentDays: summary.absent || 0
          };
          setTimeout(() => {
            this.initBarChart();
            this.initPieChart();
          }, 500);
          if (summaryData.chart) {
            this.updateCalendarStatus(summaryData.chart);
          }
        }
        return utils_api.attendanceApi.getRecords(params);
      }).then((res) => {
        if (res.code === 200) {
          let recordsData = res.data;
          if (res.data && res.data.data) {
            recordsData = res.data.data;
          }
          if (recordsData && recordsData.records) {
            this.records = this.processAttendanceRecords(recordsData.records);
          } else if (recordsData && recordsData.list) {
            this.records = this.processAttendanceRecords(recordsData.list);
          } else if (Array.isArray(recordsData)) {
            this.records = this.processAttendanceRecords(recordsData);
          } else if (recordsData && typeof recordsData === "object" && !Array.isArray(recordsData)) {
            this.records = this.processAttendanceRecords(recordsData);
          } else {
            common_vendor.index.__f__("warn", "at pages/attendance/statistics.vue:741", "未能识别的考勤记录数据格式:", res.data);
            this.records = [];
          }
        } else {
          this.records = [];
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/attendance/statistics.vue:748", "获取考勤数据失败:", err);
        common_vendor.index.showToast({
          title: "获取考勤数据失败",
          icon: "none"
        });
      }).finally(() => {
        this.isLoading = false;
        common_vendor.index.hideLoading();
      });
    },
    // 处理考勤记录数据
    processAttendanceRecords(records) {
      if (!Array.isArray(records) || records.length === 0) {
        return [];
      }
      if (!Array.isArray(records) && typeof records === "object") {
        const recordArray = [];
        for (const date in records) {
          if (records.hasOwnProperty(date)) {
            const dateObj = new Date(date);
            const month = String(dateObj.getMonth() + 1).padStart(2, "0");
            const day = String(dateObj.getDate()).padStart(2, "0");
            const weekDays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
            const week = weekDays[dateObj.getDay()];
            const record = records[date];
            recordArray.push({
              date,
              day: `${month}-${day}`,
              week,
              clockInTime: record.clockInTime || "未打卡",
              clockInStatus: record.clockInStatus || "normal",
              clockOutTime: record.clockOutTime || "未打卡",
              clockOutStatus: record.clockOutStatus || "normal",
              location: record.location || "未知位置"
            });
          }
        }
        return recordArray.slice(0, 5);
      }
      const firstRecord = records[0];
      if (firstRecord.clockInTime !== void 0 || firstRecord.clockOutTime !== void 0) {
        return records.map((item) => {
          return {
            date: item.date || this.formatDate(item.clockTime),
            week: item.week || this.formatWeek(item.clockTime),
            clockInTime: item.clockInTime || "未打卡",
            clockOutTime: item.clockOutTime || "未打卡",
            clockInStatus: item.clockInStatus || "normal",
            clockOutStatus: item.clockOutStatus || "normal",
            location: item.location || this.formatLocation(item)
          };
        }).slice(0, 5);
      } else if (firstRecord.clockType && firstRecord.clockTime) {
        const recordsByDate = {};
        records.forEach((record) => {
          const date = new Date(record.clockTime);
          const dateStr = date.toISOString().split("T")[0];
          if (!recordsByDate[dateStr]) {
            recordsByDate[dateStr] = {
              date: this.formatDate(record.clockTime),
              week: this.formatWeek(record.clockTime),
              clockInTime: "",
              clockOutTime: "",
              clockInStatus: "normal",
              clockOutStatus: "normal",
              location: record.location || this.formatLocation(record)
            };
          }
          if (record.clockType === "checkin") {
            recordsByDate[dateStr].clockInTime = this.formatTime(record.clockTime);
            recordsByDate[dateStr].clockInStatus = record.status;
          } else if (record.clockType === "checkout") {
            recordsByDate[dateStr].clockOutTime = this.formatTime(record.clockTime);
            recordsByDate[dateStr].clockOutStatus = record.status;
          }
        });
        return Object.values(recordsByDate).sort((a, b) => new Date(b.date) - new Date(a.date)).slice(0, 5);
      } else {
        common_vendor.index.__f__("warn", "at pages/attendance/statistics.vue:852", "未知的考勤记录数据格式，尝试基本处理:", firstRecord);
        return records.map((record) => {
          return {
            date: record.date || this.formatDate(record.clockTime || record.createTime),
            week: record.week || this.formatWeek(record.clockTime || record.createTime),
            clockInTime: record.clockInTime || "未打卡",
            clockOutTime: record.clockOutTime || "未打卡",
            clockInStatus: record.clockInStatus || "normal",
            clockOutStatus: record.clockOutStatus || "normal",
            location: record.location || this.formatLocation(record)
          };
        }).slice(0, 5);
      }
    },
    // 更新日历状态
    updateCalendarStatus(chartData) {
      if (!chartData || !chartData.xaxis)
        return;
      if (chartData.dailyStatus && Array.isArray(chartData.dailyStatus)) {
        this.calendarDays.forEach((day) => {
          if (day.isCurrentMonth) {
            const dayIndex = day.day - 1;
            if (dayIndex >= 0 && dayIndex < chartData.dailyStatus.length) {
              day.status = chartData.dailyStatus[dayIndex] || null;
            }
          }
        });
        return;
      }
      if (!chartData.series)
        return;
      const attendanceSeries = chartData.series.find((s) => s.name === "出勤率" || s.name === "attendance");
      const lateSeries = chartData.series.find((s) => s.name === "迟到率" || s.name === "late");
      const absentSeries = chartData.series.find((s) => s.name === "缺勤率" || s.name === "absent");
      const earlySeries = chartData.series.find((s) => s.name === "早退率" || s.name === "early");
      if (!attendanceSeries && !lateSeries && !absentSeries && !earlySeries)
        return;
      this.calendarDays.forEach((day) => {
        if (day.isCurrentMonth) {
          const dayIndex = day.day - 1;
          if (dayIndex >= 0 && dayIndex < chartData.xaxis.length) {
            if (absentSeries && absentSeries.data[dayIndex] > 0) {
              day.status = "absent";
            } else if (lateSeries && lateSeries.data[dayIndex] > 0) {
              day.status = "late";
            } else if (earlySeries && earlySeries.data[dayIndex] > 0) {
              day.status = "early";
            } else if (attendanceSeries && attendanceSeries.data[dayIndex] > 0) {
              day.status = "normal";
            } else {
              day.status = null;
            }
          }
        }
      });
    },
    // 格式化时间
    formatTime(timeString) {
      if (!timeString)
        return "未打卡";
      try {
        const date = new Date(timeString);
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        return `${hours}:${minutes}`;
      } catch (e) {
        return timeString;
      }
    },
    // 查看某天的详细考勤记录
    viewDayDetail(day) {
      if (!day.isCurrentMonth)
        return;
      this.selectedDay = day;
      this.selectedDate = `${day.year}年${day.month}月${day.day}日`;
      const params = {
        year: day.year,
        month: day.month,
        day: day.day
        // 不传userId表示获取所有人员
      };
      this.isLoading = true;
      common_vendor.index.showLoading({
        title: "加载数据中..."
      });
      utils_api.attendanceApi.getRecords(params).then((res) => {
        if (res.code === 200 && res.data) {
          let records = [];
          if (res.data.records) {
            records = res.data.records;
          } else if (res.data.list) {
            records = res.data.list;
          } else if (Array.isArray(res.data)) {
            records = res.data;
          } else if (res.data.data && (res.data.data.records || res.data.data.list)) {
            records = res.data.data.records || res.data.data.list;
          } else if (typeof res.data === "object" && !Array.isArray(res.data)) {
            const recordsData = res.data;
            records = [];
            for (const key in recordsData) {
              if (recordsData.hasOwnProperty(key)) {
                const record = recordsData[key];
                if (record && typeof record === "object") {
                  record.userId = record.userId || key;
                  record.userName = record.userName || record.name || "未知";
                  records.push(record);
                }
              }
            }
          }
          this.dayRecords = records;
          const userClockMap = {};
          records.forEach((record) => {
            var _a, _b, _c;
            const userId = ((_a = record.user) == null ? void 0 : _a.id) || record.userId || record.id || "";
            const userName = ((_b = record.user) == null ? void 0 : _b.name) || record.userName || record.staffName || record.name || "未知";
            if (!userClockMap[userId]) {
              userClockMap[userId] = {
                id: userId,
                name: userName,
                department: ((_c = record.user) == null ? void 0 : _c.department) || record.department || "",
                checkInTime: "",
                checkOutTime: "",
                isLate: false,
                isEarly: false,
                isAbsent: false,
                workHours: "--"
              };
            }
            if (record.clockType === "checkin") {
              userClockMap[userId].checkInTime = this.formatTime(record.clockTime);
              userClockMap[userId].isLate = record.status === "late";
            } else if (record.clockType === "checkout") {
              userClockMap[userId].checkOutTime = this.formatTime(record.clockTime);
              userClockMap[userId].isEarly = record.status === "early";
            } else if (record.checkInTime || record.checkOutTime) {
              if (record.checkInTime) {
                userClockMap[userId].checkInTime = this.formatTime(record.checkInTime);
                userClockMap[userId].isLate = record.clockInStatus === "late";
              }
              if (record.checkOutTime) {
                userClockMap[userId].checkOutTime = this.formatTime(record.checkOutTime);
                userClockMap[userId].isEarly = record.clockOutStatus === "early";
              }
            }
            if (record.status === "absent" || record.clockInStatus === "absent" && record.clockOutStatus === "absent") {
              userClockMap[userId].isAbsent = true;
            }
          });
          Object.values(userClockMap).forEach((user) => {
            if (user.checkInTime && user.checkOutTime && user.checkInTime !== "未打卡" && user.checkOutTime !== "未打卡") {
              const checkInRecord = records.find(
                (r) => {
                  var _a;
                  return (((_a = r.user) == null ? void 0 : _a.id) === user.id || r.userId === user.id) && r.clockType === "checkin";
                }
              );
              const checkOutRecord = records.find(
                (r) => {
                  var _a;
                  return (((_a = r.user) == null ? void 0 : _a.id) === user.id || r.userId === user.id) && r.clockType === "checkout";
                }
              );
              if (checkInRecord && checkOutRecord) {
                user.workHours = this.calculateWorkHours(checkInRecord.clockTime, checkOutRecord.clockTime);
              } else if (records.find((r) => r.userId === user.id && r.checkInTime && r.checkOutTime)) {
                const record = records.find((r) => r.userId === user.id);
                user.workHours = this.calculateWorkHours(record.checkInTime, record.checkOutTime);
              }
            }
          });
          if (this.staffList && this.staffList.length > 0) {
            this.staffList.forEach((staff) => {
              const userId = staff.id;
              if (userId && !userClockMap[userId]) {
                userClockMap[userId] = {
                  id: userId,
                  name: staff.name || "未知",
                  department: staff.department || "",
                  checkInTime: "",
                  checkOutTime: "",
                  isLate: false,
                  isEarly: false,
                  isAbsent: true,
                  workHours: "--"
                };
              }
            });
          }
          this.dayDetailData = Object.values(userClockMap);
          this.dayStats = {
            normal: this.dayDetailData.filter((item) => !item.isLate && !item.isEarly && !item.isAbsent).length,
            late: this.dayDetailData.filter((item) => item.isLate).length,
            early: this.dayDetailData.filter((item) => item.isEarly).length,
            absent: this.dayDetailData.filter((item) => item.isAbsent).length
          };
          setTimeout(() => {
            this.renderDayPieChart();
          }, 300);
          if (this.selectedStaff) {
            const staffRecord = this.dayDetailData.find(
              (r) => r.id == this.selectedStaff.id
            );
            if (staffRecord) {
              this.selectedStaffDetail = staffRecord;
            } else {
              this.selectedStaffDetail = null;
            }
          } else {
            this.selectedStaffDetail = null;
          }
        } else {
          this.dayRecords = [];
          this.dayDetailData = [];
          this.dayStats = {
            normal: 0,
            late: 0,
            early: 0,
            absent: 0
          };
          this.selectedStaffDetail = null;
        }
        this.$refs.dayDetailPopup.open();
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/attendance/statistics.vue:1128", "获取考勤详情失败:", err);
        this.dayRecords = [];
        this.dayDetailData = [];
        this.dayStats = {
          normal: 0,
          late: 0,
          early: 0,
          absent: 0
        };
        this.selectedStaffDetail = null;
        this.$refs.dayDetailPopup.open();
      }).finally(() => {
        this.isLoading = false;
        common_vendor.index.hideLoading();
      });
    },
    // 渲染当天考勤饼图
    renderDayPieChart() {
      if (this.dayDetailData.length === 0)
        return;
      const ctx = common_vendor.index.createCanvasContext("dayAttendanceChart");
      const sysInfo = common_vendor.index.getSystemInfoSync();
      const screenWidth = sysInfo.windowWidth;
      const pieData = [
        { name: "正常", value: this.normalCount, color: "#4cd964" },
        { name: "迟到", value: this.lateCount, color: "#f0ad4e" },
        { name: "早退", value: this.earlyCount, color: "#f56c6c" },
        { name: "缺勤", value: this.absentCount, color: "#dd524d" }
      ];
      const filteredData = pieData.filter((item) => item.value > 0);
      if (filteredData.length === 0)
        return;
      const canvasWidth = screenWidth - 60;
      const canvasHeight = 240;
      const centerX = canvasWidth / 2;
      const centerY = canvasHeight / 2;
      const radius = Math.min(centerX, centerY) * 0.6;
      const total = filteredData.reduce((sum, item) => sum + item.value, 0);
      ctx.clearRect(0, 0, canvasWidth, canvasHeight);
      let startAngle = 0;
      filteredData.forEach((item) => {
        const sliceAngle = item.value / total * 2 * Math.PI;
        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.arc(centerX, centerY, radius, startAngle, startAngle + sliceAngle);
        ctx.closePath();
        ctx.setFillStyle(item.color);
        ctx.fill();
        startAngle += sliceAngle;
      });
      let legendY = 40;
      const legendSpacing = 30;
      filteredData.forEach((item) => {
        ctx.setFillStyle(item.color);
        ctx.fillRect(canvasWidth - 100, legendY - 8, 16, 16);
        ctx.setFillStyle("#333");
        ctx.setFontSize(12);
        ctx.setTextAlign("right");
        ctx.fillText(`${item.name}: ${item.value}`, canvasWidth - 10, legendY);
        legendY += legendSpacing;
      });
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius * 0.5, 0, 2 * Math.PI);
      ctx.setFillStyle("#ffffff");
      ctx.fill();
      ctx.setFillStyle("#333333");
      ctx.setFontSize(14);
      ctx.setTextAlign("center");
      ctx.fillText("考勤分布", centerX, centerY - 7);
      ctx.setFontSize(16);
      ctx.fillText(total + "人", centerX, centerY + 15);
      ctx.draw();
    },
    // 计算工作时长
    calculateWorkHours(clockInTime, clockOutTime) {
      if (!clockInTime || !clockOutTime)
        return "--";
      try {
        const clockIn = new Date(clockInTime);
        const clockOut = new Date(clockOutTime);
        const diffMs = clockOut - clockIn;
        if (diffMs <= 0)
          return "--";
        const hours = Math.floor(diffMs / 36e5);
        const minutes = Math.floor(diffMs % 36e5 / 6e4);
        return `${hours}小时${minutes}分钟`;
      } catch (e) {
        return "--";
      }
    },
    // 关闭日详情弹窗
    closeDayDetailPopup() {
      this.$refs.dayDetailPopup.close();
      this.dayDetailPopupVisible = false;
      this.selectedStaffDetail = null;
    },
    // 查看全部记录
    viewAllRecords() {
      common_vendor.index.navigateTo({
        url: "/pages/attendance/all-records"
      });
    },
    // 预览图片
    previewImage(current, urls) {
      common_vendor.index.previewImage({
        current,
        urls
      });
    },
    // 导出考勤数据
    exportAttendanceData() {
      const year = this.currentYear;
      const month = this.currentMonth;
      const firstDay = new Date(year, month - 1, 1);
      const lastDay = new Date(year, month, 0);
      const formatDate = (date) => {
        const y = date.getFullYear();
        const m = String(date.getMonth() + 1).padStart(2, "0");
        const d = String(date.getDate()).padStart(2, "0");
        return `${y}-${m}-${d}`;
      };
      this.exportForm.startDate = formatDate(firstDay);
      this.exportForm.endDate = formatDate(lastDay);
      this.exportForm.type = "all";
      this.exportForm.format = "excel";
      this.selectedExportStaff = [];
      this.$refs.exportPopup.open();
    },
    // 关闭导出弹窗
    closeExportPopup() {
      this.$refs.exportPopup.close();
    },
    // 设置导出类型
    setExportType(type) {
      this.exportForm.type = type;
      if (type === "all") {
        this.selectedExportStaff = [];
      }
    },
    // 打开日期选择器
    openDatePicker(type) {
      this.currentPickerType = type;
      this.$refs.calendar.open();
    },
    // 日期选择回调
    selectDate(e) {
      const dateStr = e.fulldate;
      if (this.currentPickerType === "start") {
        this.exportForm.startDate = dateStr;
        if (this.exportForm.endDate && new Date(dateStr) > new Date(this.exportForm.endDate)) {
          this.exportForm.endDate = dateStr;
        }
      } else {
        this.exportForm.endDate = dateStr;
        if (this.exportForm.startDate && new Date(dateStr) < new Date(this.exportForm.startDate)) {
          this.exportForm.startDate = dateStr;
        }
      }
    },
    // 打开多选人员选择器
    openStaffMultiSelector() {
      this.$refs.multiStaffPopup.open();
    },
    // 关闭多选人员选择器
    closeMultiStaffSelector() {
      this.$refs.multiStaffPopup.close();
    },
    // 切换选择导出人员
    toggleExportStaff(staff) {
      const index = this.selectedExportStaff.findIndex((item) => item.id === staff.id);
      if (index >= 0) {
        this.selectedExportStaff.splice(index, 1);
      } else {
        this.selectedExportStaff.push(staff);
      }
    },
    // 检查员工是否被选中
    isStaffSelected(staff) {
      return this.selectedExportStaff.some((item) => item.id === staff.id);
    },
    // 确认导出考勤数据
    confirmExport() {
      if (!this.exportForm.startDate || !this.exportForm.endDate) {
        common_vendor.index.showToast({
          title: "请选择完整的时间范围",
          icon: "none"
        });
        return;
      }
      if (this.exportForm.type === "selected" && this.selectedExportStaff.length === 0) {
        common_vendor.index.showToast({
          title: "请至少选择一名员工",
          icon: "none"
        });
        return;
      }
      if (this.exportLoading)
        return;
      this.exportLoading = true;
      common_vendor.index.showLoading({
        title: "正在导出数据..."
      });
      const params = {
        startDate: this.exportForm.startDate,
        endDate: this.exportForm.endDate,
        format: this.exportForm.format
      };
      if (this.exportForm.type === "selected") {
        params.userIds = this.selectedExportStaff.map((staff) => staff.id);
      }
      utils_api.attendanceApi.exportAttendance(params).then((res) => {
        if (res.code === 200 && res.data) {
          const fileUrl = res.data.fileUrl || res.data;
          this.downloadFile(fileUrl);
          this.closeExportPopup();
          common_vendor.index.showToast({
            title: "导出成功",
            icon: "success"
          });
        } else {
          throw new Error(res.message || "导出失败");
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/attendance/statistics.vue:1425", "导出考勤数据失败:", err);
        common_vendor.index.showToast({
          title: err.message || "导出失败，请稍后重试",
          icon: "none"
        });
      }).finally(() => {
        common_vendor.index.hideLoading();
        this.exportLoading = false;
      });
    },
    // 下载文件
    downloadFile(url) {
      common_vendor.index.showLoading({
        title: "正在下载..."
      });
      common_vendor.index.downloadFile({
        url,
        success: (res) => {
          if (res.statusCode === 200) {
            const tempFilePath = res.tempFilePath;
            common_vendor.index.saveFile({
              tempFilePath,
              success: (saveRes) => {
                common_vendor.index.hideLoading();
                const savedFilePath = saveRes.savedFilePath;
                common_vendor.index.showModal({
                  title: "导出成功",
                  content: "文件已保存到本地，是否查看？",
                  success: (res2) => {
                    if (res2.confirm) {
                      common_vendor.index.openDocument({
                        filePath: savedFilePath,
                        showMenu: true,
                        success: () => {
                          common_vendor.index.__f__("log", "at pages/attendance/statistics.vue:1511", "打开文档成功");
                        },
                        fail: (err) => {
                          common_vendor.index.__f__("error", "at pages/attendance/statistics.vue:1514", "打开文档失败", err);
                          common_vendor.index.showToast({
                            title: "无法打开该类型文件",
                            icon: "none"
                          });
                        }
                      });
                    }
                  }
                });
              },
              fail: (err) => {
                common_vendor.index.hideLoading();
                common_vendor.index.__f__("error", "at pages/attendance/statistics.vue:1527", "保存文件失败", err);
                common_vendor.index.showToast({
                  title: "保存文件失败",
                  icon: "none"
                });
              }
            });
          }
        },
        fail: (err) => {
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("error", "at pages/attendance/statistics.vue:1538", "下载文件失败", err);
          common_vendor.index.showToast({
            title: "下载文件失败",
            icon: "none"
          });
        }
      });
    },
    // 页面准备好后的回调
    onReady() {
      setTimeout(() => {
        this.initBarChart();
        this.initPieChart();
      }, 500);
    },
    // 初始化柱状图
    initBarChart() {
      const sysInfo = common_vendor.index.getSystemInfoSync();
      sysInfo.pixelRatio || 2;
      const canvasWidth = sysInfo.windowWidth * 0.9;
      const canvasHeight = 400 * (sysInfo.windowWidth / 750);
      const ctx = common_vendor.index.createCanvasContext("attendanceBarChart", this);
      ctx.setFillStyle("#FFFFFF");
      ctx.fillRect(0, 0, canvasWidth, canvasHeight);
      const data = [
        { label: "正常", value: this.statistics.normalDays, color: "#4cd964" },
        { label: "迟到", value: this.statistics.lateDays, color: "#f0ad4e" },
        { label: "早退", value: this.statistics.earlyDays, color: "#5bc0de" },
        { label: "缺勤", value: this.statistics.absentDays, color: "#dd524d" }
      ];
      const maxValue = Math.max(...data.map((item) => item.value), 1);
      const padding = { left: 40, right: 20, top: 20, bottom: 60 };
      const chartWidth = canvasWidth - padding.left - padding.right;
      const chartHeight = canvasHeight - padding.top - padding.bottom;
      const barWidth = chartWidth / data.length / 1.5;
      const barSpacing = barWidth / 2;
      ctx.beginPath();
      ctx.setStrokeStyle("#CCCCCC");
      ctx.setLineWidth(1);
      ctx.moveTo(padding.left, padding.top);
      ctx.lineTo(padding.left, canvasHeight - padding.bottom);
      ctx.stroke();
      ctx.beginPath();
      ctx.moveTo(padding.left, canvasHeight - padding.bottom);
      ctx.lineTo(canvasWidth - padding.right, canvasHeight - padding.bottom);
      ctx.stroke();
      data.forEach((item, index) => {
        const x = padding.left + (barWidth + barSpacing * 2) * index + barSpacing * 2;
        const barHeight = item.value > 0 ? item.value / maxValue * chartHeight : 0;
        const y = canvasHeight - padding.bottom - barHeight;
        ctx.setFillStyle(item.color);
        ctx.fillRect(x, y, barWidth, barHeight);
        ctx.setFillStyle("#333333");
        ctx.setFontSize(12);
        ctx.setTextAlign("center");
        ctx.fillText(item.value.toString(), x + barWidth / 2, y - 10);
        ctx.setFillStyle("#666666");
        ctx.setFontSize(12);
        ctx.fillText(item.label, x + barWidth / 2, canvasHeight - padding.bottom + 20);
      });
      const yStep = maxValue / 5;
      for (let i = 0; i <= 5; i++) {
        const y = canvasHeight - padding.bottom - i / 5 * chartHeight;
        const value = Math.round(i * yStep);
        ctx.setFillStyle("#999999");
        ctx.setFontSize(10);
        ctx.setTextAlign("right");
        ctx.fillText(value.toString(), padding.left - 5, y + 3);
        ctx.beginPath();
        ctx.setStrokeStyle("#EEEEEE");
        ctx.setLineWidth(0.5);
        ctx.moveTo(padding.left, y);
        ctx.lineTo(canvasWidth - padding.right, y);
        ctx.stroke();
      }
      ctx.draw();
    },
    // 初始化饼图
    initPieChart() {
      const sysInfo = common_vendor.index.getSystemInfoSync();
      sysInfo.pixelRatio || 2;
      const canvasWidth = sysInfo.windowWidth * 0.9;
      const canvasHeight = 400 * (sysInfo.windowWidth / 750);
      const centerX = canvasWidth / 2;
      const centerY = canvasHeight / 2;
      const radius = Math.min(centerX, centerY) * 0.7;
      const ctx = common_vendor.index.createCanvasContext("attendancePieChart", this);
      ctx.setFillStyle("#FFFFFF");
      ctx.fillRect(0, 0, canvasWidth, canvasHeight);
      const total = this.statistics.normalDays + this.statistics.lateDays + this.statistics.earlyDays + this.statistics.absentDays || 1;
      const data = [
        { label: "正常", value: this.statistics.normalDays, color: "#4cd964" },
        { label: "迟到", value: this.statistics.lateDays, color: "#f0ad4e" },
        { label: "早退", value: this.statistics.earlyDays, color: "#5bc0de" },
        { label: "缺勤", value: this.statistics.absentDays, color: "#dd524d" }
      ];
      const validData = data.filter((item) => item.value > 0);
      ctx.setFillStyle("#333333");
      ctx.setFontSize(14);
      ctx.setTextAlign("center");
      ctx.fillText("考勤类型分布", centerX, 20);
      if (validData.length === 0) {
        ctx.setFillStyle("#999999");
        ctx.setFontSize(14);
        ctx.setTextAlign("center");
        ctx.fillText("暂无数据", centerX, centerY);
        ctx.draw();
        return;
      }
      let startAngle = 0;
      validData.forEach((item) => {
        const percentage = item.value / total;
        if (percentage <= 0)
          return;
        const endAngle = startAngle + percentage * 2 * Math.PI;
        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.arc(centerX, centerY, radius, startAngle, endAngle, false);
        ctx.setFillStyle(item.color);
        ctx.fill();
        const labelAngle = startAngle + (endAngle - startAngle) / 2;
        const labelDistance = radius * 1.2;
        const labelX = centerX + Math.cos(labelAngle) * labelDistance;
        const labelY = centerY + Math.sin(labelAngle) * labelDistance;
        if (percentage > 0.05) {
          const lineEndX = centerX + Math.cos(labelAngle) * radius;
          const lineEndY = centerY + Math.sin(labelAngle) * radius;
          ctx.beginPath();
          ctx.setStrokeStyle(item.color);
          ctx.setLineWidth(1);
          ctx.moveTo(lineEndX, lineEndY);
          ctx.lineTo(labelX, labelY);
          ctx.stroke();
          ctx.setFillStyle("#333333");
          ctx.setFontSize(12);
          ctx.setTextAlign("center");
          ctx.fillText(`${item.label}: ${Math.round(percentage * 100)}%`, labelX, labelY);
        }
        startAngle = endAngle;
      });
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius * 0.5, 0, 2 * Math.PI, false);
      ctx.setFillStyle("#FFFFFF");
      ctx.fill();
      ctx.setFillStyle("#333333");
      ctx.setFontSize(16);
      ctx.setTextAlign("center");
      ctx.fillText(total.toString(), centerX, centerY - 5);
      ctx.setFontSize(12);
      ctx.fillText("考勤总天数", centerX, centerY + 15);
      ctx.draw();
    },
    onShow() {
      setTimeout(() => {
        this.initBarChart();
        this.initPieChart();
      }, 300);
    },
    onResize() {
      this.initBarChart();
      this.initPieChart();
    },
    // 打开日详情弹窗
    openDayDetailPopup(date) {
      this.selectedDate = date;
      this.dayDetailPopupVisible = true;
      this.loadDayDetailData(date);
      this.$refs.dayDetailPopup.open();
    },
    // 加载日详情数据
    loadDayDetailData(date) {
      this.dayDetailData = [];
      this.selectedStaffDetail = null;
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      this.$http.get("/attendance/daily-detail", {
        params: {
          date,
          deptId: this.currentDept.id
        }
      }).then((res) => {
        if (res.code === 200) {
          this.dayDetailData = res.data;
          this.$nextTick(() => {
            this.renderDayPieChart();
          });
        } else {
          common_vendor.index.showToast({
            title: res.msg || "获取日详情失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        common_vendor.index.showToast({
          title: "获取日详情失败",
          icon: "none"
        });
      }).finally(() => {
        common_vendor.index.hideLoading();
      });
    },
    // 选择人员详情
    selectStaffDetail(staff) {
      this.selectedStaffDetail = staff;
    },
    // 点击饼图触摸事件
    touchChart(e) {
    },
    // 日历日期点击事件，调用openDayDetailPopup
    dayClick(day) {
      const date = this.formatDate(day);
      this.openDayDetailPopup(date);
    },
    // 格式化日期为YYYY-MM-DD
    formatDate(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    formatWeek(date) {
      const days = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
      return days[new Date(date).getDay()];
    },
    formatLocation(record) {
      return `${record.latitude.toFixed(6)}, ${record.longitude.toFixed(6)}`;
    }
  }
};
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  const _easycom_uni_calendar2 = common_vendor.resolveComponent("uni-calendar");
  (_easycom_uni_icons2 + _easycom_uni_popup2 + _easycom_uni_calendar2)();
}
const _easycom_uni_icons = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-icons/uni-icons.js";
const _easycom_uni_popup = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-popup/uni-popup.js";
const _easycom_uni_calendar = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-calendar/uni-calendar.js";
if (!Math) {
  (_easycom_uni_icons + _easycom_uni_popup + _easycom_uni_calendar)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.currentYear),
    b: common_vendor.t($data.currentMonth),
    c: common_vendor.o($options.prevMonth),
    d: common_vendor.p({
      type: "left",
      size: "20",
      color: "#333"
    }),
    e: common_vendor.o($options.nextMonth),
    f: common_vendor.p({
      type: "right",
      size: "20",
      color: "#333"
    }),
    g: common_vendor.t($data.selectedStaff ? $data.selectedStaff.name : "全部人员"),
    h: common_vendor.p({
      type: "bottom",
      size: "14",
      color: "#666"
    }),
    i: common_vendor.o((...args) => $options.openStaffSelector && $options.openStaffSelector(...args)),
    j: common_vendor.t($data.statistics.normalDays),
    k: common_vendor.t($data.statistics.lateDays),
    l: common_vendor.t($data.statistics.earlyDays),
    m: common_vendor.t($data.statistics.absentDays),
    n: common_vendor.f($data.weekDays, (day, index, i0) => {
      return {
        a: common_vendor.t(day),
        b: index
      };
    }),
    o: common_vendor.f($data.calendarDays, (day, index, i0) => {
      return {
        a: common_vendor.t(day.day),
        b: index,
        c: day.isCurrentMonth ? 1 : "",
        d: day.isToday ? 1 : "",
        e: day.status === "normal" ? 1 : "",
        f: day.status === "late" ? 1 : "",
        g: day.status === "early" ? 1 : "",
        h: day.status === "absent" ? 1 : "",
        i: common_vendor.o(($event) => $options.viewDayDetail(day), index)
      };
    }),
    p: common_vendor.o((...args) => $options.exportAttendanceData && $options.exportAttendanceData(...args)),
    q: common_vendor.o((...args) => $options.viewAllRecords && $options.viewAllRecords(...args)),
    r: $data.records.length > 0
  }, $data.records.length > 0 ? {
    s: common_vendor.f($data.records, (record, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(record.date || $options.formatDate(record.clockTime)),
        b: common_vendor.t(record.week || $options.formatWeek(record.clockTime)),
        c: common_vendor.t(record.clockInTime || "未打卡"),
        d: record.clockInStatus !== "normal" ? 1 : "",
        e: record.clockInStatus === "late"
      }, record.clockInStatus === "late" ? {} : {}, {
        f: common_vendor.t(record.clockOutTime || "未打卡"),
        g: record.clockOutStatus !== "normal" ? 1 : "",
        h: record.clockOutStatus === "early"
      }, record.clockOutStatus === "early" ? {} : {}, {
        i: common_vendor.t(record.location || $options.formatLocation(record)),
        j: index
      });
    })
  } : {
    t: common_assets._imports_0$1
  }, {
    v: common_vendor.o((...args) => $options.closeStaffSelector && $options.closeStaffSelector(...args)),
    w: common_vendor.p({
      type: "search",
      size: "16",
      color: "#999"
    }),
    x: $data.searchKeyword,
    y: common_vendor.o(($event) => $data.searchKeyword = $event.detail.value),
    z: !$data.selectedStaff
  }, !$data.selectedStaff ? {
    A: common_vendor.p({
      type: "checkmarkempty",
      size: "18",
      color: "#007AFF"
    })
  } : {}, {
    B: common_vendor.o(($event) => $options.selectStaff(null)),
    C: common_vendor.f($options.filteredStaffList, (staff, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(staff.name),
        b: $data.selectedStaff && $data.selectedStaff.id === staff.id
      }, $data.selectedStaff && $data.selectedStaff.id === staff.id ? {
        c: "85eb6f3a-6-" + i0 + ",85eb6f3a-3",
        d: common_vendor.p({
          type: "checkmarkempty",
          size: "18",
          color: "#007AFF"
        })
      } : {}, {
        e: index,
        f: common_vendor.o(($event) => $options.selectStaff(staff), index)
      });
    }),
    D: common_vendor.sr("staffPopup", "85eb6f3a-3"),
    E: common_vendor.p({
      type: "bottom"
    }),
    F: common_vendor.t($data.selectedDate),
    G: common_vendor.o((...args) => $options.closeDayDetailPopup && $options.closeDayDetailPopup(...args)),
    H: $data.dayDetailData.length > 0
  }, $data.dayDetailData.length > 0 ? {
    I: common_vendor.o((...args) => $options.touchChart && $options.touchChart(...args))
  } : {}, {
    J: $data.dayDetailData.length > 0
  }, $data.dayDetailData.length > 0 ? {
    K: common_vendor.t($options.normalCount),
    L: common_vendor.t($options.lateCount),
    M: common_vendor.t($options.earlyCount),
    N: common_vendor.t($options.absentCount)
  } : {}, {
    O: $data.dayDetailData.length > 0 && $data.selectedStaffDetail
  }, $data.dayDetailData.length > 0 && $data.selectedStaffDetail ? common_vendor.e({
    P: common_vendor.t($data.selectedStaffDetail.name),
    Q: common_vendor.t($data.selectedStaffDetail.checkInTime || "无打卡"),
    R: $data.selectedStaffDetail.isLate
  }, $data.selectedStaffDetail.isLate ? {} : {}, {
    S: common_vendor.t($data.selectedStaffDetail.checkOutTime || "无打卡"),
    T: $data.selectedStaffDetail.isEarly
  }, $data.selectedStaffDetail.isEarly ? {} : {}, {
    U: common_vendor.t($data.selectedStaffDetail.workHours || "0小时")
  }) : {}, {
    V: $data.dayDetailData.length > 0
  }, $data.dayDetailData.length > 0 ? common_vendor.e({
    W: $options.normalStaff.length > 0
  }, $options.normalStaff.length > 0 ? {
    X: common_vendor.t($options.normalStaff.length),
    Y: common_vendor.f($options.normalStaff, (staff, index, i0) => {
      return {
        a: common_vendor.t(staff.name),
        b: common_vendor.t(staff.checkInTime),
        c: common_vendor.t(staff.checkOutTime),
        d: index,
        e: common_vendor.o(($event) => $options.selectStaffDetail(staff), index)
      };
    })
  } : {}, {
    Z: $options.lateStaff.length > 0
  }, $options.lateStaff.length > 0 ? {
    aa: common_vendor.t($options.lateStaff.length),
    ab: common_vendor.f($options.lateStaff, (staff, index, i0) => {
      return {
        a: common_vendor.t(staff.name),
        b: common_vendor.t(staff.checkInTime),
        c: index,
        d: common_vendor.o(($event) => $options.selectStaffDetail(staff), index)
      };
    })
  } : {}, {
    ac: $options.earlyStaff.length > 0
  }, $options.earlyStaff.length > 0 ? {
    ad: common_vendor.t($options.earlyStaff.length),
    ae: common_vendor.f($options.earlyStaff, (staff, index, i0) => {
      return {
        a: common_vendor.t(staff.name),
        b: common_vendor.t(staff.checkOutTime),
        c: index,
        d: common_vendor.o(($event) => $options.selectStaffDetail(staff), index)
      };
    })
  } : {}, {
    af: $options.absentStaff.length > 0
  }, $options.absentStaff.length > 0 ? {
    ag: common_vendor.t($options.absentStaff.length),
    ah: common_vendor.f($options.absentStaff, (staff, index, i0) => {
      return {
        a: common_vendor.t(staff.name),
        b: index,
        c: common_vendor.o(($event) => $options.selectStaffDetail(staff), index)
      };
    })
  } : {}) : {}, {
    ai: $data.dayDetailData.length === 0
  }, $data.dayDetailData.length === 0 ? {
    aj: common_assets._imports_0$1
  } : {}, {
    ak: common_vendor.sr("dayDetailPopup", "85eb6f3a-7"),
    al: common_vendor.p({
      type: "bottom"
    }),
    am: common_vendor.o((...args) => $options.closeExportPopup && $options.closeExportPopup(...args)),
    an: common_vendor.t($data.exportForm.startDate || "开始日期"),
    ao: common_vendor.p({
      type: "calendar",
      size: "16",
      color: "#999"
    }),
    ap: common_vendor.o(($event) => $options.openDatePicker("start")),
    aq: common_vendor.t($data.exportForm.endDate || "结束日期"),
    ar: common_vendor.p({
      type: "calendar",
      size: "16",
      color: "#999"
    }),
    as: common_vendor.o(($event) => $options.openDatePicker("end")),
    at: $data.exportForm.type === "all" ? 1 : "",
    av: common_vendor.o(($event) => $options.setExportType("all")),
    aw: $data.exportForm.type === "selected" ? 1 : "",
    ax: common_vendor.o(($event) => $options.setExportType("selected")),
    ay: $data.exportForm.type === "selected"
  }, $data.exportForm.type === "selected" ? {
    az: common_vendor.t($data.selectedExportStaff.length),
    aA: common_vendor.p({
      type: "right",
      size: "16",
      color: "#999"
    }),
    aB: common_vendor.o((...args) => $options.openStaffMultiSelector && $options.openStaffMultiSelector(...args))
  } : {}, {
    aC: $data.exportForm.format === "excel" ? 1 : "",
    aD: common_vendor.o(($event) => $data.exportForm.format = "excel"),
    aE: $data.exportForm.format === "pdf" ? 1 : "",
    aF: common_vendor.o(($event) => $data.exportForm.format = "pdf"),
    aG: common_vendor.o((...args) => $options.closeExportPopup && $options.closeExportPopup(...args)),
    aH: common_vendor.o((...args) => $options.confirmExport && $options.confirmExport(...args)),
    aI: common_vendor.sr("exportPopup", "85eb6f3a-8"),
    aJ: common_vendor.p({
      type: "bottom"
    }),
    aK: common_vendor.o((...args) => $options.closeMultiStaffSelector && $options.closeMultiStaffSelector(...args)),
    aL: common_vendor.p({
      type: "search",
      size: "16",
      color: "#999"
    }),
    aM: $data.multiSearchKeyword,
    aN: common_vendor.o(($event) => $data.multiSearchKeyword = $event.detail.value),
    aO: common_vendor.f($options.filteredMultiStaffList, (staff, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(staff.name),
        b: $options.isStaffSelected(staff)
      }, $options.isStaffSelected(staff) ? {
        c: "85eb6f3a-14-" + i0 + ",85eb6f3a-12",
        d: common_vendor.p({
          type: "checkbox-filled",
          size: "18",
          color: "#007AFF"
        })
      } : {
        e: "85eb6f3a-15-" + i0 + ",85eb6f3a-12",
        f: common_vendor.p({
          type: "checkbox",
          size: "18",
          color: "#999"
        })
      }, {
        g: index,
        h: common_vendor.o(($event) => $options.toggleExportStaff(staff), index)
      });
    }),
    aP: common_vendor.sr("multiStaffPopup", "85eb6f3a-12"),
    aQ: common_vendor.p({
      type: "bottom"
    }),
    aR: common_vendor.sr("calendar", "85eb6f3a-16"),
    aS: common_vendor.o($options.selectDate),
    aT: common_vendor.p({
      insert: false
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/attendance/statistics.js.map
