"use strict";
const common_vendor = require("../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      title: "选择日期时间",
      indicatorStyle: "height: 50px;",
      pickerValue: [0, 0, 0, 0, 0, 0],
      years: [],
      months: [],
      days: [],
      hours: [],
      minutes: [],
      seconds: [],
      year: 2020,
      month: 1,
      day: 1,
      hour: 0,
      minute: 0,
      second: 0,
      hasInit: false
    };
  },
  onLoad(option) {
    if (option.params) {
      const params = JSON.parse(decodeURIComponent(option.params));
      if (params.title) {
        this.title = params.title;
      }
      if (params.current) {
        this.initPickerFromDateTime(params.current);
      } else {
        this.initPicker();
      }
    } else {
      this.initPicker();
    }
  },
  methods: {
    // 初始化选择器数据
    initPicker() {
      const date = /* @__PURE__ */ new Date();
      this.year = date.getFullYear();
      this.month = date.getMonth() + 1;
      this.day = date.getDate();
      this.hour = date.getHours();
      this.minute = date.getMinutes();
      this.second = date.getSeconds();
      this.years = [];
      for (let i = this.year - 10; i <= this.year + 10; i++) {
        this.years.push(i);
      }
      this.months = [];
      for (let i = 1; i <= 12; i++) {
        this.months.push(i);
      }
      this.updateDays();
      this.hours = [];
      for (let i = 0; i <= 23; i++) {
        this.hours.push(i);
      }
      this.minutes = [];
      this.seconds = [];
      for (let i = 0; i <= 59; i++) {
        this.minutes.push(i);
        this.seconds.push(i);
      }
      this.updatePickerValue();
      this.hasInit = true;
    },
    // 根据日期时间字符串初始化选择器
    initPickerFromDateTime(dateTimeStr) {
      const date = new Date(dateTimeStr);
      if (isNaN(date.getTime())) {
        this.initPicker();
        return;
      }
      this.year = date.getFullYear();
      this.month = date.getMonth() + 1;
      this.day = date.getDate();
      this.hour = date.getHours();
      this.minute = date.getMinutes();
      this.second = date.getSeconds();
      this.years = [];
      for (let i = this.year - 10; i <= this.year + 10; i++) {
        this.years.push(i);
      }
      this.months = [];
      for (let i = 1; i <= 12; i++) {
        this.months.push(i);
      }
      this.updateDays();
      this.hours = [];
      for (let i = 0; i <= 23; i++) {
        this.hours.push(i);
      }
      this.minutes = [];
      this.seconds = [];
      for (let i = 0; i <= 59; i++) {
        this.minutes.push(i);
        this.seconds.push(i);
      }
      this.updatePickerValue();
      this.hasInit = true;
    },
    // 更新天数，根据年月确定
    updateDays() {
      let daysInMonth = 31;
      if (this.month === 2) {
        daysInMonth = this.year % 4 === 0 && this.year % 100 !== 0 || this.year % 400 === 0 ? 29 : 28;
      } else if ([4, 6, 9, 11].includes(this.month)) {
        daysInMonth = 30;
      }
      this.days = [];
      for (let i = 1; i <= daysInMonth; i++) {
        this.days.push(i);
      }
      if (this.day > daysInMonth) {
        this.day = daysInMonth;
      }
    },
    // 更新pickerValue
    updatePickerValue() {
      const yearIndex = this.years.indexOf(this.year);
      const monthIndex = this.months.indexOf(this.month);
      const dayIndex = this.days.indexOf(this.day);
      const hourIndex = this.hours.indexOf(this.hour);
      const minuteIndex = this.minutes.indexOf(this.minute);
      const secondIndex = this.seconds.indexOf(this.second);
      this.pickerValue = [
        yearIndex !== -1 ? yearIndex : 0,
        monthIndex !== -1 ? monthIndex : 0,
        dayIndex !== -1 ? dayIndex : 0,
        hourIndex !== -1 ? hourIndex : 0,
        minuteIndex !== -1 ? minuteIndex : 0,
        secondIndex !== -1 ? secondIndex : 0
      ];
    },
    // 当picker值发生变化时的处理函数
    bindChange(e) {
      const val = e.detail.value;
      this.year = this.years[val[0]];
      this.month = this.months[val[1]];
      this.updateDays();
      if (val[2] >= this.days.length) {
        val[2] = 0;
      }
      this.day = this.days[val[2]];
      this.hour = this.hours[val[3]];
      this.minute = this.minutes[val[4]];
      this.second = this.seconds[val[5]];
    },
    // 确认选择
    confirm() {
      const datetime = this.formatDateTime();
      const eventChannel = this.getOpenerEventChannel();
      eventChannel.emit("dateTimeSelected", {
        datetime
      });
      common_vendor.index.navigateBack();
    },
    // 取消选择
    cancel() {
      common_vendor.index.navigateBack();
    },
    // 格式化日期时间
    formatDateTime() {
      const year = this.year;
      const month = String(this.month).padStart(2, "0");
      const day = String(this.day).padStart(2, "0");
      const hour = String(this.hour).padStart(2, "0");
      const minute = String(this.minute).padStart(2, "0");
      const second = String(this.second).padStart(2, "0");
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t($data.title),
    b: common_vendor.o((...args) => $options.cancel && $options.cancel(...args)),
    c: common_vendor.f($data.years, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: "year-" + index
      };
    }),
    d: common_vendor.f($data.months, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: "month-" + index
      };
    }),
    e: common_vendor.f($data.days, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: "day-" + index
      };
    }),
    f: common_vendor.f($data.hours, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: "hour-" + index
      };
    }),
    g: common_vendor.f($data.minutes, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: "minute-" + index
      };
    }),
    h: common_vendor.f($data.seconds, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: "second-" + index
      };
    }),
    i: $data.indicatorStyle,
    j: $data.pickerValue,
    k: common_vendor.o((...args) => $options.bindChange && $options.bindChange(...args)),
    l: common_vendor.o((...args) => $options.confirm && $options.confirm(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../.sourcemap/mp-weixin/components/datetime-picker.js.map
