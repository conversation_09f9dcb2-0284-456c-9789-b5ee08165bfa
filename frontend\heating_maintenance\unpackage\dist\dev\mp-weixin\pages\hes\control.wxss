
.station-control {
  min-height: 100vh;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
}
.page-title {
  background-color: #0088ff;
  color: #fff;
  padding: 20rpx 30rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  font-weight: bold;
}
.back-button {
  position: absolute;
  left: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}
.tab-section {
  background-color: #0088ff;
  padding: 0 20rpx 16rpx;
}
.tab-bar {
  display: flex;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 8rpx;
  padding: 6rpx;
}
.tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 0;
  font-size: 28rpx;
  border-radius: 6rpx;
  color: #fff;
}
.tab-item.active {
  background-color: rgba(255, 255, 255, 0.9);
  color: #0088ff;
  font-weight: bold;
}
.station-selector {
  background-color: #fff;
  padding: 30rpx;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #eee;
}
.station-label {
  font-size: 30rpx;
  color: #666;
  min-width: 150rpx;
}
.station-value-wrapper {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #e5e5e5;
  padding: 12rpx 20rpx;
  border-radius: 6rpx;
}
.station-value-text {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}
.station-value-text.placeholder {
  color: #999;
  font-weight: normal;
}
.station-arrow {
  font-size: 30rpx;
  color: #999;
}
.station-content {
  flex: 1;
  padding: 20rpx;
}
.card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 16rpx;
}
.info-row {
  display: flex;
  padding: 16rpx 0;
}
.label {
  color: #666;
  width: 180rpx;
}
.value {
  color: #333;
  flex: 1;
}
.value.online {
  color: #00c853;
}
.value.offline {
  color: #ef5350;
}
.value.fault {
  color: #ff9800;
}
.data-grid {
  display: flex;
  flex-wrap: wrap;
}
.data-item {
  width: 50%;
  padding: 20rpx 0;
  display: flex;
  flex-direction: column;
}
.data-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.data-value {
  font-size: 36rpx;
  color: #0088ff;
  font-weight: bold;
}
.control-grid {
  display: flex;
  flex-wrap: wrap;
}
.control-item {
  width: 50%;
  padding: 20rpx 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.control-label {
  font-size: 28rpx;
  color: #333;
}
.slider-control {
  margin: 30rpx 0;
}
.slider-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}
.slider-box {
  padding: 0 20rpx;
}
.process-view {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.process-image {
  width: 100%;
  text-align: center;
}
.process-image image {
  width: 100%;
  max-width: 690rpx;
}
.process-legend {
  display: flex;
  justify-content: center;
  margin-top: 30rpx;
}
.legend-item {
  display: flex;
  align-items: center;
  margin: 0 20rpx;
}
.legend-color {
  width: 30rpx;
  height: 16rpx;
  margin-right: 10rpx;
}
.legend-text {
  font-size: 24rpx;
  color: #666;
}
.no-station {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
  font-size: 32rpx;
}

/* 站点选择器样式 */
.station-picker {
  background-color: #f5f5f5;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}
.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: #fff;
}
.picker-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.picker-close {
  font-size: 28rpx;
  color: #666;
}

/* 搜索栏样式 */
.search-bar {
  padding: 20rpx 30rpx;
  background-color: #f5f5f5;
}
.search-input-wrap {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 8rpx;
  padding: 0 20rpx;
  height: 80rpx;
}
.search-icon {
  margin-right: 10rpx;
  font-size: 28rpx;
  color: #999;
}
.search-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
}
.search-clear {
  width: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
  color: #999;
  font-size: 30rpx;
}

/* 状态筛选标签样式 */
.filter-tabs {
  display: flex;
  padding: 20rpx 20rpx;
  background-color: #f5f5f5;
}
.filter-tab {
  padding: 12rpx 24rpx;
  margin: 0 10rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #fff;
  border-radius: 50rpx;
}
.filter-tab.active {
  color: #fff;
  background-color: #0088ff;
}

/* 站点列表样式 */
.station-list {
  flex: 1;
  max-height: 60vh;
}
.station-item {
  margin: 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.station-item-content {
  position: relative;
  padding: 24rpx;
}
.station-main-info {
  margin-bottom: 16rpx;
}
.station-name {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
  display: block;
}
.station-address {
  font-size: 24rpx;
  color: #999;
  display: block;
}
.station-temp-info {
  margin-top: 12rpx;
  display: flex;
  justify-content: space-between;
}
.temp-item {
  font-size: 24rpx;
  color: #666;
  flex: 1;
}
.station-status-tag {
  position: absolute;
  right: 24rpx;
  bottom: 24rpx;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}
.station-status-tag.online {
  color: #00c853;
  background-color: rgba(0, 200, 83, 0.1);
}
.station-status-tag.offline {
  color: #ff5252;
  background-color: rgba(255, 82, 82, 0.1);
}
.station-status-tag.fault {
  color: #ff9800;
  background-color: rgba(255, 152, 0, 0.1);
}
.empty-tip {
  padding: 60rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
  background-color: #f5f5f5;
}

/* 工作模式卡片样式 */
.mode-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.mode-title {
  font-size: 30rpx;
  color: #333;
  margin-right: 20rpx;
}
.mode-buttons {
  display: flex;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  overflow: hidden;
}
.mode-button {
  padding: 12rpx 40rpx;
  font-size: 28rpx;
  color: #666;
  text-align: center;
}
.mode-button.active {
  background-color: #0088ff;
  color: #fff;
}
.mode-desc {
  font-size: 24rpx;
  color: #999;
  margin-top: 16rpx;
}

/* 设备控制卡片样式 */
.device-section {
  margin-bottom: 20rpx;
}
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.blue-indicator {
  width: 8rpx;
  height: 32rpx;
  background-color: #0088ff;
  margin-right: 10rpx;
}
.section-title-text {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}
.device-control-block {
  background-color: #fff;
  border: 1rpx solid #eeeeee;
  border-radius: 8rpx;
  overflow: hidden;
  padding: 20rpx;
}
.device-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.selector-wrap {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.dropdown-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 6rpx;
}
.dropdown-value {
  display: flex;
  align-items: center;
  border-bottom: 1px dashed #e0e0e0;
  padding-bottom: 8rpx;
}
.device-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}
.dropdown-arrow {
  font-size: 20rpx;
  color: #0088ff;
  margin-left: 10rpx;
}
.device-control-params {
  padding-top: 10rpx;
}
.param-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}
.slider-row {
  display: flex;
  align-items: center;
}
.param-value {
  width: 50rpx;
  text-align: right;
  font-size: 28rpx;
  color: #333;
  margin-left: 10rpx;
}

/* 控制算法卡片样式 */
.algorithm-selector {
  margin-bottom: 20rpx;
}
.algorithm-item {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
}
.algorithm-radio {
  display: flex;
  align-items: center;
}
.algorithm-label {
  font-size: 28rpx;
  color: #333;
  margin-left: 5rpx;
}
.algorithm-desc {
  font-size: 24rpx;
  color: #666;
  padding: 10rpx;
  background-color: #f9f9f9;
  border-radius: 6rpx;
  margin-bottom: 20rpx;
}
.algorithm-params {
  margin-top: 20rpx;
}
.param-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.param-input {
  flex: 1;
  height: 70rpx;
  border: 1rpx solid #e5e5e5;
  border-radius: 6rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}
.param-unit {
  font-size: 28rpx;
  color: #666;
  margin-left: 10rpx;
}
.apply-button {
  background-color: #0088ff;
  color: #fff;
  font-size: 28rpx;
  padding: 16rpx 0;
  border-radius: 6rpx;
  margin-top: 20rpx;
}
.apply-button[disabled] {
  background-color: #cccccc;
  color: #999;
}

/* 新增样式 */
.confirm-button-row {
  display: flex;
  justify-content: flex-end;
  margin-top: 20rpx;
}
.confirm-button {
  background-color: #0088ff;
  color: #fff;
  font-size: 28rpx;
  padding: 12rpx 24rpx;
  border-radius: 6rpx;
}
.confirm-button[disabled] {
  background-color: #cccccc;
  color: #999;
}

/* 设备控制标签页样式 */
.device-tabs {
  display: flex;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.device-tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}
.device-tab.active {
  color: #0088ff;
  font-weight: bold;
}
.device-tab.active:after {
  content: "";
  position: absolute;
  bottom: -2rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40%;
  height: 4rpx;
  background-color: #0088ff;
  border-radius: 2rpx;
}

/* 工业变频器风格的频率控制样式 */
.frequency-control {
  display: flex;
  margin: 20rpx 0;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  overflow: hidden;
}
.frequency-control.disabled {
  opacity: 0.6;
}
.frequency-display {
  flex: 1;
  background: #fff;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  border: 1rpx solid #e0e0e0;
  height: 80rpx;
  border-top-left-radius: 8rpx;
  border-bottom-left-radius: 8rpx;
}
.frequency-input {
  flex: 1;
  height: 80rpx;
  font-size: 40rpx;
  color: #333;
  font-weight: bold;
  text-align: right;
  background-color: transparent;
}
.frequency-unit {
  font-size: 30rpx;
  color: #666;
  margin-left: 10rpx;
  width: 40rpx;
}
.frequency-buttons {
  width: 80rpx;
  display: flex;
  flex-direction: column;
}
.frequency-button {
  height: 40rpx;
  line-height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #0088ff;
  color: white;
  font-size: 24rpx;
}
.frequency-button.disabled {
  background-color: #cccccc;
}
.frequency-button.increase {
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.3);
}

