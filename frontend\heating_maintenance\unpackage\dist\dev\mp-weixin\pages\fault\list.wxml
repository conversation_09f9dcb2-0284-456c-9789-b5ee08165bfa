<view class="fault-list-container"><view class="tab-container"><view class="{{['tab-item', a && 'active']}}" bindtap="{{b}}"> 全部 </view><view class="{{['tab-item', c && 'active']}}" bindtap="{{d}}"> 待确认 </view><view class="{{['tab-item', e && 'active']}}" bindtap="{{f}}"> 已确认 </view><view class="{{['tab-item', g && 'active']}}" bindtap="{{h}}"> 已退回 </view></view><view class="filter-section"><view class="date-filter"><picker mode="date" value="{{j}}" bindchange="{{k}}"><view class="date-picker"><text class="date-text">{{i}}</text></view></picker></view><button class="refresh-button" bindtap="{{l}}">重置</button></view><view wx:if="{{m}}" class="fault-list"><view wx:for="{{n}}" wx:for-item="fault" wx:key="j" class="fault-item" bindtap="{{fault.k}}"><view class="fault-header"><text class="heat-unit-name">{{fault.a}}</text><view class="{{['fault-status', fault.c]}}">{{fault.b}}</view></view><view class="fault-content"><view class="fault-desc">{{fault.d}}</view><view class="fault-info"><view class="info-item"><text class="info-label">发生时间：</text><text class="info-value">{{fault.e}}</text></view><view class="info-item"><text class="info-label">故障等级：</text><text class="{{['info-value', 'level-tag', fault.g]}}">{{fault.f}}</text></view></view><view class="fault-footer"><view class="report-info"><text class="reporter">{{fault.h}}</text><text class="report-time">{{fault.i}}</text></view></view></view></view></view><view wx:if="{{o}}" class="empty-state"><image class="empty-image" src="{{p}}" mode="aspectFit"></image><text class="empty-text">暂无故障记录</text><button class="refresh-button" bindtap="{{q}}">刷新</button></view><view wx:if="{{r}}" class="loading-container"><text class="loading-text">加载中...</text></view><view wx:if="{{s}}" class="error-container"><text class="error-text">加载失败，请重试</text><button class="retry-btn" bindtap="{{t}}">重新加载</button></view><permission-check wx:if="{{w}}" u-s="{{['d']}}" u-i="3ede6efe-0" bind:__l="__l" u-p="{{w}}"><view class="floating-button" bindtap="{{v}}"><text class="plus-icon">+</text></view></permission-check></view>