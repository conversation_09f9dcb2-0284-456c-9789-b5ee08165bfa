"use strict";
const utils_auth = require("./auth.js");
const permissionMixin = {
  data() {
    return {
      // 默认允许访问
      permissionCode: "",
      // 控制内容是否显示，用于避免权限检查过程中内容闪烁
      canShowContent: false
    };
  },
  onLoad() {
    if (this.permissionCode) {
      if (!utils_auth.guardPageAccess(this.permissionCode)) {
        return;
      }
    }
    this.canShowContent = true;
    if (this.$options.originalOnLoad) {
      this.$options.originalOnLoad.call(this);
    }
  },
  // 创建前保存原始onLoad方法
  beforeCreate() {
    if (this.$options.onLoad && this.$options.onLoad !== this.$options.mixins[0].onLoad) {
      this.$options.originalOnLoad = this.$options.onLoad;
    }
  }
};
exports.permissionMixin = permissionMixin;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/permission-mixin.js.map
