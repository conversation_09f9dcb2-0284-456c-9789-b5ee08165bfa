<view class="transfer-container"><view class="form-card"><view class="form-title">转派工单</view><view class="form-item"><view class="form-label required">转派原因</view><block wx:if="{{r0}}"><textarea class="form-textarea" placeholder="请输入转派原因" value="{{a}}" bindinput="{{b}}"></textarea></block></view><view class="form-item"><view class="form-label required">转派人员</view><view class="picker-box" bindtap="{{e}}"><text class="{{['picker-text', d && 'placeholder']}}">{{c}}</text><text class="iconfont icon-right"></text></view></view></view><view class="action-buttons"><button class="btn-cancel" bindtap="{{f}}">取消</button><button class="btn-submit" bindtap="{{g}}">确认转派</button></view><uni-popup wx:if="{{r}}" class="r" u-s="{{['d']}}" u-r="userPopup" u-i="d1add81a-0" bind:__l="__l" u-p="{{r}}"><view class="popup-container"><view class="popup-header"><text class="cancel" bindtap="{{h}}">取消</text><text class="title">选择转派人员</text><text class="confirm" bindtap="{{i}}">确认</text></view><view class="search-box"><view class="search-input-wrap"><text class="iconfont icon-search"></text><input type="text" class="search-input" placeholder="搜索姓名或手机号" confirm-type="search" bindinput="{{j}}" value="{{k}}"/><text wx:if="{{l}}" class="iconfont icon-close" bindtap="{{m}}"></text></view></view><view class="popup-body"><view wx:for="{{n}}" wx:for-item="user" wx:key="d" class="{{['user-item', user.e && 'active']}}" bindtap="{{user.f}}"><view class="user-info"><text class="user-name">{{user.a}}</text><text class="user-phone">{{user.b}}</text></view><view wx:if="{{user.c}}" class="select-indicator"><text class="select-text">已选</text><text class="iconfont icon-check"></text></view></view><view wx:if="{{o}}" class="empty-tip">{{p}}</view></view></view></uni-popup></view>