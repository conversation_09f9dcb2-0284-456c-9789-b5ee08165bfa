{"version": 3, "file": "uni-transition.js", "sources": ["node_modules/@dcloudio/uni-ui/lib/uni-transition/uni-transition.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RTovdGFpYm9fY29tcGFueS90Yl9wcm9qZWN0L3NoYWFueGlfamllbWluZ19uZXdfZW5lcmd5X2NvbXBhbnkvNC1Tb3VyY2UvYXBwL2Zyb250ZW5kL2hlYXRpbmdfbWFpbnRlbmFuY2Uvbm9kZV9tb2R1bGVzL0BkY2xvdWRpby91bmktdWkvbGliL3VuaS10cmFuc2l0aW9uL3VuaS10cmFuc2l0aW9uLnZ1ZQ"], "sourcesContent": ["<template>\n  <!-- #ifndef APP-NVUE -->\n  <view v-show=\"isShow\" ref=\"ani\" :animation=\"animationData\" :class=\"customClass\" :style=\"transformStyles\" @click=\"onClick\"><slot></slot></view>\n  <!-- #endif -->\n  <!-- #ifdef APP-NVUE -->\n  <view v-if=\"isShow\" ref=\"ani\" :animation=\"animationData\" :class=\"customClass\" :style=\"transformStyles\" @click=\"onClick\"><slot></slot></view>\n  <!-- #endif -->\n</template>\n\n<script>\nimport { createAnimation } from './createAnimation'\n\n/**\n * Transition 过渡动画\n * @description 简单过渡动画组件\n * @tutorial https://ext.dcloud.net.cn/plugin?id=985\n * @property {Boolean} show = [false|true] 控制组件显示或隐藏\n * @property {Array|String} modeClass = [fade|slide-top|slide-right|slide-bottom|slide-left|zoom-in|zoom-out] 过渡动画类型\n *  @value fade 渐隐渐出过渡\n *  @value slide-top 由上至下过渡\n *  @value slide-right 由右至左过渡\n *  @value slide-bottom 由下至上过渡\n *  @value slide-left 由左至右过渡\n *  @value zoom-in 由小到大过渡\n *  @value zoom-out 由大到小过渡\n * @property {Number} duration 过渡动画持续时间\n * @property {Object} styles 组件样式，同 css 样式，注意带’-‘连接符的属性需要使用小驼峰写法如：`backgroundColor:red`\n */\nexport default {\n\tname: 'uniTransition',\n\temits:['click','change'],\n\tprops: {\n\t\tshow: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\tmodeClass: {\n\t\t\ttype: [Array, String],\n\t\t\tdefault() {\n\t\t\t\treturn 'fade'\n\t\t\t}\n\t\t},\n\t\tduration: {\n\t\t\ttype: Number,\n\t\t\tdefault: 300\n\t\t},\n\t\tstyles: {\n\t\t\ttype: Object,\n\t\t\tdefault() {\n\t\t\t\treturn {}\n\t\t\t}\n\t\t},\n\t\tcustomClass:{\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\tonceRender:{\n\t\t\ttype:Boolean,\n\t\t\tdefault:false\n\t\t},\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tisShow: false,\n\t\t\ttransform: '',\n\t\t\topacity: 1,\n\t\t\tanimationData: {},\n\t\t\tdurationTime: 300,\n\t\t\tconfig: {}\n\t\t}\n\t},\n\twatch: {\n\t\tshow: {\n\t\t\thandler(newVal) {\n\t\t\t\tif (newVal) {\n\t\t\t\t\tthis.open()\n\t\t\t\t} else {\n\t\t\t\t\t// 避免上来就执行 close,导致动画错乱\n\t\t\t\t\tif (this.isShow) {\n\t\t\t\t\t\tthis.close()\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\timmediate: true\n\t\t}\n\t},\n\tcomputed: {\n\t\t// 生成样式数据\n\t\tstylesObject() {\n\t\t\tlet styles = {\n\t\t\t\t...this.styles,\n\t\t\t\t'transition-duration': this.duration / 1000 + 's'\n\t\t\t}\n\t\t\tlet transform = ''\n\t\t\tfor (let i in styles) {\n\t\t\t\tlet line = this.toLine(i)\n\t\t\t\ttransform += line + ':' + styles[i] + ';'\n\t\t\t}\n\t\t\treturn transform\n\t\t},\n\t\t// 初始化动画条件\n\t\ttransformStyles() {\n\t\t\treturn 'transform:' + this.transform + ';' + 'opacity:' + this.opacity + ';' + this.stylesObject\n\t\t}\n\t},\n\tcreated() {\n\t\t// 动画默认配置\n\t\tthis.config = {\n\t\t\tduration: this.duration,\n\t\t\ttimingFunction: 'ease',\n\t\t\ttransformOrigin: '50% 50%',\n\t\t\tdelay: 0\n\t\t}\n\t\tthis.durationTime = this.duration\n\t},\n\tmethods: {\n\t\t/**\n\t\t *  ref 触发 初始化动画\n\t\t */\n\t\tinit(obj = {}) {\n\t\t\tif (obj.duration) {\n\t\t\t\tthis.durationTime = obj.duration\n\t\t\t}\n\t\t\tthis.animation = createAnimation(Object.assign(this.config, obj),this)\n\t\t},\n\t\t/**\n\t\t * 点击组件触发回调\n\t\t */\n\t\tonClick() {\n\t\t\tthis.$emit('click', {\n\t\t\t\tdetail: this.isShow\n\t\t\t})\n\t\t},\n\t\t/**\n\t\t * ref 触发 动画分组\n\t\t * @param {Object} obj\n\t\t */\n\t\tstep(obj, config = {}) {\n\t\t\tif (!this.animation) return\n\t\t\tfor (let i in obj) {\n\t\t\t\ttry {\n\t\t\t\t\tif(typeof obj[i] === 'object'){\n\t\t\t\t\t\tthis.animation[i](...obj[i])\n\t\t\t\t\t}else{\n\t\t\t\t\t\tthis.animation[i](obj[i])\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error(`方法 ${i} 不存在`)\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis.animation.step(config)\n\t\t\treturn this\n\t\t},\n\t\t/**\n\t\t *  ref 触发 执行动画\n\t\t */\n\t\trun(fn) {\n\t\t\tif (!this.animation) return\n\t\t\tthis.animation.run(fn)\n\t\t},\n\t\t// 开始过度动画\n\t\topen() {\n\t\t\tclearTimeout(this.timer)\n\t\t\tthis.transform = ''\n\t\t\tthis.isShow = true\n\t\t\tlet { opacity, transform } = this.styleInit(false)\n\t\t\tif (typeof opacity !== 'undefined') {\n\t\t\t\tthis.opacity = opacity\n\t\t\t}\n\t\t\tthis.transform = transform\n\t\t\t// 确保动态样式已经生效后，执行动画，如果不加 nextTick ，会导致 wx 动画执行异常\n\t\t\tthis.$nextTick(() => {\n\t\t\t\t// TODO 定时器保证动画完全执行，目前有些问题，后面会取消定时器\n\t\t\t\tthis.timer = setTimeout(() => {\n\t\t\t\t\tthis.animation = createAnimation(this.config, this)\n\t\t\t\t\tthis.tranfromInit(false).step()\n\t\t\t\t\tthis.animation.run()\n\t\t\t\t\tthis.$emit('change', {\n\t\t\t\t\t\tdetail: this.isShow\n\t\t\t\t\t})\n\t\t\t\t}, 20)\n\t\t\t})\n\t\t},\n\t\t// 关闭过度动画\n\t\tclose(type) {\n\t\t\tif (!this.animation) return\n\t\t\tthis.tranfromInit(true)\n\t\t\t\t.step()\n\t\t\t\t.run(() => {\n\t\t\t\t\tthis.isShow = false\n\t\t\t\t\tthis.animationData = null\n\t\t\t\t\tthis.animation = null\n\t\t\t\t\tlet { opacity, transform } = this.styleInit(false)\n\t\t\t\t\tthis.opacity = opacity || 1\n\t\t\t\t\tthis.transform = transform\n\t\t\t\t\tthis.$emit('change', {\n\t\t\t\t\t\tdetail: this.isShow\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t},\n\t\t// 处理动画开始前的默认样式\n\t\tstyleInit(type) {\n\t\t\tlet styles = {\n\t\t\t\ttransform: ''\n\t\t\t}\n\t\t\tlet buildStyle = (type, mode) => {\n\t\t\t\tif (mode === 'fade') {\n\t\t\t\t\tstyles.opacity = this.animationType(type)[mode]\n\t\t\t\t} else {\n\t\t\t\t\tstyles.transform += this.animationType(type)[mode] + ' '\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (typeof this.modeClass === 'string') {\n\t\t\t\tbuildStyle(type, this.modeClass)\n\t\t\t} else {\n\t\t\t\tthis.modeClass.forEach(mode => {\n\t\t\t\t\tbuildStyle(type, mode)\n\t\t\t\t})\n\t\t\t}\n\t\t\treturn styles\n\t\t},\n\t\t// 处理内置组合动画\n\t\ttranfromInit(type) {\n\t\t\tlet buildTranfrom = (type, mode) => {\n\t\t\t\tlet aniNum = null\n\t\t\t\tif (mode === 'fade') {\n\t\t\t\t\taniNum = type ? 0 : 1\n\t\t\t\t} else {\n\t\t\t\t\taniNum = type ? '-100%' : '0'\n\t\t\t\t\tif (mode === 'zoom-in') {\n\t\t\t\t\t\taniNum = type ? 0.8 : 1\n\t\t\t\t\t}\n\t\t\t\t\tif (mode === 'zoom-out') {\n\t\t\t\t\t\taniNum = type ? 1.2 : 1\n\t\t\t\t\t}\n\t\t\t\t\tif (mode === 'slide-right') {\n\t\t\t\t\t\taniNum = type ? '100%' : '0'\n\t\t\t\t\t}\n\t\t\t\t\tif (mode === 'slide-bottom') {\n\t\t\t\t\t\taniNum = type ? '100%' : '0'\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.animation[this.animationMode()[mode]](aniNum)\n\t\t\t}\n\t\t\tif (typeof this.modeClass === 'string') {\n\t\t\t\tbuildTranfrom(type, this.modeClass)\n\t\t\t} else {\n\t\t\t\tthis.modeClass.forEach(mode => {\n\t\t\t\t\tbuildTranfrom(type, mode)\n\t\t\t\t})\n\t\t\t}\n\n\t\t\treturn this.animation\n\t\t},\n\t\tanimationType(type) {\n\t\t\treturn {\n\t\t\t\tfade: type ? 0 : 1,\n\t\t\t\t'slide-top': `translateY(${type ? '0' : '-100%'})`,\n\t\t\t\t'slide-right': `translateX(${type ? '0' : '100%'})`,\n\t\t\t\t'slide-bottom': `translateY(${type ? '0' : '100%'})`,\n\t\t\t\t'slide-left': `translateX(${type ? '0' : '-100%'})`,\n\t\t\t\t'zoom-in': `scaleX(${type ? 1 : 0.8}) scaleY(${type ? 1 : 0.8})`,\n\t\t\t\t'zoom-out': `scaleX(${type ? 1 : 1.2}) scaleY(${type ? 1 : 1.2})`\n\t\t\t}\n\t\t},\n\t\t// 内置动画类型与实际动画对应字典\n\t\tanimationMode() {\n\t\t\treturn {\n\t\t\t\tfade: 'opacity',\n\t\t\t\t'slide-top': 'translateY',\n\t\t\t\t'slide-right': 'translateX',\n\t\t\t\t'slide-bottom': 'translateY',\n\t\t\t\t'slide-left': 'translateX',\n\t\t\t\t'zoom-in': 'scale',\n\t\t\t\t'zoom-out': 'scale'\n\t\t\t}\n\t\t},\n\t\t// 驼峰转中横线\n\t\ttoLine(name) {\n\t\t\treturn name.replace(/([A-Z])/g, '-$1').toLowerCase()\n\t\t}\n\t}\n}\n</script>\n\n<style></style>\n", "import Component from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/node_modules/@dcloudio/uni-ui/lib/uni-transition/uni-transition.vue'\nwx.createComponent(Component)"], "names": ["createAnimation", "uni", "type"], "mappings": ";;AA4BA,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,OAAM,CAAC,SAAQ,QAAQ;AAAA,EACvB,OAAO;AAAA,IACN,MAAM;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,WAAW;AAAA,MACV,MAAM,CAAC,OAAO,MAAM;AAAA,MACpB,UAAU;AACT,eAAO;AAAA,MACR;AAAA,IACA;AAAA,IACD,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,QAAQ;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AACT,eAAO,CAAC;AAAA,MACT;AAAA,IACA;AAAA,IACD,aAAY;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,YAAW;AAAA,MACV,MAAK;AAAA,MACL,SAAQ;AAAA,IACR;AAAA,EACD;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,SAAS;AAAA,MACT,eAAe,CAAE;AAAA,MACjB,cAAc;AAAA,MACd,QAAQ,CAAC;AAAA,IACV;AAAA,EACA;AAAA,EACD,OAAO;AAAA,IACN,MAAM;AAAA,MACL,QAAQ,QAAQ;AACf,YAAI,QAAQ;AACX,eAAK,KAAK;AAAA,eACJ;AAEN,cAAI,KAAK,QAAQ;AAChB,iBAAK,MAAM;AAAA,UACZ;AAAA,QACD;AAAA,MACA;AAAA,MACD,WAAW;AAAA,IACZ;AAAA,EACA;AAAA,EACD,UAAU;AAAA;AAAA,IAET,eAAe;AACd,UAAI,SAAS;AAAA,QACZ,GAAG,KAAK;AAAA,QACR,uBAAuB,KAAK,WAAW,MAAO;AAAA,MAC/C;AACA,UAAI,YAAY;AAChB,eAAS,KAAK,QAAQ;AACrB,YAAI,OAAO,KAAK,OAAO,CAAC;AACxB,qBAAa,OAAO,MAAM,OAAO,CAAC,IAAI;AAAA,MACvC;AACA,aAAO;AAAA,IACP;AAAA;AAAA,IAED,kBAAkB;AACjB,aAAO,eAAe,KAAK,YAAY,cAAmB,KAAK,UAAU,MAAM,KAAK;AAAA,IACrF;AAAA,EACA;AAAA,EACD,UAAU;AAET,SAAK,SAAS;AAAA,MACb,UAAU,KAAK;AAAA,MACf,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,OAAO;AAAA,IACR;AACA,SAAK,eAAe,KAAK;AAAA,EACzB;AAAA,EACD,SAAS;AAAA;AAAA;AAAA;AAAA,IAIR,KAAK,MAAM,IAAI;AACd,UAAI,IAAI,UAAU;AACjB,aAAK,eAAe,IAAI;AAAA,MACzB;AACA,WAAK,YAAYA,cAAe,gBAAC,OAAO,OAAO,KAAK,QAAQ,GAAG,GAAE,IAAI;AAAA,IACrE;AAAA;AAAA;AAAA;AAAA,IAID,UAAU;AACT,WAAK,MAAM,SAAS;AAAA,QACnB,QAAQ,KAAK;AAAA,OACb;AAAA,IACD;AAAA;AAAA;AAAA;AAAA;AAAA,IAKD,KAAK,KAAK,SAAS,IAAI;AACtB,UAAI,CAAC,KAAK;AAAW;AACrB,eAAS,KAAK,KAAK;AAClB,YAAI;AACH,cAAG,OAAO,IAAI,CAAC,MAAM,UAAS;AAC7B,iBAAK,UAAU,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;AAAA,iBACvB;AACJ,iBAAK,UAAU,CAAC,EAAE,IAAI,CAAC,CAAC;AAAA,UACzB;AAAA,QACD,SAAS,GAAG;AACXC,wBAAc,MAAA,MAAA,SAAA,8EAAA,MAAM,CAAC,MAAM;AAAA,QAC5B;AAAA,MACD;AACA,WAAK,UAAU,KAAK,MAAM;AAC1B,aAAO;AAAA,IACP;AAAA;AAAA;AAAA;AAAA,IAID,IAAI,IAAI;AACP,UAAI,CAAC,KAAK;AAAW;AACrB,WAAK,UAAU,IAAI,EAAE;AAAA,IACrB;AAAA;AAAA,IAED,OAAO;AACN,mBAAa,KAAK,KAAK;AACvB,WAAK,YAAY;AACjB,WAAK,SAAS;AACd,UAAI,EAAE,SAAS,UAAQ,IAAM,KAAK,UAAU,KAAK;AACjD,UAAI,OAAO,YAAY,aAAa;AACnC,aAAK,UAAU;AAAA,MAChB;AACA,WAAK,YAAY;AAEjB,WAAK,UAAU,MAAM;AAEpB,aAAK,QAAQ,WAAW,MAAM;AAC7B,eAAK,YAAYD,cAAAA,gBAAgB,KAAK,QAAQ,IAAI;AAClD,eAAK,aAAa,KAAK,EAAE,KAAK;AAC9B,eAAK,UAAU,IAAI;AACnB,eAAK,MAAM,UAAU;AAAA,YACpB,QAAQ,KAAK;AAAA,WACb;AAAA,QACD,GAAE,EAAE;AAAA,OACL;AAAA,IACD;AAAA;AAAA,IAED,MAAM,MAAM;AACX,UAAI,CAAC,KAAK;AAAW;AACrB,WAAK,aAAa,IAAI,EACpB,KAAK,EACL,IAAI,MAAM;AACV,aAAK,SAAS;AACd,aAAK,gBAAgB;AACrB,aAAK,YAAY;AACjB,YAAI,EAAE,SAAS,UAAQ,IAAM,KAAK,UAAU,KAAK;AACjD,aAAK,UAAU,WAAW;AAC1B,aAAK,YAAY;AACjB,aAAK,MAAM,UAAU;AAAA,UACpB,QAAQ,KAAK;AAAA,SACb;AAAA,OACD;AAAA,IACF;AAAA;AAAA,IAED,UAAU,MAAM;AACf,UAAI,SAAS;AAAA,QACZ,WAAW;AAAA,MACZ;AACA,UAAI,aAAa,CAACE,OAAM,SAAS;AAChC,YAAI,SAAS,QAAQ;AACpB,iBAAO,UAAU,KAAK,cAAcA,KAAI,EAAE,IAAI;AAAA,eACxC;AACN,iBAAO,aAAa,KAAK,cAAcA,KAAI,EAAE,IAAI,IAAI;AAAA,QACtD;AAAA,MACD;AACA,UAAI,OAAO,KAAK,cAAc,UAAU;AACvC,mBAAW,MAAM,KAAK,SAAS;AAAA,aACzB;AACN,aAAK,UAAU,QAAQ,UAAQ;AAC9B,qBAAW,MAAM,IAAI;AAAA,SACrB;AAAA,MACF;AACA,aAAO;AAAA,IACP;AAAA;AAAA,IAED,aAAa,MAAM;AAClB,UAAI,gBAAgB,CAACA,OAAM,SAAS;AACnC,YAAI,SAAS;AACb,YAAI,SAAS,QAAQ;AACpB,mBAASA,QAAO,IAAI;AAAA,eACd;AACN,mBAASA,QAAO,UAAU;AAC1B,cAAI,SAAS,WAAW;AACvB,qBAASA,QAAO,MAAM;AAAA,UACvB;AACA,cAAI,SAAS,YAAY;AACxB,qBAASA,QAAO,MAAM;AAAA,UACvB;AACA,cAAI,SAAS,eAAe;AAC3B,qBAASA,QAAO,SAAS;AAAA,UAC1B;AACA,cAAI,SAAS,gBAAgB;AAC5B,qBAASA,QAAO,SAAS;AAAA,UAC1B;AAAA,QACD;AACA,aAAK,UAAU,KAAK,cAAe,EAAC,IAAI,CAAC,EAAE,MAAM;AAAA,MAClD;AACA,UAAI,OAAO,KAAK,cAAc,UAAU;AACvC,sBAAc,MAAM,KAAK,SAAS;AAAA,aAC5B;AACN,aAAK,UAAU,QAAQ,UAAQ;AAC9B,wBAAc,MAAM,IAAI;AAAA,SACxB;AAAA,MACF;AAEA,aAAO,KAAK;AAAA,IACZ;AAAA,IACD,cAAc,MAAM;AACnB,aAAO;AAAA,QACN,MAAM,OAAO,IAAI;AAAA,QACjB,aAAa,cAAc,OAAO,MAAM,OAAO;AAAA,QAC/C,eAAe,cAAc,OAAO,MAAM,MAAM;AAAA,QAChD,gBAAgB,cAAc,OAAO,MAAM,MAAM;AAAA,QACjD,cAAc,cAAc,OAAO,MAAM,OAAO;AAAA,QAChD,WAAW,UAAU,OAAO,IAAI,GAAG,YAAY,OAAO,IAAI,GAAG;AAAA,QAC7D,YAAY,UAAU,OAAO,IAAI,GAAG,YAAY,OAAO,IAAI,GAAG;AAAA,MAC/D;AAAA,IACA;AAAA;AAAA,IAED,gBAAgB;AACf,aAAO;AAAA,QACN,MAAM;AAAA,QACN,aAAa;AAAA,QACb,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,cAAc;AAAA,QACd,WAAW;AAAA,QACX,YAAY;AAAA,MACb;AAAA,IACA;AAAA;AAAA,IAED,OAAO,MAAM;AACZ,aAAO,KAAK,QAAQ,YAAY,KAAK,EAAE,YAAY;AAAA,IACpD;AAAA,EACD;AACD;;;;;;;;;;;ACzRA,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}