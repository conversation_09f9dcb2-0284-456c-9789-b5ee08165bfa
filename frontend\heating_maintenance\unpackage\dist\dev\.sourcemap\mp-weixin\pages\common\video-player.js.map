{"version": 3, "file": "video-player.js", "sources": ["pages/common/video-player.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY29tbW9uL3ZpZGVvLXBsYXllci52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"video-player-container\">\r\n\t\t<view class=\"video-wrapper\">\r\n\t\t\t<video \r\n\t\t\t\t:src=\"videoUrl\" \r\n\t\t\t\tcontrols \r\n\t\t\t\tautoplay\r\n\t\t\t\tobject-fit=\"contain\"\r\n\t\t\t\t@error=\"onVideoError\"\r\n\t\t\t\t@fullscreenchange=\"onFullscreenChange\"\r\n\t\t\t\tshow-center-play-btn=\"true\"\r\n\t\t\t\tshow-loading=\"true\"\r\n\t\t\t\tenable-play-gesture=\"true\"\r\n\t\t\t\tenable-progress-gesture=\"true\"\r\n\t\t\t\tcodec=\"hardware\"\r\n\t\t\t\tvslide-gesture=\"true\"\r\n\t\t\t\tvslide-gesture-in-fullscreen=\"true\"\r\n\t\t\t\tplay-btn-position=\"center\"\r\n\t\t\t\tdirection=\"0\"\r\n\t\t\t\tid=\"videoPlayer\"\r\n\t\t\t></video>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- <view class=\"controls\">\r\n\t\t\t<button class=\"btn-back\" @click=\"goBack\">返回</button>\r\n\t\t\t<button class=\"btn-open-browser\" @click=\"openInBrowser\">在浏览器中打开</button>\r\n\t\t</view>\r\n\t\t -->\r\n\t\t<view class=\"error-message\" v-if=\"loadError\">\r\n\t\t\t<text>视频加载失败，请尝试在浏览器中打开</text>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * 通用视频播放器页面\r\n\t * 支持从URL参数接收视频地址并播放\r\n\t */\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tvideoUrl: '',\r\n\t\t\t\tloadError: false,\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\t// 设置状态栏为透明\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\tplus.navigator.setStatusBarStyle('light');\r\n\t\t\tplus.navigator.setStatusBarBackground('#000000');\r\n\t\t\t// #endif\r\n\t\t\t\r\n\t\t\t// 从URL参数获取视频地址\r\n\t\t\tif (options.url || options.src) {\r\n\t\t\t\t// 解码URL\r\n\t\t\t\tthis.videoUrl = decodeURIComponent(options.url || options.src);\r\n\t\t\t\tconsole.log('视频播放器: 加载视频URL:', this.videoUrl);\r\n\t\t\t\t\r\n\t\t\t\t// 设置页面标题\r\n\t\t\t\tif (options.title) {\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: options.title\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 设置导航栏背景为黑色\r\n\t\t\t\tuni.setNavigationBarColor({\r\n\t\t\t\t\tfrontColor: '#ffffff',\r\n\t\t\t\t\tbackgroundColor: '#000000'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 预加载视频\r\n\t\t\t\tthis.preloadVideo(this.videoUrl);\r\n\t\t\t} else {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '未提供视频地址',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tuni.navigateBack();\r\n\t\t\t\t}, 1500);\r\n\t\t\t}\r\n\t\t},\r\n\t\tonReady() {\r\n\t\t\t// 获取视频上下文\r\n\t\t\tthis.videoContext = uni.createVideoContext('videoPlayer', this);\r\n\t\t\t\r\n\t\t\t// 设置页面背景为黑色\r\n\t\t\t// #ifdef H5\r\n\t\t\tdocument.body.style.backgroundColor = '#000000';\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\t// 页面显示时尝试播放视频\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tif (this.videoContext) {\r\n\t\t\t\t\tthis.videoContext.play();\r\n\t\t\t\t}\r\n\t\t\t}, 300);\r\n\t\t},\r\n\t\tonHide() {\r\n\t\t\t// 页面隐藏时暂停视频\r\n\t\t\tif (this.videoContext) {\r\n\t\t\t\tthis.videoContext.pause();\r\n\t\t\t}\r\n\t\t},\r\n\t\tonUnload() {\r\n\t\t\t// 页面卸载时停止视频\r\n\t\t\tif (this.videoContext) {\r\n\t\t\t\tthis.videoContext.stop();\r\n\t\t\t\tthis.videoContext = null;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 恢复页面背景色\r\n\t\t\t// #ifdef H5\r\n\t\t\tdocument.body.style.backgroundColor = '';\r\n\t\t\t// #endif\r\n\t\t\t\r\n\t\t\t// 恢复状态栏样式\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\tplus.navigator.setStatusBarStyle('dark');\r\n\t\t\tplus.navigator.setStatusBarBackground('#ffffff');\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 视频加载错误处理\r\n\t\t\tonVideoError(e) {\r\n\t\t\t\tconsole.error('视频播放器: 视频加载失败:', e.detail);\r\n\t\t\t\tthis.loadError = true;\r\n\t\t\t\t\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '视频加载失败，请尝试其他方式查看',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 处理全屏状态变化\r\n\t\t\tonFullscreenChange(e) {\r\n\t\t\t\tconsole.log('视频全屏状态变化:', e.detail.fullScreen);\r\n\t\t\t\t\r\n\t\t\t\t// 在退出全屏时，可能需要进行一些调整\r\n\t\t\t\tif (!e.detail.fullScreen) {\r\n\t\t\t\t\t// 一些设备上可能需要手动调整布局\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t// 刷新视频组件布局\r\n\t\t\t\t\t\tif (this.videoContext) {\r\n\t\t\t\t\t\t\tthis.videoContext.play();\r\n\t\t\t\t\t\t\tsetTimeout(() => this.videoContext.pause(), 10);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 100);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 返回上一页\r\n\t\t\tgoBack() {\r\n\t\t\t\tuni.navigateBack();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 预加载视频\r\n\t\t\tpreloadVideo(url) {\r\n\t\t\t\t// 创建Image对象预加载视频首帧（对于一些平台可能有效）\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tconst img = new Image();\r\n\t\t\t\timg.src = url;\r\n\t\t\t\t// #endif\r\n\t\t\t\t\r\n\t\t\t\t// 在APP中预热视频播放引擎\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tif (this.videoContext) {\r\n\t\t\t\t\t\tthis.videoContext.seek(0);\r\n\t\t\t\t\t\tthis.videoContext.pause();\r\n\t\t\t\t\t}\r\n\t\t\t\t}, 100);\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 在浏览器中打开\r\n\t\t\topenInBrowser() {\r\n\t\t\t\tif (!this.videoUrl) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '视频URL无效',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tconsole.log('在浏览器中打开视频:', this.videoUrl);\r\n\t\t\t\t\r\n\t\t\t\t// 使用系统浏览器打开\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tplus.runtime.openURL(this.videoUrl, (err) => {\r\n\t\t\t\t\tif (err) {\r\n\t\t\t\t\t\tconsole.error('打开浏览器失败:', err);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '打开浏览器失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t\t\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\twindow.open(this.videoUrl, '_blank');\r\n\t\t\t\t// #endif\r\n\t\t\t\t\r\n\t\t\t\t// #ifdef MP\r\n\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\tdata: this.videoUrl,\r\n\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: 'URL已复制，请在浏览器中打开',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t/* 修改页面样式以消除顶部的白色间隙 */\r\n\tpage {\r\n\t\tbackground-color: #000000;\r\n\t\theight: 100%;\r\n\t\toverflow: hidden;\r\n\t\tmargin: 0;\r\n\t\tpadding: 0;\r\n\t}\r\n\r\n\t/* 隐藏默认导航栏的可能空间 */\r\n\t.uni-page-head {\r\n\t\tdisplay: none;\r\n\t}\r\n\t\r\n\t.uni-page-wrapper,\r\n\t.uni-page-body {\r\n\t\theight: 100%;\r\n\t\tbackground-color: #000000 !important;\r\n\t\toverflow: hidden;\r\n\t\tposition: relative;\r\n\t\tz-index: 1;\r\n\t}\r\n\r\n\t.video-player-container {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\twidth: 100%;\r\n\t\theight: 100vh;\r\n\t\tbackground-color: #000;\r\n\t\toverflow: hidden;\r\n\t\tz-index: 10;\r\n\t\t\r\n\t\t.video-wrapper {\r\n\t\t\tflex: 1;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\t\r\n\t\t\tvideo {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tmax-height: 100%;\r\n\t\t\t\tobject-fit: contain;\r\n\t\t\t\tz-index: 1;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.controls {\r\n\t\t\tdisplay: flex;\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tbackground-color: #222;\r\n\t\t\tgap: 30rpx;\r\n\t\t\t\r\n\t\t\tbutton {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\tpadding: 20rpx 0;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\r\n\t\t\t\t&.btn-back {\r\n\t\t\t\t\tbackground-color: #444;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&.btn-open-browser {\r\n\t\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.error-message {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 50%;\r\n\t\t\tleft: 50%;\r\n\t\t\ttransform: translate(-50%, -50%);\r\n\t\t\tbackground-color: rgba(0, 0, 0, 0.7);\r\n\t\t\tpadding: 20rpx 40rpx;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tz-index: 2;\r\n\t\t\t\r\n\t\t\ttext {\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style> ", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/common/video-player.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAuCC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,UAAU;AAAA,MACV,WAAW;AAAA,IACZ;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AAQf,QAAI,QAAQ,OAAO,QAAQ,KAAK;AAE/B,WAAK,WAAW,mBAAmB,QAAQ,OAAO,QAAQ,GAAG;AAC7DA,oBAAY,MAAA,MAAA,OAAA,uCAAA,mBAAmB,KAAK,QAAQ;AAG5C,UAAI,QAAQ,OAAO;AAClBA,sBAAAA,MAAI,sBAAsB;AAAA,UACzB,OAAO,QAAQ;AAAA,QAChB,CAAC;AAAA,MACF;AAGAA,oBAAAA,MAAI,sBAAsB;AAAA,QACzB,YAAY;AAAA,QACZ,iBAAiB;AAAA,MAClB,CAAC;AAGD,WAAK,aAAa,KAAK,QAAQ;AAAA,WACzB;AACNA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AACD,iBAAW,MAAM;AAChBA,sBAAG,MAAC,aAAY;AAAA,MAChB,GAAE,IAAI;AAAA,IACR;AAAA,EACA;AAAA,EACD,UAAU;AAET,SAAK,eAAeA,cAAG,MAAC,mBAAmB,eAAe,IAAI;AAAA,EAM9D;AAAA,EACD,SAAS;AAER,eAAW,MAAM;AAChB,UAAI,KAAK,cAAc;AACtB,aAAK,aAAa;MACnB;AAAA,IACA,GAAE,GAAG;AAAA,EACN;AAAA,EACD,SAAS;AAER,QAAI,KAAK,cAAc;AACtB,WAAK,aAAa;IACnB;AAAA,EACA;AAAA,EACD,WAAW;AAEV,QAAI,KAAK,cAAc;AACtB,WAAK,aAAa;AAClB,WAAK,eAAe;AAAA,IACrB;AAAA,EAYA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,aAAa,GAAG;AACfA,oBAAA,MAAA,MAAA,SAAA,wCAAc,kBAAkB,EAAE,MAAM;AACxC,WAAK,YAAY;AAEjBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACX,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,mBAAmB,GAAG;AACrBA,0BAAY,MAAA,OAAA,wCAAA,aAAa,EAAE,OAAO,UAAU;AAG5C,UAAI,CAAC,EAAE,OAAO,YAAY;AAEzB,mBAAW,MAAM;AAEhB,cAAI,KAAK,cAAc;AACtB,iBAAK,aAAa;AAClB,uBAAW,MAAM,KAAK,aAAa,MAAO,GAAE,EAAE;AAAA,UAC/C;AAAA,QACA,GAAE,GAAG;AAAA,MACP;AAAA,IACA;AAAA;AAAA,IAGD,SAAS;AACRA,oBAAG,MAAC,aAAY;AAAA,IAChB;AAAA;AAAA,IAGD,aAAa,KAAK;AAAA,IAgBjB;AAAA;AAAA,IAGD,gBAAgB;AACf,UAAI,CAAC,KAAK,UAAU;AACnBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEAA,oBAAA,MAAA,MAAA,OAAA,wCAAY,cAAc,KAAK,QAAQ;AAoBvCA,oBAAAA,MAAI,iBAAiB;AAAA,QACpB,MAAM,KAAK;AAAA,QACX,SAAS,MAAM;AACdA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AAAA,IAEF;AAAA,EACD;AACD;;;;;;;;;;AC5ND,GAAG,WAAW,eAAe;"}