{"version": 3, "file": "report.js", "sources": ["pages/fault/report.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZmF1bHQvcmVwb3J0LnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"fault-report-container\">\r\n\t\t<!-- 表单内容 -->\r\n\t\t<view class=\"form-card\">\r\n\t\t\t<view class=\"form-group\"> \r\n\t\t\t\t\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<text class=\"form-label required\">热用户</text>\r\n\t\t\t\t\t<view class=\"form-picker-container\">\r\n\t\t\t\t\t\t<picker class=\"form-picker\" @change=\"handleUnitChange\" :value=\"unitIndex\" :range=\"heatUnits\" range-key=\"name\">\r\n\t\t\t\t\t\t\t<view class=\"picker-text\" :class=\"{'placeholder': unitIndex === -1}\">\r\n\t\t\t\t\t\t\t\t{{ unitIndex === -1 ? '请选择热用户' : heatUnits[unitIndex].name }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<text class=\"form-label required\">详细住户</text>\r\n\t\t\t\t\t<view class=\"address-inputs\" :class=\"{'error': addressInputError}\">\r\n\t\t\t\t\t\t<view class=\"address-input-item\" :class=\"{'error': addressInputError}\">\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\tclass=\"form-input\"\r\n\t\t\t\t\t\t\t\ttype=\"number\"\r\n\t\t\t\t\t\t\t\tv-model=\"buildingNo\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"楼号\"\r\n\t\t\t\t\t\t\t\tmaxlength=\"2\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<text class=\"address-input-label\">号楼</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"address-separator\">-</text>\r\n\t\t\t\t\t\t<view class=\"address-input-item\" :class=\"{'error': addressInputError}\">\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\tclass=\"form-input\"\r\n\t\t\t\t\t\t\t\ttype=\"number\"\r\n\t\t\t\t\t\t\t\tv-model=\"unitNo\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"单元\"\r\n\t\t\t\t\t\t\t\tmaxlength=\"2\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<text class=\"address-input-label\">单元</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"address-separator\">-</text>\r\n\t\t\t\t\t\t<view class=\"address-input-item room-input\" :class=\"{'error': addressInputError}\">\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\tclass=\"form-input\"\r\n\t\t\t\t\t\t\t\ttype=\"number\"\r\n\t\t\t\t\t\t\t\tv-model=\"roomNo\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"房号\"\r\n\t\t\t\t\t\t\t\tmaxlength=\"4\"\r\n\t\t\t\t\t\t\t\t@blur=\"formatAddressFields\"\r\n\t\t\t\t\t\t\t\t@input=\"updateFormattedAddress\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<text class=\"address-input-label\">房号</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"address-hint\">请输入楼号、单元号和房号 (可选)</text>\r\n\t\t\t\t\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<text class=\"form-label required\">故障类型</text>\r\n\t\t\t\t\t<view class=\"form-picker-container\">\r\n\t\t\t\t\t\t<picker class=\"form-picker\" @change=\"handleTypeChange\" :value=\"typeIndex\" :range=\"faultTypes\">\r\n\t\t\t\t\t\t\t<view class=\"picker-text\" :class=\"{'placeholder': typeIndex === -1}\">\r\n\t\t\t\t\t\t\t\t{{ typeIndex === -1 ? '请选择故障类型' : faultTypes[typeIndex] }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<text class=\"form-label required\">故障等级</text>\r\n\t\t\t\t\t<view class=\"level-options\">\r\n\t\t\t\t\t\t<view \r\n\t\t\t\t\t\t\tclass=\"level-option\" \r\n\t\t\t\t\t\t\tv-for=\"(level, index) in faultLevels\" \r\n\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\t:class=\"{'active': faultLevel === level.value}\"\r\n\t\t\t\t\t\t\t@click=\"selectLevel(level.value)\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<text>{{ level.label }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<text class=\"form-label required\">故障描述</text>\r\n\t\t\t\t\t<textarea \r\n\t\t\t\t\t\tclass=\"form-textarea\" \r\n\t\t\t\t\t\tv-model=\"faultDesc\" \r\n\t\t\t\t\t\tplaceholder=\"请详细描述故障情况，便于维修人员了解问题\" \r\n\t\t\t\t\t\tmaxlength=\"200\"\r\n\t\t\t\t\t></textarea>\r\n\t\t\t\t\t<text class=\"textarea-counter\">{{ faultDesc.length }}/200</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 图片上传 -->\r\n\t\t<view class=\"form-card\">\r\n\t\t\t<view class=\"form-header\">\r\n\t\t\t\t<text class=\"form-title\">图片附件</text>\r\n\t\t\t\t<text class=\"form-subtitle\">上传故障现场图片（最多6张）</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"upload-area\">\r\n\t\t\t\t<view class=\"image-list\">\r\n\t\t\t\t\t<view class=\"image-item\" v-for=\"(image, index) in imageList\" :key=\"index\">\r\n\t\t\t\t\t\t<image :src=\"image\" mode=\"aspectFill\" @click=\"previewImage(index)\"></image>\r\n\t\t\t\t\t\t<text class=\"delete-icon\" @click=\"deleteImage(index)\">×</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"upload-item\" v-if=\"imageList.length < 6\" @click=\"chooseImage\">\r\n\t\t\t\t\t\t<text class=\"iconfont icon-camera\"></text>\r\n\t\t\t\t\t\t<text>上传图片</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 视频上传 -->\r\n\t\t<view class=\"form-card\">\r\n\t\t\t<view class=\"form-header\">\r\n\t\t\t\t<text class=\"form-title\">视频附件</text>\r\n\t\t\t\t<text class=\"form-subtitle\">上传故障现场视频（最多1个）</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"upload-area\">\r\n\t\t\t\t<view class=\"video-container\" v-if=\"videoPath\">\r\n\t\t\t\t\t<video \r\n\t\t\t\t\t\t:src=\"getVideoUrl()\" \r\n\t\t\t\t\t\tcontrols\r\n\t\t\t\t\t\tobject-fit=\"fill\"\r\n\t\t\t\t\t\tinitial-time=\"0\"\r\n\t\t\t\t\t\tshow-fullscreen-btn=\"true\"\r\n\t\t\t\t\t\tshow-play-btn=\"true\"\r\n\t\t\t\t\t\tshow-center-play-btn=\"true\"\r\n\t\t\t\t\t\tenable-progress-gesture=\"true\"\r\n\t\t\t\t\t\tauto-pause-if-navigate=\"true\"\r\n\t\t\t\t\t\tauto-pause-if-open-native=\"true\"\r\n\t\t\t\t\t\tcodec=\"h264\"\r\n\t\t\t\t\t\t@error=\"onVideoError\"\r\n\t\t\t\t\t\t@loadedmetadata=\"onVideoLoad\"\r\n\t\t\t\t\t></video>\r\n\t\t\t\t\t<text class=\"delete-icon\" @click=\"deleteVideo\">×</text>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 如果视频加载有问题，提供在浏览器打开的选项 -->\r\n\t\t\t\t\t<view class=\"video-actions\" v-if=\"serverVideoPath\">\r\n\t\t\t\t\t\t<view class=\"video-action-btn\" @click=\"openInBrowser\">\r\n\t\t\t\t\t\t\t在浏览器中打开\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"upload-item\" v-if=\"!videoPath\" @click=\"chooseVideo\">\r\n\t\t\t\t\t<text class=\"iconfont icon-video\"></text>\r\n\t\t\t\t\t<text>上传视频</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 发生时间 -->\r\n\t\t<view class=\"form-card\">\r\n\t\t\t<view class=\"form-group\">\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<text class=\"form-label required\">发生时间</text>\r\n\t\t\t\t\t<view class=\"form-picker-container\">\r\n\t\t\t\t\t\t<picker \r\n\t\t\t\t\t\t\tclass=\"form-picker\" \r\n\t\t\t\t\t\t\tmode=\"date\" \r\n\t\t\t\t\t\t\t:value=\"faultDate\" \r\n\t\t\t\t\t\t\t:start=\"startDate\" \r\n\t\t\t\t\t\t\t:end=\"endDate\" \r\n\t\t\t\t\t\t\t@change=\"handleDateChange\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<view class=\"picker-text\" :class=\"{'placeholder': !faultDate}\">\r\n\t\t\t\t\t\t\t\t{{ faultDate || '请选择日期' }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<text class=\"form-label\"></text>\r\n\t\t\t\t\t<view class=\"form-picker-container\">\r\n\t\t\t\t\t\t<picker \r\n\t\t\t\t\t\t\tclass=\"form-picker\" \r\n\t\t\t\t\t\t\tmode=\"time\" \r\n\t\t\t\t\t\t\t:value=\"faultTime\" \r\n\t\t\t\t\t\t\t@change=\"handleTimeChange\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<view class=\"picker-text\" :class=\"{'placeholder': !faultTime}\">\r\n\t\t\t\t\t\t\t\t{{ faultTime || '请选择时间' }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 底部提交按钮 -->\r\n\t\t<view class=\"submit-btn-container\">\r\n\t\t\t<button class=\"submit-btn\" @click=\"submitReport\">提交故障上报</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { faultApi, heatUnitApi,dictApi } from '@/utils/api.js';\r\n\timport uploadUtils from '@/utils/upload.js';\r\n\t\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\tconst now = new Date();\r\n\t\t\tconst year = now.getFullYear();\r\n\t\t\tconst month = (now.getMonth() + 1).toString().padStart(2, '0');\r\n\t\t\tconst day = now.getDate().toString().padStart(2, '0');\r\n\t\t\tconst hours = now.getHours().toString().padStart(2, '0');\r\n\t\t\tconst minutes = now.getMinutes().toString().padStart(2, '0');\r\n\t\t\t\r\n\t\t\treturn {\r\n\t\t\t\t// 故障类型\r\n\t\t\t\tfaultTypes: [],\r\n\t\t\t\ttypeIndex: -1,\r\n\t\t\t\t\r\n\t\t\t\t// 热力单元 \r\n\t\t\t\theatUnits: [],\r\n\t\t\t\tunitIndex: -1,\r\n\r\n\t\t\t\t// 告警ID 默认为 -1\r\n\t\t\t\talarmId:-1,\r\n\t\t\t\t\r\n\t\t\t\t// 故障来源\r\n\t\t\t\tfaultSources: ['用户投诉', '巡检上报'],\r\n\t\t\t\tsourceIndex: 0,  // 默认设置为\"用户投诉\"(索引0)\r\n\t\t\t\t\r\n\t\t\t\t// 故障等级 提示/一般/重要/严重\t\r\n\t\t\t\tfaultLevels: [\r\n\t\t\t\t\t{ label: '提示', value: '提示' },\r\n\t\t\t\t\t{ label: '一般', value: '一般' },\r\n\t\t\t\t\t{ label: '重要', value: '重要' },\r\n\t\t\t\t\t{ label: '严重', value: '严重' }\r\n\t\t\t\t],\r\n\t\t\t\tfaultLevel: '一般',\r\n\t\t\t\t\r\n\t\t\t\t// 故障描述\r\n\t\t\t\tfaultDesc: '',\r\n\t\t\t\t\r\n\t\t\t\t// 附件\r\n\t\t\t\timageList: [], // 本地临时路径\r\n\t\t\t\tserverImageList: [], // 服务器路径\r\n\t\t\t\tvideoPath: '', // 本地临时路径\r\n\t\t\t\tserverVideoPath: '', // 服务器路径\r\n\t\t\t\t\r\n\t\t\t\t// 发生时间\r\n\t\t\t\tfaultDate: '',\r\n\t\t\t\tfaultTime: '',\r\n\t\t\t\tstartDate: `${year - 1}-${month}-${day}`,\r\n\t\t\t\tendDate: `${year}-${month}-${day}`,\r\n\t\t\t\t\r\n\t\t\t\t// 设备ID（如果从设备详情页跳转过来）\r\n\t\t\t\tdeviceId: '',\r\n\t\t\t\t\r\n\t\t\t\t// 加载状态\r\n\t\t\t\tisLoading: false,\r\n\r\n\t\t\t\t// 地址字段\r\n\t\t\t\tbuildingNo: '',\r\n\t\t\t\tunitNo: '',\r\n\t\t\t\troomNo: '',\r\n\t\t\t\tformattedAddress: '', // 格式化后的完整地址\r\n\t\t\t\taddressInputError: false, // 地址输入错误状态\r\n\t\t\t\t\r\n\t\t\t\t// 热力单位ID\r\n\t\t\t\theatUnitId: null\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShow(){\r\n\t\t\tthis.loadFaultTypeOptions();\r\n\t\t},\r\n\t\tasync onLoad(options) {\r\n\t\t\t// 从URL参数中获取热力单位ID（如果有）\r\n\t\t\tif (options.heatUnitId) {\r\n\t\t\t\tthis.heatUnitId = options.heatUnitId;\r\n\t\t\t\tconsole.log('从参数接收到热力单位ID:', this.heatUnitId);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 加载热力单元列表（故障类型通常是固定的）\r\n\t\t\ttry {\r\n\t\t\t\tawait this.loadHeatUnits();\r\n\t\t\t\tconsole.log('热用户列表加载完毕');\r\n\t\t\t} catch (loadErr) {\r\n\t\t\t\tconsole.error('onLoad 中加载热用户列表失败:', loadErr);\r\n\t\t\t\t// 即使加载失败，也继续处理其他逻辑，可能使用默认列表\r\n\t\t\t}\r\n\r\n\t\t\t// 设置日期选择器的默认范围和值\r\n\t\t\tconst now = new Date();\r\n\t\t\tconst year = now.getFullYear();\r\n\t\t\tconst month = (now.getMonth() + 1).toString().padStart(2, '0');\r\n\t\t\tconst day = now.getDate().toString().padStart(2, '0');\r\n\t\t\tconst hours = now.getHours().toString().padStart(2, '0');\r\n\t\t\tconst minutes = now.getMinutes().toString().padStart(2, '0');\r\n\t\t\t\r\n\t\t\tthis.startDate = `${year - 1}-${month}-${day}`; // 开始日期设为一年前\r\n\t\t\tthis.endDate = `${year}-${month}-${day}`;     // 结束日期设为今天\r\n\t\t\t// 默认发生时间设为当前时间\r\n\t\t\tif (!this.faultDate) this.faultDate = `${year}-${month}-${day}`;\r\n\t\t\tif (!this.faultTime) this.faultTime = `${hours}:${minutes}`;\r\n\r\n\t\t\t// 获取路由参数\r\n\t\t\tif (options.deviceId) {\r\n\t\t\t\tthis.deviceId = options.deviceId;\r\n\t\t\t\tconsole.log('从设备详情页跳转, 设备ID:', this.deviceId);\r\n\t\t\t}\r\n\r\n\t\t\t// --- 处理从告警消息传递过来的数据 ---\r\n\t\t\tif (options.alarmData) { \r\n\r\n\t\t\t\tconsole.log('接收到告警传递的数据（原始）:', options.alarmData);\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst alarmInfo = JSON.parse(decodeURIComponent(options.alarmData));\r\n\t\t\t\t\tconsole.log('解析后的告警数据:', alarmInfo);\r\n\r\n\t\t\t\t\t// 预填故障来源\r\n\t\t\t\t\tconst sourceIdx = this.faultSources.findIndex(source => source === '系统检测');\r\n\t\t\t\t\tif (sourceIdx > -1) this.sourceIndex = sourceIdx;\r\n\r\n\t\t\t\t\t// 预填故障类型\r\n\t\t\t\t\tconst typeIdx = this.faultTypes.findIndex(type => type === '设备故障');\r\n\t\t\t\t\tif (typeIdx > -1) this.typeIndex = typeIdx;\r\n\r\n\t\t\t\t\t// 预填故障描述\r\n\t\t\t\t\tif (alarmInfo.faultDesc) this.faultDesc = alarmInfo.faultDesc;\r\n\r\n\t\t\t\t\t// 预填发生时间\r\n\t\t\t\t\tif (alarmInfo.occurTime) {\r\n\t\t\t\t\t\tconst dateTimeParts = alarmInfo.occurTime.split(' ');\r\n\t\t\t\t\t\tif (dateTimeParts.length === 2) {\r\n\t\t\t\t\t\t\tthis.faultDate = dateTimeParts[0];\r\n\t\t\t\t\t\t\tthis.faultTime = dateTimeParts[1];\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconsole.warn('无法解析告警传递的 occurTime 格式:', alarmInfo.occurTime);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} \r\n\t\t\t\t\tif (alarmInfo.faultLevel) {\r\n\t\t\t\t\t\t// 这里的 faultLevels 是 data 中定义的选项数组: [{label: '提示', value: '提示'}, ...]\r\n\t\t\t\t\t\t// 注意：确保 alarmInfo.faultLevel 的值（例如 '一般'）与 faultLevels 数组中某一项的 value 匹配\r\n\t\t\t\t\t\tconst levelObj = this.faultLevels.find(level => level.value === alarmInfo.faultLevel);\r\n\t\t\t\t\t\tif (levelObj) {\r\n\t\t\t\t\t\t\tthis.faultLevel = levelObj.value; // 设置 data 中的 faultLevel 为告警传递过来的值\r\n\t\t\t\t\t\t\tconsole.log(`已根据告警信息预选故障等级: ${this.faultLevel}`);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 如果后端传来的值在前端选项里找不到，使用默认值\r\n\t\t\t\t\t\t\tconsole.warn(`告警传递的故障等级 '${alarmInfo.faultLevel}' 在选项中未找到，使用默认值 '一般'`);\r\n\t\t\t\t\t\t\tthis.faultLevel = '一般'; // 确保 faultLevel 有一个有效值\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 如果告警信息没提供等级，也设置一个默认值\r\n\t\t\t\t\t\tthis.faultLevel = '一般';\r\n\t\t\t\t\t} \r\n\r\n\t\t\t\t\t// 预填热用户 (优先使用热力单位ID)\r\n\t\t\t\t\tif (alarmInfo.heatUnitId) {\r\n\t\t\t\t\t\tthis.heatUnitId = alarmInfo.heatUnitId;\r\n\t\t\t\t\t} else if (alarmInfo.heatUnitName) {\r\n\t\t\t\t\t\tthis.selectUnitByName(alarmInfo.heatUnitName);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tconsole.error('解析告警传递数据失败:', e);\r\n\t\t\t\t}\r\n\t\t\t}  \r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 加载热用户列表\r\n\t\t\tasync loadHeatUnits() {\r\n\t\t\t\tthis.isLoading = true;\r\n\t\t\t\t// 获取用户ID\r\n\t\t\t\tconst userId = uni.getStorageSync('userId');\r\n\t\t\t\t\r\n\t\t\t\t// 使用getByUserId方法获取用户关联的热力单位\r\n\t\t\t\treturn heatUnitApi.getByUserId(userId) \r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tconsole.log('热用户列表接口响应:', res);\r\n\t\t\t\t\t\tif (res.code === 200 && res.data) {\r\n\t\t\t\t\t\t\tthis.heatUnits = res.data;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 如果有热力单位列表\r\n\t\t\t\t\t\t\tif (this.heatUnits.length > 0) {\r\n\t\t\t\t\t\t\t\t// 如果传入了heatUnitId参数，尝试预选对应的热力单位\r\n\t\t\t\t\t\t\t\tif (this.heatUnitId) {\r\n\t\t\t\t\t\t\t\t\tconst index = this.heatUnits.findIndex(unit => unit.id === this.heatUnitId);\r\n\t\t\t\t\t\t\t\t\tif (index > -1) {\r\n\t\t\t\t\t\t\t\t\t\tthis.unitIndex = index;\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('根据传入的热力单位ID预选:', this.heatUnits[index].name);\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t// 如果没找到匹配的，默认选择第一个热力单位\r\n\t\t\t\t\t\t\t\t\t\tthis.unitIndex = 0;\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('未找到匹配的热力单位，默认选择第一个:', this.heatUnits[0].name);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t// 如果没有传入指定ID，默认选择第一个热力单位\r\n\t\t\t\t\t\t\t\t\tthis.unitIndex = 0;\r\n\t\t\t\t\t\t\t\t\tconsole.log('默认选择第一个热力单位:', this.heatUnits[0].name);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.heatUnits = [];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tconsole.error('获取热用户列表失败:', err);\r\n\t\t\t\t\t\tthis.heatUnits = [];\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.finally(() => {\r\n\t\t\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 加载故障类型选项\r\n\t\t\tloadFaultTypeOptions() {\r\n\t\t\t\tdictApi.getDictDataByDictId(5)\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tif (res.code === 200 && res.data) {\r\n\t\t\t\t\t\t\tconsole.log(res.data)\r\n\t\t\t\t\t\t\tthis.faultTypes = res.data.map(item=>item.name);\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tconsole.error('获取巡检类型选项失败:', err);\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 故障类型选择\r\n\t\t\thandleTypeChange(e) {\r\n\t\t\t\tthis.typeIndex = e.detail.value;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 热用户选择\r\n\t\t\thandleUnitChange(e) {\r\n\t\t\t\tthis.unitIndex = e.detail.value;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 故障等级选择\r\n\t\t\tselectLevel(level) {\r\n\t\t\t\tthis.faultLevel = level;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 日期选择\r\n\t\t\thandleDateChange(e) {\r\n\t\t\t\tthis.faultDate = e.detail.value;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 时间选择\r\n\t\t\thandleTimeChange(e) {\r\n\t\t\t\tthis.faultTime = e.detail.value;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 选择图片\r\n\t\t\tchooseImage() {\r\n\t\t\t\tuni.chooseImage({\r\n\t\t\t\t\tcount: 6 - this.imageList.length,\r\n\t\t\t\t\tsizeType: ['compressed'],\r\n\t\t\t\t\tsourceType: ['album', 'camera'],\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t// 显示上传中提示\r\n\t\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\t\ttitle: '上传中...',\r\n\t\t\t\t\t\t\tmask: true\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 检查token是否存在，不存在则提示用户登录\r\n\t\t\t\t\t\tconst token = uni.getStorageSync('token');\r\n\t\t\t\t\t\tif (!token) {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '请先登录',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 逐个上传图片\r\n\t\t\t\t\t\tconst uploadPromises = res.tempFilePaths.map(path => {\r\n\t\t\t\t\t\t\treturn uploadUtils.uploadImage(path)\r\n\t\t\t\t\t\t\t\t.then(serverPath => {\r\n\t\t\t\t\t\t\t\t\t// 保存本地路径和服务器路径\r\n\t\t\t\t\t\t\t\t\tthis.imageList.push(path);\r\n\t\t\t\t\t\t\t\t\tthis.serverImageList.push(serverPath);\r\n\t\t\t\t\t\t\t\t\treturn serverPath;\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 等待所有图片上传完成\r\n\t\t\t\t\t\tPromise.all(uploadPromises)\r\n\t\t\t\t\t\t\t.then(() => {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '上传成功',\r\n\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: err.message || '上传失败',\r\n\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 预览图片\r\n\t\t\tpreviewImage(index) {\r\n\t\t\t\t// 将本地路径转换为可访问的URL\r\n\t\t\t\tconst previewUrls = this.imageList.map((path, i) => {\r\n\t\t\t\t\t// 如果有对应的服务器路径，使用服务器路径构建完整URL\r\n\t\t\t\t\tif (this.serverImageList[i]) {\r\n\t\t\t\t\t\treturn uploadUtils.getFileUrl(this.serverImageList[i]);\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn path;\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\tuni.previewImage({\r\n\t\t\t\t\turls: previewUrls,\r\n\t\t\t\t\tcurrent: previewUrls[index]\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 删除图片\r\n\t\t\tdeleteImage(index) {\r\n\t\t\t\tthis.imageList.splice(index, 1);\r\n\t\t\t\tthis.serverImageList.splice(index, 1);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 选择视频\r\n\t\t\tchooseVideo() {\r\n\t\t\t\tuni.chooseVideo({\r\n\t\t\t\t\tsourceType: ['album', 'camera'],\r\n\t\t\t\t\tmaxDuration: 60,\r\n\t\t\t\t\tcamera: 'back',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t// 显示上传中提示\r\n\t\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\t\ttitle: '上传中...',\r\n\t\t\t\t\t\t\tmask: true\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 检查token是否存在，不存在则提示用户登录\r\n\t\t\t\t\t\tconst token = uni.getStorageSync('token');\r\n\t\t\t\t\t\tif (!token) {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '请先登录',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t// 可以跳转到登录页\r\n\t\t\t\t\t\t\t// setTimeout(() => {\r\n\t\t\t\t\t\t\t//   uni.navigateTo({ url: '/pages/login/index' });\r\n\t\t\t\t\t\t\t// }, 1500);\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 上传视频到服务器\r\n\t\t\t\t\t\tuploadUtils.uploadVideo(res.tempFilePath)\r\n\t\t\t\t\t\t\t.then(serverPath => {\r\n\t\t\t\t\t\t\t\t// 保存本地路径和服务器路径\r\n\t\t\t\t\t\t\t\tthis.videoPath = res.tempFilePath;\r\n\t\t\t\t\t\t\t\tthis.serverVideoPath = serverPath;\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '上传成功',\r\n\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: err.message || '上传失败',\r\n\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取视频URL\r\n\t\t\tgetVideoUrl() {\r\n\t\t\t\t// 特殊处理以@开头的URL\r\n\t\t\t\tif (this.serverVideoPath && this.serverVideoPath.startsWith('@http')) {\r\n\t\t\t\t\tconst cleanUrl = this.serverVideoPath.substring(1); // 去除@符号\r\n\t\t\t\t\tconsole.log('视频路径以@http开头，清理后:', cleanUrl);\r\n\t\t\t\t\treturn cleanUrl;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 如果是服务器路径，使用工具方法获取完整URL\r\n\t\t\t\tif (this.serverVideoPath) {\r\n\t\t\t\t\t// 确保它真的是一个http URL\r\n\t\t\t\t\tif (this.serverVideoPath.startsWith('http')) {\r\n\t\t\t\t\t\treturn this.serverVideoPath;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tconst url = uploadUtils.getFileUrl(this.serverVideoPath);\r\n\t\t\t\t\tconsole.log('处理后的视频URL:', url);\r\n\t\t\t\t\treturn url;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 否则返回本地临时路径\r\n\t\t\t\tconsole.log('使用本地视频路径:', this.videoPath);\r\n\t\t\t\treturn this.videoPath;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 在浏览器中打开视频\r\n\t\t\topenInBrowser() {\r\n\t\t\t\tlet url = this.getVideoUrl();\r\n\t\t\t\t\r\n\t\t\t\t// 检查URL是否有效\r\n\t\t\t\tif (!url) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '视频URL无效',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tconsole.log('在浏览器中打开视频:', url);\r\n\t\t\t\t\r\n\t\t\t\t// 使用系统浏览器打开\r\n\t\t\t\tplus.runtime.openURL(url, (err) => {\r\n\t\t\t\t\tif (err) {\r\n\t\t\t\t\t\tconsole.error('打开浏览器失败:', err);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '打开浏览器失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 视频加载错误处理\r\n\t\t\tonVideoError(e) {\r\n\t\t\t\tconsole.error('视频加载失败:', e.detail);\r\n\t\t\t\tconsole.error('视频路径:', this.videoPath);\r\n\t\t\t\tconsole.error('服务器视频路径:', this.serverVideoPath);\r\n\t\t\t\tconsole.error('处理后URL:', this.getVideoUrl());\r\n\t\t\t\t\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '视频加载失败，请尝试其他方式查看',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 视频加载成功\r\n\t\t\tonVideoLoad(e) {\r\n\t\t\t\tconsole.log('视频加载成功, 路径:', this.videoPath);\r\n\t\t\t\tconsole.log('视频元数据:', e.detail);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 删除视频\r\n\t\t\tdeleteVideo() {\r\n\t\t\t\tthis.videoPath = '';\r\n\t\t\t\tthis.serverVideoPath = '';\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 上传附件\r\n\t\t\tuploadAttachments() {\r\n\t\t\t\treturn new Promise((resolve) => {\r\n\t\t\t\t\tconst attachments = [];\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 添加已上传的图片\r\n\t\t\t\t\tthis.serverImageList.forEach((serverPath, index) => {\r\n\t\t\t\t\t\tattachments.push({\r\n\t\t\t\t\t\t\tfile_type: 'image',\r\n\t\t\t\t\t\t\tfile_path: serverPath\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 添加已上传的视频\r\n\t\t\t\t\tif (this.serverVideoPath) {\r\n\t\t\t\t\t\tattachments.push({\r\n\t\t\t\t\t\t\tfile_type: 'video',\r\n\t\t\t\t\t\t\tfile_path: this.serverVideoPath\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 直接返回已上传的附件列表\r\n\t\t\t\t\tresolve(attachments);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 表单验证\r\n\t\t\tvalidateForm() {\r\n\t\t\t\tif (this.typeIndex === -1) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请选择故障类型',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (this.unitIndex === -1) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请选择热用户',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 检查地址字段，如果填写了任何一个地址字段，则其他字段也必须填写\r\n\t\t\t\tif ((this.buildingNo || this.unitNo || this.roomNo) &&\r\n\t\t\t\t\t!(this.buildingNo && this.unitNo && this.roomNo)) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请完整填写楼号、单元号和房号',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (!this.faultLevel) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请选择故障等级',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (!this.faultDesc.trim()) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请填写故障描述',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (!this.faultDate || !this.faultTime) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请选择故障发生时间',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\treturn true;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 提交故障上报\r\n\t\t\tasync submitReport() {\r\n\t\t\t\tif (!this.validateForm()) return;\r\n\t\t\t\t\r\n\t\t\t\t// 显示加载提示\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '正在提交...'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 上传附件\r\n\t\t\t\t\tconst attachments = await this.uploadAttachments(); \r\n\t\t\t\t\t\r\n\t\t\t\t\t// 获取用户ID\r\n\t\t\t\t\tconst userId = uni.getStorageSync('userId') || 1; \r\n\t\t\t\t\t\r\n\t\t\t\t\t// 确保热力单位ID存在\r\n\t\t\t\t\tif (this.unitIndex === -1 || !this.heatUnits[this.unitIndex]) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请选择热用户',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 在提交前更新一次格式化地址\r\n\t\t\t\t\tthis.updateFormattedAddress();\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 构建上报数据（按照API文档要求）\r\n\t\t\t\t\tconst reportData = {\r\n\t\t\t\t\t\theat_unit_id: this.heatUnits[this.unitIndex].id,\r\n\t\t\t\t\t\talarm_id: this.alarmId,\r\n\t\t\t\t\t\tfault_type: this.faultTypes[this.typeIndex],\r\n\t\t\t\t\t\tfault_level: this.faultLevel,\r\n\t\t\t\t\t\tfault_desc: this.faultDesc,\r\n\t\t\t\t\t\tfault_source: this.faultSources[this.sourceIndex],\r\n\t\t\t\t\t\toccur_time: `${this.faultDate} ${this.faultTime}:00`,\r\n\t\t\t\t\t\treport_user_id: userId,\r\n\t\t\t\t\t\taddress: this.formattedAddress,\r\n\t\t\t\t\t\tattachment: attachments\r\n\t\t\t\t\t};\r\n\t\t\t\t\t\r\n\t\t\t\t\tconsole.log('提交故障上报数据:', reportData);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 调用API提交故障上报\r\n\t\t\t\t\tfaultApi.reportFault(reportData)\r\n\t\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\t\t\t\t// 隐藏加载提示\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t// 显示提交成功\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '故障上报成功',\r\n\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\tuni.navigateBack();\r\n\t\t\t\t\t\t\t\t}, 1500);\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthrow new Error(res.message || '上报失败');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 检查错误信息是否包含住户相关的错误\r\n\t\t\t\t\t\t\tconst errorMsg = err.message || '网络异常，请稍后重试';\r\n\t\t\t\t\t\t\tconst isHouseError = errorMsg.includes('未找到住户') || \r\n\t\t\t\t\t\t\t\t\t\t\t\terrorMsg.includes('房间号格式') || \r\n\t\t\t\t\t\t\t\t\t\t\t\terrorMsg.includes('楼号') || \r\n\t\t\t\t\t\t\t\t\t\t\t\terrorMsg.includes('单元号') || \r\n\t\t\t\t\t\t\t\t\t\t\t\terrorMsg.includes('房号');\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 如果是住户相关错误，显示特殊提示并高亮输入框\r\n\t\t\t\t\t\t\tif (isHouseError) {\r\n\t\t\t\t\t\t\t\t// 高亮显示地址输入框\r\n\t\t\t\t\t\t\t\tthis.highlightAddressInputs();\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t// 显示详细错误提示\r\n\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\ttitle: '住户信息错误',\r\n\t\t\t\t\t\t\t\t\tcontent: errorMsg,\r\n\t\t\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\t\t\tconfirmText: '我知道了'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t// 其他错误正常显示\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: errorMsg,\r\n\t\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '提交过程中出错，请重试',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 高亮显示地址输入框\r\n\t\t\thighlightAddressInputs() {\r\n\t\t\t\t// 添加错误样式类\r\n\t\t\t\tthis.addressInputError = true;\r\n\t\t\t\t\r\n\t\t\t\t// 3秒后自动移除错误样式\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.addressInputError = false;\r\n\t\t\t\t}, 3000);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// ----> 选择热用户通过名称 <---- -->\r\n\t\t\tselectUnitByName(unitName) {\r\n\t\t\t\t// 去除空格\r\n\t\t\t\tconst unitNameTrim = unitName.trim(); \r\n\t\t\t\tif (!this.heatUnits || !Array.isArray(this.heatUnits)) {\r\n\t\t\t\t\tconsole.error('selectUnitByName 调用时 heatUnits 列表无效');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tconst index = this.heatUnits.findIndex(unit => unit.name === unitNameTrim);\r\n\t\t\t\tif (index > -1) {\r\n\t\t\t\t\tthis.unitIndex = index;\r\n\t\t\t\t\tconsole.log(`已根据名称 '${unitName}' 预选热用户，索引: ${index}`);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.warn(`未在热用户列表中找到名称为 '${unitName}' 的项`);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 如果只有一个热力单位，自动选择它\r\n\t\t\t\t\tif (this.heatUnits.length === 1) {\r\n\t\t\t\t\t\tthis.unitIndex = 0;\r\n\t\t\t\t\t\tconsole.log(`未找到名称匹配的热用户，但只有一个选项，已自动选择: ${this.heatUnits[0].name}`);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.unitIndex = -1;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 格式化地址字段\r\n\t\t\tformatAddressFields() {\r\n\t\t\t\t// 如果用户输入了楼号，确保它是两位数\r\n\t\t\t\tif (this.buildingNo) {\r\n\t\t\t\t\t// 确保它是数字\r\n\t\t\t\t\tthis.buildingNo = this.buildingNo.toString().replace(/\\D/g, '');\r\n\t\t\t\t\t// 限制长度\r\n\t\t\t\t\tif (this.buildingNo.length > 2) {\r\n\t\t\t\t\t\tthis.buildingNo = this.buildingNo.substring(0, 2);\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 自动补零（只在UI上显示补零，实际值保持不变）\r\n\t\t\t\t\tif (this.buildingNo.length === 1) {\r\n\t\t\t\t\t\tconsole.log('楼号已格式化:', this.buildingNo);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 如果用户输入了单元号，确保它是两位数\r\n\t\t\t\tif (this.unitNo) {\r\n\t\t\t\t\t// 确保它是数字\r\n\t\t\t\t\tthis.unitNo = this.unitNo.toString().replace(/\\D/g, '');\r\n\t\t\t\t\t// 限制长度\r\n\t\t\t\t\tif (this.unitNo.length > 2) {\r\n\t\t\t\t\t\tthis.unitNo = this.unitNo.substring(0, 2);\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 自动补零（只在UI上显示补零，实际值保持不变）\r\n\t\t\t\t\tif (this.unitNo.length === 1) {\r\n\t\t\t\t\t\tconsole.log('单元号已格式化:', this.unitNo);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 如果用户输入了房号，确保它是四位数\r\n\t\t\t\tif (this.roomNo) {\r\n\t\t\t\t\t// 确保它是数字\r\n\t\t\t\t\tthis.roomNo = this.roomNo.toString().replace(/\\D/g, '');\r\n\t\t\t\t\t// 限制长度\r\n\t\t\t\t\tif (this.roomNo.length > 4) {\r\n\t\t\t\t\t\tthis.roomNo = this.roomNo.substring(0, 4);\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 自动补零（只在UI上显示补零，实际值保持不变）\r\n\t\t\t\t\tif (this.roomNo.length < 4) {\r\n\t\t\t\t\t\tconsole.log('房号已格式化:', this.roomNo);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 如果用户填写了任何一个字段，提示填写所有字段\r\n\t\t\t\tif ((this.buildingNo || this.unitNo || this.roomNo) && \r\n\t\t\t\t\t!(this.buildingNo && this.unitNo && this.roomNo)) {\r\n\t\t\t\t\t// 如果不是所有字段都已填写，显示提示\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请完整填写楼号、单元号和房号',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 更新格式化后的地址\r\n\t\t\tupdateFormattedAddress() {\r\n\t\t\t\t// 只有当所有三个字段都有值时才更新格式化地址\r\n\t\t\t\tif (this.buildingNo && this.unitNo && this.roomNo) {\r\n\t\t\t\t\t// 格式化为 xx-xx-xxxx\r\n\t\t\t\t\tconst paddedBuildingNo = this.buildingNo;\r\n\t\t\t\t\tconst paddedUnitNo = this.unitNo;\r\n\t\t\t\t\tconst paddedRoomNo = this.roomNo.padStart(4, '0');\r\n\t\t\t\t\tthis.formattedAddress = `${paddedBuildingNo}-${paddedUnitNo}-${paddedRoomNo}`;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.formattedAddress = '';\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.fault-report-container {\r\n\t\tpadding: 20rpx;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tmin-height: 100vh;\r\n\t\tpadding-bottom: 120rpx;\r\n\t}\r\n\t\r\n\t.form-card {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 12rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\r\n\t}\r\n\t\r\n\t.form-header {\r\n\t\tmargin-bottom: 20rpx;\r\n\t\t\r\n\t\t.form-title {\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tcolor: $uni-text-color;\r\n\t\t\tmargin-bottom: 8rpx;\r\n\t\t\tdisplay: block;\r\n\t\t}\r\n\t\t\r\n\t\t.form-subtitle {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: $uni-text-color-grey;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.form-group {\r\n\t\t.form-item {\r\n\t\t\tmargin-bottom: 24rpx;\r\n\t\t\t\r\n\t\t\t&:last-child {\r\n\t\t\t\tmargin-bottom: 0;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.form-label {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\tmargin-bottom: 12rpx;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\t\r\n\t\t\t\t&.required::before {\r\n\t\t\t\t\tcontent: '*';\r\n\t\t\t\t\tcolor: $uni-color-error;\r\n\t\t\t\t\tmargin-right: 4rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.form-input-container {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tbackground-color: #f8f8f8;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\tborder: 1rpx solid #e5e5e5;\r\n\t\t\t\ttransition: background-color 0.2s;\r\n\t\t\t\t\r\n\t\t\t\t&:active {\r\n\t\t\t\t\tbackground-color: #f0f0f0;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.form-picker-container {\r\n\t\t\t\tbackground-color: #f8f8f8;\r\n\t\t\t\tpadding: 20rpx;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\tborder: 1rpx solid #e5e5e5;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\ttransition: background-color 0.2s;\r\n\t\t\t\t\r\n\t\t\t\t&:active {\r\n\t\t\t\t\tbackground-color: #f0f0f0;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&::after {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tright: 20rpx;\r\n\t\t\t\t\ttop: 50%;\r\n\t\t\t\t\ttransform: translateY(-50%);\r\n\t\t\t\t\twidth: 0;\r\n\t\t\t\t\theight: 0;\r\n\t\t\t\t\tborder-left: 12rpx solid transparent;\r\n\t\t\t\t\tborder-right: 12rpx solid transparent;\r\n\t\t\t\t\tborder-top: 12rpx solid #999;\r\n\t\t\t\t\tpointer-events: none;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.form-picker {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tborder: none;\r\n\t\t\t\tbackground: transparent;\r\n\t\t\t\t\r\n\t\t\t\t.picker-text {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t\tpadding-right: 40rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.placeholder {\r\n\t\t\t\t\t\tcolor: $uni-text-color-placeholder;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.form-value {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\tpadding: 20rpx;\r\n\t\t\t\tbackground-color: #f8f8f8;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.level-options {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\r\n\t\t\t\t.level-option {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\theight: 80rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tmargin: 0 10rpx;\r\n\t\t\t\t\tbackground-color: #f8f8f8;\r\n\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t\t\r\n\t\t\t\t\t&:first-child {\r\n\t\t\t\t\t\tmargin-left: 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\tmargin-right: 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.active {\r\n\t\t\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.form-textarea {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 200rpx;\r\n\t\t\t\tpadding: 20rpx;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\tbackground-color: #f8f8f8;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tposition: relative;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.textarea-counter {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: 30rpx;\r\n\t\t\t\tmargin-top: 8rpx;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.upload-area {\r\n\t\t.image-list {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-wrap: wrap;\r\n\t\t\t\r\n\t\t\t.image-item, .upload-item {\r\n\t\t\t\twidth: 200rpx;\r\n\t\t\t\theight: 200rpx;\r\n\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\tposition: relative;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.image-item {\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.delete-icon {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\ttop: -16rpx;\r\n\t\t\t\t\tright: -16rpx;\r\n\t\t\t\t\twidth: 40rpx;\r\n\t\t\t\t\theight: 40rpx;\r\n\t\t\t\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.upload-item {\r\n\t\t\t\tborder: 2rpx dashed #ddd;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\t\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 60rpx;\r\n\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t\tmargin-bottom: 16rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\ttext {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.video-container {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 400rpx;\r\n\t\t\tposition: relative;\r\n\t\t\t\r\n\t\t\tvideo {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.delete-icon {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: 16rpx;\r\n\t\t\t\tright: 16rpx;\r\n\t\t\t\twidth: 40rpx;\r\n\t\t\t\theight: 40rpx;\r\n\t\t\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.video-actions {\r\n\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\t\r\n\t\t\t\t.video-action-btn {\r\n\t\t\t\t\tbackground-color: #f5f5f5;\r\n\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t\tpadding: 10rpx 30rpx;\r\n\t\t\t\t\tborder-radius: 30rpx;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tbox-shadow: 0 2rpx 5rpx rgba(0,0,0,0.1);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.submit-btn-container {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tpadding: 20rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tbox-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);\r\n\t\t\r\n\t\t.submit-btn {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 88rpx;\r\n\t\t\tline-height: 88rpx;\r\n\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\tcolor: #fff;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.form-input {\r\n\t\tbackground-color: transparent;\r\n\t\tpadding: 20rpx;\r\n\t\twidth: 100%;\r\n\t\theight: 80rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: $uni-text-color;\r\n\t\tborder: none;\r\n\t}\r\n\t\r\n\t.form-input-container {\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #f8f8f8;\r\n\t\tborder-radius: 8rpx;\r\n\t\tborder: 1rpx solid #e5e5e5;\r\n\t\ttransition: background-color 0.2s;\r\n\t\t\r\n\t\t&:active {\r\n\t\t\tbackground-color: #f0f0f0;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.address-inputs {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\t\r\n\t\t&.error {\r\n\t\t\tborder-color: #fa436a;\r\n\t\t\tbackground-color: #fff0f0;\r\n\t\t\tanimation: shake 0.5s ease-in-out;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.address-input-item {\r\n\t\tposition: relative;\r\n\t\tbackground-color: #f8f8f8;\r\n\t\tborder-radius: 8rpx;\r\n\t\tborder: 1rpx solid #e5e5e5;\r\n\t\tflex: 1;\r\n\t\tpadding-right: 60rpx; // 为标签留出空间\r\n\t\t\r\n\t\t&.error {\r\n\t\t\tborder-color: #fa436a;\r\n\t\t\tbackground-color: #fff0f0;\r\n\t\t\tanimation: shake 0.5s ease-in-out;\r\n\t\t}\r\n\t\t\r\n\t\t.form-input {\r\n\t\t\theight: 80rpx;\r\n\t\t\tpadding: 0 20rpx;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.address-input-label {\r\n\t\t\tposition: absolute;\r\n\t\t\tright: 20rpx;\r\n\t\t\ttop: 50%;\r\n\t\t\ttransform: translateY(-50%);\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: #909399;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.room-input {\r\n\t\tflex: 1.5; // 房号输入框稍宽一些\r\n\t}\r\n\t\r\n\t.address-separator {\r\n\t\tpadding: 0 10rpx;\r\n\t\tcolor: #909399;\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t\r\n\t.address-hint {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #909399;\r\n\t\tmargin-top: 6rpx;\r\n\t\tmargin-left: 10rpx;\r\n\t}\r\n\t\r\n\t.address-preview {\r\n\t\tmargin-top: 10rpx;\r\n\t\tpadding: 15rpx;\r\n\t\tbackground-color: #f0f8ff; // 淡蓝色背景\r\n\t\tborder-radius: 8rpx;\r\n\t\tborder: 1rpx solid #c8e1ff;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\t\r\n\t\t.preview-label {\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: #606266;\r\n\t\t\tmargin-right: 10rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.preview-value {\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tcolor: #409EFF; // 蓝色突出显示\r\n\t\t\tfont-weight: bold;\r\n\t\t}\r\n\t}\r\n\t\r\n\t@keyframes shake {\r\n\t\t0%, 100% { transform: translateX(0); }\r\n\t\t20%, 60% { transform: translateX(-5px); }\r\n\t\t40%, 80% { transform: translateX(5px); }\r\n\t}\r\n</style> ", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/fault/report.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "heatUnitApi", "dictApi", "uploadUtils", "faultApi"], "mappings": ";;;;AAmNC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,UAAM,MAAM,oBAAI;AAChB,UAAM,OAAO,IAAI;AACjB,UAAM,SAAS,IAAI,aAAa,GAAG,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC7D,UAAM,MAAM,IAAI,QAAS,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACtC,QAAI,SAAQ,EAAG,SAAU,EAAC,SAAS,GAAG,GAAG;AACvC,QAAI,WAAU,EAAG,SAAU,EAAC,SAAS,GAAG,GAAG;AAE3D,WAAO;AAAA;AAAA,MAEN,YAAY,CAAE;AAAA,MACd,WAAW;AAAA;AAAA,MAGX,WAAW,CAAE;AAAA,MACb,WAAW;AAAA;AAAA,MAGX,SAAQ;AAAA;AAAA,MAGR,cAAc,CAAC,QAAQ,MAAM;AAAA,MAC7B,aAAa;AAAA;AAAA;AAAA,MAGb,aAAa;AAAA,QACZ,EAAE,OAAO,MAAM,OAAO,KAAM;AAAA,QAC5B,EAAE,OAAO,MAAM,OAAO,KAAM;AAAA,QAC5B,EAAE,OAAO,MAAM,OAAO,KAAM;AAAA,QAC5B,EAAE,OAAO,MAAM,OAAO,KAAK;AAAA,MAC3B;AAAA,MACD,YAAY;AAAA;AAAA,MAGZ,WAAW;AAAA;AAAA,MAGX,WAAW,CAAE;AAAA;AAAA,MACb,iBAAiB,CAAE;AAAA;AAAA,MACnB,WAAW;AAAA;AAAA,MACX,iBAAiB;AAAA;AAAA;AAAA,MAGjB,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW,GAAG,OAAO,CAAC,IAAI,KAAK,IAAI,GAAG;AAAA,MACtC,SAAS,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA;AAAA,MAGhC,UAAU;AAAA;AAAA,MAGV,WAAW;AAAA;AAAA,MAGX,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,kBAAkB;AAAA;AAAA,MAClB,mBAAmB;AAAA;AAAA;AAAA,MAGnB,YAAY;AAAA,IACb;AAAA,EACA;AAAA,EACD,SAAQ;AACP,SAAK,qBAAoB;AAAA,EACzB;AAAA,EACD,MAAM,OAAO,SAAS;AAErB,QAAI,QAAQ,YAAY;AACvB,WAAK,aAAa,QAAQ;AAC1BA,oBAAY,MAAA,MAAA,OAAA,iCAAA,iBAAiB,KAAK,UAAU;AAAA,IAC7C;AAGA,QAAI;AACH,YAAM,KAAK;AACXA,oBAAAA,MAAY,MAAA,OAAA,iCAAA,WAAW;AAAA,IACxB,SAAS,SAAS;AACjBA,oBAAA,MAAA,MAAA,SAAA,iCAAc,sBAAsB,OAAO;AAAA,IAE5C;AAGA,UAAM,MAAM,oBAAI;AAChB,UAAM,OAAO,IAAI;AACjB,UAAM,SAAS,IAAI,aAAa,GAAG,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC7D,UAAM,MAAM,IAAI,QAAS,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACpD,UAAM,QAAQ,IAAI,SAAU,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACvD,UAAM,UAAU,IAAI,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAE3D,SAAK,YAAY,GAAG,OAAO,CAAC,IAAI,KAAK,IAAI,GAAG;AAC5C,SAAK,UAAU,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAEtC,QAAI,CAAC,KAAK;AAAW,WAAK,YAAY,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAC7D,QAAI,CAAC,KAAK;AAAW,WAAK,YAAY,GAAG,KAAK,IAAI,OAAO;AAGzD,QAAI,QAAQ,UAAU;AACrB,WAAK,WAAW,QAAQ;AACxBA,oBAAY,MAAA,MAAA,OAAA,iCAAA,mBAAmB,KAAK,QAAQ;AAAA,IAC7C;AAGA,QAAI,QAAQ,WAAW;AAEtBA,wEAAY,mBAAmB,QAAQ,SAAS;AAChD,UAAI;AACH,cAAM,YAAY,KAAK,MAAM,mBAAmB,QAAQ,SAAS,CAAC;AAClEA,sBAAY,MAAA,MAAA,OAAA,iCAAA,aAAa,SAAS;AAGlC,cAAM,YAAY,KAAK,aAAa,UAAU,YAAU,WAAW,MAAM;AACzE,YAAI,YAAY;AAAI,eAAK,cAAc;AAGvC,cAAM,UAAU,KAAK,WAAW,UAAU,UAAQ,SAAS,MAAM;AACjE,YAAI,UAAU;AAAI,eAAK,YAAY;AAGnC,YAAI,UAAU;AAAW,eAAK,YAAY,UAAU;AAGpD,YAAI,UAAU,WAAW;AACxB,gBAAM,gBAAgB,UAAU,UAAU,MAAM,GAAG;AACnD,cAAI,cAAc,WAAW,GAAG;AAC/B,iBAAK,YAAY,cAAc,CAAC;AAChC,iBAAK,YAAY,cAAc,CAAC;AAAA,iBAC1B;AACNA,0BAAa,MAAA,MAAA,QAAA,iCAAA,2BAA2B,UAAU,SAAS;AAAA,UAC5D;AAAA,QACD;AACA,YAAI,UAAU,YAAY;AAGzB,gBAAM,WAAW,KAAK,YAAY,KAAK,WAAS,MAAM,UAAU,UAAU,UAAU;AACpF,cAAI,UAAU;AACb,iBAAK,aAAa,SAAS;AAC3BA,gCAAA,MAAA,OAAA,iCAAY,kBAAkB,KAAK,UAAU,EAAE;AAAA,iBACzC;AAENA,gCAAa,MAAA,QAAA,iCAAA,cAAc,UAAU,UAAU,sBAAsB;AACrE,iBAAK,aAAa;AAAA,UACnB;AAAA,eACM;AAEN,eAAK,aAAa;AAAA,QACnB;AAGA,YAAI,UAAU,YAAY;AACzB,eAAK,aAAa,UAAU;AAAA,QAC7B,WAAW,UAAU,cAAc;AAClC,eAAK,iBAAiB,UAAU,YAAY;AAAA,QAC7C;AAAA,MAED,SAAS,GAAG;AACXA,4EAAc,eAAe,CAAC;AAAA,MAC/B;AAAA,IACD;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,MAAM,gBAAgB;AACrB,WAAK,YAAY;AAEjB,YAAM,SAASA,cAAAA,MAAI,eAAe,QAAQ;AAG1C,aAAOC,UAAW,YAAC,YAAY,MAAM,EACnC,KAAK,SAAO;AACZD,0EAAY,cAAc,GAAG;AAC7B,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AACjC,eAAK,YAAY,IAAI;AAGrB,cAAI,KAAK,UAAU,SAAS,GAAG;AAE9B,gBAAI,KAAK,YAAY;AACpB,oBAAM,QAAQ,KAAK,UAAU,UAAU,UAAQ,KAAK,OAAO,KAAK,UAAU;AAC1E,kBAAI,QAAQ,IAAI;AACf,qBAAK,YAAY;AACjBA,oCAAY,MAAA,OAAA,iCAAA,kBAAkB,KAAK,UAAU,KAAK,EAAE,IAAI;AAAA,qBAClD;AAEN,qBAAK,YAAY;AACjBA,oCAAY,MAAA,OAAA,iCAAA,uBAAuB,KAAK,UAAU,CAAC,EAAE,IAAI;AAAA,cAC1D;AAAA,mBACM;AAEN,mBAAK,YAAY;AACjBA,kCAAA,MAAA,OAAA,iCAAY,gBAAgB,KAAK,UAAU,CAAC,EAAE,IAAI;AAAA,YACnD;AAAA,UACD;AAAA,eACM;AACN,eAAK,YAAY;QAClB;AAAA,OACA,EACA,MAAM,SAAO;AACbA,4EAAc,cAAc,GAAG;AAC/B,aAAK,YAAY;OACjB,EACA,QAAQ,MAAM;AACd,aAAK,YAAY;AAAA,MAClB,CAAC;AAAA,IACF;AAAA;AAAA,IAED,uBAAuB;AACtBE,gBAAO,QAAC,oBAAoB,CAAC,EAC3B,KAAK,SAAO;AACZ,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AACjCF,wBAAAA,MAAY,MAAA,OAAA,iCAAA,IAAI,IAAI;AACpB,eAAK,aAAa,IAAI,KAAK,IAAI,UAAM,KAAK,IAAI;AAAA,QAE/C;AAAA,OACA,EACA,MAAM,SAAO;AACbA,sBAAc,MAAA,MAAA,SAAA,iCAAA,eAAe,GAAG;AAAA,MACjC,CAAC;AAAA,IACF;AAAA;AAAA,IAED,iBAAiB,GAAG;AACnB,WAAK,YAAY,EAAE,OAAO;AAAA,IAC1B;AAAA;AAAA,IAGD,iBAAiB,GAAG;AACnB,WAAK,YAAY,EAAE,OAAO;AAAA,IAC1B;AAAA;AAAA,IAGD,YAAY,OAAO;AAClB,WAAK,aAAa;AAAA,IAClB;AAAA;AAAA,IAGD,iBAAiB,GAAG;AACnB,WAAK,YAAY,EAAE,OAAO;AAAA,IAC1B;AAAA;AAAA,IAGD,iBAAiB,GAAG;AACnB,WAAK,YAAY,EAAE,OAAO;AAAA,IAC1B;AAAA;AAAA,IAGD,cAAc;AACbA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO,IAAI,KAAK,UAAU;AAAA,QAC1B,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AAEjBA,wBAAAA,MAAI,YAAY;AAAA,YACf,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAGD,gBAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,cAAI,CAAC,OAAO;AACXA,0BAAG,MAAC,YAAW;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACP,CAAC;AACD;AAAA,UACD;AAGA,gBAAM,iBAAiB,IAAI,cAAc,IAAI,UAAQ;AACpD,mBAAOG,aAAW,YAAC,YAAY,IAAI,EACjC,KAAK,gBAAc;AAEnB,mBAAK,UAAU,KAAK,IAAI;AACxB,mBAAK,gBAAgB,KAAK,UAAU;AACpC,qBAAO;AAAA,YACR,CAAC;AAAA,UACH,CAAC;AAGD,kBAAQ,IAAI,cAAc,EACxB,KAAK,MAAM;AACXH,0BAAG,MAAC,YAAW;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACP,CAAC;AAAA,WACD,EACA,MAAM,SAAO;AACbA,0BAAG,MAAC,YAAW;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO,IAAI,WAAW;AAAA,cACtB,MAAM;AAAA,YACP,CAAC;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,aAAa,OAAO;AAEnB,YAAM,cAAc,KAAK,UAAU,IAAI,CAAC,MAAM,MAAM;AAEnD,YAAI,KAAK,gBAAgB,CAAC,GAAG;AAC5B,iBAAOG,aAAAA,YAAY,WAAW,KAAK,gBAAgB,CAAC,CAAC;AAAA,QACtD;AACA,eAAO;AAAA,MACR,CAAC;AAEDH,oBAAAA,MAAI,aAAa;AAAA,QAChB,MAAM;AAAA,QACN,SAAS,YAAY,KAAK;AAAA,MAC3B,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,YAAY,OAAO;AAClB,WAAK,UAAU,OAAO,OAAO,CAAC;AAC9B,WAAK,gBAAgB,OAAO,OAAO,CAAC;AAAA,IACpC;AAAA;AAAA,IAGD,cAAc;AACbA,oBAAAA,MAAI,YAAY;AAAA,QACf,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,SAAS,CAAC,QAAQ;AAEjBA,wBAAAA,MAAI,YAAY;AAAA,YACf,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAGD,gBAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,cAAI,CAAC,OAAO;AACXA,0BAAG,MAAC,YAAW;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACP,CAAC;AAKD;AAAA,UACD;AAGAG,mCAAY,YAAY,IAAI,YAAY,EACtC,KAAK,gBAAc;AAEnB,iBAAK,YAAY,IAAI;AACrB,iBAAK,kBAAkB;AAEvBH,0BAAG,MAAC,YAAW;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACP,CAAC;AAAA,WACD,EACA,MAAM,SAAO;AACbA,0BAAG,MAAC,YAAW;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO,IAAI,WAAW;AAAA,cACtB,MAAM;AAAA,YACP,CAAC;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AAEb,UAAI,KAAK,mBAAmB,KAAK,gBAAgB,WAAW,OAAO,GAAG;AACrE,cAAM,WAAW,KAAK,gBAAgB,UAAU,CAAC;AACjDA,sBAAA,MAAA,MAAA,OAAA,iCAAY,qBAAqB,QAAQ;AACzC,eAAO;AAAA,MACR;AAGA,UAAI,KAAK,iBAAiB;AAEzB,YAAI,KAAK,gBAAgB,WAAW,MAAM,GAAG;AAC5C,iBAAO,KAAK;AAAA,QACb;AACA,cAAM,MAAMG,aAAW,YAAC,WAAW,KAAK,eAAe;AACvDH,sBAAA,MAAA,MAAA,OAAA,iCAAY,cAAc,GAAG;AAC7B,eAAO;AAAA,MACR;AAGAA,oBAAA,MAAA,MAAA,OAAA,iCAAY,aAAa,KAAK,SAAS;AACvC,aAAO,KAAK;AAAA,IACZ;AAAA;AAAA,IAGD,gBAAgB;AACf,UAAI,MAAM,KAAK;AAGf,UAAI,CAAC,KAAK;AACTA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEAA,oBAAA,MAAA,MAAA,OAAA,iCAAY,cAAc,GAAG;AAG7B,WAAK,QAAQ,QAAQ,KAAK,CAAC,QAAQ;AAClC,YAAI,KAAK;AACRA,wBAAc,MAAA,MAAA,SAAA,iCAAA,YAAY,GAAG;AAC7BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,aAAa,GAAG;AACfA,oBAAc,MAAA,MAAA,SAAA,iCAAA,WAAW,EAAE,MAAM;AACjCA,oBAAA,MAAA,MAAA,SAAA,iCAAc,SAAS,KAAK,SAAS;AACrCA,oBAAc,MAAA,MAAA,SAAA,iCAAA,YAAY,KAAK,eAAe;AAC9CA,oBAAA,MAAA,MAAA,SAAA,iCAAc,WAAW,KAAK,YAAW,CAAE;AAE3CA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACX,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,YAAY,GAAG;AACdA,oBAAA,MAAA,MAAA,OAAA,iCAAY,eAAe,KAAK,SAAS;AACzCA,oBAAY,MAAA,MAAA,OAAA,iCAAA,UAAU,EAAE,MAAM;AAAA,IAC9B;AAAA;AAAA,IAGD,cAAc;AACb,WAAK,YAAY;AACjB,WAAK,kBAAkB;AAAA,IACvB;AAAA;AAAA,IAGD,oBAAoB;AACnB,aAAO,IAAI,QAAQ,CAAC,YAAY;AAC/B,cAAM,cAAc,CAAA;AAGpB,aAAK,gBAAgB,QAAQ,CAAC,YAAY,UAAU;AACnD,sBAAY,KAAK;AAAA,YAChB,WAAW;AAAA,YACX,WAAW;AAAA,UACZ,CAAC;AAAA,QACF,CAAC;AAGD,YAAI,KAAK,iBAAiB;AACzB,sBAAY,KAAK;AAAA,YAChB,WAAW;AAAA,YACX,WAAW,KAAK;AAAA,UACjB,CAAC;AAAA,QACF;AAGA,gBAAQ,WAAW;AAAA,MACpB,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACd,UAAI,KAAK,cAAc,IAAI;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD,eAAO;AAAA,MACR;AAEA,UAAI,KAAK,cAAc,IAAI;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD,eAAO;AAAA,MACR;AAGA,WAAK,KAAK,cAAc,KAAK,UAAU,KAAK,WAC3C,EAAE,KAAK,cAAc,KAAK,UAAU,KAAK,SAAS;AAClDA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACX,CAAC;AACD,eAAO;AAAA,MACR;AAEA,UAAI,CAAC,KAAK,YAAY;AACrBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD,eAAO;AAAA,MACR;AAEA,UAAI,CAAC,KAAK,UAAU,QAAQ;AAC3BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD,eAAO;AAAA,MACR;AAEA,UAAI,CAAC,KAAK,aAAa,CAAC,KAAK,WAAW;AACvCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD,eAAO;AAAA,MACR;AAEA,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,MAAM,eAAe;AACpB,UAAI,CAAC,KAAK,aAAY;AAAI;AAG1BA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACR,CAAC;AAED,UAAI;AAEH,cAAM,cAAc,MAAM,KAAK;AAG/B,cAAM,SAASA,cAAG,MAAC,eAAe,QAAQ,KAAK;AAG/C,YAAI,KAAK,cAAc,MAAM,CAAC,KAAK,UAAU,KAAK,SAAS,GAAG;AAC7DA,wBAAG,MAAC,YAAW;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AACD;AAAA,QACD;AAGA,aAAK,uBAAsB;AAG3B,cAAM,aAAa;AAAA,UAClB,cAAc,KAAK,UAAU,KAAK,SAAS,EAAE;AAAA,UAC7C,UAAU,KAAK;AAAA,UACf,YAAY,KAAK,WAAW,KAAK,SAAS;AAAA,UAC1C,aAAa,KAAK;AAAA,UAClB,YAAY,KAAK;AAAA,UACjB,cAAc,KAAK,aAAa,KAAK,WAAW;AAAA,UAChD,YAAY,GAAG,KAAK,SAAS,IAAI,KAAK,SAAS;AAAA,UAC/C,gBAAgB;AAAA,UAChB,SAAS,KAAK;AAAA,UACd,YAAY;AAAA;AAGbA,sBAAY,MAAA,MAAA,OAAA,iCAAA,aAAa,UAAU;AAGnCI,kBAAQ,SAAC,YAAY,UAAU,EAC7B,KAAK,SAAO;AACZ,cAAI,IAAI,SAAS,KAAK;AAErBJ,0BAAG,MAAC,YAAW;AAGfA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACP,CAAC;AACD,uBAAW,MAAM;AAChBA,4BAAG,MAAC,aAAY;AAAA,YAChB,GAAE,IAAI;AAAA,iBAED;AACN,kBAAM,IAAI,MAAM,IAAI,WAAW,MAAM;AAAA,UACtC;AAAA,SACA,EACA,MAAM,SAAO;AACbA,wBAAG,MAAC,YAAW;AAGf,gBAAM,WAAW,IAAI,WAAW;AAChC,gBAAM,eAAe,SAAS,SAAS,OAAO,KACzC,SAAS,SAAS,OAAO,KACzB,SAAS,SAAS,IAAI,KACtB,SAAS,SAAS,KAAK,KACvB,SAAS,SAAS,IAAI;AAG3B,cAAI,cAAc;AAEjB,iBAAK,uBAAsB;AAG3BA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,aAAa;AAAA,YACd,CAAC;AAAA,iBACK;AAENA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YACX,CAAC;AAAA,UACF;AAAA,QACD,CAAC;AAAA,MACD,SAAO,OAAO;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,yBAAyB;AAExB,WAAK,oBAAoB;AAGzB,iBAAW,MAAM;AAChB,aAAK,oBAAoB;AAAA,MACzB,GAAE,GAAI;AAAA,IACP;AAAA;AAAA,IAGD,iBAAiB,UAAU;AAE1B,YAAM,eAAe,SAAS;AAC9B,UAAI,CAAC,KAAK,aAAa,CAAC,MAAM,QAAQ,KAAK,SAAS,GAAG;AACtDA,sBAAAA,sDAAc,qCAAqC;AACnD;AAAA,MACD;AACA,YAAM,QAAQ,KAAK,UAAU,UAAU,UAAQ,KAAK,SAAS,YAAY;AACzE,UAAI,QAAQ,IAAI;AACf,aAAK,YAAY;AACjBA,0EAAY,UAAU,QAAQ,eAAe,KAAK,EAAE;AAAA,aAC9C;AACNA,4BAAA,MAAA,QAAA,iCAAa,kBAAkB,QAAQ,MAAM;AAG7C,YAAI,KAAK,UAAU,WAAW,GAAG;AAChC,eAAK,YAAY;AACjBA,wBAAAA,MAAA,MAAA,OAAA,iCAAY,8BAA8B,KAAK,UAAU,CAAC,EAAE,IAAI,EAAE;AAAA,eAC5D;AACN,eAAK,YAAY;AAAA,QAClB;AAAA,MACD;AAAA,IACA;AAAA;AAAA,IAGD,sBAAsB;AAErB,UAAI,KAAK,YAAY;AAEpB,aAAK,aAAa,KAAK,WAAW,SAAQ,EAAG,QAAQ,OAAO,EAAE;AAE9D,YAAI,KAAK,WAAW,SAAS,GAAG;AAC/B,eAAK,aAAa,KAAK,WAAW,UAAU,GAAG,CAAC;AAAA,QACjD;AAEA,YAAI,KAAK,WAAW,WAAW,GAAG;AACjCA,wBAAY,MAAA,MAAA,OAAA,iCAAA,WAAW,KAAK,UAAU;AAAA,QACvC;AAAA,MACD;AAGA,UAAI,KAAK,QAAQ;AAEhB,aAAK,SAAS,KAAK,OAAO,SAAQ,EAAG,QAAQ,OAAO,EAAE;AAEtD,YAAI,KAAK,OAAO,SAAS,GAAG;AAC3B,eAAK,SAAS,KAAK,OAAO,UAAU,GAAG,CAAC;AAAA,QACzC;AAEA,YAAI,KAAK,OAAO,WAAW,GAAG;AAC7BA,wBAAA,MAAA,MAAA,OAAA,iCAAY,YAAY,KAAK,MAAM;AAAA,QACpC;AAAA,MACD;AAGA,UAAI,KAAK,QAAQ;AAEhB,aAAK,SAAS,KAAK,OAAO,SAAQ,EAAG,QAAQ,OAAO,EAAE;AAEtD,YAAI,KAAK,OAAO,SAAS,GAAG;AAC3B,eAAK,SAAS,KAAK,OAAO,UAAU,GAAG,CAAC;AAAA,QACzC;AAEA,YAAI,KAAK,OAAO,SAAS,GAAG;AAC3BA,wBAAA,MAAA,MAAA,OAAA,iCAAY,WAAW,KAAK,MAAM;AAAA,QACnC;AAAA,MACD;AAGA,WAAK,KAAK,cAAc,KAAK,UAAU,KAAK,WAC3C,EAAE,KAAK,cAAc,KAAK,UAAU,KAAK,SAAS;AAElDA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACX,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,yBAAyB;AAExB,UAAI,KAAK,cAAc,KAAK,UAAU,KAAK,QAAQ;AAElD,cAAM,mBAAmB,KAAK;AAC9B,cAAM,eAAe,KAAK;AAC1B,cAAM,eAAe,KAAK,OAAO,SAAS,GAAG,GAAG;AAChD,aAAK,mBAAmB,GAAG,gBAAgB,IAAI,YAAY,IAAI,YAAY;AAAA,aACrE;AACN,aAAK,mBAAmB;AAAA,MACzB;AAAA,IACA;AAAA,EACF;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/7BD,GAAG,WAAW,eAAe;"}