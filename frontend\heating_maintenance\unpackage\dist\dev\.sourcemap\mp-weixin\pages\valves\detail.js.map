{"version": 3, "file": "detail.js", "sources": ["pages/valves/detail.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdmFsdmVzL2RldGFpbC52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"valve-detail-page\">\r\n\t\t<!-- 顶部栏 -->\r\n\t\t<view class=\"header\">\r\n\t\t\t<view class=\"back-button\" @click=\"navigateBack\">\r\n\t\t\t\t<text class=\"iconfont icon-arrow-left\"></text>\r\n\t\t\t</view>\r\n\t\t\t<text class=\"page-title\">阀门详情</text>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 加载中 -->\r\n\t\t<view class=\"loading-container\" v-if=\"loading\">\r\n\t\t\t<uni-load-more :status=\"'loading'\" :content-text=\"loadingText\"></uni-load-more>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 阀门详情 -->\r\n\t\t<view class=\"valve-content\" v-else-if=\"valveDetail\">\r\n\t\t\t<!-- 状态指示部分 -->\r\n\t\t\t<view class=\"status-card\">\r\n\t\t\t\t<view class=\"status-icon\" :class=\"valveDetail.status\">\r\n\t\t\t\t\t<text class=\"iconfont\" :class=\"getStatusIcon(valveDetail.status)\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"status-info\">\r\n\t\t\t\t\t<view class=\"status-header\">\r\n\t\t\t\t\t\t<text class=\"valve-name\">{{ valveDetail.name }}</text>\r\n\t\t\t\t\t\t<view class=\"status-tag\" :class=\"'status-' + valveDetail.status\">{{ getStatusText(valveDetail.status) }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"status-detail\">\r\n\t\t\t\t\t\t<view class=\"detail-item\">\r\n\t\t\t\t\t\t\t<text class=\"detail-label\">当前开度</text>\r\n\t\t\t\t\t\t\t<text class=\"detail-value\">{{ valveDetail.openDegree }}%</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"detail-item\">\r\n\t\t\t\t\t\t\t<text class=\"detail-label\">上次操作</text>\r\n\t\t\t\t\t\t\t<text class=\"detail-value\">{{ valveDetail.lastOperationTime }} {{ valveDetail.operationType }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"detail-item\">\r\n\t\t\t\t\t\t\t<text class=\"detail-label\">操作人员</text>\r\n\t\t\t\t\t\t\t<text class=\"detail-value\">{{ valveDetail.operator }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 控制面板部分 -->\r\n\t\t\t<view class=\"control-panel\">\r\n\t\t\t\t<view class=\"panel-title\">控制面板</view>\r\n\t\t\t\t<view class=\"opening-control\">\r\n\t\t\t\t\t<text class=\"control-label\">开度调节：{{ targetOpenDegree }}%</text>\r\n\t\t\t\t\t<slider class=\"opening-slider\" :value=\"targetOpenDegree\" :min=\"0\" :max=\"100\" :step=\"1\" @change=\"handleOpenDegreeChange\" show-value />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"control-buttons\">\r\n\t\t\t\t\t<button class=\"control-btn set-btn\" @click=\"setOpenDegree\">设置开度</button>\r\n\t\t\t\t\t<button class=\"control-btn open-btn\" @click=\"fullOpen\">一键开启</button>\r\n\t\t\t\t\t<button class=\"control-btn close-btn\" @click=\"fullClose\">一键关闭</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 操作记录部分 -->\r\n\t\t\t<view class=\"operation-records\">\r\n\t\t\t\t<view class=\"records-title\">操作记录</view>\r\n\t\t\t\t<view class=\"timeline\">\r\n\t\t\t\t\t<view class=\"timeline-item\" v-for=\"(record, index) in valveDetail.operationRecords\" :key=\"index\">\r\n\t\t\t\t\t\t<view class=\"timeline-dot\"></view>\r\n\t\t\t\t\t\t<view class=\"timeline-content\">\r\n\t\t\t\t\t\t\t<text class=\"timeline-time\">{{ record.time }}</text>\r\n\t\t\t\t\t\t\t<text class=\"timeline-text\">{{ record.content }}</text>\r\n\t\t\t\t\t\t\t<text class=\"timeline-operator\">操作人：{{ record.operator }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 确认弹窗 -->\r\n\t\t<uni-popup ref=\"confirmPopup\" type=\"dialog\">\r\n\t\t\t<uni-popup-dialog \r\n\t\t\t\t:title=\"confirmDialog.title\" \r\n\t\t\t\t:content=\"confirmDialog.content\" \r\n\t\t\t\t:cancelText=\"'取消'\" \r\n\t\t\t\t:confirmText=\"'确认'\"\r\n\t\t\t\t@confirm=\"handleConfirmControl\"\r\n\t\t\t\t@close=\"handleCancelControl\">\r\n\t\t\t</uni-popup-dialog>\r\n\t\t</uni-popup>\r\n\t\t\r\n\t\t<!-- 结果提示 -->\r\n\t\t<uni-popup ref=\"resultPopup\" type=\"message\">\r\n\t\t\t<uni-popup-message \r\n\t\t\t\t:type=\"resultMessage.type\" \r\n\t\t\t\t:message=\"resultMessage.message\" \r\n\t\t\t\t:duration=\"2000\">\r\n\t\t\t</uni-popup-message>\r\n\t\t</uni-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tloading: true,\r\n\t\t\t\tloadingText: {\r\n\t\t\t\t\tcontentdown: '加载中...',\r\n\t\t\t\t\tcontentrefresh: '加载中...',\r\n\t\t\t\t\tcontentnomore: '没有更多数据'\r\n\t\t\t\t},\r\n\t\t\t\tvalveId: null,\r\n\t\t\t\tvalveDetail: null,\r\n\t\t\t\ttargetOpenDegree: 0,\r\n\t\t\t\t\r\n\t\t\t\t// 确认对话框\r\n\t\t\t\tconfirmDialog: {\r\n\t\t\t\t\ttitle: '操作确认',\r\n\t\t\t\t\tcontent: '',\r\n\t\t\t\t\taction: ''\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\t// 结果消息\r\n\t\t\t\tresultMessage: {\r\n\t\t\t\t\ttype: 'success',\r\n\t\t\t\t\tmessage: ''\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(option) {\r\n\t\t\tif (option.id) {\r\n\t\t\t\tthis.valveId = option.id;\r\n\t\t\t\tthis.loadValveDetail();\r\n\t\t\t} else {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '参数错误',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.navigateBack();\r\n\t\t\t\t}, 1500);\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 加载阀门详情\r\n\t\t\tloadValveDetail() {\r\n\t\t\t\tthis.loading = true;\r\n\t\t\t\t\r\n\t\t\t\t// 模拟API调用\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t// 模拟数据\r\n\t\t\t\t\tthis.valveDetail = {\r\n\t\t\t\t\t\tid: this.valveId,\r\n\t\t\t\t\t\tname: '东区小区 1号楼 1单元 101 入户阀门',\r\n\t\t\t\t\t\tstatus: 'open',\r\n\t\t\t\t\t\topenDegree: 80,\r\n\t\t\t\t\t\tlastOperationTime: '2025-04-03 14:35:20',\r\n\t\t\t\t\t\toperator: '系统',\r\n\t\t\t\t\t\toperationType: '自动开启(缴费成功)',\r\n\t\t\t\t\t\toperationRecords: [\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\ttime: '2025-04-03 14:35:20',\r\n\t\t\t\t\t\t\t\tcontent: '自动开启(缴费成功)',\r\n\t\t\t\t\t\t\t\toperator: '系统'\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\ttime: '2025-02-28 09:10:15',\r\n\t\t\t\t\t\t\t\tcontent: '手动关闭(欠费)',\r\n\t\t\t\t\t\t\t\toperator: '李四'\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\ttime: '2025-02-15 08:30:45',\r\n\t\t\t\t\t\t\t\tcontent: '开度调整至50%',\r\n\t\t\t\t\t\t\t\toperator: '张三'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t};\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.targetOpenDegree = this.valveDetail.openDegree;\r\n\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t}, 1000);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 处理开度变化\r\n\t\t\thandleOpenDegreeChange(e) {\r\n\t\t\t\tthis.targetOpenDegree = e.detail.value;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 设置开度\r\n\t\t\tsetOpenDegree() {\r\n\t\t\t\tif (this.targetOpenDegree === this.valveDetail.openDegree) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '开度未变化',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthis.confirmDialog = {\r\n\t\t\t\t\ttitle: '开度调整确认',\r\n\t\t\t\t\tcontent: `确定要将${this.valveDetail.name}的开度调整为${this.targetOpenDegree}%吗？`,\r\n\t\t\t\t\taction: 'setOpenDegree'\r\n\t\t\t\t};\r\n\t\t\t\t\r\n\t\t\t\tthis.$refs.confirmPopup.open();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 一键开启\r\n\t\t\tfullOpen() {\r\n\t\t\t\tif (this.valveDetail.status === 'open' && this.valveDetail.openDegree === 100) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '阀门已经完全开启',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthis.confirmDialog = {\r\n\t\t\t\t\ttitle: '操作确认',\r\n\t\t\t\t\tcontent: `确定要完全开启${this.valveDetail.name}吗？`,\r\n\t\t\t\t\taction: 'fullOpen'\r\n\t\t\t\t};\r\n\t\t\t\t\r\n\t\t\t\tthis.$refs.confirmPopup.open();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 一键关闭\r\n\t\t\tfullClose() {\r\n\t\t\t\tif (this.valveDetail.status === 'closed' && this.valveDetail.openDegree === 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '阀门已经完全关闭',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthis.confirmDialog = {\r\n\t\t\t\t\ttitle: '操作确认',\r\n\t\t\t\t\tcontent: `确定要完全关闭${this.valveDetail.name}吗？`,\r\n\t\t\t\t\taction: 'fullClose'\r\n\t\t\t\t};\r\n\t\t\t\t\r\n\t\t\t\tthis.$refs.confirmPopup.open();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 确认控制操作\r\n\t\t\thandleConfirmControl() {\r\n\t\t\t\tconst action = this.confirmDialog.action;\r\n\t\t\t\t\r\n\t\t\t\t// 准备请求参数\r\n\t\t\t\tconst params = {\r\n\t\t\t\t\tvalve_id: this.valveId\r\n\t\t\t\t};\r\n\t\t\t\t\r\n\t\t\t\t// 根据不同操作设置不同参数\r\n\t\t\t\tif (action === 'setOpenDegree') {\r\n\t\t\t\t\tparams.open_degree = this.targetOpenDegree;\r\n\t\t\t\t} else if (action === 'fullOpen') {\r\n\t\t\t\t\tparams.open_degree = 100;\r\n\t\t\t\t} else if (action === 'fullClose') {\r\n\t\t\t\t\tparams.open_degree = 0;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 模拟API调用\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '操作中...'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 模拟API调用\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t// 更新阀门状态\r\n\t\t\t\t\tif (action === 'setOpenDegree') {\r\n\t\t\t\t\t\tthis.valveDetail.openDegree = this.targetOpenDegree;\r\n\t\t\t\t\t\tthis.valveDetail.status = this.targetOpenDegree > 0 ? 'open' : 'closed';\r\n\t\t\t\t\t\tthis.addOperationRecord(`开度调整至${this.targetOpenDegree}%`);\r\n\t\t\t\t\t} else if (action === 'fullOpen') {\r\n\t\t\t\t\t\tthis.valveDetail.openDegree = 100;\r\n\t\t\t\t\t\tthis.valveDetail.status = 'open';\r\n\t\t\t\t\t\tthis.targetOpenDegree = 100;\r\n\t\t\t\t\t\tthis.addOperationRecord('完全开启阀门');\r\n\t\t\t\t\t} else if (action === 'fullClose') {\r\n\t\t\t\t\t\tthis.valveDetail.openDegree = 0;\r\n\t\t\t\t\t\tthis.valveDetail.status = 'closed';\r\n\t\t\t\t\t\tthis.targetOpenDegree = 0;\r\n\t\t\t\t\t\tthis.addOperationRecord('完全关闭阀门');\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 显示成功消息\r\n\t\t\t\t\tthis.resultMessage = {\r\n\t\t\t\t\t\ttype: 'success',\r\n\t\t\t\t\t\tmessage: '操作成功'\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthis.$refs.resultPopup.open();\r\n\t\t\t\t}, 1500);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 取消控制操作\r\n\t\t\thandleCancelControl() {\r\n\t\t\t\t// 不做任何操作，仅关闭弹窗\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 添加操作记录\r\n\t\t\taddOperationRecord(content) {\r\n\t\t\t\tconst now = new Date();\r\n\t\t\t\tconst timeStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;\r\n\t\t\t\t\r\n\t\t\t\t// 添加到记录列表开头\r\n\t\t\t\tthis.valveDetail.operationRecords.unshift({\r\n\t\t\t\t\ttime: timeStr,\r\n\t\t\t\t\tcontent: content,\r\n\t\t\t\t\toperator: '当前用户' // 实际应用中应该从用户信息中获取\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 更新最后操作信息\r\n\t\t\t\tthis.valveDetail.lastOperationTime = timeStr;\r\n\t\t\t\tthis.valveDetail.operator = '当前用户';\r\n\t\t\t\tthis.valveDetail.operationType = content;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取状态图标\r\n\t\t\tgetStatusIcon(status) {\r\n\t\t\t\tconst iconMap = {\r\n\t\t\t\t\t'open': 'icon-check',\r\n\t\t\t\t\t'closed': 'icon-clear',\r\n\t\t\t\t\t'error': 'icon-alarm'\r\n\t\t\t\t};\r\n\t\t\t\treturn iconMap[status] || 'icon-info';\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取状态文本\r\n\t\t\tgetStatusText(status) {\r\n\t\t\t\tconst statusMap = {\r\n\t\t\t\t\t'open': '已开启',\r\n\t\t\t\t\t'closed': '已关闭',\r\n\t\t\t\t\t'error': '异常'\r\n\t\t\t\t};\r\n\t\t\t\treturn statusMap[status] || status;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 返回上一页\r\n\t\t\tnavigateBack() {\r\n\t\t\t\tuni.navigateBack();\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.valve-detail-page {\r\n\tmin-height: 100vh;\r\n\tbackground-color: #f5f7fa;\r\n\tpadding-bottom: 30rpx;\r\n}\r\n\r\n.header {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\theight: 88rpx;\r\n\tbackground-color: #fff;\r\n\tpadding: 0 30rpx;\r\n\tposition: relative;\r\n\tbox-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);\r\n\t\r\n\t.back-button {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\t\r\n\t.page-title {\r\n\t\tflex: 1;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 34rpx;\r\n\t\tfont-weight: 500;\r\n\t\tpadding-right: 60rpx;\r\n\t}\r\n}\r\n\r\n.loading-container {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 100rpx 0;\r\n}\r\n\r\n.valve-content {\r\n\tpadding: 20rpx 30rpx;\r\n}\r\n\r\n.status-card {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 30rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\tdisplay: flex;\r\n\t\r\n\t.status-icon {\r\n\t\twidth: 120rpx;\r\n\t\theight: 120rpx;\r\n\t\tborder-radius: 60rpx;\r\n\t\tmargin-right: 24rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\t\r\n\t\t&.open {\r\n\t\t\tbackground-color: rgba(46, 204, 113, 0.1);\r\n\t\t\t\r\n\t\t\t.iconfont {\r\n\t\t\t\tcolor: #2ecc71;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t&.closed {\r\n\t\t\tbackground-color: rgba(149, 165, 166, 0.1);\r\n\t\t\t\r\n\t\t\t.iconfont {\r\n\t\t\t\tcolor: #95a5a6;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t&.error {\r\n\t\t\tbackground-color: rgba(231, 76, 60, 0.1);\r\n\t\t\t\r\n\t\t\t.iconfont {\r\n\t\t\t\tcolor: #e74c3c;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.iconfont {\r\n\t\t\tfont-size: 60rpx;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.status-info {\r\n\t\tflex: 1;\r\n\t\t\r\n\t\t.status-header {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin-bottom: 16rpx;\r\n\t\t\t\r\n\t\t\t.valve-name {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.status-detail {\r\n\t\t\t.detail-item {\r\n\t\t\t\tmargin-bottom: 12rpx;\r\n\t\t\t\t\r\n\t\t\t\t.detail-label {\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.detail-value {\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.status-tag {\r\n\tpadding: 6rpx 16rpx;\r\n\tborder-radius: 30rpx;\r\n\tfont-size: 24rpx;\r\n\tcolor: #fff;\r\n\t\r\n\t&.status-open {\r\n\t\tbackground-color: #2ecc71;\r\n\t}\r\n\t\r\n\t&.status-closed {\r\n\t\tbackground-color: #95a5a6;\r\n\t}\r\n\t\r\n\t&.status-error {\r\n\t\tbackground-color: #e74c3c;\r\n\t}\r\n}\r\n\r\n.control-panel {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 30rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\t\r\n\t.panel-title {\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tposition: relative;\r\n\t\tpadding-left: 20rpx;\r\n\t\t\r\n\t\t&::before {\r\n\t\t\tcontent: '';\r\n\t\t\tposition: absolute;\r\n\t\t\tleft: 0;\r\n\t\t\ttop: 8rpx;\r\n\t\t\twidth: 6rpx;\r\n\t\t\theight: 28rpx;\r\n\t\t\tbackground-color: #1989fa;\r\n\t\t\tborder-radius: 3rpx;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.opening-control {\r\n\t\tmargin-bottom: 30rpx;\r\n\t\t\r\n\t\t.control-label {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #333;\r\n\t\t\tmargin-bottom: 10rpx;\r\n\t\t\tdisplay: block;\r\n\t\t}\r\n\t\t\r\n\t\t.opening-slider {\r\n\t\t\tmargin: 20rpx 0;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.control-buttons {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\t\r\n\t\t.control-btn {\r\n\t\t\twidth: 30%;\r\n\t\t\theight: 80rpx;\r\n\t\t\tline-height: 80rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\t\r\n\t\t\t&.set-btn {\r\n\t\t\t\tbackground-color: #1989fa;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.open-btn {\r\n\t\t\t\tbackground-color: #2ecc71;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.close-btn {\r\n\t\t\t\tbackground-color: #e74c3c;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.operation-records {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 30rpx;\r\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\t\r\n\t.records-title {\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tposition: relative;\r\n\t\tpadding-left: 20rpx;\r\n\t\t\r\n\t\t&::before {\r\n\t\t\tcontent: '';\r\n\t\t\tposition: absolute;\r\n\t\t\tleft: 0;\r\n\t\t\ttop: 8rpx;\r\n\t\t\twidth: 6rpx;\r\n\t\t\theight: 28rpx;\r\n\t\t\tbackground-color: #1989fa;\r\n\t\t\tborder-radius: 3rpx;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.timeline {\r\n\t\tposition: relative;\r\n\t\tpadding-left: 30rpx;\r\n\t\t\r\n\t\t&::before {\r\n\t\t\tcontent: '';\r\n\t\t\tposition: absolute;\r\n\t\t\tleft: 10rpx;\r\n\t\t\ttop: 0;\r\n\t\t\tbottom: 0;\r\n\t\t\twidth: 2rpx;\r\n\t\t\tbackground-color: #e0e0e0;\r\n\t\t}\r\n\t\t\r\n\t\t.timeline-item {\r\n\t\t\tposition: relative;\r\n\t\t\tpadding-bottom: 30rpx;\r\n\t\t\t\r\n\t\t\t&:last-child {\r\n\t\t\t\tpadding-bottom: 0;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.timeline-dot {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tleft: -30rpx;\r\n\t\t\t\ttop: 10rpx;\r\n\t\t\t\twidth: 20rpx;\r\n\t\t\t\theight: 20rpx;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tbackground-color: #1989fa;\r\n\t\t\t\tz-index: 1;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.timeline-content {\r\n\t\t\t\t.timeline-time {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.timeline-text {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.timeline-operator {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</style> ", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/valves/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAkGC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,SAAS;AAAA,MACT,aAAa;AAAA,QACZ,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,eAAe;AAAA,MACf;AAAA,MACD,SAAS;AAAA,MACT,aAAa;AAAA,MACb,kBAAkB;AAAA;AAAA,MAGlB,eAAe;AAAA,QACd,OAAO;AAAA,QACP,SAAS;AAAA,QACT,QAAQ;AAAA,MACR;AAAA;AAAA,MAGD,eAAe;AAAA,QACd,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,IACD;AAAA,EACA;AAAA,EACD,OAAO,QAAQ;AACd,QAAI,OAAO,IAAI;AACd,WAAK,UAAU,OAAO;AACtB,WAAK,gBAAe;AAAA,WACd;AACNA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AACD,iBAAW,MAAM;AAChB,aAAK,aAAY;AAAA,MACjB,GAAE,IAAI;AAAA,IACR;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,kBAAkB;AACjB,WAAK,UAAU;AAGf,iBAAW,MAAM;AAEhB,aAAK,cAAc;AAAA,UAClB,IAAI,KAAK;AAAA,UACT,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,mBAAmB;AAAA,UACnB,UAAU;AAAA,UACV,eAAe;AAAA,UACf,kBAAkB;AAAA,YACjB;AAAA,cACC,MAAM;AAAA,cACN,SAAS;AAAA,cACT,UAAU;AAAA,YACV;AAAA,YACD;AAAA,cACC,MAAM;AAAA,cACN,SAAS;AAAA,cACT,UAAU;AAAA,YACV;AAAA,YACD;AAAA,cACC,MAAM;AAAA,cACN,SAAS;AAAA,cACT,UAAU;AAAA,YACX;AAAA,UACD;AAAA;AAGD,aAAK,mBAAmB,KAAK,YAAY;AACzC,aAAK,UAAU;AAAA,MACf,GAAE,GAAI;AAAA,IACP;AAAA;AAAA,IAGD,uBAAuB,GAAG;AACzB,WAAK,mBAAmB,EAAE,OAAO;AAAA,IACjC;AAAA;AAAA,IAGD,gBAAgB;AACf,UAAI,KAAK,qBAAqB,KAAK,YAAY,YAAY;AAC1DA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,WAAK,gBAAgB;AAAA,QACpB,OAAO;AAAA,QACP,SAAS,OAAO,KAAK,YAAY,IAAI,SAAS,KAAK,gBAAgB;AAAA,QACnE,QAAQ;AAAA;AAGT,WAAK,MAAM,aAAa;IACxB;AAAA;AAAA,IAGD,WAAW;AACV,UAAI,KAAK,YAAY,WAAW,UAAU,KAAK,YAAY,eAAe,KAAK;AAC9EA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,WAAK,gBAAgB;AAAA,QACpB,OAAO;AAAA,QACP,SAAS,UAAU,KAAK,YAAY,IAAI;AAAA,QACxC,QAAQ;AAAA;AAGT,WAAK,MAAM,aAAa;IACxB;AAAA;AAAA,IAGD,YAAY;AACX,UAAI,KAAK,YAAY,WAAW,YAAY,KAAK,YAAY,eAAe,GAAG;AAC9EA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,WAAK,gBAAgB;AAAA,QACpB,OAAO;AAAA,QACP,SAAS,UAAU,KAAK,YAAY,IAAI;AAAA,QACxC,QAAQ;AAAA;AAGT,WAAK,MAAM,aAAa;IACxB;AAAA;AAAA,IAGD,uBAAuB;AACtB,YAAM,SAAS,KAAK,cAAc;AAGnB,OAAA;AAAA,QACd,UAAU,KAAK;AAAA,MACf;AAGD,UAAI,WAAW,iBAAiB;AACV,aAAK;AAAA,MAK3B;AAGAA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACR,CAAC;AAGD,iBAAW,MAAM;AAEhB,YAAI,WAAW,iBAAiB;AAC/B,eAAK,YAAY,aAAa,KAAK;AACnC,eAAK,YAAY,SAAS,KAAK,mBAAmB,IAAI,SAAS;AAC/D,eAAK,mBAAmB,QAAQ,KAAK,gBAAgB,GAAG;AAAA,mBAC9C,WAAW,YAAY;AACjC,eAAK,YAAY,aAAa;AAC9B,eAAK,YAAY,SAAS;AAC1B,eAAK,mBAAmB;AACxB,eAAK,mBAAmB,QAAQ;AAAA,mBACtB,WAAW,aAAa;AAClC,eAAK,YAAY,aAAa;AAC9B,eAAK,YAAY,SAAS;AAC1B,eAAK,mBAAmB;AACxB,eAAK,mBAAmB,QAAQ;AAAA,QACjC;AAEAA,sBAAG,MAAC,YAAW;AAGf,aAAK,gBAAgB;AAAA,UACpB,MAAM;AAAA,UACN,SAAS;AAAA;AAEV,aAAK,MAAM,YAAY;MACvB,GAAE,IAAI;AAAA,IACP;AAAA;AAAA,IAGD,sBAAsB;AAAA,IAErB;AAAA;AAAA,IAGD,mBAAmB,SAAS;AAC3B,YAAM,MAAM,oBAAI;AAChB,YAAM,UAAU,GAAG,IAAI,YAAa,CAAA,IAAI,OAAO,IAAI,SAAW,IAAE,CAAC,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,IAAI,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,IAAI,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,IAAI,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,IAAI,YAAY,EAAE,SAAS,GAAG,GAAG,CAAC;AAGlQ,WAAK,YAAY,iBAAiB,QAAQ;AAAA,QACzC,MAAM;AAAA,QACN;AAAA,QACA,UAAU;AAAA;AAAA,MACX,CAAC;AAGD,WAAK,YAAY,oBAAoB;AACrC,WAAK,YAAY,WAAW;AAC5B,WAAK,YAAY,gBAAgB;AAAA,IACjC;AAAA;AAAA,IAGD,cAAc,QAAQ;AACrB,YAAM,UAAU;AAAA,QACf,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,SAAS;AAAA;AAEV,aAAO,QAAQ,MAAM,KAAK;AAAA,IAC1B;AAAA;AAAA,IAGD,cAAc,QAAQ;AACrB,YAAM,YAAY;AAAA,QACjB,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,SAAS;AAAA;AAEV,aAAO,UAAU,MAAM,KAAK;AAAA,IAC5B;AAAA;AAAA,IAGD,eAAe;AACdA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrVD,GAAG,WAAW,eAAe;"}