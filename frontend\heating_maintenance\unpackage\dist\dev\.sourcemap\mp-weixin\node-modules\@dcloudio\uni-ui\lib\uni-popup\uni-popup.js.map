{"version": 3, "file": "uni-popup.js", "sources": ["node_modules/@dcloudio/uni-ui/lib/uni-popup/uni-popup.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RTovdGFpYm9fY29tcGFueS90Yl9wcm9qZWN0L3NoYWFueGlfamllbWluZ19uZXdfZW5lcmd5X2NvbXBhbnkvNC1Tb3VyY2UvYXBwL2Zyb250ZW5kL2hlYXRpbmdfbWFpbnRlbmFuY2Uvbm9kZV9tb2R1bGVzL0BkY2xvdWRpby91bmktdWkvbGliL3VuaS1wb3B1cC91bmktcG9wdXAudnVl"], "sourcesContent": ["<template>\n\t<view v-if=\"showPopup\" class=\"uni-popup\" :class=\"[popupstyle, isDesktop ? 'fixforpc-z-index' : '']\">\n\t\t<view @touchstart=\"touchstart\">\n\t\t\t<uni-transition key=\"1\" v-if=\"maskShow\" name=\"mask\" mode-class=\"fade\" :styles=\"maskClass\"\n\t\t\t\t:duration=\"duration\" :show=\"showTrans\" @click=\"onTap\" />\n\t\t\t<uni-transition key=\"2\" :mode-class=\"ani\" name=\"content\" :styles=\"transClass\" :duration=\"duration\"\n\t\t\t\t:show=\"showTrans\" @click=\"onTap\">\n\t\t\t\t<view class=\"uni-popup__wrapper\" :style=\"getStyles\" :class=\"[popupstyle]\" @click=\"clear\">\n\t\t\t\t\t<slot />\n\t\t\t\t</view>\n\t\t\t</uni-transition>\n\t\t</view>\n\t\t<!-- #ifdef H5 -->\n\t\t<keypress v-if=\"maskShow\" @esc=\"onTap\" />\n\t\t<!-- #endif -->\n\t</view>\n</template>\n\n<script>\n\t// #ifdef H5\n\timport keypress from './keypress.js'\n\t// #endif\n\n\t/**\n\t * PopUp 弹出层\n\t * @description 弹出层组件，为了解决遮罩弹层的问题\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=329\n\t * @property {String} type = [top|center|bottom|left|right|message|dialog|share] 弹出方式\n\t * \t@value top 顶部弹出\n\t * \t@value center 中间弹出\n\t * \t@value bottom 底部弹出\n\t * \t@value left\t\t左侧弹出\n\t * \t@value right  右侧弹出\n\t * \t@value message 消息提示\n\t * \t@value dialog 对话框\n\t * \t@value share 底部分享示例\n\t * @property {Boolean} animation = [true|false] 是否开启动画\n\t * @property {Boolean} maskClick = [true|false] 蒙版点击是否关闭弹窗(废弃)\n\t * @property {Boolean} isMaskClick = [true|false] 蒙版点击是否关闭弹窗\n\t * @property {String}  backgroundColor 主窗口背景色\n\t * @property {String}  maskBackgroundColor 蒙版颜色\n\t * @property {String}  borderRadius 设置圆角(左上、右上、右下和左下) 示例:\"10px 10px 10px 10px\"\n\t * @property {Boolean} safeArea\t\t   是否适配底部安全区\n\t * @event {Function} change 打开关闭弹窗触发，e={show: false}\n\t * @event {Function} maskClick 点击遮罩触发\n\t */\n\n\texport default {\n\t\tname: 'uniPopup',\n\t\tcomponents: {\n\t\t\t// #ifdef H5\n\t\t\tkeypress\n\t\t\t// #endif\n\t\t},\n\t\temits: ['change', 'maskClick'],\n\t\tprops: {\n\t\t\t// 开启动画\n\t\t\tanimation: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t},\n\t\t\t// 弹出层类型，可选值，top: 顶部弹出层；bottom：底部弹出层；center：全屏弹出层\n\t\t\t// message: 消息提示 ; dialog : 对话框\n\t\t\ttype: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'center'\n\t\t\t},\n\t\t\t// maskClick\n\t\t\tisMaskClick: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: null\n\t\t\t},\n\t\t\t// TODO 2 个版本后废弃属性 ，使用 isMaskClick\n\t\t\tmaskClick: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: null\n\t\t\t},\n\t\t\tbackgroundColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'none'\n\t\t\t},\n\t\t\tsafeArea: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t},\n\t\t\tmaskBackgroundColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'rgba(0, 0, 0, 0.4)'\n\t\t\t},\n\t\t\tborderRadius:{\n\t\t\t\ttype: String,\n\t\t\t}\n\t\t},\n\n\t\twatch: {\n\t\t\t/**\n\t\t\t * 监听type类型\n\t\t\t */\n\t\t\ttype: {\n\t\t\t\thandler: function(type) {\n\t\t\t\t\tif (!this.config[type]) return\n\t\t\t\t\tthis[this.config[type]](true)\n\t\t\t\t},\n\t\t\t\timmediate: true\n\t\t\t},\n\t\t\tisDesktop: {\n\t\t\t\thandler: function(newVal) {\n\t\t\t\t\tif (!this.config[newVal]) return\n\t\t\t\t\tthis[this.config[this.type]](true)\n\t\t\t\t},\n\t\t\t\timmediate: true\n\t\t\t},\n\t\t\t/**\n\t\t\t * 监听遮罩是否可点击\n\t\t\t * @param {Object} val\n\t\t\t */\n\t\t\tmaskClick: {\n\t\t\t\thandler: function(val) {\n\t\t\t\t\tthis.mkclick = val\n\t\t\t\t},\n\t\t\t\timmediate: true\n\t\t\t},\n\t\t\tisMaskClick: {\n\t\t\t\thandler: function(val) {\n\t\t\t\t\tthis.mkclick = val\n\t\t\t\t},\n\t\t\t\timmediate: true\n\t\t\t},\n\t\t\t// H5 下禁止底部滚动\n\t\t\tshowPopup(show) {\n\t\t\t\t// #ifdef H5\n\t\t\t\t// fix by mehaotian 处理 h5 滚动穿透的问题\n\t\t\t\tdocument.getElementsByTagName('body')[0].style.overflow = show ? 'hidden' : 'visible'\n\t\t\t\t// #endif\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tduration: 300,\n\t\t\t\tani: [],\n\t\t\t\tshowPopup: false,\n\t\t\t\tshowTrans: false,\n\t\t\t\tpopupWidth: 0,\n\t\t\t\tpopupHeight: 0,\n\t\t\t\tconfig: {\n\t\t\t\t\ttop: 'top',\n\t\t\t\t\tbottom: 'bottom',\n\t\t\t\t\tcenter: 'center',\n\t\t\t\t\tleft: 'left',\n\t\t\t\t\tright: 'right',\n\t\t\t\t\tmessage: 'top',\n\t\t\t\t\tdialog: 'center',\n\t\t\t\t\tshare: 'bottom'\n\t\t\t\t},\n\t\t\t\tmaskClass: {\n\t\t\t\t\tposition: 'fixed',\n\t\t\t\t\tbottom: 0,\n\t\t\t\t\ttop: 0,\n\t\t\t\t\tleft: 0,\n\t\t\t\t\tright: 0,\n\t\t\t\t\tbackgroundColor: 'rgba(0, 0, 0, 0.4)'\n\t\t\t\t},\n\t\t\t\ttransClass: {\n\t\t\t\t\tbackgroundColor: 'transparent',\n\t\t\t\t\tborderRadius: this.borderRadius || \"0\",\n\t\t\t\t\tposition: 'fixed',\n\t\t\t\t\tleft: 0,\n\t\t\t\t\tright: 0\n\t\t\t\t},\n\t\t\t\tmaskShow: true,\n\t\t\t\tmkclick: true,\n\t\t\t\tpopupstyle: 'top'\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tgetStyles() {\n\t\t\t\tlet res = { backgroundColor: this.bg };\n\t\t\t\tif (this.borderRadius || \"0\") {\n\t\t\t\t\tres = Object.assign(res, { borderRadius: this.borderRadius })\n\t\t\t\t}\n\t\t\t\treturn res;\n\t\t\t},\n\t\t\tisDesktop() {\n\t\t\t\treturn this.popupWidth >= 500 && this.popupHeight >= 500\n\t\t\t},\n\t\t\tbg() {\n\t\t\t\tif (this.backgroundColor === '' || this.backgroundColor === 'none') {\n\t\t\t\t\treturn 'transparent'\n\t\t\t\t}\n\t\t\t\treturn this.backgroundColor\n\t\t\t}\n\t\t},\n\t\tmounted() {\n\t\t\tconst fixSize = () => {\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\tconst {\n\t\t\t\t\twindowWidth,\n\t\t\t\t\twindowHeight,\n\t\t\t\t\twindowTop,\n\t\t\t\t\tsafeArea,\n\t\t\t\t\tscreenHeight,\n\t\t\t\t\tsafeAreaInsets\n\t\t\t\t} = uni.getWindowInfo()\n\t\t\t\t// #endif\n\t\t\t\t// #ifndef MP-WEIXIN\n\t\t\t\tconst {\n\t\t\t\t\twindowWidth,\n\t\t\t\t\twindowHeight,\n\t\t\t\t\twindowTop,\n\t\t\t\t\tsafeArea,\n\t\t\t\t\tscreenHeight,\n\t\t\t\t\tsafeAreaInsets\n\t\t\t\t} = uni.getSystemInfoSync()\n\t\t\t\t// #endif\n\t\t\t\tthis.popupWidth = windowWidth\n\t\t\t\tthis.popupHeight = windowHeight + (windowTop || 0)\n\t\t\t\t// TODO fix by mehaotian 是否适配底部安全区 ,目前微信ios 、和 app ios 计算有差异，需要框架修复\n\t\t\t\tif (safeArea && this.safeArea) {\n\t\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\t\tthis.safeAreaInsets = screenHeight - safeArea.bottom\n\t\t\t\t\t// #endif\n\t\t\t\t\t// #ifndef MP-WEIXIN\n\t\t\t\t\tthis.safeAreaInsets = safeAreaInsets.bottom\n\t\t\t\t\t// #endif\n\t\t\t\t} else {\n\t\t\t\t\tthis.safeAreaInsets = 0\n\t\t\t\t}\n\t\t\t}\n\t\t\tfixSize()\n\t\t\t// #ifdef H5\n\t\t\t// window.addEventListener('resize', fixSize)\n\t\t\t// this.$once('hook:beforeDestroy', () => {\n\t\t\t// \twindow.removeEventListener('resize', fixSize)\n\t\t\t// })\n\t\t\t// #endif\n\t\t},\n\t\t// #ifndef VUE3\n\t\t// TODO vue2\n\t\tdestroyed() {\n\t\t\tthis.setH5Visible()\n\t\t},\n\t\t// #endif\n\t\t// #ifdef VUE3\n\t\t// TODO vue3\n\t\tunmounted() {\n\t\t\tthis.setH5Visible()\n\t\t},\n\t\t// #endif\n\t\tactivated() {\n   \t  this.setH5Visible(!this.showPopup);\n    },\n    deactivated() {\n      this.setH5Visible(true);\n    },\n\t\tcreated() {\n\t\t\t// this.mkclick =  this.isMaskClick || this.maskClick\n\t\t\tif (this.isMaskClick === null && this.maskClick === null) {\n\t\t\t\tthis.mkclick = true\n\t\t\t} else {\n\t\t\t\tthis.mkclick = this.isMaskClick !== null ? this.isMaskClick : this.maskClick\n\t\t\t}\n\t\t\tif (this.animation) {\n\t\t\t\tthis.duration = 300\n\t\t\t} else {\n\t\t\t\tthis.duration = 0\n\t\t\t}\n\t\t\t// TODO 处理 message 组件生命周期异常的问题\n\t\t\tthis.messageChild = null\n\t\t\t// TODO 解决头条冒泡的问题\n\t\t\tthis.clearPropagation = false\n\t\t\tthis.maskClass.backgroundColor = this.maskBackgroundColor\n\t\t},\n\t\tmethods: {\n\t\t\tsetH5Visible(visible = true) {\n\t\t\t\t// #ifdef H5\n\t\t\t\t// fix by mehaotian 处理 h5 滚动穿透的问题\n\t\t\t\tdocument.getElementsByTagName('body')[0].style.overflow =  visible ? \"visible\" : \"hidden\";\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t/**\n\t\t\t * 公用方法，不显示遮罩层\n\t\t\t */\n\t\t\tcloseMask() {\n\t\t\t\tthis.maskShow = false\n\t\t\t},\n\t\t\t/**\n\t\t\t * 公用方法，遮罩层禁止点击\n\t\t\t */\n\t\t\tdisableMask() {\n\t\t\t\tthis.mkclick = false\n\t\t\t},\n\t\t\t// TODO nvue 取消冒泡\n\t\t\tclear(e) {\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\te.stopPropagation()\n\t\t\t\t// #endif\n\t\t\t\tthis.clearPropagation = true\n\t\t\t},\n\n\t\t\topen(direction) {\n\t\t\t\t// fix by mehaotian 处理快速打开关闭的情况\n\t\t\t\tif (this.showPopup) {\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tlet innerType = ['top', 'center', 'bottom', 'left', 'right', 'message', 'dialog', 'share']\n\t\t\t\tif (!(direction && innerType.indexOf(direction) !== -1)) {\n\t\t\t\t\tdirection = this.type\n\t\t\t\t}\n\t\t\t\tif (!this.config[direction]) {\n\t\t\t\t\tconsole.error('缺少类型：', direction)\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tthis[this.config[direction]]()\n\t\t\t\tthis.$emit('change', {\n\t\t\t\t\tshow: true,\n\t\t\t\t\ttype: direction\n\t\t\t\t})\n\t\t\t},\n\t\t\tclose(type) {\n\t\t\t\tthis.showTrans = false\n\t\t\t\tthis.$emit('change', {\n\t\t\t\t\tshow: false,\n\t\t\t\t\ttype: this.type\n\t\t\t\t})\n\t\t\t\tclearTimeout(this.timer)\n\t\t\t\t// // 自定义关闭事件\n\t\t\t\t// this.customOpen && this.customClose()\n\t\t\t\tthis.timer = setTimeout(() => {\n\t\t\t\t\tthis.showPopup = false\n\t\t\t\t}, 300)\n\t\t\t},\n\t\t\t// TODO 处理冒泡事件，头条的冒泡事件有问题 ，先这样兼容\n\t\t\ttouchstart() {\n\t\t\t\tthis.clearPropagation = false\n\t\t\t},\n\n\t\t\tonTap() {\n\t\t\t\tif (this.clearPropagation) {\n\t\t\t\t\t// fix by mehaotian 兼容 nvue\n\t\t\t\t\tthis.clearPropagation = false\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tthis.$emit('maskClick')\n\t\t\t\tif (!this.mkclick) return\n\t\t\t\tthis.close()\n\t\t\t},\n\t\t\t/**\n\t\t\t * 顶部弹出样式处理\n\t\t\t */\n\t\t\ttop(type) {\n\t\t\t\tthis.popupstyle = this.isDesktop ? 'fixforpc-top' : 'top'\n\t\t\t\tthis.ani = ['slide-top']\n\t\t\t\tthis.transClass = {\n\t\t\t\t\tposition: 'fixed',\n\t\t\t\t\tleft: 0,\n\t\t\t\t\tright: 0,\n\t\t\t\t\tbackgroundColor: this.bg,\n\t\t\t\t\tborderRadius:this.borderRadius || \"0\"\n\t\t\t\t}\n\t\t\t\t// TODO 兼容 type 属性 ，后续会废弃\n\t\t\t\tif (type) return\n\t\t\t\tthis.showPopup = true\n\t\t\t\tthis.showTrans = true\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tthis.showPoptrans()\n\t\t\t\t\tif (this.messageChild && this.type === 'message') {\n\t\t\t\t\t\tthis.messageChild.timerClose()\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\t/**\n\t\t\t * 底部弹出样式处理\n\t\t\t */\n\t\t\tbottom(type) {\n\t\t\t\tthis.popupstyle = 'bottom'\n\t\t\t\tthis.ani = ['slide-bottom']\n\t\t\t\tthis.transClass = {\n\t\t\t\t\tposition: 'fixed',\n\t\t\t\t\tleft: 0,\n\t\t\t\t\tright: 0,\n\t\t\t\t\tbottom: 0,\n\t\t\t\t\tpaddingBottom: this.safeAreaInsets + 'px',\n\t\t\t\t\tbackgroundColor: this.bg,\n\t\t\t\t\tborderRadius:this.borderRadius || \"0\",\n\t\t\t\t}\n\t\t\t\t// TODO 兼容 type 属性 ，后续会废弃\n\t\t\t\tif (type) return\n\t\t\t\tthis.showPoptrans()\n\t\t\t},\n\t\t\t/**\n\t\t\t * 中间弹出样式处理\n\t\t\t */\n\t\t\tcenter(type) {\n\t\t\t\tthis.popupstyle = 'center'\n\t\t\t\t//微信小程序下，组合动画会出现文字向上闪动问题，再此做特殊处理\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\t\tthis.ani = ['fade']\n\t\t\t\t// #endif\n\t\t\t\t// #ifndef MP-WEIXIN\n\t\t\t\t\tthis.ani = ['zoom-out', 'fade']\n\t\t\t\t// #endif\n\t\t\t\tthis.transClass = {\n\t\t\t\t\tposition: 'fixed',\n\t\t\t\t\t/* #ifndef APP-NVUE */\n\t\t\t\t\tdisplay: 'flex',\n\t\t\t\t\tflexDirection: 'column',\n\t\t\t\t\t/* #endif */\n\t\t\t\t\tbottom: 0,\n\t\t\t\t\tleft: 0,\n\t\t\t\t\tright: 0,\n\t\t\t\t\ttop: 0,\n\t\t\t\t\tjustifyContent: 'center',\n\t\t\t\t\talignItems: 'center',\n\t\t\t\t\tborderRadius:this.borderRadius || \"0\"\n\t\t\t\t}\n\t\t\t\t// TODO 兼容 type 属性 ，后续会废弃\n\t\t\t\tif (type) return\n\t\t\t\tthis.showPoptrans()\n\t\t\t},\n\t\t\tleft(type) {\n\t\t\t\tthis.popupstyle = 'left'\n\t\t\t\tthis.ani = ['slide-left']\n\t\t\t\tthis.transClass = {\n\t\t\t\t\tposition: 'fixed',\n\t\t\t\t\tleft: 0,\n\t\t\t\t\tbottom: 0,\n\t\t\t\t\ttop: 0,\n\t\t\t\t\tbackgroundColor: this.bg,\n\t\t\t\t\tborderRadius:this.borderRadius || \"0\",\n\t\t\t\t\t/* #ifndef APP-NVUE */\n\t\t\t\t\tdisplay: 'flex',\n\t\t\t\t\tflexDirection: 'column'\n\t\t\t\t\t/* #endif */\n\t\t\t\t}\n\t\t\t\t// TODO 兼容 type 属性 ，后续会废弃\n\t\t\t\tif (type) return\n\t\t\t\tthis.showPoptrans()\n\t\t\t},\n\t\t\tright(type) {\n\t\t\t\tthis.popupstyle = 'right'\n\t\t\t\tthis.ani = ['slide-right']\n\t\t\t\tthis.transClass = {\n\t\t\t\t\tposition: 'fixed',\n\t\t\t\t\tbottom: 0,\n\t\t\t\t\tright: 0,\n\t\t\t\t\ttop: 0,\n\t\t\t\t\tbackgroundColor: this.bg,\n\t\t\t\t\tborderRadius:this.borderRadius || \"0\",\n\t\t\t\t\t/* #ifndef APP-NVUE */\n\t\t\t\t\tdisplay: 'flex',\n\t\t\t\t\tflexDirection: 'column'\n\t\t\t\t\t/* #endif */\n\t\t\t\t}\n\t\t\t\t// TODO 兼容 type 属性 ，后续会废弃\n\t\t\t\tif (type) return\n\t\t\t\tthis.showPoptrans()\n\t\t\t},\n\t\t\tshowPoptrans(){\n\t\t\t\tthis.$nextTick(()=>{\n\t\t\t\t\tthis.showPopup = true\n\t\t\t\t\tthis.showTrans = true\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n<style lang=\"scss\">\n\t.uni-popup {\n\t\tposition: fixed;\n\t\t/* #ifndef APP-NVUE */\n\t\tz-index: 99;\n\n\t\t/* #endif */\n\t\t&.top,\n\t\t&.left,\n\t\t&.right {\n\t\t\t/* #ifdef H5 */\n\t\t\ttop: var(--window-top);\n\t\t\t/* #endif */\n\t\t\t/* #ifndef H5 */\n\t\t\ttop: 0;\n\t\t\t/* #endif */\n\t\t}\n\n\t\t.uni-popup__wrapper {\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\tdisplay: block;\n\t\t\t/* #endif */\n\t\t\tposition: relative;\n\n\t\t\t/* iphonex 等安全区设置，底部安全区适配 */\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\t// padding-bottom: constant(safe-area-inset-bottom);\n\t\t\t// padding-bottom: env(safe-area-inset-bottom);\n\t\t\t/* #endif */\n\t\t\t&.left,\n\t\t\t&.right {\n\t\t\t\t/* #ifdef H5 */\n\t\t\t\tpadding-top: var(--window-top);\n\t\t\t\t/* #endif */\n\t\t\t\t/* #ifndef H5 */\n\t\t\t\tpadding-top: 0;\n\t\t\t\t/* #endif */\n\t\t\t\tflex: 1;\n\t\t\t}\n\t\t}\n\t}\n\n\t.fixforpc-z-index {\n\t\t/* #ifndef APP-NVUE */\n\t\tz-index: 999;\n\t\t/* #endif */\n\t}\n\n\t.fixforpc-top {\n\t\ttop: 0;\n\t}\n</style>\n", "import Component from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/node_modules/@dcloudio/uni-ui/lib/uni-popup/uni-popup.vue'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;AA+CC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,YAAY,CAIX;AAAA,EACD,OAAO,CAAC,UAAU,WAAW;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEN,WAAW;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA;AAAA,IAGD,MAAM;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,aAAa;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,WAAW;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,iBAAiB;AAAA,MAChB,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,qBAAqB;AAAA,MACpB,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,cAAa;AAAA,MACZ,MAAM;AAAA,IACP;AAAA,EACA;AAAA,EAED,OAAO;AAAA;AAAA;AAAA;AAAA,IAIN,MAAM;AAAA,MACL,SAAS,SAAS,MAAM;AACvB,YAAI,CAAC,KAAK,OAAO,IAAI;AAAG;AACxB,aAAK,KAAK,OAAO,IAAI,CAAC,EAAE,IAAI;AAAA,MAC5B;AAAA,MACD,WAAW;AAAA,IACX;AAAA,IACD,WAAW;AAAA,MACV,SAAS,SAAS,QAAQ;AACzB,YAAI,CAAC,KAAK,OAAO,MAAM;AAAG;AAC1B,aAAK,KAAK,OAAO,KAAK,IAAI,CAAC,EAAE,IAAI;AAAA,MACjC;AAAA,MACD,WAAW;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA,IAKD,WAAW;AAAA,MACV,SAAS,SAAS,KAAK;AACtB,aAAK,UAAU;AAAA,MACf;AAAA,MACD,WAAW;AAAA,IACX;AAAA,IACD,aAAa;AAAA,MACZ,SAAS,SAAS,KAAK;AACtB,aAAK,UAAU;AAAA,MACf;AAAA,MACD,WAAW;AAAA,IACX;AAAA;AAAA,IAED,UAAU,MAAM;AAAA,IAKhB;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,UAAU;AAAA,MACV,KAAK,CAAE;AAAA,MACP,WAAW;AAAA,MACX,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,QAAQ;AAAA,QACP,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,OAAO;AAAA,MACP;AAAA,MACD,WAAW;AAAA,QACV,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,KAAK;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,QACP,iBAAiB;AAAA,MACjB;AAAA,MACD,YAAY;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc,KAAK,gBAAgB;AAAA,QACnC,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,MACP;AAAA,MACD,UAAU;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA,IACb;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,YAAY;AACX,UAAI,MAAM,EAAE,iBAAiB,KAAK;AAClC,UAAI,KAAK,gBAAgB,KAAK;AAC7B,cAAM,OAAO,OAAO,KAAK,EAAE,cAAc,KAAK,cAAc;AAAA,MAC7D;AACA,aAAO;AAAA,IACP;AAAA,IACD,YAAY;AACX,aAAO,KAAK,cAAc,OAAO,KAAK,eAAe;AAAA,IACrD;AAAA,IACD,KAAK;AACJ,UAAI,KAAK,oBAAoB,MAAM,KAAK,oBAAoB,QAAQ;AACnE,eAAO;AAAA,MACR;AACA,aAAO,KAAK;AAAA,IACb;AAAA,EACA;AAAA,EACD,UAAU;AACT,UAAM,UAAU,MAAM;AAErB,YAAM;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD,IAAIA,cAAAA,MAAI,cAAc;AAYtB,WAAK,aAAa;AAClB,WAAK,cAAc,gBAAgB,aAAa;AAEhD,UAAI,YAAY,KAAK,UAAU;AAE9B,aAAK,iBAAiB,eAAe,SAAS;AAAA,aAKxC;AACN,aAAK,iBAAiB;AAAA,MACvB;AAAA,IACD;AACA,YAAQ;AAAA,EAOR;AAAA;AAAA,EASD,YAAY;AACX,SAAK,aAAa;AAAA,EAClB;AAAA,EAED,YAAY;AACR,SAAK,aAAa,CAAC,KAAK,SAAS;AAAA,EAClC;AAAA,EACD,cAAc;AACZ,SAAK,aAAa,IAAI;AAAA,EACvB;AAAA,EACH,UAAU;AAET,QAAI,KAAK,gBAAgB,QAAQ,KAAK,cAAc,MAAM;AACzD,WAAK,UAAU;AAAA,WACT;AACN,WAAK,UAAU,KAAK,gBAAgB,OAAO,KAAK,cAAc,KAAK;AAAA,IACpE;AACA,QAAI,KAAK,WAAW;AACnB,WAAK,WAAW;AAAA,WACV;AACN,WAAK,WAAW;AAAA,IACjB;AAEA,SAAK,eAAe;AAEpB,SAAK,mBAAmB;AACxB,SAAK,UAAU,kBAAkB,KAAK;AAAA,EACtC;AAAA,EACD,SAAS;AAAA,IACR,aAAa,UAAU,MAAM;AAAA,IAK5B;AAAA;AAAA;AAAA;AAAA,IAID,YAAY;AACX,WAAK,WAAW;AAAA,IAChB;AAAA;AAAA;AAAA;AAAA,IAID,cAAc;AACb,WAAK,UAAU;AAAA,IACf;AAAA;AAAA,IAED,MAAM,GAAG;AAER,QAAE,gBAAgB;AAElB,WAAK,mBAAmB;AAAA,IACxB;AAAA,IAED,KAAK,WAAW;AAEf,UAAI,KAAK,WAAW;AACnB;AAAA,MACD;AACA,UAAI,YAAY,CAAC,OAAO,UAAU,UAAU,QAAQ,SAAS,WAAW,UAAU,OAAO;AACzF,UAAI,EAAE,aAAa,UAAU,QAAQ,SAAS,MAAM,KAAK;AACxD,oBAAY,KAAK;AAAA,MAClB;AACA,UAAI,CAAC,KAAK,OAAO,SAAS,GAAG;AAC5BA,sBAAAA,yFAAc,SAAS,SAAS;AAChC;AAAA,MACD;AACA,WAAK,KAAK,OAAO,SAAS,CAAC,EAAE;AAC7B,WAAK,MAAM,UAAU;AAAA,QACpB,MAAM;AAAA,QACN,MAAM;AAAA,OACN;AAAA,IACD;AAAA,IACD,MAAM,MAAM;AACX,WAAK,YAAY;AACjB,WAAK,MAAM,UAAU;AAAA,QACpB,MAAM;AAAA,QACN,MAAM,KAAK;AAAA,OACX;AACD,mBAAa,KAAK,KAAK;AAGvB,WAAK,QAAQ,WAAW,MAAM;AAC7B,aAAK,YAAY;AAAA,MACjB,GAAE,GAAG;AAAA,IACN;AAAA;AAAA,IAED,aAAa;AACZ,WAAK,mBAAmB;AAAA,IACxB;AAAA,IAED,QAAQ;AACP,UAAI,KAAK,kBAAkB;AAE1B,aAAK,mBAAmB;AACxB;AAAA,MACD;AACA,WAAK,MAAM,WAAW;AACtB,UAAI,CAAC,KAAK;AAAS;AACnB,WAAK,MAAM;AAAA,IACX;AAAA;AAAA;AAAA;AAAA,IAID,IAAI,MAAM;AACT,WAAK,aAAa,KAAK,YAAY,iBAAiB;AACpD,WAAK,MAAM,CAAC,WAAW;AACvB,WAAK,aAAa;AAAA,QACjB,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,QACP,iBAAiB,KAAK;AAAA,QACtB,cAAa,KAAK,gBAAgB;AAAA,MACnC;AAEA,UAAI;AAAM;AACV,WAAK,YAAY;AACjB,WAAK,YAAY;AACjB,WAAK,UAAU,MAAM;AACpB,aAAK,aAAa;AAClB,YAAI,KAAK,gBAAgB,KAAK,SAAS,WAAW;AACjD,eAAK,aAAa,WAAW;AAAA,QAC9B;AAAA,OACA;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAID,OAAO,MAAM;AACZ,WAAK,aAAa;AAClB,WAAK,MAAM,CAAC,cAAc;AAC1B,WAAK,aAAa;AAAA,QACjB,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,eAAe,KAAK,iBAAiB;AAAA,QACrC,iBAAiB,KAAK;AAAA,QACtB,cAAa,KAAK,gBAAgB;AAAA,MACnC;AAEA,UAAI;AAAM;AACV,WAAK,aAAa;AAAA,IAClB;AAAA;AAAA;AAAA;AAAA,IAID,OAAO,MAAM;AACZ,WAAK,aAAa;AAGjB,WAAK,MAAM,CAAC,MAAM;AAKnB,WAAK,aAAa;AAAA,QACjB,UAAU;AAAA,QAEV,SAAS;AAAA,QACT,eAAe;AAAA,QAEf,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,OAAO;AAAA,QACP,KAAK;AAAA,QACL,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,cAAa,KAAK,gBAAgB;AAAA,MACnC;AAEA,UAAI;AAAM;AACV,WAAK,aAAa;AAAA,IAClB;AAAA,IACD,KAAK,MAAM;AACV,WAAK,aAAa;AAClB,WAAK,MAAM,CAAC,YAAY;AACxB,WAAK,aAAa;AAAA,QACjB,UAAU;AAAA,QACV,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,KAAK;AAAA,QACL,iBAAiB,KAAK;AAAA,QACtB,cAAa,KAAK,gBAAgB;AAAA,QAElC,SAAS;AAAA,QACT,eAAe;AAAA,MAEhB;AAEA,UAAI;AAAM;AACV,WAAK,aAAa;AAAA,IAClB;AAAA,IACD,MAAM,MAAM;AACX,WAAK,aAAa;AAClB,WAAK,MAAM,CAAC,aAAa;AACzB,WAAK,aAAa;AAAA,QACjB,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,KAAK;AAAA,QACL,iBAAiB,KAAK;AAAA,QACtB,cAAa,KAAK,gBAAgB;AAAA,QAElC,SAAS;AAAA,QACT,eAAe;AAAA,MAEhB;AAEA,UAAI;AAAM;AACV,WAAK,aAAa;AAAA,IAClB;AAAA,IACD,eAAc;AACb,WAAK,UAAU,MAAI;AAClB,aAAK,YAAY;AACjB,aAAK,YAAY;AAAA,OACjB;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/cD,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}