/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.account-binding-container {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding-bottom: 40rpx;
}
.page-header {
  background-color: #fff;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}
.page-header .page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.binding-card {
  margin: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 10rpx 0;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.05);
}
.binding-card .binding-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}
.binding-card .binding-item:last-child {
  border-bottom: none;
}
.binding-card .binding-item .binding-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
}
.binding-card .binding-item .binding-icon .iconfont {
  font-size: 40rpx;
  color: #fff;
}
.binding-card .binding-item .binding-icon.phone-icon {
  background: linear-gradient(135deg, #1890ff, #36b3ff);
}
.binding-card .binding-item .binding-icon.email-icon {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}
.binding-card .binding-item .binding-icon.wechat-icon {
  background: linear-gradient(135deg, #07c160, #10d878);
}
.binding-card .binding-item .binding-icon.dingtalk-icon {
  background: linear-gradient(135deg, #1677ff, #4096ff);
}
.binding-card .binding-item .binding-info {
  flex: 1;
}
.binding-card .binding-item .binding-info .binding-name {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
  display: block;
}
.binding-card .binding-item .binding-info .binding-status {
  font-size: 26rpx;
  color: #52c41a;
}
.binding-card .binding-item .binding-info .binding-status.not-bound {
  color: #999;
}
.binding-card .binding-item .binding-action .action-btn {
  display: inline-block;
  padding: 12rpx 30rpx;
  background-color: #f5f7fa;
  color: #1890ff;
  font-size: 26rpx;
  border-radius: 30rpx;
}
.binding-card .binding-item .binding-action .action-btn:active {
  opacity: 0.8;
}
.security-section {
  margin: 30rpx;
}
.security-section .security-title {
  margin-bottom: 20rpx;
}
.security-section .security-title .title-text {
  font-size: 30rpx;
  color: #666;
  font-weight: bold;
}
.security-section .security-tips .tip-item {
  display: flex;
  margin-bottom: 15rpx;
}
.security-section .security-tips .tip-item .tip-marker {
  color: #999;
  margin-right: 10rpx;
}
.security-section .security-tips .tip-item .tip-text {
  font-size: 26rpx;
  color: #999;
  line-height: 1.6;
}
.popup-content {
  width: 600rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}
.popup-content .popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eee;
}
.popup-content .popup-form {
  padding: 30rpx;
}
.popup-content .popup-form .form-item {
  margin-bottom: 30rpx;
}
.popup-content .popup-form .form-item input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}
.popup-content .popup-form .form-item.code-item {
  display: flex;
  align-items: center;
}
.popup-content .popup-form .form-item.code-item input {
  flex: 1;
}
.popup-content .popup-form .form-item.code-item .send-code-btn {
  margin-left: 20rpx;
  white-space: nowrap;
  padding: 15rpx 20rpx;
  background-color: #1890ff;
  color: #fff;
  font-size: 26rpx;
  border-radius: 8rpx;
}
.popup-content .popup-form .form-item.code-item .send-code-btn:active {
  opacity: 0.8;
}
.popup-content .popup-actions {
  display: flex;
  border-top: 1rpx solid #eee;
}
.popup-content .popup-actions .cancel-btn, .popup-content .popup-actions .confirm-btn {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 30rpx;
}
.popup-content .popup-actions .cancel-btn {
  color: #999;
  border-right: 1rpx solid #eee;
}
.popup-content .popup-actions .confirm-btn {
  color: #1890ff;
  font-weight: bold;
}