<view class="patrol-plans-container"><view class="search-filter-area"><view class="search-bar"><view class="search-input-wrapper"><input type="text" placeholder="搜索巡检计划" confirm-type="search" bindconfirm="{{a}}" value="{{b}}" bindinput="{{c}}"/><text wx:if="{{d}}" class="iconfont icon-clear" bindtap="{{e}}"></text></view></view><view class="filter-bar"><view class="filter-item" bindtap="{{g}}"><text>状态: {{f}}</text><text>▼</text></view><view class="filter-item" bindtap="{{i}}"><text>时间: {{h}}</text><text>▼</text></view><view class="filter-item" bindtap="{{k}}"><text>类型: {{j}}</text><text>▼</text></view></view></view><view wx:if="{{l}}" class="plan-list"><view wx:for="{{m}}" wx:for-item="plan" wx:key="j" class="plan-item" bindtap="{{plan.k}}"><view class="plan-header"><text class="plan-title">{{plan.a}}</text><view class="{{['plan-status', plan.c]}}">{{plan.b}}</view></view><view class="plan-info"><view class="info-row"><view class="info-item full-width"><text class="item-label">计划编号:</text><text class="item-value">{{plan.d}}</text></view></view><view class="info-row"><view class="info-item"><text class="item-label">开始时间:</text><text class="item-value">{{plan.e}}</text></view><view class="info-item"><text class="item-label">结束时间:</text><text class="item-value">{{plan.f}}</text></view></view><view class="info-row"><view class="info-item"><text class="item-label">巡检类型:</text><text class="item-value">{{plan.g}}</text></view><view class="info-item"><text class="item-label">巡检地点:</text><text class="item-value">{{plan.h}}</text></view></view></view><view class="plan-footer"><view class="plan-actions"><view class="action-button view" catchtap="{{plan.i}}"><text>查看详情</text></view></view></view></view></view><view wx:if="{{n}}" class="loading-container"><text class="loading-text">加载中...</text></view><view wx:if="{{o}}" class="error-container"><text class="error-text">加载失败，请重试</text><button class="retry-btn" bindtap="{{p}}">重新加载</button></view><view wx:if="{{q}}" class="empty-state"><image class="empty-image" src="{{r}}" mode="aspectFit"></image><text class="empty-text">暂无巡检计划</text></view><uni-popup wx:if="{{w}}" class="r" u-s="{{['d']}}" u-r="statusFilterPopup" u-i="597a6f7d-0" bind:__l="__l" u-p="{{w}}"><view class="filter-popup"><view class="popup-header"><text class="popup-title">状态筛选</text><text class="popup-close" bindtap="{{s}}">关闭</text></view><view class="popup-content"><view wx:for="{{t}}" wx:for-item="option" wx:key="b" class="{{['filter-option', option.c && 'active']}}" bindtap="{{option.d}}">{{option.a}}</view></view></view></uni-popup><uni-popup wx:if="{{A}}" class="r" u-s="{{['d']}}" u-r="timeFilterPopup" u-i="597a6f7d-1" bind:__l="__l" u-p="{{A}}"><view class="filter-popup"><view class="popup-header"><text class="popup-title">时间筛选</text><text class="popup-close" bindtap="{{x}}">关闭</text></view><view class="popup-content"><view wx:for="{{y}}" wx:for-item="option" wx:key="b" class="{{['filter-option', option.c && 'active']}}" bindtap="{{option.d}}">{{option.a}}</view></view></view></uni-popup><uni-popup wx:if="{{E}}" class="r" u-s="{{['d']}}" u-r="typeFilterPopup" u-i="597a6f7d-2" bind:__l="__l" u-p="{{E}}"><view class="filter-popup"><view class="popup-header"><text class="popup-title">类型筛选</text><text class="popup-close" bindtap="{{B}}">关闭</text></view><view class="popup-content"><view wx:for="{{C}}" wx:for-item="option" wx:key="b" class="{{['filter-option', option.c && 'active']}}" bindtap="{{option.d}}">{{option.a}}</view></view></view></uni-popup><permission-check wx:if="{{G}}" u-s="{{['d']}}" u-i="597a6f7d-3" bind:__l="__l" u-p="{{G}}"><view class="floating-button" bindtap="{{F}}"><text class="plus-icon">+</text></view></permission-check></view>