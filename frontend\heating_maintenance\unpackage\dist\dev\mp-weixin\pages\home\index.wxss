/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.home-container {
  padding: 0 30rpx;
  box-sizing: border-box;
  padding-bottom: 120rpx;
  /* 增加底部padding，避免内容被底部导航栏遮挡 */
}

/* 顶部样式增强 */
.top-style {
  height: 80rpx;
  width: 100%;
  padding: 0;
  background: linear-gradient(135deg, #35a6c8, #1e88e5);
  position: relative;
  overflow: hidden;
  z-index: 10;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 999;
}
.top-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #35a6c8, #1e88e5);
  opacity: 0.9;
  z-index: 1;
}
.top-circles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
}
.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}
.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: -80rpx;
  right: 60rpx;
}
.circle-2 {
  width: 300rpx;
  height: 300rpx;
  top: 20rpx;
  left: -120rpx;
}
.top-wave {
  position: absolute;
  bottom: -2rpx;
  left: 0;
  width: 100%;
  height: 40rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23ffffff' fill-opacity='1' d='M0,192L48,176C96,160,192,128,288,138.7C384,149,480,203,576,208C672,213,768,171,864,154.7C960,139,1056,149,1152,154.7C1248,160,1344,160,1392,160L1440,160L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
  background-size: 100% 100%;
  z-index: 3;
}

/* 调试区域样式 */
.debug-section {
  background-color: #f8f9fa;
  border: 2rpx solid #dee2e6;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}
.debug-section .debug-grid {
  margin: 20rpx 0;
}
.debug-section .debug-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #eee;
}
.debug-section .debug-label {
  width: 200rpx;
  font-size: 26rpx;
  color: #666;
  font-weight: bold;
}
.debug-section .debug-success {
  color: #52c41a;
  font-weight: bold;
}
.debug-section .debug-toggle {
  font-size: 24rpx;
  padding: 10rpx 20rpx;
  background: #eee;
  border: none;
  border-radius: 8rpx;
}
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  margin-top: 10rpx;
}
.status-bar .weather-info {
  display: flex;
  flex-direction: column;
}
.status-bar .weather-info .temp {
  font-size: 36rpx;
  font-weight: bold;
}
.status-bar .weather-info .city {
  font-size: 24rpx;
  color: #666;
}
.status-bar .date-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.status-bar .date-info text {
  font-size: 24rpx;
  color: #666;
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}
.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 8rpx;
  width: 8rpx;
  height: 32rpx;
  background-color: #1890ff;
  border-radius: 4rpx;
}
.stat-cards {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}
.stat-cards .stat-card {
  width: 30%;
  height: 160rpx;
  background-color: #fff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.stat-cards .stat-card .stat-value {
  font-size: 40rpx;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 10rpx;
}
.stat-cards .stat-card .stat-label {
  font-size: 24rpx;
  color: #666;
}
.quick-access {
  margin-bottom: 40rpx;
}
.quick-access .section-title {
  margin-bottom: 30rpx;
}
.quick-grid {
  display: flex !important;
  flex-wrap: wrap !important;
  justify-content: flex-start !important;
  align-items: flex-start !important;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);
  padding: 30rpx 20rpx 10rpx;
  position: relative;
}
.quick-grid::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  background: linear-gradient(180deg, rgba(245, 247, 250, 0.8) 0%, rgba(255, 255, 255, 0) 100%);
  border-radius: 20rpx 20rpx 0 0;
  z-index: 0;
}
.quick-item {
  width: 25% !important;
  box-sizing: border-box !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: flex-start !important;
  margin-bottom: 30rpx !important;
  position: relative !important;
  z-index: 1 !important;
  padding: 10rpx !important;
  transition: all 0.3s ease;
}
.quick-item:active {
  transform: translateY(6rpx) scale(0.95);
}
.quick-item .quick-icon-wrapper {
  width: 110rpx !important;
  height: 110rpx !important;
  margin-bottom: 16rpx !important;
  border-radius: 28rpx !important;
  overflow: hidden !important;
  position: relative !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.15) !important;
}
.quick-item .quick-icon {
  width: 60% !important;
  height: 60% !important;
  object-fit: contain !important;
  filter: brightness(0) invert(1) !important;
}
.quick-item .quick-icon-font {
  font-size: 55rpx !important;
  color: #ffffff !important;
  text-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2) !important;
}
.quick-item .quick-text {
  font-size: 26rpx !important;
  color: #333 !important;
  font-weight: 500 !important;
  line-height: 1.4 !important;
  text-align: center !important;
  letter-spacing: 1rpx !important;
  margin-top: 6rpx !important;
}

/* 快捷图标样式 - 确保每个图标有独特颜色 */
.quick-item:nth-of-type(1) .quick-icon-wrapper {
  background: linear-gradient(135deg, #6a9eef, #4483e5) !important;
}
.quick-item:nth-of-type(2) .quick-icon-wrapper {
  background: linear-gradient(135deg, #60c6a8, #3aaf8f) !important;
}
.quick-item:nth-of-type(3) .quick-icon-wrapper {
  background: linear-gradient(135deg, #f3a768, #ee8c3c) !important;
}
.quick-item:nth-of-type(4) .quick-icon-wrapper {
  background: linear-gradient(135deg, #e47474, #e05555) !important;
}
.quick-item:nth-of-type(5) .quick-icon-wrapper {
  background: linear-gradient(135deg, #8387ea, #5a5fd3) !important;
}
.quick-item:nth-of-type(6) .quick-icon-wrapper {
  background: linear-gradient(135deg, #55c1e3, #35a6c8) !important;
}
.quick-item:nth-of-type(7) .quick-icon-wrapper {
  background: linear-gradient(135deg, #e680b3, #d65698) !important;
}
.quick-item:nth-of-type(8) .quick-icon-wrapper {
  background: linear-gradient(135deg, #9dc75b, #7bae35) !important;
}
.quick-item:nth-of-type(9) .quick-icon-wrapper {
  background: linear-gradient(135deg, #6a9eef, #4483e5) !important;
}
.quick-item:nth-of-type(10) .quick-icon-wrapper {
  background: linear-gradient(135deg, #60c6a8, #3aaf8f) !important;
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.section-header .view-more {
  font-size: 24rpx;
  color: #1890ff;
}
.task-list .task-item {
  background-color: #fff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  padding: 24rpx;
  margin-bottom: 20rpx;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
}
.task-list .task-item:active {
  background-color: #f9f9f9;
}
.task-list .task-item:last-child {
  margin-bottom: 30rpx;
  /* 确保最后一个项目底部有足够的间距 */
}
.task-list .task-item .task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  width: 100%;
}
.task-list .task-item .task-header .task-code {
  font-size: 28rpx;
  color: #666;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.task-list .task-item .task-header .status-tag {
  flex-shrink: 0;
  min-width: 80rpx;
  text-align: center;
}
.task-list .task-item .task-info {
  margin-bottom: 16rpx;
  width: 100%;
}
.task-list .task-item .task-info .task-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.task-list .task-item .task-info .task-desc {
  font-size: 28rpx;
  color: #666;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.task-list .task-item .task-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.task-list .task-item .task-footer .task-time {
  font-size: 24rpx;
  color: #666;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.task-list .task-item .task-footer .task-action {
  font-size: 24rpx;
  color: #1890ff;
  flex-shrink: 0;
  margin-left: 10rpx;
}
.status-tag {
  display: inline-block;
  padding: 6rpx 16rpx;
  font-size: 24rpx;
  border-radius: 30rpx;
  min-width: 80rpx;
  text-align: center;
  font-weight: 600;
}
.status-tag.primary {
  background-color: rgba(24, 144, 255, 0.15);
  color: #1890ff;
}
.status-tag.success {
  background-color: rgba(82, 196, 26, 0.15);
  color: #52c41a;
}
.status-tag.warning {
  background-color: rgba(250, 173, 20, 0.15);
  color: #faad14;
}
.status-tag.error {
  background-color: rgba(255, 0, 0, 0.1);
  color: #FF0000;
}
.status-tag.info {
  background-color: rgba(144, 147, 153, 0.1);
  color: #909399;
  /* 使用灰色调表示已转派状态 */
}
.temp-report-modal {
  background-color: #fff;
  width: 650rpx;
  border-radius: 16rpx;
  overflow: hidden;
}
.temp-report-modal .modal-title {
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  padding: 30rpx 0;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}
.temp-report-modal .modal-content {
  padding: 30rpx;
  max-height: 800rpx;
  overflow-y: auto;
}
.temp-report-modal .modal-content .form-item {
  margin-bottom: 30rpx;
}
.temp-report-modal .modal-content .form-item .form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}
.temp-report-modal .modal-content .form-item .form-input {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  padding: 0 20rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  box-sizing: border-box;
}
.temp-report-modal .modal-content .form-item .form-input .picker-text {
  font-size: 28rpx;
}
.temp-report-modal .modal-content .form-item .temp-slider-container {
  margin-top: 20rpx;
  position: relative;
}
.temp-report-modal .modal-content .form-item .temp-slider-container .temp-value {
  position: absolute;
  right: 0;
  top: -50rpx;
  font-size: 32rpx;
  color: #1890ff;
  font-weight: bold;
}
.temp-report-modal .modal-content .form-item .outdoor-temp {
  font-size: 32rpx;
  color: #1890ff;
  font-weight: bold;
  padding: 10rpx 0;
}
.temp-report-modal .modal-content .form-item .upload-container .upload-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}
.temp-report-modal .modal-content .form-item .upload-container .upload-list .upload-item,
.temp-report-modal .modal-content .form-item .upload-container .upload-list .upload-button {
  width: 180rpx;
  height: 180rpx;
  margin: 10rpx;
  border-radius: 8rpx;
  overflow: hidden;
  position: relative;
}
.temp-report-modal .modal-content .form-item .upload-container .upload-list .upload-item .upload-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.temp-report-modal .modal-content .form-item .upload-container .upload-list .upload-item .upload-delete {
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  text-align: center;
  line-height: 40rpx;
  font-size: 24rpx;
  z-index: 1;
}
.temp-report-modal .modal-content .form-item .upload-container .upload-list .upload-button {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  border: 1rpx dashed #ddd;
}
.temp-report-modal .modal-content .form-item .upload-container .upload-list .upload-button .upload-icon {
  font-size: 60rpx;
  color: #999;
  line-height: 1;
  margin-bottom: 10rpx;
}
.temp-report-modal .modal-content .form-item .upload-container .upload-list .upload-button .upload-text {
  font-size: 24rpx;
  color: #999;
}
.temp-report-modal .modal-content .form-item .form-textarea {
  width: 100%;
  height: 160rpx;
  padding: 20rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  box-sizing: border-box;
  font-size: 28rpx;
}
.temp-report-modal .modal-footer {
  display: flex;
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
}
.temp-report-modal .modal-footer button {
  flex: 1;
  border: none;
  height: 100rpx;
  line-height: 100rpx;
  font-size: 32rpx;
  border-radius: 0;
}
.temp-report-modal .modal-footer button::after {
  border: none;
}
.temp-report-modal .modal-footer .btn-cancel {
  background-color: #f5f5f5;
  color: #333;
}
.temp-report-modal .modal-footer .btn-submit {
  background-color: #1890ff;
  color: #fff;
}

/* 无权限提示样式 */
.no-permission {
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  padding: 0 40rpx;
}
.no-permission .no-permission-text {
  font-size: 32rpx;
  color: #999;
  text-align: center;
  margin-bottom: 30rpx;
}

/* 确保组件不生成额外DOM */
 PermissionCheck {
  display: contents !important;
}
.recent-tasks {
  position: relative;
  /* 确保定位正确 */
  z-index: 1;
  /* 确保层级正确 */
}
.bottom-space {
  height: 20rpx;
  /* 底部空白区域高度 */
  width: 100%;
}
.safe-area-bottom {
  height: env(safe-area-inset-bottom, 0);
  width: 100%;
}

/* 巡检工单特殊样式 */
.patrol-tasks {
  margin-top: 30rpx;
  margin-bottom: 40rpx;
}
.patrol-more {
  color: #3aaf8f;
}
.patrol-list {
  background: linear-gradient(180deg, rgba(96, 198, 168, 0.05));
  padding: 10rpx;
  border-radius: 0 0 12rpx 12rpx;
}
.patrol-item {
  background: #fff;
  border-left: 8rpx solid #3aaf8f;
  border-radius: 4rpx 8rpx 8rpx 4rpx;
  box-shadow: 0 4rpx 16rpx rgba(58, 175, 143, 0.1);
  transition: all 0.3s ease;
}
.patrol-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(58, 175, 143, 0.1);
}
.patrol-item-header {
  padding-bottom: 16rpx;
  border-bottom: 1rpx dashed rgba(58, 175, 143, 0.2);
}
.patrol-item-title {
  color: #3aaf8f;
  font-weight: 600;
}
.patrol-status.primary {
  background-color: rgba(58, 175, 143, 0.15);
  color: #3aaf8f;
}
.patrol-action {
  color: #3aaf8f;
  font-weight: 600;
  background-color: rgba(58, 175, 143, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

/* 维修工单特殊样式 */
.repair-tasks {
  margin-top: 30rpx;
  margin-bottom: 40rpx;
}
.repair-more {
  color: #e05555;
}
.repair-list {
  background: linear-gradient(180deg, rgba(228, 116, 116, 0.05));
  padding: 10rpx;
  border-radius: 0 0 12rpx 12rpx;
}
.repair-item {
  background: #fff;
  border-left: 8rpx solid #e05555;
  border-radius: 4rpx 8rpx 8rpx 4rpx;
  box-shadow: 0 4rpx 16rpx rgba(224, 85, 85, 0.1);
  transition: all 0.3s ease;
}
.repair-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(224, 85, 85, 0.1);
}
.repair-item-header {
  padding-bottom: 16rpx;
  border-bottom: 1rpx dashed rgba(224, 85, 85, 0.2);
}
.repair-item-title {
  color: #e05555;
  font-weight: 600;
}
.repair-status.primary {
  background-color: rgba(224, 85, 85, 0.15);
  color: #e05555;
}
.repair-status.warning {
  background-color: rgba(250, 173, 20, 0.15);
  color: #faad14;
}
.repair-action {
  color: #e05555;
  font-weight: 600;
  background-color: rgba(58, 175, 143, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}