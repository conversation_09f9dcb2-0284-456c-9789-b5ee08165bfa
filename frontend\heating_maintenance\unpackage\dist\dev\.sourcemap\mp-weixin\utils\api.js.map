{"version": 3, "file": "api.js", "sources": ["utils/api.js"], "sourcesContent": ["// API配置文件\nconst BASE_URL = 'http://43.139.65.175:8889';\n//const BASE_URL = 'http://192.168.0.155:8889'; // 修改为公网地址\nconst TIMEOUT = 10000; // 10秒超时\nconst RETRY_TIMES = 2; // 重试次数\n\n// 测试API连接\nfunction testApiConnection() {\n  return new Promise((resolve, reject) => {\n    console.log('正在测试API连接:', BASE_URL);\n    \n    // 改为测试登录接口，而不是根路径\n    uni.request({\n      url: `${BASE_URL}/api/auth/login`,\n      method: 'OPTIONS', // 使用OPTIONS请求检查CORS预检请求\n      timeout: 5000,\n      // 禁用withCredentials，解决CORS问题\n      withCredentials: false,\n      header: {\n        'Content-Type': 'application/json',\n        'Access-Control-Request-Method': 'POST',\n        'Access-Control-Request-Headers': 'content-type,authorization'\n      },\n      success: (res) => {\n        console.log('API连接测试成功:', res);\n        // 只要能收到响应，不管状态码是什么，都认为API可连接\n        resolve(true);\n      },\n      fail: (err) => {\n        console.error('API连接测试失败:', err);\n        resolve(false);\n      }\n    });\n  });\n}\n\n// 封装请求函数\nfunction request(options) {\n  let retryCount = 0;\n  \n  const executeRequest = () => {\n    return new Promise((resolve, reject) => {\n      // 完整的URL处理，确保URL正确拼接\n      const url = options.url.startsWith('http') ? options.url : BASE_URL + options.url;\n      \n      // 输出请求日志\n      //console.log(`Request URL (attempt ${retryCount + 1}):`, url);\n      //console.log('Request Method:', options.method || 'GET');\n      //console.log('Request Data:', options.data);\n      \n      // 准备请求头\n      const header = {\n        'Content-Type': 'application/json',\n        ...(options.header || {})\n      };\n      \n      // 添加token (如果有)\n      const token = uni.getStorageSync('token');\n      if (token) {\n        header.Authorization = `Bearer ${token}`;\n        //console.log('Using token:', token);\n      } else {\n        console.warn('No token found in storage');\n      }\n      \n      // 发起请求\n      uni.request({\n        url: url,\n        method: options.method || 'GET',\n        data: options.data,\n        header: header,\n        // 超时时间设置\n        timeout: options.timeout || TIMEOUT,\n        success: (res) => {\n          console.log('Response:', res);\n          \n          // 检查认证相关错误\n          if (res.statusCode === 401) {\n            console.error('认证失败，需要重新登录');\n            // 清除本地token\n            uni.removeStorageSync('token');\n            // 跳转到登录页\n            uni.redirectTo({\n              url: '/pages/user/login'\n            });\n            reject({\n              errMsg: '认证失败，请重新登录',\n              statusCode: 401\n            });\n            return;\n          }\n          \n          // 检查其他HTTP状态码 - 将2xx状态码都视为成功\n          if (res.statusCode >= 200 && res.statusCode < 300) {\n            // 2xx 状态码，表示请求成功\n            resolve(res.data);\n          } else {\n            // 处理非2xx状态码\n            console.error('API错误:', res.statusCode, res.data);\n            reject({\n              errMsg: `请求失败: ${res.statusCode}`,\n              statusCode: res.statusCode,\n              data: res.data\n            });\n          }\n        },\n        fail: async (err) => {\n          console.error(`请求失败 (attempt ${retryCount + 1}):`, err);\n          \n          // 如果是网络错误且未超过重试次数，则重试\n          if (retryCount < RETRY_TIMES && \n              (err.errMsg.includes('timeout') || err.errMsg.includes('network'))) {\n            retryCount++;\n            console.log(`正在重试 (${retryCount}/${RETRY_TIMES})...`);\n            try {\n              const result = await executeRequest();\n              resolve(result);\n            } catch (retryError) {\n              reject(retryError);\n            }\n          } else {\n            reject({\n              errMsg: err.errMsg || '网络请求失败',\n              url: url,\n              method: options.method || 'GET',\n              data: options.data\n            });\n          }\n        }\n      });\n    });\n  };\n  \n  return executeRequest();\n}\n\n// 材料管理API\nconst materialsApi = {\n  /**\n   * 获取材料列表\n   * @returns {Promise} 包含材料列表数据的Promise对象\n   */\n  getMaterialsList() {\n    return new Promise((resolve, reject) => {\n      request({\n        url: '/api/materials/list',\n        method: 'GET'\n      })\n      .then(res => {\n        console.log('获取材料列表成功:', res);\n        resolve(res);\n      })\n      .catch(err => {\n        console.warn('获取材料列表失败，使用默认数据', err);\n        // 提供默认数据，避免页面错误\n        resolve({\n          code: 200,\n          message: \"材料列表查询成功(本地数据)\",\n          data: [\n            { id: 1, name: '铜管', material_type: '管材' },\n            { id: 2, name: '不锈钢管', material_type: '管材' },\n            { id: 3, name: '橡胶密封圈', material_type: '密封件' },\n            { id: 4, name: '球阀', material_type: '阀门' },\n            { id: 5, name: '闸阀', material_type: '阀门' },\n            { id: 6, name: '法兰', material_type: '连接件' },\n            { id: 7, name: '螺栓', material_type: '连接件' },\n            { id: 8, name: '防水胶带', material_type: '辅材' },\n            { id: 9, name: '保温材料', material_type: '辅材' },\n            { id: 10, name: '电线', material_type: '电气' }\n          ]\n        });\n      });\n    });\n  }\n};\n\n// 用户模块API\nconst userApi = {\n  login(data) {\n    console.log('尝试登录，数据:', data);\n    \n    // 确保数据格式正确\n    const loginData = {\n      username: data.username,\n      password: data.password\n    };\n    \n    return new Promise((resolve, reject) => {\n      uni.request({\n        url: `${BASE_URL}/api/auth/login`,\n        method: 'POST',\n        data: loginData,\n        header: {\n          'Content-Type': 'application/json'\n        },\n        withCredentials: true,\n        success: (res) => {\n          console.log('登录响应:', res);\n          if (res.statusCode === 200) {\n            resolve(res.data);\n          } else {\n            // 处理非200状态码\n            console.error('登录错误:', res.statusCode, res.data);\n            reject({\n              errMsg: `登录失败: ${res.statusCode}`,\n              statusCode: res.statusCode,\n              data: res.data\n            });\n          }\n        },\n        fail: (err) => {\n          console.error('登录请求异常:', err);\n          reject({\n            errMsg: err.errMsg || '网络请求失败',\n            url: `${BASE_URL}/api/auth/login`,\n            method: 'POST',\n            data: loginData\n          });\n        }\n      });\n    });\n  },\n  getUserInfo() {\n    return request({\n      url: '/api/auth/user-info',\n      method: 'GET'\n    });\n  },\n  getInspectorList(role) {\n    return new Promise((resolve, reject) => {\n      request({\n        url: '/api/auth/user/list',\n        method: 'POST',\n\t\tdata:role\n      })\n      .then(res => {\n        resolve(res);\n      })\n      .catch(err => {\n        console.warn('获取巡检人员列表失败，使用默认数据', err);\n        // 提供默认数据，避免页面错误\n        resolve({\n          code: 200,\n          message: \"获取人员列表成功(本地数据)\",\n          data: [\n          ]\n        });\n      });\n    });\n  },\n  updateUserInfo(data) {\n    return request({\n      url: '/api/auth/user-modify',\n      method: 'POST',\n      data\n    });\n  },\n  /**\n   * 修改用户密码\n   * @param {Object} data - 包含当前密码和新密码的对象\n   * @returns {Promise} 包含修改结果的Promise对象\n   */\n  changePassword(data) {\n    return request({\n      url: '/api/auth/change-password',\n      method: 'POST',\n      data\n    });\n  },\n  /**\n   * 获取系统权限列表\n   * 根据后端接口文档 1.6 获取系统权限列表\n   * @returns {Promise} 包含权限列表数据的Promise对象\n   */\n  getPermissionList() {\n    return new Promise((resolve, reject) => {\n      request({\n        url: '/api/system/permission',\n        method: 'GET'\n      })\n      .then(res => {\n        console.log('获取权限列表成功:', res);\n        resolve(res);\n      })\n      .catch(err => {\n        console.warn('获取权限列表失败:', err); \n      });\n    });\n  }\n};\n\n// 工单模块API\nconst workOrderApi = {\n  getList(params) {\n    // 将前端参数转换为后端API所需格式\n    const apiParams = {\n      page: params?.page || 1,\n      pageSize: params?.pageSize || 10\n    };\n    \n    // 处理日期参数\n    if (params && params.date) {\n      apiParams.date = params.date;\n    }\n    \n    // 处理状态参数\n    if (params && params.status) {\n      apiParams.status = params.status;\n    }\n    \n    // 处理用户ID参数\n    apiParams.uid = params?.uid || uni.getStorageSync(\"userId\");\n    \n    // 处理工单类型参数\n    if (params && params.type) {\n      apiParams.type = params.type;\n    }\n    \n    // 处理用户项目权限参数\n    apiParams.heatUnitId = params?.heatUnitId || uni.getStorageSync(\"heatUnitId\");\n    \n    console.log('工单列表请求参数:', apiParams);\n    return request({\n      url: '/api/WorkOrders/list',\n      method: 'GET',\n      data: apiParams\n    });\n  },\n  getDetail(id) {\n    return request({\n      url: `/api/WorkOrders/detail/${id}`,\n      method: 'GET'\n    });\n  },\n  updateStatus(data) {\n    return request({\n      url: '/api/WorkOrders/status',\n      method: 'POST',\n      data\n    });\n  },\n  completeOrder(data) {\n    return request({\n      url: '/api/WorkOrders/complete',\n      method: 'POST',\n      data\n    });\n  },\n  transferOrder(data) {\n    return request({\n      url: '/api/WorkOrders/transfer',\n      method: 'POST',\n      data\n    });\n  },\n  // 工单消息 /api/WorkOrders/messages\n  getWorkOrderMessages(userId, userRole) {\n    return request({\n      url: '/api/WorkOrders/messages',\n      method: 'GET',\n      data: {\n        uid: userId,\n        role: userRole,\n        heatUnitId: uni.getStorageSync(\"heatUnitId\")\n      }\n    });\n  },\n  // 获取工单统计数据\n  getWorkOrderStats() {\n    const apiParams = {};\n    apiParams.uid = uni.getStorageSync(\"userId\");\n    apiParams.heatUnitId = uni.getStorageSync(\"heatUnitId\");\n    \n    return request({\n      url: '/api/WorkOrders/stats',\n      method: 'GET',\n      data: apiParams\n    });\n  }\n};\n\n// 巡检模块API\nconst patrolApi = {\n  // 创建巡检计划\n  createPlan(data) {\n    return request({\n      url: '/api/patrols/plans',\n      method: 'POST',\n      data\n    });\n  },\n  \n  // 获取巡检计划列表\n  getPlanList(params) {\n    return new Promise((resolve, reject) => {\n      request({\n        url: '/api/patrols/plans/list',\n        method: 'POST',\n        data: {\n          name: params?.name || null,\n          status: params?.status || null,\n          patrol_type: params?.type || null,\n          search_date: params?.timeRange || null,\n\t\t  page: params?.page || 1,\n\t\t  pageSize: params?.pageSize || 10,\n\t\t  heatUnitId: params.heatUnitId, // 项目权限参数，格式为\"1,3\"或\"0\"\n        }\n      })\n      .then(res => {\n        resolve(res);\n      })\n      .catch(err => {\n        console.warn('获取巡检计划列表失败，使用默认数据', err);\n      });\n    });\n  },\n  \n  // 获取巡检计划详情\n  getPlanDetail(id) {\n    return new Promise((resolve, reject) => {\n      request({\n        url: `/api/patrols/plans/${id}`,\n        method: 'GET'\n      })\n      .then(res => {\n        resolve(res);\n      })\n      .catch(err => {\n        console.warn('获取巡检计划详情失败，使用默认数据', err); \n      });\n    });\n  },\n  \n  // 更新巡检计划\n  updatePlan(id, data) {\n    return request({\n      url: `/api/patrols/plans/${id}`,\n      method: 'PUT',\n      data\n    });\n  },\n  \n  // 删除巡检计划\n  deletePlan(id) {\n    return request({\n      url: `/api/patrols/plans/${id}`,\n      method: 'DELETE'\n    });\n  },\n  \n  // 获取巡检项目列表\n  getItemList(params) {\n    return new Promise((resolve, reject) => {\n      // 检查是否有planId参数，有则使用按计划获取项目的接口\n      if (params && params.planId) {\n        request({\n          url: `/api/patrols/plans/${params.planId}/items`,\n          method: 'GET'\n        })\n        .then(res => {\n          resolve(res);\n        })\n        .catch(err => {\n          console.warn('获取巡检计划项目列表失败，使用默认数据', err); \n        });\n      } else { \n      }  \n    });\n  },\n  \n  // 获取巡检计划关联的执行人员列表\n  getPlanExecutorList(params) {\n    return new Promise((resolve, reject) => {\n      if (!params || !params.planId) {\n        resolve({\n          code: 400,\n          message: '缺少planId参数',\n          data: []\n        });\n        return;\n      }\n      \n      request({\n        url: `/api/patrols/plans/${params.planId}/executors`,\n        method: 'GET'\n      })\n      .then(res => {\n        resolve(res);\n      })\n      .catch(err => {\n        console.warn('获取计划执行人员列表失败，使用默认数据', err);\n        // 提供默认数据，避免页面错误\n        resolve({\n          code: 200,\n          message: \"获取计划执行人员列表成功(本地数据)\",\n          data: [\n            {\n              id: 1,\n              name: \"张工\",\n              position: \"运维工程师\",\n              phone: \"13800138001\",\n              skills: [\"设备检查\", \"参数测量\"]\n            },\n            {\n              id: 2,\n              name: \"李工\",\n              position: \"巡检员\",\n              phone: \"13800138002\",\n              skills: [\"设备检查\", \"管道巡视\"]\n            },\n            {\n              id: 3,\n              name: \"王工\",\n              position: \"技术主管\",\n              phone: \"13800138003\",\n              skills: [\"设备检查\", \"故障诊断\", \"数据分析\"]\n            }\n          ]\n        });\n      });\n    });\n  },\n\n  // 获取巡检项目字典列表\n  getDictionaryItemList(params) { \n    return new Promise((resolve, reject) => {\n      request({\n        url: '/api/patrols/dictionary/items',\n        method: 'GET',\n        data: params\n      })\n      .then(res => {\n        resolve(res);\n      })\n      .catch(err => {\n        console.warn('获取巡检项目列表失败，使用默认数据', err);\n        // 提供默认数据，避免页面错误\n        resolve({\n          code: 200,\n          message: \"获取巡检项目列表成功(本地数据)\",\n          data: [\n            {\n              id: 1,\n              name: \"泵压力检查\",\n              type: \"parameter\",\n              standard: \"0.6-0.8MPa\"\n            },\n            {\n              id: 2,\n              name: \"管道泄漏检查\",\n              type: \"visual\",\n              standard: \"无泄漏\"\n            },\n            {\n              id: 3,\n              name: \"阀门状态检查\",\n              type: \"operation\",\n              standard: \"阀门开关灵活\"\n            }\n          ]\n        });\n      });\n    });\n  },\n  \n  // 创建巡检项目\n  createItem(data) {\n    return request({\n      url: '/api/patrols/items',\n      method: 'POST',\n      data\n    });\n  },\n  \n  // 开始执行巡检计划\n  startPlan(id) {\n    return request({\n      url: `/api/patrols/plans/${id}/start`,\n      method: 'POST'\n    });\n  },\n  \n  // 完成巡检计划\n  completePlan(id, data) {\n    return request({\n      url: `/api/patrols/plans/${id}/complete`,\n      method: 'POST',\n      data\n    });\n  },\n  \n  // 提交巡检结果\n  submitPatrolResult(patrolRecordId, data) {\n    return request({\n      url: `/api/patrols/records/${patrolRecordId}/results`,\n      method: 'POST',\n      data\n    });\n  },\n  \n  // 获取巡检记录列表\n  getPatrolRecords(params) {\n    // 构建查询参数，按接口文档要求调整\n    const queryParams = {\n      status: params?.status || undefined,\n      startDate: params?.startDate || undefined,\n      endDate: params?.endDate || undefined,\n      page: params?.page || 1,\n      pageSize: params?.pageSize || 3\n    };\n\t\n\tqueryParams.executorId=uni.getStorageSync(\"userId\");\n\tqueryParams.role=uni.getStorageSync(\"userRole\");\n    \n    // 移除undefined的属性\n    Object.keys(queryParams).forEach(key => {\n      if (queryParams[key] === undefined) {\n        delete queryParams[key];\n      }\n    });\n    \n    return new Promise((resolve, reject) => {\n      request({\n        url: '/api/patrols/records/list',\n        method: 'POST', // 根据接口文档，使用POST方法\n        data: queryParams\n      })\n      .then(res => {\n        resolve(res);\n      })\n      .catch(err => {\n        console.warn('获取巡检记录列表失败，使用默认数据', err); \n      });\n    });\n  },\n   \n  // 获取巡检结果详情\n  getPatrolResultDetail(recordId) {\n    return request({\n      url: `/api/patrols/records/${recordId}/results`,\n      method: 'GET'\n    });\n  },\n  \n  // 获取巡检周期类型选项\n  getScheduleTypes() {\n    return [\n      { label: '每日', value: 'daily' },\n      { label: '每周', value: 'weekly' },\n      { label: '每月', value: 'monthly' },\n      { label: '自定义', value: 'custom' }\n    ];\n  },\n  \n  // 获取巡检检查类型选项\n  getCheckTypes() {\n    return [\n      { label: '目视检查', value: 'visual' },\n      { label: '参数检查', value: 'parameter' },\n      { label: '操作检查', value: 'operation' }\n    ];\n  },\n  \n  // 获取巡检计划执行人列表\n  getPlanExecutors(planId) {\n    return new Promise((resolve, reject) => {\n      request({\n        url: `/api/patrols/plans/${planId}/executors`,\n        method: 'GET'\n      })\n      .then(res => {\n        resolve(res);\n      })\n      .catch(err => {\n        console.warn('获取巡检计划执行人列表失败，使用默认数据', err);\n        // 提供默认数据，避免页面错误\n        resolve({\n          code: 200,\n          message: \"获取计划执行人员列表成功(本地数据)\",\n          data: [\n             \n          ]\n        });\n      });\n    });\n  },\n  \n  // 提交巡检记录\n  submitPatrolRecord(data) {\n    return new Promise((resolve, reject) => {\n      request({\n        url: '/api/patrols/records',\n        method: 'POST',\n        data\n      })\n      .then(res => {\n        resolve(res);\n      })\n      .catch(err => {\n        console.warn('提交巡检记录失败', err);\n        // 提供默认响应，模拟提交成功\n        resolve({\n          code: 200,\n          message: \"巡检记录提交成功\"\n        });\n      });\n    });\n  },\n  \n  // 获取巡检计划列表\n  getPatrolPlans(params) {\n    return new Promise((resolve, reject) => {\n      request({\n        url: '/api/patrols/plans/list',\n        method: 'POST',\n        data: params\n      })\n      .then(res => {\n        resolve(res);\n      })\n      .catch(err => {\n        console.warn('获取巡检计划列表失败，使用默认数据', err);\n        // 提供默认数据，避免页面错误\n        resolve({\n          code: 200,\n          message: \"巡检列表查询成功\",\n          data: []\n        });\n      });\n    });\n  },\n \n  // 获取巡检计划消息\n  getPatrolPlansMessages(userId, role){\n    return request({\n      url: `/api/patrols/plans/messages`,\n      method: 'GET',\n      data: {\n        uid: userId,\n        role: role\n      }\n    });\n  },\n  \n  // 获取有限数量的巡检工单\n  getLimitedPatrolRecords(limit = 5) {\n    const userId = uni.getStorageSync(\"userId\");\n    const role = uni.getStorageSync(\"userRole\");\n    \n    return request({\n      url: '/api/patrols/records/limit',\n      method: 'GET',\n      data: {\n        uid: userId,\n        role: role,\n        limit: limit\n      }\n    });\n  } \n};\n\n// 设备模块API\nconst deviceApi = {\n  getList(params) {\n    // 参照接口文档处理参数\n    const queryParams = {\n      page: params?.page || 1,\n      pageSize: params?.pageSize || 10,\n      status: params?.status || '',\n      area: params?.area || '',\n      type: params?.type || '',\n      keyword: params?.keyword || '',\n      sortBy: params?.sortBy || '',\n      sortOrder: params?.sortOrder || 'desc'\n    };\n    \n    return request({\n      url: '/api/devices/list',\n      method: 'POST',\n      data: queryParams\n    });\n  },\n  getDetail(id) {\n    return request({\n      url: `/api/devices/${id}`,\n      method: 'GET'\n    });\n  },\n  getStats() {\n    return request({\n      url: '/api/device/stats',\n      method: 'GET'\n    });\n  },\n  // 根据热用户ID获取设备列表\n  getDevicesByHeatUnitId(heatUnitId) {\n    return new Promise((resolve, reject) => {\n      request({\n        url: `/api/devices/heat-unit/${heatUnitId}`,\n        method: 'GET'\n      })\n      .then(res => {\n        resolve(res);\n      })\n      .catch(err => {\n        console.warn(`获取热用户(ID:${heatUnitId})的设备列表失败，使用默认数据`, err); \n        // 添加默认数据作为备用，防止UI卡住\n        resolve({\n          code: 200,\n          message: \"获取设备列表成功(本地默认数据)\",\n          data: [\n            { id: '1001', name: '水泵1#', type: 'pump', deviceParent: 'heatstation' },\n            { id: '1002', name: '水泵2#', type: 'pump', deviceParent: 'heatstation' },\n            { id: '1003', name: '调节阀1#', type: 'valve', deviceParent: 'heatstation' },\n            { id: '1004', name: '调节阀2#', type: 'valve', deviceParent: 'heatstation' },\n            { id: '1005', name: '换热器', type: 'heatexchanger', deviceParent: 'heatstation' }\n          ]\n        });\n      });\n    });\n  },\n  \n  // 根据设备ID获取设备巡检项\n  getPatrolItemsByDeviceId(deviceId) {\n    return new Promise((resolve, reject) => {\n      request({\n        url: `/api/devices/patrol-items/${deviceId}`,\n        method: 'GET'\n      })\n      .then(res => {\n        resolve(res);\n      })\n      .catch(err => {\n        console.warn(`获取设备(ID:${deviceId})的巡检项失败，使用默认数据`, err); \n      });\n    });\n  }  \n};\n\n// 故障模块API\nconst faultApi = {\n  reportFault(data) {\n    return new Promise((resolve, reject) => {\n      request({\n        url: '/api/faults/report',\n        method: 'POST',\n        data\n      })\n      .then(res => {\n        if (res.code === 200) {\n          resolve(res);\n        } else {\n          // 处理业务错误（状态码是200但业务状态不成功的情况）\n          const errorMsg = res.message || '上报失败';\n          reject(new Error(errorMsg));\n        }\n      })\n      .catch(err => {\n        // 处理网络错误或服务器错误\n        console.error('故障上报请求失败:', err);\n        \n        // 尝试从错误响应中提取更有用的信息\n        let errorMsg = '网络异常，请稍后重试';\n        \n        if (err.data && typeof err.data === 'object') {\n          // 尝试从后端错误响应中提取错误信息\n          if (err.data.message) {\n            errorMsg = err.data.message;\n          } else if (err.data.error) {\n            errorMsg = err.data.error;\n          }\n        } else if (err.errMsg) {\n          errorMsg = err.errMsg;\n        }\n        \n        reject(new Error(errorMsg));\n      });\n    });\n  },\n  getFaultList(params) {\n    // 构建符合后端接口规范的参数\n   const queryParams = {\n     status: params?.status,\n     date: params?.date,\n     page: params?.page || 1,\n     pageSize: params?.pageSize || 10\n   };\n    \n    // 添加热用户ID参数\n    if (params && params.heatUnitId) {\n      queryParams.heatUnitId = params.heatUnitId;\n    }\n    \n    // 移除undefined的参数\n    Object.keys(queryParams).forEach(key => {\n      if (queryParams[key] === undefined) {\n        delete queryParams[key];\n      }\n    });\n    \n    return request({\n      url: '/api/faults/list',\n      method: 'GET',\n      data: queryParams\n    });\n  },\n  getFaultDetail(id) {\n    return request({\n      url: `/api/faults/detail/${id}`,\n      method: 'GET'\n    });\n  }, \n  updateFaultStatus(requestData) {\n   // 兼容两种调用方式\n   return request({ // 使用封装的 request 函数\n    url: '/api/faults/status', // API 路径\n    method: 'POST',            // 请求方法\n    data: {\n      fault_id: requestData.fault_id,         // 故障ID\n      operator_id: requestData.operator_id,   // 操作员ID\n      fault_status: requestData.fault_status, // 目标状态 (例如 '已确认')\n      repair_user_id: requestData.repair_user_id,\n\t  heat_unit_id: requestData.heat_unit_id // 添加热用户ID\n    }\n  })\n}, \n  \n  // 获取故障消息 /api/faults/messages\n  getFaultMessages(userId, userRole) {\n    return request({\n      url: '/api/faults/messages',\n      method: 'GET',\n      data: {\n        uid: userId,\n        role: userRole,\n        heatUnitId: uni.getStorageSync(\"heatUnitId\")\n      }\n    });\n  }  \n};\n\n// 室温API模块\nconst temperatureApi = {\n  // 获取户外温度\n  getOutdoorTemperature() {\n    return request({\n      url: '/api/temperature/outdoor',\n      method: 'GET'\n    });\n  },\n  \n  // 获取室温列表\n  getTemperatureList(params) {\n    return request({\n      url: '/api/temperatures/list',\n      method: 'GET',\n      data: params\n    });\n  },\n  \n  // 上报温度\n  reportTemperature(data) {\n    return request({\n      url: '/api/temperature/report',\n      method: 'POST',\n      data: {\n        heatUnitId: data.heatUnitId,\n        buildingNo: data.buildingNo,\n        unitNo: data.unitNo,\n        roomNo: data.roomNo,\n        indoorTemp: data.indoorTemp,\n        outdoorTemp: data.outdoorTemp,\n        longitude: data.longitude,\n        latitude: data.latitude,\n        remark: data.remark,\n        images: data.images,\n        reporterId: uni.getStorageSync('userId')\n      }\n    });\n  },\n  \n  // 获取室温详情\n  getTemperatureDetail(id) {\n    return request({\n      url: `/api/temperatures/${id}`,\n      method: 'GET'\n    });\n  }\n};\n\n// 首页模块API\nconst homeApi = {\n  // 获取首页概览数据\n  getOverview() {\n    return request({\n      url: '/api/home/<USER>',\n      method: 'GET'\n    });\n  },\n  // 获取最近工单\n  getRecentOrders(limit = 5) {\n\tconst apiParams = {};\n    apiParams.limit=limit;\n\tapiParams.uid=uni.getStorageSync(\"userId\");\n\tapiParams.role=uni.getStorageSync(\"userRole\");\n\tapiParams.heatUnitId=uni.getStorageSync(\"heatUnitId\");\n    return request({\n      url: '/api/WorkOrders/list',\n      method: 'GET',\n      data: apiParams\n    });\n  },\n  // 获取室外温度\n  getOutdoorTemperature() {\n    return temperatureApi.getOutdoorTemperature();\n  },\n  \n  // 提交室温报告\n  reportTemperature(data) {\n    return temperatureApi.reportTemperature(data);\n  },\n\n  // 获取热用户总数\n  getHeatUnitCount() {\n    return request({\n      url: '/api/heatunits/count',\n      method: 'GET'\n    });\n  },\n\n  // 获取换热站在线率\n  getHeatUnitOnlineRate() {\n    return request({\n      url: '/api/hes/online-rate',\n      method: 'GET'\n    });\n  },\n\n  // 获取本周故障告警数量\n  getWeeklyFaultCount() {\n    return request({\n      url: '/api/faults/weekly-count',\n      method: 'GET'\n    });\n  } \n};\n\n// 热用户API模块\nconst heatUnitApi = {\n  // 获取热用户列表\n  getList() {\n    return request({\n      url: '/api/heatunits/list',\n      method: 'GET'\n    });\n  },\n  \n  // 获取用户关联的热力单位\n  getByUserId(userId) {\n    return new Promise((resolve, reject) => {\n      // 获取用户的热力单位ID\n      const heatUnitId = uni.getStorageSync('heatUnitId');\n      console.log(\"获取到的原始heatUnitId:\", heatUnitId);\n      \n      // 获取所有热力单位\n      this.getList()\n        .then(res => {\n          if (res.code === 200 && res.data) {\n            // 如果用户有关联的热力单位ID且不是0\n            if (heatUnitId && heatUnitId !== '0' && heatUnitId !== 0) {\n              // 处理可能包含多个项目ID的情况（逗号分隔）\n              const userHeatUnitIds = heatUnitId.split(',').map(id => id.trim());\n              \n              // 过滤出用户关联的所有热力单位\n              // 注意：将ID统一转为字符串进行比较，避免类型不匹配问题\n              const userHeatUnits = res.data.filter(unit => \n                userHeatUnitIds.includes(String(unit.id)) || userHeatUnitIds.includes(Number(unit.id))\n              );\n              \n              if (userHeatUnits && userHeatUnits.length > 0) {\n                // 返回包含用户关联的热力单位的数组\n                resolve({\n                  code: 200,\n                  message: \"获取用户热力单位成功\",\n                  data: userHeatUnits\n                });\n              } else {\n                // 如果未找到匹配的热力单位，返回所有热力单位\n                console.log(\"未找到匹配的热力单位，返回所有热力单位\");\n                resolve({\n                  code: 200,\n                  message: \"未找到匹配的热力单位，返回所有热力单位\",\n                  data: res.data\n                });\n              }\n            } else {\n              // 如果用户没有关联的热力单位ID或ID为0，则返回所有热力单位\n              console.log(\"无热力单位ID或ID为0，返回所有热力单位\");\n              resolve({\n                code: 200,\n                message: \"获取所有热力单位成功\",\n                data: res.data\n              });\n            }\n          } else {\n            // 如果获取热力单位列表失败，返回原始响应\n            resolve(res);\n          }\n        })\n        .catch(err => {\n          reject(err);\n        });\n    });\n  }\n};\n\n// 室温上报\nexport const temperatureReportApi = {\n  submit(data) {\n    return request({\n      url: '/api/temperatures/report',\n      method: 'POST',\n      data\n    });\n  }\n};\n\n// 换热站API模块\nconst heatingStationApi = {\n  // 获取换热站列表\n  getList(params) {\n    return new Promise((resolve, reject) => {\n      request({\n        url: '/api/hes/list',\n        method: 'POST',\n        data: params\n      })\n      .then(res => {\n        resolve(res);\n      })\n      .catch(err => {\n        console.warn('获取换热站列表失败，使用默认数据', err);  \n      });\n    });\n  },\n  \n  // 获取换热站详情\n  getDetail(id) {\n    return new Promise((resolve, reject) => {\n      request({\n        url: '/api/hes/detail',\n        method: 'POST',\n        data: { id }\n      })\n      .then(res => {\n        resolve(res);\n      })\n      .catch(err => {\n        console.warn('获取换热站详情失败，使用默认数据', err);\n        // 提供默认数据，避免页面错误\n        resolve({\n          code: 200,\n          message: \"换热站数据获取成功\",\n          data: {\n            basic_info: {\n              id: id,\n              name: \"金色家园换热站\",\n              hes_code: \"HES001\",\n              heat_unit_name: \"金色家园\",\n              status: \"online\",\n              run_mode: \"自动\",\n              calc_mode: \"二次供水温度控制\",\n              used_year: 5,\n              heating_area: 25000.5,\n              design_load: 1500.0,\n              design_flow: 30.0\n            },\n            realtime_data: {\n              primary_system: {\n                supply_temp: 85.5,\n                return_temp: 60.2,\n                supply_pressure: 0.6,\n                return_pressure: 0.3,\n                flow_rate: 25.6,\n                power: 1200.5\n              },\n              secondary_system: {\n                supply_temp: 65.3,\n                return_temp: 45.1,\n                supply_pressure: 0.4,\n                return_pressure: 0.2,\n                flow_rate: 28.3\n              },\n              equipment_status: {\n                pumps: [\n                  {\n                    id: 1,\n                    name: \"一次泵1#\",\n                    status: \"running\",\n                    frequency: 42.5,\n                    current: 18.6,\n                    power: 7.5\n                  },\n                  {\n                    id: 2,\n                    name: \"一次泵2#\",\n                    status: \"standby\",\n                    frequency: 0,\n                    current: 0,\n                    power: 7.5\n                  }\n                ],\n                valves: [\n                  {\n                    id: 1,\n                    name: \"调节阀1#\",\n                    opening: 65,\n                    status: \"normal\"\n                  },\n                  {\n                    id: 2,\n                    name: \"调节阀2#\",\n                    opening: 40,\n                    status: \"normal\"\n                  }\n                ]\n              },\n              last_update_time: \"2025-03-01 12:30:45\"\n            }\n          }\n        });\n      });\n    });\n  },\n  \n  // 换热站控制\n  controlDevice(params) {\n    return new Promise((resolve, reject) => {\n      request({\n        url: '/api/hes/control',\n        method: 'POST',\n        data: params\n      })\n      .then(res => {\n        resolve(res);\n      })\n      .catch(err => {\n        console.warn('换热站控制命令发送失败', err);\n        // 实际生产环境不应提供默认数据，这里是为了开发和测试\n        if (err.statusCode === 600) {\n          reject({\n            code: 600,\n            message: \"设备离线\",\n            details: \"无法发送控制命令，请检查设备连接状态\"\n          });\n        } else {\n          reject(err);\n        }\n      });\n    });\n  },\n  \n  // 获取换热站远程协助信息\n  getRemoteAssistance(hesId) {\n    return request({\n      url: '/api/hes/remote',\n      method: 'POST',\n      data: { hes_id: hesId }\n    });\n  },\n  \n  // 获取换热站历史数据\n  getHistoryData(params) {\n    return request({\n      url: '/api/hes/history',\n      method: 'POST',\n      data: params\n    });\n  },\n  \n  // 获取换热站数据曲线\n  getChartData(params) {\n    return request({\n      url: '/api/hes/chart',\n      method: 'POST',\n      data: params\n    });\n  },\n  \n  // 获取换热站告警列表\n  getAlarmList(params) {\n    return new Promise((resolve, reject) => {\n      request({\n        url: '/api/hes/alarms/list',\n        method: 'POST',\n        data: params\n      })\n      .then(res => {\n        resolve(res);\n      })\n      .catch(err => {\n        console.warn('获取换热站告警列表失败', err);\n        // 提供默认数据，避免页面错误\n        resolve({\n          code: 200,\n          message: \"告警列表获取成功\",\n          data: {\n            total: 2,\n            list: [\n              {\n                id: 1,\n                hes_id: params.hes_id || 1,\n                hes_name: \"金色家园换热站\",\n                alarm_type: \"热源\",\n                alarm_name: \"一次供水温度过高\",\n                level: \"urgent\",\n                value: 95.5,\n                threshold: 90.0,\n                status: \"active\",\n                start_time: \"2025-03-01 10:15:30\",\n                end_time: null,\n                duration: \"02:45:12\"\n              },\n              {\n                id: 2,\n                hes_id: params.hes_id || 1,\n                hes_name: \"金色家园换热站\",\n                alarm_type: \"压力\",\n                alarm_name: \"二次回水压力过低\",\n                level: \"warning\",\n                value: 0.15,\n                threshold: 0.2,\n                status: \"active\",\n                start_time: \"2025-03-01 11:05:22\",\n                end_time: null,\n                duration: \"01:55:20\"\n              }\n            ]\n          }\n        });\n      });\n    });\n  },\n  \n  // 获取换热站告警详情\n  getAlarmDetail(id) {\n    return request({\n      url: '/api/hes/alarms/detail',\n      method: 'POST',\n      data: { id }\n    });\n  },\n  \n  // 获取换热站告警统计\n  getAlarmStats(params) {\n    return request({\n      url: '/api/hes/alarms/stats',\n      method: 'POST',\n      data: params\n    });\n  },\n  \n  // 获取换热站在线率\n  getOnlineRate() {\n    return request({\n      url: '/api/hes/online-rate',\n      method: 'GET'\n    });\n  }\n};\n\n// 考勤管理API模块\nconst attendanceApi = {\n  // 提交打卡记录\n  submitClock(data) {\n    return request({\n      url: '/api/attendance/clock',\n      method: 'POST',\n      data\n    });\n  },\n  \n  // 获取考勤记录\n  getRecords(params = {}) {\n    const { userId, year, month, day, status, startDate, endDate, keyword } = params;\n    let url = '/api/attendance/records';\n    \n    // 构建查询参数\n    const queryParams = [];\n    if (userId) queryParams.push(`user_id=${userId}`);\n    if (year) queryParams.push(`year=${year}`);\n    if (month) queryParams.push(`month=${month}`);\n    if (day) queryParams.push(`day=${day}`);\n    if (status) queryParams.push(`status=${status}`);\n    if (startDate) queryParams.push(`startDate=${startDate}`);\n    if (endDate) queryParams.push(`endDate=${endDate}`);\n    if (keyword) queryParams.push(`keyword=${encodeURIComponent(keyword)}`);\n    \n    if (queryParams.length > 0) {\n      url += `?${queryParams.join('&')}`;\n    }\n    \n    return request({\n      url,\n      method: 'GET'\n    });\n  },\n  \n  // 获取考勤统计\n  getStats(params = {}) {\n    const { userId, year, month, day } = params;\n    let url = '/api/attendance/stats';\n    \n    // 构建查询参数\n    const queryParams = [];\n    if (userId) queryParams.push(`user_id=${userId}`);\n    if (year) queryParams.push(`year=${year}`);\n    if (month) queryParams.push(`month=${month}`);\n    if (day) queryParams.push(`day=${day}`);\n    \n    if (queryParams.length > 0) {\n      url += `?${queryParams.join('&')}`;\n    }\n    \n    return request({\n      url,\n      method: 'GET'\n    });\n  },\n  \n  // 获取今日打卡记录\n  getTodayRecord() \n  {\n\t  const apiParams = {\n\t  };\n\t  apiParams.user_id = uni.getStorageSync(\"userId\");\n      return request({\n      url: '/api/attendance/today',\n      method: 'GET',\n\t  data:apiParams\n    });\n  },\n  \n  // 获取最近N天的打卡记录\n  getRecentRecords(days = 7) { \n\t  const apiParams = {\n\t\t  days  \n\t  };\n\t    apiParams.user_id = uni.getStorageSync(\"userId\");\n    return request({\n      url: '/api/attendance/recent',\n      method: 'GET',\n      data: apiParams\n    });\n  },\n  \n  // 获取打卡规则\n  getClockRules() {\n    return request({\n      url: '/api/attendance/rules',\n      method: 'GET'\n    });\n  },\n  \n  // 更新打卡规则\n  updateClockRules(clockInTime, clockOutTime) {\n    return request({\n      url: '/api/attendance/updaterules',\n      method: 'POST',\n      data: {\n        clock_in_time: clockInTime,\n        clock_out_time: clockOutTime\n      }\n    });\n  },\n  \n  // 检查是否在考勤范围内\n  checkAttendanceArea(data) {\n    return request({\n      url: '/api/attendance/check-area',\n      method: 'POST',\n      data\n    });\n  },\n  \n  // 提交补卡申请\n  submitSupplementApplication(data) {\n    return request({\n      url: '/api/attendance/supplement',\n      method: 'POST',\n      data\n    });\n  },\n  \n  // 获取员工考勤列表（管理员用）\n  getStaffAttendance(params = {}) {\n    return request({\n      url: '/api/attendance/staff',\n      method: 'GET',\n      data: params\n    });\n  },\n  \n  // 获取部门考勤统计（管理员用）\n  getDepartmentStats(params = {}) {\n    return request({\n      url: '/api/attendance/department-stats',\n      method: 'GET',\n      data: params\n    });\n  },\n  \n  // 审批补卡申请（管理员用）\n  approveSupplementApplication(data) {\n    return request({\n      url: '/api/attendance/supplement-approve',\n      method: 'POST',\n      data\n    });\n  },\n  \n  // 获取所有员工列表（用于统计页面的员工选择）\n  getAllStaff() {\n    return request({\n      url: '/api/attendance/all-staff',\n      method: 'GET'\n    });\n  },\n  \n  // 导出考勤数据\n  exportAttendance(params = {}) {\n    return request({\n      url: '/api/attendance/export',\n      method: 'POST',\n      data: params\n    });\n  },\n\n  /**\n   * 上传员工位置轨迹\n   * @param {Object} data 包含userId, longitude, latitude的对象\n   * @returns {Promise} 上传结果\n   */\n  uploadPersonTrajectory(data) {\n    console.log('准备上传位置轨迹:', data);\n    \n    // 检查参数有效性\n    if (!data.userId || !data.longitude || !data.latitude) {\n      console.error('上传位置轨迹参数无效:', data);\n      return Promise.reject({errMsg: '位置参数无效'});\n    }\n    \n    // 构建查询参数字符串，同时传递userId和employeeId（使用相同的值）\n    const url = `/api/person/trajectory/location?userId=${data.userId}&employeeId=${data.userId}&longitude=${data.longitude}&latitude=${data.latitude}`;\n    \n    // 特殊处理：直接使用uni.request发起请求，处理201状态码\n    return new Promise((resolve, reject) => {\n      // 获取token\n      const token = uni.getStorageSync('token');\n      const header = {\n        'Content-Type': 'application/json'\n      };\n      \n      // 添加token (如果有)\n      if (token) {\n        header.Authorization = `Bearer ${token}`;\n      }\n      \n      uni.request({\n        url: BASE_URL + url,\n        method: 'POST',\n        header: header,\n        success: (res) => {\n          console.log('位置轨迹上传响应:', res);\n          \n          // 特别处理201状态码，将其视为成功\n          if ((res.statusCode === 200 || res.statusCode === 201) && \n              res.data && res.data.code === 200) {\n            resolve(res.data);\n          } else {\n            reject({\n              errMsg: `请求失败: ${res.statusCode}`,\n              statusCode: res.statusCode,\n              data: res.data\n            });\n          }\n        },\n        fail: (err) => {\n          console.error('位置轨迹上传请求失败:', err);\n          reject(err);\n        }\n      });\n    });\n  }\n};\n\n//  告警管理  \nconst alarmApi = {\n  // 获取告警列表\n  getAlarmList(userId, role) {\n    console.log(`调用获取告警列表: userId=${userId}, role=${role}`);\n    return request({ // 使用封装的 request 函数\n      url: '/api/alarm/messages', // 后端接口路径\n      method: 'GET',\n      data: {\n        uid: userId,\n        role: role,\n        heatUnitId: uni.getStorageSync(\"heatUnitId\")\n      }\n    });\n  }, \n  \n  // 更新告警状态\n  updateAlarmStatus(alarmId, status) {\n    console.log(`调用更新告警状态API: alarmId=${alarmId}, status=${status}`);\n    return request({ // 使用封装的 request 函数\n      url: '/api/alarm/updateStatus', // 后端接口路径\n      method: 'POST', // 根据接口文档和实践，推测使用 POST\n      data: {\n        alarm_id: alarmId,    // 使用下划线命名，匹配接口文档中的 request body\n        alarm_status: status // 接口文档中这个键名包含空格，需要加引号\n      }\n    });\n  }\n};\n \n/**\n * 系统字典API\n */\nconst dictApi = {\n  /**\n   * 根据字典ID获取字典数据列表\n   * @param {Number} dictId 字典ID\n   * @returns {Promise} 包含字典数据的Promise对象\n   */\n  getDictDataByDictId(dictId) {\n    return request({\n      url: `/api/dict/data/${dictId}`,\n      method: 'GET'\n    });\n  }\n};\n// 导出API模块\nexport {\n  BASE_URL,\n  request,\n  testApiConnection,\n  userApi,\n  workOrderApi,\n  patrolApi,\n  deviceApi,\n  faultApi,\n  homeApi,\n  temperatureApi,\n  heatUnitApi,\n  heatingStationApi,\n  attendanceApi,\n  alarmApi,\n  materialsApi,\n  dictApi\n};\n\n/**\n * 系统API\n */\nexport const systemApi = {\n  /**\n   * 获取系统参数\n   * @returns {Promise} 包含系统参数的Promise对象\n   */\n  getSystemParams() {\n    return request({\n      url: '/api/system/params',\n      method: 'GET'\n    });\n  }\n};\n\n "], "names": ["uni"], "mappings": ";;;AACK,MAAC,WAAW;AAEjB,MAAM,UAAU;AAChB,MAAM,cAAc;AAGpB,SAAS,oBAAoB;AAC3B,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,kBAAY,MAAA,MAAA,OAAA,sBAAA,cAAc,QAAQ;AAGlCA,kBAAAA,MAAI,QAAQ;AAAA,MACV,KAAK,GAAG,QAAQ;AAAA,MAChB,QAAQ;AAAA;AAAA,MACR,SAAS;AAAA;AAAA,MAET,iBAAiB;AAAA,MACjB,QAAQ;AAAA,QACN,gBAAgB;AAAA,QAChB,iCAAiC;AAAA,QACjC,kCAAkC;AAAA,MACnC;AAAA,MACD,SAAS,CAAC,QAAQ;AAChBA,sBAAY,MAAA,MAAA,OAAA,sBAAA,cAAc,GAAG;AAE7B,gBAAQ,IAAI;AAAA,MACb;AAAA,MACD,MAAM,CAAC,QAAQ;AACbA,sBAAc,MAAA,MAAA,SAAA,sBAAA,cAAc,GAAG;AAC/B,gBAAQ,KAAK;AAAA,MACd;AAAA,IACP,CAAK;AAAA,EACL,CAAG;AACH;AAGA,SAAS,QAAQ,SAAS;AACxB,MAAI,aAAa;AAEjB,QAAM,iBAAiB,MAAM;AAC3B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEtC,YAAM,MAAM,QAAQ,IAAI,WAAW,MAAM,IAAI,QAAQ,MAAM,WAAW,QAAQ;AAQ9E,YAAM,SAAS;AAAA,QACb,gBAAgB;AAAA,QAChB,GAAI,QAAQ,UAAU,CAAE;AAAA,MAChC;AAGM,YAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,UAAI,OAAO;AACT,eAAO,gBAAgB,UAAU,KAAK;AAAA,MAE9C,OAAa;AACLA,sBAAAA,MAAA,MAAA,QAAA,sBAAa,2BAA2B;AAAA,MACzC;AAGDA,oBAAAA,MAAI,QAAQ;AAAA,QACV;AAAA,QACA,QAAQ,QAAQ,UAAU;AAAA,QAC1B,MAAM,QAAQ;AAAA,QACd;AAAA;AAAA,QAEA,SAAS,QAAQ,WAAW;AAAA,QAC5B,SAAS,CAAC,QAAQ;AAChBA,wBAAY,MAAA,MAAA,OAAA,sBAAA,aAAa,GAAG;AAG5B,cAAI,IAAI,eAAe,KAAK;AAC1BA,0BAAAA,MAAc,MAAA,SAAA,sBAAA,aAAa;AAE3BA,gCAAI,kBAAkB,OAAO;AAE7BA,0BAAAA,MAAI,WAAW;AAAA,cACb,KAAK;AAAA,YACnB,CAAa;AACD,mBAAO;AAAA,cACL,QAAQ;AAAA,cACR,YAAY;AAAA,YAC1B,CAAa;AACD;AAAA,UACD;AAGD,cAAI,IAAI,cAAc,OAAO,IAAI,aAAa,KAAK;AAEjD,oBAAQ,IAAI,IAAI;AAAA,UAC5B,OAAiB;AAELA,gCAAc,MAAA,SAAA,sBAAA,UAAU,IAAI,YAAY,IAAI,IAAI;AAChD,mBAAO;AAAA,cACL,QAAQ,SAAS,IAAI,UAAU;AAAA,cAC/B,YAAY,IAAI;AAAA,cAChB,MAAM,IAAI;AAAA,YACxB,CAAa;AAAA,UACF;AAAA,QACF;AAAA,QACD,MAAM,OAAO,QAAQ;AACnBA,wBAAAA,MAAc,MAAA,SAAA,uBAAA,iBAAiB,aAAa,CAAC,MAAM,GAAG;AAGtD,cAAI,aAAa,gBACZ,IAAI,OAAO,SAAS,SAAS,KAAK,IAAI,OAAO,SAAS,SAAS,IAAI;AACtE;AACAA,0BAAAA,MAAA,MAAA,OAAA,uBAAY,SAAS,UAAU,IAAI,WAAW,MAAM;AACpD,gBAAI;AACF,oBAAM,SAAS,MAAM;AACrB,sBAAQ,MAAM;AAAA,YACf,SAAQ,YAAY;AACnB,qBAAO,UAAU;AAAA,YAClB;AAAA,UACb,OAAiB;AACL,mBAAO;AAAA,cACL,QAAQ,IAAI,UAAU;AAAA,cACtB;AAAA,cACA,QAAQ,QAAQ,UAAU;AAAA,cAC1B,MAAM,QAAQ;AAAA,YAC5B,CAAa;AAAA,UACF;AAAA,QACF;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACL;AAEE,SAAO,eAAc;AACvB;AAGK,MAAC,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnB,mBAAmB;AACjB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,cAAQ;AAAA,QACN,KAAK;AAAA,QACL,QAAQ;AAAA,MAChB,CAAO,EACA,KAAK,SAAO;AACXA,sBAAA,MAAA,MAAA,OAAA,uBAAY,aAAa,GAAG;AAC5B,gBAAQ,GAAG;AAAA,MACnB,CAAO,EACA,MAAM,SAAO;AACZA,sBAAA,MAAA,MAAA,QAAA,uBAAa,mBAAmB,GAAG;AAEnC,gBAAQ;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA,YACJ,EAAE,IAAI,GAAG,MAAM,MAAM,eAAe,KAAM;AAAA,YAC1C,EAAE,IAAI,GAAG,MAAM,QAAQ,eAAe,KAAM;AAAA,YAC5C,EAAE,IAAI,GAAG,MAAM,SAAS,eAAe,MAAO;AAAA,YAC9C,EAAE,IAAI,GAAG,MAAM,MAAM,eAAe,KAAM;AAAA,YAC1C,EAAE,IAAI,GAAG,MAAM,MAAM,eAAe,KAAM;AAAA,YAC1C,EAAE,IAAI,GAAG,MAAM,MAAM,eAAe,MAAO;AAAA,YAC3C,EAAE,IAAI,GAAG,MAAM,MAAM,eAAe,MAAO;AAAA,YAC3C,EAAE,IAAI,GAAG,MAAM,QAAQ,eAAe,KAAM;AAAA,YAC5C,EAAE,IAAI,GAAG,MAAM,QAAQ,eAAe,KAAM;AAAA,YAC5C,EAAE,IAAI,IAAI,MAAM,MAAM,eAAe,KAAM;AAAA,UAC5C;AAAA,QACX,CAAS;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AACH;AAGK,MAAC,UAAU;AAAA,EACd,MAAM,MAAM;AACVA,kBAAA,MAAA,MAAA,OAAA,uBAAY,YAAY,IAAI;AAG5B,UAAM,YAAY;AAAA,MAChB,UAAU,KAAK;AAAA,MACf,UAAU,KAAK;AAAA,IACrB;AAEI,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,oBAAAA,MAAI,QAAQ;AAAA,QACV,KAAK,GAAG,QAAQ;AAAA,QAChB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,gBAAgB;AAAA,QACjB;AAAA,QACD,iBAAiB;AAAA,QACjB,SAAS,CAAC,QAAQ;AAChBA,wBAAY,MAAA,MAAA,OAAA,uBAAA,SAAS,GAAG;AACxB,cAAI,IAAI,eAAe,KAAK;AAC1B,oBAAQ,IAAI,IAAI;AAAA,UAC5B,OAAiB;AAELA,gCAAc,MAAA,SAAA,uBAAA,SAAS,IAAI,YAAY,IAAI,IAAI;AAC/C,mBAAO;AAAA,cACL,QAAQ,SAAS,IAAI,UAAU;AAAA,cAC/B,YAAY,IAAI;AAAA,cAChB,MAAM,IAAI;AAAA,YACxB,CAAa;AAAA,UACF;AAAA,QACF;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAA,MAAA,MAAA,SAAA,uBAAc,WAAW,GAAG;AAC5B,iBAAO;AAAA,YACL,QAAQ,IAAI,UAAU;AAAA,YACtB,KAAK,GAAG,QAAQ;AAAA,YAChB,QAAQ;AAAA,YACR,MAAM;AAAA,UAClB,CAAW;AAAA,QACF;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA,EACD,cAAc;AACZ,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AAAA,EACD,iBAAiB,MAAM;AACrB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,cAAQ;AAAA,QACN,KAAK;AAAA,QACL,QAAQ;AAAA,QACd,MAAK;AAAA,MACP,CAAO,EACA,KAAK,SAAO;AACX,gBAAQ,GAAG;AAAA,MACnB,CAAO,EACA,MAAM,SAAO;AACZA,sBAAa,MAAA,MAAA,QAAA,uBAAA,qBAAqB,GAAG;AAErC,gBAAQ;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM,CACL;AAAA,QACX,CAAS;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA,EACD,eAAe,MAAM;AACnB,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,eAAe,MAAM;AACnB,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,oBAAoB;AAClB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,cAAQ;AAAA,QACN,KAAK;AAAA,QACL,QAAQ;AAAA,MAChB,CAAO,EACA,KAAK,SAAO;AACXA,sBAAA,MAAA,MAAA,OAAA,uBAAY,aAAa,GAAG;AAC5B,gBAAQ,GAAG;AAAA,MACnB,CAAO,EACA,MAAM,SAAO;AACZA,sBAAa,MAAA,MAAA,QAAA,uBAAA,aAAa,GAAG;AAAA,MACrC,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AACH;AAGK,MAAC,eAAe;AAAA,EACnB,QAAQ,QAAQ;AAEd,UAAM,YAAY;AAAA,MAChB,OAAM,iCAAQ,SAAQ;AAAA,MACtB,WAAU,iCAAQ,aAAY;AAAA,IACpC;AAGI,QAAI,UAAU,OAAO,MAAM;AACzB,gBAAU,OAAO,OAAO;AAAA,IACzB;AAGD,QAAI,UAAU,OAAO,QAAQ;AAC3B,gBAAU,SAAS,OAAO;AAAA,IAC3B;AAGD,cAAU,OAAM,iCAAQ,QAAOA,cAAAA,MAAI,eAAe,QAAQ;AAG1D,QAAI,UAAU,OAAO,MAAM;AACzB,gBAAU,OAAO,OAAO;AAAA,IACzB;AAGD,cAAU,cAAa,iCAAQ,eAAcA,cAAAA,MAAI,eAAe,YAAY;AAE5EA,kBAAY,MAAA,MAAA,OAAA,uBAAA,aAAa,SAAS;AAClC,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,IACZ,CAAK;AAAA,EACF;AAAA,EACD,UAAU,IAAI;AACZ,WAAO,QAAQ;AAAA,MACb,KAAK,0BAA0B,EAAE;AAAA,MACjC,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AAAA,EACD,aAAa,MAAM;AACjB,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA,EACD,cAAc,MAAM;AAClB,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA,EACD,cAAc,MAAM;AAClB,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA,EAED,qBAAqB,QAAQ,UAAU;AACrC,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,QACJ,KAAK;AAAA,QACL,MAAM;AAAA,QACN,YAAYA,cAAAA,MAAI,eAAe,YAAY;AAAA,MAC5C;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA,EAED,oBAAoB;AAClB,UAAM,YAAY,CAAA;AAClB,cAAU,MAAMA,cAAAA,MAAI,eAAe,QAAQ;AAC3C,cAAU,aAAaA,cAAAA,MAAI,eAAe,YAAY;AAEtD,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,IACZ,CAAK;AAAA,EACF;AACH;AAGK,MAAC,YAAY;AAAA;AAAA,EAEhB,WAAW,MAAM;AACf,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,YAAY,QAAQ;AAClB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,cAAQ;AAAA,QACN,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,MAAM;AAAA,UACJ,OAAM,iCAAQ,SAAQ;AAAA,UACtB,SAAQ,iCAAQ,WAAU;AAAA,UAC1B,cAAa,iCAAQ,SAAQ;AAAA,UAC7B,cAAa,iCAAQ,cAAa;AAAA,UACxC,OAAM,iCAAQ,SAAQ;AAAA,UACtB,WAAU,iCAAQ,aAAY;AAAA,UAC9B,YAAY,OAAO;AAAA;AAAA,QACd;AAAA,MACT,CAAO,EACA,KAAK,SAAO;AACX,gBAAQ,GAAG;AAAA,MACnB,CAAO,EACA,MAAM,SAAO;AACZA,sBAAa,MAAA,MAAA,QAAA,uBAAA,qBAAqB,GAAG;AAAA,MAC7C,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,cAAc,IAAI;AAChB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,cAAQ;AAAA,QACN,KAAK,sBAAsB,EAAE;AAAA,QAC7B,QAAQ;AAAA,MAChB,CAAO,EACA,KAAK,SAAO;AACX,gBAAQ,GAAG;AAAA,MACnB,CAAO,EACA,MAAM,SAAO;AACZA,sBAAa,MAAA,MAAA,QAAA,uBAAA,qBAAqB,GAAG;AAAA,MAC7C,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,WAAW,IAAI,MAAM;AACnB,WAAO,QAAQ;AAAA,MACb,KAAK,sBAAsB,EAAE;AAAA,MAC7B,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,WAAW,IAAI;AACb,WAAO,QAAQ;AAAA,MACb,KAAK,sBAAsB,EAAE;AAAA,MAC7B,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,YAAY,QAAQ;AAClB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEtC,UAAI,UAAU,OAAO,QAAQ;AAC3B,gBAAQ;AAAA,UACN,KAAK,sBAAsB,OAAO,MAAM;AAAA,UACxC,QAAQ;AAAA,QAClB,CAAS,EACA,KAAK,SAAO;AACX,kBAAQ,GAAG;AAAA,QACrB,CAAS,EACA,MAAM,SAAO;AACZA,wBAAA,MAAA,MAAA,QAAA,uBAAa,uBAAuB,GAAG;AAAA,QACjD,CAAS;AAAA,MAEF;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,oBAAoB,QAAQ;AAC1B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI,CAAC,UAAU,CAAC,OAAO,QAAQ;AAC7B,gBAAQ;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM,CAAE;AAAA,QAClB,CAAS;AACD;AAAA,MACD;AAED,cAAQ;AAAA,QACN,KAAK,sBAAsB,OAAO,MAAM;AAAA,QACxC,QAAQ;AAAA,MAChB,CAAO,EACA,KAAK,SAAO;AACX,gBAAQ,GAAG;AAAA,MACnB,CAAO,EACA,MAAM,SAAO;AACZA,sBAAa,MAAA,MAAA,QAAA,uBAAA,uBAAuB,GAAG;AAEvC,gBAAQ;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA,YACJ;AAAA,cACE,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,UAAU;AAAA,cACV,OAAO;AAAA,cACP,QAAQ,CAAC,QAAQ,MAAM;AAAA,YACxB;AAAA,YACD;AAAA,cACE,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,UAAU;AAAA,cACV,OAAO;AAAA,cACP,QAAQ,CAAC,QAAQ,MAAM;AAAA,YACxB;AAAA,YACD;AAAA,cACE,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,UAAU;AAAA,cACV,OAAO;AAAA,cACP,QAAQ,CAAC,QAAQ,QAAQ,MAAM;AAAA,YAChC;AAAA,UACF;AAAA,QACX,CAAS;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,sBAAsB,QAAQ;AAC5B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,cAAQ;AAAA,QACN,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,MAAM;AAAA,MACd,CAAO,EACA,KAAK,SAAO;AACX,gBAAQ,GAAG;AAAA,MACnB,CAAO,EACA,MAAM,SAAO;AACZA,sBAAa,MAAA,MAAA,QAAA,uBAAA,qBAAqB,GAAG;AAErC,gBAAQ;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA,YACJ;AAAA,cACE,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,MAAM;AAAA,cACN,UAAU;AAAA,YACX;AAAA,YACD;AAAA,cACE,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,MAAM;AAAA,cACN,UAAU;AAAA,YACX;AAAA,YACD;AAAA,cACE,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,MAAM;AAAA,cACN,UAAU;AAAA,YACX;AAAA,UACF;AAAA,QACX,CAAS;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,WAAW,MAAM;AACf,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,UAAU,IAAI;AACZ,WAAO,QAAQ;AAAA,MACb,KAAK,sBAAsB,EAAE;AAAA,MAC7B,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,aAAa,IAAI,MAAM;AACrB,WAAO,QAAQ;AAAA,MACb,KAAK,sBAAsB,EAAE;AAAA,MAC7B,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,mBAAmB,gBAAgB,MAAM;AACvC,WAAO,QAAQ;AAAA,MACb,KAAK,wBAAwB,cAAc;AAAA,MAC3C,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,iBAAiB,QAAQ;AAEvB,UAAM,cAAc;AAAA,MAClB,SAAQ,iCAAQ,WAAU;AAAA,MAC1B,YAAW,iCAAQ,cAAa;AAAA,MAChC,UAAS,iCAAQ,YAAW;AAAA,MAC5B,OAAM,iCAAQ,SAAQ;AAAA,MACtB,WAAU,iCAAQ,aAAY;AAAA,IACpC;AAEC,gBAAY,aAAWA,cAAAA,MAAI,eAAe,QAAQ;AAClD,gBAAY,OAAKA,cAAAA,MAAI,eAAe,UAAU;AAG3C,WAAO,KAAK,WAAW,EAAE,QAAQ,SAAO;AACtC,UAAI,YAAY,GAAG,MAAM,QAAW;AAClC,eAAO,YAAY,GAAG;AAAA,MACvB;AAAA,IACP,CAAK;AAED,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,cAAQ;AAAA,QACN,KAAK;AAAA,QACL,QAAQ;AAAA;AAAA,QACR,MAAM;AAAA,MACd,CAAO,EACA,KAAK,SAAO;AACX,gBAAQ,GAAG;AAAA,MACnB,CAAO,EACA,MAAM,SAAO;AACZA,sBAAa,MAAA,MAAA,QAAA,uBAAA,qBAAqB,GAAG;AAAA,MAC7C,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,sBAAsB,UAAU;AAC9B,WAAO,QAAQ;AAAA,MACb,KAAK,wBAAwB,QAAQ;AAAA,MACrC,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,mBAAmB;AACjB,WAAO;AAAA,MACL,EAAE,OAAO,MAAM,OAAO,QAAS;AAAA,MAC/B,EAAE,OAAO,MAAM,OAAO,SAAU;AAAA,MAChC,EAAE,OAAO,MAAM,OAAO,UAAW;AAAA,MACjC,EAAE,OAAO,OAAO,OAAO,SAAU;AAAA,IACvC;AAAA,EACG;AAAA;AAAA,EAGD,gBAAgB;AACd,WAAO;AAAA,MACL,EAAE,OAAO,QAAQ,OAAO,SAAU;AAAA,MAClC,EAAE,OAAO,QAAQ,OAAO,YAAa;AAAA,MACrC,EAAE,OAAO,QAAQ,OAAO,YAAa;AAAA,IAC3C;AAAA,EACG;AAAA;AAAA,EAGD,iBAAiB,QAAQ;AACvB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,cAAQ;AAAA,QACN,KAAK,sBAAsB,MAAM;AAAA,QACjC,QAAQ;AAAA,MAChB,CAAO,EACA,KAAK,SAAO;AACX,gBAAQ,GAAG;AAAA,MACnB,CAAO,EACA,MAAM,SAAO;AACZA,sBAAA,MAAA,MAAA,QAAA,uBAAa,wBAAwB,GAAG;AAExC,gBAAQ;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM,CAEL;AAAA,QACX,CAAS;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,mBAAmB,MAAM;AACvB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,cAAQ;AAAA,QACN,KAAK;AAAA,QACL,QAAQ;AAAA,QACR;AAAA,MACR,CAAO,EACA,KAAK,SAAO;AACX,gBAAQ,GAAG;AAAA,MACnB,CAAO,EACA,MAAM,SAAO;AACZA,sBAAA,MAAA,MAAA,QAAA,uBAAa,YAAY,GAAG;AAE5B,gBAAQ;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,QACnB,CAAS;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,eAAe,QAAQ;AACrB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,cAAQ;AAAA,QACN,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,MAAM;AAAA,MACd,CAAO,EACA,KAAK,SAAO;AACX,gBAAQ,GAAG;AAAA,MACnB,CAAO,EACA,MAAM,SAAO;AACZA,sBAAa,MAAA,MAAA,QAAA,uBAAA,qBAAqB,GAAG;AAErC,gBAAQ;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM,CAAE;AAAA,QAClB,CAAS;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,uBAAuB,QAAQ,MAAK;AAClC,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,QACJ,KAAK;AAAA,QACL;AAAA,MACD;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,wBAAwB,QAAQ,GAAG;AACjC,UAAM,SAASA,cAAAA,MAAI,eAAe,QAAQ;AAC1C,UAAM,OAAOA,cAAAA,MAAI,eAAe,UAAU;AAE1C,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,QACJ,KAAK;AAAA,QACL;AAAA,QACA;AAAA,MACD;AAAA,IACP,CAAK;AAAA,EACF;AACH;AAGK,MAAC,YAAY;AAAA,EAChB,QAAQ,QAAQ;AAEd,UAAM,cAAc;AAAA,MAClB,OAAM,iCAAQ,SAAQ;AAAA,MACtB,WAAU,iCAAQ,aAAY;AAAA,MAC9B,SAAQ,iCAAQ,WAAU;AAAA,MAC1B,OAAM,iCAAQ,SAAQ;AAAA,MACtB,OAAM,iCAAQ,SAAQ;AAAA,MACtB,UAAS,iCAAQ,YAAW;AAAA,MAC5B,SAAQ,iCAAQ,WAAU;AAAA,MAC1B,YAAW,iCAAQ,cAAa;AAAA,IACtC;AAEI,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,IACZ,CAAK;AAAA,EACF;AAAA,EACD,UAAU,IAAI;AACZ,WAAO,QAAQ;AAAA,MACb,KAAK,gBAAgB,EAAE;AAAA,MACvB,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AAAA,EACD,WAAW;AACT,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AAAA;AAAA,EAED,uBAAuB,YAAY;AACjC,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,cAAQ;AAAA,QACN,KAAK,0BAA0B,UAAU;AAAA,QACzC,QAAQ;AAAA,MAChB,CAAO,EACA,KAAK,SAAO;AACX,gBAAQ,GAAG;AAAA,MACnB,CAAO,EACA,MAAM,SAAO;AACZA,4BAAA,MAAA,QAAA,uBAAa,YAAY,UAAU,mBAAmB,GAAG;AAEzD,gBAAQ;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA,YACJ,EAAE,IAAI,QAAQ,MAAM,QAAQ,MAAM,QAAQ,cAAc,cAAe;AAAA,YACvE,EAAE,IAAI,QAAQ,MAAM,QAAQ,MAAM,QAAQ,cAAc,cAAe;AAAA,YACvE,EAAE,IAAI,QAAQ,MAAM,SAAS,MAAM,SAAS,cAAc,cAAe;AAAA,YACzE,EAAE,IAAI,QAAQ,MAAM,SAAS,MAAM,SAAS,cAAc,cAAe;AAAA,YACzE,EAAE,IAAI,QAAQ,MAAM,OAAO,MAAM,iBAAiB,cAAc,cAAe;AAAA,UAChF;AAAA,QACX,CAAS;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,yBAAyB,UAAU;AACjC,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,cAAQ;AAAA,QACN,KAAK,6BAA6B,QAAQ;AAAA,QAC1C,QAAQ;AAAA,MAChB,CAAO,EACA,KAAK,SAAO;AACX,gBAAQ,GAAG;AAAA,MACnB,CAAO,EACA,MAAM,SAAO;AACZA,4BAAa,MAAA,QAAA,uBAAA,WAAW,QAAQ,kBAAkB,GAAG;AAAA,MAC7D,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AACH;AAGK,MAAC,WAAW;AAAA,EACf,YAAY,MAAM;AAChB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,cAAQ;AAAA,QACN,KAAK;AAAA,QACL,QAAQ;AAAA,QACR;AAAA,MACR,CAAO,EACA,KAAK,SAAO;AACX,YAAI,IAAI,SAAS,KAAK;AACpB,kBAAQ,GAAG;AAAA,QACrB,OAAe;AAEL,gBAAM,WAAW,IAAI,WAAW;AAChC,iBAAO,IAAI,MAAM,QAAQ,CAAC;AAAA,QAC3B;AAAA,MACT,CAAO,EACA,MAAM,SAAO;AAEZA,sBAAA,MAAA,MAAA,SAAA,uBAAc,aAAa,GAAG;AAG9B,YAAI,WAAW;AAEf,YAAI,IAAI,QAAQ,OAAO,IAAI,SAAS,UAAU;AAE5C,cAAI,IAAI,KAAK,SAAS;AACpB,uBAAW,IAAI,KAAK;AAAA,UAChC,WAAqB,IAAI,KAAK,OAAO;AACzB,uBAAW,IAAI,KAAK;AAAA,UACrB;AAAA,QACX,WAAmB,IAAI,QAAQ;AACrB,qBAAW,IAAI;AAAA,QAChB;AAED,eAAO,IAAI,MAAM,QAAQ,CAAC;AAAA,MAClC,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA,EACD,aAAa,QAAQ;AAEpB,UAAM,cAAc;AAAA,MAClB,QAAQ,iCAAQ;AAAA,MAChB,MAAM,iCAAQ;AAAA,MACd,OAAM,iCAAQ,SAAQ;AAAA,MACtB,WAAU,iCAAQ,aAAY;AAAA,IACnC;AAGI,QAAI,UAAU,OAAO,YAAY;AAC/B,kBAAY,aAAa,OAAO;AAAA,IACjC;AAGD,WAAO,KAAK,WAAW,EAAE,QAAQ,SAAO;AACtC,UAAI,YAAY,GAAG,MAAM,QAAW;AAClC,eAAO,YAAY,GAAG;AAAA,MACvB;AAAA,IACP,CAAK;AAED,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,IACZ,CAAK;AAAA,EACF;AAAA,EACD,eAAe,IAAI;AACjB,WAAO,QAAQ;AAAA,MACb,KAAK,sBAAsB,EAAE;AAAA,MAC7B,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AAAA,EACD,kBAAkB,aAAa;AAE9B,WAAO,QAAQ;AAAA;AAAA,MACd,KAAK;AAAA;AAAA,MACL,QAAQ;AAAA;AAAA,MACR,MAAM;AAAA,QACJ,UAAU,YAAY;AAAA;AAAA,QACtB,aAAa,YAAY;AAAA;AAAA,QACzB,cAAc,YAAY;AAAA;AAAA,QAC1B,gBAAgB,YAAY;AAAA,QAC/B,cAAc,YAAY;AAAA;AAAA,MACxB;AAAA,IACL,CAAG;AAAA,EACH;AAAA;AAAA,EAGE,iBAAiB,QAAQ,UAAU;AACjC,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,QACJ,KAAK;AAAA,QACL,MAAM;AAAA,QACN,YAAYA,cAAAA,MAAI,eAAe,YAAY;AAAA,MAC5C;AAAA,IACP,CAAK;AAAA,EACF;AACH;AAGK,MAAC,iBAAiB;AAAA;AAAA,EAErB,wBAAwB;AACtB,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,mBAAmB,QAAQ;AACzB,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,IACZ,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,kBAAkB,MAAM;AACtB,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,QACJ,YAAY,KAAK;AAAA,QACjB,YAAY,KAAK;AAAA,QACjB,QAAQ,KAAK;AAAA,QACb,QAAQ,KAAK;AAAA,QACb,YAAY,KAAK;AAAA,QACjB,aAAa,KAAK;AAAA,QAClB,WAAW,KAAK;AAAA,QAChB,UAAU,KAAK;AAAA,QACf,QAAQ,KAAK;AAAA,QACb,QAAQ,KAAK;AAAA,QACb,YAAYA,cAAAA,MAAI,eAAe,QAAQ;AAAA,MACxC;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,qBAAqB,IAAI;AACvB,WAAO,QAAQ;AAAA,MACb,KAAK,qBAAqB,EAAE;AAAA,MAC5B,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AACH;AAGK,MAAC,UAAU;AAAA;AAAA,EAEd,cAAc;AACZ,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AAAA;AAAA,EAED,gBAAgB,QAAQ,GAAG;AAC5B,UAAM,YAAY,CAAA;AACf,cAAU,QAAM;AACnB,cAAU,MAAIA,cAAAA,MAAI,eAAe,QAAQ;AACzC,cAAU,OAAKA,cAAAA,MAAI,eAAe,UAAU;AAC5C,cAAU,aAAWA,cAAAA,MAAI,eAAe,YAAY;AACjD,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,IACZ,CAAK;AAAA,EACF;AAAA;AAAA,EAED,wBAAwB;AACtB,WAAO,eAAe;EACvB;AAAA;AAAA,EAGD,kBAAkB,MAAM;AACtB,WAAO,eAAe,kBAAkB,IAAI;AAAA,EAC7C;AAAA;AAAA,EAGD,mBAAmB;AACjB,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,wBAAwB;AACtB,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,sBAAsB;AACpB,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AACH;AAGK,MAAC,cAAc;AAAA;AAAA,EAElB,UAAU;AACR,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,YAAY,QAAQ;AAClB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEtC,YAAM,aAAaA,cAAAA,MAAI,eAAe,YAAY;AAClDA,+DAAY,qBAAqB,UAAU;AAG3C,WAAK,QAAS,EACX,KAAK,SAAO;AACX,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAEhC,cAAI,cAAc,eAAe,OAAO,eAAe,GAAG;AAExD,kBAAM,kBAAkB,WAAW,MAAM,GAAG,EAAE,IAAI,QAAM,GAAG,KAAI,CAAE;AAIjE,kBAAM,gBAAgB,IAAI,KAAK;AAAA,cAAO,UACpC,gBAAgB,SAAS,OAAO,KAAK,EAAE,CAAC,KAAK,gBAAgB,SAAS,OAAO,KAAK,EAAE,CAAC;AAAA,YACrG;AAEc,gBAAI,iBAAiB,cAAc,SAAS,GAAG;AAE7C,sBAAQ;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,MAAM;AAAA,cACxB,CAAiB;AAAA,YACjB,OAAqB;AAELA,4BAAAA,MAAA,MAAA,OAAA,wBAAY,qBAAqB;AACjC,sBAAQ;AAAA,gBACN,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,MAAM,IAAI;AAAA,cAC5B,CAAiB;AAAA,YACF;AAAA,UACf,OAAmB;AAELA,0BAAAA,2CAAY,uBAAuB;AACnC,oBAAQ;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,MAAM,IAAI;AAAA,YAC1B,CAAe;AAAA,UACF;AAAA,QACb,OAAiB;AAEL,kBAAQ,GAAG;AAAA,QACZ;AAAA,MACX,CAAS,EACA,MAAM,SAAO;AACZ,eAAO,GAAG;AAAA,MACpB,CAAS;AAAA,IACT,CAAK;AAAA,EACF;AACH;AAGY,MAAC,uBAAuB;AAAA,EAClC,OAAO,MAAM;AACX,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AACH;AAGK,MAAC,oBAAoB;AAAA;AAAA,EAExB,QAAQ,QAAQ;AACd,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,cAAQ;AAAA,QACN,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,MAAM;AAAA,MACd,CAAO,EACA,KAAK,SAAO;AACX,gBAAQ,GAAG;AAAA,MACnB,CAAO,EACA,MAAM,SAAO;AACZA,sBAAa,MAAA,MAAA,QAAA,wBAAA,oBAAoB,GAAG;AAAA,MAC5C,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,UAAU,IAAI;AACZ,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,cAAQ;AAAA,QACN,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,MAAM,EAAE,GAAI;AAAA,MACpB,CAAO,EACA,KAAK,SAAO;AACX,gBAAQ,GAAG;AAAA,MACnB,CAAO,EACA,MAAM,SAAO;AACZA,sBAAa,MAAA,MAAA,QAAA,wBAAA,oBAAoB,GAAG;AAEpC,gBAAQ;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA,YACJ,YAAY;AAAA,cACV;AAAA,cACA,MAAM;AAAA,cACN,UAAU;AAAA,cACV,gBAAgB;AAAA,cAChB,QAAQ;AAAA,cACR,UAAU;AAAA,cACV,WAAW;AAAA,cACX,WAAW;AAAA,cACX,cAAc;AAAA,cACd,aAAa;AAAA,cACb,aAAa;AAAA,YACd;AAAA,YACD,eAAe;AAAA,cACb,gBAAgB;AAAA,gBACd,aAAa;AAAA,gBACb,aAAa;AAAA,gBACb,iBAAiB;AAAA,gBACjB,iBAAiB;AAAA,gBACjB,WAAW;AAAA,gBACX,OAAO;AAAA,cACR;AAAA,cACD,kBAAkB;AAAA,gBAChB,aAAa;AAAA,gBACb,aAAa;AAAA,gBACb,iBAAiB;AAAA,gBACjB,iBAAiB;AAAA,gBACjB,WAAW;AAAA,cACZ;AAAA,cACD,kBAAkB;AAAA,gBAChB,OAAO;AAAA,kBACL;AAAA,oBACE,IAAI;AAAA,oBACJ,MAAM;AAAA,oBACN,QAAQ;AAAA,oBACR,WAAW;AAAA,oBACX,SAAS;AAAA,oBACT,OAAO;AAAA,kBACR;AAAA,kBACD;AAAA,oBACE,IAAI;AAAA,oBACJ,MAAM;AAAA,oBACN,QAAQ;AAAA,oBACR,WAAW;AAAA,oBACX,SAAS;AAAA,oBACT,OAAO;AAAA,kBACR;AAAA,gBACF;AAAA,gBACD,QAAQ;AAAA,kBACN;AAAA,oBACE,IAAI;AAAA,oBACJ,MAAM;AAAA,oBACN,SAAS;AAAA,oBACT,QAAQ;AAAA,kBACT;AAAA,kBACD;AAAA,oBACE,IAAI;AAAA,oBACJ,MAAM;AAAA,oBACN,SAAS;AAAA,oBACT,QAAQ;AAAA,kBACT;AAAA,gBACF;AAAA,cACF;AAAA,cACD,kBAAkB;AAAA,YACnB;AAAA,UACF;AAAA,QACX,CAAS;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,cAAc,QAAQ;AACpB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,cAAQ;AAAA,QACN,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,MAAM;AAAA,MACd,CAAO,EACA,KAAK,SAAO;AACX,gBAAQ,GAAG;AAAA,MACnB,CAAO,EACA,MAAM,SAAO;AACZA,sBAAa,MAAA,MAAA,QAAA,wBAAA,eAAe,GAAG;AAE/B,YAAI,IAAI,eAAe,KAAK;AAC1B,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,SAAS;AAAA,YACT,SAAS;AAAA,UACrB,CAAW;AAAA,QACX,OAAe;AACL,iBAAO,GAAG;AAAA,QACX;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,oBAAoB,OAAO;AACzB,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM,EAAE,QAAQ,MAAO;AAAA,IAC7B,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,eAAe,QAAQ;AACrB,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,IACZ,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,aAAa,QAAQ;AACnB,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,IACZ,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,aAAa,QAAQ;AACnB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,cAAQ;AAAA,QACN,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,MAAM;AAAA,MACd,CAAO,EACA,KAAK,SAAO;AACX,gBAAQ,GAAG;AAAA,MACnB,CAAO,EACA,MAAM,SAAO;AACZA,sBAAa,MAAA,MAAA,QAAA,wBAAA,eAAe,GAAG;AAE/B,gBAAQ;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA,YACJ,OAAO;AAAA,YACP,MAAM;AAAA,cACJ;AAAA,gBACE,IAAI;AAAA,gBACJ,QAAQ,OAAO,UAAU;AAAA,gBACzB,UAAU;AAAA,gBACV,YAAY;AAAA,gBACZ,YAAY;AAAA,gBACZ,OAAO;AAAA,gBACP,OAAO;AAAA,gBACP,WAAW;AAAA,gBACX,QAAQ;AAAA,gBACR,YAAY;AAAA,gBACZ,UAAU;AAAA,gBACV,UAAU;AAAA,cACX;AAAA,cACD;AAAA,gBACE,IAAI;AAAA,gBACJ,QAAQ,OAAO,UAAU;AAAA,gBACzB,UAAU;AAAA,gBACV,YAAY;AAAA,gBACZ,YAAY;AAAA,gBACZ,OAAO;AAAA,gBACP,OAAO;AAAA,gBACP,WAAW;AAAA,gBACX,QAAQ;AAAA,gBACR,YAAY;AAAA,gBACZ,UAAU;AAAA,gBACV,UAAU;AAAA,cACX;AAAA,YACF;AAAA,UACF;AAAA,QACX,CAAS;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,eAAe,IAAI;AACjB,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM,EAAE,GAAI;AAAA,IAClB,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,cAAc,QAAQ;AACpB,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,IACZ,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,gBAAgB;AACd,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AACH;AAGK,MAAC,gBAAgB;AAAA;AAAA,EAEpB,YAAY,MAAM;AAChB,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,WAAW,SAAS,IAAI;AACtB,UAAM,EAAE,QAAQ,MAAM,OAAO,KAAK,QAAQ,WAAW,SAAS,QAAS,IAAG;AAC1E,QAAI,MAAM;AAGV,UAAM,cAAc,CAAA;AACpB,QAAI;AAAQ,kBAAY,KAAK,WAAW,MAAM,EAAE;AAChD,QAAI;AAAM,kBAAY,KAAK,QAAQ,IAAI,EAAE;AACzC,QAAI;AAAO,kBAAY,KAAK,SAAS,KAAK,EAAE;AAC5C,QAAI;AAAK,kBAAY,KAAK,OAAO,GAAG,EAAE;AACtC,QAAI;AAAQ,kBAAY,KAAK,UAAU,MAAM,EAAE;AAC/C,QAAI;AAAW,kBAAY,KAAK,aAAa,SAAS,EAAE;AACxD,QAAI;AAAS,kBAAY,KAAK,WAAW,OAAO,EAAE;AAClD,QAAI;AAAS,kBAAY,KAAK,WAAW,mBAAmB,OAAO,CAAC,EAAE;AAEtE,QAAI,YAAY,SAAS,GAAG;AAC1B,aAAO,IAAI,YAAY,KAAK,GAAG,CAAC;AAAA,IACjC;AAED,WAAO,QAAQ;AAAA,MACb;AAAA,MACA,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,SAAS,SAAS,IAAI;AACpB,UAAM,EAAE,QAAQ,MAAM,OAAO,IAAG,IAAK;AACrC,QAAI,MAAM;AAGV,UAAM,cAAc,CAAA;AACpB,QAAI;AAAQ,kBAAY,KAAK,WAAW,MAAM,EAAE;AAChD,QAAI;AAAM,kBAAY,KAAK,QAAQ,IAAI,EAAE;AACzC,QAAI;AAAO,kBAAY,KAAK,SAAS,KAAK,EAAE;AAC5C,QAAI;AAAK,kBAAY,KAAK,OAAO,GAAG,EAAE;AAEtC,QAAI,YAAY,SAAS,GAAG;AAC1B,aAAO,IAAI,YAAY,KAAK,GAAG,CAAC;AAAA,IACjC;AAED,WAAO,QAAQ;AAAA,MACb;AAAA,MACA,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,iBACA;AACC,UAAM,YAAY,CACrB;AACG,cAAU,UAAUA,cAAAA,MAAI,eAAe,QAAQ;AAC5C,WAAO,QAAQ;AAAA,MACf,KAAK;AAAA,MACL,QAAQ;AAAA,MACX,MAAK;AAAA,IACR,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,iBAAiB,OAAO,GAAG;AAC1B,UAAM,YAAY;AAAA,MACjB;AAAA,IACJ;AACK,cAAU,UAAUA,cAAAA,MAAI,eAAe,QAAQ;AAChD,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,IACZ,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,gBAAgB;AACd,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,iBAAiB,aAAa,cAAc;AAC1C,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,QACJ,eAAe;AAAA,QACf,gBAAgB;AAAA,MACjB;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,oBAAoB,MAAM;AACxB,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,4BAA4B,MAAM;AAChC,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,mBAAmB,SAAS,IAAI;AAC9B,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,IACZ,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,mBAAmB,SAAS,IAAI;AAC9B,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,IACZ,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,6BAA6B,MAAM;AACjC,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR;AAAA,IACN,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,cAAc;AACZ,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,iBAAiB,SAAS,IAAI;AAC5B,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,IACZ,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,uBAAuB,MAAM;AAC3BA,kBAAA,MAAA,MAAA,OAAA,wBAAY,aAAa,IAAI;AAG7B,QAAI,CAAC,KAAK,UAAU,CAAC,KAAK,aAAa,CAAC,KAAK,UAAU;AACrDA,oBAAc,MAAA,MAAA,SAAA,wBAAA,eAAe,IAAI;AACjC,aAAO,QAAQ,OAAO,EAAC,QAAQ,SAAQ,CAAC;AAAA,IACzC;AAGD,UAAM,MAAM,0CAA0C,KAAK,MAAM,eAAe,KAAK,MAAM,cAAc,KAAK,SAAS,aAAa,KAAK,QAAQ;AAGjJ,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEtC,YAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,YAAM,SAAS;AAAA,QACb,gBAAgB;AAAA,MACxB;AAGM,UAAI,OAAO;AACT,eAAO,gBAAgB,UAAU,KAAK;AAAA,MACvC;AAEDA,oBAAAA,MAAI,QAAQ;AAAA,QACV,KAAK,WAAW;AAAA,QAChB,QAAQ;AAAA,QACR;AAAA,QACA,SAAS,CAAC,QAAQ;AAChBA,wBAAY,MAAA,MAAA,OAAA,wBAAA,aAAa,GAAG;AAG5B,eAAK,IAAI,eAAe,OAAO,IAAI,eAAe,QAC9C,IAAI,QAAQ,IAAI,KAAK,SAAS,KAAK;AACrC,oBAAQ,IAAI,IAAI;AAAA,UAC5B,OAAiB;AACL,mBAAO;AAAA,cACL,QAAQ,SAAS,IAAI,UAAU;AAAA,cAC/B,YAAY,IAAI;AAAA,cAChB,MAAM,IAAI;AAAA,YACxB,CAAa;AAAA,UACF;AAAA,QACF;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAA,MAAA,MAAA,SAAA,wBAAc,eAAe,GAAG;AAChC,iBAAO,GAAG;AAAA,QACX;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AACH;AAGK,MAAC,WAAW;AAAA;AAAA,EAEf,aAAa,QAAQ,MAAM;AACzBA,wBAAA,MAAA,OAAA,wBAAY,oBAAoB,MAAM,UAAU,IAAI,EAAE;AACtD,WAAO,QAAQ;AAAA;AAAA,MACb,KAAK;AAAA;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,QACJ,KAAK;AAAA,QACL;AAAA,QACA,YAAYA,cAAAA,MAAI,eAAe,YAAY;AAAA,MAC5C;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,kBAAkB,SAAS,QAAQ;AACjCA,wBAAY,MAAA,OAAA,wBAAA,wBAAwB,OAAO,YAAY,MAAM,EAAE;AAC/D,WAAO,QAAQ;AAAA;AAAA,MACb,KAAK;AAAA;AAAA,MACL,QAAQ;AAAA;AAAA,MACR,MAAM;AAAA,QACJ,UAAU;AAAA;AAAA,QACV,cAAc;AAAA;AAAA,MACf;AAAA,IACP,CAAK;AAAA,EACF;AACH;AAKK,MAAC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMd,oBAAoB,QAAQ;AAC1B,WAAO,QAAQ;AAAA,MACb,KAAK,kBAAkB,MAAM;AAAA,MAC7B,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AACH;AAwBY,MAAC,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,kBAAkB;AAChB,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,IACd,CAAK;AAAA,EACF;AACH;;;;;;;;;;;;;;;;;;;"}