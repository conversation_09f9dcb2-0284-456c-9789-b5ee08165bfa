{"version": 3, "file": "list.js", "sources": ["pages/fault/list.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZmF1bHQvbGlzdC52dWU"], "sourcesContent": ["<template>\n  <view class=\"fault-list-container\">\n    <!-- Tab 切换 -->\n    <view class=\"tab-container\">\n      <view \n        class=\"tab-item\" \n        :class=\"{ active: activeTab === 'all' }\" \n        @click=\"switchTab('all')\"\n      >\n        全部\n      </view>\n      <view \n        class=\"tab-item\" \n        :class=\"{ active: activeTab === 'pending' }\" \n        @click=\"switchTab('pending')\"\n      >\n        待确认\n      </view>\n      <view \n        class=\"tab-item\" \n        :class=\"{ active: activeTab === 'confirmed' }\" \n        @click=\"switchTab('confirmed')\"\n      >\n        已确认\n      </view>\n      <view \n        class=\"tab-item\" \n        :class=\"{ active: activeTab === 'returned' }\" \n        @click=\"switchTab('returned')\"\n      >\n        已退回\n      </view>\n    </view>\n    \n    <!-- 顶部筛选 -->\n    <view class=\"filter-section\">\n      <view class=\"date-filter\">\n        <picker mode=\"date\" :value=\"filterDate\" @change=\"onDateChange\">\n          <view class=\"date-picker\">\n            <text class=\"date-text\">{{ filterDate || \"选择日期\" }}</text>\n          </view>\n        </picker>\n      </view>\n\n      <button class=\"refresh-button\" @click=\"refreshData\">重置</button>\n    </view>\n\n    <!-- 故障列表 -->\n    <view class=\"fault-list\" v-if=\"faultList.length > 0\">\n      <view\n        class=\"fault-item\"\n        v-for=\"fault in faultList\"\n        :key=\"fault.fault_id\"\n        @click=\"viewFaultDetail(fault.fault_id)\"\n      >\n        <view class=\"fault-header\">\n          <text class=\"heat-unit-name\">{{ fault.heat_unit_name }}</text>\n          <view class=\"fault-status\" :class=\"getFaultStatusClass(fault.fault_status)\">\n            {{ fault.fault_status }}\n          </view>\n        </view>\n\n        <view class=\"fault-content\">\n          <view class=\"fault-desc\">{{ fault.fault_desc }}</view>\n\n          <view class=\"fault-info\">\n            <view class=\"info-item\">\n              <text class=\"info-label\">发生时间：</text>\n              <text class=\"info-value\">{{ formatTimestamp(fault.occur_time) }}</text>\n            </view>\n           \n\t\t\t<view class=\"info-item\">\n\t\t\t  <text class=\"info-label\">故障等级：</text>\n\t\t\t  <text\n\t\t\t    class=\"info-value level-tag\"\n\t\t\t    :class=\"getFaultLevelClass(fault.fault_level)\"\n\t\t\t  >\n\t\t\t    {{ fault.fault_level }}\n\t\t\t  </text>\n\t\t\t</view>\n          </view>\n\n          <view class=\"fault-footer\">\n            <view class=\"report-info\">\n              <text class=\"reporter\">{{ fault.report_user_name }}</text>\n              <text class=\"report-time\">{{ formatTimestamp(fault.report_time) }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 空状态 -->\n    <view class=\"empty-state\" v-if=\"!isLoading && faultList.length === 0\">\n      <image\n        class=\"empty-image\"\n        src=\"/static/images/empty-state.png\"\n        mode=\"aspectFit\"\n      ></image>\n      <text class=\"empty-text\">暂无故障记录</text>\n\t  <button class=\"refresh-button\" @click=\"refreshData\">刷新</button>\n    </view>\n\n    <!-- 加载中 -->\n    <view class=\"loading-container\" v-if=\"isLoading\">\n      <text class=\"loading-text\">加载中...</text>\n    </view>\n\n    <!-- 加载失败 -->\n    <view class=\"error-container\" v-if=\"loadError && !isLoading\">\n      <text class=\"error-text\">加载失败，请重试</text>\n      <button class=\"retry-btn\" @click=\"loadFaultList\">重新加载</button>\n    </view>\n\n    <!-- 底部添加按钮 -->\n    <PermissionCheck permission=\"fault:report:create\">\n      <view class=\"floating-button\" @click=\"navigateToReport\">\n        <text class=\"plus-icon\">+</text>\n      </view>\n    </PermissionCheck>\n  </view>\n</template>\n\n<script>\nimport { faultApi, heatUnitApi } from \"@/utils/api.js\";\nimport PermissionCheck from \"@/components/PermissionCheck.vue\";\n\nexport default {\n  components: {\n    PermissionCheck, // 本地注册组件\n  },\n  data() {\n    // 获取当前日期，格式为 YYYY-MM-DD\n    const now = new Date();\n    const year = now.getFullYear();\n    const month = (now.getMonth() + 1).toString().padStart(2, \"0\");\n    const day = now.getDate().toString().padStart(2, \"0\");\n    const today = `${year}-${month}-${day}`;\n\n    return {\n      faultList: [],\n      isLoading: false,\n      loadError: false,\n\n      // 当前激活的标签页\n      activeTab: 'all', // 'all', 'pending', 'confirmed', 'returned'\n\n      // 筛选条件\n      filterDate: \"\",\n      currentStatus: \"\",\n\n      // 分页相关\n      page: 1,\n      pageSize: 5,\n      hasMore: true,\n    };\n  },\n  onLoad() {\n   this.page = 1;\n   this.faultList = [];\n   this.hasMore = true;\n   // 加载故障列表\n   this.loadFaultList().then(() => {\n     uni.stopPullDownRefresh();\n   });\n  },\n  onShow()\n  {\n     // 检查是否需要刷新列表数据\n     const app = getApp();\n     const needRefresh = app.globalData && app.globalData.refreshFaultList;\n     \n     if (needRefresh) {\n        // 重置页码和列表\n        this.page = 1;\n        this.faultList = [];\n        this.hasMore = true;\n        \n        // 清除刷新标记\n        app.globalData.refreshFaultList = false;\n        \n        // 加载数据\n        this.loadFaultList();\n     } else {\n        // 如果列表为空，则初始化加载\n        if (this.faultList.length === 0) {\n           this.loadFaultList();\n        }\n     }\n  },\n  // 下拉刷新\n  onPullDownRefresh() {\n    this.page = 1;\n    this.faultList = [];\n    this.hasMore = true;\n    this.loadFaultList().then(() => {\n      uni.stopPullDownRefresh();\n    });\n  },\n  // 上拉加载更多\n  onReachBottom() {\n    if (this.hasMore && !this.isLoading) {\n      this.page++;\n      this.loadFaultList(true);\n    }\n  },\n  methods: {\n    // 切换标签页\n    switchTab(tab) {\n      if (this.activeTab !== tab) {\n        this.activeTab = tab;\n        this.page = 1;\n        this.faultList = [];\n        this.hasMore = true;\n        \n        // 根据选中的标签页设置状态过滤\n        switch(tab) {\n          case 'all':\n            this.currentStatus = \"\";\n            break;\n          case 'pending':\n            this.currentStatus = \"待确认\";\n            break;\n          case 'confirmed':\n            this.currentStatus = \"已确认\";\n            break;\n          case 'returned':\n            this.currentStatus = \"已退回\";\n            break;\n        }\n        \n        this.loadFaultList();\n      }\n    },\n    \n    // 格式化时间戳为 yyyy-MM-dd HH:mm:ss 格式\n    formatTimestamp(timestamp) {\n      if (!timestamp) return '';\n      \n      // 如果时间戳是字符串，转换为数字\n      const ts = typeof timestamp === 'string' ? Number(timestamp) : timestamp;\n      \n      // 检查时间戳长度，如果是13位则直接使用，如果是10位则乘以1000\n      const date = new Date(ts.toString().length === 13 ? ts : ts * 1000);\n      \n      const year = date.getFullYear();\n      const month = (date.getMonth() + 1).toString().padStart(2, '0');\n      const day = date.getDate().toString().padStart(2, '0');\n      const hours = date.getHours().toString().padStart(2, '0');\n      const minutes = date.getMinutes().toString().padStart(2, '0');\n      const seconds = date.getSeconds().toString().padStart(2, '0');\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n    },\n\t\n    // 加载故障列表\n    async loadFaultList(isLoadMore = false) {\n      if (this.isLoading) return;\n\n      this.isLoading = true;\n      if (!isLoadMore) {\n        this.loadError = false;\n      }\n\n      try {\n        // 构建查询参数\n        const params = {\n          page: this.page,\n          pageSize: this.pageSize,\n        };\n\n        // 添加日期筛选\n        if (this.filterDate) {\n          params.date = this.filterDate;\n        }\n\n        // 添加状态筛选\n        if (this.currentStatus) {\n          params.status = this.currentStatus;\n        }\n        \n        // 添加用户的项目权限IDs (必须参数)\n        const heatUnitId = uni.getStorageSync('heatUnitId');\n\t\tconsole.log(\"heatUnitId==\",heatUnitId)\n        if (!heatUnitId) {\n          // 如果没有热用户ID权限，显示错误并返回\n          uni.showToast({\n            title: \"无项目访问权限\",\n            icon: \"none\",\n          });\n          this.loadError = true;\n          this.isLoading = false;\n          return;\n        }\n        \n        // 设置热用户ID参数\n        params.heatUnitId = heatUnitId;\n\n        // 调用API获取故障列表\n        const res = await faultApi.getFaultList(params);\n\n        if (res.code === 200) {\n          const { list, total, totalPages } = res.data;\n          \n          if (isLoadMore) {\n            // 加载更多模式：追加数据\n            this.faultList = [...this.faultList, ...list];\n          } else {\n            // 初始加载模式：替换数据\n            this.faultList = list;\n          }\n\n          // 判断是否还有更多数据\n          this.hasMore = this.page < totalPages;\n        } else {\n          this.loadError = true;\n          uni.showToast({\n            title: res.message || \"获取故障列表失败\",\n            icon: \"none\",\n          });\n        }\n      } catch (err) {\n        console.error(\"获取故障列表异常:\", err);\n        this.loadError = true;\n        uni.showToast({\n          title: \"网络异常，请稍后重试\",\n          icon: \"none\",\n        });\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    // 刷新\n    async refreshData() {\n      this.page = 1;\n      this.hasMore = true;\n      this.filterDate = \"\";\n      this.faultList = [];\n      this.activeTab = 'all';\n      this.currentStatus = \"\";\n      \n      // 重新加载故障列表\n      this.loadFaultList();\n    },\n    // 查看故障详情\n    viewFaultDetail(faultId) {\n      uni.navigateTo({\n        url: `/pages/fault/detail?id=${faultId}`,\n      });\n    },\n\n    // 导航到故障上报页面\n    navigateToReport() {\n      uni.navigateTo({\n        url: \"/pages/fault/createfault\",\n      });\n    },\n\n    // 日期选择变更\n    onDateChange(e) {\n      this.filterDate = e.detail.value;\n      this.page = 1; // 重置页码\n      this.faultList = []; // 清空列表\n      this.hasMore = true; // 重置加载更多状态\n      this.loadFaultList();\n    },\n\n    // 获取故障状态对应的样式类\n    getFaultStatusClass(status) {\n      const statusClassMap = {\n        待确认: \"pending\",\n        已确认: \"confirmed\",\n        已完成: \"completed\",\n        已退回: \"returned\",\n      };\n      return statusClassMap[status] || \"default\";\n    },\n\n    // 获取故障等级对应的样式类\n    getFaultLevelClass(level) {\n      const levelClassMap = {\n        提示: \"notice\",\n        一般: \"normal\",\n        重要: \"important\",\n        严重: \"serious \",\n\t\t紧急: \"critical\",\n      };\n      return levelClassMap[level] || \"default\";\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\">\n.fault-list-container {\n  background-color: #f5f7fa;\n  min-height: 100vh;\n  padding-bottom: 120rpx; // 为底部按钮留出空间\n}\n\n/* Tab切换 */\n.tab-container {\n  display: flex;\n  background-color: #fff;\n  border-bottom: 1rpx solid #eee;\n  position: sticky;\n  top: 0;\n  z-index: 999;\n  \n  .tab-item {\n    flex: 1;\n    height: 80rpx;\n    line-height: 80rpx;\n    text-align: center;\n    font-size: 30rpx;\n    color: #666;\n    position: relative;\n    \n    &.active {\n      color: #007aff;\n      font-weight: 500;\n      \n      &::after {\n        content: '';\n        position: absolute;\n        bottom: 0;\n        left: 50%;\n        transform: translateX(-50%);\n        width: 60rpx;\n        height: 4rpx;\n        background-color: #007aff;\n        border-radius: 2rpx;\n      }\n    }\n  }\n}\n\n// 筛选部分\n.filter-section {\n  background-color: #ffffff;\n  display: flex;\n  justify-content: space-between;\n  padding: 20rpx 30rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n  margin-bottom: 20rpx;\n  position: sticky;\n  top: 80rpx;\n  z-index: 998;\n  \n  .date-filter {\n    flex: 1;\n    height: 70rpx;\n\n    .date-picker {\n      display: flex;\n      align-items: center;\n      height: 70rpx;\n      border: 1px solid #e5e5e5;\n      border-radius: 6rpx;\n      padding: 0 20rpx;\n      font-size: 28rpx;\n      color: #333;\n\n      .date-text {\n        flex: 1;\n      }\n\n      .iconfont {\n        margin-left: 10rpx;\n        color: #999;\n      }\n    }\n  }\n\n  .date-filter {\n    margin-right: 20rpx;\n  }\n}\n\n// 故障列表\n.fault-list {\n  padding: 0 30rpx;\n\n  .fault-item {\n    background-color: #ffffff;\n    border-radius: 12rpx;\n    margin-bottom: 20rpx;\n    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n    overflow: hidden;\n\n    .fault-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 20rpx 30rpx;\n      border-bottom: 1px solid #f0f0f0;\n\n      .heat-unit-name {\n        font-size: 32rpx;\n        font-weight: 500;\n        color: #333;\n      }\n\n      .fault-status {\n        padding: 6rpx 16rpx;\n        border-radius: 6rpx;\n        font-size: 24rpx;\n\n        &.pending {\n          background-color: #fff7e6;\n          color: #fa8c16;\n        }\n\n        &.confirmed {\n          background-color: #e6f7ff;\n          color: #1890ff;\n        }\n\n        &.completed {\n          background-color: #f6ffed;\n          color: #52c41a;\n        }\n        \n        &.returned {\n          background-color: #fff1f0;\n          color: #f5222d;\n        }\n\n        &.default {\n          background-color: #f5f5f5;\n          color: #999;\n        }\n      }\n    }\n\n    .fault-content {\n      padding: 30rpx;\n\n      .fault-desc {\n        font-size: 28rpx;\n        color: #333;\n        line-height: 1.6;\n        margin-bottom: 20rpx;\n      }\n\n      .fault-info {\n        margin-bottom: 20rpx;\n\n        .info-item {\n          display: flex;\n          align-items: center;\n          margin-bottom: 10rpx;\n\n          .info-label {\n            font-size: 26rpx;\n            color: #999;\n            width: 150rpx;\n          }\n\n          .info-value {\n            font-size: 26rpx;\n            color: #333;\n          }\n\n          .level-tag {\n            padding: 4rpx 12rpx;\n            border-radius: 4rpx;\n            font-size: 24rpx;\n\n            &.notice {\n              background-color: #e6f7ff;\n              color: #1890ff;\n            }\n\n            &.normal {\n              background-color: #e6f7ff;\n              color: #1890ff;\n            }\n\n            &.important {\n              background-color: #fff7e6;\n              color: #fa8c16;\n            }\n             &.serious {\n              background-color: #ffebee;\n              color: #EF5350;\n            }\n            &.critical {\n              background-color: #fff1f0;\n              color: #f5222d;\n            }\n\n            &.default {\n              background-color: #f5f5f5;\n              color: #999;\n            }\n          }\n        }\n      }\n\n      .fault-footer {\n        display: flex;\n        justify-content: flex-end;\n\n        .report-info {\n          font-size: 24rpx;\n          color: #999;\n\n          .reporter {\n            margin-right: 10rpx;\n          }\n        }\n      }\n    }\n  }\n}\n/* 重置按钮 */\n.refresh-button {\n    font-size: 28rpx;\n    background-color: #fff;\n    border-radius: 5rpx;\n    align-items: center;\n    height: 72rpx;\n    border-radius: 6rpx;\n    padding: 0 20rpx;\n    font-size: 28rpx;\n    position: relative;\n    min-width: 80rpx;\n  }\n// 空状态\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 100rpx 0;\n\n  .empty-image {\n    width: 240rpx;\n    height: 240rpx;\n    margin-bottom: 30rpx;\n  }\n\n  .empty-text {\n    font-size: 28rpx;\n    color: #999;\n    margin-bottom: 40rpx;\n  }\n\n  .refresh-button {\n    font-size: 28rpx;\n    color: #007aff;\n    background-color: #fff;\n    border: 2rpx solid #007aff;\n    border-radius: 40rpx;\n    padding: 10rpx 60rpx;\n  }\n}\n\n// 加载状态\n.loading-container {\n  display: flex;\n  justify-content: center;\n  padding: 30rpx 0;\n\n  .loading-text {\n    font-size: 28rpx;\n    color: #999;\n  }\n}\n\n// 错误状态\n.error-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 50rpx 0;\n\n  .error-text {\n    font-size: 28rpx;\n    color: #f5222d;\n    margin-bottom: 20rpx;\n  }\n\n  .retry-btn {\n    font-size: 28rpx;\n    color: #fff;\n    background-color: #1890ff;\n    padding: 8rpx 30rpx;\n    border-radius: 30rpx;\n  }\n}\n\n.floating-button {\n  position: fixed;\n  right: 30rpx;\n  bottom: 30rpx;\n  width: 100rpx;\n  height: 100rpx;\n  border-radius: 50%;\n  background-color: #0088ff;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  box-shadow: 0 4rpx 16rpx rgba(0, 136, 255, 0.4);\n  z-index: 10;\n\n  .plus-icon {\n    font-size: 60rpx;\n    color: #fff;\n    font-weight: normal;\n    line-height: 60rpx;\n    margin-top: -3rpx;\n  }\n}\n\n// 热用户选择器\n.heat-unit-filter-section {\n  background-color: #ffffff;\n  display: flex;\n  justify-content: space-between;\n  padding: 20rpx 30rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n  margin-bottom: 20rpx;\n}\n\n.heat-unit-filter {\n  flex: 1;\n  height: 70rpx;\n\n  .heat-unit-picker {\n    display: flex;\n    align-items: center;\n    height: 70rpx;\n    border: 1px solid #e5e5e5;\n    border-radius: 6rpx;\n    padding: 0 20rpx;\n    font-size: 28rpx;\n    color: #333;\n\n    .heat-unit-text {\n      flex: 1;\n    }\n\n    .iconfont {\n      margin-left: 10rpx;\n      color: #999;\n    }\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/fault/list.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "faultApi"], "mappings": ";;;;AA6HA,MAAK,kBAAmB,MAAW;AAEnC,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA;AAAA,EACD;AAAA,EACD,OAAO;AAEL,UAAM,MAAM,oBAAI;AACH,QAAI,YAAa;AAChB,KAAC,IAAI,SAAQ,IAAK,GAAG,SAAQ,EAAG,SAAS,GAAG,GAAG;AACjD,QAAI,QAAO,EAAG,SAAU,EAAC,SAAS,GAAG,GAAG;AAGpD,WAAO;AAAA,MACL,WAAW,CAAE;AAAA,MACb,WAAW;AAAA,MACX,WAAW;AAAA;AAAA,MAGX,WAAW;AAAA;AAAA;AAAA,MAGX,YAAY;AAAA,MACZ,eAAe;AAAA;AAAA,MAGf,MAAM;AAAA,MACN,UAAU;AAAA,MACV,SAAS;AAAA;EAEZ;AAAA,EACD,SAAS;AACR,SAAK,OAAO;AACZ,SAAK,YAAY;AACjB,SAAK,UAAU;AAEf,SAAK,gBAAgB,KAAK,MAAM;AAC9BA,oBAAG,MAAC,oBAAmB;AAAA,IACzB,CAAC;AAAA,EACD;AAAA,EACD,SACA;AAEG,UAAM,MAAM;AACZ,UAAM,cAAc,IAAI,cAAc,IAAI,WAAW;AAErD,QAAI,aAAa;AAEd,WAAK,OAAO;AACZ,WAAK,YAAY;AACjB,WAAK,UAAU;AAGf,UAAI,WAAW,mBAAmB;AAGlC,WAAK,cAAa;AAAA,WACd;AAEJ,UAAI,KAAK,UAAU,WAAW,GAAG;AAC9B,aAAK,cAAa;AAAA,MACrB;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAED,oBAAoB;AAClB,SAAK,OAAO;AACZ,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,gBAAgB,KAAK,MAAM;AAC9BA,oBAAG,MAAC,oBAAmB;AAAA,IACzB,CAAC;AAAA,EACF;AAAA;AAAA,EAED,gBAAgB;AACd,QAAI,KAAK,WAAW,CAAC,KAAK,WAAW;AACnC,WAAK;AACL,WAAK,cAAc,IAAI;AAAA,IACzB;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,UAAU,KAAK;AACb,UAAI,KAAK,cAAc,KAAK;AAC1B,aAAK,YAAY;AACjB,aAAK,OAAO;AACZ,aAAK,YAAY;AACjB,aAAK,UAAU;AAGf,gBAAO,KAAG;AAAA,UACR,KAAK;AACH,iBAAK,gBAAgB;AACrB;AAAA,UACF,KAAK;AACH,iBAAK,gBAAgB;AACrB;AAAA,UACF,KAAK;AACH,iBAAK,gBAAgB;AACrB;AAAA,UACF,KAAK;AACH,iBAAK,gBAAgB;AACrB;AAAA,QACJ;AAEA,aAAK,cAAa;AAAA,MACpB;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB,WAAW;AACzB,UAAI,CAAC;AAAW,eAAO;AAGvB,YAAM,KAAK,OAAO,cAAc,WAAW,OAAO,SAAS,IAAI;AAG/D,YAAM,OAAO,IAAI,KAAK,GAAG,SAAQ,EAAG,WAAW,KAAK,KAAK,KAAK,GAAI;AAElE,YAAM,OAAO,KAAK;AAClB,YAAM,SAAS,KAAK,aAAa,GAAG,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC9D,YAAM,MAAM,KAAK,QAAS,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACrD,YAAM,QAAQ,KAAK,SAAU,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACxD,YAAM,UAAU,KAAK,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC5D,YAAM,UAAU,KAAK,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC5D,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,OAAO,IAAI,OAAO;AAAA,IAC9D;AAAA;AAAA,IAGD,MAAM,cAAc,aAAa,OAAO;AACtC,UAAI,KAAK;AAAW;AAEpB,WAAK,YAAY;AACjB,UAAI,CAAC,YAAY;AACf,aAAK,YAAY;AAAA,MACnB;AAEA,UAAI;AAEF,cAAM,SAAS;AAAA,UACb,MAAM,KAAK;AAAA,UACX,UAAU,KAAK;AAAA;AAIjB,YAAI,KAAK,YAAY;AACnB,iBAAO,OAAO,KAAK;AAAA,QACrB;AAGA,YAAI,KAAK,eAAe;AACtB,iBAAO,SAAS,KAAK;AAAA,QACvB;AAGA,cAAM,aAAaA,cAAAA,MAAI,eAAe,YAAY;AACxDA,sBAAAA,MAAY,MAAA,OAAA,+BAAA,gBAAe,UAAU;AAC/B,YAAI,CAAC,YAAY;AAEfA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AACD,eAAK,YAAY;AACjB,eAAK,YAAY;AACjB;AAAA,QACF;AAGA,eAAO,aAAa;AAGpB,cAAM,MAAM,MAAMC,UAAAA,SAAS,aAAa,MAAM;AAE9C,YAAI,IAAI,SAAS,KAAK;AACpB,gBAAM,EAAE,MAAM,OAAO,WAAW,IAAI,IAAI;AAExC,cAAI,YAAY;AAEd,iBAAK,YAAY,CAAC,GAAG,KAAK,WAAW,GAAG,IAAI;AAAA,iBACvC;AAEL,iBAAK,YAAY;AAAA,UACnB;AAGA,eAAK,UAAU,KAAK,OAAO;AAAA,eACtB;AACL,eAAK,YAAY;AACjBD,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,IAAI,WAAW;AAAA,YACtB,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACA,SAAO,KAAK;AACZA,sBAAA,MAAA,MAAA,SAAA,+BAAc,aAAa,GAAG;AAC9B,aAAK,YAAY;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH,UAAU;AACR,aAAK,YAAY;AAAA,MACnB;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,cAAc;AAClB,WAAK,OAAO;AACZ,WAAK,UAAU;AACf,WAAK,aAAa;AAClB,WAAK,YAAY;AACjB,WAAK,YAAY;AACjB,WAAK,gBAAgB;AAGrB,WAAK,cAAa;AAAA,IACnB;AAAA;AAAA,IAED,gBAAgB,SAAS;AACvBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,0BAA0B,OAAO;AAAA,MACxC,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,mBAAmB;AACjBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,aAAa,GAAG;AACd,WAAK,aAAa,EAAE,OAAO;AAC3B,WAAK,OAAO;AACZ,WAAK,YAAY;AACjB,WAAK,UAAU;AACf,WAAK,cAAa;AAAA,IACnB;AAAA;AAAA,IAGD,oBAAoB,QAAQ;AAC1B,YAAM,iBAAiB;AAAA,QACrB,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA;AAEP,aAAO,eAAe,MAAM,KAAK;AAAA,IAClC;AAAA;AAAA,IAGD,mBAAmB,OAAO;AACxB,YAAM,gBAAgB;AAAA,QACpB,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACV,IAAI;AAAA;AAEA,aAAO,cAAc,KAAK,KAAK;AAAA,IAChC;AAAA,EACF;AACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrYA,GAAG,WAAW,eAAe;"}