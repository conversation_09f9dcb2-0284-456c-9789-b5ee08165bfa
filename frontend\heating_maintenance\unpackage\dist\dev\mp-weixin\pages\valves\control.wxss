/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.valve-control-page {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 30rpx;
}
.header {
  display: flex;
  align-items: center;
  height: 88rpx;
  background-color: #fff;
  padding: 0 30rpx;
  position: relative;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}
.header .back-button {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.header .page-title {
  flex: 1;
  text-align: center;
  font-size: 34rpx;
  font-weight: 500;
  padding-right: 60rpx;
}
.select-area {
  margin: 20rpx 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.select-area .form-item {
  margin-bottom: 20rpx;
}
.select-area .form-item .form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}
.select-area .form-item .form-picker, .select-area .form-item .form-input {
  width: 100%;
  height: 80rpx;
  border: 1px solid #e5e5e5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  background-color: #f8f9fc;
}
.select-area .form-item .form-picker .picker-content, .select-area .form-item .form-input .picker-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80rpx;
}
.select-area .form-item .form-picker .picker-content .picker-value, .select-area .form-item .form-input .picker-content .picker-value {
  font-size: 28rpx;
  color: #333;
}
.select-area .form-item .form-picker .picker-content .iconfont, .select-area .form-item .form-input .picker-content .iconfont {
  font-size: 24rpx;
  color: #999;
}
.select-area .form-item .form-input {
  font-size: 28rpx;
}
.select-area .search-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #1989fa;
  color: #fff;
  border-radius: 8rpx;
  font-size: 30rpx;
  margin-top: 20rpx;
}
.loading-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.loading-container .empty-image, .empty-container .empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.loading-container .empty-text, .empty-container .empty-text {
  font-size: 28rpx;
  color: #999;
}
.valves-list {
  padding: 0 30rpx;
}
.valves-list .valve-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.valves-list .valve-card .valve-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.valves-list .valve-card .valve-header .valve-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.valves-list .valve-card .valve-info {
  display: flex;
  flex-wrap: wrap;
}
.valves-list .valve-card .valve-info .info-item {
  width: 50%;
  margin-bottom: 16rpx;
}
.valves-list .valve-card .valve-info .info-item .info-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 4rpx;
  display: block;
}
.valves-list .valve-card .valve-info .info-item .info-value {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}
.status-tag {
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #fff;
}
.status-tag.status-open {
  background-color: #2ecc71;
}
.status-tag.status-closed {
  background-color: #95a5a6;
}
.status-tag.status-error {
  background-color: #e74c3c;
}