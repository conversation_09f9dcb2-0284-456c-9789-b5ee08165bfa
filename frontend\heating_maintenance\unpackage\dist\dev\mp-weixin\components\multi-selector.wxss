/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1px solid #eee;
}
.header-left,
.header-right {
  display: flex;
  align-items: center;
}
.header-icon {
  font-size: 40rpx;
  color: #666;
  margin-right: 6rpx;
}
.header-cancel {
  font-size: 30rpx;
  color: #666;
}
.header-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
}
.header-confirm {
  font-size: 30rpx;
  color: #1890ff;
  font-weight: 500;
}
.search-box {
  padding: 20rpx 30rpx;
  background-color: #fff;
}
.search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
}
.search-icon {
  font-size: 30rpx;
  color: #999;
  margin-right: 10rpx;
}
.search-input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
}
.clear-icon {
  font-size: 36rpx;
  color: #999;
  padding: 0 6rpx;
}
.selected-area {
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #eee;
}
.selected-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}
.selected-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.selected-clear {
  font-size: 28rpx;
  color: #1890ff;
}
.selected-list {
  white-space: nowrap;
  padding-bottom: 10rpx;
}
.selected-item {
  display: inline-flex;
  align-items: center;
  background-color: rgba(24, 144, 255, 0.1);
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
  margin-right: 16rpx;
}
.selected-item-name {
  font-size: 26rpx;
  color: #1890ff;
  margin-right: 6rpx;
}
.selected-item-remove {
  font-size: 28rpx;
  color: #1890ff;
}
.items-list {
  flex: 1;
  background-color: #fff;
}
.list-item {
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #f0f0f0;
}
.item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.item-name {
  font-size: 30rpx;
  color: #333;
}
.checkbox {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
}
.checkbox.checked {
  background-color: #1890ff;
  border-color: #1890ff;
}
.checkbox-inner {
  color: #fff;
  font-size: 24rpx;
}
.empty-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}