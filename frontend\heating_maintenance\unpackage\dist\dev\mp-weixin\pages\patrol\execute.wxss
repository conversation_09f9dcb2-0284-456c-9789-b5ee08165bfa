/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.patrol-execute-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f5f5;
}
.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(0, 122, 255, 0.1);
  border-top-color: #007aff;
  border-radius: 50%;
  animation: spin 1s infinite linear;
  margin-bottom: 30rpx;
}
.loading-text {
  font-size: 30rpx;
  color: #666;
}
@keyframes spin {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.progress-bar {
  height: 10rpx;
  background-color: #eee;
  border-radius: 5rpx;
  overflow: hidden;
  margin-bottom: 10rpx;
}
.progress-bar .progress-inner {
  height: 100%;
  background-color: #1890ff;
}
.progress-info {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 20rpx;
}
.task-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.task-card .task-header {
  margin-bottom: 20rpx;
}
.task-card .task-header .task-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.task-card .task-details .detail-row {
  display: flex;
  margin-bottom: 16rpx;
}
.task-card .task-details .detail-row:last-child {
  margin-bottom: 0;
}
.task-card .task-details .detail-row .detail-label {
  width: 120rpx;
  font-size: 28rpx;
  color: #666;
}
.task-card .task-details .detail-row .detail-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.task-card .task-details .detail-row .importance-tag {
  display: inline-block;
  padding: 4rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  max-width: -webkit-fit-content;
  max-width: fit-content;
}
.task-card .task-details .detail-row .importance-tag.normal-importance {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1rpx solid #91d5ff;
}
.task-card .task-details .detail-row .importance-tag.important-importance {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1rpx solid #ffd591;
}
.task-card .task-details .detail-row .importance-tag.critical-importance {
  background-color: #fff1f0;
  color: #f5222d;
  border: 1rpx solid #ffa39e;
}
.input-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.input-card .card-header {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.input-card .card-content .form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.input-card .card-content .picker-value {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
}
.input-card .card-content .radio-group {
  display: flex;
}
.input-card .card-content .radio-group .radio-item {
  display: flex;
  align-items: center;
  margin-right: 60rpx;
}
.input-card .card-content .radio-group .radio-item .radio-dot {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  border: 1rpx solid #ddd;
  margin-right: 10rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.input-card .card-content .radio-group .radio-item .radio-dot::after {
  content: "";
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #1890ff;
  opacity: 0;
}
.input-card .card-content .radio-group .radio-item text {
  font-size: 28rpx;
  color: #333;
}
.input-card .card-content .radio-group .radio-item.radio-selected .radio-dot {
  border-color: #1890ff;
}
.input-card .card-content .radio-group .radio-item.radio-selected .radio-dot::after {
  opacity: 1;
}
.input-card .card-content .radio-group .radio-item.radio-selected text {
  color: #1890ff;
}
.input-card .card-content .form-textarea {
  width: 100%;
  height: 160rpx;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.input-card .card-content .image-list {
  display: flex;
  flex-wrap: wrap;
}
.input-card .card-content .image-list .image-item {
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
  border-radius: 8rpx;
  overflow: hidden;
}
.input-card .card-content .image-list .image-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.input-card .card-content .image-list .image-item .delete-icon {
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 28rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom-left-radius: 8rpx;
}
.input-card .card-content .image-list .image-upload {
  width: 160rpx;
  height: 160rpx;
  border: 1rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
}
.input-card .card-content .image-list .image-upload .upload-icon {
  font-size: 48rpx;
  color: #999;
  margin-bottom: 8rpx;
}
.input-card .card-content .image-list .image-upload .upload-text {
  font-size: 24rpx;
  color: #999;
}
.action-buttons {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx;
  background-color: #fff;
  display: flex;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.action-buttons .action-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 30rpx;
  margin: 0 10rpx;
}
.action-buttons .action-btn.prev {
  background-color: #f5f5f5;
  color: #333;
}
.action-buttons .action-btn.skip {
  background-color: #f5f5f5;
  color: #faad14;
}
.action-buttons .action-btn.submit {
  background-color: #1890ff;
  color: #fff;
}
.popup-content {
  width: 560rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}
.popup-content .popup-title {
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  padding: 30rpx 0;
}
.popup-content .popup-message {
  padding: 0 30rpx 30rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
}
.popup-content .popup-buttons {
  display: flex;
  border-top: 1rpx solid #eee;
}
.popup-content .popup-buttons .popup-button {
  flex: 1;
  text-align: center;
  height: 90rpx;
  line-height: 90rpx;
  font-size: 30rpx;
}
.popup-content .popup-buttons .popup-button.cancel {
  color: #333;
  border-right: 1rpx solid #eee;
}
.popup-content .popup-buttons .popup-button.confirm {
  color: #1890ff;
}
.content-description {
  word-break: break-word;
  white-space: normal;
}
textarea {
  width: 100%;
  height: 160rpx;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.unit-text {
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}