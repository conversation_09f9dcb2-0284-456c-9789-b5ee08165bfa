"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const utils_upload = require("../../utils/upload.js");
const common_assets = require("../../common/assets.js");
const PermissionCheck = () => "../../components/PermissionCheck.js";
const _sfc_main = {
  components: {
    PermissionCheck
  },
  data() {
    return {
      loading: true,
      showError: false,
      errorMessage: "",
      recordId: null,
      recordInfo: {},
      patrolResults: [],
      resultSummary: null,
      planInfo: null,
      currentTab: 0,
      tabs: [
        { name: "巡检信息" },
        { name: "巡检任务及结果" },
        { name: "巡检照片" }
      ]
    };
  },
  onLoad(options) {
    if (options && options.id) {
      this.recordId = options.id;
      this.loadPatrolResults();
    } else {
      this.showError = true;
      this.errorMessage = "未找到记录ID，无法加载详情";
      this.loading = false;
    }
  },
  onReady() {
    this.currentTab = 0;
  },
  methods: {
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        pending: "待执行",
        processing: "执行中",
        completed: "已完成",
        overdue: "已超时"
      };
      return statusMap[status] || "未知";
    },
    // 获取状态样式类
    getStatusClass(status) {
      const statusClassMap = {
        pending: "status-pending",
        processing: "status-processing",
        completed: "status-completed",
        overdue: "status-overdue"
      };
      return statusClassMap[status] || "";
    },
    // 开始巡检
    startPatrol(id, planId) {
      common_vendor.index.navigateTo({
        url: `/pages/patrol/execute?id=${id}&&planId=${planId}`
      });
    },
    // 加载巡检结果
    loadPatrolResults() {
      if (!utils_api.patrolApi || !utils_api.patrolApi.getPatrolResultDetail) {
        this.showError = true;
        this.errorMessage = "API服务不可用";
        this.loading = false;
        return;
      }
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      utils_api.patrolApi.getPatrolResultDetail(this.recordId).then((res) => {
        this.loading = false;
        if (res.code === 200 && res.data) {
          this.recordInfo = res.data.recordInfo || {};
          if (res.data.resultList && Array.isArray(res.data.resultList)) {
            this.patrolResults = res.data.resultList.map((result) => {
              if (result.images && !Array.isArray(result.images)) {
                try {
                  result.images = JSON.parse(result.images);
                } catch (e) {
                  result.images = result.images ? [result.images] : [];
                }
              }
              return result;
            });
          } else {
            this.patrolResults = [];
          }
          this.resultSummary = res.data.summary || null;
          this.planInfo = res.data.planInfo || null;
          common_vendor.index.hideLoading();
        } else {
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("warn", "at pages/patrol/record_detail.vue:381", "获取巡检结果失败:", res.message);
          this.patrolResults = [];
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        this.loading = false;
        common_vendor.index.__f__("error", "at pages/patrol/record_detail.vue:388", "加载巡检结果失败:", err);
        common_vendor.index.showToast({
          title: "网络异常，无法加载巡检结果",
          icon: "none",
          duration: 2e3
        });
        this.patrolResults = [];
      });
    },
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 预览图片
    previewImage(images, current) {
      const fullUrls = images.map((path) => this.getFullImageUrl(path));
      common_vendor.index.previewImage({
        urls: fullUrls,
        current: fullUrls[current]
      });
    },
    // 获取完整的图片URL
    getFullImageUrl(path) {
      if (!path)
        return "";
      if (path.startsWith("http")) {
        return path;
      }
      return utils_upload.uploadUtils.getFileUrl(path);
    },
    // 格式化日期（不含时间）
    formatDateOnly(date) {
      if (!date)
        return "-";
      if (Array.isArray(date)) {
        if (date.length >= 3) {
          const year = date[0];
          const month = String(date[1]).padStart(2, "0");
          const day = String(date[2]).padStart(2, "0");
          return `${year}-${month}-${day}`;
        }
        return date.join("-");
      }
      try {
        const d = new Date(date);
        if (!isNaN(d.getTime())) {
          const year = d.getFullYear();
          const month = String(d.getMonth() + 1).padStart(2, "0");
          const day = String(d.getDate()).padStart(2, "0");
          return `${year}-${month}-${day}`;
        }
        return String(date);
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/patrol/record_detail.vue:451", "格式化日期出错:", e);
        return String(date);
      }
    },
    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime)
        return "-";
      if (Array.isArray(dateTime)) {
        if (dateTime.length >= 3) {
          const year = dateTime[0];
          const month = String(dateTime[1]).padStart(2, "0");
          const day = String(dateTime[2]).padStart(2, "0");
          if (dateTime.length >= 5) {
            const hour = String(dateTime[3]).padStart(2, "0");
            const minute = String(dateTime[4]).padStart(2, "0");
            const second = dateTime.length > 5 ? String(dateTime[5]).padStart(2, "0") : "00";
            return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
          }
          return `${year}-${month}-${day}`;
        }
        return dateTime.join("-");
      }
      const dateTimeStr = String(dateTime);
      if (dateTimeStr.includes("T")) {
        const date = new Date(dateTimeStr);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        const seconds = String(date.getSeconds()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      }
      if (!isNaN(dateTime) && typeof dateTime === "number") {
        const date = new Date(dateTime);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        const seconds = String(date.getSeconds()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      }
      try {
        const date = new Date(dateTimeStr);
        if (!isNaN(date.getTime())) {
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, "0");
          const day = String(date.getDate()).padStart(2, "0");
          const hours = String(date.getHours()).padStart(2, "0");
          const minutes = String(date.getMinutes()).padStart(2, "0");
          const seconds = String(date.getSeconds()).padStart(2, "0");
          if (hours !== "00" || minutes !== "00" || seconds !== "00") {
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
          } else {
            return `${year}-${month}-${day}`;
          }
        }
        return dateTimeStr;
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/patrol/record_detail.vue:530", "格式化日期时间出错:", e, dateTimeStr);
        return dateTimeStr;
      }
    },
    // 格式化时间，只提取时间部分 (HH:MM:SS)
    formatTimeOnly(dateTime) {
      if (!dateTime)
        return "-";
      const fullDateTime = this.formatDateTime(dateTime);
      if (fullDateTime.includes(" ")) {
        return fullDateTime.split(" ")[1];
      }
      return fullDateTime;
    },
    // 获取巡检计划类型文本
    getScheduleTypeText(scheduleType) {
      if (!scheduleType)
        return "-";
      const scheduleTypeMap = {
        daily: "每日巡检",
        weekly: "每周巡检",
        monthly: "每月巡检",
        custom: "自定义巡检"
      };
      return scheduleTypeMap[scheduleType] || scheduleType;
    },
    // 切换任务详情显示/隐藏
    toggleTaskDetails(index) {
      if (this.patrolResults[index] && !this.patrolResults[index].hasOwnProperty("showDetails")) {
        this.$set(this.patrolResults[index], "showDetails", true);
      } else {
        this.$set(this.patrolResults[index], "showDetails", !this.patrolResults[index].showDetails);
      }
    },
    // 格式化周执行日文本
    formatWeekDays(weekDays) {
      if (!weekDays || !Array.isArray(weekDays))
        return "-";
      const weekDayNames = {
        1: "周一",
        2: "周二",
        3: "周三",
        4: "周四",
        5: "周五",
        6: "周六",
        7: "周日"
      };
      return weekDays.map((day) => weekDayNames[day] || `周${day}`).join(", ");
    },
    // 格式化月执行日文本
    formatMonthDays(monthDays) {
      if (!monthDays || !Array.isArray(monthDays))
        return "-";
      return monthDays.map((day) => `${day}日`).join(", ");
    },
    // 获取巡检项目状态文本
    getTaskStatusText(result) {
      if (this.recordInfo.status === "pending") {
        return "待执行";
      } else if (this.recordInfo.status === "overdue") {
        return "已超时";
      } else {
        return result.checkResult === "normal" ? "正常" : "异常";
      }
    },
    // 获取所有图片信息
    getAllImages() {
      const images = [];
      this.patrolResults.forEach((result) => {
        if (result.images && Array.isArray(result.images)) {
          result.images.forEach((img) => {
            images.push({
              url: this.getFullImageUrl(img),
              itemName: result.itemName || `巡检项 ${result.index + 1}`
            });
          });
        }
      });
      return images;
    },
    // 获取所有图片URL
    getAllImagesUrls() {
      const urls = [];
      this.getAllImages().forEach((image) => {
        urls.push(image.url);
      });
      return urls;
    },
    // 切换选项卡
    switchTab(index) {
      this.currentTab = index;
      setTimeout(() => {
        common_vendor.index.pageScrollTo({
          scrollTop: 0,
          duration: 0
        });
      }, 50);
    }
  }
};
if (!Array) {
  const _component_PermissionCheck = common_vendor.resolveComponent("PermissionCheck");
  _component_PermissionCheck();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p, _q, _r, _s, _t, _u, _v;
  return common_vendor.e({
    a: common_vendor.t($data.recordInfo.planName || "-"),
    b: common_vendor.t($options.getStatusText($data.recordInfo.status)),
    c: common_vendor.n($options.getStatusClass($data.recordInfo.status)),
    d: common_vendor.t($data.recordInfo.locations || "未知"),
    e: common_vendor.t($data.recordInfo.executorName || "未知"),
    f: common_vendor.t($options.formatDateOnly($data.recordInfo.executionDate)),
    g: common_vendor.f($data.tabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab.name),
        b: index,
        c: $data.currentTab === index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchTab(index), index)
      };
    }),
    h: $data.showError
  }, $data.showError ? {
    i: common_vendor.t($data.errorMessage),
    j: common_vendor.o((...args) => $options.loadPatrolResults && $options.loadPatrolResults(...args))
  } : {}, {
    k: !$data.loading && !$data.showError
  }, !$data.loading && !$data.showError ? common_vendor.e({
    l: $data.currentTab === 0
  }, $data.currentTab === 0 ? common_vendor.e({
    m: common_vendor.t(((_a = $data.planInfo) == null ? void 0 : _a.planName) || $data.recordInfo.planName || "-"),
    n: (_b = $data.planInfo) == null ? void 0 : _b.planNo
  }, ((_c = $data.planInfo) == null ? void 0 : _c.planNo) ? {
    o: common_vendor.t($data.planInfo.planNo)
  } : {}, {
    p: (_d = $data.planInfo) == null ? void 0 : _d.patrolType
  }, ((_e = $data.planInfo) == null ? void 0 : _e.patrolType) ? {
    q: common_vendor.t($data.planInfo.patrolType)
  } : {}, {
    r: (_f = $data.planInfo) == null ? void 0 : _f.scheduleType
  }, ((_g = $data.planInfo) == null ? void 0 : _g.scheduleType) ? {
    s: common_vendor.t($options.getScheduleTypeText($data.planInfo.scheduleType))
  } : {}, {
    t: ((_h = $data.planInfo) == null ? void 0 : _h.scheduleType) === "weekly" && ((_i = $data.planInfo) == null ? void 0 : _i.scheduleWeekDays)
  }, ((_j = $data.planInfo) == null ? void 0 : _j.scheduleType) === "weekly" && ((_k = $data.planInfo) == null ? void 0 : _k.scheduleWeekDays) ? {
    v: common_vendor.t($options.formatWeekDays($data.planInfo.scheduleWeekDays))
  } : {}, {
    w: ((_l = $data.planInfo) == null ? void 0 : _l.scheduleType) === "monthly" && ((_m = $data.planInfo) == null ? void 0 : _m.scheduleMonthDays)
  }, ((_n = $data.planInfo) == null ? void 0 : _n.scheduleType) === "monthly" && ((_o = $data.planInfo) == null ? void 0 : _o.scheduleMonthDays) ? {
    x: common_vendor.t($options.formatMonthDays($data.planInfo.scheduleMonthDays))
  } : {}, {
    y: ((_p = $data.planInfo) == null ? void 0 : _p.scheduleType) === "custom" && ((_q = $data.planInfo) == null ? void 0 : _q.scheduleInterval)
  }, ((_r = $data.planInfo) == null ? void 0 : _r.scheduleType) === "custom" && ((_s = $data.planInfo) == null ? void 0 : _s.scheduleInterval) ? {
    z: common_vendor.t($data.planInfo.scheduleInterval)
  } : {}, {
    A: common_vendor.t($options.formatDateOnly((_t = $data.planInfo) == null ? void 0 : _t.startDate)),
    B: (_u = $data.planInfo) == null ? void 0 : _u.endDate
  }, ((_v = $data.planInfo) == null ? void 0 : _v.endDate) ? {
    C: common_vendor.t($options.formatDateOnly($data.planInfo.endDate))
  } : {}, {
    D: common_vendor.t($options.formatTimeOnly($data.recordInfo.startTime)),
    E: common_vendor.t($options.formatTimeOnly($data.recordInfo.endTime)),
    F: common_vendor.t($data.recordInfo.locations || "-"),
    G: $data.recordInfo.executorPhone
  }, $data.recordInfo.executorPhone ? {
    H: common_vendor.t($data.recordInfo.executorPhone)
  } : {}, {
    I: $data.recordInfo.remark
  }, $data.recordInfo.remark ? {
    J: common_vendor.t($data.recordInfo.remark)
  } : {}) : {}, {
    K: $data.currentTab === 1
  }, $data.currentTab === 1 ? common_vendor.e({
    L: common_vendor.t($data.recordInfo.status === "pending" || $data.recordInfo.status === "overdue" ? "巡检任务" : "巡检结果"),
    M: common_vendor.t($data.patrolResults.length),
    N: $data.resultSummary && $data.resultSummary.abnormalCount > 0 && $data.recordInfo.status !== "pending" && $data.recordInfo.status !== "overdue"
  }, $data.resultSummary && $data.resultSummary.abnormalCount > 0 && $data.recordInfo.status !== "pending" && $data.recordInfo.status !== "overdue" ? {
    O: common_vendor.t($data.resultSummary.abnormalCount)
  } : {}, {
    P: $data.patrolResults.length === 0
  }, $data.patrolResults.length === 0 ? {
    Q: common_assets._imports_0$1,
    R: common_vendor.t($data.recordInfo.status === "pending" || $data.recordInfo.status === "overdue" ? "暂无巡检任务" : "暂无巡检结果")
  } : {
    S: common_vendor.f($data.patrolResults, (result, index, i0) => {
      return common_vendor.e({
        a: common_vendor.n($data.recordInfo.status === "pending" ? "pending" : $data.recordInfo.status === "overdue" ? "overdue" : result.checkResult === "normal" ? "completed" : "abnormal"),
        b: common_vendor.t(result.itemName || `巡检项 ${index + 1}`),
        c: common_vendor.t($options.getTaskStatusText(result)),
        d: common_vendor.n($data.recordInfo.status === "pending" ? "status-pending" : $data.recordInfo.status === "overdue" ? "status-overdue" : result.checkResult === "normal" ? "status-normal" : "status-abnormal"),
        e: result.showDetails
      }, result.showDetails ? common_vendor.e({
        f: result.deviceName
      }, result.deviceName ? {
        g: common_vendor.t(result.deviceName),
        h: common_vendor.t(result.deviceType ? ` (${result.deviceType})` : "")
      } : {}, {
        i: result.categoryName
      }, result.categoryName ? {
        j: common_vendor.t(result.categoryName)
      } : {}, {
        k: result.checkMethod
      }, result.checkMethod ? {
        l: common_vendor.t(result.checkMethod)
      } : {}, {
        m: result.checkDescription
      }, result.checkDescription ? {
        n: common_vendor.t(result.checkDescription)
      } : {}, {
        o: result.paramValue
      }, result.paramValue ? {
        p: common_vendor.t(result.paramValue),
        q: common_vendor.t(result.unit ? " " + result.unit : ""),
        r: result.checkResult === "abnormal" ? 1 : ""
      } : {}, {
        s: result.normalRange
      }, result.normalRange ? {
        t: common_vendor.t(result.normalRange)
      } : {}, {
        v: result.description
      }, result.description ? {
        w: common_vendor.t(result.description)
      } : {}, {
        x: result.images && result.images.length > 0
      }, result.images && result.images.length > 0 ? {
        y: common_vendor.f(result.images, (img, imgIndex, i1) => {
          return {
            a: imgIndex,
            b: $options.getFullImageUrl(img),
            c: common_vendor.o(($event) => $options.previewImage(result.images, imgIndex), imgIndex)
          };
        })
      } : {}) : {}, {
        z: index,
        A: common_vendor.o(($event) => $options.toggleTaskDetails(index), index)
      });
    })
  }) : {}, {
    T: $data.currentTab === 2
  }, $data.currentTab === 2 ? common_vendor.e({
    U: $options.getAllImages().length > 0
  }, $options.getAllImages().length > 0 ? {
    V: common_vendor.f($options.getAllImages(), (imageInfo, index, i0) => {
      return {
        a: $options.getFullImageUrl(imageInfo.url),
        b: common_vendor.t(imageInfo.itemName),
        c: index,
        d: common_vendor.o(($event) => $options.previewImage($options.getAllImagesUrls(), index), index)
      };
    })
  } : {
    W: common_assets._imports_0$1
  }) : {}) : {}, {
    X: $data.recordInfo.status === "pending" || $data.recordInfo.status === "overdue"
  }, $data.recordInfo.status === "pending" || $data.recordInfo.status === "overdue" ? {
    Y: common_vendor.o(($event) => $options.startPatrol($data.recordInfo.id, $data.recordInfo.patrolPlanId))
  } : {}, {
    Z: common_vendor.p({
      permission: "patrol:plans:execute"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/patrol/record_detail.js.map
