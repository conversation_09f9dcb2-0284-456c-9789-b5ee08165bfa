{"version": 3, "file": "attendance.js", "sources": ["store/attendance.js"], "sourcesContent": ["// attendance.js - 考勤相关状态管理\r\n\r\nimport { attendanceApi } from '@/utils/api.js';\r\n\r\n// 全局状态管理 - 考勤模块\r\nexport default {\r\n  namespaced: true, // 明确设置为命名空间模块\r\n  state: {\r\n    // 定位上传相关\r\n    locationUploadTimer: null,\r\n    isUploadingLocation: false,\r\n    useNativeGeolocation: true,\r\n    locationUploadInterval: 1, // 默认1分钟\r\n    latitude: 0,\r\n    longitude: 0,\r\n    // 打卡记录\r\n    clockInTime: '',\r\n    clockOutTime: '',\r\n  },\r\n  \r\n  mutations: {\r\n    // 设置定时器\r\n    SET_LOCATION_UPLOAD_TIMER(state, timer) {\r\n      state.locationUploadTimer = timer;\r\n    },\r\n    \r\n    // 设置是否正在上传位置\r\n    SET_IS_UPLOADING_LOCATION(state, status) {\r\n      state.isUploadingLocation = status;\r\n    },\r\n    \r\n    // 设置定位方式\r\n    SET_USE_NATIVE_GEOLOCATION(state, value) {\r\n      state.useNativeGeolocation = value;\r\n    },\r\n    \r\n    // 设置位置上传间隔\r\n    SET_LOCATION_UPLOAD_INTERVAL(state, minutes) {\r\n      state.locationUploadInterval = minutes;\r\n    },\r\n    \r\n    // 设置经纬度\r\n    SET_LOCATION(state, { latitude, longitude }) {\r\n      state.latitude = latitude;\r\n      state.longitude = longitude;\r\n    },\r\n    \r\n    // 设置打卡记录\r\n    SET_CLOCK_RECORDS(state, { clockInTime, clockOutTime }) {\r\n      state.clockInTime = clockInTime || '';\r\n      state.clockOutTime = clockOutTime || '';\r\n    }\r\n  },\r\n  \r\n  actions: {\r\n    // 获取今日打卡记录\r\n    getTodayClockRecord({ commit, dispatch }) {\r\n      return new Promise((resolve, reject) => {\r\n        attendanceApi.getTodayRecord()\r\n          .then(res => {\r\n            //console.log('获取今日打卡记录:', res);\r\n            if (res.code === 200) {\r\n              // 即使res.data为null也进行处理\r\n              const data = res.data || {};\r\n              const clockInTime = data.clockInTime || '';\r\n              const clockOutTime = data.clockOutTime || '';\r\n              \r\n              // 更新打卡记录\r\n              commit('SET_CLOCK_RECORDS', { \r\n                clockInTime: clockInTime, \r\n                clockOutTime: clockOutTime \r\n              });\r\n              \r\n              // 获取是否开启定位上传的设置\r\n              const isPositioning = uni.getStorageSync('isPositioning') || 0;\r\n             // console.log('定位上传设置:', isPositioning);\r\n              \r\n              // 只有当开启了定位上传功能，才执行后续的定位上传逻辑\r\n              if (isPositioning === 1) {\r\n                // 检查是否需要开始或停止定位上传\r\n                // 只有当clockInTime存在且不为空字符串，且clockOutTime不存在或为空字符串时，才启动定位上传\r\n                if (clockInTime && clockInTime !== '' && (!clockOutTime || clockOutTime === '')) {\r\n                  // 已上班打卡但未下班打卡，启动定位上传\r\n                  //console.log('检测到已上班但未下班，启动定位上传');\r\n                  dispatch('startLocationUpload');\r\n                } else if (clockOutTime && clockOutTime !== '') {\r\n                  // 已下班打卡，停止定位上传\r\n                //  console.log('检测到已下班，停止定位上传');\r\n                  dispatch('stopLocationUpload');\r\n                } else {\r\n                  // 没有打卡记录或返回异常数据，停止定位上传\r\n                 // console.log('没有有效的打卡记录，不启动定位上传');\r\n                  dispatch('stopLocationUpload');\r\n                }\r\n              } else {\r\n                // 未开启定位上传功能，确保停止定位上传\r\n                console.log('未开启定位上传功能，确保停止定位上传');\r\n                dispatch('stopLocationUpload');\r\n              }\r\n              \r\n              resolve(data);\r\n            } else {\r\n              console.error('获取打卡记录失败:', res.message);\r\n              reject(new Error('获取打卡记录失败: ' + (res.message || '未知错误')));\r\n            }\r\n          })\r\n          .catch(err => {\r\n            console.error('获取今日打卡记录失败:', err);\r\n            reject(err);\r\n          });\r\n      });\r\n    },\r\n    \r\n    // 开始定时上传位置\r\n    startLocationUpload({ commit, state, dispatch }) {\r\n      // 如果已经在上传，则不重复启动\r\n      if (state.isUploadingLocation) {\r\n        return;\r\n      }\r\n\r\n      // 如果已经有定时器在运行，先清除\r\n      if (state.locationUploadTimer) {\r\n        clearInterval(state.locationUploadTimer);\r\n      }\r\n\r\n      // 标记为正在上传位置\r\n      commit('SET_IS_UPLOADING_LOCATION', true);\r\n\r\n      // 获取存储的定位上传周期参数（单位：分钟）\r\n      const storedPositionCycle = uni.getStorageSync('positionCycle') || 1;\r\n      const interval = storedPositionCycle;\r\n      const intervalMs = interval * 60 * 1000; // 转换为毫秒\r\n\r\n      console.log(`开始定时上传位置，间隔时间: ${interval}分钟（来源：系统配置）`);\r\n\r\n      // 立即上传一次位置\r\n      dispatch('uploadLocation');\r\n\r\n      // 设置定时器，定时上传位置\r\n      const timer = setInterval(() => {\r\n        dispatch('uploadLocation');\r\n      }, intervalMs);\r\n\r\n      // 保存定时器ID\r\n      commit('SET_LOCATION_UPLOAD_TIMER', timer);\r\n    },\r\n    \r\n    // 停止定时上传位置\r\n    stopLocationUpload({ commit, state }) {\r\n      if (state.locationUploadTimer) {\r\n        console.log('停止定时上传位置');\r\n        clearInterval(state.locationUploadTimer);\r\n        commit('SET_LOCATION_UPLOAD_TIMER', null);\r\n      }\r\n      \r\n      // 标记为不在上传位置\r\n      commit('SET_IS_UPLOADING_LOCATION', false);\r\n    },\r\n    \r\n    // 上传位置\r\n    uploadLocation({ state, dispatch }) {\r\n      console.log('准备获取位置并上传轨迹...');\r\n      \r\n      // 获取最新位置\r\n      if (state.useNativeGeolocation) {\r\n        // 优先使用原生定位\r\n        dispatch('getNativeLocation')\r\n          .then(position => {\r\n            // 使用获取到的位置上传轨迹\r\n            const coords = position.coords;\r\n            dispatch('uploadTrajectoryRecord', { \r\n              latitude: coords.latitude, \r\n              longitude: coords.longitude \r\n            });\r\n          })\r\n          .catch(err => {\r\n            console.error('获取原生位置失败，尝试使用uni定位:', err);\r\n            // 回退到uni定位\r\n            dispatch('getUniLocation')\r\n              .then(res => {\r\n                // 使用获取到的位置上传轨迹\r\n                dispatch('uploadTrajectoryRecord', { \r\n                  latitude: res.latitude, \r\n                  longitude: res.longitude \r\n                });\r\n              })\r\n              .catch(err => {\r\n                console.error('位置上传失败，无法获取位置:', err);\r\n              });\r\n          });\r\n      } else {\r\n        // 直接使用uni定位\r\n        dispatch('getUniLocation')\r\n          .then(res => {\r\n            // 使用获取到的位置上传轨迹\r\n            dispatch('uploadTrajectoryRecord', { \r\n              latitude: res.latitude, \r\n              longitude: res.longitude \r\n            });\r\n          })\r\n          .catch(err => {\r\n            console.error('位置上传失败，无法获取位置:', err);\r\n          });\r\n      }\r\n    },\r\n    \r\n    // 使用原生定位API获取位置\r\n    getNativeLocation({ dispatch }) {\r\n      return new Promise((resolve, reject) => {\r\n        // #ifdef APP-PLUS\r\n        try {\r\n          plus.geolocation.getCurrentPosition(\r\n            (position) => {\r\n              console.log('原生定位成功:', position);\r\n              \r\n              // 提取位置信息\r\n              const coords = position.coords;\r\n              \r\n              resolve(position);\r\n            },\r\n            (err) => {\r\n              console.error('原生定位失败:', err);\r\n              reject({\r\n                errMsg: err.message || '获取位置失败',\r\n                detail: err\r\n              });\r\n            },\r\n            {\r\n              enableHighAccuracy: true, // 高精度定位\r\n              timeout: 15000, // 超时时间\r\n              maximumAge: 0, // 不使用缓存\r\n              provider: 'system', // 使用系统定位\r\n              geocode: false // 不获取地理编码信息\r\n            }\r\n          );\r\n        } catch (e) {\r\n          console.error('调用原生定位API异常:', e);\r\n          reject({\r\n            errMsg: '调用定位服务失败',\r\n            detail: e.message\r\n          });\r\n        }\r\n        // #endif\r\n        \r\n        // #ifndef APP-PLUS\r\n        // 非APP环境下，使用uni定位API\r\n        dispatch('getUniLocation').then(resolve).catch(reject);\r\n        // #endif\r\n      });\r\n    },\r\n    \r\n    // 使用uni.getLocation获取位置\r\n    getUniLocation() {\r\n      return new Promise((resolve, reject) => {\r\n        uni.getLocation({\r\n          type: 'gcj02',\r\n          isHighAccuracy: true,\r\n          highAccuracyExpireTime: 5000,\r\n          success: (res) => {\r\n            console.log('uni位置获取成功:', res);\r\n            resolve(res);\r\n          },\r\n          fail: (err) => {\r\n            console.error('uni获取位置失败:', err);\r\n            reject(err);\r\n          }\r\n        });\r\n      });\r\n    },\r\n    \r\n    // 上传位置轨迹记录\r\n    uploadTrajectoryRecord({ commit }, { latitude, longitude }) {\r\n      // 检查经纬度是否有效\r\n      if (!longitude || !latitude || longitude === 0 || latitude === 0) {\r\n        console.error('经纬度无效，无法上传位置轨迹:', {\r\n          longitude: longitude,\r\n          latitude: latitude\r\n        });\r\n        return Promise.reject({errMsg: '位置参数无效'});\r\n      }\r\n      \r\n      // 更新当前位置\r\n      commit('SET_LOCATION', { latitude, longitude });\r\n      \r\n      // 获取用户ID\r\n      const userId = uni.getStorageSync('userId');\r\n      if (!userId) {\r\n        console.warn('未找到用户ID，无法上传位置轨迹');\r\n        return Promise.reject({errMsg: '未找到用户ID'});\r\n      }\r\n      \r\n      console.log('上传位置轨迹:', {\r\n        userId: userId,\r\n        longitude: longitude,\r\n        latitude: latitude\r\n      });\r\n      \r\n      // 调用API上传位置数据\r\n      return attendanceApi.uploadPersonTrajectory({\r\n        userId: userId, // 传递userId参数\r\n        employeeId: userId, // 同时传递employeeId参数，值与userId相同\r\n        longitude: longitude,\r\n        latitude: latitude\r\n      }).then(res => {\r\n        // 无论服务器返回什么状态码，只要有data且code为200，就认为成功\r\n        if (res && res.code === 200) {\r\n          console.log('位置轨迹上传成功:', res.data);\r\n          return res.data;\r\n        } else {\r\n          const errorMsg = (res && res.message) ? res.message : '未知错误';\r\n          console.error('位置轨迹上传失败:', errorMsg, res);\r\n          return Promise.reject({\r\n            errMsg: '位置轨迹上传失败: ' + errorMsg,\r\n            detail: res\r\n          });\r\n        }\r\n      }).catch(err => {\r\n        // 特殊处理：如果是HTTP 201并且包含有效数据，视为成功\r\n        if (err && err.statusCode === 201 && err.data && err.data.code === 200) {\r\n          console.log('位置轨迹上传成功(201):', err.data.data);\r\n          return err.data.data;\r\n        }\r\n        \r\n        console.error('位置轨迹上传请求异常:', err);\r\n        return Promise.reject(err);\r\n      });\r\n    }\r\n  }\r\n}; "], "names": ["attendanceApi", "uni", "err"], "mappings": ";;;AAKA,MAAe,mBAAA;AAAA,EACb,YAAY;AAAA;AAAA,EACZ,OAAO;AAAA;AAAA,IAEL,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,sBAAsB;AAAA,IACtB,wBAAwB;AAAA;AAAA,IACxB,UAAU;AAAA,IACV,WAAW;AAAA;AAAA,IAEX,aAAa;AAAA,IACb,cAAc;AAAA,EACf;AAAA,EAED,WAAW;AAAA;AAAA,IAET,0BAA0B,OAAO,OAAO;AACtC,YAAM,sBAAsB;AAAA,IAC7B;AAAA;AAAA,IAGD,0BAA0B,OAAO,QAAQ;AACvC,YAAM,sBAAsB;AAAA,IAC7B;AAAA;AAAA,IAGD,2BAA2B,OAAO,OAAO;AACvC,YAAM,uBAAuB;AAAA,IAC9B;AAAA;AAAA,IAGD,6BAA6B,OAAO,SAAS;AAC3C,YAAM,yBAAyB;AAAA,IAChC;AAAA;AAAA,IAGD,aAAa,OAAO,EAAE,UAAU,UAAS,GAAI;AAC3C,YAAM,WAAW;AACjB,YAAM,YAAY;AAAA,IACnB;AAAA;AAAA,IAGD,kBAAkB,OAAO,EAAE,aAAa,aAAY,GAAI;AACtD,YAAM,cAAc,eAAe;AACnC,YAAM,eAAe,gBAAgB;AAAA,IACtC;AAAA,EACF;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,oBAAoB,EAAE,QAAQ,YAAY;AACxC,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,kBAAAA,cAAc,eAAgB,EAC3B,KAAK,SAAO;AAEX,cAAI,IAAI,SAAS,KAAK;AAEpB,kBAAM,OAAO,IAAI,QAAQ;AACzB,kBAAM,cAAc,KAAK,eAAe;AACxC,kBAAM,eAAe,KAAK,gBAAgB;AAG1C,mBAAO,qBAAqB;AAAA,cAC1B;AAAA,cACA;AAAA,YAChB,CAAe;AAGD,kBAAM,gBAAgBC,cAAG,MAAC,eAAe,eAAe,KAAK;AAI7D,gBAAI,kBAAkB,GAAG;AAGvB,kBAAI,eAAe,gBAAgB,OAAO,CAAC,gBAAgB,iBAAiB,KAAK;AAG/E,yBAAS,qBAAqB;AAAA,cAChD,WAA2B,gBAAgB,iBAAiB,IAAI;AAG9C,yBAAS,oBAAoB;AAAA,cAC/C,OAAuB;AAGL,yBAAS,oBAAoB;AAAA,cAC9B;AAAA,YACjB,OAAqB;AAELA,4BAAAA,MAAA,MAAA,OAAA,6BAAY,oBAAoB;AAChC,uBAAS,oBAAoB;AAAA,YAC9B;AAED,oBAAQ,IAAI;AAAA,UAC1B,OAAmB;AACLA,0BAAA,MAAA,MAAA,SAAA,8BAAc,aAAa,IAAI,OAAO;AACtC,mBAAO,IAAI,MAAM,gBAAgB,IAAI,WAAW,OAAO,CAAC;AAAA,UACzD;AAAA,QACb,CAAW,EACA,MAAM,SAAO;AACZA,wBAAc,MAAA,MAAA,SAAA,8BAAA,eAAe,GAAG;AAChC,iBAAO,GAAG;AAAA,QACtB,CAAW;AAAA,MACX,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,oBAAoB,EAAE,QAAQ,OAAO,SAAQ,GAAI;AAE/C,UAAI,MAAM,qBAAqB;AAC7B;AAAA,MACD;AAGD,UAAI,MAAM,qBAAqB;AAC7B,sBAAc,MAAM,mBAAmB;AAAA,MACxC;AAGD,aAAO,6BAA6B,IAAI;AAGxC,YAAM,sBAAsBA,cAAG,MAAC,eAAe,eAAe,KAAK;AACnE,YAAM,WAAW;AACjB,YAAM,aAAa,WAAW,KAAK;AAEnCA,0BAAY,MAAA,OAAA,8BAAA,kBAAkB,QAAQ,aAAa;AAGnD,eAAS,gBAAgB;AAGzB,YAAM,QAAQ,YAAY,MAAM;AAC9B,iBAAS,gBAAgB;AAAA,MAC1B,GAAE,UAAU;AAGb,aAAO,6BAA6B,KAAK;AAAA,IAC1C;AAAA;AAAA,IAGD,mBAAmB,EAAE,QAAQ,SAAS;AACpC,UAAI,MAAM,qBAAqB;AAC7BA,sBAAAA,iDAAY,UAAU;AACtB,sBAAc,MAAM,mBAAmB;AACvC,eAAO,6BAA6B,IAAI;AAAA,MACzC;AAGD,aAAO,6BAA6B,KAAK;AAAA,IAC1C;AAAA;AAAA,IAGD,eAAe,EAAE,OAAO,YAAY;AAClCA,oBAAAA,iDAAY,gBAAgB;AAG5B,UAAI,MAAM,sBAAsB;AAE9B,iBAAS,mBAAmB,EACzB,KAAK,cAAY;AAEhB,gBAAM,SAAS,SAAS;AACxB,mBAAS,0BAA0B;AAAA,YACjC,UAAU,OAAO;AAAA,YACjB,WAAW,OAAO;AAAA,UAChC,CAAa;AAAA,QACb,CAAW,EACA,MAAM,SAAO;AACZA,2EAAc,uBAAuB,GAAG;AAExC,mBAAS,gBAAgB,EACtB,KAAK,SAAO;AAEX,qBAAS,0BAA0B;AAAA,cACjC,UAAU,IAAI;AAAA,cACd,WAAW,IAAI;AAAA,YACjC,CAAiB;AAAA,UACjB,CAAe,EACA,MAAM,CAAAC,SAAO;AACZD,0BAAA,MAAA,MAAA,SAAA,8BAAc,kBAAkBC,IAAG;AAAA,UACnD,CAAe;AAAA,QACf,CAAW;AAAA,MACX,OAAa;AAEL,iBAAS,gBAAgB,EACtB,KAAK,SAAO;AAEX,mBAAS,0BAA0B;AAAA,YACjC,UAAU,IAAI;AAAA,YACd,WAAW,IAAI;AAAA,UAC7B,CAAa;AAAA,QACb,CAAW,EACA,MAAM,SAAO;AACZD,wBAAA,MAAA,MAAA,SAAA,8BAAc,kBAAkB,GAAG;AAAA,QAC/C,CAAW;AAAA,MACJ;AAAA,IACF;AAAA;AAAA,IAGD,kBAAkB,EAAE,YAAY;AAC9B,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAsCtC,iBAAS,gBAAgB,EAAE,KAAK,OAAO,EAAE,MAAM,MAAM;AAAA,MAE7D,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB;AACf,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,sBAAAA,MAAI,YAAY;AAAA,UACd,MAAM;AAAA,UACN,gBAAgB;AAAA,UAChB,wBAAwB;AAAA,UACxB,SAAS,CAAC,QAAQ;AAChBA,0BAAA,MAAA,MAAA,OAAA,8BAAY,cAAc,GAAG;AAC7B,oBAAQ,GAAG;AAAA,UACZ;AAAA,UACD,MAAM,CAAC,QAAQ;AACbA,0BAAA,MAAA,MAAA,SAAA,8BAAc,cAAc,GAAG;AAC/B,mBAAO,GAAG;AAAA,UACX;AAAA,QACX,CAAS;AAAA,MACT,CAAO;AAAA,IACF;AAAA;AAAA,IAGD,uBAAuB,EAAE,OAAM,GAAI,EAAE,UAAU,UAAS,GAAI;AAE1D,UAAI,CAAC,aAAa,CAAC,YAAY,cAAc,KAAK,aAAa,GAAG;AAChEA,sBAAAA,MAAA,MAAA,SAAA,8BAAc,mBAAmB;AAAA,UAC/B;AAAA,UACA;AAAA,QACV,CAAS;AACD,eAAO,QAAQ,OAAO,EAAC,QAAQ,SAAQ,CAAC;AAAA,MACzC;AAGD,aAAO,gBAAgB,EAAE,UAAU,UAAW,CAAA;AAG9C,YAAM,SAASA,cAAAA,MAAI,eAAe,QAAQ;AAC1C,UAAI,CAAC,QAAQ;AACXA,sBAAAA,MAAA,MAAA,QAAA,8BAAa,kBAAkB;AAC/B,eAAO,QAAQ,OAAO,EAAC,QAAQ,UAAS,CAAC;AAAA,MAC1C;AAEDA,oBAAAA,MAAA,MAAA,OAAA,8BAAY,WAAW;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,MACR,CAAO;AAGD,aAAOD,UAAAA,cAAc,uBAAuB;AAAA,QAC1C;AAAA;AAAA,QACA,YAAY;AAAA;AAAA,QACZ;AAAA,QACA;AAAA,MACR,CAAO,EAAE,KAAK,SAAO;AAEb,YAAI,OAAO,IAAI,SAAS,KAAK;AAC3BC,wBAAY,MAAA,MAAA,OAAA,8BAAA,aAAa,IAAI,IAAI;AACjC,iBAAO,IAAI;AAAA,QACrB,OAAe;AACL,gBAAM,WAAY,OAAO,IAAI,UAAW,IAAI,UAAU;AACtDA,wBAAA,MAAA,MAAA,SAAA,8BAAc,aAAa,UAAU,GAAG;AACxC,iBAAO,QAAQ,OAAO;AAAA,YACpB,QAAQ,eAAe;AAAA,YACvB,QAAQ;AAAA,UACpB,CAAW;AAAA,QACF;AAAA,MACT,CAAO,EAAE,MAAM,SAAO;AAEd,YAAI,OAAO,IAAI,eAAe,OAAO,IAAI,QAAQ,IAAI,KAAK,SAAS,KAAK;AACtEA,yEAAY,kBAAkB,IAAI,KAAK,IAAI;AAC3C,iBAAO,IAAI,KAAK;AAAA,QACjB;AAEDA,sBAAA,MAAA,MAAA,SAAA,8BAAc,eAAe,GAAG;AAChC,eAAO,QAAQ,OAAO,GAAG;AAAA,MACjC,CAAO;AAAA,IACF;AAAA,EACF;AACH;;"}