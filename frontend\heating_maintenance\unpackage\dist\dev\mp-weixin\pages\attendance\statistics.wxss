/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.statistics-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
}
.filter-section .month-selector {
  display: flex;
  align-items: center;
}
.filter-section .month-selector .current-month {
  font-size: 32rpx;
  font-weight: bold;
  margin-right: 10rpx;
}
.filter-section .month-selector .month-arrows {
  display: flex;
  align-items: center;
}
.filter-section .staff-selector {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
}
.filter-section .staff-selector text {
  margin-right: 10rpx;
}
.statistics-cards {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.statistics-cards .stat-card {
  flex: 1;
  margin: 0 10rpx;
  background-color: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);
}
.statistics-cards .stat-card:first-child {
  margin-left: 0;
}
.statistics-cards .stat-card:last-child {
  margin-right: 0;
}
.statistics-cards .stat-card .stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.statistics-cards .stat-card .stat-label {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}
.statistics-cards .stat-card:nth-child(1) .stat-value {
  color: #4cd964;
}
.statistics-cards .stat-card:nth-child(2) .stat-value {
  color: #f0ad4e;
}
.statistics-cards .stat-card:nth-child(3) .stat-value {
  color: #5bc0de;
}
.statistics-cards .stat-card:nth-child(4) .stat-value {
  color: #dd524d;
}
.main-calendar {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);
}
.main-calendar .calendar-header {
  display: flex;
  margin-bottom: 20rpx;
}
.main-calendar .calendar-header .header-item {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #666;
}
.main-calendar .calendar-days {
  display: flex;
  flex-wrap: wrap;
}
.main-calendar .calendar-days .calendar-day {
  width: 14.2857142857%;
  height: 80rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  margin-bottom: 10rpx;
}
.main-calendar .calendar-days .calendar-day .day-number {
  font-size: 28rpx;
  color: #333;
}
.main-calendar .calendar-days .calendar-day.current-month .day-number {
  color: #007AFF;
}
.main-calendar .calendar-days .calendar-day.today .day-number {
  width: 60rpx;
  height: 60rpx;
  background-color: #007AFF;
  color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.main-calendar .calendar-days .calendar-day.normal .day-number {
  color: #4cd964;
}
.main-calendar .calendar-days .calendar-day.late .day-number {
  color: #f0ad4e;
}
.main-calendar .calendar-days .calendar-day.early .day-number {
  color: #5bc0de;
}
.main-calendar .calendar-days .calendar-day.absent .day-number {
  color: #dd524d;
}
.charts-section {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);
}
.charts-section .section-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.charts-section .charts-container {
  display: flex;
  flex-direction: column;
}
.charts-section .charts-container .chart-card {
  width: 100%;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 10rpx;
  text-align: center;
}
.charts-section .charts-container .chart-card .chart-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.charts-section .charts-container .chart-card .chart-container {
  height: 400rpx;
  width: 100%;
  position: relative;
  padding: 10rpx 0 30rpx 0;
}
.charts-section .charts-container .chart-card .chart-container canvas {
  width: 100%;
  height: 100%;
}
.attendance-records {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);
}
.attendance-records .record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.attendance-records .record-header text {
  font-size: 30rpx;
  font-weight: bold;
}
.attendance-records .record-header .view-all {
  font-size: 26rpx;
  color: #007AFF;
  font-weight: normal;
}
.attendance-records .record-list .record-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}
.attendance-records .record-list .record-item:last-child {
  border-bottom: none;
}
.attendance-records .record-list .record-item .record-date {
  width: 100rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.attendance-records .record-list .record-item .record-date .day {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.attendance-records .record-list .record-item .record-date .week {
  font-size: 24rpx;
  color: #999;
  margin-top: 5rpx;
}
.attendance-records .record-list .record-item .record-time {
  flex: 1;
  margin-left: 20rpx;
}
.attendance-records .record-list .record-item .record-time .time-item {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.attendance-records .record-list .record-item .record-time .time-item .time-label {
  width: 120rpx;
  font-size: 26rpx;
  color: #999;
}
.attendance-records .record-list .record-item .record-time .time-item .time-value {
  font-size: 28rpx;
  color: #333;
  margin-right: 10rpx;
}
.attendance-records .record-list .record-item .record-time .time-item .time-value.abnormal {
  color: #f0ad4e;
}
.attendance-records .record-list .record-item .record-time .time-item .status-tag {
  font-size: 22rpx;
  padding: 2rpx 10rpx;
  border-radius: 20rpx;
}
.attendance-records .record-list .record-item .record-time .time-item .status-tag.late {
  background-color: #fef0e5;
  color: #f0ad4e;
}
.attendance-records .record-list .record-item .record-time .time-item .status-tag.early {
  background-color: #e5f5fa;
  color: #5bc0de;
}
.attendance-records .record-list .record-item .record-location {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-top: 10rpx;
}
.attendance-records .record-list .record-item .record-location .location-label {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 5rpx;
}
.attendance-records .record-list .record-item .record-location .location-value {
  font-size: 26rpx;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.attendance-records .empty-records {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}
.attendance-records .empty-records image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.attendance-records .empty-records text {
  font-size: 28rpx;
  color: #999;
}
.staff-popup {
  background-color: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  padding-bottom: 30rpx;
}
.staff-popup .popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}
.staff-popup .popup-header text {
  font-size: 32rpx;
  font-weight: bold;
}
.staff-popup .popup-header .close-btn {
  font-size: 28rpx;
  color: #007AFF;
}
.staff-popup .search-box {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  margin: 20rpx 30rpx;
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
}
.staff-popup .search-box input {
  flex: 1;
  height: 60rpx;
  margin-left: 10rpx;
  font-size: 28rpx;
}
.staff-popup .staff-list {
  max-height: 600rpx;
}
.staff-popup .staff-list .staff-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}
.staff-popup .staff-list .staff-item text {
  font-size: 30rpx;
  color: #333;
}

/* 日考勤详情弹窗样式 */
.day-detail-popup {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 30rpx;
  max-height: 80vh;
  overflow-y: auto;
}
.popup-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
}
.close-btn {
  color: #999;
  font-size: 28rpx;
}
.day-stats-chart {
  width: 100%;
  height: 350rpx;
  margin-bottom: 20rpx;
}
.day-stats-summary {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
  border-radius: 10rpx;
  background-color: #f8f8f8;
  padding: 20rpx 10rpx;
}
.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 25%;
}
.stat-value {
  font-size: 34rpx;
  font-weight: bold;
  margin-bottom: 6rpx;
}
.stat-item.normal .stat-value {
  color: #4cd964;
}
.stat-item.late .stat-value {
  color: #f0ad4e;
}
.stat-item.early .stat-value {
  color: #f56c6c;
}
.stat-item.absent .stat-value {
  color: #dd524d;
}
.stat-label {
  font-size: 24rpx;
  color: #666;
}
.section-title {
  font-size: 28rpx;
  font-weight: bold;
  margin: 20rpx 0;
  color: #333;
  border-left: 8rpx solid #007aff;
  padding-left: 20rpx;
}
.staff-detail-section {
  margin-bottom: 30rpx;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  padding: 20rpx;
}
.staff-list-section {
  margin-bottom: 30rpx;
}
.staff-category {
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  overflow: hidden;
  background-color: #f8f8f8;
}
.category-header {
  display: flex;
  justify-content: space-between;
  padding: 15rpx 20rpx;
  font-size: 26rpx;
  color: #fff;
}
.category-header.normal {
  background-color: #4cd964;
}
.category-header.late {
  background-color: #f0ad4e;
}
.category-header.early {
  background-color: #f56c6c;
}
.category-header.absent {
  background-color: #dd524d;
}
.staff-items {
  padding: 10rpx 20rpx;
}
.staff-item {
  display: flex;
  justify-content: space-between;
  padding: 15rpx 0;
  border-bottom: 1px solid #eee;
}
.staff-item:last-child {
  border-bottom: none;
}
.staff-name {
  font-size: 26rpx;
  color: #333;
}
.staff-time {
  font-size: 24rpx;
  color: #666;
}
.empty-day-detail {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50rpx 0;
}
.empty-day-detail image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.empty-day-detail text {
  font-size: 28rpx;
  color: #999;
}

/* 现有的日详情内容样式 */
.day-detail-content {
  background-color: #fff;
  border-radius: 10rpx;
}
.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 15rpx 0;
  border-bottom: 1px solid #eee;
}
.detail-label {
  color: #666;
  font-size: 26rpx;
}
.detail-value {
  color: #333;
  font-size: 26rpx;
}
.detail-value-box {
  display: flex;
  align-items: center;
}
.status-tag {
  display: inline-block;
  padding: 2rpx 10rpx;
  font-size: 22rpx;
  border-radius: 6rpx;
  margin-left: 10rpx;
  color: #fff;
}
.status-tag.late {
  background-color: #f0ad4e;
}
.status-tag.early {
  background-color: #f56c6c;
}
.detail-photos {
  margin-top: 15rpx;
}
.photo-list {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10rpx;
}
.photo-list image {
  width: 160rpx;
  height: 160rpx;
  margin: 10rpx;
  border-radius: 8rpx;
}