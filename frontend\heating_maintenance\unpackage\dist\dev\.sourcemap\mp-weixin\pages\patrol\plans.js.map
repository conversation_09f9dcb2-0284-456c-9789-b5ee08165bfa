{"version": 3, "file": "plans.js", "sources": ["pages/patrol/plans.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcGF0cm9sL3BsYW5zLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"patrol-plans-container\">\r\n    <!-- 搜索栏 -->\r\n    <view class=\"search-filter-area\">\r\n      <view class=\"search-bar\">\r\n        <view class=\"search-input-wrapper\">\r\n<!--          <text class=\"iconfont icon-search\"></text> -->\r\n          <input\r\n            type=\"text\"\r\n            placeholder=\"搜索巡检计划\"\r\n            v-model=\"searchKeyword\"\r\n            confirm-type=\"search\"\r\n            @confirm=\"handleSearch\"\r\n          />\r\n          <text\r\n            class=\"iconfont icon-clear\"\r\n            v-if=\"searchKeyword\"\r\n            @click=\"clearSearch\"\r\n          ></text>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 筛选栏 -->\r\n      <view class=\"filter-bar\">\r\n        <view class=\"filter-item\" @click=\"showStatusFilter\">\r\n          <text>状态: {{ statusFilterText }}</text>\r\n\t\t      <text>▼</text>\r\n     <!--     <text class=\"iconfont icon-arrow-down\"></text> -->\r\n        </view>\r\n        <view class=\"filter-item\" @click=\"showTimeFilter\">\r\n          <text>时间: {{ timeFilterText }}</text>\r\n        <!--  <text class=\"iconfont icon-arrow-down\"></text> -->\r\n\t\t    <text>▼</text>\r\n        </view>\r\n        <view class=\"filter-item\" @click=\"showTypeFilter\">\r\n          <text>类型: {{ typeFilterText }}</text>\r\n         <!-- <text class=\"iconfont icon-arrow-down\"></text> -->\r\n\t\t     <text>▼</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    <!-- 巡检计划列表 -->\r\n    <view class=\"plan-list\" v-if=\"patrolPlans.length > 0\">\r\n      <view\r\n        class=\"plan-item\"\r\n        v-for=\"(plan, index) in patrolPlans\"\r\n        :key=\"index\"\r\n        @click=\"navigateToPlanDetail(plan.id)\"\r\n      >\r\n        <view class=\"plan-header\">\r\n          <text class=\"plan-title\">{{ plan.title }}</text>\r\n          <view class=\"plan-status\" :class=\"plan.status\">{{\r\n            getStatusText(plan.status)\r\n          }}</view>\r\n        </view>\r\n\r\n        <view class=\"plan-info\">\r\n          <view class=\"info-row\">\r\n            <view class=\"info-item full-width\">\r\n              <text class=\"item-label\">计划编号:</text>\r\n              <text class=\"item-value\">{{ plan.code }}</text>\r\n            </view>\r\n         \r\n          </view>\r\n\r\n          <view class=\"info-row\">\r\n            <view class=\"info-item\">\r\n              <text class=\"item-label\">开始时间:</text>\r\n              <text class=\"item-value\">{{ formatDate(plan.startTime) }}</text>\r\n            </view>\r\n            <view class=\"info-item\">\r\n              <text class=\"item-label\">结束时间:</text>\r\n              <text class=\"item-value\">{{ formatDate(plan.endTime) }}</text>\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"info-row\">\r\n\t\t\t  <view class=\"info-item\">\r\n\t\t\t    <text class=\"item-label\">巡检类型:</text>\r\n\t\t\t    <text class=\"item-value\">{{ plan.type }}</text>\r\n\t\t\t  </view>\r\n            <view class=\"info-item\">\r\n              <text class=\"item-label\">巡检地点:</text>\r\n              <text class=\"item-value\">{{ plan.location }}</text>\r\n            </view>\r\n      <!--      <view class=\"info-item\">\r\n              <text class=\"item-label\">负责人:</text>\r\n              <text class=\"item-value\">{{ plan.manager }}</text>\r\n            </view> -->\r\n          </view>\r\n        </view>\r\n\r\n        <view class=\"plan-footer\">\r\n          <view class=\"plan-actions\">\r\n          <!--  <PermissionCheck permission=\"patrol:plans:execute\">\r\n              <view\r\n                v-if=\"plan.isPlay\"\r\n                class=\"action-button start\"\r\n                @click.stop=\"startPatrolPlan(plan.id)\"\r\n              >\r\n                <text class=\"iconfont icon-play\"></text>\r\n                <text>开始巡检</text>\r\n              </view>\r\n\r\n              <view\r\n                v-if=\"plan.status === 'in_progress'\"\r\n                class=\"action-button continue\"\r\n                @click.stop=\"continuePatrolPlan(plan.id)\"\r\n              >\r\n                <text class=\"iconfont icon-continue\"></text>\r\n                <text>继续巡检</text>\r\n              </view>\r\n            </PermissionCheck> -->\r\n\r\n            <view\r\n              class=\"action-button view\"\r\n              @click.stop=\"navigateToPlanDetail(plan.id)\"\r\n            >\r\n               <!-- <text class=\"iconfont icon-detail\"></text> -->\r\n                <text>查看详情</text>\r\n              </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 加载中 -->\r\n    <view class=\"loading-container\" v-if=\"isLoading\">\r\n      <text class=\"loading-text\">加载中...</text>\r\n    </view>\r\n\r\n    <!-- 加载失败 -->\r\n    <view class=\"error-container\" v-if=\"loadError && !isLoading\">\r\n      <text class=\"error-text\">加载失败，请重试</text>\r\n      <button class=\"retry-btn\" @click=\"loadPatrolPlans\">重新加载</button>\r\n    </view>\r\n\r\n    <!-- 空状态 -->\r\n    <view class=\"empty-state\" v-if=\"!isLoading && patrolPlans.length === 0\">\r\n      <image class=\"empty-image\" src=\"/static/images/empty.png\" mode=\"aspectFit\"></image>\r\n      <text class=\"empty-text\">暂无巡检计划</text>\r\n    </view>\r\n\r\n    <!-- 状态筛选弹窗 -->\r\n    <uni-popup ref=\"statusFilterPopup\" type=\"bottom\">\r\n      <view class=\"filter-popup\">\r\n        <view class=\"popup-header\">\r\n          <text class=\"popup-title\">状态筛选</text>\r\n          <text class=\"popup-close\" @click=\"$refs.statusFilterPopup.close()\">关闭</text>\r\n        </view>\r\n        <view class=\"popup-content\">\r\n          <view\r\n            class=\"filter-option\"\r\n            v-for=\"(option, index) in statusOptions\"\r\n            :key=\"index\"\r\n            :class=\"{ active: statusFilter === option.value }\"\r\n            @click=\"selectStatusFilter(option.value)\"\r\n          >\r\n            {{ option.label }}\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </uni-popup>\r\n\r\n    <!-- 时间筛选弹窗 -->\r\n    <uni-popup ref=\"timeFilterPopup\" type=\"bottom\">\r\n      <view class=\"filter-popup\">\r\n        <view class=\"popup-header\">\r\n          <text class=\"popup-title\">时间筛选</text>\r\n          <text class=\"popup-close\" @click=\"$refs.timeFilterPopup.close()\">关闭</text>\r\n        </view>\r\n        <view class=\"popup-content\">\r\n          <view\r\n            class=\"filter-option\"\r\n            v-for=\"(option, index) in timeOptions\"\r\n            :key=\"index\"\r\n            :class=\"{ active: timeFilter === option.value }\"\r\n            @click=\"selectTimeFilter(option.value)\"\r\n          >\r\n            {{ option.label }}\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </uni-popup>\r\n\r\n    <!-- 类型筛选弹窗 -->\r\n    <uni-popup ref=\"typeFilterPopup\" type=\"bottom\">\r\n      <view class=\"filter-popup\">\r\n        <view class=\"popup-header\">\r\n          <text class=\"popup-title\">类型筛选</text>\r\n          <text class=\"popup-close\" @click=\"$refs.typeFilterPopup.close()\">关闭</text>\r\n        </view>\r\n        <view class=\"popup-content\">\r\n          <view\r\n            class=\"filter-option\"\r\n            v-for=\"(option, index) in typeOptions\"\r\n            :key=\"index\"\r\n            :class=\"{ active: typeFilter === option.value }\"\r\n            @click=\"selectTypeFilter(option.value)\"\r\n          >\r\n            {{ option.label }}\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </uni-popup>\r\n\r\n    <!-- 底部新建按钮 -->\r\n    <PermissionCheck permission=\"patrol:plans:add\">\r\n      <view class=\"floating-button\" @click=\"navigateToCreatePlan\">\r\n        <text class=\"plus-icon\">+</text>\r\n      </view>\r\n    </PermissionCheck>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { patrolApi } from \"@/utils/api.js\";\r\nimport PermissionCheck from \"@/components/PermissionCheck.vue\";\r\n\r\nexport default {\r\n  components: {\r\n    PermissionCheck, // 本地注册组件\r\n  },\r\n  data() {\r\n    return {\r\n      searchKeyword: \"\",\r\n      patrolPlans: [],\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      hasMore: true,\r\n      isLoading: false,\r\n      loadError: false,\r\n\r\n      // 筛选相关\r\n      statusFilter: \"all\",\r\n      timeFilter: \"all\",\r\n      typeFilter: \"all\",\r\n\r\n      // 筛选选项\r\n      statusOptions: [\r\n        { label: \"全部\", value: \"all\" },\r\n        { label: \"待执行\", value: \"pending\" },\r\n        { label: \"进行中\", value: \"processing\" },\r\n        { label: \"已完成\", value: \"completed\" }\r\n      ],\r\n      timeOptions: [\r\n        { label: \"全部时间\", value: \"all\" },\r\n        { label: \"今天\", value: \"today\" },\r\n        { label: \"本周\", value: \"this_week\" },\r\n        { label: \"本月\", value: \"this_month\" },\r\n        { label: \"上个月\", value: \"last_month\" },\r\n      ],\r\n      typeOptions: [\r\n        { label: \"全部类型\", value: \"all\" },\r\n      ],\r\n      patrolTypeOptions: [], // 存储从后端获取的巡检类型选项\r\n    };\r\n  },\r\n  computed: {\r\n    statusFilterText() {\r\n      const option = this.statusOptions.find((item) => item.value === this.statusFilter);\r\n      return option ? option.label : \"全部\";\r\n    },\r\n    timeFilterText() {\r\n      const option = this.timeOptions.find((item) => item.value === this.timeFilter);\r\n      return option ? option.label : \"全部时间\";\r\n    },\r\n    typeFilterText() {\r\n      const option = this.typeOptions.find((item) => item.value === this.typeFilter);\r\n      return option ? option.label : \"全部类型\";\r\n    },\r\n  },\r\n  onPullDownRefresh() {\r\n\tconsole.log(\"执行下拉刷新！\")\r\n    this.currentPage = 1;\r\n    this.patrolPlans = [];\r\n    this.hasMore = true;\r\n    this.loadPatrolPlans().then(() => {\r\n      uni.stopPullDownRefresh();\r\n    });\r\n  },\r\n    // 上拉加载更多\r\n  onReachBottom() {\r\n\tconsole.log(\"执行上拉加载！\")\r\n    if (this.hasMore && !this.isLoading) {\r\n      this.currentPage++;\r\n      this.loadPatrolPlans(true);\r\n    }\r\n  },\r\n  onLoad() {\r\n    this.loadPatrolPlans();\r\n    this.loadPatrolTypeOptions(); // 加载巡检类型选项\r\n  },\r\n  methods: {\r\n    // 加载巡检计划列表\r\n    async loadPatrolPlans(isLoadMore = false) {\r\n      if (this.isLoading) return;\r\n      this.isLoading = true;\r\n      \r\n      if (!isLoadMore) {\r\n        this.loadError = false;\r\n      }\r\n\r\n      try {\r\n        // 准备请求参数\r\n        const params = {\r\n          page: this.currentPage,\r\n          pageSize: this.pageSize,\r\n          name: this.searchKeyword,\r\n          status: this.statusFilter !== \"all\" ? this.statusFilter : null,\r\n          type: this.typeFilter !== \"all\" ? this.typeFilter : null,\r\n          timeRange: this.timeFilter !== \"all\" ? this.timeFilter : null,\r\n        };\r\n\t\t// 添加用户的项目权限IDs (必须参数)\r\n\t\tconst heatUnitId = uni.getStorageSync('heatUnitId');\r\n\t\t\r\n\t\t// 设置热用户ID参数\r\n\t\tparams.heatUnitId = heatUnitId;\r\n        //console.log(\"请求参数:\", params);\r\n        // 调用API获取巡检计划列表\r\n        const res = await patrolApi.getPlanList(params);\r\n        console.log(\"API响应:\", res);\r\n\r\n        if (res.code === 200) {\r\n          // 兼容不同的数据结构\r\n          let planList = [];\r\n          let totalPages = 1;\r\n          if (Array.isArray(res.data)) {\r\n            // 如果直接返回数组\r\n            planList = res.data;\r\n            totalPages = Math.ceil(planList.length / this.pageSize);\r\n          } else if (res.data && res.data.list) {\r\n            // 如果返回对象中包含list字段\r\n            planList = res.data.list;\r\n            totalPages = res.data.totalPages || Math.ceil(res.data.total / this.pageSize) || 1;\r\n          } else if (res.data) {\r\n            // 其他情况，尝试使用res.data\r\n            planList = Array.isArray(res.data) ? res.data : [res.data];\r\n            totalPages = 1;\r\n          }\r\n          \r\n          // 转换为页面需要的格式\r\n          const formattedPlans = planList.map((item) => ({\r\n            id: item.id,\r\n            title: item.name,\r\n            code: item.planNo || item.plan_no,\r\n            type: item.patrolType || item.patrol_type,\r\n            startTime: item.startDate || item.start_date,\r\n            endTime: item.endDate || item.end_date,\r\n            location: item.locations,\r\n            manager: item.executors?.join(\"、\") || \"未分配\",\r\n            status: item.status,\r\n            isPlay: item.isPlay\r\n          }));\r\n\r\n          if (isLoadMore) {\r\n            // 加载更多模式：追加数据\r\n            this.patrolPlans = [...this.patrolPlans, ...formattedPlans];\r\n          } else {\r\n            // 刷新模式：替换数据\r\n            this.patrolPlans = formattedPlans;\r\n          }\r\n\r\n          // 判断是否还有更多数据\r\n          this.hasMore = this.currentPage < totalPages;\r\n        } else {\r\n          this.loadError = true;\r\n          uni.showToast({\r\n            title: res.message || \"获取巡检计划失败\",\r\n            icon: \"none\"\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取巡检计划列表失败:\", error);\r\n        this.loadError = true;\r\n        uni.showToast({\r\n          title: \"网络异常，请稍后重试\",\r\n          icon: \"none\"\r\n        });\r\n      } finally {\r\n        this.isLoading = false;\r\n      }\r\n    },\r\n\r\n    // 搜索处理\r\n    handleSearch() {\r\n      this.currentPage = 1;\r\n      this.patrolPlans = [];\r\n      this.hasMore = true;\r\n      this.loadPatrolPlans();\r\n    },\r\n    \r\n    // 加载巡检类型选项\r\n    loadPatrolTypeOptions() {\r\n      import(\"@/utils/api.js\").then(({ dictApi }) => {\r\n        dictApi.getDictDataByDictId(11)\r\n          .then(res => {\r\n            if (res.code === 200 && res.data) {\r\n              this.patrolTypeOptions = res.data.map(item => ({\r\n                label: item.name,\r\n                value: item.name\r\n              }));\r\n              \r\n              // 将获取到的选项添加到typeOptions中\r\n              this.typeOptions = [\r\n                { label: \"全部类型\", value: \"all\" },\r\n                ...this.patrolTypeOptions\r\n              ];\r\n              \r\n              // 如果没有数据，使用默认选项\r\n              if (this.patrolTypeOptions.length === 0) {\r\n                this.typeOptions = [\r\n                  { label: \"全部类型\", value: \"all\" },\r\n                  { label: \"日常巡检\", value: \"日常巡检\" },\r\n                  { label: \"设备巡检\", value: \"设备巡检\" },\r\n                  { label: \"管道巡检\", value: \"管道巡检\" },\r\n                  { label: \"阀门巡检\", value: \"阀门巡检\" },\r\n                  { label: \"换热站巡检\", value: \"换热站巡检\" }\r\n                ];\r\n              }\r\n            }\r\n          })\r\n          .catch(err => {\r\n            console.error('获取巡检类型选项失败:', err);\r\n            // 加载失败时使用默认选项\r\n            this.typeOptions = [\r\n              { label: \"全部类型\", value: \"all\" },\r\n              { label: \"日常巡检\", value: \"日常巡检\" },\r\n              { label: \"设备巡检\", value: \"设备巡检\" },\r\n              { label: \"管道巡检\", value: \"管道巡检\" },\r\n              { label: \"阀门巡检\", value: \"阀门巡检\" },\r\n              { label: \"换热站巡检\", value: \"换热站巡检\" }\r\n            ];\r\n          });\r\n      }).catch(err => {\r\n        console.error('导入dictApi失败:', err);\r\n        // 导入失败时使用默认选项\r\n        this.typeOptions = [\r\n          { label: \"全部类型\", value: \"all\" },\r\n          { label: \"日常巡检\", value: \"日常巡检\" },\r\n          { label: \"设备巡检\", value: \"设备巡检\" },\r\n          { label: \"管道巡检\", value: \"管道巡检\" },\r\n          { label: \"阀门巡检\", value: \"阀门巡检\" },\r\n          { label: \"换热站巡检\", value: \"换热站巡检\" }\r\n        ];\r\n      });\r\n    },\r\n\r\n    // 清除搜索关键词\r\n    clearSearch() {\r\n      this.searchKeyword = \"\";\r\n      this.currentPage = 1;\r\n      this.patrolPlans = [];\r\n      this.hasMore = true;\r\n      this.loadPatrolPlans();\r\n    },\r\n\r\n    // 显示状态筛选弹窗\r\n    showStatusFilter() {\r\n      this.$refs.statusFilterPopup.open();\r\n    },\r\n\r\n    // 显示时间筛选弹窗\r\n    showTimeFilter() {\r\n      this.$refs.timeFilterPopup.open();\r\n    },\r\n\r\n    // 显示类型筛选弹窗\r\n    showTypeFilter() {\r\n      this.$refs.typeFilterPopup.open();\r\n    },\r\n\r\n    // 选择状态筛选\r\n    selectStatusFilter(value) {\r\n      this.statusFilter = value;\r\n      this.currentPage = 1;\r\n      this.patrolPlans = [];\r\n      this.hasMore = true;\r\n      this.loadPatrolPlans();\r\n      this.$refs.statusFilterPopup.close();\r\n    },\r\n\r\n    // 选择时间筛选\r\n    selectTimeFilter(value) {\r\n      this.timeFilter = value;\r\n      this.currentPage = 1;\r\n      this.patrolPlans = [];\r\n      this.hasMore = true;\r\n      this.loadPatrolPlans();\r\n      this.$refs.timeFilterPopup.close();\r\n    },\r\n\r\n    // 选择类型筛选\r\n    selectTypeFilter(value) {\r\n      this.typeFilter = value;\r\n      this.currentPage = 1;\r\n      this.patrolPlans = [];\r\n      this.hasMore = true;\r\n      this.loadPatrolPlans();\r\n      this.$refs.typeFilterPopup.close();\r\n    },\r\n\r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        pending: \"待执行\",\r\n        processing: \"进行中\",\r\n        completed: \"已完成\"\r\n      };\r\n      return statusMap[status] || \"未知\";\r\n    },\r\n\r\n    // 导航到巡检计划详情\r\n    navigateToPlanDetail(id) {\r\n      uni.navigateTo({\r\n        url: `/pages/patrol/detail?id=${id}`,\r\n      });\r\n    },\r\n\r\n    // 导航到创建巡检计划\r\n    navigateToCreatePlan() {\r\n      uni.navigateTo({\r\n        url: \"/pages/patrol/create\",\r\n      });\r\n    },\r\n\r\n    // 开始巡检\r\n    startPatrolPlan(id) {\r\n      uni.navigateTo({\r\n        url: `/pages/patrol/execute?id=${id}`,\r\n      });\r\n    },\r\n\r\n    // 继续巡检\r\n    continuePatrolPlan(id) {\r\n      uni.navigateTo({\r\n        url: `/pages/patrol/execute?id=${id}`,\r\n      });\r\n    },\r\n\r\n    // 格式化日期\r\n    formatDate(dateStr) {\r\n      if (!dateStr) return \"\";\r\n      const date = new Date(dateStr);\r\n      if (isNaN(date.getTime())) return dateStr;\r\n\r\n      const year = date.getFullYear();\r\n      const month = (date.getMonth() + 1).toString().padStart(2, \"0\");\r\n      const day = date.getDate().toString().padStart(2, \"0\");\r\n\r\n      return `${year}-${month}-${day}`;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.patrol-plans-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 100vh;\r\n  background-color: #f5f5f5;\r\n  position: relative;\r\n  padding-top: 0;\r\n  padding-bottom: 120rpx; // 为底部按钮留出空间\r\n}\r\n\r\n/* 页面标题 */\r\n.page-title {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 90rpx;\r\n  background-color: #0088ff;\r\n  color: #ffffff;\r\n  font-size: 34rpx;\r\n  font-weight: bold;\r\n  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 999;\r\n}\r\n\r\n/* 搜索和筛选区域样式 */\r\n.search-filter-area {\r\n  background-color: #fff;\r\n  border-bottom: 1rpx solid #eeeeee;\r\n  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);\r\n  position: sticky;\r\n  top: 0rpx;\r\n  z-index: 999;\r\n}\r\n\r\n/* 搜索栏样式 */\r\n.search-bar {\r\n  padding: 20rpx 24rpx 16rpx;\r\n\r\n  .search-input-wrapper {\r\n    display: flex;\r\n    align-items: center;\r\n    background-color: #f5f5f5;\r\n    border-radius: 36rpx;\r\n    padding: 14rpx 24rpx;\r\n\r\n    .iconfont {\r\n      font-size: 28rpx;\r\n      color: #999;\r\n    }\r\n\r\n    input {\r\n      flex: 1;\r\n      height: 40rpx;\r\n      font-size: 28rpx;\r\n      margin: 0 16rpx;\r\n      color: #333;\r\n    }\r\n  }\r\n}\r\n\r\n/* 筛选栏样式 */\r\n.filter-bar {\r\n  display: flex;\r\n  padding: 0;\r\n  margin: 0 12rpx 16rpx;\r\n  height: 70rpx;\r\n\r\n  .filter-item {\r\n    flex: 1;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    font-size: 26rpx;\r\n    color: #333;\r\n    height: 100%;\r\n    position: relative;\r\n\r\n    &:after {\r\n      content: \"\";\r\n      position: absolute;\r\n      right: 0;\r\n      top: 20%;\r\n      height: 60%;\r\n      width: 1rpx;\r\n      background-color: #eee;\r\n    }\r\n\r\n    &:last-child:after {\r\n      display: none;\r\n    }\r\n\r\n    text {\r\n      margin-right: 6rpx;\r\n    }\r\n\r\n    .iconfont {\r\n      font-size: 22rpx;\r\n      color: #999;\r\n      margin-left: 4rpx;\r\n    }\r\n  }\r\n}\r\n\r\n.plan-list {\r\n  padding: 20rpx;\r\n\r\n  .plan-item {\r\n    background-color: #fff;\r\n    border-radius: 12rpx;\r\n    margin-bottom: 20rpx;\r\n    padding: 24rpx;\r\n    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n    width: 100%;\r\n    box-sizing: border-box;\r\n    border: 1rpx solid #eeeeee;\r\n\r\n    .plan-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 16rpx;\r\n\r\n      .plan-title {\r\n        font-size: 30rpx;\r\n        font-weight: bold;\r\n        color: $uni-text-color;\r\n        max-width: 65%;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .plan-status {\r\n        padding: 4rpx 16rpx;\r\n        border-radius: 24rpx;\r\n        font-size: 24rpx;\r\n        font-weight: normal;\r\n\r\n        &.pending {\r\n          background-color: rgba(24, 144, 255, 0.08);\r\n          color: #FFA500\r\n;\r\n        }\r\n\r\n        &.processing {\r\n          background-color:  rgba(24, 144, 255, 0.15);\r\n          color: #1E90FF;\r\n        }\r\n\r\n        &.completed {\r\n          background-color: rgba(82, 196, 26, 0.08);\r\n          color: #52c41a;\r\n        }\r\n\r\n        &.overdue {\r\n          background-color: rgba(245, 34, 45, 0.08);\r\n          color: #f5222d;\r\n        }\r\n\r\n        &.canceled {\r\n          background-color: rgba(102, 102, 102, 0.08);\r\n          color: #999999;\r\n        }\r\n      }\r\n    }\r\n\r\n    .plan-info {\r\n      padding: 16rpx 0;\r\n      border-top: 1rpx solid #f2f2f2;\r\n      border-bottom: 1rpx solid #f2f2f2;\r\n      margin-bottom: 16rpx;\r\n\r\n      .info-row {\r\n        display: flex;\r\n        margin-bottom: 16rpx;\r\n        flex-wrap: wrap;\r\n\r\n        &:last-child {\r\n          margin-bottom: 0;\r\n        }\r\n\r\n        .info-item {\r\n          flex: 0 0 50%;\r\n          display: flex;\r\n          align-items: flex-start;\r\n          min-width: 0;\r\n          box-sizing: border-box;\r\n          padding-right: 15rpx;\r\n          margin-bottom: 10rpx;\r\n          overflow: hidden;\r\n          \r\n          &.full-width {\r\n            flex: 0 0 100%;\r\n          }\r\n\r\n          .item-label {\r\n            width: 130rpx;\r\n            flex-shrink: 0;\r\n            font-size: 26rpx;\r\n            color: #888888;\r\n          }\r\n\r\n          .item-value {\r\n            flex: 1;\r\n            font-size: 26rpx;\r\n            color: #333333;\r\n            overflow: visible;\r\n            word-break: break-all;\r\n            white-space: normal;\r\n            max-width: calc(100% - 130rpx);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .plan-footer {\r\n      .plan-actions {\r\n        display: flex;\r\n        justify-content: flex-end;\r\n        flex-wrap: wrap;\r\n\r\n        .action-button {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 8rpx 20rpx;\r\n          border-radius: 28rpx;\r\n          margin-left: 16rpx;\r\n          margin-bottom: 8rpx;\r\n          transition: all 0.2s;\r\n\r\n          &:active {\r\n            opacity: 0.8;\r\n          }\r\n\r\n          .iconfont {\r\n            font-size: 24rpx;\r\n            margin-right: 8rpx;\r\n          }\r\n\r\n          text {\r\n            font-size: 26rpx;\r\n          }\r\n\r\n          &.start {\r\n            background-color: rgba(24, 144, 255, 0.1);\r\n            color: #1890ff;\r\n          }\r\n\r\n          &.continue {\r\n            background-color:  rgba(82, 196, 26, 0.15);\r\n            color: #52c41a;\r\n          }\r\n\r\n          &.view {\r\n            background-color: #f5f5f5;\r\n            color: #666666;\r\n            margin-right: 0;\r\n            padding: 8rpx 20rpx;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 加载状态\r\n.loading-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  padding: 30rpx 0;\r\n\r\n  .loading-text {\r\n    font-size: 28rpx;\r\n    color: #999;\r\n  }\r\n}\r\n\r\n// 错误状态\r\n.error-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 50rpx 0;\r\n\r\n  .error-text {\r\n    font-size: 28rpx;\r\n    color: #f5222d;\r\n    margin-bottom: 20rpx;\r\n  }\r\n\r\n  .retry-btn {\r\n    font-size: 28rpx;\r\n    color: #fff;\r\n    background-color: #1890ff;\r\n    padding: 8rpx 30rpx;\r\n    border-radius: 30rpx;\r\n  }\r\n}\r\n\r\n// 空状态\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 100rpx 0;\r\n\r\n  .empty-image {\r\n    width: 200rpx;\r\n    height: 200rpx;\r\n    margin-bottom: 30rpx;\r\n  }\r\n\r\n  .empty-text {\r\n    font-size: 28rpx;\r\n    color: #999;\r\n  }\r\n}\r\n\r\n.filter-popup {\r\n  background-color: #fff;\r\n  border-top-left-radius: 20rpx;\r\n  border-top-right-radius: 20rpx;\r\n\r\n  .popup-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 30rpx;\r\n    border-bottom: 1rpx solid #eee;\r\n\r\n    .popup-title {\r\n      font-size: 32rpx;\r\n      font-weight: bold;\r\n    }\r\n\r\n    .popup-close {\r\n      font-size: 28rpx;\r\n      color: $uni-color-primary;\r\n    }\r\n  }\r\n\r\n  .popup-content {\r\n    padding: 20rpx 30rpx;\r\n    max-height: 600rpx;\r\n    overflow-y: auto;\r\n\r\n    .filter-option {\r\n      padding: 20rpx 0;\r\n      font-size: 30rpx;\r\n      color: $uni-text-color;\r\n      border-bottom: 1rpx solid #eee;\r\n\r\n      &.active {\r\n        color: $uni-color-primary;\r\n        font-weight: bold;\r\n      }\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.floating-button {\r\n  position: fixed;\r\n  right: 30rpx;\r\n  bottom: 30rpx;\r\n  width: 100rpx;\r\n  height: 100rpx;\r\n  border-radius: 50%;\r\n  background-color: #0088ff;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  box-shadow: 0 4rpx 16rpx rgba(0, 136, 255, 0.4);\r\n  z-index: 10;\r\n\r\n  .plus-icon {\r\n    font-size: 60rpx;\r\n    color: #fff;\r\n    font-weight: normal;\r\n    line-height: 60rpx;\r\n    margin-top: -3rpx;\r\n  }\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/patrol/plans.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "patrolApi"], "mappings": ";;;;AAyNA,MAAK,kBAAmB,MAAW;AAEnC,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,eAAe;AAAA,MACf,aAAa,CAAE;AAAA,MACf,aAAa;AAAA,MACb,UAAU;AAAA,MACV,SAAS;AAAA,MACT,WAAW;AAAA,MACX,WAAW;AAAA;AAAA,MAGX,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA;AAAA,MAGZ,eAAe;AAAA,QACb,EAAE,OAAO,MAAM,OAAO,MAAO;AAAA,QAC7B,EAAE,OAAO,OAAO,OAAO,UAAW;AAAA,QAClC,EAAE,OAAO,OAAO,OAAO,aAAc;AAAA,QACrC,EAAE,OAAO,OAAO,OAAO,YAAY;AAAA,MACpC;AAAA,MACD,aAAa;AAAA,QACX,EAAE,OAAO,QAAQ,OAAO,MAAO;AAAA,QAC/B,EAAE,OAAO,MAAM,OAAO,QAAS;AAAA,QAC/B,EAAE,OAAO,MAAM,OAAO,YAAa;AAAA,QACnC,EAAE,OAAO,MAAM,OAAO,aAAc;AAAA,QACpC,EAAE,OAAO,OAAO,OAAO,aAAc;AAAA,MACtC;AAAA,MACD,aAAa;AAAA,QACX,EAAE,OAAO,QAAQ,OAAO,MAAO;AAAA,MAChC;AAAA,MACD,mBAAmB,CAAE;AAAA;AAAA;EAExB;AAAA,EACD,UAAU;AAAA,IACR,mBAAmB;AACjB,YAAM,SAAS,KAAK,cAAc,KAAK,CAAC,SAAS,KAAK,UAAU,KAAK,YAAY;AACjF,aAAO,SAAS,OAAO,QAAQ;AAAA,IAChC;AAAA,IACD,iBAAiB;AACf,YAAM,SAAS,KAAK,YAAY,KAAK,CAAC,SAAS,KAAK,UAAU,KAAK,UAAU;AAC7E,aAAO,SAAS,OAAO,QAAQ;AAAA,IAChC;AAAA,IACD,iBAAiB;AACf,YAAM,SAAS,KAAK,YAAY,KAAK,CAAC,SAAS,KAAK,UAAU,KAAK,UAAU;AAC7E,aAAO,SAAS,OAAO,QAAQ;AAAA,IAChC;AAAA,EACF;AAAA,EACD,oBAAoB;AACrBA,kBAAAA,MAAA,MAAA,OAAA,iCAAY,SAAS;AAClB,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,kBAAkB,KAAK,MAAM;AAChCA,oBAAG,MAAC,oBAAmB;AAAA,IACzB,CAAC;AAAA,EACF;AAAA;AAAA,EAED,gBAAgB;AACjBA,kBAAAA,MAAA,MAAA,OAAA,iCAAY,SAAS;AAClB,QAAI,KAAK,WAAW,CAAC,KAAK,WAAW;AACnC,WAAK;AACL,WAAK,gBAAgB,IAAI;AAAA,IAC3B;AAAA,EACD;AAAA,EACD,SAAS;AACP,SAAK,gBAAe;AACpB,SAAK,sBAAqB;AAAA,EAC3B;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,MAAM,gBAAgB,aAAa,OAAO;AACxC,UAAI,KAAK;AAAW;AACpB,WAAK,YAAY;AAEjB,UAAI,CAAC,YAAY;AACf,aAAK,YAAY;AAAA,MACnB;AAEA,UAAI;AAEF,cAAM,SAAS;AAAA,UACb,MAAM,KAAK;AAAA,UACX,UAAU,KAAK;AAAA,UACf,MAAM,KAAK;AAAA,UACX,QAAQ,KAAK,iBAAiB,QAAQ,KAAK,eAAe;AAAA,UAC1D,MAAM,KAAK,eAAe,QAAQ,KAAK,aAAa;AAAA,UACpD,WAAW,KAAK,eAAe,QAAQ,KAAK,aAAa;AAAA;AAGjE,cAAM,aAAaA,cAAAA,MAAI,eAAe,YAAY;AAGlD,eAAO,aAAa;AAGd,cAAM,MAAM,MAAMC,UAAAA,UAAU,YAAY,MAAM;AAC9CD,sBAAY,MAAA,MAAA,OAAA,iCAAA,UAAU,GAAG;AAEzB,YAAI,IAAI,SAAS,KAAK;AAEpB,cAAI,WAAW,CAAA;AACf,cAAI,aAAa;AACjB,cAAI,MAAM,QAAQ,IAAI,IAAI,GAAG;AAE3B,uBAAW,IAAI;AACf,yBAAa,KAAK,KAAK,SAAS,SAAS,KAAK,QAAQ;AAAA,UACxD,WAAW,IAAI,QAAQ,IAAI,KAAK,MAAM;AAEpC,uBAAW,IAAI,KAAK;AACpB,yBAAa,IAAI,KAAK,cAAc,KAAK,KAAK,IAAI,KAAK,QAAQ,KAAK,QAAQ,KAAK;AAAA,UACnF,WAAW,IAAI,MAAM;AAEnB,uBAAW,MAAM,QAAQ,IAAI,IAAI,IAAI,IAAI,OAAO,CAAC,IAAI,IAAI;AACzD,yBAAa;AAAA,UACf;AAGA,gBAAM,iBAAiB,SAAS,IAAI,CAAC,SAAU;;AAAA;AAAA,cAC7C,IAAI,KAAK;AAAA,cACT,OAAO,KAAK;AAAA,cACZ,MAAM,KAAK,UAAU,KAAK;AAAA,cAC1B,MAAM,KAAK,cAAc,KAAK;AAAA,cAC9B,WAAW,KAAK,aAAa,KAAK;AAAA,cAClC,SAAS,KAAK,WAAW,KAAK;AAAA,cAC9B,UAAU,KAAK;AAAA,cACf,WAAS,UAAK,cAAL,mBAAgB,KAAK,SAAQ;AAAA,cACtC,QAAQ,KAAK;AAAA,cACb,QAAQ,KAAK;AAAA,YACd;AAAA,WAAC;AAEF,cAAI,YAAY;AAEd,iBAAK,cAAc,CAAC,GAAG,KAAK,aAAa,GAAG,cAAc;AAAA,iBACrD;AAEL,iBAAK,cAAc;AAAA,UACrB;AAGA,eAAK,UAAU,KAAK,cAAc;AAAA,eAC7B;AACL,eAAK,YAAY;AACjBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,IAAI,WAAW;AAAA,YACtB,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,iCAAc,eAAe,KAAK;AAClC,aAAK,YAAY;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH,UAAU;AACR,aAAK,YAAY;AAAA,MACnB;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACb,WAAK,cAAc;AACnB,WAAK,cAAc;AACnB,WAAK,UAAU;AACf,WAAK,gBAAe;AAAA,IACrB;AAAA;AAAA,IAGD,wBAAwB;AACtB,MAAO,qBAAkB,KAAK,CAAC,EAAE,cAAc;AAC7C,gBAAQ,oBAAoB,EAAE,EAC3B,KAAK,SAAO;AACX,cAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAChC,iBAAK,oBAAoB,IAAI,KAAK,IAAI,WAAS;AAAA,cAC7C,OAAO,KAAK;AAAA,cACZ,OAAO,KAAK;AAAA,YACb,EAAC;AAGF,iBAAK,cAAc;AAAA,cACjB,EAAE,OAAO,QAAQ,OAAO,MAAO;AAAA,cAC/B,GAAG,KAAK;AAAA;AAIV,gBAAI,KAAK,kBAAkB,WAAW,GAAG;AACvC,mBAAK,cAAc;AAAA,gBACjB,EAAE,OAAO,QAAQ,OAAO,MAAO;AAAA,gBAC/B,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,gBAChC,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,gBAChC,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,gBAChC,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,gBAChC,EAAE,OAAO,SAAS,OAAO,QAAQ;AAAA;YAErC;AAAA,UACF;AAAA,SACD,EACA,MAAM,SAAO;AACZA,wBAAc,MAAA,MAAA,SAAA,iCAAA,eAAe,GAAG;AAEhC,eAAK,cAAc;AAAA,YACjB,EAAE,OAAO,QAAQ,OAAO,MAAO;AAAA,YAC/B,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,YAChC,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,YAChC,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,YAChC,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,YAChC,EAAE,OAAO,SAAS,OAAO,QAAQ;AAAA;QAErC,CAAC;AAAA,MACL,CAAC,EAAE,MAAM,SAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,iCAAc,gBAAgB,GAAG;AAEjC,aAAK,cAAc;AAAA,UACjB,EAAE,OAAO,QAAQ,OAAO,MAAO;AAAA,UAC/B,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,UAChC,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,UAChC,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,UAChC,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,UAChC,EAAE,OAAO,SAAS,OAAO,QAAQ;AAAA;MAErC,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,cAAc;AACZ,WAAK,gBAAgB;AACrB,WAAK,cAAc;AACnB,WAAK,cAAc;AACnB,WAAK,UAAU;AACf,WAAK,gBAAe;AAAA,IACrB;AAAA;AAAA,IAGD,mBAAmB;AACjB,WAAK,MAAM,kBAAkB;IAC9B;AAAA;AAAA,IAGD,iBAAiB;AACf,WAAK,MAAM,gBAAgB;IAC5B;AAAA;AAAA,IAGD,iBAAiB;AACf,WAAK,MAAM,gBAAgB;IAC5B;AAAA;AAAA,IAGD,mBAAmB,OAAO;AACxB,WAAK,eAAe;AACpB,WAAK,cAAc;AACnB,WAAK,cAAc;AACnB,WAAK,UAAU;AACf,WAAK,gBAAe;AACpB,WAAK,MAAM,kBAAkB;IAC9B;AAAA;AAAA,IAGD,iBAAiB,OAAO;AACtB,WAAK,aAAa;AAClB,WAAK,cAAc;AACnB,WAAK,cAAc;AACnB,WAAK,UAAU;AACf,WAAK,gBAAe;AACpB,WAAK,MAAM,gBAAgB;IAC5B;AAAA;AAAA,IAGD,iBAAiB,OAAO;AACtB,WAAK,aAAa;AAClB,WAAK,cAAc;AACnB,WAAK,cAAc;AACnB,WAAK,UAAU;AACf,WAAK,gBAAe;AACpB,WAAK,MAAM,gBAAgB;IAC5B;AAAA;AAAA,IAGD,cAAc,QAAQ;AACpB,YAAM,YAAY;AAAA,QAChB,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,WAAW;AAAA;AAEb,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,qBAAqB,IAAI;AACvBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,2BAA2B,EAAE;AAAA,MACpC,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,uBAAuB;AACrBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB,IAAI;AAClBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4BAA4B,EAAE;AAAA,MACrC,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,mBAAmB,IAAI;AACrBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4BAA4B,EAAE;AAAA,MACrC,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,WAAW,SAAS;AAClB,UAAI,CAAC;AAAS,eAAO;AACrB,YAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,UAAI,MAAM,KAAK,QAAO,CAAE;AAAG,eAAO;AAElC,YAAM,OAAO,KAAK;AAClB,YAAM,SAAS,KAAK,aAAa,GAAG,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC9D,YAAM,MAAM,KAAK,QAAS,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAErD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,IAC/B;AAAA,EACF;AACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxiBA,GAAG,WAAW,eAAe;"}