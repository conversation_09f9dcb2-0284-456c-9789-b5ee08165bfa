{"version": 3, "file": "account-binding.js", "sources": ["pages/user/account-binding.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlci9hY2NvdW50LWJpbmRpbmcudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"account-binding-container\">\r\n\t\t<view class=\"page-header\">\r\n\t\t\t<text class=\"page-title\">账号绑定</text>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"binding-card\">\r\n\t\t\t<view class=\"binding-item\">\r\n\t\t\t\t<view class=\"binding-icon phone-icon\">\r\n\t\t\t\t\t<text class=\"iconfont icon-phone\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"binding-info\">\r\n\t\t\t\t\t<text class=\"binding-name\">手机号</text>\r\n\t\t\t\t\t<text class=\"binding-status\" v-if=\"bindings.phone\">已绑定 ({{ maskPhone(bindings.phone) }})</text>\r\n\t\t\t\t\t<text class=\"binding-status not-bound\" v-else>未绑定</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"binding-action\">\r\n\t\t\t\t\t<text class=\"action-btn\" @click=\"handlePhone\">{{ bindings.phone ? '更换' : '绑定' }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"binding-item\">\r\n\t\t\t\t<view class=\"binding-icon email-icon\">\r\n\t\t\t\t\t<text class=\"iconfont icon-email\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"binding-info\">\r\n\t\t\t\t\t<text class=\"binding-name\">邮箱</text>\r\n\t\t\t\t\t<text class=\"binding-status\" v-if=\"bindings.email\">已绑定 ({{ maskEmail(bindings.email) }})</text>\r\n\t\t\t\t\t<text class=\"binding-status not-bound\" v-else>未绑定</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"binding-action\">\r\n\t\t\t\t\t<text class=\"action-btn\" @click=\"handleEmail\">{{ bindings.email ? '更换' : '绑定' }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"binding-item\">\r\n\t\t\t\t<view class=\"binding-icon wechat-icon\">\r\n\t\t\t\t\t<text class=\"iconfont icon-wechat\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"binding-info\">\r\n\t\t\t\t\t<text class=\"binding-name\">微信</text>\r\n\t\t\t\t\t<text class=\"binding-status\" v-if=\"bindings.wechat\">已绑定 ({{ bindings.wechatName }})</text>\r\n\t\t\t\t\t<text class=\"binding-status not-bound\" v-else>未绑定</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"binding-action\">\r\n\t\t\t\t\t<text class=\"action-btn\" @click=\"handleWechat\">{{ bindings.wechat ? '解绑' : '绑定' }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"binding-item\">\r\n\t\t\t\t<view class=\"binding-icon dingtalk-icon\">\r\n\t\t\t\t\t<text class=\"iconfont icon-dingtalk\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"binding-info\">\r\n\t\t\t\t\t<text class=\"binding-name\">钉钉</text>\r\n\t\t\t\t\t<text class=\"binding-status\" v-if=\"bindings.dingtalk\">已绑定 ({{ bindings.dingtalkName }})</text>\r\n\t\t\t\t\t<text class=\"binding-status not-bound\" v-else>未绑定</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"binding-action\">\r\n\t\t\t\t\t<text class=\"action-btn\" @click=\"handleDingtalk\">{{ bindings.dingtalk ? '解绑' : '绑定' }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"security-section\">\r\n\t\t\t<view class=\"security-title\">\r\n\t\t\t\t<text class=\"title-text\">安全提示</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"security-tips\">\r\n\t\t\t\t<view class=\"tip-item\">\r\n\t\t\t\t\t<text class=\"tip-marker\">•</text>\r\n\t\t\t\t\t<text class=\"tip-text\">绑定手机号和邮箱可用于接收通知和找回密码</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tip-item\">\r\n\t\t\t\t\t<text class=\"tip-marker\">•</text>\r\n\t\t\t\t\t<text class=\"tip-text\">绑定第三方账号可快速登录系统</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tip-item\">\r\n\t\t\t\t\t<text class=\"tip-marker\">•</text>\r\n\t\t\t\t\t<text class=\"tip-text\">为保障账号安全，建议至少绑定手机号</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 弹出层：绑定手机号 -->\r\n\t\t<uni-popup ref=\"phonePopup\" type=\"center\">\r\n\t\t\t<view class=\"popup-content\">\r\n\t\t\t\t<view class=\"popup-title\">绑定手机号</view>\r\n\t\t\t\t<view class=\"popup-form\">\r\n\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t<input type=\"number\" v-model=\"phoneForm.number\" placeholder=\"请输入手机号码\" maxlength=\"11\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"form-item code-item\">\r\n\t\t\t\t\t\t<input type=\"number\" v-model=\"phoneForm.code\" placeholder=\"请输入验证码\" maxlength=\"6\" />\r\n\t\t\t\t\t\t<text class=\"send-code-btn\" @click=\"sendCode('phone')\">获取验证码</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"popup-actions\">\r\n\t\t\t\t\t<text class=\"cancel-btn\" @click=\"closePopup('phonePopup')\">取消</text>\r\n\t\t\t\t\t<text class=\"confirm-btn\" @click=\"confirmBinding('phone')\">确定</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t\t\r\n\t\t<!-- 弹出层：绑定邮箱 -->\r\n\t\t<uni-popup ref=\"emailPopup\" type=\"center\">\r\n\t\t\t<view class=\"popup-content\">\r\n\t\t\t\t<view class=\"popup-title\">绑定邮箱</view>\r\n\t\t\t\t<view class=\"popup-form\">\r\n\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t<input type=\"text\" v-model=\"emailForm.address\" placeholder=\"请输入邮箱地址\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"form-item code-item\">\r\n\t\t\t\t\t\t<input type=\"number\" v-model=\"emailForm.code\" placeholder=\"请输入验证码\" maxlength=\"6\" />\r\n\t\t\t\t\t\t<text class=\"send-code-btn\" @click=\"sendCode('email')\">获取验证码</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"popup-actions\">\r\n\t\t\t\t\t<text class=\"cancel-btn\" @click=\"closePopup('emailPopup')\">取消</text>\r\n\t\t\t\t\t<text class=\"confirm-btn\" @click=\"confirmBinding('email')\">确定</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\t// 绑定状态\r\n\t\t\tbindings: {\r\n\t\t\t\tphone: '13812345678',\r\n\t\t\t\temail: '<EMAIL>',\r\n\t\t\t\twechat: 'wx123456789',\r\n\t\t\t\twechatName: '微信用户',\r\n\t\t\t\tdingtalk: '',\r\n\t\t\t\tdingtalkName: ''\r\n\t\t\t},\r\n\t\t\t// 手机绑定表单\r\n\t\t\tphoneForm: {\r\n\t\t\t\tnumber: '',\r\n\t\t\t\tcode: ''\r\n\t\t\t},\r\n\t\t\t// 邮箱绑定表单\r\n\t\t\temailForm: {\r\n\t\t\t\taddress: '',\r\n\t\t\t\tcode: ''\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\t\t// 加载用户的绑定信息\r\n\t\tthis.loadBindingInfo();\r\n\t},\r\n\tmethods: {\r\n\t\t// 加载绑定信息\r\n\t\tloadBindingInfo() {\r\n\t\t\t// 从本地存储获取用户信息\r\n\t\t\tconst userInfo = uni.getStorageSync('userInfo');\r\n\t\t\tif (userInfo) {\r\n\t\t\t\t// 如果有用户信息，更新绑定状态\r\n\t\t\t\tthis.bindings.phone = userInfo.phone || '';\r\n\t\t\t\tthis.bindings.email = userInfo.email || '';\r\n\t\t\t\tthis.bindings.wechat = userInfo.wechatId || '';\r\n\t\t\t\tthis.bindings.wechatName = userInfo.wechatName || '微信用户';\r\n\t\t\t\tthis.bindings.dingtalk = userInfo.dingtalkId || '';\r\n\t\t\t\tthis.bindings.dingtalkName = userInfo.dingtalkName || '钉钉用户';\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 手机号脱敏\r\n\t\tmaskPhone(phone) {\r\n\t\t\tif (!phone) return '';\r\n\t\t\treturn phone.replace(/(\\d{3})\\d{4}(\\d{4})/, '$1****$2');\r\n\t\t},\r\n\t\t\r\n\t\t// 邮箱脱敏\r\n\t\tmaskEmail(email) {\r\n\t\t\tif (!email) return '';\r\n\t\t\tconst parts = email.split('@');\r\n\t\t\tif (parts.length !== 2) return email;\r\n\t\t\tconst name = parts[0];\r\n\t\t\tconst domain = parts[1];\r\n\t\t\treturn name.substring(0, 3) + '****@' + domain;\r\n\t\t},\r\n\t\t\r\n\t\t// 处理手机号绑定\r\n\t\thandlePhone() {\r\n\t\t\tthis.$refs.phonePopup.open();\r\n\t\t},\r\n\t\t\r\n\t\t// 处理邮箱绑定\r\n\t\thandleEmail() {\r\n\t\t\tthis.$refs.emailPopup.open();\r\n\t\t},\r\n\t\t\r\n\t\t// 处理微信绑定\r\n\t\thandleWechat() {\r\n\t\t\tif (this.bindings.wechat) {\r\n\t\t\t\t// 已绑定，询问是否解绑\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '解绑确认',\r\n\t\t\t\t\tcontent: '确定要解绑微信账号吗？',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t// 模拟解绑成功\r\n\t\t\t\t\t\t\tthis.bindings.wechat = '';\r\n\t\t\t\t\t\t\tthis.bindings.wechatName = '';\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 更新本地存储\r\n\t\t\t\t\t\t\tconst userInfo = uni.getStorageSync('userInfo') || {};\r\n\t\t\t\t\t\t\tuserInfo.wechatId = '';\r\n\t\t\t\t\t\t\tuserInfo.wechatName = '';\r\n\t\t\t\t\t\t\tuni.setStorageSync('userInfo', userInfo);\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '解绑成功',\r\n\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\t// 未绑定，调用绑定流程\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '微信绑定功能开发中...',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 处理钉钉绑定\r\n\t\thandleDingtalk() {\r\n\t\t\tif (this.bindings.dingtalk) {\r\n\t\t\t\t// 已绑定，询问是否解绑\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '解绑确认',\r\n\t\t\t\t\tcontent: '确定要解绑钉钉账号吗？',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t// 模拟解绑成功\r\n\t\t\t\t\t\t\tthis.bindings.dingtalk = '';\r\n\t\t\t\t\t\t\tthis.bindings.dingtalkName = '';\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 更新本地存储\r\n\t\t\t\t\t\t\tconst userInfo = uni.getStorageSync('userInfo') || {};\r\n\t\t\t\t\t\t\tuserInfo.dingtalkId = '';\r\n\t\t\t\t\t\t\tuserInfo.dingtalkName = '';\r\n\t\t\t\t\t\t\tuni.setStorageSync('userInfo', userInfo);\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '解绑成功',\r\n\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\t// 未绑定，调用绑定流程\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '钉钉绑定功能开发中...',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 发送验证码\r\n\t\tsendCode(type) {\r\n\t\t\tif (type === 'phone') {\r\n\t\t\t\tif (!this.phoneForm.number || this.phoneForm.number.length !== 11) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入正确的手机号',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t} else if (type === 'email') {\r\n\t\t\t\tconst emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,6}$/;\r\n\t\t\t\tif (!emailRegex.test(this.emailForm.address)) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入正确的邮箱地址',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 模拟发送验证码\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '验证码已发送',\r\n\t\t\t\ticon: 'success'\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 确认绑定\r\n\t\tconfirmBinding(type) {\r\n\t\t\tif (type === 'phone') {\r\n\t\t\t\tif (!this.phoneForm.number || this.phoneForm.number.length !== 11) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入正确的手机号',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (!this.phoneForm.code || this.phoneForm.code.length !== 6) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入6位验证码',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 模拟绑定成功\r\n\t\t\t\tthis.bindings.phone = this.phoneForm.number;\r\n\t\t\t\t\r\n\t\t\t\t// 更新本地存储\r\n\t\t\t\tconst userInfo = uni.getStorageSync('userInfo') || {};\r\n\t\t\t\tuserInfo.phone = this.bindings.phone;\r\n\t\t\t\tuni.setStorageSync('userInfo', userInfo);\r\n\t\t\t\t\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '手机绑定成功',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\tthis.closePopup('phonePopup');\r\n\t\t\t} else if (type === 'email') {\r\n\t\t\t\tconst emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,6}$/;\r\n\t\t\t\tif (!emailRegex.test(this.emailForm.address)) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入正确的邮箱地址',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (!this.emailForm.code || this.emailForm.code.length !== 6) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入6位验证码',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 模拟绑定成功\r\n\t\t\t\tthis.bindings.email = this.emailForm.address;\r\n\t\t\t\t\r\n\t\t\t\t// 更新本地存储\r\n\t\t\t\tconst userInfo = uni.getStorageSync('userInfo') || {};\r\n\t\t\t\tuserInfo.email = this.bindings.email;\r\n\t\t\t\tuni.setStorageSync('userInfo', userInfo);\r\n\t\t\t\t\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '邮箱绑定成功',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\tthis.closePopup('emailPopup');\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 关闭弹出层\r\n\t\tclosePopup(ref) {\r\n\t\t\tthis.$refs[ref].close();\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.account-binding-container {\r\n\tbackground-color: #f5f7fa;\r\n\tmin-height: 100vh;\r\n\tpadding-bottom: 40rpx;\r\n}\r\n\r\n.page-header {\r\n\tbackground-color: #fff;\r\n\tpadding: 30rpx;\r\n\tborder-bottom: 1rpx solid #eee;\r\n\t\r\n\t.page-title {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t}\r\n}\r\n\r\n.binding-card {\r\n\tmargin: 30rpx;\r\n\tbackground-color: #fff;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 10rpx 0;\r\n\tbox-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.05);\r\n\t\r\n\t.binding-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 30rpx;\r\n\t\tborder-bottom: 1rpx solid rgba(0, 0, 0, 0.05);\r\n\t\t\r\n\t\t&:last-child {\r\n\t\t\tborder-bottom: none;\r\n\t\t}\r\n\t\t\r\n\t\t.binding-icon {\r\n\t\t\twidth: 80rpx;\r\n\t\t\theight: 80rpx;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin-right: 20rpx;\r\n\t\t\t\r\n\t\t\t.iconfont {\r\n\t\t\t\tfont-size: 40rpx;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.phone-icon {\r\n\t\t\t\tbackground: linear-gradient(135deg, #1890ff, #36b3ff);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.email-icon {\r\n\t\t\t\tbackground: linear-gradient(135deg, #52c41a, #73d13d);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.wechat-icon {\r\n\t\t\t\tbackground: linear-gradient(135deg, #07c160, #10d878);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.dingtalk-icon {\r\n\t\t\t\tbackground: linear-gradient(135deg, #1677ff, #4096ff);\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.binding-info {\r\n\t\t\tflex: 1;\r\n\t\t\t\r\n\t\t\t.binding-name {\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.binding-status {\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #52c41a;\r\n\t\t\t\t\r\n\t\t\t\t&.not-bound {\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.binding-action {\r\n\t\t\t.action-btn {\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tpadding: 12rpx 30rpx;\r\n\t\t\t\tbackground-color: #f5f7fa;\r\n\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tborder-radius: 30rpx;\r\n\t\t\t\t\r\n\t\t\t\t&:active {\r\n\t\t\t\t\topacity: 0.8;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.security-section {\r\n\tmargin: 30rpx;\r\n\t\r\n\t.security-title {\r\n\t\tmargin-bottom: 20rpx;\r\n\t\t\r\n\t\t.title-text {\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tcolor: #666;\r\n\t\t\tfont-weight: bold;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.security-tips {\r\n\t\t.tip-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\tmargin-bottom: 15rpx;\r\n\t\t\t\r\n\t\t\t.tip-marker {\r\n\t\t\t\tcolor: #999;\r\n\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.tip-text {\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #999;\r\n\t\t\t\tline-height: 1.6;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.popup-content {\r\n\twidth: 600rpx;\r\n\tbackground-color: #fff;\r\n\tborder-radius: 12rpx;\r\n\toverflow: hidden;\r\n\t\r\n\t.popup-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t\ttext-align: center;\r\n\t\tpadding: 30rpx 0;\r\n\t\tborder-bottom: 1rpx solid #eee;\r\n\t}\r\n\t\r\n\t.popup-form {\r\n\t\tpadding: 30rpx;\r\n\t\t\r\n\t\t.form-item {\r\n\t\t\tmargin-bottom: 30rpx;\r\n\t\t\t\r\n\t\t\tinput {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 80rpx;\r\n\t\t\t\tborder: 1rpx solid #ddd;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\tpadding: 0 20rpx;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.code-item {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\t\r\n\t\t\t\tinput {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.send-code-btn {\r\n\t\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\tpadding: 15rpx 20rpx;\r\n\t\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\t&:active {\r\n\t\t\t\t\t\topacity: 0.8;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.popup-actions {\r\n\t\tdisplay: flex;\r\n\t\tborder-top: 1rpx solid #eee;\r\n\t\t\r\n\t\t.cancel-btn, .confirm-btn {\r\n\t\t\tflex: 1;\r\n\t\t\ttext-align: center;\r\n\t\t\tpadding: 30rpx 0;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.cancel-btn {\r\n\t\t\tcolor: #999;\r\n\t\t\tborder-right: 1rpx solid #eee;\r\n\t\t}\r\n\t\t\r\n\t\t.confirm-btn {\r\n\t\t\tcolor: $uni-color-primary;\r\n\t\t\tfont-weight: bold;\r\n\t\t}\r\n\t}\r\n}\r\n</style> ", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaan<PERSON>_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/user/account-binding.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA+HA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA;AAAA,MAEN,UAAU;AAAA,QACT,OAAO;AAAA,QACP,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,cAAc;AAAA,MACd;AAAA;AAAA,MAED,WAAW;AAAA,QACV,QAAQ;AAAA,QACR,MAAM;AAAA,MACN;AAAA;AAAA,MAED,WAAW;AAAA,QACV,SAAS;AAAA,QACT,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACA;AAAA,EACD,SAAS;AAER,SAAK,gBAAe;AAAA,EACpB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,kBAAkB;AAEjB,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9C,UAAI,UAAU;AAEb,aAAK,SAAS,QAAQ,SAAS,SAAS;AACxC,aAAK,SAAS,QAAQ,SAAS,SAAS;AACxC,aAAK,SAAS,SAAS,SAAS,YAAY;AAC5C,aAAK,SAAS,aAAa,SAAS,cAAc;AAClD,aAAK,SAAS,WAAW,SAAS,cAAc;AAChD,aAAK,SAAS,eAAe,SAAS,gBAAgB;AAAA,MACvD;AAAA,IACA;AAAA;AAAA,IAGD,UAAU,OAAO;AAChB,UAAI,CAAC;AAAO,eAAO;AACnB,aAAO,MAAM,QAAQ,uBAAuB,UAAU;AAAA,IACtD;AAAA;AAAA,IAGD,UAAU,OAAO;AAChB,UAAI,CAAC;AAAO,eAAO;AACnB,YAAM,QAAQ,MAAM,MAAM,GAAG;AAC7B,UAAI,MAAM,WAAW;AAAG,eAAO;AAC/B,YAAM,OAAO,MAAM,CAAC;AACpB,YAAM,SAAS,MAAM,CAAC;AACtB,aAAO,KAAK,UAAU,GAAG,CAAC,IAAI,UAAU;AAAA,IACxC;AAAA;AAAA,IAGD,cAAc;AACb,WAAK,MAAM,WAAW;IACtB;AAAA;AAAA,IAGD,cAAc;AACb,WAAK,MAAM,WAAW;IACtB;AAAA;AAAA,IAGD,eAAe;AACd,UAAI,KAAK,SAAS,QAAQ;AAEzBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,SAAS;AAAA,UACT,SAAS,CAAC,QAAQ;AACjB,gBAAI,IAAI,SAAS;AAEhB,mBAAK,SAAS,SAAS;AACvB,mBAAK,SAAS,aAAa;AAG3B,oBAAM,WAAWA,cAAG,MAAC,eAAe,UAAU,KAAK,CAAA;AACnD,uBAAS,WAAW;AACpB,uBAAS,aAAa;AACtBA,4BAAAA,MAAI,eAAe,YAAY,QAAQ;AAEvCA,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO;AAAA,gBACP,MAAM;AAAA,cACP,CAAC;AAAA,YACF;AAAA,UACD;AAAA,QACD,CAAC;AAAA,aACK;AAENA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,iBAAiB;AAChB,UAAI,KAAK,SAAS,UAAU;AAE3BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,SAAS;AAAA,UACT,SAAS,CAAC,QAAQ;AACjB,gBAAI,IAAI,SAAS;AAEhB,mBAAK,SAAS,WAAW;AACzB,mBAAK,SAAS,eAAe;AAG7B,oBAAM,WAAWA,cAAG,MAAC,eAAe,UAAU,KAAK,CAAA;AACnD,uBAAS,aAAa;AACtB,uBAAS,eAAe;AACxBA,4BAAAA,MAAI,eAAe,YAAY,QAAQ;AAEvCA,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO;AAAA,gBACP,MAAM;AAAA,cACP,CAAC;AAAA,YACF;AAAA,UACD;AAAA,QACD,CAAC;AAAA,aACK;AAENA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,SAAS,MAAM;AACd,UAAI,SAAS,SAAS;AACrB,YAAI,CAAC,KAAK,UAAU,UAAU,KAAK,UAAU,OAAO,WAAW,IAAI;AAClEA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AACD;AAAA,QACD;AAAA,iBACU,SAAS,SAAS;AAC5B,cAAM,aAAa;AACnB,YAAI,CAAC,WAAW,KAAK,KAAK,UAAU,OAAO,GAAG;AAC7CA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AACD;AAAA,QACD;AAAA,MACD;AAGAA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,eAAe,MAAM;AACpB,UAAI,SAAS,SAAS;AACrB,YAAI,CAAC,KAAK,UAAU,UAAU,KAAK,UAAU,OAAO,WAAW,IAAI;AAClEA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AACD;AAAA,QACD;AAEA,YAAI,CAAC,KAAK,UAAU,QAAQ,KAAK,UAAU,KAAK,WAAW,GAAG;AAC7DA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AACD;AAAA,QACD;AAGA,aAAK,SAAS,QAAQ,KAAK,UAAU;AAGrC,cAAM,WAAWA,cAAG,MAAC,eAAe,UAAU,KAAK,CAAA;AACnD,iBAAS,QAAQ,KAAK,SAAS;AAC/BA,sBAAAA,MAAI,eAAe,YAAY,QAAQ;AAEvCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAED,aAAK,WAAW,YAAY;AAAA,iBAClB,SAAS,SAAS;AAC5B,cAAM,aAAa;AACnB,YAAI,CAAC,WAAW,KAAK,KAAK,UAAU,OAAO,GAAG;AAC7CA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AACD;AAAA,QACD;AAEA,YAAI,CAAC,KAAK,UAAU,QAAQ,KAAK,UAAU,KAAK,WAAW,GAAG;AAC7DA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AACD;AAAA,QACD;AAGA,aAAK,SAAS,QAAQ,KAAK,UAAU;AAGrC,cAAM,WAAWA,cAAG,MAAC,eAAe,UAAU,KAAK,CAAA;AACnD,iBAAS,QAAQ,KAAK,SAAS;AAC/BA,sBAAAA,MAAI,eAAe,YAAY,QAAQ;AAEvCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAED,aAAK,WAAW,YAAY;AAAA,MAC7B;AAAA,IACA;AAAA;AAAA,IAGD,WAAW,KAAK;AACf,WAAK,MAAM,GAAG,EAAE,MAAK;AAAA,IACtB;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/WA,GAAG,WAAW,eAAe;"}