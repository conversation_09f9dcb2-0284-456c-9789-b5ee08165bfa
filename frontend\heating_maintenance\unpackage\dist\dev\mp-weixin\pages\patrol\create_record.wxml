<view class="container"><view class="page-title"><text>创建巡检记录</text></view><scroll-view scroll-y class="form-container"><view class="form-group"><text class="form-label required">巡检计划</text><view class="form-input select-box" bindtap="{{c}}"><text wx:if="{{a}}">{{b}}</text><text wx:else class="placeholder">请选择巡检计划</text><text class="select-arrow">▼</text></view></view><view class="form-group"><text class="form-label required">执行人</text><view class="form-input select-box" bindtap="{{g}}"><text wx:if="{{d}}"> 已选择 {{e}} 人{{f}}</text><text wx:else class="placeholder">请选择执行人</text><text class="select-arrow">▼</text></view></view><view class="form-group"><text class="form-label required">开始时间</text><view class="form-input select-box" bindtap="{{j}}"><text wx:if="{{h}}">{{i}}</text><text wx:else class="placeholder">请选择开始时间</text><text class="select-arrow">▼</text></view></view><view class="form-group"><text class="form-label required">结束时间</text><view class="form-input select-box" bindtap="{{m}}"><text wx:if="{{k}}">{{l}}</text><text wx:else class="placeholder">请选择结束时间</text><text class="select-arrow">▼</text></view></view><view class="form-group"><text class="form-label required">记录状态</text><view class="status-group"><view wx:for="{{n}}" wx:for-item="status" wx:key="b" class="{{['status-option', status.c && 'active']}}" bindtap="{{status.d}}"><text class="status-dot"></text><text class="status-text">{{status.a}}</text></view></view></view><view class="form-group"><text class="form-label">备注信息</text><block wx:if="{{r0}}"><textarea class="form-textarea" placeholder="请输入巡检记录备注信息" maxlength="200" value="{{o}}" bindinput="{{p}}"></textarea></block><text class="char-count">{{q}}/200</text></view><view wx:if="{{r}}" class="form-group"><text class="form-label">巡检结果</text><view class="results-list"><view wx:for="{{s}}" wx:for-item="result" wx:key="n" class="result-item" bindtap="{{result.o}}"><view class="result-header"><text class="result-title">{{result.a}}</text><view class="{{['result-status', result.c]}}">{{result.b}}</view></view><view class="result-body"><view class="result-row device-info"><text class="result-label">设备:</text><text class="result-value">{{result.d}}</text></view><view wx:if="{{result.e}}" class="result-row"><text class="result-label">正常范围:</text><text class="result-value">{{result.f}}</text></view><view wx:if="{{result.g}}" class="result-row"><text class="result-label">参数值:</text><text class="result-value">{{result.h}}{{result.i}}</text></view><view wx:if="{{result.j}}" class="result-row"><text class="result-label">描述:</text><text class="result-value">{{result.k}}</text></view><view wx:if="{{result.l}}" class="result-row"><text class="result-label">照片:</text><text class="result-value">已上传 {{result.m}} 张</text></view></view></view></view></view><view class="submit-section"><button class="{{['submit-btn', v && 'disabled']}}" disabled="{{w}}" bindtap="{{x}}">{{t}}</button></view></scroll-view><uni-popup wx:if="{{ai}}" class="r" u-s="{{['d']}}" u-r="resultPopup" u-i="a56eebf2-0" bind:__l="__l" u-p="{{ai}}"><view class="result-popup"><view class="popup-header"><text class="popup-title">巡检结果 - {{y}}</text><text class="popup-close" bindtap="{{z}}">关闭</text></view><view class="popup-content"><view wx:if="{{A}}" class="popup-form-group"><text class="popup-form-label">设备信息</text><view class="device-info-box"><text class="device-name">{{B}}</text><text wx:if="{{C}}" class="device-category">分类: {{D}}</text></view></view><view wx:if="{{E}}" class="popup-form-group"><text class="popup-form-label">检查标准</text><view class="standard-info"><text class="normal-range">正常范围: {{F}}</text><text wx:if="{{G}}" class="check-method">检查方法: {{H}}</text></view></view><view wx:if="{{I}}" class="popup-form-group"><text class="popup-form-label required">当前值</text><input class="popup-input" type="text" placeholder="请输入当前测量值" value="{{J}}" bindinput="{{K}}"/><text wx:if="{{L}}" class="parameter-unit">{{M}}</text></view><view class="popup-form-group"><text class="popup-form-label">检查结果</text><view class="result-radio-group"><view class="{{['result-radio', N && 'result-radio-selected']}}" bindtap="{{O}}"><text>正常</text></view><view class="{{['result-radio', P && 'result-radio-selected']}}" bindtap="{{Q}}"><text>异常</text></view></view></view><view wx:if="{{R}}" class="popup-form-group"><text class="popup-form-label">参数值</text><input class="popup-input" type="text" placeholder="请输入参数值" value="{{S}}" bindinput="{{T}}"/><text wx:if="{{U}}" class="parameter-unit">{{V}}</text></view><view class="popup-form-group"><text class="popup-form-label">描述</text><block wx:if="{{r0}}"><textarea class="popup-form-textarea" placeholder="请输入描述信息" value="{{W}}" bindinput="{{X}}"></textarea></block></view><view class="popup-form-group"><text class="popup-form-label">照片</text><view wx:if="{{Y}}" class="result-image-list"><view wx:for="{{Z}}" wx:for-item="image" wx:key="c" class="result-image-item"><image class="result-image" src="{{image.a}}" mode="aspectFill"></image><view class="result-image-delete" bindtap="{{image.b}}"><text class="icon-delete">×</text></view></view><view wx:if="{{aa}}" class="result-image-add" bindtap="{{ab}}"><text class="icon-add">+</text><text class="result-image-tip">添加图片</text></view></view></view><view class="popup-form-group"><text class="popup-form-label">位置信息</text><view class="location-btns"><button class="location-btn" bindtap="{{ac}}">获取当前位置</button><text wx:if="{{ad}}"> 已获取位置 ({{ae}}, {{af}}) </text></view></view><button class="popup-save-btn" bindtap="{{ag}}">保存</button></view></view></uni-popup><uni-popup wx:if="{{ax}}" class="r" u-s="{{['d']}}" u-r="datetimePopup" u-i="a56eebf2-1" bind:__l="__l" u-p="{{ax}}"><view class="datetime-popup"><view class="popup-header"><text class="popup-title">{{aj}}</text><text class="popup-close" bindtap="{{ak}}">关闭</text></view><view class="picker-body"><block wx:if="{{r0}}"><picker-view class="picker" indicator-style="{{ar}}" value="{{as}}" bindchange="{{at}}"><picker-view-column><view wx:for="{{al}}" wx:for-item="item" wx:key="b" class="picker-item">{{item.a}}年</view></picker-view-column><picker-view-column><view wx:for="{{am}}" wx:for-item="item" wx:key="b" class="picker-item">{{item.a}}月</view></picker-view-column><picker-view-column><view wx:for="{{an}}" wx:for-item="item" wx:key="b" class="picker-item">{{item.a}}日</view></picker-view-column><picker-view-column><view wx:for="{{ao}}" wx:for-item="item" wx:key="b" class="picker-item">{{item.a}}时</view></picker-view-column><picker-view-column><view wx:for="{{ap}}" wx:for-item="item" wx:key="b" class="picker-item">{{item.a}}分</view></picker-view-column><picker-view-column><view wx:for="{{aq}}" wx:for-item="item" wx:key="b" class="picker-item">{{item.a}}秒</view></picker-view-column></picker-view></block></view><view class="btn-area"><button class="confirm-btn" bindtap="{{av}}">确定</button></view></view></uni-popup><uni-popup wx:if="{{aG}}" class="r" u-s="{{['d']}}" u-r="executorPopup" u-i="a56eebf2-2" bind:__l="__l" u-p="{{aG}}"><view class="executor-popup"><view class="popup-header"><text class="popup-title">选择执行人</text><text class="popup-close" bindtap="{{ay}}">关闭</text></view><view class="executor-body"><view class="executor-search"><input type="text" placeholder="搜索执行人" class="search-input" value="{{az}}" bindinput="{{aA}}"/></view><scroll-view scroll-y class="executor-list"><checkbox-group bindchange="{{aC}}"><label wx:for="{{aB}}" wx:for-item="executor" wx:key="f" class="executor-item"><view class="executor-info"><checkbox value="{{executor.a}}" checked="{{executor.b}}"/><text class="executor-name">{{executor.c}}</text></view><text wx:if="{{executor.d}}" class="executor-dept">{{executor.e}}</text></label></checkbox-group></scroll-view></view><view class="btn-area"><button class="confirm-btn" bindtap="{{aE}}">确定 (已选 {{aD}} 人)</button></view></view></uni-popup></view>