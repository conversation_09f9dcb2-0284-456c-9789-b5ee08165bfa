"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const PermissionCheck = () => "../../components/PermissionCheck.js";
const _sfc_main = {
  components: {
    PermissionCheck
    // 本地注册组件
  },
  data() {
    return {
      // 当前时间相关
      timer: null,
      currentDate: "",
      currentTime: "",
      // 位置相关
      latitude: 0,
      // 默认纬度，使用默认值
      longitude: 0,
      // 默认经度，使用默认值
      clockStatus: "normal",
      // 默认为normal，始终允许打卡
      locationError: "",
      // 存储定位错误信息
      useNativeGeolocation: true,
      // 默认使用原生定位
      locationPermissionDenied: false,
      // 位置权限是否被拒绝
      hasShownLocationError: false,
      // 是否已经显示过位置错误提醒
      // 今日打卡记录
      clockInTime: "",
      clockOutTime: "",
      // 补卡相关
      supplementDate: this.formatDate(/* @__PURE__ */ new Date()),
      supplementTypes: ["上班打卡", "下班打卡"],
      supplementTypeIndex: 0,
      supplementReason: "",
      supplementImages: [],
      // 最近打卡记录
      recentRecords: [],
      // 考勤规则
      attendanceRules: {
        clockInTime: "08:30",
        clockOutTime: "17:30",
        allowedDistance: 500,
        // 允许打卡的距离范围(米)
        lateThreshold: 15,
        // 迟到阈值(分钟)
        earlyLeaveThreshold: 15,
        // 早退阈值(分钟)
        locationUploadInterval: 1
        // 位置上传时间间隔(分钟)
      },
      // 加载状态
      loading: false,
      // 是否是管理员
      isAdmin: false,
      // 定位轨迹上传定时器
      locationUploadTimer: null,
      isUploadingLocation: false,
      // 是否正在上传位置
      isGettingLocation: false
      // 是否正在获取位置
    };
  },
  computed: {
    // 移除canClockIn计算属性，始终允许打卡
    canClockIn() {
      return true;
    }
  },
  onLoad() {
    this.updateDateTime();
    this.timer = setInterval(() => {
      this.updateDateTime();
    }, 1e3);
    this.hasShownLocationError = false;
    this.isGettingLocation = false;
    this.getAttendanceRules();
    this.getTodayClockRecord();
    this.getRecentRecords();
    setTimeout(() => {
      this.checkLocationPermission().then((hasPermission) => {
        if (hasPermission) {
          this.tryGetLocation();
        } else {
          this.requestLocationPermission();
        }
      });
    }, 500);
  },
  onUnload() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  },
  onShow() {
    this.getAttendanceRules();
    this.getTodayClockRecord();
    this.getRecentRecords();
  },
  methods: {
    // 启动定位上传
    startLocationUpload() {
      this.$store.dispatch("attendance/startLocationUpload");
      common_vendor.index.__f__("log", "at pages/attendance/clock-in.vue:275", "启动全局位置上传");
    },
    // 停止定位上传
    stopLocationUpload() {
      this.$store.dispatch("attendance/stopLocationUpload");
      common_vendor.index.__f__("log", "at pages/attendance/clock-in.vue:281", "停止全局位置上传");
    },
    // 更新日期时间
    updateDateTime() {
      const now = /* @__PURE__ */ new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, "0");
      const day = String(now.getDate()).padStart(2, "0");
      const weekDays = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
      const weekDay = weekDays[now.getDay()];
      this.currentDate = `${year}年${month}月${day}日 ${weekDay}`;
      const hours = String(now.getHours()).padStart(2, "0");
      const minutes = String(now.getMinutes()).padStart(2, "0");
      const seconds = String(now.getSeconds()).padStart(2, "0");
      this.currentTime = `${hours}:${minutes}:${seconds}`;
    },
    // 获取位置信息
    getLocation() {
      if (this.isGettingLocation) {
        common_vendor.index.__f__("log", "at pages/attendance/clock-in.vue:309", "正在获取位置中，跳过重复请求");
        return;
      }
      this.isGettingLocation = true;
      this.clockStatus = "normal";
      const locationTimeout = setTimeout(() => {
        common_vendor.index.__f__("log", "at pages/attendance/clock-in.vue:318", "位置获取超时，停止尝试");
        this.locationError = "位置获取超时";
        this.clockStatus = "normal";
        this.isGettingLocation = false;
        this.requestLocationPermission();
      }, 15e3);
      if (this.useNativeGeolocation) {
        this.getNativeLocation().then(() => {
          clearTimeout(locationTimeout);
          this.isGettingLocation = false;
        }).catch((err) => {
          common_vendor.index.__f__("error", "at pages/attendance/clock-in.vue:333", "原生定位失败，尝试使用uni定位:", err);
          this.useNativeGeolocation = false;
          this.getUniLocation().then(() => {
            clearTimeout(locationTimeout);
            this.isGettingLocation = false;
          }).catch((err2) => {
            clearTimeout(locationTimeout);
            this.handleLocationError(err2);
            this.isGettingLocation = false;
          });
        });
      } else {
        this.getUniLocation().then(() => {
          clearTimeout(locationTimeout);
          this.isGettingLocation = false;
        }).catch((err) => {
          clearTimeout(locationTimeout);
          this.handleLocationError(err);
          this.isGettingLocation = false;
        });
      }
    },
    // 使用原生定位API获取位置
    getNativeLocation() {
      return new Promise((resolve, reject) => {
      });
    },
    // 使用uni.getLocation获取位置
    getUniLocation() {
      return new Promise((resolve, reject) => {
        common_vendor.index.getLocation({
          type: "gcj02",
          isHighAccuracy: true,
          highAccuracyExpireTime: 5e3,
          success: (res) => {
            common_vendor.index.__f__("log", "at pages/attendance/clock-in.vue:415", "uni位置获取成功:", res);
            this.latitude = res.latitude;
            this.longitude = res.longitude;
            this.locationError = "";
            this.locationPermissionDenied = false;
            this.clockStatus = "normal";
            this.hasShownLocationError = false;
            resolve(res);
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/attendance/clock-in.vue:426", "uni获取位置失败:", err);
            reject(err);
          },
          complete: () => {
            common_vendor.index.__f__("log", "at pages/attendance/clock-in.vue:431", "位置获取完成");
          }
        });
      });
    },
    // 检查位置权限
    checkLocationPermission() {
      return new Promise((resolve) => {
        common_vendor.index.getSetting({
          success: (res) => {
            resolve(res.authSetting["scope.userLocation"] === true);
          },
          fail: () => {
            resolve(false);
          }
        });
      });
    },
    // 处理位置错误
    handleLocationError(err) {
      common_vendor.index.__f__("log", "at pages/attendance/clock-in.vue:516", "位置获取失败:", err);
      this.clockStatus = "outside";
      this.locationError = "无法获取位置，请授权位置权限";
      this.checkLocationPermission().then((hasPermission) => {
        if (!hasPermission) {
          this.requestLocationPermission();
        }
      });
      this.isGettingLocation = false;
    },
    // 请求位置权限
    requestLocationPermission() {
      common_vendor.index.authorize({
        scope: "scope.userLocation",
        success: () => {
          this.tryGetLocation();
        },
        fail: () => {
          common_vendor.index.__f__("log", "at pages/attendance/clock-in.vue:623", "小程序环境获取位置授权失败，使用默认位置");
        }
      });
    },
    // 打开系统位置设置
    openLocationSettings() {
    },
    // 检查是否在考勤范围内
    checkAttendanceArea(latitude, longitude) {
      const checkLocal = () => {
        const companyLocation = {
          latitude: 34.341576,
          // 根据实际情况修改
          longitude: 108.940174
          // 根据实际情况修改
        };
        const distance = this.calculateDistance(
          latitude,
          longitude,
          companyLocation.latitude,
          companyLocation.longitude
        );
        const inArea = distance <= this.attendanceRules.allowedDistance;
        this.clockStatus = inArea ? "normal" : "outside";
        if (!inArea) {
          common_vendor.index.showToast({
            title: `您距离考勤点${Math.round(distance)}米，不在打卡范围内`,
            icon: "none",
            duration: 3e3
          });
        }
      };
      utils_api.attendanceApi.checkAttendanceArea({
        latitude,
        longitude
      }).then((res) => {
        if (res.code === 200) {
          this.clockStatus = res.data.inArea ? "normal" : "outside";
          if (!res.data.inArea && res.data.distance) {
            common_vendor.index.showToast({
              title: `您距离考勤点${res.data.distance}米，不在打卡范围内`,
              icon: "none",
              duration: 3e3
            });
          }
        } else {
          checkLocal();
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/attendance/clock-in.vue:701", "考勤范围校验异常:", err);
        checkLocal();
      });
    },
    // 计算两点之间的距离（米）
    calculateDistance(lat1, lon1, lat2, lon2) {
      const R = 6371e3;
      const dLat = this.deg2rad(lat2 - lat1);
      const dLon = this.deg2rad(lon2 - lon1);
      const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      const distance = R * c;
      return distance;
    },
    // 角度转弧度
    deg2rad(deg) {
      return deg * (Math.PI / 180);
    },
    // 获取考勤规则
    getAttendanceRules() {
      utils_api.attendanceApi.getClockRules().then((res) => {
        if (res.code === 200 && res.data) {
          common_vendor.index.__f__("log", "at pages/attendance/clock-in.vue:731", "获取考勤规则成功:", res.data);
          this.attendanceRules = {
            ...this.attendanceRules,
            ...res.data
          };
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/attendance/clock-in.vue:739", "获取考勤规则失败:", err);
      });
    },
    // 获取今日打卡记录
    getTodayClockRecord() {
      utils_api.attendanceApi.getTodayRecord().then((res) => {
        common_vendor.index.__f__("log", "at pages/attendance/clock-in.vue:747", "获取今日打卡记录:", res);
        if (res.code === 200 && res.data) {
          this.clockInTime = res.data.clockInTime || "";
          this.clockOutTime = res.data.clockOutTime || "";
          if (this.clockInTime && res.data.clockInStatus) {
            const statusText = res.data.clockInStatus === "late" ? "（迟到）" : "";
            this.clockInTime = this.clockInTime + statusText;
          }
          if (this.clockOutTime && res.data.clockOutStatus) {
            const statusText = res.data.clockOutStatus === "early" ? "（早退）" : "";
            this.clockOutTime = this.clockOutTime + statusText;
          }
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/attendance/clock-in.vue:766", "获取今日打卡记录失败:", err);
      });
    },
    // 获取最近打卡记录
    getRecentRecords() {
      utils_api.attendanceApi.getRecentRecords(7).then((res) => {
        if (res.code === 200 && res.data && Array.isArray(res.data.records)) {
          this.recentRecords = res.data.records.map((item) => {
            return {
              date: item.date,
              week: item.week,
              clockInTime: item.clockInTime || "未打卡",
              clockOutTime: item.clockOutTime || "未打卡",
              clockInStatus: item.clockInStatus || "normal",
              clockOutStatus: item.clockOutStatus || "normal"
            };
          });
        } else {
          common_vendor.index.__f__("warn", "at pages/attendance/clock-in.vue:787", "获取最近打卡记录返回数据格式异常:", res);
          this.recentRecords = [];
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/attendance/clock-in.vue:792", "获取最近打卡记录失败:", err);
        this.recentRecords = [];
        common_vendor.index.showToast({
          title: "获取打卡记录失败",
          icon: "none"
        });
      });
    },
    // 处理打卡
    handleClockIn() {
      const isClockIn = !this.clockInTime;
      const isClockOut = !!this.clockInTime && !this.clockOutTime;
      if (!isClockIn && !isClockOut) {
        common_vendor.index.showToast({
          title: "今日打卡已完成",
          icon: "info"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "打卡中..."
      });
      this.tryGetLocation().finally(() => {
        common_vendor.index.hideLoading();
        this.submitAttendance();
      });
    },
    // 尝试获取位置，但不影响打卡流程
    tryGetLocation() {
      return new Promise((resolve) => {
        if (this.isGettingLocation) {
          common_vendor.index.__f__("log", "at pages/attendance/clock-in.vue:832", "正在获取位置中，跳过重复请求");
          resolve();
          return;
        }
        this.isGettingLocation = true;
        const locationTimeout = setTimeout(() => {
          common_vendor.index.__f__("log", "at pages/attendance/clock-in.vue:841", "位置获取超时，使用默认位置");
          this.isGettingLocation = false;
          resolve();
        }, 3e3);
        if (this.useNativeGeolocation) {
          this.getNativeLocation().then(() => {
            clearTimeout(locationTimeout);
            this.isGettingLocation = false;
            resolve();
          }).catch(() => {
            this.getUniLocation().then(() => {
              clearTimeout(locationTimeout);
              this.isGettingLocation = false;
              resolve();
            }).catch(() => {
              clearTimeout(locationTimeout);
              this.isGettingLocation = false;
              resolve();
            });
          });
        } else {
          this.getUniLocation().then(() => {
            clearTimeout(locationTimeout);
            this.isGettingLocation = false;
            resolve();
          }).catch(() => {
            clearTimeout(locationTimeout);
            this.isGettingLocation = false;
            resolve();
          });
        }
        this.checkLocationPermission().then((hasPermission) => {
          if (!hasPermission) {
            this.requestLocationPermission();
          }
        });
      });
    },
    // 显示补卡申请弹窗
    showSupplementModal() {
      this.resetSupplementForm();
      this.$refs.supplementPopup.open();
    },
    // 重置补卡表单
    resetSupplementForm() {
      this.supplementDate = this.formatDate(/* @__PURE__ */ new Date());
      this.supplementTypeIndex = 0;
      this.supplementReason = "";
      this.supplementImages = [];
    },
    // 选择补卡证明图片
    chooseSupplementImage() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          if (this.supplementImages.length < 3) {
            this.supplementImages.push(res.tempFilePaths[0]);
          } else {
            common_vendor.index.showToast({
              title: "最多上传3张图片",
              icon: "none"
            });
          }
        }
      });
    },
    // 删除补卡证明图片
    deleteSupplementImage(index) {
      this.supplementImages.splice(index, 1);
    },
    // 提交补卡申请
    submitSupplementApplication() {
      if (!this.supplementDate) {
        common_vendor.index.showToast({
          title: "请选择补卡日期",
          icon: "none"
        });
        return;
      }
      if (!this.supplementReason || this.supplementReason.trim() === "") {
        common_vendor.index.showToast({
          title: "请输入补卡原因",
          icon: "none"
        });
        return;
      }
      this.loading = true;
      const formData = {
        userId: this.getUserId(),
        date: this.supplementDate,
        type: this.supplementTypeIndex + 1,
        // 1-上班打卡, 2-下班打卡
        reason: this.supplementReason,
        images: []
      };
      if (this.supplementImages.length > 0) {
        this.uploadSupplementImages().then((imageUrls) => {
          formData.images = imageUrls;
          this.sendSupplementRequest(formData);
        }).catch((err) => {
          this.loading = false;
          common_vendor.index.showToast({
            title: "图片上传失败",
            icon: "none"
          });
        });
      } else {
        this.sendSupplementRequest(formData);
      }
    },
    // 上传补卡证明图片
    uploadSupplementImages() {
      return new Promise((resolve, reject) => {
        const uploadTasks = this.supplementImages.map((imagePath) => {
          return new Promise((uploadResolve, uploadReject) => {
            common_vendor.index.uploadFile({
              url: this.$api.baseUrl + "/attendance/upload",
              filePath: imagePath,
              name: "file",
              header: {
                token: common_vendor.index.getStorageSync("token")
              },
              success: (res) => {
                if (res.statusCode === 200) {
                  const data = JSON.parse(res.data);
                  if (data.code === 0 && data.data) {
                    uploadResolve(data.data.url);
                  } else {
                    uploadReject(new Error(data.msg || "上传失败"));
                  }
                } else {
                  uploadReject(new Error("上传失败"));
                }
              },
              fail: (err) => {
                uploadReject(err);
              }
            });
          });
        });
        Promise.all(uploadTasks).then(resolve).catch(reject);
      });
    },
    // 发送补卡申请请求
    sendSupplementRequest(formData) {
      utils_api.attendanceApi.submitSupplementApplication(formData).then((res) => {
        this.loading = false;
        if (res.code === 0) {
          common_vendor.index.showToast({
            title: res.msg || "补卡申请已提交",
            icon: "success"
          });
          this.$refs.supplementPopup.close();
          this.resetSupplementForm();
          this.getTodayClockRecord();
          this.getRecentRecords();
        } else {
          common_vendor.index.showToast({
            title: res.msg || "补卡申请失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        this.loading = false;
        common_vendor.index.__f__("error", "at pages/attendance/clock-in.vue:1034", "补卡申请失败:", err);
        common_vendor.index.showToast({
          title: "补卡申请失败",
          icon: "none"
        });
      });
    },
    // 获取打卡按钮文本
    getClockButtonText() {
      if (!this.clockInTime) {
        return "上班打卡";
      }
      if (!this.clockOutTime) {
        return "下班打卡";
      }
      return "今日打卡已完成";
    },
    // 获取操作描述
    getActionDescription() {
      const now = /* @__PURE__ */ new Date();
      const currentHour = now.getHours();
      const currentMinute = now.getMinutes();
      if (!this.clockInTime) {
        const clockInTimeArr = (this.attendanceRules.clockInTime || "08:30").split(":");
        const clockInHour = parseInt(clockInTimeArr[0]);
        const clockInMinute = parseInt(clockInTimeArr[1]);
        if (currentHour > clockInHour || currentHour === clockInHour && currentMinute > clockInMinute) {
          return `已超过上班时间${this.attendanceRules.clockInTime}，打卡将记为迟到`;
        } else {
          return `上班时间${this.attendanceRules.clockInTime}，请按时打卡`;
        }
      }
      if (!this.clockOutTime) {
        const clockOutTimeArr = (this.attendanceRules.clockOutTime || "17:30").split(":");
        const clockOutHour = parseInt(clockOutTimeArr[0]);
        const clockOutMinute = parseInt(clockOutTimeArr[1]);
        if (currentHour < clockOutHour || currentHour === clockOutHour && currentMinute < clockOutMinute) {
          return `下班时间${this.attendanceRules.clockOutTime}，提前打卡将记为早退`;
        } else {
          return `您已完成上班打卡，下班时间${this.attendanceRules.clockOutTime}`;
        }
      }
      return "您已完成今日打卡";
    },
    // 格式化日期为YYYY-MM-DD
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    // 获取用户ID
    getUserId() {
      const userId = common_vendor.index.getStorageSync("userId");
      if (!userId) {
        common_vendor.index.__f__("warn", "at pages/attendance/clock-in.vue:1101", "未找到用户ID，使用默认值1");
        return 1;
      }
      return userId || 1;
    },
    // 切换定位方式
    toggleLocationMethod() {
      this.useNativeGeolocation = !this.useNativeGeolocation;
      common_vendor.index.showToast({
        title: `已切换为${this.useNativeGeolocation ? "原生" : "统一"}定位API`,
        icon: "none",
        duration: 2e3
      });
      setTimeout(() => {
        this.getLocation();
      }, 500);
    },
    // 提交考勤打卡
    submitAttendance() {
      const now = /* @__PURE__ */ new Date();
      const clockType = !this.clockInTime ? "checkin" : "checkout";
      let status = "normal";
      if (clockType === "checkin") {
        const clockInTimeArr = this.attendanceRules.clockInTime.split(":");
        const clockInHour = parseInt(clockInTimeArr[0]);
        const clockInMinute = parseInt(clockInTimeArr[1]);
        const clockInTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), clockInHour, clockInMinute);
        if (now > clockInTime) {
          status = "late";
        }
      } else if (clockType === "checkout") {
        const clockOutTimeArr = this.attendanceRules.clockOutTime.split(":");
        const clockOutHour = parseInt(clockOutTimeArr[0]);
        const clockOutMinute = parseInt(clockOutTimeArr[1]);
        const clockOutTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), clockOutHour, clockOutMinute);
        if (now < clockOutTime) {
          status = "early";
        }
      }
      this.loading = true;
      common_vendor.index.showLoading({
        title: "打卡中..."
      });
      const currentLocation = {
        latitude: this.latitude,
        longitude: this.longitude
      };
      utils_api.attendanceApi.submitClock({
        clock_type: clockType,
        latitude: this.latitude,
        longitude: this.longitude,
        status,
        // 打卡状态
        user_id: this.getUserId()
        // 添加用户ID
      }).then((res) => {
        common_vendor.index.hideLoading();
        if (res.code === 200) {
          common_vendor.index.showToast({
            title: clockType === "checkin" ? "上班打卡成功" : "下班打卡成功",
            icon: "success"
          });
          this.getTodayClockRecord();
          this.getRecentRecords();
          this.uploadTrajectoryRecord(currentLocation.latitude, currentLocation.longitude).catch((err) => {
            common_vendor.index.__f__("error", "at pages/attendance/clock-in.vue:1186", "打卡后上传位置失败:", err);
          });
          const positionCycle = common_vendor.index.getStorageSync("positionCycle") || 1;
          if (clockType === "checkin") {
            this.startLocationUpload();
            common_vendor.index.__f__("log", "at pages/attendance/clock-in.vue:1195", "上班打卡成功，启动全局位置上传，上传周期:", positionCycle, "分钟");
          } else {
            this.stopLocationUpload();
            common_vendor.index.__f__("log", "at pages/attendance/clock-in.vue:1199", "下班打卡成功，停止全局位置上传");
          }
        } else {
          common_vendor.index.showToast({
            title: res.message || "打卡失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/attendance/clock-in.vue:1209", "打卡提交失败:", err);
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      }).finally(() => {
        this.loading = false;
      });
    },
    // 切换管理员设置
    goToAdminSettings() {
      common_vendor.index.__f__("log", "at pages/attendance/clock-in.vue:1221", "正在跳转到考勤规则设置页面");
      common_vendor.index.navigateTo({
        url: "/pages/attendance/admin/rules",
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/attendance/clock-in.vue:1226", "页面跳转失败:", err);
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "none"
          });
        }
      });
    },
    // 上传位置轨迹记录
    uploadTrajectoryRecord(latitude, longitude) {
      if (!longitude || !latitude || longitude === 0 || latitude === 0) {
        common_vendor.index.__f__("error", "at pages/attendance/clock-in.vue:1239", "经纬度无效，无法上传位置轨迹:", {
          longitude,
          latitude
        });
        return Promise.reject({ errMsg: "位置参数无效" });
      }
      const userId = this.getUserId();
      common_vendor.index.__f__("log", "at pages/attendance/clock-in.vue:1249", "上传位置轨迹:", {
        userId,
        longitude,
        latitude
      });
      return utils_api.attendanceApi.uploadPersonTrajectory({
        userId,
        // 传递userId参数
        employeeId: userId,
        // 同时传递employeeId参数，值与userId相同
        longitude,
        latitude
      }).then((res) => {
        if (res.code === 200) {
          common_vendor.index.__f__("log", "at pages/attendance/clock-in.vue:1263", "位置轨迹上传成功:", res.data);
          return res.data;
        } else {
          common_vendor.index.__f__("error", "at pages/attendance/clock-in.vue:1266", "位置轨迹上传失败:", res.message || "未知错误", res);
          return Promise.reject(res);
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/attendance/clock-in.vue:1270", "位置轨迹上传请求异常:", err);
        return Promise.reject(err);
      });
    },
    // 重新获取位置
    retryGetLocation() {
      this.requestLocationPermission();
    }
  }
};
if (!Array) {
  const _component_PermissionCheck = common_vendor.resolveComponent("PermissionCheck");
  _component_PermissionCheck();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.currentDate),
    b: common_vendor.t($data.currentTime),
    c: common_vendor.o((...args) => $options.goToAdminSettings && $options.goToAdminSettings(...args)),
    d: common_vendor.p({
      permission: "attendance:clock-rules"
    }),
    e: common_vendor.t($data.attendanceRules.clockInTime || "08:30"),
    f: common_vendor.t($data.attendanceRules.clockOutTime || "17:30"),
    g: common_vendor.t($data.clockInTime || "未打卡"),
    h: !$data.clockInTime ? 1 : "",
    i: common_vendor.t($data.clockOutTime || "未打卡"),
    j: !$data.clockOutTime ? 1 : "",
    k: common_vendor.t($options.getClockButtonText()),
    l: common_vendor.o((...args) => $options.handleClockIn && $options.handleClockIn(...args)),
    m: common_vendor.t($options.getActionDescription()),
    n: $data.recentRecords.length === 0
  }, $data.recentRecords.length === 0 ? {} : {}, {
    o: common_vendor.f($data.recentRecords, (record, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(record.date),
        b: common_vendor.t(record.week),
        c: common_vendor.t(record.clockInTime),
        d: record.clockInStatus === "late"
      }, record.clockInStatus === "late" ? {} : {}, {
        e: record.clockInStatus !== "normal" ? 1 : "",
        f: common_vendor.t(record.clockOutTime),
        g: record.clockOutStatus === "early"
      }, record.clockOutStatus === "early" ? {} : {}, {
        h: record.clockOutStatus !== "normal" ? 1 : "",
        i: index
      });
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/attendance/clock-in.js.map
