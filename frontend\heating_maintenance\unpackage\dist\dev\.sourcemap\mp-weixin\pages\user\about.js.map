{"version": 3, "file": "about.js", "sources": ["pages/user/about.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlci9hYm91dC52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"about-container\">\r\n\t\t<!-- 加载中提示 -->\r\n\t\t<view class=\"loading-container\" v-if=\"isLoading\">\r\n\t\t\t<view class=\"loading-spinner\"></view>\r\n\t\t\t<text class=\"loading-text\">加载中...</text>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 内容区域 -->\r\n\t\t<view v-else>\r\n\t\t\t<view class=\"logo-section\">\r\n\t\t\t\t<image class=\"logo\" :src=\"getFullImageUrl(systemParams.systemLogo)\" mode=\"aspectFit\"></image>\r\n\t\t\t\t<view class=\"app-info\">\r\n\t\t\t\t\t<text class=\"app-name\">{{ systemParams.systemName }}</text>\r\n\t\t\t\t\t<text class=\"app-version\">版本号: {{ systemParams.systemVersions }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"info-section\">\r\n\t\t\t\t<view class=\"info-group\">\r\n\t\t\t\t\t<view class=\"group-title\">系统信息</view>\r\n\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t<text class=\"item-label\">系统名称</text>\r\n\t\t\t\t\t\t<text class=\"item-value\">{{ systemParams.systemName }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t<text class=\"item-label\">系统版本</text>\r\n\t\t\t\t\t\t<text class=\"item-value\">{{ systemParams.systemVersions }} ({{ systemParams.buildNumber }})</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t<text class=\"item-label\">发布日期</text>\r\n\t\t\t\t\t\t<text class=\"item-value\">{{ formatDate(systemParams.releaseDate) }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-item\" @click=\"checkUpdate\">\r\n\t\t\t\t\t\t<text class=\"item-label\">检查更新</text>\r\n\t\t\t\t\t\t<text class=\"check-update\">检查 <text class=\"iconfont icon-arrow-right\"></text></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"info-group\">\r\n\t\t\t\t\t<view class=\"group-title\">开发团队</view>\r\n\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t<text class=\"item-label\">开发公司</text>\r\n\t\t\t\t\t\t<text class=\"item-value\">{{ systemParams.company }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t<text class=\"item-label\">联系电话</text>\r\n\t\t\t\t\t\t<text class=\"item-value\">{{ systemParams.mobile }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t<text class=\"item-label\">官方网站</text>\r\n\t\t\t\t\t\t<text class=\"item-value link\" @click=\"openWebsite\">{{ systemParams.internetAddr }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t<text class=\"item-label\">联系邮箱</text>\r\n\t\t\t\t\t\t<text class=\"item-value\">{{ systemParams.email }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"info-group\">\r\n\t\t\t\t\t<view class=\"group-title\">使用帮助</view>\r\n\t\t\t\t\t<view class=\"info-item\" @click=\"navigateTo('/pages/user/agreement')\">\r\n\t\t\t\t\t\t<text class=\"item-label\">用户协议</text>\r\n\t\t\t\t\t\t<text class=\"item-value arrow\">查看 <text class=\"iconfont icon-arrow-right\"></text></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-item\" @click=\"navigateTo('/pages/user/faq')\">\r\n\t\t\t\t\t\t<text class=\"item-label\">常见问题</text>\r\n\t\t\t\t\t\t<text class=\"item-value arrow\">查看 <text class=\"iconfont icon-arrow-right\"></text></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"info-item\" @click=\"contactSupport\">\r\n\t\t\t\t\t\t<text class=\"item-label\">联系客服</text>\r\n\t\t\t\t\t\t<text class=\"item-value arrow\">查看 <text class=\"iconfont icon-arrow-right\"></text></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"copyright\">\r\n\t\t\t\t<text class=\"copyright-text\">{{ systemParams.copyright }}</text>\r\n\t\t\t\t<text class=\"copyright-text\">All Rights Reserved</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { systemApi } from '../../utils/api';\r\nimport uploadUtils from '@/utils/upload.js';\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tsystemParams: {\r\n\t\t\t\tsystemName: '热力维护管理系统',\r\n\t\t\t\tsystemLogo: '/static/logo.png',\r\n\t\t\t\tsystemVersions: 'V1.0.0',\r\n\t\t\t\tbuildNumber: 'Build 20240502',\r\n\t\t\t\tcopyright: 'Copyright © 2024 陕西杰明新能源有限公司',\r\n\t\t\t\tcompany: '陕西杰明新能源有限公司',\r\n\t\t\t\tlinkman: '客服',\r\n\t\t\t\tmobile: '029-88888888',\r\n\t\t\t\tinternetAddr: 'www.example.com',\r\n\t\t\t\tcompanyAddr: '陕西省西安市',\r\n\t\t\t\tintro: '热力维护管理系统是一款专业的热力设备维护管理软件，提供工单管理、巡检管理、设备管理等功能。'\r\n\t\t\t},\r\n\t\t\tisLoading: true\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\t\t// 加载系统参数\r\n\t\tthis.loadSystemParams();\r\n\t},\r\n\tmethods: {\r\n\t\t// 格式化日期\r\n\t\tformatDate(dateStr) {\r\n\t\t  if (!dateStr) return \"\";\r\n\t\t  const date = new Date(dateStr);\r\n\t\t  if (isNaN(date.getTime())) return dateStr;\r\n\t\t\r\n\t\t  const year = date.getFullYear();\r\n\t\t  const month = (date.getMonth() + 1).toString();\r\n\t\t  const day = date.getDate().toString();\r\n\t\t\r\n\t\t  return `${year}年${month}月${day}日`;\r\n\t\t},\r\n\t\t// 加载系统参数\r\n\t\tloadSystemParams() {\r\n\t\t\tthis.isLoading = true;\r\n\t\t\t\r\n\t\t\tsystemApi.getSystemParams()\r\n\t\t\t\t.then(res => {\r\n\t\t\t\t\tif (res.code === 200 && res.data) {\r\n\t\t\t\t\t\t// 更新系统参数\r\n\t\t\t\t\t\tthis.systemParams = res.data;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.error('获取系统参数失败:', res.message);\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tconsole.error('获取系统参数异常:', err);\r\n\t\t\t\t})\r\n\t\t\t\t.finally(() => {\r\n\t\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 导航到页面\r\n\t\tnavigateTo(url) {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: url\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 打开网站\r\n\t\topenWebsite() {\r\n\t\t\t// 在实际应用中，这里应该使用系统浏览器打开网站\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '打开网站',\r\n\t\t\t\tcontent: '是否打开官方网站？',\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t// 实际应用中使用plus.runtime.openURL或其他方式打开网站\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '打开网站功能开发中...',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 获取完整的图片URL\r\n\t\tgetFullImageUrl(path) {\r\n\t\t\tconsole.log(uploadUtils.getFileUrl(path))\r\n\t\t\treturn uploadUtils.getFileUrl(path);\r\n\t\t},\r\n\t\t// 检查更新\r\n\t\tcheckUpdate() {\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '检查更新中...'\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 模拟检查更新\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\r\n\t\t\t\t// 这里模拟没有更新的情况\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '检查更新',\r\n\t\t\t\t\tcontent: '当前已是最新版本',\r\n\t\t\t\t\tshowCancel: false\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 如果有更新，可以显示如下对话框\r\n\t\t\t\t/*\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '发现新版本',\r\n\t\t\t\t\tcontent: '发现新版本 V1.1.0，是否立即更新？\\n\\n更新内容：\\n1. 修复了已知问题\\n2. 优化了系统性能\\n3. 增加了新功能',\r\n\t\t\t\t\tconfirmText: '立即更新',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t// 处理更新逻辑\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '更新功能开发中...',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t*/\r\n\t\t\t}, 1500);\r\n\t\t},\r\n\t\t\r\n\t\t// 联系客服\r\n\t\tcontactSupport() {\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '联系客服',\r\n\t\t\t\tcontent: `客服热线：${this.systemParams.mobile}\\n联系人：${this.systemParams.linkman}\\n工作时间：周一至周五 9:00-18:00`,\r\n\t\t\t\tshowCancel: false,\r\n\t\t\t\tconfirmText: '我知道了'\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.about-container {\r\n\tbackground-color: #f5f7fa;\r\n\tmin-height: 100vh;\r\n\tpadding-bottom: 30rpx;\r\n}\r\n\r\n.loading-container {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\theight: 100vh;\r\n\t\r\n\t.loading-spinner {\r\n\t\twidth: 80rpx;\r\n\t\theight: 80rpx;\r\n\t\tborder: 6rpx solid rgba(24, 144, 255, 0.2);\r\n\t\tborder-top: 6rpx solid $uni-color-primary;\r\n\t\tborder-radius: 50%;\r\n\t\tanimation: spin 1s linear infinite;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t\r\n\t.loading-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\t\r\n\t@keyframes spin {\r\n\t\t0% { transform: rotate(0deg); }\r\n\t\t100% { transform: rotate(360deg); }\r\n\t}\r\n}\r\n\r\n.logo-section {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 60rpx 0;\r\n\tbackground-color: #fff;\r\n\t\r\n\t.logo {\r\n\t\twidth: 180rpx;\r\n\t\theight: 180rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tborder-radius: 30rpx;\r\n\t\tbox-shadow: 0 10rpx 30rpx rgba(24, 144, 255, 0.2);\r\n\t}\r\n\t\r\n\t.app-info {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\t\r\n\t\t.app-name {\r\n\t\t\tfont-size: 36rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tcolor: #333;\r\n\t\t\tmargin-bottom: 10rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.app-version {\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: #999;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.info-section {\r\n\tpadding: 20rpx 0;\r\n\t\r\n\t.info-group {\r\n\t\tmargin: 20rpx 30rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 12rpx;\r\n\t\tpadding: 20rpx;\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\r\n\t\t\r\n\t\t.group-title {\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tcolor: #333;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t\tpadding-left: 20rpx;\r\n\t\t\tborder-left: 6rpx solid $uni-color-primary;\r\n\t\t}\r\n\t\t\r\n\t\t.info-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 20rpx 10rpx;\r\n\t\t\tborder-bottom: 1rpx solid rgba(0, 0, 0, 0.05);\r\n\t\t\t\r\n\t\t\t&:last-child {\r\n\t\t\t\tborder-bottom: none;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.item-label {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #666;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.item-value {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\t\r\n\t\t\t\t&.link {\r\n\t\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t\t\ttext-decoration: underline;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&.arrow {\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.iconfont {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tmargin-left: 10rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.check-update {\r\n\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\t\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tmargin-left: 10rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.copyright {\r\n\tmargin-top: 40rpx;\r\n\tpadding: 30rpx;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\t\r\n\t.copyright-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t\tline-height: 1.6;\r\n\t}\r\n}\r\n</style>", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/user/about.vue'\nwx.createPage(MiniProgramPage)"], "names": ["systemApi", "uni", "uploadUtils"], "mappings": ";;;;AAwFA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,cAAc;AAAA,QACb,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,gBAAgB;AAAA,QAChB,aAAa;AAAA,QACb,WAAW;AAAA,QACX,SAAS;AAAA,QACT,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,cAAc;AAAA,QACd,aAAa;AAAA,QACb,OAAO;AAAA,MACP;AAAA,MACD,WAAW;AAAA,IACZ;AAAA,EACA;AAAA,EACD,SAAS;AAER,SAAK,iBAAgB;AAAA,EACrB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,WAAW,SAAS;AAClB,UAAI,CAAC;AAAS,eAAO;AACrB,YAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,UAAI,MAAM,KAAK,QAAO,CAAE;AAAG,eAAO;AAElC,YAAM,OAAO,KAAK;AAClB,YAAM,SAAS,KAAK,SAAQ,IAAK,GAAG;AACpC,YAAM,MAAM,KAAK,QAAS,EAAC,SAAQ;AAEnC,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,IAC/B;AAAA;AAAA,IAED,mBAAmB;AAClB,WAAK,YAAY;AAEjBA,gBAAAA,UAAU,gBAAgB,EACxB,KAAK,SAAO;AACZ,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAEjC,eAAK,eAAe,IAAI;AAAA,eAClB;AACNC,wBAAc,MAAA,MAAA,SAAA,+BAAA,aAAa,IAAI,OAAO;AAAA,QACvC;AAAA,OACA,EACA,MAAM,SAAO;AACbA,0EAAc,aAAa,GAAG;AAAA,OAC9B,EACA,QAAQ,MAAM;AACd,aAAK,YAAY;AAAA,MAClB,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,WAAW,KAAK;AACfA,oBAAAA,MAAI,WAAW;AAAA,QACd;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AAEbA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAEhBA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACP,CAAC;AAAA,UACF;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAED,gBAAgB,MAAM;AACrBA,oBAAY,MAAA,MAAA,OAAA,+BAAAC,aAAAA,YAAY,WAAW,IAAI,CAAC;AACxC,aAAOA,aAAW,YAAC,WAAW,IAAI;AAAA,IAClC;AAAA;AAAA,IAED,cAAc;AACbD,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACR,CAAC;AAGD,iBAAW,MAAM;AAChBA,sBAAG,MAAC,YAAW;AAGfA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,QACb,CAAC;AAAA,MAmBD,GAAE,IAAI;AAAA,IACP;AAAA;AAAA,IAGD,iBAAiB;AAChBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS,QAAQ,KAAK,aAAa,MAAM;AAAA,MAAS,KAAK,aAAa,OAAO;AAAA;AAAA,QAC3E,YAAY;AAAA,QACZ,aAAa;AAAA,MACd,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;AC5NA,GAAG,WAAW,eAAe;"}