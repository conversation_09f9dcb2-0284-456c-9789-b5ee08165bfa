package com.heating.entity.weather;

import lombok.Data;
import jakarta.persistence.*;

/**
 * 气象站数据实体类
 */
@Entity
@Table(name = "t_weather_station_data")
@Data
public class TWeatherStationData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 气象站
     */
    @Column(name = "WS")
    private String ws;

    /**
     * 温度
     */
    @Column(name = "T")
    private Double t;

    /**
     * 体感温度
     */
    @Column(name = "F")
    private Double f;

    /**
     * 湿度
     */
    @Column(name = "H")
    private Double h;

    /**
     * 风速
     */
    @Column(name = "R")
    private Double r;

    /**
     * 太阳辐射
     */
    @Column(name = "S")
    private Double s;

    /**
     * 采集时间
     */
    @Column(name = "CollectDT")
    private String collectDt;
}
