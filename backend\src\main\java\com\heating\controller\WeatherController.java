package com.heating.controller;

import com.heating.dto.weather.WeatherRequest;
import com.heating.service.WeatherService;
import com.heating.util.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 气象数据控制器
 */
@RestController
@RequestMapping("/api/weather")
public class WeatherController {

    private static final Logger logger = LoggerFactory.getLogger(WeatherController.class);

    @Autowired
    private WeatherService weatherService;

    /**
     * 获取最新气象数据
     * @param request 包含地点信息的请求
     * @return 最新气象数据
     */
    @PostMapping("/latest")
    public ResponseEntity<?> getLatestWeatherData(@RequestBody WeatherRequest request) {
        logger.info("接收获取气象数据请求，地点: {}", request.getLocation());
        
        try {
            Map<String, Object> weatherData = weatherService.getLatestWeatherData(request);
            return ResponseEntity.ok(ApiResponse.success("获取气象数据成功", weatherData));
        } catch (Exception e) {
            logger.error("获取气象数据失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(ApiResponse.error("获取气象数据失败: " + e.getMessage()));
        }
    }
}
