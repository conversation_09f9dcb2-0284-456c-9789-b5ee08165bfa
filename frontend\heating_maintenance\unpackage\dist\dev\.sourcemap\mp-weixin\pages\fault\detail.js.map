{"version": 3, "file": "detail.js", "sources": ["pages/fault/detail.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZmF1bHQvZGV0YWlsLnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"fault-detail-container\">\n\t\t<!-- 基本信息卡片 -->\n\t\t<view class=\"detail-card\">\n\t\t\t<view class=\"card-header\">\n\t\t\t\t<text class=\"card-title\">故障信息</text>\n\t\t\t\t<view class=\"fault-status\" :class=\"getFaultStatusClass(faultInfo.fault_status)\">{{ getStatusText(faultInfo.fault_status) }}</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"info-group\">\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"info-label\">故障编号</text>\n\t\t\t\t\t<text class=\"info-value\">{{ faultInfo.fault_no || '暂无' }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"info-label\">换热站</text>\n\t\t\t\t\t<text class=\"info-value\">{{ faultInfo.heat_unit_name || '暂无' }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\" v-if=\"faultInfo.address\">\n\t\t\t\t\t<text class=\"info-label\">详细地址</text>\n\t\t\t\t\t<text class=\"info-value\">{{ faultInfo.address }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"info-label\">故障类型</text>\n\t\t\t\t\t<text class=\"info-value\">{{ faultInfo.fault_type || '暂无' }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"info-label\">故障等级</text>\n\t\t\t\t\t<text class=\"info-value level-tag\" :class=\"getFaultLevelClass(faultInfo.fault_level)\">{{ faultInfo.fault_level || '未知' }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"info-label\">故障来源</text>\n\t\t\t\t\t<text class=\"info-value\">{{ faultInfo.fault_source || '暂无' }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"info-label\">发生时间</text>\n\t\t\t\t\t<text class=\"info-value\">{{ faultInfo.occur_time || '暂无' }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"info-label\">上报人员</text>\n\t\t\t\t\t<text class=\"info-value\">{{ faultInfo.report_user_name || '暂无' }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"info-label\">上报时间</text>\n\t\t\t\t\t<text class=\"info-value\">{{ faultInfo.report_time || '暂无' }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"info-label\">创建时间</text>\n\t\t\t\t\t<text class=\"info-value\">{{ faultInfo.created_time || '暂无' }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 故障描述 -->\n\t\t<view class=\"detail-card\">\n\t\t\t<view class=\"card-header\">\n\t\t\t\t<text class=\"card-title\">故障描述</text>\n\t\t\t</view>\n\t\t\t<view class=\"fault-desc\">{{ faultInfo.fault_desc || '暂无描述' }}</view>\n\t\t</view>\n\t\t\n\t\t<!-- 图片附件 -->\n\t\t<view class=\"detail-card\" v-if=\"images && images.length > 0\">\n\t\t\t<view class=\"card-header\">\n\t\t\t\t<text class=\"card-title\">图片附件</text>\n\t\t\t</view>\n\t\t\t<view class=\"image-grid\">\n\t\t\t\t<view class=\"image-item\" v-for=\"(image, index) in images\" :key=\"index\" @click=\"previewImage(index)\">\n\t\t\t\t\t<image :src=\"getFullImageUrl(image)\" mode=\"aspectFill\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 视频附件 -->\n\t\t<view class=\"detail-card\" v-if=\"video\">\n\t\t\t<view class=\"card-header\">\n\t\t\t\t<text class=\"card-title\">视频附件</text>\n\t\t\t</view>\n\t\t\t<view class=\"video-container\">\n\t\t\t\t<video :src=\"getFullVideoUrl(video)\" controls\n\t\t\t\t       @error=\"onVideoError\" \n\t\t\t\t       show-center-play-btn=\"true\" \n\t\t\t\t       enable-progress-gesture=\"true\"></video>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 工单信息卡片 - 优化版 -->\n\t\t<view class=\"detail-card\" v-if=\"workOrderInfo\">\n\t\t\t<view class=\"card-header\">\n\t\t\t\t<text class=\"card-title\">工单信息</text>\n\t\t\t\t<view class=\"work-order-status\" :class=\"getWorkOrderStatusClass(workOrderInfo.status)\">{{ getWorkOrderStatusText(workOrderInfo.status) }}</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"info-group\">\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"info-label\">工单编号</text>\n\t\t\t\t\t<text class=\"info-value\">{{ workOrderInfo.code }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"info-label\">生成时间</text>\n\t\t\t\t\t<text class=\"info-value\">{{ workOrderInfo.create_time }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\" v-if=\"workOrderInfo.assignee\">\n\t\t\t\t\t<text class=\"info-label\">维修人员</text>\n\t\t\t\t\t<text class=\"info-value\">{{ workOrderInfo.assignee }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\" v-if=\"workOrderInfo.phone\">\n\t\t\t\t\t<text class=\"info-label\">联系电话</text>\n\t\t\t\t\t<text class=\"info-value\">{{ workOrderInfo.phone }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\" v-if=\"workOrderInfo.repair_content\">\n\t\t\t\t\t<text class=\"info-label\">维修内容</text>\n\t\t\t\t\t<text class=\"info-value\">{{ workOrderInfo.repair_content }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\" v-if=\"workOrderInfo.repair_result\">\n\t\t\t\t\t<text class=\"info-label\">维修结果</text>\n\t\t\t\t\t<text class=\"info-value\">{{ workOrderInfo.repair_result }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\" v-if=\"workOrderInfo.repair_materials\">\n\t\t\t\t\t<text class=\"info-label\">维修耗材</text>\n\t\t\t\t\t<text class=\"info-value\">{{ workOrderInfo.repair_materials }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\" v-if=\"workOrderInfo.complete_time\">\n\t\t\t\t\t<text class=\"info-label\">完成时间</text>\n\t\t\t\t\t<text class=\"info-value\">{{ workOrderInfo.complete_time }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 操作日志 - 优化版 -->\n\t\t<view class=\"detail-card\" v-if=\"operationLogs && operationLogs.length > 0\">\n\t\t\t<view class=\"card-header\">\n\t\t\t\t<text class=\"card-title\">处理记录</text>\n\t\t\t</view>\n\t\t\t<view class=\"timeline\">\n\t\t\t\t<view class=\"log-item\" v-for=\"(log, index) in operationLogs\" :key=\"index\">\n\t\t\t\t\t<view class=\"timeline-dot\" :class=\"getTimelineDotClass(log.operationType)\"></view>\n\t\t\t\t\t<view class=\"timeline-line\" v-if=\"index !== operationLogs.length - 1\"></view>\n\t\t\t\t\t<view class=\"log-content\">\n\t\t\t\t\t\t<view class=\"log-header\">\n\t\t\t\t\t\t\t<text class=\"log-type\">{{ log.operationType }}</text>\n\t\t\t\t\t\t\t<text class=\"log-time\">{{ log.createdAt }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"log-body\">\n\t\t\t\t\t\t\t<text class=\"log-desc\">{{ log.operationDesc }}</text>\n\t\t\t\t\t\t\t<text class=\"log-operator\" v-if=\"log.operatorName\">操作人：{{ log.operatorName }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 操作按钮 -->\n\t\t <PermissionCheck permission=\"fault:fault-handle\">\n\t\t<view class=\"action-buttons\" v-if=\"isAdmin && faultInfo.fault_status === '待确认'\">\n\t\t\t<button class=\"btn-confirm\" @click=\"confirmFault\">确认故障</button>\n\t\t\t<button class=\"btn-reject\" @click=\"rejectFault\">退回上报</button>\n\t\t</view>\n\t\t</PermissionCheck>\n\t\t<!-- 提示信息 -->\n\t\t<view class=\"status-tip\" v-if=\"faultInfo.fault_status === '已退回'\">\n\t\t\t<text>该故障上报已被退回，请检查信息准确性后重新上报。</text>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { faultApi } from '@/utils/api.js';\n\timport uploadUtils from '@/utils/upload.js';\n\timport PermissionCheck from \"@/components/PermissionCheck.vue\"; // 导入权限检查组件\n\texport default {\n\t\tcomponents: {\n\t\t  PermissionCheck, // 本地注册组件\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tfaultId: null,\n\t\t\t\tfaultInfo: {},\n\t\t\t\timages: [],\n\t\t\t\tvideo: '',\n\t\t\t\tloading: true,\n\t\t\t\tloadError: false,\n\t\t\t\tisAdmin: true,\n\t\t\t\tisDev: process.env.NODE_ENV === 'development',\n\t\t\t\toperationLogs: [],\n\t\t\t\t// 工单信息\n\t\t\t\tworkOrderInfo: null\n\t\t\t}\n\t\t},\n\t\tonShow() {\n\t\t\tthis.loadFaultDetail();\n\t\t},\n\t\tonLoad(options) {\n\t\t\tthis.faultId = options.id;\n\t\t\tif (this.faultId) {\n\t\t\t\tthis.loadFaultDetail();\n\t\t\t} else {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '参数错误，无法获取故障详情',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.navigateBack();\n\t\t\t\t}, 1500);\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// 加载故障详情\n\t\t\tloadFaultDetail() {\n\t\t\t\t// 显示加载提示\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '加载中...'\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tthis.loading = true;\n\t\t\t\tthis.loadError = false;\n\t\t\t\t\n\t\t\t\t// 调用API获取故障详情\n\t\t\t\tfaultApi.getFaultDetail(this.faultId)\n\t\t\t\t\t.then(res => {\n\t\t\t\t\t\tif (res.code === 200) {\n\t\t\t\t\t\t\t// 处理接口返回的数据结构\n\t\t\t\t\t\t\tconsole.log('故障详情数据:', res.data);\n\t\t\t\t\t\t\tif (res.data && res.data.fault_info) {\n\t\t\t\t\t\t\t\tthis.faultInfo = res.data.fault_info;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 处理图片列表\n\t\t\t\t\t\t\tif (res.data && Array.isArray(res.data.images)) {\n\t\t\t\t\t\t\t\tthis.images = res.data.images;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis.images = [];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 处理视频\n\t\t\t\t\t\t\tif (res.data && res.data.video) {\n\t\t\t\t\t\t\t\tthis.video = res.data.video;\n\t\t\t\t\t\t\t\tconsole.log('视频地址:', this.video);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis.video = '';\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 处理操作日志\n\t\t\t\t\t\t\tif (res.data && Array.isArray(res.data.operation_logs)) {\n\t\t\t\t\t\t\t\t// 处理操作日志中的日期和操作人信息\n\t\t\t\t\t\t\t\tthis.operationLogs = res.data.operation_logs.map(log => {\n\t\t\t\t\t\t\t\t\t// 创建新对象避免直接修改原始数据\n\t\t\t\t\t\t\t\t\tconst processedLog = {...log};\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// 处理操作人ID显示为时间的问题\n\t\t\t\t\t\t\t\t\tif (processedLog.operatorId && !processedLog.operatorName) {\n\t\t\t\t\t\t\t\t\t\t// 如果没有操作人名称，但有ID，则显示\"操作人ID: xxx\"\n\t\t\t\t\t\t\t\t\t\tprocessedLog.operatorName = \"操作人ID: \" + processedLog.operatorId;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// 确保日期显示正确\n\t\t\t\t\t\t\t\t\tif (processedLog.createdAt) {\n\t\t\t\t\t\t\t\t\t\t// 如果createdAt是数字(时间戳)或看起来像数字，尝试格式化\n\t\t\t\t\t\t\t\t\t\tif (!isNaN(processedLog.createdAt) || /^\\d+$/.test(processedLog.createdAt)) {\n\t\t\t\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\t\t\t\t// 尝试将时间戳转换为日期\n\t\t\t\t\t\t\t\t\t\t\t\tconst date = new Date(parseInt(processedLog.createdAt));\n\t\t\t\t\t\t\t\t\t\t\t\tif (!isNaN(date.getTime())) {\n\t\t\t\t\t\t\t\t\t\t\t\t\tprocessedLog.createdAt = this.formatDateTime(date);\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\t\t\t\t\t\tconsole.error(\"日期转换错误:\", e);\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\treturn processedLog;\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis.operationLogs = [];\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\n\t\t\t\t\t\t\t// 处理工单信息\n\t\t\t\t\t\t\tif (res.data && res.data.work_order) {\n\t\t\t\t\t\t\t\tthis.workOrderInfo = res.data.work_order;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis.workOrderInfo = null;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.loadError = true;\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: res.message || '获取故障详情失败',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\tconsole.error('获取故障详情异常:', err);\n\t\t\t\t\t\tthis.loadError = true;\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '网络异常，请稍后重试',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t})\n\t\t\t\t\t.finally(() => {\n\t\t\t\t\t\tthis.loading = false;\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 获取完整的图片URL\n\t\t\tgetFullImageUrl(path) {\n\t\t\t\tconsole.log(uploadUtils.getFileUrl(path))\n\t\t\t\treturn uploadUtils.getFileUrl(path);\n\t\t\t},\n\t\t\t\n\t\t\t// 获取完整的视频URL\n\t\t\tgetFullVideoUrl(path) {\n\t\t\t\t// 处理视频URL\n\t\t\t\tif (!path) return '';\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\t// 特殊处理以@开头的URL\n\t\t\t\t\tif (path && path.startsWith('@http')) {\n\t\t\t\t\t\tconst cleanUrl = path.substring(1); // 去除@符号\n\t\t\t\t\t\tconsole.log('视频路径以@http开头，清理后:', cleanUrl);\n\t\t\t\t\t\treturn cleanUrl;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 如果已经是完整URL，直接返回\n\t\t\t\t\tif (path.startsWith('http')) {\n\t\t\t\t\t\tconsole.log('视频已是完整URL，直接返回:', path);\n\t\t\t\t\t\treturn path;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 使用工具方法获取完整URL\n\t\t\t\t\tconst fullUrl = uploadUtils.getFileUrl(path);\n\t\t\t\t\tconsole.log('处理后的视频URL:', fullUrl);\n\t\t\t\t\t\n\t\t\t\t\t// 添加时间戳避免缓存问题\n\t\t\t\t\treturn fullUrl + '?t=' + new Date().getTime();\n\t\t\t\t} catch (err) {\n\t\t\t\t\tconsole.error('视频URL处理出错:', err);\n\t\t\t\t\treturn path; // 出错时返回原路径\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 获取状态文本\n\t\t\tgetStatusText(status) {\n\t\t\t\tconst statusMap = {\n\t\t\t\t\t'待确认': '待确认',\n\t\t\t\t\t'已确认': '已确认',\n\t\t\t\t\t'已退回': '已退回',\n\t\t\t\t};\n\t\t\t\treturn statusMap[status] || '未知';\n\t\t\t},\n\t\t\t\n\t\t\t// 获取状态对应的CSS类名\n\t\t\tgetFaultStatusClass(status) {\n\t\t\t\tconst statusClassMap = {\n\t\t\t\t\t'待确认': 'pending',\n\t\t\t\t\t'已确认': 'confirmed',\n\t\t\t\t\t'已退回': 'rejected',\n\t\t\t\t\t'pending': '待确认',\n\t\t\t\t\t'confirmed': '已确认',\n\t\t\t\t\t'rejected': '已退回',\n\t\t\t\t};\n\t\t\t\treturn statusClassMap[status] || 'pending';\n\t\t\t},\n\t\t\t// 格式化日期时间\n\t\t\tformatDateTime(date) {\n\t\t\t\tif (!date) return \"\";\n\t\t\t\t\n\t\t\t\t// 如果是时间戳（数字）\n\t\t\t\tif (typeof date === 'number') {\n\t\t\t\t\tconst d = new Date(date);\n\t\t\t\t\tif (!isNaN(d.getTime())) {\n\t\t\t\t\t\treturn `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, \"0\")}-${String(d.getDate()).padStart(2, \"0\")} ${String(d.getHours()).padStart(2, \"0\")}:${String(d.getMinutes()).padStart(2, \"0\")}`;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 如果是Date对象\n\t\t\t\tif (date instanceof Date) {\n\t\t\t\t\treturn `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, \"0\")}-${String(date.getDate()).padStart(2, \"0\")} ${String(date.getHours()).padStart(2, \"0\")}:${String(date.getMinutes()).padStart(2, \"0\")}`;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 如果是字符串但可能需要标准化格式\n\t\t\t\tif (typeof date === 'string') {\n\t\t\t\t\t// 尝试解析日期字符串，如果解析失败则返回原始字符串\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst parsedDate = new Date(date);\n\t\t\t\t\t\tif (!isNaN(parsedDate.getTime())) {\n\t\t\t\t\t\t\treturn `${parsedDate.getFullYear()}-${String(parsedDate.getMonth() + 1).padStart(2, \"0\")}-${String(parsedDate.getDate()).padStart(2, \"0\")} ${String(parsedDate.getHours()).padStart(2, \"0\")}:${String(parsedDate.getMinutes()).padStart(2, \"0\")}`;\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.error(\"日期解析错误:\", e);\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 如果解析失败，返回原始字符串\n\t\t\t\t\treturn date;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 其他情况，尝试转为字符串\n\t\t\t\treturn String(date);\n\t\t\t},\n\t\t\t// 获取等级对应的CSS类名\n\t\t\tgetFaultLevelClass(level) {\n\t\t\t\tconst levelMap = {\n\t\t\t\t\t'minor': 'minor',\n\t\t\t\t\t'normal': 'normal',\n\t\t\t\t\t'critical': 'critical',\n\t\t\t\t\t'emergency': 'emergency',\n\t\t\t\t\t'轻微': 'minor',\n\t\t\t\t\t'一般': 'normal',\n\t\t\t\t\t'严重': 'critical',\n\t\t\t\t\t'紧急': 'emergency'\n\t\t\t\t};\n\t\t\t\treturn levelMap[level] || 'normal';\n\t\t\t},\n\t\t\t\n\t\t\t// 获取工单状态文本\n\t\t\tgetWorkOrderStatusText(status) {\n\t\t\t\tconst statusMap = {\n\t\t\t\t\t'pending': '待接单',\n\t\t\t\t\t'in_progress': '处理中',\n\t\t\t\t\t'completed': '已完成',\n\t\t\t\t\t'待接单': '待接单',\n\t\t\t\t\t'处理中': '处理中',\n\t\t\t\t\t'已完成': '已完成'\n\t\t\t\t};\n\t\t\t\treturn statusMap[status] || '未知';\n\t\t\t},\n\t\t\t\n\t\t\t// 获取工单状态对应的CSS类名\n\t\t\tgetWorkOrderStatusClass(status) {\n\t\t\t\tconst statusClassMap = {\n\t\t\t\t\t'pending': 'pending',\n\t\t\t\t\t'in_progress': 'in_progress',\n\t\t\t\t\t'completed': 'completed',\n\t\t\t\t\t'待接单': 'pending',\n\t\t\t\t\t'处理中': 'in_progress',\n\t\t\t\t\t'已完成': 'completed'\n\t\t\t\t};\n\t\t\t\treturn statusClassMap[status] || 'pending';\n\t\t\t},\n\t\t\t\n\t\t\t// 预览图片\n\t\t\tpreviewImage(index) {\n\t\t\t\tif (this.images && this.images.length > 0) {\n\t\t\t\t\t// 将所有图片路径转换为完整URL\n\t\t\t\t\tconst fullUrls = this.images.map(path => this.getFullImageUrl(path));\n\t\t\t\t\tuni.previewImage({\n\t\t\t\t\t\turls: fullUrls,\n\t\t\t\t\t\tcurrent: fullUrls[index]\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// 确认故障\n\t\t\tconfirmFault() {\n\t\t\t\tconst operatorId = uni.getStorageSync('userId') || null; // 获取当前操作员ID\n\t\t\t\tconst targetStatus = '已确认';\n\n\t\t\t\tif (!operatorId) {\n\t\t\t\t\tuni.showToast({ title: '无法获取操作员信息', icon: 'none' });\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// 显示 Modal 再次确认\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '确认操作',\n\t\t\t\t\tcontent: '您正在确认该故障为有效上报，请确保已核实故障真实性\\n（确认后将生成待处理工单）',\n\t\t\t\t\tconfirmText: '确认',\n\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\tsuccess: (modalRes) => {\n\t\t\t\t\t\tif (modalRes.confirm) {\n\t\t\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\t\t\ttitle: '提交中...'\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 构建请求数据\n\t\t\t\t\t\t\tconst requestData = {\n\t\t\t\t\t\t\t\tfault_id: this.faultId,\n\t\t\t\t\t\t\t\toperator_id: operatorId,\n\t\t\t\t\t\t\t\tfault_status: targetStatus,\n\t\t\t\t\t\t\t\theat_unit_id: this.faultInfo.heat_unit_id // 添加热用户ID\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 调用API\n\t\t\t\t\t\t\tfaultApi.updateFaultStatus(requestData)\n\t\t\t\t\t\t\t\t.then(apiRes => {\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t\tif (apiRes.code === 200) {\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\ttitle: '故障已确认',\n\t\t\t\t\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\t\t\t\t\tduration: 1500 // 稍长提示时间\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t// 确认成功后返回上一页\n\t\t\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\t\t\t// 设置一个标记，表示发生了状态变更，需要刷新列表\n\t\t\t\t\t\t\t\t\t\t\tgetApp().globalData = getApp().globalData || {};\n\t\t\t\t\t\t\t\t\t\t\tgetApp().globalData.refreshFaultList = true;\n\t\t\t\t\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tuni.showToast({ title: apiRes.message || '操作失败', icon: 'none' });\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t\tconsole.error('确认故障 API 调用失败:', err);\n\t\t\t\t\t\t\t\t\tuni.showToast({ title: '网络错误，请稍后重试', icon: 'none' });\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t} // success end\n\t\t\t\t}); // showModal end\n\t\t\t},\n\t\t\t\n\t\t\t// 退回故障上报\n\t\t\trejectFault() {\n\t\t\t\tconst operatorId = uni.getStorageSync('userId') || null; // 获取当前操作员ID\n\t\t\t\tconst targetStatus = '已退回';\n\t\t\t\t\n\t\t\t\tif (!operatorId) {\n\t\t\t\t\tuni.showToast({ title: '无法获取操作员信息', icon: 'none' });\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '退回上报',\n\t\t\t\t\tcontent: '确认退回此故障上报？',\n\t\t\t\t\tconfirmText: '确认',\n\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t// 实际应用中这里会调用API退回故障上报\n\t\t\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\t\t\ttitle: '提交中...'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 构建请求数据\n\t\t\t\t\t\t\tconst requestData = {\n\t\t\t\t\t\t\t\tfault_id: this.faultId,\n\t\t\t\t\t\t\t\toperator_id: operatorId,\n\t\t\t\t\t\t\t\tfault_status: targetStatus\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 调用API\n\t\t\t\t\t\t\tfaultApi.updateFaultStatus(requestData)\n\t\t\t\t\t\t\t\t.then(apiRes => {\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t\tif (apiRes.code === 200) {\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\ttitle: '故障已退回',\n\t\t\t\t\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\t\t\t\t\tduration: 1500 // 稍长提示时间\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t// 确认成功后返回上一页\n\t\t\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\t\t\t// 设置一个标记，表示发生了状态变更，需要刷新列表\n\t\t\t\t\t\t\t\t\t\t\tgetApp().globalData = getApp().globalData || {};\n\t\t\t\t\t\t\t\t\t\t\tgetApp().globalData.refreshFaultList = true;\n\t\t\t\t\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tuni.showToast({ title: apiRes.message || '操作失败', icon: 'none' });\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t\tconsole.error('故障退回 API 调用失败:', err);\n\t\t\t\t\t\t\t\t\tuni.showToast({ title: '网络错误，请稍后重试', icon: 'none' });\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 视频加载错误处理\n\t\t\tonVideoError(e) {\n\t\t\t\tconsole.error('视频加载失败:', e.detail);\n\t\t\t\tconsole.error('视频路径:', this.video);\n\t\t\t\tconsole.error('处理后URL:', this.getFullVideoUrl(this.video));\n\t\t\t\tthis.videoLoadError = true;\n\t\t\t\t\n\t\t\t\t// 尝试加载失败时，记录到日志并显示提示\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '视频加载失败，请尝试在浏览器中查看',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 在浏览器中打开视频\n\t\t\topenVideoInBrowser() {\n\t\t\t\tlet url = this.getFullVideoUrl(this.video);\n\t\t\t\t\n\t\t\t\t// 检查URL是否有效\n\t\t\t\tif (!url) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '视频URL无效',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconsole.log('在浏览器中打开视频:', url);\n\t\t\t\t\n\t\t\t\t// 使用系统浏览器打开\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\tplus.runtime.openURL(url, (err) => {\n\t\t\t\t\tif (err) {\n\t\t\t\t\t\tconsole.error('打开浏览器失败:', err);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '打开浏览器失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t\t\n\t\t\t\t// #ifdef H5\n\t\t\t\twindow.open(url, '_blank');\n\t\t\t\t// #endif\n\t\t\t\t\n\t\t\t\t// #ifdef MP\n\t\t\t\tuni.setClipboardData({\n\t\t\t\t\tdata: url,\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: 'URL已复制，请在浏览器中打开',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t\n\t\t\t// 获取时间轴点类名\n\t\t\tgetTimelineDotClass(operationType) {\n\t\t\t\t// 基于操作类型的部分关键词匹配\n\t\t\t\tif (!operationType) return 'dot-unknown';\n\t\t\t\t\n\t\t\t\tif (operationType.includes('上报') || operationType.includes('创建')) {\n\t\t\t\t\treturn 'dot-report';\n\t\t\t\t} else if (operationType.includes('确认') || operationType.includes('接单')) {\n\t\t\t\t\treturn 'dot-confirm';\n\t\t\t\t} else if (operationType.includes('退回') || operationType.includes('拒绝')) {\n\t\t\t\t\treturn 'dot-reject';\n\t\t\t\t} else if (operationType.includes('维修') || operationType.includes('处理')) {\n\t\t\t\t\treturn 'dot-repair';\n\t\t\t\t} else if (operationType.includes('完成') || operationType.includes('结束')) {\n\t\t\t\t\treturn 'dot-complete';\n\t\t\t\t} else if (operationType.includes('转派') || operationType.includes('分配')) {\n\t\t\t\t\treturn 'dot-assign';\n\t\t\t\t} else {\n\t\t\t\t\treturn 'dot-unknown';\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t.fault-detail-container {\n\t\tpadding: 30rpx;\n\t\tbackground-color: #f5f5f5;\n\t\tmin-height: 100vh;\n\t}\n\t\n\t.detail-card {\n\t\tbackground-color: #fff;\n\t\tborder-radius: 12rpx;\n\t\tpadding: 30rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\n\t\t\n\t\t.card-header {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\tmargin-bottom: 30rpx;\n\t\t\t\n\t\t\t.card-title {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: bold;\n\t\t\t\tcolor: $uni-text-color;\n\t\t\t\tposition: relative;\n\t\t\t\tpadding-left: 20rpx;\n\t\t\t\t\n\t\t\t\t&::before {\n\t\t\t\t\tcontent: '';\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\tleft: 0;\n\t\t\t\t\ttop: 50%;\n\t\t\t\t\ttransform: translateY(-50%);\n\t\t\t\t\twidth: 8rpx;\n\t\t\t\t\theight: 32rpx;\n\t\t\t\t\tbackground-color: $uni-color-primary;\n\t\t\t\t\tborder-radius: 4rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.fault-status, .work-order-status {\n\t\t\t\tpadding: 6rpx 20rpx;\n\t\t\t\tborder-radius: 4rpx;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\n\t\t\t\t&.pending {\n\t\t\t\t\tbackground-color: rgba(24, 144, 255, 0.1);\n\t\t\t\t\tcolor: $uni-color-primary;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&.confirmed {\n\t\t\t\t\tbackground-color: rgba(82, 196, 26, 0.1);\n\t\t\t\t\tcolor: $uni-color-success;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&.rejected {\n\t\t\t\t\tbackground-color: rgba(245, 34, 45, 0.1);\n\t\t\t\t\tcolor: $uni-color-error;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&.in_progress {\n\t\t\t\t\tbackground-color: rgba(250, 173, 20, 0.1);\n\t\t\t\t\tcolor: $uni-color-warning;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&.completed {\n\t\t\t\t\tbackground-color: rgba(82, 196, 26, 0.1);\n\t\t\t\t\tcolor: $uni-color-success;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.info-group {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\tgap: 20rpx;\n\t\t\t\n\t\t\t.info-item {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: flex-start;\n\t\t\t\t\n\t\t\t\t.info-label {\n\t\t\t\t\tmin-width: 160rpx;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: $uni-text-color-grey;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.info-value {\n\t\t\t\t\tflex: 1;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: $uni-text-color;\n\t\t\t\t\tword-break: break-all;\n\t\t\t\t\t\n\t\t\t\t\t&.level-tag {\n\t\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\t\tpadding: 4rpx 16rpx;\n\t\t\t\t\t\tborder-radius: 4rpx;\n\t\t\t\t\t\t\n\t\t\t\t\t\t&.minor {\n\t\t\t\t\t\t\tbackground-color: rgba(24, 144, 255, 0.1);\n\t\t\t\t\t\t\tcolor: $uni-color-primary;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t&.normal {\n\t\t\t\t\t\t\tbackground-color: rgba(82, 196, 26, 0.1);\n\t\t\t\t\t\t\tcolor: $uni-color-success;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t&.critical {\n\t\t\t\t\t\t\tbackground-color: rgba(250, 173, 20, 0.1);\n\t\t\t\t\t\t\tcolor: $uni-color-warning;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t&.emergency {\n\t\t\t\t\t\t\tbackground-color: rgba(245, 34, 45, 0.1);\n\t\t\t\t\t\t\tcolor: $uni-color-error;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.fault-desc {\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: $uni-text-color;\n\t\t\tline-height: 1.6;\n\t\t}\n\t\t\n\t\t.image-grid {\n\t\t\tdisplay: flex;\n\t\t\tflex-wrap: wrap;\n\t\t\tmargin: 0 -10rpx;\n\t\t\t\n\t\t\t.image-item {\n\t\t\t\twidth: 33.33%;\n\t\t\t\tpadding: 10rpx;\n\t\t\t\tbox-sizing: border-box;\n\t\t\t\t\n\t\t\t\timage {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\theight: 200rpx;\n\t\t\t\t\tborder-radius: 8rpx;\n\t\t\t\t\tbackground-color: #f5f5f5;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.video-container {\n\t\t\tmargin-top: 20rpx;\n\t\t\t\n\t\t\tvideo {\n\t\t\t\twidth: 100%;\n\t\t\t\theight: 400rpx;\n\t\t\t\tborder-radius: 8rpx;\n\t\t\t\tbackground-color: #000;\n\t\t\t}\n\t\t\t\n\t\t\t.video-fallback {\n\t\t\t\twidth: 100%;\n\t\t\t\theight: 400rpx;\n\t\t\t\tbackground-color: #f5f5f5;\n\t\t\t\tborder-radius: 8rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\t\n\t\t\t\t.play-icon {\n\t\t\t\t\twidth: 80rpx;\n\t\t\t\t\theight: 80rpx;\n\t\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.fallback-text {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: #666;\n\t\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.btn-open-browser {\n\t\t\t\t\tbackground-color: $uni-color-primary;\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tpadding: 10rpx 30rpx;\n\t\t\t\t\tborder-radius: 8rpx;\n\t\t\t\t\tmargin-top: 20rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.video-debug-info {\n\t\t\tmargin-top: 20rpx;\n\t\t\tpadding: 20rpx;\n\t\t\tbackground-color: #f8f8f8;\n\t\t\tborder-radius: 8rpx;\n\t\t\tfont-size: 24rpx;\n\t\t\t\n\t\t\t.debug-title {\n\t\t\t\tfont-weight: bold;\n\t\t\t\tmargin-bottom: 10rpx;\n\t\t\t\tdisplay: block;\n\t\t\t}\n\t\t\t\n\t\t\t.debug-text {\n\t\t\t\tcolor: #666;\n\t\t\t\tword-break: break-all;\n\t\t\t\tdisplay: block;\n\t\t\t\tmargin-bottom: 6rpx;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.btn-view-order {\n\t\t\tmargin-top: 20rpx;\n\t\t\tbackground-color: #f0f8ff;\n\t\t\tborder: 1rpx solid $uni-color-primary;\n\t\t\tborder-radius: 8rpx;\n\t\t\tpadding: 16rpx 0;\n\t\t\ttext-align: center;\n\t\t\t\n\t\t\ttext {\n\t\t\t\tcolor: $uni-color-primary;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.action-buttons {\n\t\tdisplay: flex;\n\t\tgap: 30rpx;\n\t\tmargin-top: 50rpx;\n\t\tmargin-bottom: 30rpx;\n\t\t\n\t\tbutton {\n\t\t\tflex: 1;\n\t\t\tmargin: 0;\n\t\t\theight: 88rpx;\n\t\t\tline-height: 88rpx;\n\t\t\tfont-size: 30rpx;\n\t\t\tborder-radius: 8rpx;\n\t\t\t\n\t\t\t&.btn-confirm {\n\t\t\t\tbackground-color: $uni-color-primary;\n\t\t\t\tcolor: #fff;\n\t\t\t}\n\t\t\t\n\t\t\t&.btn-reject {\n\t\t\t\tbackground-color: #fff;\n\t\t\t\tcolor: $uni-color-error;\n\t\t\t\tborder: 1rpx solid $uni-color-error;\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.status-tip {\n\t\tbackground-color: rgba(245, 34, 45, 0.1);\n\t\tpadding: 20rpx 30rpx;\n\t\tborder-radius: 8rpx;\n\t\tmargin: 30rpx 0;\n\t\t\n\t\ttext {\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: $uni-color-error;\n\t\t}\n\t}\n\t\n\t/* 操作日志样式 - 时间轴效果 */\n\t.timeline {\n\t\tposition: relative;\n\t\tpadding: 20rpx 0;\n\t\t\n\t\t.log-item {\n\t\t\tposition: relative;\n\t\t\tpadding-left: 40rpx;\n\t\t\tmargin-bottom: 30rpx;\n\t\t\t\n\t\t\t&:last-child {\n\t\t\t\tmargin-bottom: 0;\n\t\t\t}\n\t\t\t\n\t\t\t.timeline-dot {\n\t\t\t\tposition: absolute;\n\t\t\t\tleft: 0;\n\t\t\t\ttop: 16rpx;\n\t\t\t\twidth: 20rpx;\n\t\t\t\theight: 20rpx;\n\t\t\t\tborder-radius: 50%;\n\t\t\t\tbackground-color: #666;\n\t\t\t\tz-index: 2;\n\t\t\t\t\n\t\t\t\t&.dot-report {\n\t\t\t\t\tbackground-color: $uni-color-primary;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&.dot-confirm {\n\t\t\t\t\tbackground-color: $uni-color-success;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&.dot-reject {\n\t\t\t\t\tbackground-color: $uni-color-error;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&.dot-repair {\n\t\t\t\t\tbackground-color: $uni-color-warning;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&.dot-complete {\n\t\t\t\t\tbackground-color: $uni-color-success;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&.dot-assign {\n\t\t\t\t\tbackground-color: #8a2be2; // 紫色\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.timeline-line {\n\t\t\t\tposition: absolute;\n\t\t\t\tleft: 9rpx;\n\t\t\t\ttop: 36rpx;\n\t\t\t\twidth: 2rpx;\n\t\t\t\theight: calc(100% + 10rpx);\n\t\t\t\tbackground-color: rgba(0, 0, 0, 0.1);\n\t\t\t\tz-index: 1;\n\t\t\t}\n\t\t\t\n\t\t\t.log-content {\n\t\t\t\tbackground-color: rgba(0, 0, 0, 0.02);\n\t\t\t\tborder-radius: 8rpx;\n\t\t\t\tpadding: 16rpx;\n\t\t\t\t\n\t\t\t\t.log-header {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tmargin-bottom: 10rpx;\n\t\t\t\t\t\n\t\t\t\t\t.log-type {\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\tcolor: $uni-color-primary;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.log-time {\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tcolor: $uni-text-color-grey;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.log-body {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex-direction: column;\n\t\t\t\t\tgap: 6rpx;\n\t\t\t\t\t\n\t\t\t\t\t.log-desc {\n\t\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\t\tcolor: $uni-text-color;\n\t\t\t\t\t\tline-height: 1.5;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.log-operator {\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tcolor: $uni-text-color-grey;\n\t\t\t\t\t\tmargin-top: 4rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</style> ", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/fault/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "faultApi", "uploadUtils"], "mappings": ";;;;AAyKC,MAAA,kBAA4B,MAAA;AAC5B,MAAK,YAAU;AAAA,EACd,YAAY;AAAA,IACV;AAAA;AAAA,EACF;AAAA,EACA,OAAO;AACC,WAAA;AAAA,MACN,SAAS;AAAA,MACT,WAAW,CAAC;AAAA,MACZ,QAAQ,CAAC;AAAA,MACT,OAAO;AAAA,MACP,SAAS;AAAA,MACT,WAAW;AAAA,MACX,SAAS;AAAA,MACT,OAAO;AAAA,MACP,eAAe,CAAC;AAAA;AAAA,MAEhB,eAAe;AAAA,IAAA;AAAA,EAEjB;AAAA,EACA,SAAS;AACR,SAAK,gBAAgB;AAAA,EACtB;AAAA,EACA,OAAO,SAAS;AACf,SAAK,UAAU,QAAQ;AACvB,QAAI,KAAK,SAAS;AACjB,WAAK,gBAAgB;AAAA,IAAA,OACf;AACNA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACN;AACD,iBAAW,MAAM;AAChBA,sBAAA,MAAI,aAAa;AAAA,SACf,IAAI;AAAA,IACR;AAAA,EACD;AAAA,EACA,SAAS;AAAA;AAAA,IAER,kBAAkB;AAEjBA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MAAA,CACP;AAED,WAAK,UAAU;AACf,WAAK,YAAY;AAGjBC,gBAAA,SAAS,eAAe,KAAK,OAAO,EAClC,KAAK,CAAO,QAAA;AACR,YAAA,IAAI,SAAS,KAAK;AAErBD,wBAAA,MAAY,MAAA,OAAA,iCAAA,WAAW,IAAI,IAAI;AAC/B,cAAI,IAAI,QAAQ,IAAI,KAAK,YAAY;AAC/B,iBAAA,YAAY,IAAI,KAAK;AAAA,UAC3B;AAGA,cAAI,IAAI,QAAQ,MAAM,QAAQ,IAAI,KAAK,MAAM,GAAG;AAC1C,iBAAA,SAAS,IAAI,KAAK;AAAA,UAAA,OACjB;AACN,iBAAK,SAAS;UACf;AAGA,cAAI,IAAI,QAAQ,IAAI,KAAK,OAAO;AAC1B,iBAAA,QAAQ,IAAI,KAAK;AACtBA,0BAAA,MAAA,MAAA,OAAA,iCAAY,SAAS,KAAK,KAAK;AAAA,UAAA,OACzB;AACN,iBAAK,QAAQ;AAAA,UACd;AAGA,cAAI,IAAI,QAAQ,MAAM,QAAQ,IAAI,KAAK,cAAc,GAAG;AAEvD,iBAAK,gBAAgB,IAAI,KAAK,eAAe,IAAI,CAAO,QAAA;AAEjD,oBAAA,eAAe,EAAC,GAAG;AAGzB,kBAAI,aAAa,cAAc,CAAC,aAAa,cAAc;AAE7C,6BAAA,eAAe,YAAY,aAAa;AAAA,cACtD;AAGA,kBAAI,aAAa,WAAW;AAEvB,oBAAA,CAAC,MAAM,aAAa,SAAS,KAAK,QAAQ,KAAK,aAAa,SAAS,GAAG;AACvE,sBAAA;AAEH,0BAAM,OAAO,IAAI,KAAK,SAAS,aAAa,SAAS,CAAC;AACtD,wBAAI,CAAC,MAAM,KAAK,QAAS,CAAA,GAAG;AACd,mCAAA,YAAY,KAAK,eAAe,IAAI;AAAA,oBAClD;AAAA,2BACQ,GAAG;AACXA,kCAAA,MAAc,MAAA,SAAA,iCAAA,WAAW,CAAC;AAAA,kBAC3B;AAAA,gBACD;AAAA,cACD;AAEO,qBAAA;AAAA,YAAA,CACP;AAAA,UAAA,OACK;AACN,iBAAK,gBAAgB;UACtB;AAIA,cAAI,IAAI,QAAQ,IAAI,KAAK,YAAY;AAC/B,iBAAA,gBAAgB,IAAI,KAAK;AAAA,UAAA,OACxB;AACN,iBAAK,gBAAgB;AAAA,UACtB;AAAA,QAAA,OACM;AACN,eAAK,YAAY;AACjBA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,IAAI,WAAW;AAAA,YACtB,MAAM;AAAA,UAAA,CACN;AAAA,QACF;AAAA,MAAA,CACA,EACA,MAAM,CAAO,QAAA;AACbA,sBAAA,sDAAc,aAAa,GAAG;AAC9B,aAAK,YAAY;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AAAA,MAAA,CACD,EACA,QAAQ,MAAM;AACd,aAAK,UAAU;AACfA,sBAAA,MAAI,YAAY;AAAA,MAAA,CAChB;AAAA,IACH;AAAA;AAAA,IAGA,gBAAgB,MAAM;AACrBA,oBAAA,MAAA,MAAA,OAAA,iCAAYE,aAAAA,YAAY,WAAW,IAAI,CAAC;AACjC,aAAAA,aAAA,YAAY,WAAW,IAAI;AAAA,IACnC;AAAA;AAAA,IAGA,gBAAgB,MAAM;AAErB,UAAI,CAAC;AAAa,eAAA;AAEd,UAAA;AAEH,YAAI,QAAQ,KAAK,WAAW,OAAO,GAAG;AAC/B,gBAAA,WAAW,KAAK,UAAU,CAAC;AACjCF,wBAAA,oDAAY,qBAAqB,QAAQ;AAClC,iBAAA;AAAA,QACR;AAGI,YAAA,KAAK,WAAW,MAAM,GAAG;AAC5BA,wBAAA,MAAA,MAAA,OAAA,iCAAY,mBAAmB,IAAI;AAC5B,iBAAA;AAAA,QACR;AAGM,cAAA,UAAUE,aAAAA,YAAY,WAAW,IAAI;AAC3CF,sBAAA,MAAY,MAAA,OAAA,iCAAA,cAAc,OAAO;AAGjC,eAAO,UAAU,SAAY,oBAAA,QAAO,QAAQ;AAAA,eACpC,KAAK;AACbA,sBAAA,MAAA,MAAA,SAAA,iCAAc,cAAc,GAAG;AACxB,eAAA;AAAA,MACR;AAAA,IACD;AAAA;AAAA,IAGA,cAAc,QAAQ;AACrB,YAAM,YAAY;AAAA,QACjB,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,MAAA;AAED,aAAA,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGA,oBAAoB,QAAQ;AAC3B,YAAM,iBAAiB;AAAA,QACtB,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,WAAW;AAAA,QACX,aAAa;AAAA,QACb,YAAY;AAAA,MAAA;AAEN,aAAA,eAAe,MAAM,KAAK;AAAA,IAClC;AAAA;AAAA,IAEA,eAAe,MAAM;AACpB,UAAI,CAAC;AAAa,eAAA;AAGd,UAAA,OAAO,SAAS,UAAU;AACvB,cAAA,IAAI,IAAI,KAAK,IAAI;AACvB,YAAI,CAAC,MAAM,EAAE,QAAS,CAAA,GAAG;AACjB,iBAAA,GAAG,EAAE,YAAa,CAAA,IAAI,OAAO,EAAE,aAAa,CAAC,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,EAAE,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,EAAE,UAAU,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,EAAE,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,QACnM;AAAA,MACD;AAGA,UAAI,gBAAgB,MAAM;AAClB,eAAA,GAAG,KAAK,YAAa,CAAA,IAAI,OAAO,KAAK,aAAa,CAAC,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,KAAK,UAAU,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,MAClN;AAGI,UAAA,OAAO,SAAS,UAAU;AAEzB,YAAA;AACG,gBAAA,aAAa,IAAI,KAAK,IAAI;AAChC,cAAI,CAAC,MAAM,WAAW,QAAS,CAAA,GAAG;AAC1B,mBAAA,GAAG,WAAW,YAAa,CAAA,IAAI,OAAO,WAAW,aAAa,CAAC,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,WAAW,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,WAAW,UAAU,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,WAAW,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,UAChP;AAAA,iBACQ,GAAG;AACXA,wBAAA,MAAc,MAAA,SAAA,iCAAA,WAAW,CAAC;AAAA,QAC3B;AAGO,eAAA;AAAA,MACR;AAGA,aAAO,OAAO,IAAI;AAAA,IACnB;AAAA;AAAA,IAEA,mBAAmB,OAAO;AACzB,YAAM,WAAW;AAAA,QAChB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,MAAA;AAEA,aAAA,SAAS,KAAK,KAAK;AAAA,IAC3B;AAAA;AAAA,IAGA,uBAAuB,QAAQ;AAC9B,YAAM,YAAY;AAAA,QACjB,WAAW;AAAA,QACX,eAAe;AAAA,QACf,aAAa;AAAA,QACb,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,MAAA;AAED,aAAA,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGA,wBAAwB,QAAQ;AAC/B,YAAM,iBAAiB;AAAA,QACtB,WAAW;AAAA,QACX,eAAe;AAAA,QACf,aAAa;AAAA,QACb,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,MAAA;AAED,aAAA,eAAe,MAAM,KAAK;AAAA,IAClC;AAAA;AAAA,IAGA,aAAa,OAAO;AACnB,UAAI,KAAK,UAAU,KAAK,OAAO,SAAS,GAAG;AAEpC,cAAA,WAAW,KAAK,OAAO,IAAI,UAAQ,KAAK,gBAAgB,IAAI,CAAC;AACnEA,sBAAAA,MAAI,aAAa;AAAA,UAChB,MAAM;AAAA,UACN,SAAS,SAAS,KAAK;AAAA,QAAA,CACvB;AAAA,MACF;AAAA,IACD;AAAA;AAAA,IAGA,eAAe;AACd,YAAM,aAAaA,cAAA,MAAI,eAAe,QAAQ,KAAK;AACnD,YAAM,eAAe;AAErB,UAAI,CAAC,YAAY;AAChBA,sBAAA,MAAI,UAAU,EAAE,OAAO,aAAa,MAAM,QAAQ;AAClD;AAAA,MACD;AAGAA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,SAAS,CAAC,aAAa;AACtB,cAAI,SAAS,SAAS;AACrBA,0BAAAA,MAAI,YAAY;AAAA,cACf,OAAO;AAAA,YAAA,CACP;AAGD,kBAAM,cAAc;AAAA,cACnB,UAAU,KAAK;AAAA,cACf,aAAa;AAAA,cACb,cAAc;AAAA,cACd,cAAc,KAAK,UAAU;AAAA;AAAA,YAAA;AAI9BC,sBAAAA,SAAS,kBAAkB,WAAW,EACpC,KAAK,CAAU,WAAA;AACfD,4BAAA,MAAI,YAAY;AACZ,kBAAA,OAAO,SAAS,KAAK;AACxBA,8BAAAA,MAAI,UAAU;AAAA,kBACb,OAAO;AAAA,kBACP,MAAM;AAAA,kBACN,UAAU;AAAA;AAAA,gBAAA,CACV;AAED,2BAAW,MAAM;AAEhB,yBAAS,EAAA,aAAa,OAAO,EAAE,cAAc,CAAA;AACtC,2BAAE,WAAW,mBAAmB;AACvCA,gCAAA,MAAI,aAAa;AAAA,mBACf,IAAI;AAAA,cAAA,OACD;AACFA,oCAAA,UAAU,EAAE,OAAO,OAAO,WAAW,QAAQ,MAAM,QAAQ;AAAA,cAChE;AAAA,YAAA,CACA,EACA,MAAM,CAAO,QAAA;AACbA,4BAAA,MAAI,YAAY;AAChBA,4BAAA,MAAA,MAAA,SAAA,iCAAc,kBAAkB,GAAG;AACnCA,4BAAA,MAAI,UAAU,EAAE,OAAO,cAAc,MAAM,QAAQ;AAAA,YAAA,CACnD;AAAA,UACH;AAAA,QACD;AAAA;AAAA,MAAA,CACA;AAAA,IACF;AAAA;AAAA,IAGA,cAAc;AACb,YAAM,aAAaA,cAAA,MAAI,eAAe,QAAQ,KAAK;AACnD,YAAM,eAAe;AAErB,UAAI,CAAC,YAAY;AAChBA,sBAAA,MAAI,UAAU,EAAE,OAAO,aAAa,MAAM,QAAQ;AAClD;AAAA,MACD;AACAA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAEhBA,0BAAAA,MAAI,YAAY;AAAA,cACf,OAAO;AAAA,YAAA,CACP;AAGD,kBAAM,cAAc;AAAA,cACnB,UAAU,KAAK;AAAA,cACf,aAAa;AAAA,cACb,cAAc;AAAA,YAAA;AAIfC,sBAAAA,SAAS,kBAAkB,WAAW,EACpC,KAAK,CAAU,WAAA;AACfD,4BAAA,MAAI,YAAY;AACZ,kBAAA,OAAO,SAAS,KAAK;AACxBA,8BAAAA,MAAI,UAAU;AAAA,kBACb,OAAO;AAAA,kBACP,MAAM;AAAA,kBACN,UAAU;AAAA;AAAA,gBAAA,CACV;AAED,2BAAW,MAAM;AAEhB,yBAAS,EAAA,aAAa,OAAO,EAAE,cAAc,CAAA;AACtC,2BAAE,WAAW,mBAAmB;AACvCA,gCAAA,MAAI,aAAa;AAAA,mBACf,IAAI;AAAA,cAAA,OACD;AACFA,oCAAA,UAAU,EAAE,OAAO,OAAO,WAAW,QAAQ,MAAM,QAAQ;AAAA,cAChE;AAAA,YAAA,CACA,EACA,MAAM,CAAO,QAAA;AACbA,4BAAA,MAAI,YAAY;AAChBA,4BAAA,MAAA,MAAA,SAAA,iCAAc,kBAAkB,GAAG;AACnCA,4BAAA,MAAI,UAAU,EAAE,OAAO,cAAc,MAAM,QAAQ;AAAA,YAAA,CACnD;AAAA,UACH;AAAA,QACD;AAAA,MAAA,CACA;AAAA,IACF;AAAA;AAAA,IAGA,aAAa,GAAG;AACfA,oBAAA,MAAc,MAAA,SAAA,iCAAA,WAAW,EAAE,MAAM;AACjCA,oBAAA,MAAc,MAAA,SAAA,iCAAA,SAAS,KAAK,KAAK;AACjCA,0BAAA,MAAA,SAAA,iCAAc,WAAW,KAAK,gBAAgB,KAAK,KAAK,CAAC;AACzD,WAAK,iBAAiB;AAGtBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MAAA,CACV;AAAA,IACF;AAAA;AAAA,IAGA,qBAAqB;AACpB,UAAI,MAAM,KAAK,gBAAgB,KAAK,KAAK;AAGzC,UAAI,CAAC,KAAK;AACTA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACN;AACD;AAAA,MACD;AAEAA,oBAAA,MAAA,MAAA,OAAA,iCAAY,cAAc,GAAG;AAoB7BA,oBAAAA,MAAI,iBAAiB;AAAA,QACpB,MAAM;AAAA,QACN,SAAS,MAAM;AACdA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACN;AAAA,QACF;AAAA,MAAA,CACA;AAAA,IAEF;AAAA;AAAA,IAGA,oBAAoB,eAAe;AAElC,UAAI,CAAC;AAAsB,eAAA;AAE3B,UAAI,cAAc,SAAS,IAAI,KAAK,cAAc,SAAS,IAAI,GAAG;AAC1D,eAAA;AAAA,MAAA,WACG,cAAc,SAAS,IAAI,KAAK,cAAc,SAAS,IAAI,GAAG;AACjE,eAAA;AAAA,MAAA,WACG,cAAc,SAAS,IAAI,KAAK,cAAc,SAAS,IAAI,GAAG;AACjE,eAAA;AAAA,MAAA,WACG,cAAc,SAAS,IAAI,KAAK,cAAc,SAAS,IAAI,GAAG;AACjE,eAAA;AAAA,MAAA,WACG,cAAc,SAAS,IAAI,KAAK,cAAc,SAAS,IAAI,GAAG;AACjE,eAAA;AAAA,MAAA,WACG,cAAc,SAAS,IAAI,KAAK,cAAc,SAAS,IAAI,GAAG;AACjE,eAAA;AAAA,MAAA,OACD;AACC,eAAA;AAAA,MACR;AAAA,IACD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/oBD,GAAG,WAAW,eAAe;"}