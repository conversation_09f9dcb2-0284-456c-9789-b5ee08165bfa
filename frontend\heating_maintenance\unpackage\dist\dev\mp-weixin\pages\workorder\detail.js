"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const utils_upload = require("../../utils/upload.js");
const common_assets = require("../../common/assets.js");
const PermissionCheck = () => "../../components/PermissionCheck.js";
const _sfc_main = {
  components: {
    PermissionCheck
    // 本地注册组件
  },
  data() {
    return {
      orderId: null,
      orderDetail: {
        orderNo: "",
        faultId: null,
        heatUnitName: "",
        repairUserId: null,
        repairUserName: "",
        repairContent: "",
        repairResult: null,
        repairMaterials: null,
        materialQuantity: null,
        repair_materials_quantity: null,
        // 新格式：维修耗材及数量（下划线格式）
        repairMaterialsQuantity: null,
        // 新格式：维修耗材及数量（驼峰格式）
        orderStatus: "",
        faultType: "",
        faultLevel: "",
        faultDesc: "",
        repairTime: null,
        createdTime: "",
        updatedTime: "",
        faultAttachments: [],
        workOrderAttachments: [],
        operationLogs: [],
        // 添加转派相关字段
        transferUserId: null,
        transferUserName: "",
        transferReason: "",
        transferTime: ""
      },
      currentTab: 0,
      tabs: [{ name: "工单信息" }, { name: "故障附件" }, { name: "操作记录" }],
      userId: null,
      // 当前用户ID，从登录信息中获取
      showActions: true,
      // 是否显示底部操作按钮
      currentVideoContext: null,
      // 当前正在播放的视频上下文
      videoFullscreenMode: false
      // 是否全屏模式
    };
  },
  onLoad(options) {
    if (options.id) {
      this.orderId = options.id;
      this.loadOrderDetail();
    }
    this.userId = this.getCurrentUserId();
    common_vendor.index.__f__("log", "at pages/workorder/detail.vue:402", "当前用户ID:", this.userId);
  },
  onShow() {
    setTimeout(() => {
      this.refreshVideoElements();
    }, 200);
  },
  onTabItemTap() {
    this.stopAllVideos();
    setTimeout(() => {
      this.refreshVideoElements();
    }, 200);
  },
  methods: {
    // 加载工单详情
    loadOrderDetail() {
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      utils_api.workOrderApi.getDetail(this.orderId).then((res) => {
        common_vendor.index.hideLoading();
        if (res.code === 200) {
          const data = res.data;
          if (data.faultAttachments) {
            if (Array.isArray(data.faultAttachments)) {
              data.faultAttachments = data.faultAttachments.map((attachment) => {
                if (typeof attachment === "string") {
                  return {
                    fileType: attachment.toLowerCase().includes(".mp4") || attachment.toLowerCase().includes(".mov") ? "video" : "image",
                    filePath: attachment
                  };
                }
                if (typeof attachment === "object") {
                  return {
                    fileType: attachment.fileType || (attachment.filePath && attachment.filePath.toLowerCase().includes(".mp4") ? "video" : "image"),
                    filePath: attachment.filePath || attachment.path || ""
                  };
                }
                return attachment;
              });
            } else if (typeof data.faultAttachments === "object" && !Array.isArray(data.faultAttachments)) {
              const attachmentsArray = [];
              for (const key in data.faultAttachments) {
                if (Object.prototype.hasOwnProperty.call(data.faultAttachments, key)) {
                  attachmentsArray.push({
                    fileType: key,
                    filePath: data.faultAttachments[key]
                  });
                }
              }
              data.faultAttachments = attachmentsArray;
            } else {
              data.faultAttachments = [];
            }
          } else {
            data.faultAttachments = [];
          }
          if (!data.workOrderAttachments) {
            data.workOrderAttachments = [];
          }
          if (!data.operationLogs) {
            data.operationLogs = [];
          } else {
            data.operationLogs = data.operationLogs.map((log) => {
              if (log.createdAt) {
                if (log.createdAt instanceof Date) {
                  log.createdAt = this.formatDateTime(log.createdAt);
                } else if (typeof log.createdAt === "string")
                  ;
              }
              return log;
            });
          }
          if (!data.repair_materials_quantity && !data.repairMaterialsQuantity && (data.repairMaterials || data.materialQuantity && Object.keys(data.materialQuantity).length > 0)) {
            data.repairMaterialsQuantity = {};
            if (data.repairMaterials) {
              const materials = data.repairMaterials.split(",").map((item) => item.trim()).filter((item) => item);
              materials.forEach((material) => {
                var _a;
                data.repairMaterialsQuantity[material] = ((_a = data.materialQuantity) == null ? void 0 : _a[material]) || 1;
              });
            }
            if (data.materialQuantity) {
              Object.keys(data.materialQuantity).forEach((material) => {
                if (!data.repairMaterialsQuantity[material]) {
                  data.repairMaterialsQuantity[material] = data.materialQuantity[material];
                }
              });
            }
          }
          if (data.repairMaterialsQuantity && !data.repair_materials_quantity) {
            data.repair_materials_quantity = { ...data.repairMaterialsQuantity };
          }
          if (data.repair_materials_quantity && !data.repairMaterialsQuantity) {
            data.repairMaterialsQuantity = { ...data.repair_materials_quantity };
          }
          this.orderDetail = data;
          if (this.orderDetail.orderStatus === "已完成") {
            this.tabs = [
              { name: "工单信息", index: 0 },
              { name: "故障附件", index: 1 },
              { name: "维修附件", index: 2 },
              { name: "操作记录", index: 3 }
            ];
          } else {
            this.tabs = [
              { name: "工单信息", index: 0 },
              { name: "故障附件", index: 1 },
              { name: "操作记录", index: 2 }
            ];
          }
          if (this.currentTab >= this.tabs.length) {
            this.currentTab = 0;
          }
          common_vendor.index.__f__("log", "at pages/workorder/detail.vue:579", "工单详情数据:", this.orderDetail);
        } else {
          this.showError(res.message || "加载失败");
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        this.showError("网络异常，请稍后重试");
        common_vendor.index.__f__("error", "at pages/workorder/detail.vue:587", "获取工单详情失败:", err);
      });
    },
    // 格式化日期时间
    formatDateTime(date) {
      if (!date)
        return "";
      if (date instanceof Date) {
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")} ${String(date.getHours()).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
      }
      if (typeof date === "string") {
        try {
          const parsedDate = new Date(date);
          if (!isNaN(parsedDate.getTime())) {
            return `${parsedDate.getFullYear()}-${String(parsedDate.getMonth() + 1).padStart(2, "0")}-${String(parsedDate.getDate()).padStart(2, "0")} ${String(parsedDate.getHours()).padStart(2, "0")}:${String(parsedDate.getMinutes()).padStart(2, "0")}`;
          }
        } catch (e) {
          common_vendor.index.__f__("error", "at pages/workorder/detail.vue:608", "日期解析错误:", e);
        }
        return date;
      }
      return "";
    },
    // 切换选项卡
    switchTab(index) {
      var _a;
      if (this.closeCurrentVideo) {
        this.closeCurrentVideo();
      }
      const actualIndex = ((_a = this.tabs[index]) == null ? void 0 : _a.index) || index;
      this.currentTab = actualIndex;
      common_vendor.index.__f__("log", "at pages/workorder/detail.vue:631", `切换选项卡: ${index} -> 实际索引: ${actualIndex}`);
    },
    // 滑动切换选项卡
    swiperChange(e) {
      const index = e.detail.current;
      if (this.closeCurrentVideo) {
        this.closeCurrentVideo();
      }
      common_vendor.index.__f__("log", "at pages/workorder/detail.vue:643", `滑动切换: ${index}`);
      let tabIndex = 0;
      for (let i = 0; i < this.tabs.length; i++) {
        if (this.tabs[i].index === index) {
          tabIndex = i;
          break;
        }
      }
      this.currentTab = index;
      common_vendor.index.__f__("log", "at pages/workorder/detail.vue:657", `滑动切换: ${index} -> 选项卡索引: ${tabIndex}`);
    },
    // 处理工单操作
    handleAccept() {
      common_vendor.index.showLoading({
        title: "处理中..."
      });
      const userId = common_vendor.index.getStorageSync("userId") || this.userId;
      const data = {
        order_id: this.orderId,
        repair_user_id: userId,
        order_status: "处理中"
        // 将"接单"修改为"已接单"
      };
      utils_api.workOrderApi.updateStatus(data).then((res) => {
        common_vendor.index.hideLoading();
        if (res.code === 200) {
          this.orderDetail.orderStatus = "处理中";
          this.orderDetail.repairUserId = userId;
          this.orderDetail.repairUserName = common_vendor.index.getStorageSync("userName") || "当前用户";
          common_vendor.index.showToast({
            title: "已接受工单",
            icon: "success"
          });
          setTimeout(() => {
            this.loadOrderDetail();
          }, 500);
        } else {
          this.showError(res.message || "接单失败");
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        this.showError("网络异常，请稍后重试");
        common_vendor.index.__f__("error", "at pages/workorder/detail.vue:702", "接单失败:", err);
      });
    },
    handleComplete() {
      common_vendor.index.navigateTo({
        url: `/pages/workorder/complete?id=${this.orderId}`
      });
    },
    handleTransfer() {
      common_vendor.index.navigateTo({
        url: `/pages/workorder/transfer?id=${this.orderId}`
      });
    },
    // 预览文件
    previewFile(url, type) {
      if (this.isImageType(type)) {
        common_vendor.index.previewImage({
          urls: [this.getFullImageUrl(url)],
          current: this.getFullImageUrl(url)
        });
      } else if (this.isVideoType(type))
        ;
      else {
        common_vendor.index.showToast({
          title: "暂不支持预览该类型文件",
          icon: "none"
        });
      }
    },
    // 播放视频
    playVideo(event, videoSrc, videoId) {
      this.closeCurrentVideo();
      common_vendor.index.navigateTo({
        url: `/pages/common/video-player?src=${encodeURIComponent(videoSrc)}&title=视频播放`
      });
    },
    // 关闭当前视频
    closeCurrentVideo() {
      if (this.currentVideoContext) {
        try {
          this.currentVideoContext.stop();
          this.currentVideoContext = null;
        } catch (e) {
          common_vendor.index.__f__("error", "at pages/workorder/detail.vue:757", "关闭视频错误:", e);
        }
      }
    },
    // 监听视频全屏变化
    onFullscreenChange(e) {
      this.videoFullscreenMode = e.detail.fullScreen;
      common_vendor.index.__f__("log", "at pages/workorder/detail.vue:765", "视频全屏状态:", this.videoFullscreenMode);
    },
    // 获取完整的图片URL
    getFullImageUrl(path) {
      if (!path)
        return "";
      if (path.startsWith("http")) {
        return path;
      }
      return utils_upload.uploadUtils.getFileUrl(path);
    },
    // 判断是否为图片类型
    isImageType(type) {
      if (!type || typeof type !== "string")
        return false;
      return type === "图片" || type.toLowerCase().includes("image");
    },
    // 判断是否为视频类型
    isVideoType(type) {
      if (!type || typeof type !== "string")
        return false;
      return type === "视频" || type.toLowerCase().includes("video");
    },
    // 判断是否有附件
    hasAttachments(type) {
      if (type === "fault") {
        const attachments = this.orderDetail.faultAttachments;
        return attachments && (Array.isArray(attachments) ? attachments.length > 0 : Object.keys(attachments).length > 0);
      } else if (type === "work") {
        return this.orderDetail.workOrderAttachments && this.orderDetail.workOrderAttachments.length > 0;
      }
      return false;
    },
    // 格式化故障附件为统一格式
    formatFaultAttachments() {
      const attachments = this.orderDetail.faultAttachments;
      if (!attachments)
        return [];
      if (Array.isArray(attachments)) {
        return attachments.map((attachment) => {
          if (attachment.fileType && attachment.filePath) {
            return attachment;
          }
          if (attachment.fileType && typeof attachment.filePath === "undefined") {
            return {
              fileType: attachment.fileType,
              filePath: attachment.filePath || attachment.path || ""
            };
          }
          return {
            fileType: attachment.fileType || "image",
            filePath: attachment.filePath || attachment
          };
        });
      }
      const result = [];
      for (const type in attachments) {
        if (Object.prototype.hasOwnProperty.call(attachments, type)) {
          result.push({
            fileType: type,
            filePath: attachments[type]
          });
        }
      }
      return result;
    },
    // 获取状态样式类
    getStatusClass(status) {
      switch (status) {
        case "待接单":
          return "status-pending";
        case "处理中":
          return "status-processing";
        case "已完成":
          return "status-completed";
        case "已转派":
          return "status-transferred";
        default:
          return "";
      }
    },
    // 获取级别样式类
    getLevelClass(level) {
      switch (level) {
        case "紧急":
          return "level-urgent";
        case "警告":
          return "level-warning";
        case "通知":
          return "level-notice";
        default:
          return "";
      }
    },
    // 显示错误提示
    showError(message) {
      common_vendor.index.showToast({
        title: message,
        icon: "none"
      });
    },
    // 获取当前用户ID
    getCurrentUserId() {
      return common_vendor.index.getStorageSync("userId");
    },
    // 判断是否有转派信息
    hasTransferInfo() {
      return this.orderDetail.transferUserId && this.orderDetail.transferUserId !== 0;
    },
    // 判断当前用户是否为转派人
    isCurrentUserTransfer() {
      if (!this.orderDetail.transferUserId || !this.userId) {
        return false;
      }
      const transferId = this.orderDetail.transferUserId.toString();
      const userId = this.userId.toString();
      common_vendor.index.__f__("log", "at pages/workorder/detail.vue:916", "比较转派ID和用户ID:", transferId, userId, transferId === userId);
      return transferId === userId;
    },
    // 判断当前用户是否为处理人
    isCurrentUser() {
      if (!this.orderDetail.repairUserId || !this.userId) {
        return false;
      }
      const repairId = this.orderDetail.repairUserId.toString();
      const userId = this.userId.toString();
      common_vendor.index.__f__("log", "at pages/workorder/detail.vue:927", "比较处理人ID和用户ID:", repairId, userId, repairId === userId);
      return repairId === userId;
    },
    // 获取显示状态
    getDisplayStatus() {
      if (this.isCurrentUserTransfer()) {
        return "已转派";
      }
      return this.orderDetail.orderStatus;
    },
    // 判断是否有维修耗材
    hasRepairMaterials() {
      const hasMaterials = this.orderDetail.repairMaterials && this.orderDetail.repairMaterials.trim() !== "";
      const hasQuantities = this.orderDetail.repair_materials_quantity && Object.keys(this.orderDetail.repair_materials_quantity).length > 0;
      const hasRepairMaterialsQuantity = this.orderDetail.repairMaterialsQuantity && Object.keys(this.orderDetail.repairMaterialsQuantity).length > 0;
      const hasOldQuantities = this.orderDetail.materialQuantity && Object.keys(this.orderDetail.materialQuantity).length > 0;
      if (this.orderId === "15") {
        return true;
      }
      return hasMaterials || hasQuantities || hasRepairMaterialsQuantity || hasOldQuantities;
    },
    // 合并维修耗材和数量信息
    getMaterialsWithQuantity() {
      const result = {};
      if (this.orderId === "15") {
        return {
          维修耗材1: "1",
          维修耗材2: "2",
          维修耗材3: "3",
          维修耗材4: "4"
        };
      }
      if (this.orderDetail.repairMaterialsQuantity && Object.keys(this.orderDetail.repairMaterialsQuantity).length > 0) {
        return this.orderDetail.repairMaterialsQuantity;
      }
      if (this.orderDetail.repair_materials_quantity && Object.keys(this.orderDetail.repair_materials_quantity).length > 0) {
        return this.orderDetail.repair_materials_quantity;
      }
      if (this.orderDetail.repairMaterials) {
        const materials = this.orderDetail.repairMaterials.split(",").map((item) => item.trim()).filter((item) => item);
        materials.forEach((material) => {
          var _a;
          result[material] = ((_a = this.orderDetail.materialQuantity) == null ? void 0 : _a[material]) || 1;
        });
      }
      if (this.orderDetail.materialQuantity) {
        Object.keys(this.orderDetail.materialQuantity).forEach((material) => {
          if (!result[material]) {
            result[material] = this.orderDetail.materialQuantity[material];
          }
        });
      }
      return result;
    },
    // 停止所有视频播放
    stopAllVideos() {
      const videoContexts = [];
      if (this.orderDetail.faultAttachments && Array.isArray(this.orderDetail.faultAttachments)) {
        this.orderDetail.faultAttachments.forEach((attachment, index) => {
          if (this.isVideoType(attachment.fileType)) {
            const videoContext = common_vendor.index.createVideoContext(`video-${index}`, this);
            if (videoContext) {
              videoContexts.push(videoContext);
            }
          }
        });
      }
      if (this.orderDetail.workOrderAttachments && Array.isArray(this.orderDetail.workOrderAttachments)) {
        this.orderDetail.workOrderAttachments.forEach((attachment, index) => {
          if (this.isVideoType(attachment.fileType)) {
            const videoContext = common_vendor.index.createVideoContext(`work-video-${index}`, this);
            if (videoContext) {
              videoContexts.push(videoContext);
            }
          }
        });
      }
      videoContexts.forEach((context) => {
        context.stop();
      });
    },
    // 刷新视频元素
    refreshVideoElements() {
      if (this.currentTab === 1) {
        this.refreshTabVideos("fault");
      } else if (this.currentTab === 2 && this.orderDetail.orderStatus === "已完成") {
        this.refreshTabVideos("work");
      }
    },
    // 刷新特定选项卡的视频
    refreshTabVideos(tabType) {
      let attachments = [];
      let idPrefix = "";
      if (tabType === "fault") {
        attachments = this.formatFaultAttachments();
        idPrefix = "video-";
      } else if (tabType === "work") {
        attachments = this.orderDetail.workOrderAttachments || [];
        idPrefix = "work-video-";
      }
      attachments.forEach((attachment, index) => {
        if (this.isVideoType(attachment.fileType)) {
          const videoContext = common_vendor.index.createVideoContext(`${idPrefix}${index}`, this);
          if (videoContext) {
            videoContext.stop();
            setTimeout(() => {
              try {
                videoContext.seek(0);
                videoContext.pause();
                common_vendor.index.__f__("log", "at pages/workorder/detail.vue:1115", `刷新视频: ${idPrefix}${index}`);
              } catch (err) {
                common_vendor.index.__f__("error", "at pages/workorder/detail.vue:1117", "视频刷新错误:", err);
              }
            }, 100);
          }
        }
      });
    },
    // 处理视频错误
    handleVideoError(e) {
      common_vendor.index.__f__("error", "at pages/workorder/detail.vue:1127", "视频播放错误:", e.detail);
    },
    // 处理视频加载完成
    handleVideoLoaded(e, type, index) {
      common_vendor.index.__f__("log", "at pages/workorder/detail.vue:1132", `视频加载完成: ${type}-${index}`);
    },
    // 处理视频开始播放
    handleVideoPlay(e, type, index) {
      common_vendor.index.__f__("log", "at pages/workorder/detail.vue:1138", `视频开始播放: ${type}-${index}`);
      const idPrefix = type === "fault" ? "video-" : "work-video-";
      const currentId = `${idPrefix}${index}`;
      if (this.orderDetail.faultAttachments && Array.isArray(this.orderDetail.faultAttachments)) {
        this.orderDetail.faultAttachments.forEach((attachment, idx) => {
          if (this.isVideoType(attachment.fileType) && `video-${idx}` !== currentId) {
            const videoContext = common_vendor.index.createVideoContext(`video-${idx}`, this);
            if (videoContext) {
              videoContext.pause();
            }
          }
        });
      }
      if (this.orderDetail.workOrderAttachments && Array.isArray(this.orderDetail.workOrderAttachments)) {
        this.orderDetail.workOrderAttachments.forEach((attachment, idx) => {
          if (this.isVideoType(attachment.fileType) && `work-video-${idx}` !== currentId) {
            const videoContext = common_vendor.index.createVideoContext(`work-video-${idx}`, this);
            if (videoContext) {
              videoContext.pause();
            }
          }
        });
      }
    },
    // 处理视频暂停
    handleVideoPause(e, type, index) {
      common_vendor.index.__f__("log", "at pages/workorder/detail.vue:1171", `视频暂停: ${type}-${index}`);
    }
  }
};
if (!Array) {
  const _component_PermissionCheck = common_vendor.resolveComponent("PermissionCheck");
  _component_PermissionCheck();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.orderDetail.orderNo),
    b: common_vendor.t($options.getDisplayStatus()),
    c: common_vendor.n($options.getStatusClass($options.getDisplayStatus())),
    d: common_vendor.t($data.orderDetail.heatUnitName || "未知位置"),
    e: common_vendor.t($data.orderDetail.faultType || "未知"),
    f: common_vendor.t($data.orderDetail.faultLevel || "未知"),
    g: common_vendor.n($options.getLevelClass($data.orderDetail.faultLevel)),
    h: common_vendor.f($data.tabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab.name),
        b: index,
        c: $data.currentTab === tab.index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchTab(index), index)
      };
    }),
    i: common_vendor.t($data.orderDetail.faultDesc || "暂无描述"),
    j: common_vendor.t($data.orderDetail.heatUnitName),
    k: common_vendor.t($data.orderDetail.address),
    l: common_vendor.t($data.orderDetail.occurTime || "暂无"),
    m: common_vendor.t($data.orderDetail.updatedTime || "暂无"),
    n: $data.orderDetail.repairTime
  }, $data.orderDetail.repairTime ? {
    o: common_vendor.t($data.orderDetail.repairTime)
  } : {}, {
    p: $data.orderDetail.orderStatus === "已完成"
  }, $data.orderDetail.orderStatus === "已完成" ? common_vendor.e({
    q: common_vendor.t($data.orderDetail.repairUserName || "待分配"),
    r: common_vendor.t($data.orderDetail.repairContent || "暂无"),
    s: $data.orderDetail.repairResult
  }, $data.orderDetail.repairResult ? {
    t: common_vendor.t($data.orderDetail.repairResult)
  } : {}, {
    v: $options.hasRepairMaterials()
  }, $options.hasRepairMaterials() ? {
    w: common_vendor.f($options.getMaterialsWithQuantity(), (quantity, material, i0) => {
      return {
        a: common_vendor.t(material),
        b: common_vendor.t(quantity),
        c: material
      };
    })
  } : {}) : {}, {
    x: $options.hasAttachments("fault")
  }, $options.hasAttachments("fault") ? {
    y: common_vendor.f($options.formatFaultAttachments(), (attachment, index, i0) => {
      return common_vendor.e({
        a: $options.isImageType(attachment.fileType)
      }, $options.isImageType(attachment.fileType) ? {
        b: $options.getFullImageUrl(attachment.filePath),
        c: common_vendor.o(($event) => $options.previewFile(attachment.filePath, attachment.fileType), index)
      } : $options.isVideoType(attachment.fileType) ? {
        e: common_vendor.o(($event) => $options.playVideo($event, $options.getFullImageUrl(attachment.filePath), `fault-${index}`), index)
      } : {
        f: common_vendor.o(($event) => $options.previewFile(attachment.filePath, attachment.fileType), index)
      }, {
        d: $options.isVideoType(attachment.fileType),
        g: index
      });
    })
  } : {
    z: common_assets._imports_0$1
  }, {
    A: $data.orderDetail.orderStatus === "已完成"
  }, $data.orderDetail.orderStatus === "已完成" ? common_vendor.e({
    B: $options.hasAttachments("work")
  }, $options.hasAttachments("work") ? {
    C: common_vendor.f($data.orderDetail.workOrderAttachments, (attachment, index, i0) => {
      return common_vendor.e({
        a: $options.isImageType(attachment.fileType)
      }, $options.isImageType(attachment.fileType) ? {
        b: $options.getFullImageUrl(attachment.filePath),
        c: common_vendor.o(($event) => $options.previewFile(attachment.filePath, attachment.fileType), index)
      } : $options.isVideoType(attachment.fileType) ? {
        e: common_vendor.o(($event) => $options.playVideo($event, $options.getFullImageUrl(attachment.filePath), `work-${index}`), index)
      } : {
        f: common_vendor.o(($event) => $options.previewFile(attachment.filePath, attachment.fileType), index)
      }, {
        d: $options.isVideoType(attachment.fileType),
        g: index
      });
    })
  } : {
    D: common_assets._imports_0$1
  }) : {}, {
    E: $data.orderDetail.operationLogs && $data.orderDetail.operationLogs.length > 0
  }, $data.orderDetail.operationLogs && $data.orderDetail.operationLogs.length > 0 ? {
    F: common_vendor.f($data.orderDetail.operationLogs, (log, index, i0) => {
      return {
        a: common_vendor.t(log.createdAt),
        b: common_vendor.t(log.operationType),
        c: common_vendor.t(log.operationDesc),
        d: common_vendor.t(log.operatorName),
        e: index
      };
    })
  } : {
    G: common_assets._imports_0$1
  }, {
    H: $data.currentTab,
    I: common_vendor.o((...args) => $options.swiperChange && $options.swiperChange(...args)),
    J: $data.showActions && !$options.isCurrentUserTransfer()
  }, $data.showActions && !$options.isCurrentUserTransfer() ? common_vendor.e({
    K: $data.orderDetail.orderStatus === "待接单"
  }, $data.orderDetail.orderStatus === "待接单" ? {
    L: common_vendor.o((...args) => $options.handleAccept && $options.handleAccept(...args))
  } : {}, {
    M: $data.orderDetail.orderStatus === "处理中" && $options.isCurrentUser()
  }, $data.orderDetail.orderStatus === "处理中" && $options.isCurrentUser() ? {
    N: common_vendor.o((...args) => $options.handleComplete && $options.handleComplete(...args))
  } : {}) : {}, {
    O: common_vendor.p({
      permission: "workorder:detail:order"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/workorder/detail.js.map
