"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      dateRange: "thisMonth",
      customDateRange: {
        start: "",
        end: ""
      },
      dateOptions: [
        { label: "本月", value: "thisMonth" },
        { label: "上月", value: "lastMonth" },
        { label: "近7天", value: "last7Days" },
        { label: "近30天", value: "last30Days" }
      ],
      selectedStaff: null,
      staffSearchKeyword: "",
      staffList: [],
      selectedStatus: null,
      statusTypes: {
        normal: { label: "正常", color: "#4cd964" },
        late: { label: "迟到", color: "#f0ad4e" },
        early: { label: "早退", color: "#5bc0de" },
        absent: { label: "缺勤", color: "#dd524d" }
      },
      searchKeyword: "",
      currentDatePickerType: "start",
      page: 1,
      pageSize: 20,
      hasMoreData: true,
      totalRecords: 0,
      statistics: {
        normalCount: 0,
        lateCount: 0,
        earlyCount: 0,
        absentCount: 0
      },
      records: [],
      isLoading: false
    };
  },
  computed: {
    displayDateRange() {
      switch (this.dateRange) {
        case "thisMonth":
          return "本月";
        case "lastMonth":
          return "上月";
        case "last7Days":
          return "近7天";
        case "last30Days":
          return "近30天";
        case "custom":
          return `${this.customDateRange.start} 至 ${this.customDateRange.end}`;
        default:
          return "选择日期";
      }
    },
    filteredStaffList() {
      if (!this.staffSearchKeyword)
        return this.staffList;
      return this.staffList.filter(
        (staff) => staff.name.includes(this.staffSearchKeyword) || staff.department && staff.department.includes(this.staffSearchKeyword)
      );
    },
    groupedRecords() {
      const groups = {};
      this.records.forEach((record) => {
        if (!groups[record.date]) {
          groups[record.date] = {
            date: record.date,
            week: record.week,
            records: []
          };
        }
        groups[record.date].records.push(record);
      });
      return Object.values(groups);
    },
    isCustomDateRangeValid() {
      return this.customDateRange.start && this.customDateRange.end;
    }
  },
  onLoad() {
    this.loadStaffList();
    this.loadRecords();
  },
  methods: {
    // 加载员工列表
    loadStaffList() {
      utils_api.attendanceApi.getAllStaff().then((res) => {
        common_vendor.index.__f__("log", "at pages/attendance/all-records.vue:305", "获取员工列表结果:", res);
        if (res.code === 200 && res.data && res.data.length > 0) {
          this.staffList = res.data;
        } else {
          this.loadStaffListFallback();
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/attendance/all-records.vue:313", "获取员工列表失败:", err);
        this.loadStaffListFallback();
      });
    },
    // 降级方案：使用用户API获取员工列表
    loadStaffListFallback() {
      utils_api.userApi.getInspectorList().then((userRes) => {
        common_vendor.index.__f__("log", "at pages/attendance/all-records.vue:322", "降级获取员工列表结果:", userRes);
        if (userRes.code === 200 && userRes.data) {
          this.staffList = userRes.data.map((user) => ({
            id: user.id,
            name: user.name,
            department: user.department || "未知部门"
          }));
        } else {
          this.staffList = [
            { id: "001", name: "张工", department: "运维部" },
            { id: "002", name: "李工", department: "工程部" },
            { id: "003", name: "王工", department: "管理部" },
            { id: "004", name: "赵工", department: "技术部" },
            { id: "005", name: "刘工", department: "客服部" }
          ];
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/attendance/all-records.vue:341", "降级获取员工列表失败:", err);
        this.staffList = [
          { id: "001", name: "张工", department: "运维部" },
          { id: "002", name: "李工", department: "工程部" },
          { id: "003", name: "王工", department: "管理部" },
          { id: "004", name: "赵工", department: "技术部" },
          { id: "005", name: "刘工", department: "客服部" }
        ];
      });
    },
    // 获取日期范围参数
    getDateRangeParams() {
      const now = /* @__PURE__ */ new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      const day = now.getDate();
      let startDate = "";
      let endDate = "";
      switch (this.dateRange) {
        case "thisMonth":
          startDate = `${year}-${String(month).padStart(2, "0")}-01`;
          endDate = this.getLastDayOfMonth(year, month);
          break;
        case "lastMonth":
          const lastMonth = month === 1 ? 12 : month - 1;
          const lastMonthYear = month === 1 ? year - 1 : year;
          startDate = `${lastMonthYear}-${String(lastMonth).padStart(2, "0")}-01`;
          endDate = this.getLastDayOfMonth(lastMonthYear, lastMonth);
          break;
        case "last7Days":
          const last7Date = new Date(now);
          last7Date.setDate(day - 6);
          startDate = this.formatDate(last7Date);
          endDate = this.formatDate(now);
          break;
        case "last30Days":
          const last30Date = new Date(now);
          last30Date.setDate(day - 29);
          startDate = this.formatDate(last30Date);
          endDate = this.formatDate(now);
          break;
        case "custom":
          startDate = this.customDateRange.start;
          endDate = this.customDateRange.end;
          break;
      }
      return { startDate, endDate };
    },
    // 获取月份的最后一天
    getLastDayOfMonth(year, month) {
      const lastDay = new Date(year, month, 0).getDate();
      return `${year}-${String(month).padStart(2, "0")}-${String(lastDay).padStart(2, "0")}`;
    },
    // 格式化日期为YYYY-MM-DD
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    // 加载考勤记录
    loadRecords() {
      if (this.isLoading)
        return;
      this.isLoading = true;
      common_vendor.index.showLoading({
        title: "加载数据中..."
      });
      const { startDate, endDate } = this.getDateRangeParams();
      const params = {
        page: this.page,
        pageSize: this.pageSize,
        startDate,
        endDate
      };
      common_vendor.index.__f__("log", "at pages/attendance/all-records.vue:428", "查询参数:", params);
      if (this.selectedStaff) {
        params.userId = this.selectedStaff.id;
      }
      if (this.selectedStatus) {
        params.status = this.selectedStatus;
      }
      if (this.searchKeyword) {
        params.keyword = this.searchKeyword;
      }
      utils_api.attendanceApi.getRecords(params).then((res) => {
        if (res.code === 200 && res.data) {
          const summary = res.data.summary || {};
          this.statistics = {
            normalCount: summary.workdays ? summary.workdays - summary.late - summary.absent - summary.early : 0,
            lateCount: summary.late || 0,
            earlyCount: summary.early || 0,
            absentCount: summary.absent || 0
          };
          this.totalRecords = res.data.records ? res.data.records.length : 0;
          const formattedRecords = this.processAttendanceRecords(res.data.records || []);
          if (this.page === 1) {
            this.records = formattedRecords;
          } else {
            this.records = [...this.records, ...formattedRecords];
          }
          this.hasMoreData = formattedRecords.length === this.pageSize;
        } else {
          common_vendor.index.showToast({
            title: res.message || "获取考勤记录失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/attendance/all-records.vue:478", "获取考勤记录失败:", err);
        common_vendor.index.showToast({
          title: "获取考勤记录失败",
          icon: "none"
        });
      }).finally(() => {
        common_vendor.index.hideLoading();
        this.isLoading = false;
      });
    },
    // 处理考勤记录数据
    processAttendanceRecords(records) {
      if (!Array.isArray(records) || records.length === 0) {
        return [];
      }
      const firstRecord = records[0];
      if (firstRecord.clockInTime !== void 0 || firstRecord.clockOutTime !== void 0) {
        return records.map((item) => {
          const user = item.user || {};
          return {
            id: item.id,
            date: item.date || this.formatDateFromString(item.clockTime || item.createTime),
            week: item.week || this.formatWeekFromString(item.clockTime || item.createTime),
            staffName: user.name || "未知",
            department: user.department || "未知部门",
            avatar: user.avatar || "/static/images/avatar.jpg",
            clockInTime: item.clockInTime || "未打卡",
            clockInStatus: item.clockInStatus || "normal",
            clockOutTime: item.clockOutTime || "未打卡",
            clockOutStatus: item.clockOutStatus || "normal",
            location: item.location || this.formatLocation(item)
          };
        });
      }
      const recordsByDateAndUser = {};
      records.forEach((record) => {
        if (!record.clockTime) {
          common_vendor.index.__f__("warn", "at pages/attendance/all-records.vue:526", "记录缺少clockTime字段:", record);
          return;
        }
        const date = new Date(record.clockTime);
        const dateStr = date.toISOString().split("T")[0];
        const userId = record.userId || (record.user ? record.user.id : "unknown");
        const key = `${dateStr}-${userId}`;
        if (!recordsByDateAndUser[key]) {
          const user = record.user || {};
          recordsByDateAndUser[key] = {
            id: record.id,
            date: this.formatDateFromString(record.clockTime),
            week: this.formatWeekFromString(record.clockTime),
            staffName: user.name || record.userName || "未知",
            department: user.department || record.department || "未知部门",
            avatar: user.avatar || record.avatar || "/static/images/avatar.jpg",
            clockInTime: "未打卡",
            clockOutTime: "未打卡",
            clockInStatus: "normal",
            clockOutStatus: "normal",
            userId,
            location: record.location || (record.latitude && record.longitude ? `${record.latitude.toFixed(6)}, ${record.longitude.toFixed(6)}` : "未知位置")
          };
        }
        if (record.clockType === "checkin") {
          recordsByDateAndUser[key].clockInTime = this.formatTime(record.clockTime);
          recordsByDateAndUser[key].clockInStatus = record.status || "normal";
        } else if (record.clockType === "checkout") {
          recordsByDateAndUser[key].clockOutTime = this.formatTime(record.clockTime);
          recordsByDateAndUser[key].clockOutStatus = record.status || "normal";
        }
      });
      return Object.values(recordsByDateAndUser);
    },
    // 格式化时间
    formatTime(timeString) {
      if (!timeString)
        return "未打卡";
      try {
        const date = new Date(timeString);
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        return `${hours}:${minutes}`;
      } catch (e) {
        return timeString;
      }
    },
    // 加载更多记录
    loadMoreRecords() {
      if (!this.hasMoreData || this.isLoading)
        return;
      this.page++;
      this.loadRecords();
    },
    // 搜索相关
    clearSearch() {
      this.searchKeyword = "";
      this.page = 1;
      this.loadRecords();
    },
    // 日期选择相关
    openDatePicker() {
      this.$refs.datePopup.open();
    },
    closeDatePicker() {
      this.$refs.datePopup.close();
    },
    selectDateRange(range) {
      this.dateRange = range;
      this.closeDatePicker();
      this.page = 1;
      this.loadRecords();
    },
    onStartDateChange(e) {
      this.customDateRange.start = e.detail.value;
    },
    onEndDateChange(e) {
      this.customDateRange.end = e.detail.value;
    },
    applyCustomDateRange() {
      this.dateRange = "custom";
      this.closeDatePicker();
      this.page = 1;
      this.loadRecords();
    },
    // 人员选择相关
    openStaffSelector() {
      this.$refs.staffPopup.open();
    },
    closeStaffSelector() {
      this.$refs.staffPopup.close();
    },
    selectStaff(staff) {
      this.selectedStaff = staff;
      this.closeStaffSelector();
      this.page = 1;
      this.loadRecords();
    },
    // 状态选择相关
    openStatusSelector() {
      this.$refs.statusPopup.open();
    },
    closeStatusSelector() {
      this.$refs.statusPopup.close();
    },
    selectStatus(status) {
      this.selectedStatus = status;
      this.closeStatusSelector();
      this.page = 1;
      this.loadRecords();
    },
    // 从字符串格式化日期为YYYY-MM-DD
    formatDateFromString(dateString) {
      if (!dateString)
        return "";
      try {
        const date = new Date(dateString);
        return this.formatDate(date);
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/attendance/all-records.vue:669", "日期格式化错误:", e);
        return "";
      }
    },
    // 从字符串格式化星期
    formatWeekFromString(dateString) {
      if (!dateString)
        return "";
      try {
        const date = new Date(dateString);
        const weekDays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
        return weekDays[date.getDay()];
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/attendance/all-records.vue:682", "星期格式化错误:", e);
        return "";
      }
    },
    // 格式化位置信息
    formatLocation(record) {
      if (!record)
        return "未知位置";
      if (record.location)
        return record.location;
      if (record.latitude && record.longitude) {
        return `${record.latitude.toFixed(6)}, ${record.longitude.toFixed(6)}`;
      }
      return "未知位置";
    }
  }
};
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_easycom_uni_icons2 + _easycom_uni_popup2)();
}
const _easycom_uni_icons = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-icons/uni-icons.js";
const _easycom_uni_popup = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-popup/uni-popup.js";
if (!Math) {
  (_easycom_uni_icons + _easycom_uni_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($options.displayDateRange),
    b: common_vendor.p({
      type: "bottom",
      size: "14",
      color: "#666"
    }),
    c: common_vendor.o((...args) => $options.openDatePicker && $options.openDatePicker(...args)),
    d: common_vendor.t($data.selectedStaff ? $data.selectedStaff.name : "全部人员"),
    e: common_vendor.p({
      type: "bottom",
      size: "14",
      color: "#666"
    }),
    f: common_vendor.o((...args) => $options.openStaffSelector && $options.openStaffSelector(...args)),
    g: common_vendor.t($data.selectedStatus ? $data.statusTypes[$data.selectedStatus].label : "全部状态"),
    h: common_vendor.p({
      type: "bottom",
      size: "14",
      color: "#666"
    }),
    i: common_vendor.o((...args) => $options.openStatusSelector && $options.openStatusSelector(...args)),
    j: common_vendor.p({
      type: "search",
      size: "16",
      color: "#999"
    }),
    k: $data.searchKeyword,
    l: common_vendor.o(($event) => $data.searchKeyword = $event.detail.value),
    m: $data.searchKeyword
  }, $data.searchKeyword ? {
    n: common_vendor.o((...args) => $options.clearSearch && $options.clearSearch(...args))
  } : {}, {
    o: common_vendor.t($data.totalRecords),
    p: $data.records.length > 0
  }, $data.records.length > 0 ? common_vendor.e({
    q: common_vendor.f($options.groupedRecords, (group, index, i0) => {
      return {
        a: common_vendor.t(group.date),
        b: common_vendor.t(group.week),
        c: common_vendor.f(group.records, (record, recordIndex, i1) => {
          return common_vendor.e({
            a: record.avatar,
            b: common_vendor.t(record.staffName),
            c: common_vendor.t(record.department),
            d: common_vendor.t(record.clockInTime),
            e: record.clockInStatus !== "normal" ? 1 : "",
            f: record.clockInStatus === "late"
          }, record.clockInStatus === "late" ? {} : {}, {
            g: common_vendor.t(record.clockOutTime),
            h: record.clockOutStatus !== "normal" ? 1 : "",
            i: record.clockOutStatus === "early"
          }, record.clockOutStatus === "early" ? {} : {}, {
            j: common_vendor.t(record.location),
            k: recordIndex
          });
        }),
        d: index
      };
    }),
    r: $data.hasMoreData
  }, $data.hasMoreData ? {
    s: common_vendor.o((...args) => $options.loadMoreRecords && $options.loadMoreRecords(...args))
  } : {}) : {
    t: common_assets._imports_0$1
  }, {
    v: common_vendor.o((...args) => $options.closeDatePicker && $options.closeDatePicker(...args)),
    w: common_vendor.f($data.dateOptions, (option, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(option.label),
        b: $data.dateRange === option.value
      }, $data.dateRange === option.value ? {
        c: "1b919886-5-" + i0 + ",1b919886-4",
        d: common_vendor.p({
          type: "checkmarkempty",
          size: "18",
          color: "#007AFF"
        })
      } : {}, {
        e: index,
        f: $data.dateRange === option.value ? 1 : "",
        g: common_vendor.o(($event) => $options.selectDateRange(option.value), index)
      });
    }),
    x: common_vendor.t($data.customDateRange.start ? $data.customDateRange.start : "开始日期"),
    y: $data.customDateRange.start,
    z: common_vendor.o((...args) => $options.onStartDateChange && $options.onStartDateChange(...args)),
    A: common_vendor.t($data.customDateRange.end ? $data.customDateRange.end : "结束日期"),
    B: $data.customDateRange.end,
    C: common_vendor.o((...args) => $options.onEndDateChange && $options.onEndDateChange(...args)),
    D: common_vendor.o((...args) => $options.applyCustomDateRange && $options.applyCustomDateRange(...args)),
    E: !$options.isCustomDateRangeValid,
    F: common_vendor.sr("datePopup", "1b919886-4"),
    G: common_vendor.p({
      type: "bottom"
    }),
    H: common_vendor.o((...args) => $options.closeStaffSelector && $options.closeStaffSelector(...args)),
    I: common_vendor.p({
      type: "search",
      size: "16",
      color: "#999"
    }),
    J: $data.staffSearchKeyword,
    K: common_vendor.o(($event) => $data.staffSearchKeyword = $event.detail.value),
    L: !$data.selectedStaff
  }, !$data.selectedStaff ? {
    M: common_vendor.p({
      type: "checkmarkempty",
      size: "18",
      color: "#007AFF"
    })
  } : {}, {
    N: common_vendor.o(($event) => $options.selectStaff(null)),
    O: common_vendor.f($options.filteredStaffList, (staff, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(staff.name),
        b: $data.selectedStaff && $data.selectedStaff.id === staff.id
      }, $data.selectedStaff && $data.selectedStaff.id === staff.id ? {
        c: "1b919886-9-" + i0 + ",1b919886-6",
        d: common_vendor.p({
          type: "checkmarkempty",
          size: "18",
          color: "#007AFF"
        })
      } : {}, {
        e: index,
        f: common_vendor.o(($event) => $options.selectStaff(staff), index)
      });
    }),
    P: common_vendor.sr("staffPopup", "1b919886-6"),
    Q: common_vendor.p({
      type: "bottom"
    }),
    R: common_vendor.o((...args) => $options.closeStatusSelector && $options.closeStatusSelector(...args)),
    S: !$data.selectedStatus
  }, !$data.selectedStatus ? {
    T: common_vendor.p({
      type: "checkmarkempty",
      size: "18",
      color: "#007AFF"
    })
  } : {}, {
    U: common_vendor.o(($event) => $options.selectStatus(null)),
    V: common_vendor.f($data.statusTypes, (status, key, i0) => {
      return common_vendor.e({
        a: common_vendor.t(status.label),
        b: $data.selectedStatus === key
      }, $data.selectedStatus === key ? {
        c: "1b919886-12-" + i0 + ",1b919886-10",
        d: common_vendor.p({
          type: "checkmarkempty",
          size: "18",
          color: "#007AFF"
        })
      } : {}, {
        e: key,
        f: common_vendor.o(($event) => $options.selectStatus(key), key)
      });
    }),
    W: common_vendor.sr("statusPopup", "1b919886-10"),
    X: common_vendor.p({
      type: "bottom"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/attendance/all-records.js.map
