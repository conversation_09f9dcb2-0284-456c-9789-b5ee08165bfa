/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-calendar-item__weeks-box.data-v-f9a24ebd {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.uni-calendar-item__weeks-box-text.data-v-f9a24ebd {
  font-size: 14px;
  color: #333;
}
.uni-calendar-item__weeks-lunar-text.data-v-f9a24ebd {
  font-size: 12px;
  color: #333;
}
.uni-calendar-item__weeks-box-item.data-v-f9a24ebd {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100rpx;
  height: 100rpx;
}
.uni-calendar-item__weeks-box-circle.data-v-f9a24ebd {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 8px;
  height: 8px;
  border-radius: 8px;
  background-color: #e43d33;
}
.uni-calendar-item--disable.data-v-f9a24ebd {
  background-color: rgba(249, 249, 249, 0.3);
  color: #c0c0c0;
}
.uni-calendar-item--isDay-text.data-v-f9a24ebd {
  color: #2979ff;
}
.uni-calendar-item--isDay.data-v-f9a24ebd {
  background-color: #2979ff;
  opacity: 0.8;
  color: #fff;
}
.uni-calendar-item--extra.data-v-f9a24ebd {
  color: #e43d33;
  opacity: 0.8;
}
.uni-calendar-item--checked.data-v-f9a24ebd {
  background-color: #2979ff;
  color: #fff;
  opacity: 0.8;
}
.uni-calendar-item--multiple.data-v-f9a24ebd {
  background-color: #2979ff;
  color: #fff;
  opacity: 0.8;
}
.uni-calendar-item--before-checked.data-v-f9a24ebd {
  background-color: #ff5a5f;
  color: #fff;
}
.uni-calendar-item--after-checked.data-v-f9a24ebd {
  background-color: #ff5a5f;
  color: #fff;
}