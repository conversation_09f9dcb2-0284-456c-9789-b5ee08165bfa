"use strict";
const common_vendor = require("../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      // 标题
      title: "多选",
      // 数据列表
      items: [],
      // 已选择的项
      selectedIds: [],
      // 名称字段
      nameField: "name",
      // ID字段
      idField: "id",
      // 搜索关键词
      searchKeyword: "",
      // 列表高度
      listHeight: "70vh"
    };
  },
  computed: {
    // 过滤后的列表
    filteredItems() {
      if (!this.searchKeyword) {
        return this.items;
      }
      const keyword = this.searchKeyword.toLowerCase();
      return this.items.filter((item) => {
        const name = item[this.nameField].toLowerCase();
        return name.includes(keyword);
      });
    },
    // 已选择的项目
    selectedItems() {
      return this.items.filter((item) => this.selectedIds.includes(item[this.idField]));
    }
  },
  onLoad() {
    const eventChannel = this.getOpenerEventChannel();
    if (eventChannel) {
      eventChannel.on("initData", (data) => {
        if (data) {
          this.title = data.title || "多选";
          this.items = data.items || [];
          this.selectedIds = data.selectedIds || [];
          this.nameField = data.nameField || "name";
          this.idField = data.idField || "id";
        }
      });
    }
    const systemInfo = common_vendor.index.getSystemInfoSync();
    const windowHeight = systemInfo.windowHeight;
    const headerHeight = 100;
    const selectedAreaHeight = 160;
    const headerPx = headerHeight / 750 * systemInfo.windowWidth;
    const selectedPx = selectedAreaHeight / 750 * systemInfo.windowWidth;
    this.listHeight = `${windowHeight - headerPx - selectedPx}px`;
  },
  methods: {
    // 检查项目是否已选择
    isItemSelected(item) {
      return this.selectedIds.includes(item[this.idField]);
    },
    // 切换项目选择状态
    toggleItem(item) {
      const id = item[this.idField];
      const index = this.selectedIds.indexOf(id);
      if (index > -1) {
        this.selectedIds.splice(index, 1);
      } else {
        this.selectedIds.push(id);
      }
    },
    // 处理搜索
    handleSearch() {
    },
    // 清除搜索
    clearSearch() {
      this.searchKeyword = "";
    },
    // 清空选择
    clearSelection() {
      this.selectedIds = [];
    },
    // 取消操作
    cancel() {
      common_vendor.index.navigateBack();
    },
    // 确认选择
    confirm() {
      const eventChannel = this.getOpenerEventChannel();
      if (eventChannel) {
        eventChannel.emit("selectResult", {
          selectedItems: this.selectedItems,
          selectedIds: this.selectedIds
        });
      }
      common_vendor.index.navigateBack();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.cancel && $options.cancel(...args)),
    b: common_vendor.t($data.title),
    c: common_vendor.o((...args) => $options.confirm && $options.confirm(...args)),
    d: common_vendor.o([($event) => $data.searchKeyword = $event.detail.value, (...args) => $options.handleSearch && $options.handleSearch(...args)]),
    e: $data.searchKeyword,
    f: $data.searchKeyword
  }, $data.searchKeyword ? {
    g: common_vendor.o((...args) => $options.clearSearch && $options.clearSearch(...args))
  } : {}, {
    h: $options.selectedItems.length > 0
  }, $options.selectedItems.length > 0 ? {
    i: common_vendor.t($options.selectedItems.length),
    j: common_vendor.o((...args) => $options.clearSelection && $options.clearSelection(...args)),
    k: common_vendor.f($options.selectedItems, (item, k0, i0) => {
      return {
        a: common_vendor.t(item[$data.nameField]),
        b: item[$data.idField],
        c: common_vendor.o(($event) => $options.toggleItem(item), item[$data.idField])
      };
    })
  } : {}, {
    l: common_vendor.f($options.filteredItems, (item, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item[$data.nameField]),
        b: $options.isItemSelected(item)
      }, $options.isItemSelected(item) ? {} : {}, {
        c: $options.isItemSelected(item) ? 1 : "",
        d: item[$data.idField],
        e: common_vendor.o(($event) => $options.toggleItem(item), item[$data.idField])
      });
    }),
    m: $options.filteredItems.length === 0
  }, $options.filteredItems.length === 0 ? {
    n: common_vendor.t($data.searchKeyword ? "未找到匹配的结果" : "暂无可选项")
  } : {}, {
    o: $data.listHeight
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../.sourcemap/mp-weixin/components/multi-selector.js.map
