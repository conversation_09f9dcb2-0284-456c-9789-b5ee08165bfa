"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    const now = /* @__PURE__ */ new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    const day = now.getDate();
    const formatDate = (y, m, d) => {
      return `${y}-${m < 10 ? "0" + m : m}-${d < 10 ? "0" + d : d}`;
    };
    const startDate = formatDate(year, month, 1);
    const endDate = formatDate(year, month, day);
    return {
      // 查询条件
      heatUnitOptions: ["全部", "金色家园", "翠湖花园", "阳光小区", "幸福家园", "和平广场"],
      heatUnitIndex: 0,
      statusOptions: ["全部", "已缴费", "未缴费"],
      statusIndex: 0,
      startDate,
      endDate,
      // 加载状态
      loading: false,
      loadingText: {
        contentdown: "正在加载...",
        contentrefresh: "加载中...",
        contentnomore: "没有更多数据了"
      },
      // 统计数据
      statsData: {
        total: 1e6,
        paid: 8e5,
        unpaid: 2e5
      },
      // 图表控制
      activeChart: "trend",
      // 模拟趋势数据
      mockTrendData: [
        { date: "1月", paid: 65, unpaid: 35 },
        { date: "2月", paid: 70, unpaid: 30 },
        { date: "3月", paid: 75, unpaid: 25 },
        { date: "4月", paid: 80, unpaid: 20 },
        { date: "5月", paid: 85, unpaid: 15 },
        { date: "6月", paid: 90, unpaid: 10 }
      ],
      isEmpty: false
    };
  },
  onLoad() {
    this.getPaymentStats();
  },
  methods: {
    // 选择热力单元变化
    handleHeatUnitChange(e) {
      this.heatUnitIndex = e.detail.value;
    },
    // 开始日期变化
    handleStartDateChange(e) {
      this.startDate = e.detail.value;
    },
    // 结束日期变化
    handleEndDateChange(e) {
      this.endDate = e.detail.value;
    },
    // 缴费状态变化
    handleStatusChange(e) {
      this.statusIndex = e.detail.value;
    },
    // 查询按钮点击
    queryStats() {
      this.getPaymentStats();
    },
    // 获取缴费统计数据
    getPaymentStats() {
      this.loading = true;
      const params = {
        heat_unit_name: this.heatUnitIndex === 0 ? "" : this.heatUnitOptions[this.heatUnitIndex],
        start_time: this.startDate + " 00:00:00",
        end_time: this.endDate + " 23:59:59",
        status: this.statusIndex === 0 ? "" : this.statusIndex === 1 ? "paid" : "unpaid"
      };
      common_vendor.index.__f__("log", "at pages/payment/stats.vue:250", "查询参数:", params);
      setTimeout(() => {
        const mockResponse = {
          code: 200,
          message: "缴费统计获取成功",
          data: {
            total: Math.floor(Math.random() * 1e6) + 5e5,
            paid: Math.floor(Math.random() * 8e5) + 2e5,
            unpaid: 0
          }
        };
        mockResponse.data.unpaid = mockResponse.data.total - mockResponse.data.paid;
        this.statsData = mockResponse.data;
        this.updateChartData();
        this.isEmpty = mockResponse.data.total === 0;
        this.loading = false;
      }, 1e3);
    },
    // 更新图表数据
    updateChartData() {
      const mockDates = ["1月", "2月", "3月", "4月", "5月", "6月"];
      this.mockTrendData = mockDates.map((date) => {
        const paidPercent = Math.floor(Math.random() * 30) + 60;
        return {
          date,
          paid: paidPercent,
          unpaid: 100 - paidPercent
        };
      });
    }
  }
};
if (!Array) {
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  _easycom_uni_load_more2();
}
const _easycom_uni_load_more = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-load-more/uni-load-more.js";
if (!Math) {
  _easycom_uni_load_more();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.heatUnitOptions[$data.heatUnitIndex]),
    b: common_vendor.o((...args) => $options.handleHeatUnitChange && $options.handleHeatUnitChange(...args)),
    c: $data.heatUnitIndex,
    d: $data.heatUnitOptions,
    e: common_vendor.t($data.startDate),
    f: $data.startDate,
    g: common_vendor.o((...args) => $options.handleStartDateChange && $options.handleStartDateChange(...args)),
    h: common_vendor.t($data.endDate),
    i: $data.endDate,
    j: common_vendor.o((...args) => $options.handleEndDateChange && $options.handleEndDateChange(...args)),
    k: common_vendor.t($data.statusOptions[$data.statusIndex]),
    l: common_vendor.o((...args) => $options.handleStatusChange && $options.handleStatusChange(...args)),
    m: $data.statusIndex,
    n: $data.statusOptions,
    o: common_vendor.o((...args) => $options.queryStats && $options.queryStats(...args)),
    p: !$data.loading
  }, !$data.loading ? {
    q: common_vendor.t($data.statsData.total.toLocaleString()),
    r: common_vendor.t($data.statsData.paid.toLocaleString()),
    s: common_vendor.t($data.statsData.unpaid.toLocaleString()),
    t: common_vendor.t(($data.statsData.paid / $data.statsData.total * 100).toFixed(1))
  } : {}, {
    v: $data.loading
  }, $data.loading ? {
    w: common_vendor.p({
      status: "loading",
      ["content-text"]: $data.loadingText
    })
  } : {}, {
    x: !$data.loading
  }, !$data.loading ? common_vendor.e({
    y: $data.activeChart === "trend" ? 1 : "",
    z: common_vendor.o(($event) => $data.activeChart = "trend"),
    A: $data.activeChart === "proportion" ? 1 : "",
    B: common_vendor.o(($event) => $data.activeChart = "proportion"),
    C: $data.activeChart === "trend"
  }, $data.activeChart === "trend" ? {
    D: common_vendor.f($data.mockTrendData, (item, index, i0) => {
      return {
        a: common_vendor.t(item.date),
        b: item.paid + "%",
        c: item.unpaid + "%",
        d: index
      };
    })
  } : {}, {
    E: $data.activeChart === "proportion"
  }, $data.activeChart === "proportion" ? {
    F: "polygon(50% 50%, 50% 0%, " + (50 + 50 * Math.cos(Math.PI * 2 * $data.statsData.paid / $data.statsData.total)) + "% " + (50 - 50 * Math.sin(Math.PI * 2 * $data.statsData.paid / $data.statsData.total)) + "%)",
    G: "rotate(" + 360 * $data.statsData.paid / $data.statsData.total + "deg)",
    H: "polygon(50% 50%, 50% 0%, " + (50 + 50 * Math.cos(Math.PI * 2 * $data.statsData.unpaid / $data.statsData.total)) + "% " + (50 - 50 * Math.sin(Math.PI * 2 * $data.statsData.unpaid / $data.statsData.total)) + "%)",
    I: common_vendor.t(($data.statsData.paid / $data.statsData.total * 100).toFixed(0)),
    J: common_vendor.t($data.statsData.paid.toLocaleString()),
    K: common_vendor.t($data.statsData.unpaid.toLocaleString())
  } : {}) : {}, {
    L: !$data.loading && $data.isEmpty
  }, !$data.loading && $data.isEmpty ? {
    M: common_assets._imports_0$3
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/payment/stats.js.map
