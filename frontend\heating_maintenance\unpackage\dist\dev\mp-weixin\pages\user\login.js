"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const _sfc_main = {
  data() {
    return {
      loginAccount: "",
      // 用户名或手机号
      password: "",
      // 密码
      rememberPassword: false,
      // 是否记住密码
      apiConnected: true,
      // API连接状态
      showRegister: false,
      // 是否显示注册表单
      countDown: 0,
      // 验证码倒计时
      registerForm: {
        username: "",
        password: "",
        confirmPassword: "",
        name: "",
        phone: "",
        verificationCode: ""
      }
    };
  },
  onLoad() {
    this.checkApiConnection();
    this.loadRememberedCredentials();
  },
  methods: {
    // 检测API连接
    async checkApiConnection() {
      try {
        common_vendor.index.__f__("log", "at pages/user/login.vue:197", "开始检测API连接...");
        this.apiConnected = await utils_api.testApiConnection();
        common_vendor.index.__f__("log", "at pages/user/login.vue:199", "API连接检测结果:", this.apiConnected);
        if (!this.apiConnected) {
          common_vendor.index.showToast({
            title: "无法连接到服务器，请确认后端服务是否已启动",
            icon: "none",
            duration: 3e3
          });
        }
      } catch (err) {
        common_vendor.index.__f__("error", "at pages/user/login.vue:209", "API连接检测出错:", err);
        this.apiConnected = false;
      }
    },
    // 加载记住的登录信息
    loadRememberedCredentials() {
      try {
        const rememberedAccount = common_vendor.index.getStorageSync("rememberedAccount");
        const rememberedPassword = common_vendor.index.getStorageSync("rememberedPassword");
        const isRemembered = common_vendor.index.getStorageSync("rememberPassword");
        if (isRemembered && rememberedAccount && rememberedPassword) {
          this.loginAccount = rememberedAccount;
          this.password = rememberedPassword;
          this.rememberPassword = true;
          common_vendor.index.__f__("log", "at pages/user/login.vue:225", "已加载记住的登录信息");
        }
      } catch (err) {
        common_vendor.index.__f__("error", "at pages/user/login.vue:228", "加载记住的登录信息失败:", err);
      }
    },
    // 保存登录信息
    saveCredentials() {
      try {
        if (this.rememberPassword) {
          common_vendor.index.setStorageSync("rememberedAccount", this.loginAccount);
          common_vendor.index.setStorageSync("rememberedPassword", this.password);
          common_vendor.index.setStorageSync("rememberPassword", true);
          common_vendor.index.__f__("log", "at pages/user/login.vue:239", "已保存登录信息");
        } else {
          common_vendor.index.removeStorageSync("rememberedAccount");
          common_vendor.index.removeStorageSync("rememberedPassword");
          common_vendor.index.removeStorageSync("rememberPassword");
          common_vendor.index.__f__("log", "at pages/user/login.vue:245", "已清除保存的登录信息");
        }
      } catch (err) {
        common_vendor.index.__f__("error", "at pages/user/login.vue:248", "保存登录信息失败:", err);
      }
    },
    // 切换记住密码状态
    toggleRememberPassword() {
      this.rememberPassword = !this.rememberPassword;
      if (!this.rememberPassword) {
        try {
          common_vendor.index.removeStorageSync("rememberedAccount");
          common_vendor.index.removeStorageSync("rememberedPassword");
          common_vendor.index.removeStorageSync("rememberPassword");
          common_vendor.index.__f__("log", "at pages/user/login.vue:261", "已清除保存的登录信息");
        } catch (err) {
          common_vendor.index.__f__("error", "at pages/user/login.vue:263", "清除登录信息失败:", err);
        }
      }
    },
    // 处理登录
    handleLogin() {
      if (!this.loginAccount.trim()) {
        common_vendor.index.showToast({
          title: "请输入用户名或手机号",
          icon: "none",
          position: "center"
        });
        return;
      }
      if (!this.password.trim()) {
        common_vendor.index.showToast({
          title: "请输入密码",
          icon: "none",
          position: "center"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "登录中..."
      });
      common_vendor.index.__f__("log", "at pages/user/login.vue:294", "准备提交登录请求:", this.loginAccount);
      const isPhone = /^1\d{10}$/.test(this.loginAccount);
      common_vendor.index.request({
        url: `${this.$baseUrl}/api/auth/login`,
        method: "POST",
        data: {
          username: this.loginAccount,
          password: this.password,
          loginType: isPhone ? "phone" : "username"
          // 添加登录类型标识
        },
        header: {
          "Content-Type": "application/json"
        },
        withCredentials: false,
        success: (res) => {
          var _a;
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("log", "at pages/user/login.vue:314", "登录响应:", res);
          if (res.statusCode === 200 && res.data.code === 200) {
            this.saveCredentials();
            const { token, userId, role, permissions, isPositioning, heatUnitId, positionCycle } = res.data.data;
            common_vendor.index.__f__("log", "at pages/user/login.vue:322", "userId=======", userId);
            common_vendor.index.setStorageSync("token", token);
            common_vendor.index.setStorageSync("userId", userId);
            common_vendor.index.setStorageSync("userRole", role);
            common_vendor.index.setStorageSync("userPermissions", permissions);
            common_vendor.index.setStorageSync("heatUnitId", heatUnitId || "");
            common_vendor.index.setStorageSync("isPositioning", isPositioning || 0);
            common_vendor.index.setStorageSync("positionCycle", positionCycle || 1);
            common_vendor.index.__f__("log", "at pages/user/login.vue:334", "是否开启定位上传: ", isPositioning || 0);
            common_vendor.index.__f__("log", "at pages/user/login.vue:335", "定位上传周期: ", positionCycle || 1, "分钟");
            common_vendor.index.__f__("log", "at pages/user/login.vue:336", "用户热力单位ID: ", heatUnitId || "无");
            this.getUserInfo();
            common_vendor.index.showToast({
              title: "登录成功",
              icon: "success"
            });
            setTimeout(() => {
              common_vendor.index.reLaunch({
                url: "/pages/home/<USER>"
              });
            }, 1e3);
          } else {
            common_vendor.index.showToast({
              title: ((_a = res.data) == null ? void 0 : _a.message) || "登录失败，请检查用户名和密码",
              icon: "none"
            });
          }
        },
        fail: (err) => {
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("error", "at pages/user/login.vue:365", "登录请求失败详情:", err);
          if (err.errMsg.includes("OPTIONS")) {
            common_vendor.index.showToast({
              title: "跨域请求失败，请检查后端CORS配置",
              icon: "none",
              duration: 3e3
            });
            return;
          }
          let errorMsg = "网络异常，请稍后重试";
          if (err.errMsg.includes("timeout")) {
            errorMsg = "请求超时，请检查网络连接";
          } else if (err.errMsg.includes("fail")) {
            errorMsg = "无法连接到服务器，请检查网络或后端服务状态";
          }
          common_vendor.index.showToast({
            title: errorMsg,
            icon: "none",
            duration: 3e3
          });
        }
      });
    },
    // 获取用户详细信息
    getUserInfo() {
      utils_api.userApi.getUserInfo().then((res) => {
        if (res.code === 200) {
          common_vendor.index.setStorageSync("userInfo", res.data);
        } else {
          common_vendor.index.__f__("error", "at pages/user/login.vue:405", "获取用户信息失败:", res);
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/user/login.vue:409", "获取用户信息请求失败:", err);
      });
    },
    // 打开用户协议
    openAgreement() {
      common_vendor.index.navigateTo({
        url: "/pages/user/agreement"
      });
    },
    // 切换登录/注册表单
    toggleForm() {
      this.showRegister = !this.showRegister;
    },
    // 获取验证码
    getVerificationCode() {
      if (this.countDown > 0) {
        return;
      }
      if (!this.registerForm.phone.trim()) {
        common_vendor.index.showToast({
          title: "请输入手机号",
          icon: "none"
        });
        return;
      }
      if (!/^1\d{10}$/.test(this.registerForm.phone)) {
        common_vendor.index.showToast({
          title: "请输入正确的手机号",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "发送中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        this.countDown = 60;
        const timer = setInterval(() => {
          this.countDown--;
          if (this.countDown <= 0) {
            clearInterval(timer);
          }
        }, 1e3);
        common_vendor.index.showToast({
          title: "验证码已发送",
          icon: "success"
        });
      }, 1500);
    },
    // 处理注册
    handleRegister() {
      if (!this.registerForm.phone.trim()) {
        common_vendor.index.showToast({
          title: "请输入手机号",
          icon: "none"
        });
        return;
      }
      if (!/^1\d{10}$/.test(this.registerForm.phone)) {
        common_vendor.index.showToast({
          title: "请输入正确的手机号",
          icon: "none"
        });
        return;
      }
      if (!this.registerForm.username.trim()) {
        common_vendor.index.showToast({
          title: "请输入用户名",
          icon: "none"
        });
        return;
      }
      if (!this.registerForm.password.trim()) {
        common_vendor.index.showToast({
          title: "请输入密码",
          icon: "none"
        });
        return;
      }
      if (this.registerForm.password !== this.registerForm.confirmPassword) {
        common_vendor.index.showToast({
          title: "两次输入的密码不一致",
          icon: "none"
        });
        return;
      }
      if (!this.registerForm.name.trim()) {
        common_vendor.index.showToast({
          title: "请输入姓名",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "注册中..."
      });
      common_vendor.index.request({
        url: `${this.$baseUrl}/api/auth/register`,
        method: "POST",
        data: {
          username: this.registerForm.username,
          password: this.registerForm.password,
          name: this.registerForm.name,
          phone: this.registerForm.phone
        },
        header: {
          "Content-Type": "application/json"
        },
        success: (res) => {
          var _a;
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("log", "at pages/user/login.vue:546", "注册响应:", res);
          if (res.statusCode === 200 && res.data.code === 200) {
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "该账号暂未审核,请等待管理员审核",
              duration: 3e3,
              icon: "none"
            });
            setTimeout(() => {
              this.loginAccount = this.registerForm.username;
              this.password = this.registerForm.password;
              this.registerForm = {
                username: "",
                password: "",
                confirmPassword: "",
                name: "",
                phone: "",
                verificationCode: ""
              };
              this.showRegister = false;
            }, 2500);
          } else {
            common_vendor.index.showToast({
              title: ((_a = res.data) == null ? void 0 : _a.message) || "注册失败，请稍后重试",
              icon: "none",
              duration: 2e3
            });
          }
        },
        fail: (err) => {
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("error", "at pages/user/login.vue:582", "注册请求失败:", err);
          common_vendor.index.showToast({
            title: "网络异常，请稍后重试",
            icon: "none"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.showRegister
  }, !$data.showRegister ? {} : {}, {
    b: !$data.showRegister
  }, !$data.showRegister ? {} : {}, {
    c: !$data.showRegister
  }, !$data.showRegister ? common_vendor.e({
    d: $data.loginAccount,
    e: common_vendor.o(($event) => $data.loginAccount = $event.detail.value),
    f: $data.password,
    g: common_vendor.o(($event) => $data.password = $event.detail.value),
    h: $data.rememberPassword
  }, $data.rememberPassword ? {} : {}, {
    i: $data.rememberPassword ? 1 : "",
    j: common_vendor.o((...args) => $options.toggleRememberPassword && $options.toggleRememberPassword(...args)),
    k: common_vendor.o((...args) => $options.handleLogin && $options.handleLogin(...args))
  }) : {}, {
    l: $data.showRegister
  }, $data.showRegister ? {
    m: $data.registerForm.username,
    n: common_vendor.o(($event) => $data.registerForm.username = $event.detail.value),
    o: $data.registerForm.name,
    p: common_vendor.o(($event) => $data.registerForm.name = $event.detail.value),
    q: $data.registerForm.phone,
    r: common_vendor.o(($event) => $data.registerForm.phone = $event.detail.value),
    s: $data.registerForm.password,
    t: common_vendor.o(($event) => $data.registerForm.password = $event.detail.value),
    v: $data.registerForm.confirmPassword,
    w: common_vendor.o(($event) => $data.registerForm.confirmPassword = $event.detail.value),
    x: common_vendor.o((...args) => $options.handleRegister && $options.handleRegister(...args)),
    y: common_vendor.o((...args) => $options.toggleForm && $options.toggleForm(...args))
  } : {}, {
    z: !$data.showRegister
  }, !$data.showRegister ? {} : {}, {
    A: common_vendor.o((...args) => $options.openAgreement && $options.openAgreement(...args)),
    B: !$data.showRegister
  }, !$data.showRegister ? {
    C: common_vendor.o((...args) => $options.toggleForm && $options.toggleForm(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/user/login.js.map
