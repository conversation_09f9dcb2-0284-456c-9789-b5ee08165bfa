"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const _sfc_main = {
  data() {
    return {
      formData: {
        planNo: "",
        name: "",
        startDate: "",
        endDate: "",
        executorIds: [],
        scheduleType: "daily",
        scheduleInterval: null,
        scheduleWeekDays: [],
        scheduleMonthDays: [],
        deviceIds: [],
        locations: "",
        patrolItem: [],
        description: "",
        heatUnitId: null
      },
      scheduleTypeOptions: [
        { label: "每日", value: "daily" },
        { label: "每周", value: "weekly" },
        { label: "每月", value: "monthly" }
      ],
      patrolTypeOptions: [],
      // 初始化为空数组，将通过API动态获取
      scheduleTypeIndex: 0,
      patrolTypeIndex: 0,
      staffOptions: [],
      staffIndex: 0,
      deviceOptions: [],
      deviceIndex: 0,
      // 任务模板
      taskTemplates: [],
      devicePatrolItems: {},
      searchKeyword: "",
      // 周和月选项
      weekOptions: [
        { label: "周一", value: 1 },
        { label: "周二", value: 2 },
        { label: "周三", value: 3 },
        { label: "周四", value: 4 },
        { label: "周五", value: 5 },
        { label: "周六", value: 6 },
        { label: "周日", value: 7 }
      ],
      monthOptions: Array.from({ length: 31 }, (_, i) => ({
        label: `${i + 1}`,
        value: i + 1
      })),
      // 已选执行人和设备的显示列表
      selectedExecutors: [],
      selectedDevices: [],
      // 热用户相关
      selectedHeatUnit: null,
      heatUnitOptions: [],
      heatUnitSearchKeyword: "",
      filteredHeatUnits: [],
      // 设备筛选
      deviceFilterIndex: 0,
      currentFilterDeviceId: null,
      // 弹窗状态
      isPopupOpen: false,
      tempSelectedDevices: [],
      // 临时存储选中的设备
      tempSelectedExecutors: []
      // 临时存储选中的人员
    };
  },
  computed: {
    filteredTemplates() {
      let templates = [];
      if (this.currentFilterDeviceId) {
        templates = this.devicePatrolItems[this.currentFilterDeviceId] || [];
      } else {
        templates = [];
        this.selectedDevices.forEach((device) => {
          const items = this.devicePatrolItems[device.id] || [];
          templates = templates.concat(items);
        });
      }
      if (!this.searchKeyword || this.searchKeyword.trim() === "") {
        return templates;
      }
      const keyword = this.searchKeyword.toLowerCase().trim();
      return templates.filter((template) => {
        return template.itemName && template.itemName.toLowerCase().includes(keyword) || template.description && template.description.toLowerCase().includes(keyword) || template.checkMethod && template.checkMethod.toLowerCase().includes(keyword) || template.categoryName && template.categoryName.toLowerCase().includes(keyword);
      });
    },
    selectedCount() {
      let count = 0;
      Object.values(this.devicePatrolItems).forEach((items) => {
        count += items.filter((item) => item.selected).length;
      });
      return count;
    },
    filteredMonthOptions() {
      return this.monthOptions.filter((_, index) => index < 31);
    },
    // 设备全选相关计算属性
    isAllDevicesSelected() {
      return this.deviceOptions.length > 0 && this.tempSelectedDevices.length === this.deviceOptions.length;
    },
    // 巡检项全选相关计算属性
    isAllTasksSelected() {
      return this.filteredTemplates.length > 0 && this.selectedTaskCount === this.filteredTemplates.length;
    },
    selectedTaskCount() {
      return this.filteredTemplates.filter((template) => template.selected).length;
    }
  },
  onLoad() {
    this.loadHeatUnitList();
    this.loadPatrolTypeOptions();
    this.selectedDevices = [];
    this.selectedExecutors = [];
    this.deviceFilterIndex = 0;
    this.currentFilterDeviceId = null;
  },
  methods: {
    // 加载热用户列表
    loadHeatUnitList() {
      const userHeatUnitId = common_vendor.index.getStorageSync("heatUnitId") || "";
      common_vendor.index.__f__("log", "at pages/patrol/create.vue:637", "用户项目权限:", userHeatUnitId);
      const hasAllPermission = userHeatUnitId === "0" || userHeatUnitId.split(",").length > 0 && userHeatUnitId.split(",").includes("0");
      utils_api.heatUnitApi.getList().then((res) => {
        if (res.code === 200 && res.data) {
          let allHeatUnits = res.data;
          if (!hasAllPermission && userHeatUnitId) {
            const authorizedIds = userHeatUnitId.split(",").map((id) => parseInt(id.trim()));
            allHeatUnits = allHeatUnits.filter((unit) => authorizedIds.includes(unit.id));
          }
          this.heatUnitOptions = allHeatUnits;
          this.filteredHeatUnits = [...allHeatUnits];
          if (this.filteredHeatUnits.length > 0) {
            this.selectedHeatUnit = this.filteredHeatUnits[0];
            this.formData.heatUnitId = this.selectedHeatUnit.id;
            this.loadDevicesByHeatUnit(this.selectedHeatUnit.id);
            this.loadStaffOptions(this.selectedHeatUnit.id);
          } else {
            this.loadStaffOptions();
          }
          common_vendor.index.__f__("log", "at pages/patrol/create.vue:677", "过滤后的热用户列表:", this.filteredHeatUnits);
          common_vendor.index.__f__("log", "at pages/patrol/create.vue:678", "默认选中的热用户:", this.selectedHeatUnit);
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/patrol/create.vue:682", "获取热用户列表失败:", err);
      });
    },
    // 加载人员列表 - 根据选中的热用户过滤
    loadStaffOptions(heatUnitId = null) {
      utils_api.userApi.getInspectorList({ role: "inspector" }).then((res) => {
        common_vendor.index.__f__("log", "at pages/patrol/create.vue:690", "巡检人员原始数据:", res.data);
        if (res.code === 200 && res.data) {
          const allStaff = res.data.map((user) => ({
            id: user.id,
            name: user.name,
            heatUnitId: user.heatUnitId,
            // 保存权限信息
            username: user.username,
            phone: user.phone,
            department: user.department
          }));
          if (heatUnitId !== null) {
            this.staffOptions = this.filterStaffByHeatUnit(allStaff, heatUnitId);
            common_vendor.index.__f__("log", "at pages/patrol/create.vue:705", `🔄 热用户ID ${heatUnitId} 对应的巡检人员:`, this.staffOptions);
            common_vendor.index.__f__("log", "at pages/patrol/create.vue:706", `📊 过滤前总人员数: ${allStaff.length}, 过滤后人员数: ${this.staffOptions.length}`);
            if (this.staffOptions.length === 0) {
              common_vendor.index.__f__("warn", "at pages/patrol/create.vue:710", `⚠️ 热用户ID ${heatUnitId} 没有对应权限的巡检人员`);
            }
          } else {
            this.staffOptions = allStaff;
            common_vendor.index.__f__("log", "at pages/patrol/create.vue:715", "📋 显示所有巡检人员:", this.staffOptions);
          }
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/patrol/create.vue:720", "获取人员列表失败:", err);
      });
    },
    // 根据热用户ID过滤人员
    filterStaffByHeatUnit(allStaff, heatUnitId) {
      common_vendor.index.__f__("log", "at pages/patrol/create.vue:726", `🔍 开始过滤人员，目标热用户ID: ${heatUnitId}`);
      const filteredStaff = allStaff.filter((staff) => {
        if (!staff.heatUnitId) {
          common_vendor.index.__f__("log", "at pages/patrol/create.vue:731", `❌ ${staff.name}: 无权限信息`);
          return false;
        }
        const staffHeatUnits = staff.heatUnitId.toString().split(",").map((id) => id.trim());
        common_vendor.index.__f__("log", "at pages/patrol/create.vue:737", `👤 ${staff.name}: 权限范围 [${staffHeatUnits.join(", ")}]`);
        const hasPermission = staffHeatUnits.includes("0") || staffHeatUnits.includes(heatUnitId.toString());
        common_vendor.index.__f__("log", "at pages/patrol/create.vue:741", `${hasPermission ? "✅" : "❌"} ${staff.name}: ${hasPermission ? "有权限" : "无权限"}`);
        return hasPermission;
      });
      common_vendor.index.__f__("log", "at pages/patrol/create.vue:746", `🎯 过滤结果: ${filteredStaff.length}/${allStaff.length} 人员有权限`);
      return filteredStaff;
    },
    // 根据热用户ID加载设备列表
    loadDevicesByHeatUnit(heatUnitId) {
      if (!heatUnitId) {
        common_vendor.index.__f__("error", "at pages/patrol/create.vue:753", "无效的热用户ID");
        return;
      }
      common_vendor.index.__f__("log", "at pages/patrol/create.vue:757", "开始加载热用户设备列表, 热用户ID:", heatUnitId);
      common_vendor.index.showLoading({
        title: "加载设备列表..."
      });
      utils_api.deviceApi.getDevicesByHeatUnitId(heatUnitId).then((res) => {
        common_vendor.index.__f__("log", "at pages/patrol/create.vue:766", "设备列表API响应:", res);
        common_vendor.index.hideLoading();
        if (res.code === 200 && res.data) {
          this.deviceOptions = res.data.map((device) => ({
            id: device.id,
            name: device.name,
            type: device.type,
            device_parent: device.deviceParent,
            heat_unit_id: this.selectedHeatUnit.id
          }));
          common_vendor.index.__f__("log", "at pages/patrol/create.vue:779", "处理后的设备列表:", this.deviceOptions);
          this.selectedDevices = [];
          this.formData.deviceIds = [];
          this.devicePatrolItems = {};
          this.formData.patrolItem = [];
        } else {
          common_vendor.index.__f__("error", "at pages/patrol/create.vue:789", "获取设备列表失败:", res.message || "未知错误");
          common_vendor.index.showToast({
            title: "获取设备列表失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/patrol/create.vue:798", `获取热用户ID:${heatUnitId})的设备列表失败`, err);
        common_vendor.index.showToast({
          title: "获取设备列表失败，请重试",
          icon: "none"
        });
      });
    },
    // 根据设备ID加载巡检项
    loadPatrolItemsByDevice(deviceId) {
      if (!deviceId)
        return;
      utils_api.deviceApi.getPatrolItemsByDeviceId(deviceId).then((res) => {
        if (res.code === 200 && res.data) {
          const items = res.data.map((item) => ({
            id: item.id,
            itemId: item.patrolItemDictId,
            deviceId: item.deviceId,
            itemName: item.itemName,
            categoryName: item.categoryName,
            paramType: item.paramType,
            unit: item.unit,
            normalRange: item.normalRange,
            checkMethod: item.checkMethod,
            importance: item.importance,
            description: item.description,
            selected: false
          }));
          this.$set(this.devicePatrolItems, deviceId, items);
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/patrol/create.vue:831", `获取设备(ID:${deviceId})的巡检项失败`, err);
      });
    },
    // 处理设备选择变更
    handleDeviceChange(e) {
      this.deviceIndex = e.detail.value;
      const selectedDevice = this.deviceOptions[this.deviceIndex];
      if (!this.formData.deviceIds.includes(selectedDevice.id)) {
        this.formData.deviceIds.push(selectedDevice.id);
        this.selectedDevices.push(selectedDevice);
        this.loadPatrolItemsByDevice(selectedDevice.id);
      } else {
        common_vendor.index.showToast({
          title: "该设备已添加",
          icon: "none"
        });
      }
    },
    // 移除已选设备
    removeDevice(deviceId) {
      const index = this.formData.deviceIds.findIndex((id) => id === deviceId);
      if (index !== -1) {
        this.formData.deviceIds.splice(index, 1);
        const deviceIndex = this.selectedDevices.findIndex((device) => device.id === deviceId);
        if (deviceIndex !== -1) {
          this.selectedDevices.splice(deviceIndex, 1);
        }
        if (this.devicePatrolItems[deviceId]) {
          delete this.devicePatrolItems[deviceId];
        }
        this.formData.patrolItem = this.formData.patrolItem.filter((item) => item.deviceId !== deviceId);
      }
    },
    // 多选执行人员变更
    handleStaffChange(e) {
      this.staffIndex = e.detail.value;
      const selectedStaff = this.staffOptions[this.staffIndex];
      if (!this.formData.executorIds.includes(selectedStaff.id)) {
        this.formData.executorIds.push(selectedStaff.id);
        this.selectedExecutors.push(selectedStaff);
      } else {
        common_vendor.index.showToast({
          title: "该人员已添加",
          icon: "none"
        });
      }
    },
    // 移除已选执行人
    removeExecutor(executorId) {
      const index = this.formData.executorIds.findIndex((id) => id === executorId);
      if (index !== -1) {
        this.formData.executorIds.splice(index, 1);
        const staffIndex = this.selectedExecutors.findIndex((staff) => staff.id === executorId);
        if (staffIndex !== -1) {
          this.selectedExecutors.splice(staffIndex, 1);
        }
      }
    },
    // 类型选择变更
    handleScheduleTypeChange(e) {
      this.scheduleTypeIndex = e.detail.value;
      this.formData.scheduleType = this.scheduleTypeOptions[this.scheduleTypeIndex].value;
    },
    // 巡检类型选择变更
    handlePatrolTypeChange(e) {
      this.patrolTypeIndex = e.detail.value;
      this.formData.patrolType = this.patrolTypeOptions[this.patrolTypeIndex].value;
    },
    // 开始日期变更
    handleStartDateChange(e) {
      this.formData.startDate = e.detail.value;
      if (this.formData.endDate && this.formData.endDate < this.formData.startDate) {
        this.formData.endDate = "";
      }
    },
    // 结束日期变更
    handleEndDateChange(e) {
      this.formData.endDate = e.detail.value;
    },
    // 显示任务选择
    showTaskSelector() {
      if (this.selectedDevices.length === 0) {
        common_vendor.index.showToast({
          title: "请先选择巡检设备",
          icon: "none"
        });
        return;
      }
      this.deviceFilterIndex = 0;
      this.currentFilterDeviceId = null;
      this.isPopupOpen = true;
      this.$refs.taskSelector.open();
    },
    // 隐藏任务选择
    hideTaskSelector() {
      common_vendor.index.showModal({
        title: "提示",
        content: "您尚未确认选择，关闭后已选项将不会保存，是否确认关闭？",
        success: (res) => {
          if (res.confirm) {
            this.isPopupOpen = false;
            this.$refs.taskSelector.close();
          }
        }
      });
    },
    // 切换任务选择状态
    toggleTaskSelection(index) {
      const template = this.filteredTemplates[index];
      const deviceItems = this.devicePatrolItems[template.deviceId];
      if (deviceItems) {
        const itemIndex = deviceItems.findIndex((item) => item.id === template.id);
        if (itemIndex !== -1) {
          this.$set(deviceItems[itemIndex], "selected", !deviceItems[itemIndex].selected);
        }
      }
    },
    // 切换周几选择
    toggleWeekDay(day) {
      const index = this.formData.scheduleWeekDays.indexOf(day);
      if (index === -1) {
        this.formData.scheduleWeekDays.push(day);
      } else {
        this.formData.scheduleWeekDays.splice(index, 1);
      }
    },
    // 切换月日选择
    toggleMonthDay(day) {
      const index = this.formData.scheduleMonthDays.indexOf(day);
      if (index === -1) {
        this.formData.scheduleMonthDays.push(day);
      } else {
        this.formData.scheduleMonthDays.splice(index, 1);
      }
    },
    // 确认任务选择
    confirmTaskSelection() {
      let selectedItems = [];
      Object.entries(this.devicePatrolItems).forEach(([deviceId, items]) => {
        const selected = items.filter((item) => item.selected);
        if (selected.length > 0) {
          selectedItems = selectedItems.concat(selected.map((item) => ({
            ...item,
            deviceId: parseInt(deviceId)
          })));
        }
      });
      this.formData.patrolItem = selectedItems.map((item) => ({
        id: item.id,
        itemId: item.itemId,
        deviceId: item.deviceId,
        itemName: item.itemName,
        categoryName: item.categoryName,
        checkMethod: item.checkMethod,
        normalRange: item.normalRange,
        description: item.description,
        paramType: item.paramType,
        unit: item.unit,
        importance: item.importance
      }));
      this.isPopupOpen = false;
      this.$refs.taskSelector.close();
      common_vendor.index.showToast({
        title: `已添加${selectedItems.length}个巡检项`,
        icon: "success",
        duration: 2e3
      });
    },
    // 移除任务
    removeTask(index) {
      const task = this.formData.patrolItem[index];
      if (task && task.deviceId && this.devicePatrolItems[task.deviceId]) {
        const items = this.devicePatrolItems[task.deviceId];
        const itemIndex = items.findIndex((item) => item.id === task.id);
        if (itemIndex !== -1) {
          this.$set(items[itemIndex], "selected", false);
        }
      }
      this.formData.patrolItem.splice(index, 1);
    },
    // 提交表单
    submitPlan() {
      if (this.filteredHeatUnits.length === 0) {
        common_vendor.index.showToast({
          title: "您没有任何热用户的权限，无法创建巡检计划",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      if (!this.selectedHeatUnit) {
        common_vendor.index.showToast({
          title: "请选择热用户",
          icon: "none"
        });
        return;
      }
      if (!this.formData.name) {
        common_vendor.index.showToast({
          title: "请输入计划名?",
          icon: "none"
        });
        return;
      }
      if (!this.formData.startDate) {
        common_vendor.index.showToast({
          title: "请选择开始日期",
          icon: "none"
        });
        return;
      }
      if (!this.formData.endDate) {
        common_vendor.index.showToast({
          title: "请选择结束日期",
          icon: "none"
        });
        return;
      }
      if (this.formData.executorIds.length === 0) {
        common_vendor.index.showToast({
          title: "请选择执行人员",
          icon: "none"
        });
        return;
      }
      if (this.formData.deviceIds.length === 0) {
        common_vendor.index.showToast({
          title: "请选择巡检设备",
          icon: "none"
        });
        return;
      }
      if (this.formData.scheduleType === "custom" && !this.formData.scheduleInterval) {
        common_vendor.index.showToast({
          title: "请输入巡检间隔天数",
          icon: "none"
        });
        return;
      }
      if (this.formData.scheduleType === "weekly" && this.formData.scheduleWeekDays.length === 0) {
        common_vendor.index.showToast({
          title: "请选择巡检星期",
          icon: "none"
        });
        return;
      }
      if (this.formData.scheduleType === "monthly" && this.formData.scheduleMonthDays.length === 0) {
        common_vendor.index.showToast({
          title: "请选择巡检日期",
          icon: "none"
        });
        return;
      }
      if (!this.formData.patrolType) {
        common_vendor.index.showToast({
          title: "请选择巡检类型",
          icon: "none"
        });
        return;
      }
      if (this.formData.patrolItem.length === 0) {
        common_vendor.index.showToast({
          title: "请添加巡检任务",
          icon: "none"
        });
        return;
      }
      this.formData.patrolItem.forEach((item) => {
        if (!item.deviceId && this.formData.deviceIds.length > 0) {
          item.deviceId = this.formData.deviceIds[0];
        }
      });
      const submitData = {
        name: this.formData.name,
        start_date: this.formData.startDate,
        end_date: this.formData.endDate,
        executor_ids: this.formData.executorIds,
        patrol_type: this.formData.patrolType,
        locations: this.selectedHeatUnit.name,
        heat_unit_id: this.selectedHeatUnit.id,
        device_ids: this.formData.deviceIds,
        schedule_type: this.formData.scheduleType,
        schedule_interval: this.formData.scheduleInterval,
        schedule_week_days: this.formData.scheduleWeekDays,
        schedule_month_days: this.formData.scheduleMonthDays,
        patrol_item: this.formData.patrolItem.map((item) => ({
          device_patrol_item_id: item.id
        }))
      };
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      common_vendor.index.__f__("log", "at pages/patrol/create.vue:1170", "提交巡检计划数据:", JSON.stringify(submitData, null, 2));
      utils_api.patrolApi.createPlan(submitData).then((res) => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "创建成功",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      }).catch((err) => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: err.errMsg || "创建失败，请重试",
          icon: "none"
        });
      });
    },
    // 搜索巡检项
    handleSearch() {
      common_vendor.index.__f__("log", "at pages/patrol/create.vue:1195", "搜索关键?", this.searchKeyword);
    },
    // 清空搜索
    clearSearch() {
      this.searchKeyword = "";
    },
    // 显示热用户选择
    showHeatUnitSelector() {
      if (this.filteredHeatUnits.length === 0) {
        common_vendor.index.showToast({
          title: "无可用热用户",
          icon: "none"
        });
        return;
      }
      this.isPopupOpen = true;
      this.$refs.heatUnitSelector.open();
    },
    // 隐藏热用户选择
    hideHeatUnitSelector() {
      this.isPopupOpen = false;
      this.$refs.heatUnitSelector.close();
    },
    // 选择热用户
    selectHeatUnit(heatUnit) {
      this.selectedHeatUnit = heatUnit;
      common_vendor.index.__f__("log", "at pages/patrol/create.vue:1225", "选择热用户 ", heatUnit.name, "ID:", heatUnit.id);
      this.formData.heatUnitId = heatUnit.id;
      this.loadStaffOptions(heatUnit.id);
      this.selectedExecutors.length > 0;
      this.selectedExecutors = [];
      this.formData.executorIds = [];
      this.loadDevicesByHeatUnit(heatUnit.id);
    },
    // 确认热用户选择
    confirmHeatUnitSelection() {
      if (this.selectedHeatUnit) {
        common_vendor.index.__f__("log", "at pages/patrol/create.vue:1244", "确认选择热用户", this.selectedHeatUnit.name, "ID:", this.selectedHeatUnit.id);
        this.isPopupOpen = false;
        this.$refs.heatUnitSelector.close();
      } else {
        common_vendor.index.showToast({
          title: "请先选择热用户",
          icon: "none"
        });
        return;
      }
    },
    // 搜索热用户
    handleHeatUnitSearch() {
      if (!this.heatUnitSearchKeyword || this.heatUnitSearchKeyword.trim() === "") {
        this.filteredHeatUnits = [...this.heatUnitOptions];
        return;
      }
      const keyword = this.heatUnitSearchKeyword.toLowerCase().trim();
      this.filteredHeatUnits = this.heatUnitOptions.filter(
        (unit) => unit.name.toLowerCase().includes(keyword)
      );
    },
    // 清空热用户搜索
    clearHeatUnitSearch() {
      this.heatUnitSearchKeyword = "";
      this.filteredHeatUnits = [...this.heatUnitOptions];
    },
    // 获取设备名称
    getDeviceName(deviceId) {
      const device = this.selectedDevices.find((d) => d.id === deviceId);
      return device ? device.name : "未知设备";
    },
    // 处理设备筛选
    handleDeviceFilterChange(e) {
      this.deviceFilterIndex = parseInt(e.detail.value);
      if (this.deviceFilterIndex === 0) {
        this.currentFilterDeviceId = null;
      } else {
        const selectedDevice = this.selectedDevices[this.deviceFilterIndex - 1];
        this.currentFilterDeviceId = selectedDevice ? selectedDevice.id : null;
      }
    },
    // 获取重要性标签
    getImportanceLabel(importance) {
      switch (importance) {
        case "normal":
          return "普通";
        case "important":
          return "重要";
        case "critical":
          return "关键";
        default:
          return "普通";
      }
    },
    // 获取重要性类
    getImportanceClass(importance) {
      switch (importance) {
        case "normal":
          return "normal-importance";
        case "important":
          return "important-importance";
        case "critical":
          return "critical-importance";
        default:
          return "normal-importance";
      }
    },
    // 显示设备选择器
    showDeviceSelector() {
      if (!this.selectedHeatUnit) {
        common_vendor.index.showToast({
          title: "请先选择热用户",
          icon: "none"
        });
        return;
      }
      this.tempSelectedDevices = [...this.selectedDevices];
      this.isPopupOpen = true;
      this.$refs.deviceSelector.open();
    },
    // 隐藏设备选择器
    hideDeviceSelector() {
      common_vendor.index.showModal({
        title: "提示",
        content: "您尚未确认选择，关闭后已选项将不会保存，是否确认关闭？",
        success: (res) => {
          if (res.confirm) {
            this.isPopupOpen = false;
            this.$refs.deviceSelector.close();
            this.tempSelectedDevices = [...this.selectedDevices];
          }
        }
      });
    },
    // 切换设备选择状态
    toggleDeviceSelection(device) {
      const index = this.tempSelectedDevices.findIndex((d) => d.id === device.id);
      if (index === -1) {
        this.tempSelectedDevices.push(device);
      } else {
        this.tempSelectedDevices.splice(index, 1);
      }
    },
    // 确认设备选择
    confirmDeviceSelection() {
      if (this.tempSelectedDevices.length === 0) {
        common_vendor.index.showToast({
          title: "请至少选择一个设备",
          icon: "none"
        });
        return;
      }
      this.selectedDevices = [...this.tempSelectedDevices];
      this.formData.deviceIds = this.selectedDevices.map((device) => device.id);
      this.formData.patrolItem = [];
      this.selectedDevices.forEach((device) => {
        this.loadPatrolItemsByDevice(device.id);
      });
      this.isPopupOpen = false;
      this.$refs.deviceSelector.close();
      common_vendor.index.showToast({
        title: `已选择${this.selectedDevices.length}个设备`,
        icon: "success"
      });
    },
    // 检查设备是否被选中
    isDeviceSelected(deviceId) {
      return this.tempSelectedDevices.some((device) => device.id === deviceId);
    },
    // 显示人员选择
    showStaffSelector() {
      this.tempSelectedExecutors = [...this.selectedExecutors];
      this.isPopupOpen = true;
      this.$refs.staffSelector.open();
    },
    // 隐藏人员选择
    hideStaffSelector() {
      common_vendor.index.showModal({
        title: "提示",
        content: "您尚未确认选择，关闭后已选项将不会保存，是否确认关闭？",
        success: (res) => {
          if (res.confirm) {
            this.isPopupOpen = false;
            this.$refs.staffSelector.close();
            this.tempSelectedExecutors = [...this.selectedExecutors];
          }
        }
      });
    },
    // 切换人员选择状态
    toggleStaffSelection(staff) {
      const index = this.tempSelectedExecutors.findIndex((s) => s.id === staff.id);
      if (index === -1) {
        this.tempSelectedExecutors.push(staff);
      } else {
        this.tempSelectedExecutors.splice(index, 1);
      }
    },
    // 检查人员是否被选中
    isStaffSelected(staffId) {
      return this.tempSelectedExecutors.some((staff) => staff.id === staffId);
    },
    // 确认人员选择
    confirmStaffSelection() {
      if (this.tempSelectedExecutors.length === 0) {
        common_vendor.index.showToast({
          title: "请至少选择一个人员",
          icon: "none"
        });
        return;
      }
      this.selectedExecutors = [...this.tempSelectedExecutors];
      this.formData.executorIds = this.selectedExecutors.map((staff) => staff.id);
      this.isPopupOpen = false;
      this.$refs.staffSelector.close();
      common_vendor.index.showToast({
        title: `已选择${this.selectedExecutors.length}个人员`,
        icon: "success"
      });
    },
    // 加载巡检类型选项
    loadPatrolTypeOptions() {
      utils_api.dictApi.getDictDataByDictId(11).then((res) => {
        if (res.code === 200 && res.data) {
          this.patrolTypeOptions = res.data.map((item) => ({
            label: item.name,
            value: item.name
          }));
          if (this.patrolTypeOptions.length === 0) {
            this.patrolTypeOptions = [
              { label: "日常巡检", value: "日常巡检" },
              { label: "设备巡检", value: "设备巡检" },
              { label: "管道巡检", value: "管道巡检" },
              { label: "阀门巡检", value: "阀门巡检" },
              { label: "换热站巡检", value: "换热站巡检" }
            ];
          }
          if (this.patrolTypeOptions.length > 0 && !this.formData.patrolType) {
            this.formData.patrolType = this.patrolTypeOptions[0].value;
            this.patrolTypeIndex = 0;
          }
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/patrol/create.vue:1496", "获取巡检类型选项失败:", err);
        this.patrolTypeOptions = [
          { label: "日常巡检", value: "日常巡检" },
          { label: "设备巡检", value: "设备巡检" },
          { label: "管道巡检", value: "管道巡检" },
          { label: "阀门巡检", value: "阀门巡检" },
          { label: "换热站巡检", value: "换热站巡检" }
        ];
        if (this.patrolTypeOptions.length > 0 && !this.formData.patrolType) {
          this.formData.patrolType = this.patrolTypeOptions[0].value;
          this.patrolTypeIndex = 0;
        }
      });
    },
    // 设备全选功能
    toggleDeviceSelectAll() {
      if (this.isAllDevicesSelected) {
        this.tempSelectedDevices = [];
      } else {
        this.tempSelectedDevices = [...this.deviceOptions];
      }
    },
    // 巡检项全选功能
    toggleTaskSelectAll() {
      const shouldSelectAll = !this.isAllTasksSelected;
      this.filteredTemplates.forEach((template) => {
        const deviceItems = this.devicePatrolItems[template.deviceId];
        if (deviceItems) {
          const itemIndex = deviceItems.findIndex((item) => item.id === template.id);
          if (itemIndex !== -1) {
            this.$set(deviceItems[itemIndex], "selected", shouldSelectAll);
          }
        }
      });
      const message = shouldSelectAll ? `已全选 ${this.filteredTemplates.length} 个巡检项` : `已取消全选 ${this.filteredTemplates.length} 个巡检项`;
      common_vendor.index.showToast({
        title: message,
        icon: "none",
        duration: 1500
      });
    }
  }
};
if (!Array) {
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  _easycom_uni_popup2();
}
const _easycom_uni_popup = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a;
  return common_vendor.e({
    a: $data.formData.name,
    b: common_vendor.o(($event) => $data.formData.name = $event.detail.value),
    c: common_vendor.t($data.scheduleTypeOptions[$data.scheduleTypeIndex].label),
    d: $data.scheduleTypeOptions,
    e: $data.scheduleTypeIndex,
    f: common_vendor.o((...args) => $options.handleScheduleTypeChange && $options.handleScheduleTypeChange(...args)),
    g: common_vendor.t($data.patrolTypeOptions[$data.patrolTypeIndex].label),
    h: $data.patrolTypeOptions,
    i: $data.patrolTypeIndex,
    j: common_vendor.o((...args) => $options.handlePatrolTypeChange && $options.handlePatrolTypeChange(...args)),
    k: common_vendor.t($data.formData.startDate || "请选择开始日期"),
    l: $data.formData.startDate,
    m: common_vendor.o((...args) => $options.handleStartDateChange && $options.handleStartDateChange(...args)),
    n: common_vendor.t($data.formData.endDate || "请选择结束日期"),
    o: $data.formData.endDate,
    p: $data.formData.startDate || "2023-01-01",
    q: common_vendor.o((...args) => $options.handleEndDateChange && $options.handleEndDateChange(...args)),
    r: $data.selectedHeatUnit
  }, $data.selectedHeatUnit ? {
    s: common_vendor.t($data.selectedHeatUnit.name)
  } : {
    t: common_vendor.t($data.filteredHeatUnits.length > 0 ? "请选择热用户" : "无可用热用户")
  }, {
    v: common_vendor.o((...args) => $options.showHeatUnitSelector && $options.showHeatUnitSelector(...args)),
    w: common_vendor.t($data.selectedExecutors.length > 0 ? `已选择 ${$data.selectedExecutors.length} 个人员` : "请选择巡检人员"),
    x: common_vendor.o((...args) => $options.showStaffSelector && $options.showStaffSelector(...args)),
    y: $data.selectedExecutors.length > 0
  }, $data.selectedExecutors.length > 0 ? {
    z: common_vendor.f($data.selectedExecutors, (executor, index, i0) => {
      return {
        a: common_vendor.t(executor.name),
        b: common_vendor.o(($event) => $options.removeExecutor(executor.id), "executor-" + index),
        c: "executor-" + index
      };
    })
  } : {}, {
    A: common_vendor.t($data.selectedDevices.length > 0 ? `已选择 ${$data.selectedDevices.length} 个设备` : "请选择巡检设备"),
    B: common_vendor.o((...args) => $options.showDeviceSelector && $options.showDeviceSelector(...args)),
    C: $data.selectedDevices.length > 0
  }, $data.selectedDevices.length > 0 ? {
    D: common_vendor.f($data.selectedDevices, (device, index, i0) => {
      return {
        a: common_vendor.t(device.name),
        b: common_vendor.o(($event) => $options.removeDevice(device.id), "device-" + index),
        c: "device-" + index
      };
    })
  } : {}, {
    E: $data.formData.description,
    F: common_vendor.o(($event) => $data.formData.description = $event.detail.value),
    G: $data.formData.scheduleType === "custom"
  }, $data.formData.scheduleType === "custom" ? {
    H: $data.formData.scheduleInterval,
    I: common_vendor.o(($event) => $data.formData.scheduleInterval = $event.detail.value)
  } : {}, {
    J: $data.formData.scheduleType === "weekly"
  }, $data.formData.scheduleType === "weekly" ? {
    K: common_vendor.f($data.weekOptions, (day, index, i0) => {
      return {
        a: common_vendor.t(day.label),
        b: "week-" + index,
        c: $data.formData.scheduleWeekDays.includes(day.value) ? 1 : "",
        d: common_vendor.o(($event) => $options.toggleWeekDay(day.value), "week-" + index)
      };
    })
  } : {}, {
    L: $data.formData.scheduleType === "monthly"
  }, $data.formData.scheduleType === "monthly" ? {
    M: common_vendor.f($options.filteredMonthOptions, (day, index, i0) => {
      return {
        a: common_vendor.t(day.label),
        b: "month-" + index,
        c: $data.formData.scheduleMonthDays.includes(day.value) ? 1 : "",
        d: common_vendor.o(($event) => $options.toggleMonthDay(day.value), "month-" + index)
      };
    })
  } : {}, {
    N: common_vendor.o((...args) => $options.showTaskSelector && $options.showTaskSelector(...args)),
    O: $data.selectedDevices.length === 0 ? 1 : "",
    P: $data.formData.patrolItem.length > 0
  }, $data.formData.patrolItem.length > 0 ? {
    Q: common_vendor.f($data.formData.patrolItem, (task, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(task.itemName),
        b: common_vendor.t($options.getDeviceName(task.deviceId)),
        c: common_vendor.t($options.getImportanceLabel(task.importance)),
        d: common_vendor.n($options.getImportanceClass(task.importance)),
        e: common_vendor.t(task.categoryName),
        f: common_vendor.t(task.description),
        g: common_vendor.t(task.checkMethod),
        h: task.paramType === "number"
      }, task.paramType === "number" ? {
        i: common_vendor.t(task.normalRange),
        j: common_vendor.t(task.unit)
      } : task.paramType === "selection" ? {
        l: common_vendor.t(task.normalRange)
      } : task.paramType === "boolean" ? {
        n: common_vendor.t(task.normalRange === "true" ? "是" : "否")
      } : {}, {
        k: task.paramType === "selection",
        m: task.paramType === "boolean",
        o: common_vendor.o(($event) => $options.removeTask(index), "task-" + index),
        p: "task-" + index
      });
    })
  } : {}, {
    R: $data.isPopupOpen ? "none" : "block",
    S: common_vendor.o((...args) => $options.submitPlan && $options.submitPlan(...args)),
    T: common_vendor.o((...args) => $options.confirmHeatUnitSelection && $options.confirmHeatUnitSelection(...args)),
    U: common_vendor.o((...args) => $options.hideHeatUnitSelector && $options.hideHeatUnitSelector(...args)),
    V: common_vendor.o([($event) => $data.heatUnitSearchKeyword = $event.detail.value, (...args) => $options.handleHeatUnitSearch && $options.handleHeatUnitSearch(...args)]),
    W: $data.heatUnitSearchKeyword,
    X: $data.heatUnitSearchKeyword
  }, $data.heatUnitSearchKeyword ? {
    Y: common_vendor.o((...args) => $options.clearHeatUnitSearch && $options.clearHeatUnitSearch(...args))
  } : {}, {
    Z: common_vendor.o((...args) => $options.handleHeatUnitSearch && $options.handleHeatUnitSearch(...args)),
    aa: common_vendor.f($data.filteredHeatUnits, (heatUnit, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(heatUnit.name),
        b: $data.selectedHeatUnit && $data.selectedHeatUnit.id === heatUnit.id
      }, $data.selectedHeatUnit && $data.selectedHeatUnit.id === heatUnit.id ? {} : {}, {
        c: $data.selectedHeatUnit && $data.selectedHeatUnit.id === heatUnit.id ? 1 : "",
        d: heatUnit.id,
        e: common_vendor.o(($event) => $options.selectHeatUnit(heatUnit), heatUnit.id)
      });
    }),
    ab: $data.filteredHeatUnits.length === 0
  }, $data.filteredHeatUnits.length === 0 ? {} : {}, {
    ac: common_vendor.sr("heatUnitSelector", "778b6e1a-0"),
    ad: common_vendor.p({
      type: "bottom"
    }),
    ae: common_vendor.o((...args) => $options.confirmDeviceSelection && $options.confirmDeviceSelection(...args)),
    af: common_vendor.o((...args) => $options.hideDeviceSelector && $options.hideDeviceSelector(...args)),
    ag: $data.deviceOptions.length > 0
  }, $data.deviceOptions.length > 0 ? common_vendor.e({
    ah: $options.isAllDevicesSelected
  }, $options.isAllDevicesSelected ? {} : {}, {
    ai: $options.isAllDevicesSelected ? 1 : "",
    aj: common_vendor.t($data.tempSelectedDevices.length),
    ak: common_vendor.t($data.deviceOptions.length),
    al: $options.isAllDevicesSelected ? 1 : "",
    am: common_vendor.o((...args) => $options.toggleDeviceSelectAll && $options.toggleDeviceSelectAll(...args))
  }) : {}, {
    an: common_vendor.f($data.deviceOptions, (device, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(device.name),
        b: $options.isDeviceSelected(device.id)
      }, $options.isDeviceSelected(device.id) ? {} : {}, {
        c: $options.isDeviceSelected(device.id) ? 1 : "",
        d: device.id,
        e: common_vendor.o(($event) => $options.toggleDeviceSelection(device), device.id)
      });
    }),
    ao: $data.deviceOptions.length === 0
  }, $data.deviceOptions.length === 0 ? {} : {}, {
    ap: common_vendor.sr("deviceSelector", "778b6e1a-1"),
    aq: common_vendor.p({
      type: "bottom"
    }),
    ar: common_vendor.o((...args) => $options.confirmStaffSelection && $options.confirmStaffSelection(...args)),
    as: common_vendor.o((...args) => $options.hideStaffSelector && $options.hideStaffSelector(...args)),
    at: common_vendor.f($data.staffOptions, (staff, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(staff.name),
        b: $options.isStaffSelected(staff.id)
      }, $options.isStaffSelected(staff.id) ? {} : {}, {
        c: $options.isStaffSelected(staff.id) ? 1 : "",
        d: staff.id,
        e: common_vendor.o(($event) => $options.toggleStaffSelection(staff), staff.id)
      });
    }),
    av: $data.staffOptions.length === 0
  }, $data.staffOptions.length === 0 ? common_vendor.e({
    aw: $data.selectedHeatUnit
  }, $data.selectedHeatUnit ? {
    ax: common_vendor.t($data.selectedHeatUnit.name)
  } : {}) : {}, {
    ay: common_vendor.sr("staffSelector", "778b6e1a-2"),
    az: common_vendor.p({
      type: "bottom"
    }),
    aA: common_vendor.o((...args) => $options.confirmTaskSelection && $options.confirmTaskSelection(...args)),
    aB: common_vendor.o((...args) => $options.hideTaskSelector && $options.hideTaskSelector(...args)),
    aC: common_vendor.t($data.deviceFilterIndex === 0 ? "全部设备" : (_a = $data.selectedDevices[$data.deviceFilterIndex - 1]) == null ? void 0 : _a.name),
    aD: [{
      name: "全部设备"
    }, ...$data.selectedDevices],
    aE: $data.deviceFilterIndex,
    aF: common_vendor.o((...args) => $options.handleDeviceFilterChange && $options.handleDeviceFilterChange(...args)),
    aG: common_vendor.o([($event) => $data.searchKeyword = $event.detail.value, (...args) => $options.handleSearch && $options.handleSearch(...args)]),
    aH: $data.searchKeyword,
    aI: $data.searchKeyword
  }, $data.searchKeyword ? {
    aJ: common_vendor.o((...args) => $options.clearSearch && $options.clearSearch(...args))
  } : {}, {
    aK: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    aL: $options.filteredTemplates.length > 0
  }, $options.filteredTemplates.length > 0 ? common_vendor.e({
    aM: $options.isAllTasksSelected
  }, $options.isAllTasksSelected ? {} : {}, {
    aN: $options.isAllTasksSelected ? "#007aff" : "#fff",
    aO: common_vendor.t($options.selectedTaskCount),
    aP: common_vendor.t($options.filteredTemplates.length),
    aQ: $options.isAllTasksSelected ? "#007aff" : "#e6f7ff",
    aR: $options.isAllTasksSelected ? "#fff" : "#007aff",
    aS: $options.isAllTasksSelected ? "1px solid #007aff" : "1px solid #91d5ff",
    aT: common_vendor.o((...args) => $options.toggleTaskSelectAll && $options.toggleTaskSelectAll(...args))
  }) : {}, {
    aU: common_vendor.f($options.filteredTemplates, (template, index, i0) => {
      return common_vendor.e({
        a: template.selected
      }, template.selected ? {} : {}, {
        b: template.selected ? 1 : "",
        c: common_vendor.t(template.itemName),
        d: common_vendor.t($options.getImportanceLabel(template.importance)),
        e: common_vendor.n($options.getImportanceClass(template.importance)),
        f: common_vendor.t($options.getDeviceName(template.deviceId)),
        g: common_vendor.t(template.categoryName),
        h: common_vendor.t(template.description),
        i: common_vendor.t(template.checkMethod),
        j: template.paramType === "number"
      }, template.paramType === "number" ? {
        k: common_vendor.t(template.normalRange),
        l: common_vendor.t(template.unit)
      } : template.paramType === "selection" ? {
        n: common_vendor.t(template.normalRange)
      } : template.paramType === "boolean" ? {
        p: common_vendor.t(template.normalRange === "true" ? "是" : "否")
      } : {}, {
        m: template.paramType === "selection",
        o: template.paramType === "boolean",
        q: index,
        r: template.selected ? 1 : "",
        s: common_vendor.o(($event) => $options.toggleTaskSelection(index), index)
      });
    }),
    aV: $options.filteredTemplates.length === 0
  }, $options.filteredTemplates.length === 0 ? {} : {}, {
    aW: common_vendor.t($options.selectedCount),
    aX: common_vendor.sr("taskSelector", "778b6e1a-3"),
    aY: common_vendor.p({
      type: "bottom"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-778b6e1a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/patrol/create.js.map
