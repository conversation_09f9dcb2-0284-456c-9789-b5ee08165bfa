/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.message-center-container {
  padding: 0rpx 20rpx 20rpx 20rpx;
  box-sizing: border-box;
}
.message-tabs {
  display: flex;
  background-color: #fff;
  margin-bottom: 20rpx;
  overflow-x: auto;
  white-space: nowrap;
  position: -webkit-sticky;
  position: sticky;
  top: 0rpx;
  z-index: 999;
}
.message-tabs .message-tab {
  flex: 1;
  min-width: 120rpx;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  position: relative;
}
.message-tabs .message-tab .badge {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background-color: #f5222d;
  color: #fff;
  font-size: 20rpx;
  border-radius: 20rpx;
  padding: 2rpx 10rpx;
  min-width: 20rpx;
  height: 20rpx;
  line-height: 20rpx;
  text-align: center;
}
.message-tabs .message-tab.active {
  color: #1890ff;
  font-weight: bold;
}
.message-tabs .message-tab.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #1890ff;
}
.action-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
  padding: 16rpx 20rpx;
}
.action-toolbar .action-btn {
  font-size: 26rpx;
  color: #1890ff;
  padding: 6rpx 16rpx;
}
.action-toolbar .action-filter {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #333;
}
.action-toolbar .action-filter .action-arrow {
  margin-left: 10rpx;
  font-size: 24rpx;
}
.filter-panel {
  background-color: #fff;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}
.filter-panel .filter-item {
  padding: 16rpx 0;
  font-size: 28rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}
.filter-panel .filter-item:last-child {
  border-bottom: none;
}
.filter-panel .filter-item.active {
  color: #1890ff;
}
.message-list .message-card {
  background-color: #fff;
  border-radius: 8rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  border-left: 6rpx solid transparent;
}
.message-list .message-card.unread {
  background-color: rgba(24, 144, 255, 0.05);
}
.message-list .message-card.urgent {
  border-left-color: #f5222d;
}
.message-list .message-card.important {
  border-left-color: #faad14;
}
.message-list .message-card.normal {
  border-left-color: #1890ff;
}
.message-list .message-card .message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.message-list .message-card .message-header .message-title {
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
}
.message-list .message-card .message-header .message-title .priority-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 12rpx;
}
.message-list .message-card .message-header .message-title .priority-dot.urgent {
  background-color: #f5222d;
}
.message-list .message-card .message-header .message-title .priority-dot.important {
  background-color: #faad14;
}
.message-list .message-card .message-header .message-title .priority-dot.normal {
  background-color: #1890ff;
}
.message-list .message-card .message-header .message-time {
  font-size: 24rpx;
  color: #666;
}
.message-list .message-card .message-content {
  font-size: 28rpx;
  line-height: 1.5;
  margin-bottom: 16rpx;
}
.message-list .message-card .message-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.message-list .message-card .message-footer .message-type {
  font-size: 24rpx;
  color: #666;
  background-color: #f5f5f5;
  padding: 4rpx 16rpx;
  border-radius: 4rpx;
}
.message-list .message-card .message-footer .message-actions {
  display: flex;
}
.message-list .message-card .message-footer .message-actions .action-item {
  font-size: 24rpx;
  color: #1890ff;
  margin-left: 20rpx;
}
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-state .empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-state .empty-text {
  font-size: 28rpx;
  color: #666;
}
.load-more,
.no-more {
  text-align: center;
  padding: 20rpx 0;
  font-size: 26rpx;
  color: #666;
}
.message-actions .action-item.ignore-btn {
  color: #faad14;
}
.message-actions .action-item.delete-btn {
  color: #f5222d;
}