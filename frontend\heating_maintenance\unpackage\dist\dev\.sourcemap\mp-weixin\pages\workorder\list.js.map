{"version": 3, "file": "list.js", "sources": ["pages/workorder/list.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvd29ya29yZGVyL2xpc3QudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"workorder-list-container\">\r\n    <!-- Tab 切换 -->\r\n    <view class=\"tab-container\">\r\n      <view \r\n        class=\"tab-item\" \r\n        :class=\"{ active: activeTab === 'my' }\" \r\n        @click=\"switchTab('my')\"\r\n      >\r\n        我的工单\r\n      </view>\r\n      <view \r\n        class=\"tab-item\" \r\n        :class=\"{ active: activeTab === 'pending' }\" \r\n        @click=\"switchTab('pending')\"\r\n      >\r\n        待接单\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 顶部筛选 -->\r\n    <view class=\"filter-section-seach\">\r\n      <view class=\"date-filter\" style=\"flex: 1;\">\r\n        <picker mode=\"date\" :value=\"filterDate\" @change=\"onDateChange\">\r\n          <view class=\"date-picker\">\r\n             <text class=\"date-text\">{{ filterDate || \"选择日期\" }}</text>\r\n          </view>\r\n        </picker>\r\n      </view>\r\n      <button class=\"refresh-button\" @click=\"refreshData\">重置</button>\r\n    </view>\r\n    \r\n    <!-- 筛选条件区域 -->\r\n    <view class=\"filter-condition\" v-if=\"showFilterCondition\">\r\n      <view class=\"condition-header\">\r\n        <text class=\"condition-title\">筛选条件</text>\r\n        <text class=\"close-icon\" @click=\"toggleFilterCondition\">×</text>\r\n      </view>\r\n\r\n      <!-- 查询日期 -->\r\n      <view class=\"filter-section\">\r\n        <view class=\"section-header\">\r\n          <view class=\"blue-bar\"></view>\r\n          <text class=\"section-title\">查询日期</text>\r\n        </view>\r\n        <view class=\"date-range\">\r\n          <view class=\"date-picker full-width\">\r\n            <picker mode=\"date\" :value=\"filters.startDate\" @change=\"onStartDateChange\">\r\n              <view class=\"date-text\">{{ filters.startDate || \"选择日期\" }}</view>\r\n            </picker>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 筛选操作按钮 -->\r\n      <view class=\"filter-actions\">\r\n        <button class=\"filter-reset\" @click=\"resetFilters\">重置</button>\r\n        <button class=\"filter-confirm\" @click=\"applyFilters\">确认</button>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 工单列表 -->\r\n    <scroll-view\r\n      scroll-y=\"true\"\r\n      class=\"workorder-list\"\r\n      :style=\"{\r\n        height: showFilterCondition ? 'calc(100vh - 480rpx)' : 'calc(100vh - 290rpx)',\r\n      }\"\r\n      @scrolltolower=\"loadMoreData\"\r\n      lower-threshold=\"100\"\r\n      :refresher-enabled=\"true\"\r\n      :refresher-triggered=\"isRefreshing\"\r\n      @refresherrefresh=\"refreshData\"\r\n    >\r\n      <template v-if=\"workorderList.length > 0\">\r\n        <view\r\n          class=\"workorder-item\"\r\n          v-for=\"(item, index) in workorderList\"\r\n          :key=\"index\"\r\n          @click=\"viewOrderDetail(item.orderId)\"\r\n        >\r\n          <view class=\"workorder-header\">\r\n            <text class=\"workorder-code\">{{ item.orderNo }}</text>\r\n            <view class=\"status-tag\" :class=\"getStatusClass(getDisplayStatus(item))\">{{ getDisplayStatus(item) }}</view>\r\n          </view>\r\n          <view class=\"workorder-info\">\r\n            <view class=\"info-item\">\r\n              <text class=\"location\">{{ item.heatUnitName }}</text>\r\n            </view>\r\n            <view class=\"info-item\">\r\n              <text class=\"info-label\">故障类型:</text>\r\n              <text class=\"fault-type\">{{ item.faultType }}</text>\r\n            </view>\r\n            <view class=\"info-item\">\r\n              <text class=\"info-label\">故障等级:</text>\r\n              <text\r\n                class=\"fault-level level-tag\"\r\n                :class=\"getFaultLevelClass(item.faultLevel)\"\r\n              >\r\n                {{ item.faultLevel }}\r\n              </text>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"workorder-footer\">\r\n            <view class=\"workorder-meta\">\r\n              <text class=\"workorder-time\">{{ item.createdTime }}</text>\r\n            </view>\r\n            <view class=\"workorder-actions\">\r\n              <view class=\"action-button\" @click.stop=\"handleAction(item, 'detail')\">查看</view>\r\n              <view\r\n                class=\"action-button primary\"\r\n                @click.stop=\"handleAction(item, 'accept')\"\r\n                v-if=\"activeTab === 'pending' && item.orderStatus === '待接单'\"\r\n              >接单</view>\r\n\t\t\t  <view\r\n\t\t\t    class=\"action-button primary\"\r\n\t\t        @click=\"handleComplete\"\r\n\t\t\t    v-if=\"activeTab === 'my' && item.orderStatus === '处理中'\"\r\n\t\t\t  >完成工单</view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 加载更多 -->\r\n        <view class=\"loading-more\" v-if=\"isLoadingMore\">\r\n          <text>正在加载更多...</text>\r\n        </view>\r\n        <view class=\"no-more\" v-if=\"hasNoMore\">\r\n          <text>没有更多数据了</text>\r\n        </view>\r\n      </template>\r\n\r\n      <!-- 空状态 -->\r\n      <view class=\"empty-state\" v-else>\r\n        <image\r\n          class=\"empty-image\"\r\n          src=\"/static/images/empty-workorder.png\"\r\n          mode=\"aspectFit\"\r\n        ></image>\r\n        <text class=\"empty-text\">{{ activeTab === 'my' ? '暂无我的工单' : '暂无待接单工单' }}</text>\r\n      <!--  <button class=\"refresh-button\" @click=\"refreshData\">刷新</button> -->\r\n      </view>\r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { workOrderApi } from \"@/utils/api.js\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 当前用户ID\r\n      userId: uni.getStorageSync('userId') || '',\r\n      // 用户项目权限\r\n      heatUnitId: uni.getStorageSync('heatUnitId') || '',\r\n      // 当前激活的标签页\r\n      activeTab: 'my', // 'my' 或 'pending'\r\n      // 列表数据\r\n      workorderList: [],\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      hasNoMore: false,\r\n      isLoadingMore: false,\r\n      isRefreshing: false,\r\n      isLoading: false,\r\n      loadError: false,\r\n\r\n      // 筛选条件显示控制\r\n      showFilterCondition: false,\r\n\r\n      // 筛选条件\r\n      filterDate: \"\",\r\n      \r\n      // 筛选条件\r\n      filters: {\r\n        startDate: \"\",\r\n        endDate: \"\",\r\n      }\r\n    };\r\n  },\r\n\r\n  onLoad() {\r\n    this.getWorkorderList();\r\n  },\r\n\r\n  // 下拉刷新\r\n  onPullDownRefresh() {\r\n    this.refreshData().then(() => {\r\n      uni.stopPullDownRefresh();\r\n    });\r\n  },\r\n\r\n  // 上拉加载更多\r\n  onReachBottom() {\r\n    console.log(\"执行上拉加载\")\r\n    this.loadMoreData();\r\n  },\r\n\r\n  methods: {\r\n    // 切换标签页\r\n    switchTab(tab) {\r\n      if (this.activeTab !== tab) {\r\n        this.activeTab = tab;\r\n        this.currentPage = 1;\r\n        this.workorderList = [];\r\n        this.hasNoMore = false;\r\n        this.getWorkorderList();\r\n      }\r\n    },\r\n    \r\n    // 加载工单列表\r\n    async getWorkorderList(isLoadMore = false) {\r\n      if (this.isLoading) return;\r\n\r\n      this.isLoading = true;\r\n      if (!isLoadMore) {\r\n        this.loadError = false;\r\n      }\r\n\r\n      try {\r\n        // 构建查询参数\r\n        const params = {\r\n          page: this.currentPage,\r\n          pageSize: this.pageSize,\r\n          uid: this.userId,\r\n          heatUnitId: this.heatUnitId // 添加用户项目权限参数\r\n        };\r\n\r\n        // 添加日期筛选\r\n        if (this.filterDate) {\r\n          params.date = this.filterDate;\r\n        }\r\n\r\n        // 根据当前标签页添加不同的查询参数\r\n        if (this.activeTab === 'my') {\r\n          params.type = 'my'; // 我的工单\r\n        } else if (this.activeTab === 'pending') {\r\n          params.type = 'pending'; // 待接单工单\r\n          params.status = '待接单'; // 强制设置状态为待接单\r\n        }\r\n\r\n        // 调用API获取工单列表\r\n        const res = await workOrderApi.getList(params);\r\n\r\n        if (res.code === 200) {\r\n          const { list, total, totalPages } = res.data;\r\n          \r\n          if (isLoadMore) {\r\n            // 加载更多模式：追加数据\r\n            this.workorderList = [...this.workorderList, ...list];\r\n          } else {\r\n            // 初始加载模式：替换数据\r\n            this.workorderList = list;\r\n          }\r\n\r\n          // 判断是否还有更多数据\r\n          this.hasNoMore = this.currentPage >= totalPages;\r\n        } else {\r\n          this.loadError = true;\r\n          uni.showToast({\r\n            title: res.message || \"获取工单列表失败\",\r\n            icon: \"none\",\r\n          });\r\n        }\r\n      } catch (err) {\r\n        console.error(\"获取工单列表异常:\", err);\r\n        this.loadError = true;\r\n        uni.showToast({\r\n          title: \"网络异常，请稍后重试\",\r\n          icon: \"none\",\r\n        });\r\n      } finally {\r\n        this.isLoading = false;\r\n        this.isLoadingMore = false;\r\n        this.isRefreshing = false;\r\n      }\r\n    },\r\n\r\n    // 加载更多数据\r\n    loadMoreData() {\r\n      if (!this.isLoadingMore && !this.hasNoMore && !this.isLoading) {\r\n        this.isLoadingMore = true;\r\n        this.currentPage++;\r\n        this.getWorkorderList(true);\r\n      }\r\n    },\r\n\r\n    // 下拉刷新\r\n    async refreshData() {\r\n      this.isRefreshing = true;\r\n      this.currentPage = 1;\r\n      this.hasNoMore = false;\r\n      this.workorderList = [];\r\n      this.filterDate=\"\";\r\n      await this.getWorkorderList(false);\r\n    },\r\n    handleComplete() {\n      // 跳转到完成工单页面\n      uni.navigateTo({\n        url: `/pages/workorder/complete?id=${this.orderId}`,\n      });\n    },\r\n    // 日期选择变更\r\n    onDateChange(e) {\r\n      this.filterDate = e.detail.value;\r\n      this.currentPage = 1;\r\n      this.workorderList = [];\r\n      this.hasNoMore = false;\r\n      this.getWorkorderList();\r\n    },\r\n\r\n    // 获取状态对应的样式类\r\n    getStatusClass(status) {\r\n      const statusClassMap = {\r\n        '待接单': 'pending',\r\n        '处理中': 'processing',\r\n        '已完成': 'completed',\r\n        '已转派': 'transferred',\r\n        '已取消': 'cancelled'\r\n      };\r\n      return statusClassMap[status] || 'default';\r\n    },\r\n    \r\n    // 获取显示的工单状态\r\n    getDisplayStatus(item) {\r\n      // 如果工单有transferUserId字段且等于当前用户的userId，显示为\"已转派\"\r\n      if (item.transferUserId && item.transferUserId === this.userId) {\r\n        return '已转派';\r\n      }\r\n      // 否则显示原始状态\r\n      return item.orderStatus;\r\n    },\r\n    \r\n    // 获取故障等级对应的样式类\r\n    getFaultLevelClass(level) {\r\n      const levelClassMap = {\r\n        提示: \"notice\",\r\n        一般: \"normal\",\r\n        重要: \"important\",\r\n        严重: \"critical\",\r\n      };\r\n      return levelClassMap[level] || \"default\";\r\n    },\r\n    \r\n    // 查看工单详情\r\n    viewOrderDetail(orderId) {\r\n      uni.navigateTo({\r\n        url: `/pages/workorder/detail?id=${orderId}`,\r\n      });\r\n    },\r\n\r\n    // 处理工单操作\r\n    handleAction(item, action) {\r\n      switch (action) {\r\n        case \"detail\":\r\n          this.viewOrderDetail(item.orderId);\r\n          break;\r\n        case \"accept\":\r\n          this.acceptOrder(item);\r\n          break;\r\n        case \"complete\":\r\n          uni.navigateTo({\r\n            url: `/pages/workorder/complete?id=${item.orderId}`,\r\n          });\r\n          break;\r\n      }\r\n    },\r\n    \r\n    // 接单操作\r\n    acceptOrder(item) {\r\n      uni.showModal({\r\n        title: '接单确认',\r\n        content: '确认接单？接单后您将负责处理此工单',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            // 显示加载中\r\n            uni.showLoading({\r\n              title: '处理中...'\r\n            });\r\n            \r\n            // 构建接单请求参数\r\n            const requestData = {\r\n              order_id: item.orderId,\r\n              repair_user_id: this.userId,\r\n              order_status: '处理中'\r\n            };\r\n            \r\n            // 调用接单API\r\n            workOrderApi.updateStatus(requestData)\r\n              .then(res => {\r\n                if (res.code === 200) {\r\n                  uni.showToast({\r\n                    title: '接单成功',\r\n                    icon: 'success'\r\n                  });\r\n                  // 刷新列表\r\n                  this.refreshData();\r\n                } else {\r\n                  uni.showToast({\r\n                    title: res.message || '接单失败',\r\n                    icon: 'none'\r\n                  });\r\n                }\r\n              })\r\n              .catch(err => {\r\n                console.error('接单失败:', err);\r\n                uni.showToast({\r\n                  title: '网络异常，请稍后重试',\r\n                  icon: 'none'\r\n                });\r\n              })\r\n              .finally(() => {\r\n                uni.hideLoading();\r\n              });\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    // 切换筛选条件区域显示/隐藏\r\n    toggleFilterCondition() {\r\n      this.showFilterCondition = !this.showFilterCondition;\r\n    },\r\n\r\n    // 日期选择\r\n    onStartDateChange(e) {\r\n      this.filters.startDate = e.detail.value;\r\n    },\r\n\r\n    // 重置筛选\r\n    resetFilters() {\r\n      this.filters = {\r\n        startDate: \"\",\r\n      };\r\n    },\r\n\r\n    // 应用筛选\r\n    applyFilters() {\r\n      this.toggleFilterCondition();\r\n      // 重置页码\r\n      this.currentPage = 1;\r\n      this.getWorkorderList();\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.workorder-list-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n/* Tab切换 */\r\n.tab-container {\r\n  display: flex;\r\n  background-color: #fff;\r\n  border-bottom: 1rpx solid #eee;\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 999;\r\n  \r\n  .tab-item {\r\n    flex: 1;\r\n    height: 80rpx;\r\n    line-height: 80rpx;\r\n    text-align: center;\r\n    font-size: 30rpx;\r\n    color: #666;\r\n    position: relative;\r\n    \r\n    &.active {\r\n      color: #007aff;\r\n      font-weight: 500;\r\n      \r\n      &::after {\r\n        content: '';\r\n        position: absolute;\r\n        bottom: 0;\r\n        left: 50%;\r\n        transform: translateX(-50%);\r\n        width: 60rpx;\r\n        height: 4rpx;\r\n        background-color: #007aff;\r\n        border-radius: 2rpx;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 顶部筛选 */\r\n.filter-section-seach {\r\n  background-color: #ffffff;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  padding: 20rpx 30rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n  position: sticky;\r\n  height:75rpx;\r\n  top: 165rpx;\r\n  z-index: 999;\r\n  \r\n\r\n  .date-filter,\r\n  .status-filter {\r\n    flex: 1;\r\n    height: 70rpx;\r\n    .date-picker,\r\n    .status-picker {\r\n      display: flex;\r\n      align-items: center;\r\n      height: 70rpx;\r\n      border: 1px solid #e5e5e5;\r\n      border-radius: 6rpx;\r\n      padding: 0 20rpx;\r\n      font-size: 28rpx;\r\n      color: #333;\r\n\r\n      .date-text,\r\n      .status-text {\r\n        flex: 1;\r\n      }\r\n\r\n      .iconfont {\r\n        margin-left: 10rpx;\r\n        color: #999;\r\n      }\r\n    }\r\n  }\r\n\r\n  .date-filter {\r\n    margin-right: 20rpx;\r\n  }\r\n  \r\n  .status-filter {\r\n    margin-right: 20rpx;\r\n  }\r\n}\r\n\r\n/* 筛选条件区域 */\r\n.filter-condition {\r\n  background-color: #fff;\r\n  margin-bottom: 20rpx;\r\n  padding: 0 30rpx 20rpx;\r\n\r\n  .condition-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 20rpx 0;\r\n    border-bottom: 1px solid #f5f5f5;\r\n    margin-bottom: 20rpx;\r\n\r\n    .condition-title {\r\n      font-size: 30rpx;\r\n      font-weight: 600;\r\n      color: #333;\r\n    }\r\n\r\n    .close-icon {\r\n      font-size: 40rpx;\r\n      color: #999;\r\n      padding: 0 10rpx;\r\n    }\r\n  }\r\n\r\n  .filter-section {\r\n    margin-bottom: 20rpx;\r\n\r\n    .section-header {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 20rpx;\r\n\r\n      .blue-bar {\r\n        width: 6rpx;\r\n        height: 30rpx;\r\n        background-color: #007aff;\r\n        margin-right: 15rpx;\r\n        border-radius: 3rpx;\r\n      }\r\n\r\n      .section-title {\r\n        font-size: 28rpx;\r\n        color: #333;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n\r\n    .filter-options {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n\r\n      .filter-option {\r\n        padding: 10rpx 24rpx;\r\n        margin-right: 20rpx;\r\n        margin-bottom: 15rpx;\r\n        font-size: 26rpx;\r\n        color: #666;\r\n        background-color: #f5f7fa;\r\n        border-radius: 30rpx;\r\n\r\n        &.active {\r\n          color: #fff;\r\n          background-color: #007aff;\r\n        }\r\n      }\r\n    }\r\n\r\n    .date-range {\r\n      .date-picker {\r\n        height: 70rpx;\r\n        background-color: #f5f7fa;\r\n        border-radius: 8rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        .date-text {\r\n          font-size: 26rpx;\r\n          color: #666;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .filter-actions {\r\n    display: flex;\r\n    margin-top: 30rpx;\r\n\r\n    button {\r\n      flex: 1;\r\n      height: 80rpx;\r\n      line-height: 80rpx;\r\n      text-align: center;\r\n      font-size: 28rpx;\r\n      border-radius: 40rpx;\r\n\r\n      &.filter-reset {\r\n        color: #666;\r\n        background-color: #f5f5f5;\r\n        margin-right: 20rpx;\r\n      }\r\n\r\n      &.filter-confirm {\r\n        color: #fff;\r\n        background-color: #007aff;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 工单列表 */\r\n.workorder-list {\r\n  .workorder-item {\r\n    margin: 20rpx;\r\n    padding: 30rpx;\r\n    background-color: #fff;\r\n    border-radius: 16rpx;\r\n    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\r\n    .workorder-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 20rpx;\r\n\r\n      .workorder-code {\r\n        font-size: 28rpx;\r\n        color: #666;\r\n      }\r\n\r\n      .status-tag {\r\n        padding: 4rpx 16rpx;\r\n        border-radius: 20rpx;\r\n        font-size: 24rpx;\r\n\r\n        &.pending {\r\n          background-color: #fff7e6;\r\n          color: #fa8c16;\r\n        }\r\n\r\n        &.processing {\r\n          background-color: #e6f7ff;\r\n          color:  #1890ff;\r\n        }\r\n\r\n        &.completed {\r\n          background-color: #f6ffed;\r\n          color: #52c41a;\r\n        }\r\n        \r\n        &.transferred {\r\n          background-color: #f2f4f8;\r\n          color: #6777ef;\r\n        }\r\n\r\n        &.cancelled {\r\n          background-color: #f5f5f5;\r\n          color: #999;\r\n        }\r\n      }\r\n    }\r\n\r\n    .workorder-info {\r\n      margin-bottom: 20rpx;\r\n       .info-item {\r\n          display: flex;\r\n          align-items: center;\r\n          margin-bottom: 10rpx;\r\n\r\n          .info-label {\r\n            font-size: 26rpx;\r\n            color: #999;\r\n            width: 150rpx;\r\n          }\r\n          .location {\r\n            font-size: 30rpx;\r\n            font-weight: 600;\r\n            color: #333;\r\n            margin-bottom: 8rpx;\r\n          }\r\n\r\n          .fault-type {\r\n            font-size: 28rpx;\r\n            color: #333;\r\n            margin-bottom: 8rpx;\r\n          }\r\n\r\n          .fault-level {\r\n            font-size: 28rpx;\r\n            color: #666;\r\n            line-height: 1.5;\r\n          }\r\n          .level-tag {\r\n            padding: 4rpx 12rpx;\r\n            border-radius: 4rpx;\r\n            font-size: 24rpx;\r\n          \r\n            &.notice {\r\n              background-color: #e6f7ff;\r\n              color: #1890ff;\r\n            }\r\n          \r\n            &.normal {\r\n              background-color: #e6f7ff;\r\n              color: #1890ff;\r\n            }\r\n          \r\n            &.important {\r\n              background-color: #fff7e6;\r\n              color: #fa8c16;\r\n            }\r\n          \r\n            &.critical {\r\n              background-color: #fff1f0;\r\n              color: #f5222d;\r\n            }\r\n          \r\n            &.default {\r\n              background-color: #f5f5f5;\r\n              color: #999;\r\n            }\r\n          }\r\n      }\r\n    }\r\n\r\n    .workorder-footer {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n\r\n      .workorder-meta {\r\n        display: flex;\r\n        flex-direction: column;\r\n\r\n        .workorder-time {\r\n          font-size: 24rpx;\r\n          color: #999;\r\n          margin-bottom: 8rpx;\r\n        }\r\n      }\r\n\r\n      .workorder-actions {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .action-button {\r\n          padding: 10rpx 24rpx;\r\n          margin-left: 20rpx;\r\n          font-size: 24rpx;\r\n          color: #666;\r\n          background-color: #f5f7fa;\r\n          border-radius: 30rpx;\r\n\r\n          &.primary {\r\n            color: #fff;\r\n            background-color: #007aff;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 加载状态 */\r\n.loading-more,\r\n.no-more {\r\n  text-align: center;\r\n  padding: 30rpx;\r\n  font-size: 26rpx;\r\n  color: #999;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 100rpx 0;\r\n\r\n  .empty-image {\r\n    width: 240rpx;\r\n    height: 240rpx;\r\n    margin-bottom: 30rpx;\r\n  }\r\n\r\n  .empty-text {\r\n    font-size: 28rpx;\r\n    color: #999;\r\n    margin-bottom: 40rpx;\r\n  }\r\n\r\n  .refresh-button {\r\n    font-size: 28rpx;\r\n    color: #007aff;\r\n    background-color: #fff;\r\n    border: 2rpx solid #007aff;\r\n    border-radius: 40rpx;\r\n    padding: 10rpx 60rpx;\r\n  }\r\n}\r\n/* 重置按钮 */\r\n.refresh-button {\r\n    font-size: 28rpx;\r\n    background-color: #fff;\r\n    border-radius: 5rpx;\r\n    align-items: center;\r\n    height: 72rpx;\r\n    border-radius: 6rpx;\r\n    padding: 0 20rpx;\r\n    font-size: 28rpx;\r\n    position: relative;\r\n  }\r\n</style>\r\n", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/workorder/list.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "workOrderApi", "res"], "mappings": ";;;;AAsJA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA;AAAA,MAEL,QAAQA,cAAG,MAAC,eAAe,QAAQ,KAAK;AAAA;AAAA,MAExC,YAAYA,cAAG,MAAC,eAAe,YAAY,KAAK;AAAA;AAAA,MAEhD,WAAW;AAAA;AAAA;AAAA,MAEX,eAAe,CAAE;AAAA,MACjB,aAAa;AAAA,MACb,UAAU;AAAA,MACV,WAAW;AAAA,MACX,eAAe;AAAA,MACf,cAAc;AAAA,MACd,WAAW;AAAA,MACX,WAAW;AAAA;AAAA,MAGX,qBAAqB;AAAA;AAAA,MAGrB,YAAY;AAAA;AAAA,MAGZ,SAAS;AAAA,QACP,WAAW;AAAA,QACX,SAAS;AAAA,MACX;AAAA;EAEH;AAAA,EAED,SAAS;AACP,SAAK,iBAAgB;AAAA,EACtB;AAAA;AAAA,EAGD,oBAAoB;AAClB,SAAK,cAAc,KAAK,MAAM;AAC5BA,oBAAG,MAAC,oBAAmB;AAAA,IACzB,CAAC;AAAA,EACF;AAAA;AAAA,EAGD,gBAAgB;AACdA,kBAAAA,MAAA,MAAA,OAAA,mCAAY,QAAQ;AACpB,SAAK,aAAY;AAAA,EAClB;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,UAAU,KAAK;AACb,UAAI,KAAK,cAAc,KAAK;AAC1B,aAAK,YAAY;AACjB,aAAK,cAAc;AACnB,aAAK,gBAAgB;AACrB,aAAK,YAAY;AACjB,aAAK,iBAAgB;AAAA,MACvB;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,iBAAiB,aAAa,OAAO;AACzC,UAAI,KAAK;AAAW;AAEpB,WAAK,YAAY;AACjB,UAAI,CAAC,YAAY;AACf,aAAK,YAAY;AAAA,MACnB;AAEA,UAAI;AAEF,cAAM,SAAS;AAAA,UACb,MAAM,KAAK;AAAA,UACX,UAAU,KAAK;AAAA,UACf,KAAK,KAAK;AAAA,UACV,YAAY,KAAK;AAAA;AAAA;AAInB,YAAI,KAAK,YAAY;AACnB,iBAAO,OAAO,KAAK;AAAA,QACrB;AAGA,YAAI,KAAK,cAAc,MAAM;AAC3B,iBAAO,OAAO;AAAA,QAChB,WAAW,KAAK,cAAc,WAAW;AACvC,iBAAO,OAAO;AACd,iBAAO,SAAS;AAAA,QAClB;AAGA,cAAM,MAAM,MAAMC,UAAAA,aAAa,QAAQ,MAAM;AAE7C,YAAI,IAAI,SAAS,KAAK;AACpB,gBAAM,EAAE,MAAM,OAAO,WAAW,IAAI,IAAI;AAExC,cAAI,YAAY;AAEd,iBAAK,gBAAgB,CAAC,GAAG,KAAK,eAAe,GAAG,IAAI;AAAA,iBAC/C;AAEL,iBAAK,gBAAgB;AAAA,UACvB;AAGA,eAAK,YAAY,KAAK,eAAe;AAAA,eAChC;AACL,eAAK,YAAY;AACjBD,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,IAAI,WAAW;AAAA,YACtB,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACA,SAAO,KAAK;AACZA,sBAAc,MAAA,MAAA,SAAA,mCAAA,aAAa,GAAG;AAC9B,aAAK,YAAY;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH,UAAU;AACR,aAAK,YAAY;AACjB,aAAK,gBAAgB;AACrB,aAAK,eAAe;AAAA,MACtB;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACb,UAAI,CAAC,KAAK,iBAAiB,CAAC,KAAK,aAAa,CAAC,KAAK,WAAW;AAC7D,aAAK,gBAAgB;AACrB,aAAK;AACL,aAAK,iBAAiB,IAAI;AAAA,MAC5B;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,cAAc;AAClB,WAAK,eAAe;AACpB,WAAK,cAAc;AACnB,WAAK,YAAY;AACjB,WAAK,gBAAgB;AACrB,WAAK,aAAW;AAChB,YAAM,KAAK,iBAAiB,KAAK;AAAA,IAClC;AAAA,IACD,iBAAiB;AAEfA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,gCAAgC,KAAK,OAAO;AAAA,MACnD,CAAC;AAAA,IACF;AAAA;AAAA,IAED,aAAa,GAAG;AACd,WAAK,aAAa,EAAE,OAAO;AAC3B,WAAK,cAAc;AACnB,WAAK,gBAAgB;AACrB,WAAK,YAAY;AACjB,WAAK,iBAAgB;AAAA,IACtB;AAAA;AAAA,IAGD,eAAe,QAAQ;AACrB,YAAM,iBAAiB;AAAA,QACrB,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA;AAET,aAAO,eAAe,MAAM,KAAK;AAAA,IAClC;AAAA;AAAA,IAGD,iBAAiB,MAAM;AAErB,UAAI,KAAK,kBAAkB,KAAK,mBAAmB,KAAK,QAAQ;AAC9D,eAAO;AAAA,MACT;AAEA,aAAO,KAAK;AAAA,IACb;AAAA;AAAA,IAGD,mBAAmB,OAAO;AACxB,YAAM,gBAAgB;AAAA,QACpB,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA;AAEN,aAAO,cAAc,KAAK,KAAK;AAAA,IAChC;AAAA;AAAA,IAGD,gBAAgB,SAAS;AACvBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,8BAA8B,OAAO;AAAA,MAC5C,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,aAAa,MAAM,QAAQ;AACzB,cAAQ,QAAM;AAAA,QACZ,KAAK;AACH,eAAK,gBAAgB,KAAK,OAAO;AACjC;AAAA,QACF,KAAK;AACH,eAAK,YAAY,IAAI;AACrB;AAAA,QACF,KAAK;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK,gCAAgC,KAAK,OAAO;AAAA,UACnD,CAAC;AACD;AAAA,MACJ;AAAA,IACD;AAAA;AAAA,IAGD,YAAY,MAAM;AAChBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEfA,0BAAAA,MAAI,YAAY;AAAA,cACd,OAAO;AAAA,YACT,CAAC;AAGD,kBAAM,cAAc;AAAA,cAClB,UAAU,KAAK;AAAA,cACf,gBAAgB,KAAK;AAAA,cACrB,cAAc;AAAA;AAIhBC,sBAAY,aAAC,aAAa,WAAW,EAClC,KAAK,CAAAC,SAAO;AACX,kBAAIA,KAAI,SAAS,KAAK;AACpBF,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACR,CAAC;AAED,qBAAK,YAAW;AAAA,qBACX;AACLA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAOE,KAAI,WAAW;AAAA,kBACtB,MAAM;AAAA,gBACR,CAAC;AAAA,cACH;AAAA,aACD,EACA,MAAM,SAAO;AACZF,4BAAc,MAAA,MAAA,SAAA,mCAAA,SAAS,GAAG;AAC1BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AAAA,aACF,EACA,QAAQ,MAAM;AACbA,4BAAG,MAAC,YAAW;AAAA,YACjB,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,wBAAwB;AACtB,WAAK,sBAAsB,CAAC,KAAK;AAAA,IAClC;AAAA;AAAA,IAGD,kBAAkB,GAAG;AACnB,WAAK,QAAQ,YAAY,EAAE,OAAO;AAAA,IACnC;AAAA;AAAA,IAGD,eAAe;AACb,WAAK,UAAU;AAAA,QACb,WAAW;AAAA;IAEd;AAAA;AAAA,IAGD,eAAe;AACb,WAAK,sBAAqB;AAE1B,WAAK,cAAc;AACnB,WAAK,iBAAgB;AAAA,IACtB;AAAA,EACF;AACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7bA,GAAG,WAAW,eAAe;"}