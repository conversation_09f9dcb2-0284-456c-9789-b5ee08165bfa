<view class="device-detail-container"><view class="detail-card"><view class="card-header"><text class="card-title">基本信息</text><view class="{{['device-status', b]}}">{{a}}</view></view><view class="info-item"><text class="info-label">设备名称</text><text class="info-value">{{c}}</text></view><view class="info-item"><text class="info-label">设备类型</text><text class="info-value">{{d}}</text></view><view class="info-item"><text class="info-label">设备型号</text><text class="info-value">{{e}}</text></view><view class="info-item"><text class="info-label">设备编号</text><text class="info-value">{{f}}</text></view><view class="info-item"><text class="info-label">安装位置</text><text class="info-value">{{g}}</text></view><view class="info-item"><text class="info-label">生产厂商</text><text class="info-value">{{h}}</text></view><view class="info-item"><text class="info-label">安装日期</text><text class="info-value">{{i}}</text></view></view><view class="detail-card"><view class="card-header"><text class="card-title">运行状态</text><text class="refresh-btn" bindtap="{{j}}">刷新</text></view><view class="status-panel"><view wx:for="{{k}}" wx:for-item="metric" wx:key="d" class="status-item"><view class="{{['metric-value', metric.b && 'alert']}}">{{metric.a}}</view><view class="metric-label">{{metric.c}}</view></view></view><view wx:if="{{l}}" class="chart-container"><view class="chart-placeholder"><text>实时数据趋势图</text></view></view></view><view wx:if="{{m}}" class="detail-card"><view class="card-header"><text class="card-title">告警信息</text><text class="view-all" bindtap="{{n}}">查看全部</text></view><view class="alarm-list"><view wx:for="{{o}}" wx:for-item="alarm" wx:key="e" class="alarm-item"><view class="{{['alarm-icon', alarm.a]}}"></view><view class="alarm-content"><view class="alarm-title">{{alarm.b}}</view><view class="alarm-desc">{{alarm.c}}</view><view class="alarm-time">{{alarm.d}}</view></view></view></view></view><view class="detail-card"><view class="card-header"><text class="card-title">维护记录</text><text class="view-all" bindtap="{{p}}">查看全部</text></view><view class="maintenance-list"><view wx:for="{{q}}" wx:for-item="record" wx:key="f" class="maintenance-item"><view class="maintenance-time"><text class="date">{{record.a}}</text><text class="time">{{record.b}}</text></view><view class="maintenance-content"><view class="maintenance-title">{{record.c}}</view><view class="maintenance-desc">{{record.d}}</view><view class="maintenance-operator">操作人：{{record.e}}</view></view></view><view wx:if="{{r}}" class="empty-list"><text>暂无维护记录</text></view></view></view><view class="action-buttons"><view class="action-btn warning" bindtap="{{s}}"><text class="iconfont icon-warning"></text><text>上报故障</text></view><view class="action-btn primary" bindtap="{{t}}"><text class="iconfont icon-maintain"></text><text>添加维护</text></view></view></view>