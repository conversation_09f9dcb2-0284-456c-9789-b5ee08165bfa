<view class="workorder-list-container"><view class="tab-container"><view class="{{['tab-item', a && 'active']}}" bindtap="{{b}}"> 我的工单 </view><view class="{{['tab-item', c && 'active']}}" bindtap="{{d}}"> 待接单 </view></view><view class="filter-section-seach"><view class="date-filter" style="flex:1"><picker mode="date" value="{{f}}" bindchange="{{g}}"><view class="date-picker"><text class="date-text">{{e}}</text></view></picker></view><button class="refresh-button" bindtap="{{h}}">重置</button></view><view wx:if="{{i}}" class="filter-condition"><view class="condition-header"><text class="condition-title">筛选条件</text><text class="close-icon" bindtap="{{j}}">×</text></view><view class="filter-section"><view class="section-header"><view class="blue-bar"></view><text class="section-title">查询日期</text></view><view class="date-range"><view class="date-picker full-width"><picker mode="date" value="{{l}}" bindchange="{{m}}"><view class="date-text">{{k}}</view></picker></view></view></view><view class="filter-actions"><button class="filter-reset" bindtap="{{n}}">重置</button><button class="filter-confirm" bindtap="{{o}}">确认</button></view></view><scroll-view scroll-y="true" class="workorder-list" style="{{'height:' + w}}" bindscrolltolower="{{x}}" lower-threshold="100" refresher-enabled="{{true}}" refresher-triggered="{{y}}" bindrefresherrefresh="{{z}}"><block wx:if="{{p}}"><view wx:for="{{q}}" wx:for-item="item" wx:key="n" class="workorder-item" bindtap="{{item.o}}"><view class="workorder-header"><text class="workorder-code">{{item.a}}</text><view class="{{['status-tag', item.c]}}">{{item.b}}</view></view><view class="workorder-info"><view class="info-item"><text class="location">{{item.d}}</text></view><view class="info-item"><text class="info-label">故障类型:</text><text class="fault-type">{{item.e}}</text></view><view class="info-item"><text class="info-label">故障等级:</text><text class="{{['fault-level', 'level-tag', item.g]}}">{{item.f}}</text></view></view><view class="workorder-footer"><view class="workorder-meta"><text class="workorder-time">{{item.h}}</text></view><view class="workorder-actions"><view class="action-button" catchtap="{{item.i}}">查看</view><view wx:if="{{item.j}}" class="action-button primary" catchtap="{{item.k}}">接单</view><view wx:if="{{item.l}}" class="action-button primary" bindtap="{{item.m}}">完成工单</view></view></view></view><view wx:if="{{r}}" class="loading-more"><text>正在加载更多...</text></view><view wx:if="{{s}}" class="no-more"><text>没有更多数据了</text></view></block><view wx:else class="empty-state"><image class="empty-image" src="{{t}}" mode="aspectFit"></image><text class="empty-text">{{v}}</text></view></scroll-view></view>