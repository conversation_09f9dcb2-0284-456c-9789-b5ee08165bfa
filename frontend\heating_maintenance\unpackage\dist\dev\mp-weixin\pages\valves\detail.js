"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      loading: true,
      loadingText: {
        contentdown: "加载中...",
        contentrefresh: "加载中...",
        contentnomore: "没有更多数据"
      },
      valveId: null,
      valveDetail: null,
      targetOpenDegree: 0,
      // 确认对话框
      confirmDialog: {
        title: "操作确认",
        content: "",
        action: ""
      },
      // 结果消息
      resultMessage: {
        type: "success",
        message: ""
      }
    };
  },
  onLoad(option) {
    if (option.id) {
      this.valveId = option.id;
      this.loadValveDetail();
    } else {
      common_vendor.index.showToast({
        title: "参数错误",
        icon: "none"
      });
      setTimeout(() => {
        this.navigateBack();
      }, 1500);
    }
  },
  methods: {
    // 加载阀门详情
    loadValveDetail() {
      this.loading = true;
      setTimeout(() => {
        this.valveDetail = {
          id: this.valveId,
          name: "东区小区 1号楼 1单元 101 入户阀门",
          status: "open",
          openDegree: 80,
          lastOperationTime: "2025-04-03 14:35:20",
          operator: "系统",
          operationType: "自动开启(缴费成功)",
          operationRecords: [
            {
              time: "2025-04-03 14:35:20",
              content: "自动开启(缴费成功)",
              operator: "系统"
            },
            {
              time: "2025-02-28 09:10:15",
              content: "手动关闭(欠费)",
              operator: "李四"
            },
            {
              time: "2025-02-15 08:30:45",
              content: "开度调整至50%",
              operator: "张三"
            }
          ]
        };
        this.targetOpenDegree = this.valveDetail.openDegree;
        this.loading = false;
      }, 1e3);
    },
    // 处理开度变化
    handleOpenDegreeChange(e) {
      this.targetOpenDegree = e.detail.value;
    },
    // 设置开度
    setOpenDegree() {
      if (this.targetOpenDegree === this.valveDetail.openDegree) {
        common_vendor.index.showToast({
          title: "开度未变化",
          icon: "none"
        });
        return;
      }
      this.confirmDialog = {
        title: "开度调整确认",
        content: `确定要将${this.valveDetail.name}的开度调整为${this.targetOpenDegree}%吗？`,
        action: "setOpenDegree"
      };
      this.$refs.confirmPopup.open();
    },
    // 一键开启
    fullOpen() {
      if (this.valveDetail.status === "open" && this.valveDetail.openDegree === 100) {
        common_vendor.index.showToast({
          title: "阀门已经完全开启",
          icon: "none"
        });
        return;
      }
      this.confirmDialog = {
        title: "操作确认",
        content: `确定要完全开启${this.valveDetail.name}吗？`,
        action: "fullOpen"
      };
      this.$refs.confirmPopup.open();
    },
    // 一键关闭
    fullClose() {
      if (this.valveDetail.status === "closed" && this.valveDetail.openDegree === 0) {
        common_vendor.index.showToast({
          title: "阀门已经完全关闭",
          icon: "none"
        });
        return;
      }
      this.confirmDialog = {
        title: "操作确认",
        content: `确定要完全关闭${this.valveDetail.name}吗？`,
        action: "fullClose"
      };
      this.$refs.confirmPopup.open();
    },
    // 确认控制操作
    handleConfirmControl() {
      const action = this.confirmDialog.action;
      ({
        valve_id: this.valveId
      });
      if (action === "setOpenDegree") {
        this.targetOpenDegree;
      }
      common_vendor.index.showLoading({
        title: "操作中..."
      });
      setTimeout(() => {
        if (action === "setOpenDegree") {
          this.valveDetail.openDegree = this.targetOpenDegree;
          this.valveDetail.status = this.targetOpenDegree > 0 ? "open" : "closed";
          this.addOperationRecord(`开度调整至${this.targetOpenDegree}%`);
        } else if (action === "fullOpen") {
          this.valveDetail.openDegree = 100;
          this.valveDetail.status = "open";
          this.targetOpenDegree = 100;
          this.addOperationRecord("完全开启阀门");
        } else if (action === "fullClose") {
          this.valveDetail.openDegree = 0;
          this.valveDetail.status = "closed";
          this.targetOpenDegree = 0;
          this.addOperationRecord("完全关闭阀门");
        }
        common_vendor.index.hideLoading();
        this.resultMessage = {
          type: "success",
          message: "操作成功"
        };
        this.$refs.resultPopup.open();
      }, 1500);
    },
    // 取消控制操作
    handleCancelControl() {
    },
    // 添加操作记录
    addOperationRecord(content) {
      const now = /* @__PURE__ */ new Date();
      const timeStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, "0")}-${String(now.getDate()).padStart(2, "0")} ${String(now.getHours()).padStart(2, "0")}:${String(now.getMinutes()).padStart(2, "0")}:${String(now.getSeconds()).padStart(2, "0")}`;
      this.valveDetail.operationRecords.unshift({
        time: timeStr,
        content,
        operator: "当前用户"
        // 实际应用中应该从用户信息中获取
      });
      this.valveDetail.lastOperationTime = timeStr;
      this.valveDetail.operator = "当前用户";
      this.valveDetail.operationType = content;
    },
    // 获取状态图标
    getStatusIcon(status) {
      const iconMap = {
        "open": "icon-check",
        "closed": "icon-clear",
        "error": "icon-alarm"
      };
      return iconMap[status] || "icon-info";
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        "open": "已开启",
        "closed": "已关闭",
        "error": "异常"
      };
      return statusMap[status] || status;
    },
    // 返回上一页
    navigateBack() {
      common_vendor.index.navigateBack();
    }
  }
};
if (!Array) {
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  const _easycom_uni_popup_dialog2 = common_vendor.resolveComponent("uni-popup-dialog");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  const _easycom_uni_popup_message2 = common_vendor.resolveComponent("uni-popup-message");
  (_easycom_uni_load_more2 + _easycom_uni_popup_dialog2 + _easycom_uni_popup2 + _easycom_uni_popup_message2)();
}
const _easycom_uni_load_more = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-load-more/uni-load-more.js";
const _easycom_uni_popup_dialog = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-popup-dialog/uni-popup-dialog.js";
const _easycom_uni_popup = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-popup/uni-popup.js";
const _easycom_uni_popup_message = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-popup-message/uni-popup-message.js";
if (!Math) {
  (_easycom_uni_load_more + _easycom_uni_popup_dialog + _easycom_uni_popup + _easycom_uni_popup_message)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.navigateBack && $options.navigateBack(...args)),
    b: $data.loading
  }, $data.loading ? {
    c: common_vendor.p({
      status: "loading",
      ["content-text"]: $data.loadingText
    })
  } : $data.valveDetail ? {
    e: common_vendor.n($options.getStatusIcon($data.valveDetail.status)),
    f: common_vendor.n($data.valveDetail.status),
    g: common_vendor.t($data.valveDetail.name),
    h: common_vendor.t($options.getStatusText($data.valveDetail.status)),
    i: common_vendor.n("status-" + $data.valveDetail.status),
    j: common_vendor.t($data.valveDetail.openDegree),
    k: common_vendor.t($data.valveDetail.lastOperationTime),
    l: common_vendor.t($data.valveDetail.operationType),
    m: common_vendor.t($data.valveDetail.operator),
    n: common_vendor.t($data.targetOpenDegree),
    o: $data.targetOpenDegree,
    p: common_vendor.o((...args) => $options.handleOpenDegreeChange && $options.handleOpenDegreeChange(...args)),
    q: common_vendor.o((...args) => $options.setOpenDegree && $options.setOpenDegree(...args)),
    r: common_vendor.o((...args) => $options.fullOpen && $options.fullOpen(...args)),
    s: common_vendor.o((...args) => $options.fullClose && $options.fullClose(...args)),
    t: common_vendor.f($data.valveDetail.operationRecords, (record, index, i0) => {
      return {
        a: common_vendor.t(record.time),
        b: common_vendor.t(record.content),
        c: common_vendor.t(record.operator),
        d: index
      };
    })
  } : {}, {
    d: $data.valveDetail,
    v: common_vendor.o($options.handleConfirmControl),
    w: common_vendor.o($options.handleCancelControl),
    x: common_vendor.p({
      title: $data.confirmDialog.title,
      content: $data.confirmDialog.content,
      cancelText: "取消",
      confirmText: "确认"
    }),
    y: common_vendor.sr("confirmPopup", "63d56152-1"),
    z: common_vendor.p({
      type: "dialog"
    }),
    A: common_vendor.p({
      type: $data.resultMessage.type,
      message: $data.resultMessage.message,
      duration: 2e3
    }),
    B: common_vendor.sr("resultPopup", "63d56152-3"),
    C: common_vendor.p({
      type: "message"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/valves/detail.js.map
