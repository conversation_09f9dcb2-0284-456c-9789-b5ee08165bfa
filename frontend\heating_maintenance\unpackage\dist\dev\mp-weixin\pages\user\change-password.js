"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const _sfc_main = {
  data() {
    return {
      password: {
        current: "",
        new: "",
        confirm: ""
      },
      isSubmitting: false
    };
  },
  computed: {
    // 检查密码长度
    checkLength() {
      return this.password.new.length >= 8;
    },
    // 检查是否包含数字
    checkNumber() {
      return /\d/.test(this.password.new);
    },
    // 检查是否包含字母
    checkLetter() {
      return /[a-zA-Z]/.test(this.password.new);
    },
    // 检查是否包含特殊字符
    checkSpecial() {
      return /[^a-zA-Z0-9]/.test(this.password.new);
    },
    // 表单是否有效
    isFormValid() {
      return this.password.current && this.password.new && this.password.confirm && this.password.new === this.password.confirm && this.checkLength && this.checkNumber && this.checkLetter;
    }
  },
  methods: {
    // 修改密码
    changePassword() {
      if (!this.isFormValid) {
        return;
      }
      if (this.isSubmitting) {
        return;
      }
      this.isSubmitting = true;
      common_vendor.index.showLoading({
        title: "处理中..."
      });
      const passwordData = {
        oldPassword: this.password.current,
        newPassword: this.password.new
      };
      utils_api.userApi.changePassword(passwordData).then((res) => {
        common_vendor.index.hideLoading();
        this.isSubmitting = false;
        if (res.code === 200) {
          common_vendor.index.showModal({
            title: "修改成功",
            content: "密码修改成功，请使用新密码重新登录",
            showCancel: false,
            success: () => {
              common_vendor.index.removeStorageSync("token");
              common_vendor.index.removeStorageSync("userInfo");
              common_vendor.index.removeStorageSync("userId");
              common_vendor.index.removeStorageSync("userRole");
              common_vendor.index.removeStorageSync("userPermissions");
              common_vendor.index.reLaunch({
                url: "/pages/user/login"
              });
            }
          });
        } else {
          this.showError(res.message || "密码修改失败");
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        this.isSubmitting = false;
        common_vendor.index.__f__("error", "at pages/user/change-password.vue:151", "密码修改请求失败:", err);
        this.showError("网络错误，请稍后重试");
      });
    },
    // 显示错误提示
    showError(message) {
      common_vendor.index.showToast({
        title: message,
        icon: "none"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.password.current,
    b: common_vendor.o(($event) => $data.password.current = $event.detail.value),
    c: $data.password.new,
    d: common_vendor.o(($event) => $data.password.new = $event.detail.value),
    e: $options.checkLength ? 1 : "",
    f: $options.checkNumber ? 1 : "",
    g: $options.checkLetter ? 1 : "",
    h: $options.checkSpecial ? 1 : "",
    i: $data.password.confirm,
    j: common_vendor.o(($event) => $data.password.confirm = $event.detail.value),
    k: $data.password.new && $data.password.confirm && $data.password.new !== $data.password.confirm
  }, $data.password.new && $data.password.confirm && $data.password.new !== $data.password.confirm ? {} : {}, {
    l: !$options.isFormValid ? 1 : "",
    m: common_vendor.o((...args) => $options.changePassword && $options.changePassword(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/user/change-password.js.map
