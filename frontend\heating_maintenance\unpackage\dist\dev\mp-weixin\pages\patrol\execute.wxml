<view class="patrol-execute-container"><view wx:if="{{a}}" class="loading-container"><view class="loading-spinner"></view><text class="loading-text">正在加载巡检任务...</text></view><view wx:else><view class="progress-bar"><view class="progress-inner" style="{{'width:' + b}}"></view></view><view class="progress-info"><text>{{c}}/巡检项目总数：{{d}}</text><text>{{e}}%</text></view><view class="task-card"><view class="task-header"><text class="task-title">{{f}}</text></view><view class="task-details"><view class="detail-row"><text class="detail-label">巡检对象</text><text class="detail-value">{{g}}</text></view><view class="detail-row"><text class="detail-label">巡检类型</text><text class="detail-value">{{h}}</text></view><view class="detail-row"><text class="detail-label">巡检内容</text><text class="detail-value content-description">{{i}}</text></view><view class="detail-row"><text class="detail-label">检测方法</text><text class="detail-value">{{j}}</text></view><view wx:if="{{k}}" class="detail-row"><text class="detail-label">单位</text><text class="detail-value">{{l}}</text></view><view class="detail-row"><text class="detail-label">重要性</text><text class="{{['detail-value', 'importance-tag', n]}}">{{m}}</text></view></view></view><view class="input-card"><view class="card-header"><text>实际值</text><text wx:if="{{o}}" class="unit-text">{{p}}</text></view><view class="card-content"><input wx:if="{{q}}" type="text" class="form-input" placeholder="请输入数值" value="{{r}}" bindinput="{{s}}"/><input wx:else class="form-input" placeholder="请输入实际值" value="{{t}}" bindinput="{{v}}"/></view><view class="card-header" style="margin-top:30rpx"><text>检查结果</text></view><view class="card-content"><view class="radio-group"><view class="{{['radio-item', w && 'radio-selected']}}" bindtap="{{x}}"><view class="radio-dot"></view><text>正常</text></view><view class="{{['radio-item', y && 'radio-selected']}}" bindtap="{{z}}"><view class="radio-dot"></view><text>异常</text></view></view></view></view><view class="input-card"><view class="card-header"><text>备注</text></view><view class="card-content"><block wx:if="{{r0}}"><textarea class="form-textarea" placeholder="请输入备注信息（选填）" value="{{A}}" bindinput="{{B}}"></textarea></block></view></view><view class="input-card"><view class="card-header"><text>现场照片</text></view><view class="card-content"><view class="image-list"><view wx:for="{{C}}" wx:for-item="image" wx:key="d" class="image-item"><image src="{{image.a}}" mode="aspectFill" bindtap="{{image.b}}"></image><view class="delete-icon" bindtap="{{image.c}}">×</view></view><view wx:if="{{D}}" class="image-upload" bindtap="{{E}}"><text class="upload-icon">+</text><text class="upload-text">上传照片</text></view></view></view></view><view class="action-buttons"><view wx:if="{{F}}" class="action-btn prev" bindtap="{{G}}"><text>上一项</text></view><view wx:if="{{H}}" class="action-btn skip" bindtap="{{I}}"><text>跳过</text></view><view class="action-btn submit" bindtap="{{K}}"><text>{{J}}</text></view></view></view><uni-popup wx:if="{{O}}" class="r" u-s="{{['d']}}" u-r="skipPopup" u-i="a661c770-0" bind:__l="__l" u-p="{{O}}"><view class="popup-content"><view class="popup-title">确认跳过</view><view class="popup-message">确定要跳过当前巡检项吗？</view><view class="popup-buttons"><view class="popup-button cancel" bindtap="{{L}}">取消</view><view class="popup-button confirm" bindtap="{{M}}">确认</view></view></view></uni-popup><uni-popup wx:if="{{S}}" class="r" u-s="{{['d']}}" u-r="completePopup" u-i="a661c770-1" bind:__l="__l" u-p="{{S}}"><view class="popup-content"><view class="popup-title">巡检完成</view><view class="popup-message">所有巡检项已完成，是否提交？</view><view class="popup-buttons"><view class="popup-button cancel" bindtap="{{P}}">继续检查</view><view class="popup-button confirm" bindtap="{{Q}}">确认提交</view></view></view></uni-popup></view>