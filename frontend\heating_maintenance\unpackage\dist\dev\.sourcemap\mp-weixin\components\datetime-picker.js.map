{"version": 3, "file": "datetime-picker.js", "sources": ["components/datetime-picker.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/Y29tcG9uZW50cy9kYXRldGltZS1waWNrZXIudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"datetime-picker-container\">\r\n    <view class=\"header\">\r\n      <text class=\"title\">{{ title }}</text>\r\n      <text class=\"close-btn\" @click=\"cancel\">取消</text>\r\n    </view>\r\n    \r\n    <view class=\"picker-body\">\r\n      <picker-view class=\"picker\" :indicator-style=\"indicatorStyle\" :value=\"pickerValue\" @change=\"bindChange\">\r\n        <picker-view-column>\r\n          <view class=\"picker-item\" v-for=\"(item, index) in years\" :key=\"'year-'+index\">{{ item }}年</view>\r\n        </picker-view-column>\r\n        <picker-view-column>\r\n          <view class=\"picker-item\" v-for=\"(item, index) in months\" :key=\"'month-'+index\">{{ item }}月</view>\r\n        </picker-view-column>\r\n        <picker-view-column>\r\n          <view class=\"picker-item\" v-for=\"(item, index) in days\" :key=\"'day-'+index\">{{ item }}日</view>\r\n        </picker-view-column>\r\n        <picker-view-column>\r\n          <view class=\"picker-item\" v-for=\"(item, index) in hours\" :key=\"'hour-'+index\">{{ item }}时</view>\r\n        </picker-view-column>\r\n        <picker-view-column>\r\n          <view class=\"picker-item\" v-for=\"(item, index) in minutes\" :key=\"'minute-'+index\">{{ item }}分</view>\r\n        </picker-view-column>\r\n        <picker-view-column>\r\n          <view class=\"picker-item\" v-for=\"(item, index) in seconds\" :key=\"'second-'+index\">{{ item }}秒</view>\r\n        </picker-view-column>\r\n      </picker-view>\r\n    </view>\r\n    \r\n    <view class=\"btn-area\">\r\n      <button class=\"confirm-btn\" @click=\"confirm\">确定</button>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      title: '选择日期时间',\r\n      indicatorStyle: 'height: 50px;',\r\n      pickerValue: [0, 0, 0, 0, 0, 0],\r\n      years: [],\r\n      months: [],\r\n      days: [],\r\n      hours: [],\r\n      minutes: [],\r\n      seconds: [],\r\n      year: 2020,\r\n      month: 1,\r\n      day: 1,\r\n      hour: 0,\r\n      minute: 0,\r\n      second: 0,\r\n      hasInit: false\r\n    };\r\n  },\r\n  onLoad(option) {\r\n    if (option.params) {\r\n      const params = JSON.parse(decodeURIComponent(option.params));\r\n      if (params.title) {\r\n        this.title = params.title;\r\n      }\r\n      \r\n      if (params.current) {\r\n        this.initPickerFromDateTime(params.current);\r\n      } else {\r\n        this.initPicker();\r\n      }\r\n    } else {\r\n      this.initPicker();\r\n    }\r\n  },\r\n  methods: {\r\n    // 初始化选择器数据\r\n    initPicker() {\r\n      const date = new Date();\r\n      this.year = date.getFullYear();\r\n      this.month = date.getMonth() + 1;\r\n      this.day = date.getDate();\r\n      this.hour = date.getHours();\r\n      this.minute = date.getMinutes();\r\n      this.second = date.getSeconds();\r\n      \r\n      // 生成年份数据，当前年份前后10年\r\n      this.years = [];\r\n      for (let i = this.year - 10; i <= this.year + 10; i++) {\r\n        this.years.push(i);\r\n      }\r\n      \r\n      // 生成月份数据\r\n      this.months = [];\r\n      for (let i = 1; i <= 12; i++) {\r\n        this.months.push(i);\r\n      }\r\n      \r\n      this.updateDays();\r\n      \r\n      // 生成小时数据\r\n      this.hours = [];\r\n      for (let i = 0; i <= 23; i++) {\r\n        this.hours.push(i);\r\n      }\r\n      \r\n      // 生成分钟和秒数据\r\n      this.minutes = [];\r\n      this.seconds = [];\r\n      for (let i = 0; i <= 59; i++) {\r\n        this.minutes.push(i);\r\n        this.seconds.push(i);\r\n      }\r\n      \r\n      // 设置当前值\r\n      this.updatePickerValue();\r\n      this.hasInit = true;\r\n    },\r\n    \r\n    // 根据日期时间字符串初始化选择器\r\n    initPickerFromDateTime(dateTimeStr) {\r\n      const date = new Date(dateTimeStr);\r\n      if (isNaN(date.getTime())) {\r\n        this.initPicker();\r\n        return;\r\n      }\r\n      \r\n      this.year = date.getFullYear();\r\n      this.month = date.getMonth() + 1;\r\n      this.day = date.getDate();\r\n      this.hour = date.getHours();\r\n      this.minute = date.getMinutes();\r\n      this.second = date.getSeconds();\r\n      \r\n      // 生成年份数据，当前年份前后10年\r\n      this.years = [];\r\n      for (let i = this.year - 10; i <= this.year + 10; i++) {\r\n        this.years.push(i);\r\n      }\r\n      \r\n      // 生成月份数据\r\n      this.months = [];\r\n      for (let i = 1; i <= 12; i++) {\r\n        this.months.push(i);\r\n      }\r\n      \r\n      this.updateDays();\r\n      \r\n      // 生成小时数据\r\n      this.hours = [];\r\n      for (let i = 0; i <= 23; i++) {\r\n        this.hours.push(i);\r\n      }\r\n      \r\n      // 生成分钟和秒数据\r\n      this.minutes = [];\r\n      this.seconds = [];\r\n      for (let i = 0; i <= 59; i++) {\r\n        this.minutes.push(i);\r\n        this.seconds.push(i);\r\n      }\r\n      \r\n      // 设置当前值\r\n      this.updatePickerValue();\r\n      this.hasInit = true;\r\n    },\r\n    \r\n    // 更新天数，根据年月确定\r\n    updateDays() {\r\n      let daysInMonth = 31;\r\n      \r\n      // 计算当月天数\r\n      if (this.month === 2) {\r\n        // 闰年2月29天，平年28天\r\n        daysInMonth = (this.year % 4 === 0 && this.year % 100 !== 0) || this.year % 400 === 0 ? 29 : 28;\r\n      } else if ([4, 6, 9, 11].includes(this.month)) {\r\n        daysInMonth = 30;\r\n      }\r\n      \r\n      this.days = [];\r\n      for (let i = 1; i <= daysInMonth; i++) {\r\n        this.days.push(i);\r\n      }\r\n      \r\n      // 如果当前选择的日期超出了当月的最大天数，则调整为当月最后一天\r\n      if (this.day > daysInMonth) {\r\n        this.day = daysInMonth;\r\n      }\r\n    },\r\n    \r\n    // 更新pickerValue\r\n    updatePickerValue() {\r\n      const yearIndex = this.years.indexOf(this.year);\r\n      const monthIndex = this.months.indexOf(this.month);\r\n      const dayIndex = this.days.indexOf(this.day);\r\n      const hourIndex = this.hours.indexOf(this.hour);\r\n      const minuteIndex = this.minutes.indexOf(this.minute);\r\n      const secondIndex = this.seconds.indexOf(this.second);\r\n      \r\n      this.pickerValue = [\r\n        yearIndex !== -1 ? yearIndex : 0,\r\n        monthIndex !== -1 ? monthIndex : 0,\r\n        dayIndex !== -1 ? dayIndex : 0,\r\n        hourIndex !== -1 ? hourIndex : 0,\r\n        minuteIndex !== -1 ? minuteIndex : 0,\r\n        secondIndex !== -1 ? secondIndex : 0\r\n      ];\r\n    },\r\n    \r\n    // 当picker值发生变化时的处理函数\r\n    bindChange(e) {\r\n      const val = e.detail.value;\r\n      \r\n      // 更新选中的值\r\n      this.year = this.years[val[0]];\r\n      this.month = this.months[val[1]];\r\n      \r\n      // 更新天数列表\r\n      this.updateDays();\r\n      \r\n      // 如果当前选中的日期超出了更新后的天数范围，则重置为1号\r\n      if (val[2] >= this.days.length) {\r\n        val[2] = 0;\r\n      }\r\n      \r\n      this.day = this.days[val[2]];\r\n      this.hour = this.hours[val[3]];\r\n      this.minute = this.minutes[val[4]];\r\n      this.second = this.seconds[val[5]];\r\n    },\r\n    \r\n    // 确认选择\r\n    confirm() {\r\n      // 格式化日期时间为字符串\r\n      const datetime = this.formatDateTime();\r\n      \r\n      // 将选择结果返回给调用页面\r\n      const eventChannel = this.getOpenerEventChannel();\r\n      eventChannel.emit('dateTimeSelected', {\r\n        datetime: datetime\r\n      });\r\n      \r\n      // 返回上一页\r\n      uni.navigateBack();\r\n    },\r\n    \r\n    // 取消选择\r\n    cancel() {\r\n      uni.navigateBack();\r\n    },\r\n    \r\n    // 格式化日期时间\r\n    formatDateTime() {\r\n      const year = this.year;\r\n      const month = String(this.month).padStart(2, '0');\r\n      const day = String(this.day).padStart(2, '0');\r\n      const hour = String(this.hour).padStart(2, '0');\r\n      const minute = String(this.minute).padStart(2, '0');\r\n      const second = String(this.second).padStart(2, '0');\r\n      \r\n      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style>\r\n.datetime-picker-container {\r\n  background-color: #fff;\r\n  height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20rpx 30rpx;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.title {\r\n  font-size: 32rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.close-btn {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n}\r\n\r\n.picker-body {\r\n  flex: 1;\r\n  padding: 20rpx 0;\r\n}\r\n\r\n.picker {\r\n  width: 100%;\r\n  height: 400rpx;\r\n}\r\n\r\n.picker-item {\r\n  line-height: 50px;\r\n  text-align: center;\r\n}\r\n\r\n.btn-area {\r\n  padding: 30rpx;\r\n}\r\n\r\n.confirm-btn {\r\n  width: 100%;\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n  background-color: #1890ff;\r\n  color: #fff;\r\n  border-radius: 8rpx;\r\n}\r\n</style> ", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/components/datetime-picker.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAqCA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,MAC9B,OAAO,CAAE;AAAA,MACT,QAAQ,CAAE;AAAA,MACV,MAAM,CAAE;AAAA,MACR,OAAO,CAAE;AAAA,MACT,SAAS,CAAE;AAAA,MACX,SAAS,CAAE;AAAA,MACX,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,MACL,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS;AAAA;EAEZ;AAAA,EACD,OAAO,QAAQ;AACb,QAAI,OAAO,QAAQ;AACjB,YAAM,SAAS,KAAK,MAAM,mBAAmB,OAAO,MAAM,CAAC;AAC3D,UAAI,OAAO,OAAO;AAChB,aAAK,QAAQ,OAAO;AAAA,MACtB;AAEA,UAAI,OAAO,SAAS;AAClB,aAAK,uBAAuB,OAAO,OAAO;AAAA,aACrC;AACL,aAAK,WAAU;AAAA,MACjB;AAAA,WACK;AACL,WAAK,WAAU;AAAA,IACjB;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,aAAa;AACX,YAAM,OAAO,oBAAI;AACjB,WAAK,OAAO,KAAK;AACjB,WAAK,QAAQ,KAAK,SAAQ,IAAK;AAC/B,WAAK,MAAM,KAAK;AAChB,WAAK,OAAO,KAAK;AACjB,WAAK,SAAS,KAAK;AACnB,WAAK,SAAS,KAAK;AAGnB,WAAK,QAAQ;AACb,eAAS,IAAI,KAAK,OAAO,IAAI,KAAK,KAAK,OAAO,IAAI,KAAK;AACrD,aAAK,MAAM,KAAK,CAAC;AAAA,MACnB;AAGA,WAAK,SAAS;AACd,eAAS,IAAI,GAAG,KAAK,IAAI,KAAK;AAC5B,aAAK,OAAO,KAAK,CAAC;AAAA,MACpB;AAEA,WAAK,WAAU;AAGf,WAAK,QAAQ;AACb,eAAS,IAAI,GAAG,KAAK,IAAI,KAAK;AAC5B,aAAK,MAAM,KAAK,CAAC;AAAA,MACnB;AAGA,WAAK,UAAU;AACf,WAAK,UAAU;AACf,eAAS,IAAI,GAAG,KAAK,IAAI,KAAK;AAC5B,aAAK,QAAQ,KAAK,CAAC;AACnB,aAAK,QAAQ,KAAK,CAAC;AAAA,MACrB;AAGA,WAAK,kBAAiB;AACtB,WAAK,UAAU;AAAA,IAChB;AAAA;AAAA,IAGD,uBAAuB,aAAa;AAClC,YAAM,OAAO,IAAI,KAAK,WAAW;AACjC,UAAI,MAAM,KAAK,QAAO,CAAE,GAAG;AACzB,aAAK,WAAU;AACf;AAAA,MACF;AAEA,WAAK,OAAO,KAAK;AACjB,WAAK,QAAQ,KAAK,SAAQ,IAAK;AAC/B,WAAK,MAAM,KAAK;AAChB,WAAK,OAAO,KAAK;AACjB,WAAK,SAAS,KAAK;AACnB,WAAK,SAAS,KAAK;AAGnB,WAAK,QAAQ;AACb,eAAS,IAAI,KAAK,OAAO,IAAI,KAAK,KAAK,OAAO,IAAI,KAAK;AACrD,aAAK,MAAM,KAAK,CAAC;AAAA,MACnB;AAGA,WAAK,SAAS;AACd,eAAS,IAAI,GAAG,KAAK,IAAI,KAAK;AAC5B,aAAK,OAAO,KAAK,CAAC;AAAA,MACpB;AAEA,WAAK,WAAU;AAGf,WAAK,QAAQ;AACb,eAAS,IAAI,GAAG,KAAK,IAAI,KAAK;AAC5B,aAAK,MAAM,KAAK,CAAC;AAAA,MACnB;AAGA,WAAK,UAAU;AACf,WAAK,UAAU;AACf,eAAS,IAAI,GAAG,KAAK,IAAI,KAAK;AAC5B,aAAK,QAAQ,KAAK,CAAC;AACnB,aAAK,QAAQ,KAAK,CAAC;AAAA,MACrB;AAGA,WAAK,kBAAiB;AACtB,WAAK,UAAU;AAAA,IAChB;AAAA;AAAA,IAGD,aAAa;AACX,UAAI,cAAc;AAGlB,UAAI,KAAK,UAAU,GAAG;AAEpB,sBAAe,KAAK,OAAO,MAAM,KAAK,KAAK,OAAO,QAAQ,KAAM,KAAK,OAAO,QAAQ,IAAI,KAAK;AAAA,MAC/F,WAAW,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,SAAS,KAAK,KAAK,GAAG;AAC7C,sBAAc;AAAA,MAChB;AAEA,WAAK,OAAO;AACZ,eAAS,IAAI,GAAG,KAAK,aAAa,KAAK;AACrC,aAAK,KAAK,KAAK,CAAC;AAAA,MAClB;AAGA,UAAI,KAAK,MAAM,aAAa;AAC1B,aAAK,MAAM;AAAA,MACb;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB;AAClB,YAAM,YAAY,KAAK,MAAM,QAAQ,KAAK,IAAI;AAC9C,YAAM,aAAa,KAAK,OAAO,QAAQ,KAAK,KAAK;AACjD,YAAM,WAAW,KAAK,KAAK,QAAQ,KAAK,GAAG;AAC3C,YAAM,YAAY,KAAK,MAAM,QAAQ,KAAK,IAAI;AAC9C,YAAM,cAAc,KAAK,QAAQ,QAAQ,KAAK,MAAM;AACpD,YAAM,cAAc,KAAK,QAAQ,QAAQ,KAAK,MAAM;AAEpD,WAAK,cAAc;AAAA,QACjB,cAAc,KAAK,YAAY;AAAA,QAC/B,eAAe,KAAK,aAAa;AAAA,QACjC,aAAa,KAAK,WAAW;AAAA,QAC7B,cAAc,KAAK,YAAY;AAAA,QAC/B,gBAAgB,KAAK,cAAc;AAAA,QACnC,gBAAgB,KAAK,cAAc;AAAA;IAEtC;AAAA;AAAA,IAGD,WAAW,GAAG;AACZ,YAAM,MAAM,EAAE,OAAO;AAGrB,WAAK,OAAO,KAAK,MAAM,IAAI,CAAC,CAAC;AAC7B,WAAK,QAAQ,KAAK,OAAO,IAAI,CAAC,CAAC;AAG/B,WAAK,WAAU;AAGf,UAAI,IAAI,CAAC,KAAK,KAAK,KAAK,QAAQ;AAC9B,YAAI,CAAC,IAAI;AAAA,MACX;AAEA,WAAK,MAAM,KAAK,KAAK,IAAI,CAAC,CAAC;AAC3B,WAAK,OAAO,KAAK,MAAM,IAAI,CAAC,CAAC;AAC7B,WAAK,SAAS,KAAK,QAAQ,IAAI,CAAC,CAAC;AACjC,WAAK,SAAS,KAAK,QAAQ,IAAI,CAAC,CAAC;AAAA,IAClC;AAAA;AAAA,IAGD,UAAU;AAER,YAAM,WAAW,KAAK;AAGtB,YAAM,eAAe,KAAK;AAC1B,mBAAa,KAAK,oBAAoB;AAAA,QACpC;AAAA,MACF,CAAC;AAGDA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA;AAAA,IAGD,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA;AAAA,IAGD,iBAAiB;AACf,YAAM,OAAO,KAAK;AAClB,YAAM,QAAQ,OAAO,KAAK,KAAK,EAAE,SAAS,GAAG,GAAG;AAChD,YAAM,MAAM,OAAO,KAAK,GAAG,EAAE,SAAS,GAAG,GAAG;AAC5C,YAAM,OAAO,OAAO,KAAK,IAAI,EAAE,SAAS,GAAG,GAAG;AAC9C,YAAM,SAAS,OAAO,KAAK,MAAM,EAAE,SAAS,GAAG,GAAG;AAClD,YAAM,SAAS,OAAO,KAAK,MAAM,EAAE,SAAS,GAAG,GAAG;AAElD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM,IAAI,MAAM;AAAA,IAC5D;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrQA,GAAG,WAAW,eAAe;"}