{"version": 3, "file": "execute.js", "sources": ["pages/patrol/execute.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcGF0cm9sL2V4ZWN1dGUudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"patrol-execute-container\">\r\n\t\t<!-- 加载状态 -->\r\n\t\t<view class=\"loading-container\" v-if=\"isLoading\">\r\n\t\t\t<view class=\"loading-spinner\"></view>\r\n\t\t\t<text class=\"loading-text\">正在加载巡检任务...</text>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view v-else>\r\n\t\t\t<!-- 顶部进度条 -->\r\n\t\t\t<view class=\"progress-bar\">\r\n\t\t\t\t<view class=\"progress-inner\" :style=\"{ width: progressPercent + '%' }\"></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"progress-info\">\r\n\t\t\t\t<text>{{ currentTaskIndex + 1 }}/巡检项目总数：{{ planTasks.length }}</text>\r\n\t\t\t\t<text>{{ progressPercent }}%</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 当前任务内容 -->\r\n\t\t\t<view class=\"task-card\">\r\n\t\t\t\t<view class=\"task-header\">\r\n\t\t\t\t\t<text class=\"task-title\">{{ currentTask.itemName }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"task-details\">\r\n\t\t\t\t\t<view class=\"detail-row\">\r\n\t\t\t\t\t\t<text class=\"detail-label\">巡检对象</text>\r\n\t\t\t\t\t\t<text class=\"detail-value\">{{ currentTask.deviceName }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail-row\">\r\n\t\t\t\t\t\t<text class=\"detail-label\">巡检类型</text>\r\n\t\t\t\t\t\t<text class=\"detail-value\">{{ currentTask.categoryName }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail-row\">\r\n\t\t\t\t\t\t<text class=\"detail-label\">巡检内容</text>\r\n\t\t\t\t\t\t<text class=\"detail-value content-description\">{{ currentTask.description }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail-row\">\r\n\t\t\t\t\t\t<text class=\"detail-label\">检测方法</text>\r\n\t\t\t\t\t\t<text class=\"detail-value\">{{ currentTask.checkMethod }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail-row\" v-if=\"currentTask.unit\">\r\n\t\t\t\t\t\t<text class=\"detail-label\">单位</text>\r\n\t\t\t\t\t\t<text class=\"detail-value\">{{ currentTask.unit }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail-row\">\r\n\t\t\t\t\t\t<text class=\"detail-label\">重要性</text>\r\n\t\t\t\t\t\t<text class=\"detail-value importance-tag\" :class=\"getImportanceClass(currentTask.importance)\">\r\n\t\t\t\t\t\t\t{{ getImportanceLabel(currentTask.importance) }}\r\n\t\t\t\t\t\t</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 巡检结果录入 -->\r\n\t\t\t<view class=\"input-card\">\r\n\t\t\t\t<view class=\"card-header\">\r\n\t\t\t\t\t<text>实际值</text>\r\n\t\t\t\t\t<text v-if=\"currentTask.paramType === 'numeric'\" class=\"unit-text\">{{currentTask.unit}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t<input v-if=\"currentTask.paramType === 'numeric' || currentTask.paramType === 'selection' \" \r\n\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\tclass=\"form-input\" \r\n\t\t\t\t\t\tv-model=\"taskResult.actualValue\" \r\n\t\t\t\t\t\tplaceholder=\"请输入数值\" />\r\n<!-- \t\t\t\t\t<picker v-else-if=\"currentTask.paramType === 'selection'\" \r\n\t\t\t\t\t\t:value=\"taskResult.actualValue\" \r\n\t\t\t\t\t\t:range=\"currentTask.options || []\" \r\n\t\t\t\t\t\t@change=\"onSelectionChange\">\r\n\t\t\t\t\t\t<view class=\"picker-value\">\r\n\t\t\t\t\t\t\t{{taskResult.actualValue || '请选择'}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</picker> -->\r\n\t\t\t        <input v-else class=\"form-input\" v-model=\"taskResult.actualValue\" placeholder=\"请输入实际值\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card-header\" style=\"margin-top: 30rpx;\">\r\n\t\t\t\t\t<text>检查结果</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t\t<view class=\"radio-item\" :class=\"{'radio-selected': taskResult.checkResult === 'normal'}\" @click=\"onCheckResultChange('normal')\">\r\n\t\t\t\t\t\t\t<view class=\"radio-dot\"></view>\r\n\t\t\t\t\t\t\t<text>正常</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"radio-item\" :class=\"{'radio-selected': taskResult.checkResult === 'abnormal'}\" @click=\"onCheckResultChange('abnormal')\">\r\n\t\t\t\t\t\t\t<view class=\"radio-dot\"></view>\r\n\t\t\t\t\t\t\t<text>异常</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\r\n\t\t\t<view class=\"input-card\">\r\n\t\t\t\t<view class=\"card-header\">\r\n\t\t\t\t\t<text>备注</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t<textarea class=\"form-textarea\" v-model=\"taskResult.remark\" placeholder=\"请输入备注信息（选填）\"></textarea>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"input-card\">\r\n\t\t\t\t<view class=\"card-header\">\r\n\t\t\t\t\t<text>现场照片</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t<view class=\"image-list\">\r\n\t\t\t\t\t\t<view class=\"image-item\" v-for=\"(image, index) in taskResult.images\" :key=\"index\">\r\n\t\t\t\t\t\t\t<image :src=\"image.url\" mode=\"aspectFill\" @click=\"previewImage(image.url)\"></image>\r\n\t\t\t\t\t\t\t<view class=\"delete-icon\" @click=\"deleteImage(index)\">×</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"image-upload\" @click=\"uploadImage\" v-if=\"taskResult.images.length < 3\">\r\n\t\t\t\t\t\t\t<text class=\"upload-icon\">+</text>\r\n\t\t\t\t\t\t\t<text class=\"upload-text\">上传照片</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 底部操作按钮 -->\r\n\t\t\t<view class=\"action-buttons\">\r\n\t\t\t\t<view class=\"action-btn prev\" @click=\"prevTask\" v-if=\"currentTaskIndex > 0\">\r\n\t\t\t\t\t<text>上一项</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"action-btn skip\" @click=\"skipTask\" v-if=\"currentTaskIndex < planTasks.length - 1\">\r\n\t\t\t\t\t<text>跳过</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"action-btn submit\" @click=\"submitTask\">\r\n\t\t\t\t\t<text>{{ currentTaskIndex < planTasks.length - 1 ? '下一项' : '完成' }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 确认跳过弹窗 -->\r\n\t\t<uni-popup ref=\"skipPopup\" type=\"center\">\r\n\t\t\t<view class=\"popup-content\">\r\n\t\t\t\t<view class=\"popup-title\">确认跳过</view>\r\n\t\t\t\t<view class=\"popup-message\">确定要跳过当前巡检项吗？</view>\r\n\t\t\t\t<view class=\"popup-buttons\">\r\n\t\t\t\t\t<view class=\"popup-button cancel\" @click=\"cancelSkip\">取消</view>\r\n\t\t\t\t\t<view class=\"popup-button confirm\" @click=\"confirmSkip\">确认</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t\t\r\n\t\t<!-- 确认完成弹窗 -->\r\n\t\t<uni-popup ref=\"completePopup\" type=\"center\">\r\n\t\t\t<view class=\"popup-content\">\r\n\t\t\t\t<view class=\"popup-title\">巡检完成</view>\r\n\t\t\t\t<view class=\"popup-message\">所有巡检项已完成，是否提交？</view>\r\n\t\t\t\t<view class=\"popup-buttons\">\r\n\t\t\t\t\t<view class=\"popup-button cancel\" @click=\"cancelComplete\">继续检查</view>\r\n\t\t\t\t\t<view class=\"popup-button confirm\" @click=\"confirmComplete\">确认提交</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\r\n\timport { patrolApi } from '@/utils/api.js';\r\n\timport uploadUtils from '@/utils/upload.js';\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tplanId: '',\r\n\t\t\t\tplanTasks: [],\r\n\t\t\t\tcurrentTaskIndex: 0,\r\n\t\t\t\ttaskResult: {\r\n\t\t\t\t\tactualValue: '',\r\n\t\t\t\t\tcheckResult: 'normal',\r\n\t\t\t\t\tremark: '',\r\n\t\t\t\t\timages: []\r\n\t\t\t\t},\r\n\t\t\t\tisLoading: false,\r\n\t\t\t\tisSkipping: false,\r\n\t\t\t\tisCompleting: false,\r\n\t\t\t\tcompletedResults: [] // 存储所有已完成的巡检结果\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tcurrentTask() {\r\n\t\t\t\treturn this.planTasks[this.currentTaskIndex] || {};\r\n\t\t\t},\r\n\t\t\tprogressPercent() {\r\n\t\t\t\tif (!this.planTasks.length) return 0;\r\n\t\t\t\tconst completedTasks = this.planTasks.filter(task => task.status === 'completed').length;\r\n\t\t\t\treturn Math.floor((completedTasks / this.planTasks.length) * 100);\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tthis.planId = options.planId;\r\n\t\t\tthis.id = options.id;\r\n\t\t\tconsole.log(\"记录id=\",options.id)\r\n\t\t\tconsole.log(\"计划id=\",options.planId)\r\n\t\t\tthis.loadPlanTasks();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 格式化日期时间为 YYYY-MM-DD HH:MM:SS\r\n\t\t\tformatDateTime(dateStr) {\r\n\t\t\t\tconst date = new Date(dateStr);\r\n\t\t\t\tconst year = date.getFullYear();\r\n\t\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\r\n\t\t\t\tconst day = String(date.getDate()).padStart(2, '0');\r\n\t\t\t\tconst hours = String(date.getHours()).padStart(2, '0');\r\n\t\t\t\tconst minutes = String(date.getMinutes()).padStart(2, '0');\r\n\t\t\t\tconst seconds = String(date.getSeconds()).padStart(2, '0');\r\n\t\t\t\t\r\n\t\t\t\treturn `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 加载巡检任务列表\r\n\t\t\tloadPlanTasks() {\r\n\t\t\t\tthis.isLoading = true; \r\n\t\t\t\t// 显示加载中提示\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '加载中...'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\tpatrolApi.getItemList({\r\n\t\t\t\t\tplanId: this.planId\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.data && Array.isArray(res.data)) {\r\n\t\t\t\t\t\t// 处理API返回的数据，添加状态字段\r\n\t\t\t\t\t\tthis.planTasks = res.data.map(item => ({\r\n\t\t\t\t\t\t\t...item,\r\n\t\t\t\t\t\t\tstatus: 'pending', // 初始状态都设为待处理\r\n\t\t\t\t\t\t\tcompletedTime: null\r\n\t\t\t\t\t\t}));\r\n\t\t\t\t\t\tthis.findNextPendingTask();\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '获取数据格式错误',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tconsole.warn('获取巡检任务列表失败', err);\r\n\t\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t// 显示错误提示\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '加载任务失败，请重试',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}); \r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 查找下一个待处理任务\r\n\t\t\tfindNextPendingTask() {\r\n\t\t\t\tconst pendingTaskIndex = this.planTasks.findIndex(task => task.status === 'pending');\r\n\t\t\t\tif (pendingTaskIndex !== -1) {\r\n\t\t\t\t\tthis.currentTaskIndex = pendingTaskIndex;\r\n\t\t\t\t\tthis.resetTaskResult();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 重置当前任务的录入结果\r\n\t\t\tresetTaskResult() {\r\n\t\t\t\tthis.taskResult = {\r\n\t\t\t\t\tactualValue: '',\r\n\t\t\t\t\tcheckResult: 'normal',\r\n\t\t\t\t\tremark: '',\r\n\t\t\t\t\timages: []\r\n\t\t\t\t};\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 选择图片\r\n\t\t\tuploadImage() {\r\n\t\t\t\tuni.chooseImage({\r\n\t\t\t\t\tcount: 3 - this.taskResult.images.length,\r\n\t\t\t\t\tsizeType: ['compressed'],\r\n\t\t\t\t\tsourceType: ['album', 'camera'],\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t// 显示上传中提示\r\n\t\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\t\ttitle: '上传中...',\r\n\t\t\t\t\t\t\tmask: true\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 检查token是否存在，不存在则提示用户登录\r\n\t\t\t\t\t\tconst token = uni.getStorageSync('token');\r\n\t\t\t\t\t\tif (!token) {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '请先登录',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 逐个上传图片\r\n\t\t\t\t\t\tconst uploadPromises = res.tempFilePaths.map(path => {\r\n\t\t\t\t\t\t\treturn uploadUtils.uploadImage(path)\r\n\t\t\t\t\t\t\t\t.then(serverPath => {\r\n\t\t\t\t\t\t\t\t\t// 保存本地路径和服务器路径\r\n\t\t\t\t\t\t\t\t\tthis.taskResult.images.push({\r\n\t\t\t\t\t\t\t\t\t\turl: path,\r\n\t\t\t\t\t\t\t\t\t\tserverUrl: serverPath\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\treturn serverPath;\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 等待所有图片上传完成\r\n\t\t\t\t\t\tPromise.all(uploadPromises)\r\n\t\t\t\t\t\t\t.then(() => {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '上传成功',\r\n\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: err.message || '上传失败',\r\n\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 删除图片\r\n\t\t\tdeleteImage(index) {\r\n\t\t\t\tthis.taskResult.images.splice(index, 1);\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 预览图片\r\n\t\t\tpreviewImage(url) {\r\n\t\t\t\tconst previewUrls = this.taskResult.images.map(img => img.url);\r\n\t\t\t\t\r\n\t\t\t\tuni.previewImage({\r\n\t\t\t\t\turls: previewUrls,\r\n\t\t\t\t\tcurrent: url\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 上一个任务\r\n\t\t\tprevTask() {\r\n\t\t\t\tif (this.currentTaskIndex > 0) {\r\n\t\t\t\t\t// 先保存当前任务的输入值到临时存储\r\n\t\t\t\t\tthis.saveCurrentInputToTemp();\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 切换到上一个任务\r\n\t\t\t\t\tthis.currentTaskIndex--;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 恢复上一个任务的输入值\r\n\t\t\t\t\tthis.restoreInputFromTemp();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 跳过任务\r\n\t\t\tskipTask() {\r\n\t\t\t\tthis.isSkipping = true;\r\n\t\t\t\tthis.$refs.skipPopup.open();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 取消跳过\r\n\t\t\tcancelSkip() {\r\n\t\t\t\tthis.$refs.skipPopup.close();\r\n\t\t\t\tthis.isSkipping = false;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 确认跳过\r\n\t\t\tconfirmSkip() {\r\n\t\t\t\tthis.$refs.skipPopup.close();\r\n\t\t\t\tthis.isSkipping = false;\r\n\t\t\t\t\r\n\t\t\t\t// 显示加载中\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '保存中...'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 创建跳过的任务结果\r\n\t\t\t\tconst skippedResult = {\r\n\t\t\t\t\titemId: this.currentTask.id,\r\n\t\t\t\t\tactualValue: '',\r\n\t\t\t\t\tcheckResult: 'normal',\r\n\t\t\t\t\tremark: '已跳过',\r\n\t\t\t\t\timages: [],\r\n\t\t\t\t\tskipped: true\r\n\t\t\t\t};\r\n\t\t\t\t\r\n\t\t\t\t// 更新当前任务状态\r\n\t\t\t\tthis.planTasks[this.currentTaskIndex] = {\r\n\t\t\t\t\t...this.planTasks[this.currentTaskIndex],\r\n\t\t\t\t\tstatus: 'skipped',\r\n\t\t\t\t\tskippedTime: this.formatDateTime(new Date().toISOString())\r\n\t\t\t\t};\r\n\t\t\t\t\r\n\t\t\t\t// 保存到完成结果数组，标记为跳过\r\n\t\t\t\tconst existingIndex = this.completedResults.findIndex(item => item.itemId === skippedResult.itemId);\r\n\t\t\t\tif (existingIndex !== -1) {\r\n\t\t\t\t\tthis.completedResults[existingIndex] = skippedResult;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.completedResults.push(skippedResult);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\r\n\t\t\t\t// 提示\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '已跳过此项',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 前进到下一个任务\r\n\t\t\t\tif (this.currentTaskIndex < this.planTasks.length - 1) {\r\n\t\t\t\t\tthis.currentTaskIndex++;\r\n\t\t\t\t\tthis.resetTaskResult();\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果是最后一个任务，询问是否完成整个巡检\r\n\t\t\t\t\tthis.isCompleting = true;\r\n\t\t\t\t\tthis.$refs.completePopup.open();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 提交任务\r\n\t\t\tsubmitTask() {\r\n\t\t\t\t// 验证输入\r\n\t\t\t\tif (!this.taskResult.actualValue) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入实际值',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 显示提交中\r\n\t\t\t\t// uni.showLoading({\r\n\t\t\t\t// \ttitle: '保存中...'\r\n\t\t\t\t// });\r\n\t\t\t\t\r\n\t\t\t\t// 处理图片数据，提取服务器路径\r\n\t\t\t\tconst processedImages = this.taskResult.images.map(img => ({\r\n\t\t\t\t\turl: img.url,\r\n\t\t\t\t\tserverUrl: img.serverUrl\r\n\t\t\t\t}));\r\n\t\t\t\t\r\n\t\t\t\t// 保存当前任务结果\r\n\t\t\t\tconst currentResult = {\r\n\t\t\t\t\titemId: this.currentTask.id,\r\n\t\t\t\t\tactualValue: this.taskResult.actualValue,\r\n\t\t\t\t\tcheckResult: this.taskResult.checkResult,\r\n\t\t\t\t\tremark: this.taskResult.remark || '',\r\n\t\t\t\t\timages: processedImages || []\r\n\t\t\t\t};\r\n\t\t\t\t\r\n\t\t\t\t// 更新当前任务状态\r\n\t\t\t\tthis.planTasks[this.currentTaskIndex] = {\r\n\t\t\t\t\t...this.planTasks[this.currentTaskIndex],\r\n\t\t\t\t\tactualValue: this.taskResult.actualValue,\r\n\t\t\t\t\tcheckResult: this.taskResult.checkResult,\r\n\t\t\t\t\tremark: this.taskResult.remark,\r\n\t\t\t\t\timages: processedImages,\r\n\t\t\t\t\tstatus: 'completed',\r\n\t\t\t\t\tcompletedTime: this.formatDateTime(new Date().toISOString())\r\n\t\t\t\t};\r\n\t\t\t\t\r\n\t\t\t\t// 保存到完成结果数组中\r\n\t\t\t\t// 检查是否已存在，如果存在则更新，否则添加\r\n\t\t\t\tconst existingIndex = this.completedResults.findIndex(item => item.itemId === currentResult.itemId);\r\n\t\t\t\tif (existingIndex !== -1) {\r\n\t\t\t\t\tthis.completedResults[existingIndex] = currentResult;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.completedResults.push(currentResult);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\t// uni.showToast({\r\n\t\t\t\t// \ttitle: '保存成功',\r\n\t\t\t\t// \ticon: 'success'\r\n\t\t\t\t// });\r\n\t\t\t\t\r\n\t\t\t\tif (this.currentTaskIndex < this.planTasks.length - 1) {\r\n\t\t\t\t\t// 如果还有下一个任务，前进到下一个\r\n\t\t\t\t\t// 保存当前任务的输入值到临时存储\r\n\t\t\t\t\tthis.saveCurrentInputToTemp();\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.currentTaskIndex++;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 恢复下一个任务的输入值\r\n\t\t\t\t\tthis.restoreInputFromTemp();\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果是最后一个任务，询问是否完成整个巡检\r\n\t\t\t\t\tthis.isCompleting = true;\r\n\t\t\t\t\tthis.$refs.completePopup.open();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 保存当前输入到临时存储\r\n\t\t\tsaveCurrentInputToTemp() {\r\n\t\t\t\t// 为当前任务创建或更新临时存储\r\n\t\t\t\tconst tempData = {\r\n\t\t\t\t\titemId: this.currentTask.id,\r\n\t\t\t\t\tactualValue: this.taskResult.actualValue,\r\n\t\t\t\t\tcheckResult: this.taskResult.checkResult,\r\n\t\t\t\t\tremark: this.taskResult.remark,\r\n\t\t\t\t\timages: this.taskResult.images\r\n\t\t\t\t};\r\n\t\t\t\t\r\n\t\t\t\t// 检查是否已有此任务的临时数据\r\n\t\t\t\tconst taskIndex = this.planTasks.findIndex(task => task.id === this.currentTask.id);\r\n\t\t\t\tif (taskIndex !== -1) {\r\n\t\t\t\t\t// 将临时数据存储到任务对象中\r\n\t\t\t\t\tthis.planTasks[taskIndex].tempData = tempData;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 从临时存储恢复输入\r\n\t\t\trestoreInputFromTemp() {\r\n\t\t\t\t// 重置当前任务的录入结果\r\n\t\t\t\tthis.resetTaskResult();\r\n\t\t\t\t\r\n\t\t\t\tconst currentTask = this.planTasks[this.currentTaskIndex];\r\n\t\t\t\t\r\n\t\t\t\t// 如果任务已完成，填充已有数据\r\n\t\t\t\tif (currentTask.status === 'completed') {\r\n\t\t\t\t\tthis.taskResult.actualValue = currentTask.actualValue || '';\r\n\t\t\t\t\tthis.taskResult.checkResult = currentTask.checkResult || 'normal';\r\n\t\t\t\t\tthis.taskResult.remark = currentTask.remark || '';\r\n\t\t\t\t\tthis.taskResult.images = currentTask.images || [];\r\n\t\t\t\t} \r\n\t\t\t\t// 如果有临时保存的数据，恢复临时数据\r\n\t\t\t\telse if (currentTask.tempData) {\r\n\t\t\t\t\tthis.taskResult.actualValue = currentTask.tempData.actualValue || '';\r\n\t\t\t\t\tthis.taskResult.checkResult = currentTask.tempData.checkResult || 'normal';\r\n\t\t\t\t\tthis.taskResult.remark = currentTask.tempData.remark || '';\r\n\t\t\t\t\tthis.taskResult.images = currentTask.tempData.images || [];\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 取消完成\r\n\t\t\tcancelComplete() {\r\n\t\t\t\tthis.isCompleting = false;\r\n\t\t\t\tthis.$refs.completePopup.close();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 确认完成\r\n\t\t\tconfirmComplete() {\r\n\t\t\t\tthis.$refs.completePopup.close();\r\n\t\t\t\t\r\n\t\t\t\t// 显示加载中\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '提交中...'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 检查是否有未完成的任务\r\n\t\t\t\tconst pendingTasks = this.planTasks.filter(task => task.status === 'pending');\r\n\t\t\t\tif (pendingTasks.length > 0) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\tcontent: `还有${pendingTasks.length}个巡检项未完成，是否继续提交？`,\r\n\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\tthis.submitAllResults();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthis.submitAllResults();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 提交所有巡检结果\r\n\t\t\tsubmitAllResults() {\r\n\t\t\t\t// 显示加载中\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '提交中...'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 获取开始时间（第一个完成的任务时间或当前时间）\r\n\t\t\t\tconst startTimeISO = this.planTasks[0]?.completedTime || new Date().toISOString();\r\n\t\t\t\tconst endTimeISO = new Date().toISOString();\r\n\t\t\t\t\r\n\t\t\t\t// 构建提交数据\r\n\t\t\t\tconst submitData = {\r\n\t\t\t\t\tpatrol_plan_id: this.planId,\r\n\t\t\t\t\tid:this.id,\r\n\t\t\t\t\texecutor_id: uni.getStorageSync('userId') || 1, // 从缓存获取当前登录用户ID，如果没有则默认为1\r\n\t\t\t\t\tstart_time: this.formatDateTime(startTimeISO), // 格式化为 YYYY-MM-DD HH:MM:SS\r\n\t\t\t\t\tend_time: this.formatDateTime(endTimeISO), // 格式化为 YYYY-MM-DD HH:MM:SS\r\n\t\t\t\t\tstatus: \"completed\",\r\n\t\t\t\t\tremark: \"巡检完成\",\r\n\t\t\t\t\tpatrol_results: this.completedResults.map(item => {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tpatrol_item_id: item.itemId,\r\n\t\t\t\t\t\t\tcheck_result: item.checkResult === 'abnormal' ? 'abnormal' : 'normal',\r\n\t\t\t\t\t\t\tparam_value: item.actualValue,\r\n\t\t\t\t\t\t\tdescription: item.remark || '',\r\n\t\t\t\t\t\t\timages: item.images.map(img => img.serverUrl || img.url), // 优先使用服务器路径\r\n\t\t\t\t\t\t\tlatitude: 0, // 此处可以添加定位功能获取实际经纬度\r\n\t\t\t\t\t\t\tlongitude: 0\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t})\r\n\t\t\t\t};\r\n\t\t\t\t// uni.hideLoading();\r\n\t\t\t\t// setTimeout(() => {\r\n\t\t\t\t// \tuni.navigateBack({\r\n\t\t\t\t// \t    delta: 2\r\n\t\t\t\t// \t});王Zeui-/\r\n\t\t\t\t\t\t\t// }, 1500);\r\n\t\t\t\t// 调用API提交所有结果\r\n\t\t\t\tpatrolApi.submitPatrolRecord(submitData)\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '巡检已完成',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t// 跳转到巡检工单记录页面\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\t\t\t    delta: 2\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}, 1500);\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tconsole.error('提交巡检记录失败:', err);\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '提交失败，请重试',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取重要性类\r\n\t\t\tgetImportanceClass(importance) {\r\n\t\t\t\tswitch (importance) {\r\n\t\t\t\t\tcase 'normal':\r\n\t\t\t\t\t\treturn 'normal-importance';\r\n\t\t\t\t\tcase 'important':\r\n\t\t\t\t\t\treturn 'important-importance';\r\n\t\t\t\t\tcase 'critical':\r\n\t\t\t\t\t\treturn 'critical-importance';\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t\t\treturn 'normal-importance';\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取重要性标签\r\n\t\t\tgetImportanceLabel(importance) {\r\n\t\t\t\tswitch (importance) {\r\n\t\t\t\t\tcase 'normal':\r\n\t\t\t\t\t\treturn '普通';\r\n\t\t\t\t\tcase 'important':\r\n\t\t\t\t\t\treturn '重要';\r\n\t\t\t\t\tcase 'critical':\r\n\t\t\t\t\t\treturn '关键';\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t\t\treturn '普通';\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 选择类型输入\r\n\t\t\tonSelectionChange(e) {\r\n\t\t\t\tthis.taskResult.actualValue = e.detail.value;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 检查结果输入\r\n\t\t\tonCheckResultChange(result) {\r\n\t\t\t\tthis.taskResult.checkResult = result;\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.patrol-execute-container {\r\n\t\tpadding: 20rpx;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tmin-height: 100vh;\r\n\t\tpadding-bottom: 120rpx;\r\n\t}\r\n\t\r\n\t/* 加载状态样式 */\r\n\t.loading-container {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\theight: 100vh;\r\n\t\tbackground-color: #f5f5f5;\r\n\t}\r\n\t\r\n\t.loading-spinner {\r\n\t\twidth: 80rpx;\r\n\t\theight: 80rpx;\r\n\t\tborder: 6rpx solid rgba(0, 122, 255, 0.1);\r\n\t\tborder-top-color: #007aff;\r\n\t\tborder-radius: 50%;\r\n\t\tanimation: spin 1s infinite linear;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\t\r\n\t.loading-text {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\t\r\n\t@keyframes spin {\r\n\t\t0% {\r\n\t\t\ttransform: rotate(0deg);\r\n\t\t}\r\n\t\t100% {\r\n\t\t\ttransform: rotate(360deg);\r\n\t\t}\r\n\t}\r\n\t\r\n\t.progress-bar {\r\n\t\theight: 10rpx;\r\n\t\tbackground-color: #eee;\r\n\t\tborder-radius: 5rpx;\r\n\t\toverflow: hidden;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\t\r\n\t\t.progress-inner {\r\n\t\t\theight: 100%;\r\n\t\t\tbackground-color: $uni-color-primary;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.progress-info {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: $uni-text-color-grey;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t\r\n\t.task-card {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 12rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\r\n\t\t\r\n\t\t.task-header {\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\r\n\t\t\t.task-title {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.task-details {\r\n\t\t\t.detail-row {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tmargin-bottom: 16rpx;\r\n\t\t\t\t\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tmargin-bottom: 0;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.detail-label {\r\n\t\t\t\t\twidth: 120rpx;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.detail-value {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.importance-tag {\r\n\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\tpadding: 4rpx 16rpx;\r\n\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tmax-width: fit-content;\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.normal-importance {\r\n\t\t\t\t\t\tbackground-color: #e6f7ff;\r\n\t\t\t\t\t\tcolor: #1890ff;\r\n\t\t\t\t\t\tborder: 1rpx solid #91d5ff;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.important-importance {\r\n\t\t\t\t\t\tbackground-color: #fff7e6;\r\n\t\t\t\t\t\tcolor: #fa8c16;\r\n\t\t\t\t\t\tborder: 1rpx solid #ffd591;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.critical-importance {\r\n\t\t\t\t\t\tbackground-color: #fff1f0;\r\n\t\t\t\t\t\tcolor: #f5222d;\r\n\t\t\t\t\t\tborder: 1rpx solid #ffa39e;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.input-card {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 12rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\r\n\t\t\r\n\t\t.card-header {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tcolor: $uni-text-color;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.card-content {\r\n\t\t\t.form-input {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 80rpx;\r\n\t\t\t\tpadding: 0 20rpx;\r\n\t\t\t\tborder: 1rpx solid #ddd;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.picker-value {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 80rpx;\r\n\t\t\t\tline-height: 80rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.radio-group {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\t\r\n\t\t\t\t.radio-item {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tmargin-right: 60rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.radio-dot {\r\n\t\t\t\t\t\twidth: 32rpx;\r\n\t\t\t\t\t\theight: 32rpx;\r\n\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\tborder: 1rpx solid #ddd;\r\n\t\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&::after {\r\n\t\t\t\t\t\t\tcontent: '';\r\n\t\t\t\t\t\t\twidth: 16rpx;\r\n\t\t\t\t\t\t\theight: 16rpx;\r\n\t\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\t\t\t\topacity: 0;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\ttext {\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.radio-selected {\r\n\t\t\t\t\t\t.radio-dot {\r\n\t\t\t\t\t\t\tborder-color: $uni-color-primary;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t&::after {\r\n\t\t\t\t\t\t\t\topacity: 1;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.form-textarea {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 160rpx;\r\n\t\t\t\tpadding: 20rpx;\r\n\t\t\t\tborder: 1rpx solid #ddd;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.image-list {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-wrap: wrap;\r\n\t\t\t\t\r\n\t\t\t\t.image-item {\r\n\t\t\t\t\twidth: 160rpx;\r\n\t\t\t\t\theight: 160rpx;\r\n\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\r\n\t\t\t\t\timage {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tobject-fit: cover;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.delete-icon {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\twidth: 40rpx;\r\n\t\t\t\t\t\theight: 40rpx;\r\n\t\t\t\t\t\tbackground-color: rgba(0,0,0,0.5);\r\n\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tborder-bottom-left-radius: 8rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.image-upload {\r\n\t\t\t\t\twidth: 160rpx;\r\n\t\t\t\t\theight: 160rpx;\r\n\t\t\t\t\tborder: 1rpx dashed #ddd;\r\n\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.upload-icon {\r\n\t\t\t\t\t\tfont-size: 48rpx;\r\n\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.upload-text {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.action-buttons {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tpadding: 20rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tdisplay: flex;\r\n\t\tbox-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);\r\n\t\t\r\n\t\t.action-btn {\r\n\t\t\tflex: 1;\r\n\t\t\theight: 80rpx;\r\n\t\t\tline-height: 80rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tmargin: 0 10rpx;\r\n\t\t\t\r\n\t\t\t&.prev {\r\n\t\t\t\tbackground-color: #f5f5f5;\r\n\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.skip {\r\n\t\t\t\tbackground-color: #f5f5f5;\r\n\t\t\t\tcolor: $uni-color-warning;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.submit {\r\n\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.popup-content {\r\n\t\twidth: 560rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 12rpx;\r\n\t\toverflow: hidden;\r\n\t\t\r\n\t\t.popup-title {\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tpadding: 30rpx 0;\r\n\t\t}\r\n\t\t\r\n\t\t.popup-message {\r\n\t\t\tpadding: 0 30rpx 30rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: $uni-text-color;\r\n\t\t}\r\n\t\t\r\n\t\t.popup-buttons {\r\n\t\t\tdisplay: flex;\r\n\t\t\tborder-top: 1rpx solid #eee;\r\n\t\t\t\r\n\t\t\t.popup-button {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\theight: 90rpx;\r\n\t\t\t\tline-height: 90rpx;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\r\n\t\t\t\t&.cancel {\r\n\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t\tborder-right: 1rpx solid #eee;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&.confirm {\r\n\t\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.content-description {\r\n\t\tword-break: break-word;\r\n\t\twhite-space: normal;\r\n\t}\r\n\t\r\n\ttextarea {\r\n\t\twidth: 100%;\r\n\t\theight: 160rpx;\r\n\t\tpadding: 20rpx;\r\n\t\tborder: 1rpx solid #ddd;\r\n\t\tborder-radius: 8rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\t\r\n\t.unit-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t\tmargin-left: 10rpx;\r\n\t}\r\n</style> ", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/patrol/execute.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "patrolApi", "uploadUtils"], "mappings": ";;;;AAqKC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,QAAQ;AAAA,MACR,WAAW,CAAE;AAAA,MACb,kBAAkB;AAAA,MAClB,YAAY;AAAA,QACX,aAAa;AAAA,QACb,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,QAAQ,CAAC;AAAA,MACT;AAAA,MACD,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,kBAAkB,CAAG;AAAA;AAAA,IACtB;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,cAAc;AACb,aAAO,KAAK,UAAU,KAAK,gBAAgB,KAAK,CAAA;AAAA,IAChD;AAAA,IACD,kBAAkB;AACjB,UAAI,CAAC,KAAK,UAAU;AAAQ,eAAO;AACnC,YAAM,iBAAiB,KAAK,UAAU,OAAO,UAAQ,KAAK,WAAW,WAAW,EAAE;AAClF,aAAO,KAAK,MAAO,iBAAiB,KAAK,UAAU,SAAU,GAAG;AAAA,IACjE;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AACf,SAAK,SAAS,QAAQ;AACtB,SAAK,KAAK,QAAQ;AAClBA,kBAAY,MAAA,MAAA,OAAA,mCAAA,SAAQ,QAAQ,EAAE;AAC9BA,kBAAA,MAAA,MAAA,OAAA,mCAAY,SAAQ,QAAQ,MAAM;AAClC,SAAK,cAAa;AAAA,EAClB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,eAAe,SAAS;AACvB,YAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,YAAM,OAAO,KAAK;AAClB,YAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,YAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,YAAM,QAAQ,OAAO,KAAK,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AACrD,YAAM,UAAU,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACzD,YAAM,UAAU,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AAEzD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,OAAO,IAAI,OAAO;AAAA,IAC7D;AAAA;AAAA,IAGD,gBAAgB;AACf,WAAK,YAAY;AAEjBA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACR,CAAC;AAEDC,gBAAAA,UAAU,YAAY;AAAA,QACrB,QAAQ,KAAK;AAAA,OACb,EAAE,KAAK,SAAO;AACd,YAAI,IAAI,QAAQ,MAAM,QAAQ,IAAI,IAAI,GAAG;AAExC,eAAK,YAAY,IAAI,KAAK,IAAI,WAAS;AAAA,YACtC,GAAG;AAAA,YACH,QAAQ;AAAA;AAAA,YACR,eAAe;AAAA,UACf,EAAC;AACF,eAAK,oBAAmB;AAAA,eAClB;AACND,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AACA,aAAK,YAAY;AACjBA,sBAAG,MAAC,YAAW;AAAA,MAChB,CAAC,EAAE,MAAM,SAAO;AACfA,sBAAA,MAAA,MAAA,QAAA,mCAAa,cAAc,GAAG;AAC9B,aAAK,YAAY;AACjBA,sBAAG,MAAC,YAAW;AAEfA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,sBAAsB;AACrB,YAAM,mBAAmB,KAAK,UAAU,UAAU,UAAQ,KAAK,WAAW,SAAS;AACnF,UAAI,qBAAqB,IAAI;AAC5B,aAAK,mBAAmB;AACxB,aAAK,gBAAe;AAAA,MACrB;AAAA,IACA;AAAA;AAAA,IAGD,kBAAkB;AACjB,WAAK,aAAa;AAAA,QACjB,aAAa;AAAA,QACb,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,QAAQ,CAAC;AAAA;IAEV;AAAA;AAAA,IAGD,cAAc;AACbA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO,IAAI,KAAK,WAAW,OAAO;AAAA,QAClC,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AAEjBA,wBAAAA,MAAI,YAAY;AAAA,YACf,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAGD,gBAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,cAAI,CAAC,OAAO;AACXA,0BAAG,MAAC,YAAW;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACP,CAAC;AACD;AAAA,UACD;AAGA,gBAAM,iBAAiB,IAAI,cAAc,IAAI,UAAQ;AACpD,mBAAOE,aAAW,YAAC,YAAY,IAAI,EACjC,KAAK,gBAAc;AAEnB,mBAAK,WAAW,OAAO,KAAK;AAAA,gBAC3B,KAAK;AAAA,gBACL,WAAW;AAAA,cACZ,CAAC;AACD,qBAAO;AAAA,YACR,CAAC;AAAA,UACH,CAAC;AAGD,kBAAQ,IAAI,cAAc,EACxB,KAAK,MAAM;AACXF,0BAAG,MAAC,YAAW;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACP,CAAC;AAAA,WACD,EACA,MAAM,SAAO;AACbA,0BAAG,MAAC,YAAW;AACfA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO,IAAI,WAAW;AAAA,cACtB,MAAM;AAAA,YACP,CAAC;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,YAAY,OAAO;AAClB,WAAK,WAAW,OAAO,OAAO,OAAO,CAAC;AAAA,IACtC;AAAA;AAAA,IAGD,aAAa,KAAK;AACjB,YAAM,cAAc,KAAK,WAAW,OAAO,IAAI,SAAO,IAAI,GAAG;AAE7DA,oBAAAA,MAAI,aAAa;AAAA,QAChB,MAAM;AAAA,QACN,SAAS;AAAA,MACV,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,WAAW;AACV,UAAI,KAAK,mBAAmB,GAAG;AAE9B,aAAK,uBAAsB;AAG3B,aAAK;AAGL,aAAK,qBAAoB;AAAA,MAC1B;AAAA,IACA;AAAA;AAAA,IAGD,WAAW;AACV,WAAK,aAAa;AAClB,WAAK,MAAM,UAAU;IACrB;AAAA;AAAA,IAGD,aAAa;AACZ,WAAK,MAAM,UAAU;AACrB,WAAK,aAAa;AAAA,IAClB;AAAA;AAAA,IAGD,cAAc;AACb,WAAK,MAAM,UAAU;AACrB,WAAK,aAAa;AAGlBA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACR,CAAC;AAGD,YAAM,gBAAgB;AAAA,QACrB,QAAQ,KAAK,YAAY;AAAA,QACzB,aAAa;AAAA,QACb,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,QAAQ,CAAE;AAAA,QACV,SAAS;AAAA;AAIV,WAAK,UAAU,KAAK,gBAAgB,IAAI;AAAA,QACvC,GAAG,KAAK,UAAU,KAAK,gBAAgB;AAAA,QACvC,QAAQ;AAAA,QACR,aAAa,KAAK,gBAAe,oBAAI,KAAM,GAAC,YAAW,CAAE;AAAA;AAI1D,YAAM,gBAAgB,KAAK,iBAAiB,UAAU,UAAQ,KAAK,WAAW,cAAc,MAAM;AAClG,UAAI,kBAAkB,IAAI;AACzB,aAAK,iBAAiB,aAAa,IAAI;AAAA,aACjC;AACN,aAAK,iBAAiB,KAAK,aAAa;AAAA,MACzC;AAEAA,oBAAG,MAAC,YAAW;AAGfA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAGD,UAAI,KAAK,mBAAmB,KAAK,UAAU,SAAS,GAAG;AACtD,aAAK;AACL,aAAK,gBAAe;AAAA,aACd;AAEN,aAAK,eAAe;AACpB,aAAK,MAAM,cAAc;MAC1B;AAAA,IACA;AAAA;AAAA,IAGD,aAAa;AAEZ,UAAI,CAAC,KAAK,WAAW,aAAa;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAQA,YAAM,kBAAkB,KAAK,WAAW,OAAO,IAAI,UAAQ;AAAA,QAC1D,KAAK,IAAI;AAAA,QACT,WAAW,IAAI;AAAA,MACf,EAAC;AAGF,YAAM,gBAAgB;AAAA,QACrB,QAAQ,KAAK,YAAY;AAAA,QACzB,aAAa,KAAK,WAAW;AAAA,QAC7B,aAAa,KAAK,WAAW;AAAA,QAC7B,QAAQ,KAAK,WAAW,UAAU;AAAA,QAClC,QAAQ,mBAAmB,CAAC;AAAA;AAI7B,WAAK,UAAU,KAAK,gBAAgB,IAAI;AAAA,QACvC,GAAG,KAAK,UAAU,KAAK,gBAAgB;AAAA,QACvC,aAAa,KAAK,WAAW;AAAA,QAC7B,aAAa,KAAK,WAAW;AAAA,QAC7B,QAAQ,KAAK,WAAW;AAAA,QACxB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,eAAe,KAAK,gBAAe,oBAAI,KAAM,GAAC,YAAW,CAAE;AAAA;AAK5D,YAAM,gBAAgB,KAAK,iBAAiB,UAAU,UAAQ,KAAK,WAAW,cAAc,MAAM;AAClG,UAAI,kBAAkB,IAAI;AACzB,aAAK,iBAAiB,aAAa,IAAI;AAAA,aACjC;AACN,aAAK,iBAAiB,KAAK,aAAa;AAAA,MACzC;AAEAA,oBAAG,MAAC,YAAW;AAMf,UAAI,KAAK,mBAAmB,KAAK,UAAU,SAAS,GAAG;AAGtD,aAAK,uBAAsB;AAE3B,aAAK;AAGL,aAAK,qBAAoB;AAAA,aACnB;AAEN,aAAK,eAAe;AACpB,aAAK,MAAM,cAAc;MAC1B;AAAA,IACA;AAAA;AAAA,IAGD,yBAAyB;AAExB,YAAM,WAAW;AAAA,QAChB,QAAQ,KAAK,YAAY;AAAA,QACzB,aAAa,KAAK,WAAW;AAAA,QAC7B,aAAa,KAAK,WAAW;AAAA,QAC7B,QAAQ,KAAK,WAAW;AAAA,QACxB,QAAQ,KAAK,WAAW;AAAA;AAIzB,YAAM,YAAY,KAAK,UAAU,UAAU,UAAQ,KAAK,OAAO,KAAK,YAAY,EAAE;AAClF,UAAI,cAAc,IAAI;AAErB,aAAK,UAAU,SAAS,EAAE,WAAW;AAAA,MACtC;AAAA,IACA;AAAA;AAAA,IAGD,uBAAuB;AAEtB,WAAK,gBAAe;AAEpB,YAAM,cAAc,KAAK,UAAU,KAAK,gBAAgB;AAGxD,UAAI,YAAY,WAAW,aAAa;AACvC,aAAK,WAAW,cAAc,YAAY,eAAe;AACzD,aAAK,WAAW,cAAc,YAAY,eAAe;AACzD,aAAK,WAAW,SAAS,YAAY,UAAU;AAC/C,aAAK,WAAW,SAAS,YAAY,UAAU,CAAA;AAAA,MAChD,WAES,YAAY,UAAU;AAC9B,aAAK,WAAW,cAAc,YAAY,SAAS,eAAe;AAClE,aAAK,WAAW,cAAc,YAAY,SAAS,eAAe;AAClE,aAAK,WAAW,SAAS,YAAY,SAAS,UAAU;AACxD,aAAK,WAAW,SAAS,YAAY,SAAS,UAAU;MACzD;AAAA,IACA;AAAA;AAAA,IAGD,iBAAiB;AAChB,WAAK,eAAe;AACpB,WAAK,MAAM,cAAc;IACzB;AAAA;AAAA,IAGD,kBAAkB;AACjB,WAAK,MAAM,cAAc;AAGzBA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACR,CAAC;AAGD,YAAM,eAAe,KAAK,UAAU,OAAO,UAAQ,KAAK,WAAW,SAAS;AAC5E,UAAI,aAAa,SAAS,GAAG;AAC5BA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,SAAS,KAAK,aAAa,MAAM;AAAA,UACjC,SAAS,CAAC,QAAQ;AACjB,gBAAI,IAAI,SAAS;AAChB,mBAAK,iBAAgB;AAAA,YACtB;AAAA,UACD;AAAA,QACD,CAAC;AACD;AAAA,MACD;AAEA,WAAK,iBAAgB;AAAA,IACrB;AAAA;AAAA,IAGD,mBAAmB;;AAElBA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACR,CAAC;AAGD,YAAM,iBAAe,UAAK,UAAU,CAAC,MAAhB,mBAAmB,mBAAiB,oBAAI,QAAO;AACpE,YAAM,cAAa,oBAAI,KAAM,GAAC,YAAW;AAGzC,YAAM,aAAa;AAAA,QAClB,gBAAgB,KAAK;AAAA,QACrB,IAAG,KAAK;AAAA,QACR,aAAaA,cAAG,MAAC,eAAe,QAAQ,KAAK;AAAA;AAAA,QAC7C,YAAY,KAAK,eAAe,YAAY;AAAA;AAAA,QAC5C,UAAU,KAAK,eAAe,UAAU;AAAA;AAAA,QACxC,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,gBAAgB,KAAK,iBAAiB,IAAI,UAAQ;AACjD,iBAAO;AAAA,YACN,gBAAgB,KAAK;AAAA,YACrB,cAAc,KAAK,gBAAgB,aAAa,aAAa;AAAA,YAC7D,aAAa,KAAK;AAAA,YAClB,aAAa,KAAK,UAAU;AAAA,YAC5B,QAAQ,KAAK,OAAO,IAAI,SAAO,IAAI,aAAa,IAAI,GAAG;AAAA;AAAA,YACvD,UAAU;AAAA;AAAA,YACV,WAAW;AAAA;SAEZ;AAAA;AASFC,gBAAS,UAAC,mBAAmB,UAAU,EACrC,KAAK,SAAO;AACZD,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAED,mBAAW,MAAM;AAChBA,wBAAAA,MAAI,aAAa;AAAA,YACb,OAAO;AAAA,UACX,CAAC;AAAA,QACD,GAAE,IAAI;AAAA,OACP,EACA,MAAM,SAAO;AACbA,8EAAc,aAAa,GAAG;AAC9BA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,mBAAmB,YAAY;AAC9B,cAAQ,YAAU;AAAA,QACjB,KAAK;AACJ,iBAAO;AAAA,QACR,KAAK;AACJ,iBAAO;AAAA,QACR,KAAK;AACJ,iBAAO;AAAA,QACR;AACC,iBAAO;AAAA,MACT;AAAA,IACA;AAAA;AAAA,IAGD,mBAAmB,YAAY;AAC9B,cAAQ,YAAU;AAAA,QACjB,KAAK;AACJ,iBAAO;AAAA,QACR,KAAK;AACJ,iBAAO;AAAA,QACR,KAAK;AACJ,iBAAO;AAAA,QACR;AACC,iBAAO;AAAA,MACT;AAAA,IACA;AAAA;AAAA,IAGD,kBAAkB,GAAG;AACpB,WAAK,WAAW,cAAc,EAAE,OAAO;AAAA,IACvC;AAAA;AAAA,IAGD,oBAAoB,QAAQ;AAC3B,WAAK,WAAW,cAAc;AAAA,IAC9B;AAAA,EACF;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjqBD,GAAG,WAAW,eAAe;"}