"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      // 绑定状态
      bindings: {
        phone: "13812345678",
        email: "<EMAIL>",
        wechat: "wx123456789",
        wechatName: "微信用户",
        dingtalk: "",
        dingtalkName: ""
      },
      // 手机绑定表单
      phoneForm: {
        number: "",
        code: ""
      },
      // 邮箱绑定表单
      emailForm: {
        address: "",
        code: ""
      }
    };
  },
  onLoad() {
    this.loadBindingInfo();
  },
  methods: {
    // 加载绑定信息
    loadBindingInfo() {
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      if (userInfo) {
        this.bindings.phone = userInfo.phone || "";
        this.bindings.email = userInfo.email || "";
        this.bindings.wechat = userInfo.wechatId || "";
        this.bindings.wechatName = userInfo.wechatName || "微信用户";
        this.bindings.dingtalk = userInfo.dingtalkId || "";
        this.bindings.dingtalkName = userInfo.dingtalkName || "钉钉用户";
      }
    },
    // 手机号脱敏
    maskPhone(phone) {
      if (!phone)
        return "";
      return phone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
    },
    // 邮箱脱敏
    maskEmail(email) {
      if (!email)
        return "";
      const parts = email.split("@");
      if (parts.length !== 2)
        return email;
      const name = parts[0];
      const domain = parts[1];
      return name.substring(0, 3) + "****@" + domain;
    },
    // 处理手机号绑定
    handlePhone() {
      this.$refs.phonePopup.open();
    },
    // 处理邮箱绑定
    handleEmail() {
      this.$refs.emailPopup.open();
    },
    // 处理微信绑定
    handleWechat() {
      if (this.bindings.wechat) {
        common_vendor.index.showModal({
          title: "解绑确认",
          content: "确定要解绑微信账号吗？",
          success: (res) => {
            if (res.confirm) {
              this.bindings.wechat = "";
              this.bindings.wechatName = "";
              const userInfo = common_vendor.index.getStorageSync("userInfo") || {};
              userInfo.wechatId = "";
              userInfo.wechatName = "";
              common_vendor.index.setStorageSync("userInfo", userInfo);
              common_vendor.index.showToast({
                title: "解绑成功",
                icon: "success"
              });
            }
          }
        });
      } else {
        common_vendor.index.showToast({
          title: "微信绑定功能开发中...",
          icon: "none"
        });
      }
    },
    // 处理钉钉绑定
    handleDingtalk() {
      if (this.bindings.dingtalk) {
        common_vendor.index.showModal({
          title: "解绑确认",
          content: "确定要解绑钉钉账号吗？",
          success: (res) => {
            if (res.confirm) {
              this.bindings.dingtalk = "";
              this.bindings.dingtalkName = "";
              const userInfo = common_vendor.index.getStorageSync("userInfo") || {};
              userInfo.dingtalkId = "";
              userInfo.dingtalkName = "";
              common_vendor.index.setStorageSync("userInfo", userInfo);
              common_vendor.index.showToast({
                title: "解绑成功",
                icon: "success"
              });
            }
          }
        });
      } else {
        common_vendor.index.showToast({
          title: "钉钉绑定功能开发中...",
          icon: "none"
        });
      }
    },
    // 发送验证码
    sendCode(type) {
      if (type === "phone") {
        if (!this.phoneForm.number || this.phoneForm.number.length !== 11) {
          common_vendor.index.showToast({
            title: "请输入正确的手机号",
            icon: "none"
          });
          return;
        }
      } else if (type === "email") {
        const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
        if (!emailRegex.test(this.emailForm.address)) {
          common_vendor.index.showToast({
            title: "请输入正确的邮箱地址",
            icon: "none"
          });
          return;
        }
      }
      common_vendor.index.showToast({
        title: "验证码已发送",
        icon: "success"
      });
    },
    // 确认绑定
    confirmBinding(type) {
      if (type === "phone") {
        if (!this.phoneForm.number || this.phoneForm.number.length !== 11) {
          common_vendor.index.showToast({
            title: "请输入正确的手机号",
            icon: "none"
          });
          return;
        }
        if (!this.phoneForm.code || this.phoneForm.code.length !== 6) {
          common_vendor.index.showToast({
            title: "请输入6位验证码",
            icon: "none"
          });
          return;
        }
        this.bindings.phone = this.phoneForm.number;
        const userInfo = common_vendor.index.getStorageSync("userInfo") || {};
        userInfo.phone = this.bindings.phone;
        common_vendor.index.setStorageSync("userInfo", userInfo);
        common_vendor.index.showToast({
          title: "手机绑定成功",
          icon: "success"
        });
        this.closePopup("phonePopup");
      } else if (type === "email") {
        const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
        if (!emailRegex.test(this.emailForm.address)) {
          common_vendor.index.showToast({
            title: "请输入正确的邮箱地址",
            icon: "none"
          });
          return;
        }
        if (!this.emailForm.code || this.emailForm.code.length !== 6) {
          common_vendor.index.showToast({
            title: "请输入6位验证码",
            icon: "none"
          });
          return;
        }
        this.bindings.email = this.emailForm.address;
        const userInfo = common_vendor.index.getStorageSync("userInfo") || {};
        userInfo.email = this.bindings.email;
        common_vendor.index.setStorageSync("userInfo", userInfo);
        common_vendor.index.showToast({
          title: "邮箱绑定成功",
          icon: "success"
        });
        this.closePopup("emailPopup");
      }
    },
    // 关闭弹出层
    closePopup(ref) {
      this.$refs[ref].close();
    }
  }
};
if (!Array) {
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  _easycom_uni_popup2();
}
const _easycom_uni_popup = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.bindings.phone
  }, $data.bindings.phone ? {
    b: common_vendor.t($options.maskPhone($data.bindings.phone))
  } : {}, {
    c: common_vendor.t($data.bindings.phone ? "更换" : "绑定"),
    d: common_vendor.o((...args) => $options.handlePhone && $options.handlePhone(...args)),
    e: $data.bindings.email
  }, $data.bindings.email ? {
    f: common_vendor.t($options.maskEmail($data.bindings.email))
  } : {}, {
    g: common_vendor.t($data.bindings.email ? "更换" : "绑定"),
    h: common_vendor.o((...args) => $options.handleEmail && $options.handleEmail(...args)),
    i: $data.bindings.wechat
  }, $data.bindings.wechat ? {
    j: common_vendor.t($data.bindings.wechatName)
  } : {}, {
    k: common_vendor.t($data.bindings.wechat ? "解绑" : "绑定"),
    l: common_vendor.o((...args) => $options.handleWechat && $options.handleWechat(...args)),
    m: $data.bindings.dingtalk
  }, $data.bindings.dingtalk ? {
    n: common_vendor.t($data.bindings.dingtalkName)
  } : {}, {
    o: common_vendor.t($data.bindings.dingtalk ? "解绑" : "绑定"),
    p: common_vendor.o((...args) => $options.handleDingtalk && $options.handleDingtalk(...args)),
    q: $data.phoneForm.number,
    r: common_vendor.o(($event) => $data.phoneForm.number = $event.detail.value),
    s: $data.phoneForm.code,
    t: common_vendor.o(($event) => $data.phoneForm.code = $event.detail.value),
    v: common_vendor.o(($event) => $options.sendCode("phone")),
    w: common_vendor.o(($event) => $options.closePopup("phonePopup")),
    x: common_vendor.o(($event) => $options.confirmBinding("phone")),
    y: common_vendor.sr("phonePopup", "628d1c37-0"),
    z: common_vendor.p({
      type: "center"
    }),
    A: $data.emailForm.address,
    B: common_vendor.o(($event) => $data.emailForm.address = $event.detail.value),
    C: $data.emailForm.code,
    D: common_vendor.o(($event) => $data.emailForm.code = $event.detail.value),
    E: common_vendor.o(($event) => $options.sendCode("email")),
    F: common_vendor.o(($event) => $options.closePopup("emailPopup")),
    G: common_vendor.o(($event) => $options.confirmBinding("email")),
    H: common_vendor.sr("emailPopup", "628d1c37-1"),
    I: common_vendor.p({
      type: "center"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/user/account-binding.js.map
