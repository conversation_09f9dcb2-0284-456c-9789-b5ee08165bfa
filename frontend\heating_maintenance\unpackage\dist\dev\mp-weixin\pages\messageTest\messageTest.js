"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_messageService = require("../../utils/messageService.js");
const _sfc_main = {
  data() {
    return {
      // 用于在模板中显示服务状态
      isServiceRunning: false
    };
  },
  computed: {
    // 计算属性，返回更友好的状态文本
    serviceStatusText() {
      return this.isServiceRunning ? "运行中" : "已停止";
    }
  },
  onLoad() {
    this.isServiceRunning = utils_messageService.messageService.getStatus();
  },
  methods: {
    // 启动服务按钮点击事件
    startMsgService() {
      common_vendor.index.__f__("log", "at pages/messageTest/messageTest.vue:37", "测试页面：尝试启动消息服务...");
      utils_messageService.messageService.start();
      this.isServiceRunning = utils_messageService.messageService.getStatus();
    },
    // 停止服务按钮点击事件
    stopMsgService() {
      common_vendor.index.__f__("log", "at pages/messageTest/messageTest.vue:44", "测试页面：尝试停止消息服务...");
      utils_messageService.messageService.stop();
      this.isServiceRunning = utils_messageService.messageService.getStatus();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t($options.serviceStatusText),
    b: $data.isServiceRunning ? 1 : "",
    c: !$data.isServiceRunning ? 1 : "",
    d: common_vendor.o((...args) => $options.startMsgService && $options.startMsgService(...args)),
    e: $data.isServiceRunning,
    f: common_vendor.o((...args) => $options.stopMsgService && $options.stopMsgService(...args)),
    g: !$data.isServiceRunning
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-577bde04"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/messageTest/messageTest.js.map
