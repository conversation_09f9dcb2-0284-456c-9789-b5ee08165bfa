{"version": 3, "file": "agreement.js", "sources": ["pages/user/agreement.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlci9hZ3JlZW1lbnQudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"agreement-container\">\r\n    <!-- 顶部导航栏 -->\r\n    <view class=\"nav-bar\">\r\n      <view class=\"back-button\" @click=\"goBack\">\r\n        <text class=\"back-icon\">&#xe6e4;</text>\r\n      </view>\r\n      <text class=\"page-title\">用户协议</text>\r\n    </view>\r\n\r\n    <!-- 协议内容 -->\r\n    <scroll-view class=\"agreement-content\" scroll-y=\"true\">\r\n      <view class=\"agreement-section\">\r\n        <text class=\"section-title\">洁明智慧供热服务协议</text>\r\n        <text class=\"section-date\">生效日期：2025年01月01日</text>\r\n\r\n        <view class=\"section-paragraph\">\r\n          <text class=\"paragraph-text\">欢迎您使用洁明智慧供热服务！</text>\r\n        </view>\r\n\r\n        <view class=\"section-paragraph\">\r\n          <text class=\"paragraph-text\"\r\n            >请您务必审慎阅读、充分理解本协议各条款内容，特别是免除或限制责任的相应条款。除非您已阅读并接受本协议所有条款，否则您无权使用本平台提供的服务。您使用本平台服务的行为即视为您已阅读并同意本协议的约束。</text\r\n          >\r\n        </view>\r\n\r\n        <text class=\"sub-section-title\">一、服务内容</text>\r\n        <view class=\"section-paragraph\">\r\n          <text class=\"paragraph-text\"\r\n            >1.1 洁明智慧供热是一个为用户提供能源管理、监测、分析等服务的综合性平台。</text\r\n          >\r\n        </view>\r\n        <view class=\"section-paragraph\">\r\n          <text class=\"paragraph-text\"\r\n            >1.2\r\n            本平台向用户提供的具体服务内容包括但不限于：能源数据监测、设备维护管理、故障报警、能源消耗分析、节能建议等。</text\r\n          >\r\n        </view>\r\n\r\n        <text class=\"sub-section-title\">二、账号注册与安全</text>\r\n        <view class=\"section-paragraph\">\r\n          <text class=\"paragraph-text\"\r\n            >2.1\r\n            用户在使用本平台服务前需要注册账号。用户在注册过程中须提供真实、准确、完整的个人资料，并及时更新。</text\r\n          >\r\n        </view>\r\n        <view class=\"section-paragraph\">\r\n          <text class=\"paragraph-text\"\r\n            >2.2\r\n            用户注册成功后，应妥善保管账号及密码信息，因用户保管不善造成的损失由用户自行承担。</text\r\n          >\r\n        </view>\r\n        <view class=\"section-paragraph\">\r\n          <text class=\"paragraph-text\"\r\n            >2.3\r\n            用户在使用本平台服务过程中，应遵守国家相关法律法规，不得利用本平台从事违法违规活动。</text\r\n          >\r\n        </view>\r\n\r\n        <text class=\"sub-section-title\">三、用户隐私保护</text>\r\n        <view class=\"section-paragraph\">\r\n          <text class=\"paragraph-text\"\r\n            >3.1\r\n            本平台重视用户隐私保护，将按照本协议及隐私政策收集、使用、存储、共享和保护用户个人信息。</text\r\n          >\r\n        </view>\r\n        <view class=\"section-paragraph\">\r\n          <text class=\"paragraph-text\"\r\n            >3.2\r\n            本平台收集用户个人信息的目的是为了提供更好的服务，包括但不限于使用本平台服务、满足用户个性化需求、改进服务质量等。</text\r\n          >\r\n        </view>\r\n        <view class=\"section-paragraph\">\r\n          <text class=\"paragraph-text\"\r\n            >3.3\r\n            未经用户许可，本平台不会向任何第三方提供、公开或共享用户个人信息，但以下情况除外：\r\n            (1) 获得用户明确授权； (2) 根据法律法规或政府强制性规定； (3)\r\n            为维护社会公共利益； (4) 为维护本平台合法权益。</text\r\n          >\r\n        </view>\r\n\r\n        <text class=\"sub-section-title\">四、用户行为规范</text>\r\n        <view class=\"section-paragraph\">\r\n          <text class=\"paragraph-text\"\r\n            >4.1 用户在使用本平台服务过程中，必须遵守中华人民共和国相关法律法规。</text\r\n          >\r\n        </view>\r\n        <view class=\"section-paragraph\">\r\n          <text class=\"paragraph-text\"\r\n            >4.2 用户不得利用本平台服务从事以下行为： (1) 违反国家法律法规的行为； (2)\r\n            侵犯他人知识产权、商业秘密等合法权益的行为； (3)\r\n            上传、发布含有计算机病毒、木马或可能危害系统安全的内容； (4)\r\n            干扰或破坏本平台服务或服务器的行为； (5)\r\n            其他违背社会公德或可能导致不良后果的行为。</text\r\n          >\r\n        </view>\r\n\r\n        <text class=\"sub-section-title\">五、知识产权</text>\r\n        <view class=\"section-paragraph\">\r\n          <text class=\"paragraph-text\"\r\n            >5.1\r\n            本平台所包含的全部内容，包括但不限于文字、图片、数据、代码等，均受著作权、商标权、专利权及其他知识产权法律法规保护。</text\r\n          >\r\n        </view>\r\n        <view class=\"section-paragraph\">\r\n          <text class=\"paragraph-text\"\r\n            >5.2\r\n            未经本平台事先书面许可，用户不得以任何方式使用、复制或传播本平台内容。</text\r\n          >\r\n        </view>\r\n\r\n        <text class=\"sub-section-title\">六、免责声明</text>\r\n        <view class=\"section-paragraph\">\r\n          <text class=\"paragraph-text\"\r\n            >6.1\r\n            本平台不保证服务一定能满足用户的所有要求，也不保证服务不会中断，不保证服务的及时性、安全性、准确性。</text\r\n          >\r\n        </view>\r\n        <view class=\"section-paragraph\">\r\n          <text class=\"paragraph-text\"\r\n            >6.2\r\n            对于因不可抗力、系统故障、通讯故障、网络拥塞、供电系统故障、恶意攻击、计算机病毒等原因而导致的服务中断或其他缺陷，本平台不承担任何责任。</text\r\n          >\r\n        </view>\r\n\r\n        <text class=\"sub-section-title\">七、协议修改</text>\r\n        <view class=\"section-paragraph\">\r\n          <text class=\"paragraph-text\"\r\n            >7.1\r\n            本平台有权在必要时修改本协议条款。协议条款一旦发生变动，将会在本平台相关页面上公布修改后的协议。</text\r\n          >\r\n        </view>\r\n        <view class=\"section-paragraph\">\r\n          <text class=\"paragraph-text\"\r\n            >7.2\r\n            如果不同意本平台对本协议相关条款所做的修改，用户有权停止使用本平台服务。如果用户继续使用本平台服务，则视为用户接受本平台对本协议相关条款所做的修改。</text\r\n          >\r\n        </view>\r\n\r\n        <text class=\"sub-section-title\">八、适用法律及争议解决</text>\r\n        <view class=\"section-paragraph\">\r\n          <text class=\"paragraph-text\"\r\n            >8.1 本协议的订立、执行和解释及争议的解决均应适用中华人民共和国法律。</text\r\n          >\r\n        </view>\r\n        <view class=\"section-paragraph\">\r\n          <text class=\"paragraph-text\"\r\n            >8.2\r\n            如发生本平台服务相关的争议，应尽量友好协商解决；协商不成时，任何一方均有权将争议提交至本平台运营主体所在地有管辖权的人民法院诉讼解决。</text\r\n          >\r\n        </view>\r\n\r\n        <text class=\"sub-section-title\">九、其他</text>\r\n        <view class=\"section-paragraph\">\r\n          <text class=\"paragraph-text\"\r\n            >9.1\r\n            本协议所有条款的标题仅为阅读方便，本身并无实际涵义，不能作为本协议涵义解释的依据。</text\r\n          >\r\n        </view>\r\n        <view class=\"section-paragraph\">\r\n          <text class=\"paragraph-text\"\r\n            >9.2\r\n            如本协议中的任何条款无论因何种原因完全或部分无效或不具有执行力，本协议的其余条款仍应有效并且有约束力。</text\r\n          >\r\n        </view>\r\n\r\n        <view class=\"section-end\">\r\n          <text class=\"end-text\">感谢您对洁明智慧供热的支持！</text>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {};\r\n  },\r\n  methods: {\r\n    // 返回上一页\r\n    goBack() {\r\n      uni.navigateBack();\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.agreement-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 导航栏 */\r\n.nav-bar {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 30rpx;\r\n  background-color: #ffffff;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\r\n  .back-button {\r\n    width: 60rpx;\r\n    height: 60rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .back-icon {\r\n      font-family: \"iconfont\";\r\n      font-size: 36rpx;\r\n      color: #333;\r\n    }\r\n  }\r\n\r\n  .page-title {\r\n    flex: 1;\r\n    text-align: center;\r\n    font-size: 34rpx;\r\n    font-weight: bold;\r\n    color: #333;\r\n    margin-right: 60rpx; /* 为了让标题居中 */\r\n  }\r\n}\r\n\r\n/* 协议内容 */\r\n.agreement-content {\r\n  flex: 1;\r\n  padding: 30rpx;\r\n\r\n  .agreement-section {\r\n    background-color: #ffffff;\r\n    border-radius: 16rpx;\r\n    padding: 40rpx 30rpx;\r\n\r\n    .section-title {\r\n      font-size: 36rpx;\r\n      font-weight: bold;\r\n      color: #333;\r\n      display: block;\r\n      margin-bottom: 20rpx;\r\n      text-align: center;\r\n    }\r\n\r\n    .section-date {\r\n      font-size: 26rpx;\r\n      color: #999;\r\n      display: block;\r\n      margin-bottom: 40rpx;\r\n      text-align: center;\r\n    }\r\n\r\n    .sub-section-title {\r\n      font-size: 32rpx;\r\n      font-weight: bold;\r\n      color: #333;\r\n      display: block;\r\n      margin: 40rpx 0 20rpx;\r\n    }\r\n\r\n    .section-paragraph {\r\n      margin-bottom: 20rpx;\r\n\r\n      .paragraph-text {\r\n        font-size: 28rpx;\r\n        color: #666;\r\n        line-height: 1.6;\r\n      }\r\n    }\r\n\r\n    .section-end {\r\n      margin-top: 60rpx;\r\n      text-align: center;\r\n\r\n      .end-text {\r\n        font-size: 28rpx;\r\n        color: #999;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/user/agreement.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA+KA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;EACR;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,EACF;AACH;;;;;;;ACxLA,GAAG,WAAW,eAAe;"}