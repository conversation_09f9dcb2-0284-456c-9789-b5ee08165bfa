/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.complete-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}
.form-card {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
}
.form-card .form-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}
.form-card .form-item {
  margin-bottom: 30rpx;
}
.form-card .form-item .form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}
.form-card .form-item .form-label.required::before {
  content: "*";
  color: #f5222d;
  margin-right: 6rpx;
}
.form-card .form-item .form-textarea {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}
.form-card .form-item .picker-view {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10rpx;
}
.form-card .form-item .picker-view .iconfont {
  font-size: 32rpx;
  color: #999;
}
.form-card .form-item .upload-area .form-header {
  margin-bottom: 20rpx;
}
.form-card .form-item .upload-area .form-header .form-subtitle {
  font-size: 24rpx;
  color: #666;
}
.form-card .form-item .upload-area .image-grid {
  display: flex;
  flex-wrap: wrap;
}
.form-card .form-item .upload-area .image-grid .image-item, .form-card .form-item .upload-area .image-grid .upload-item {
  width: 200rpx;
  height: 200rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
}
.form-card .form-item .upload-area .image-grid .image-item image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}
.form-card .form-item .upload-area .image-grid .image-item .delete-icon {
  position: absolute;
  top: -16rpx;
  right: -16rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
}
.form-card .form-item .upload-area .image-grid .upload-item {
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.form-card .form-item .upload-area .image-grid .upload-item .iconfont {
  font-size: 60rpx;
  color: #666;
  margin-bottom: 16rpx;
}
.form-card .form-item .upload-area .image-grid .upload-item text {
  font-size: 24rpx;
  color: #666;
}
.form-card .form-item .upload-area .video-container {
  width: 100%;
  height: 400rpx;
  position: relative;
}
.form-card .form-item .upload-area .video-container video {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}
.form-card .form-item .upload-area .video-container .delete-icon {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
}
.action-buttons {
  display: flex;
  padding: 20rpx 0;
}
.action-buttons button {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  margin: 0 10rpx;
}
.action-buttons button::after {
  border: none;
}
.action-buttons .btn-cancel {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}
.action-buttons .btn-submit {
  background-color: #1890ff;
  color: #fff;
}
.materials-container .material-item {
  margin-bottom: 15rpx;
}
.materials-container .material-item .material-row {
  display: flex;
  align-items: center;
}
.materials-container .material-item .material-row .material-selector {
  flex: 2;
  margin-right: 15rpx;
}
.materials-container .material-item .material-row .material-selector .material-input {
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.materials-container .material-item .material-row .material-selector .material-input .arrow-down {
  font-size: 24rpx;
  color: #999;
}
.materials-container .material-item .material-row .quantity-input {
  flex: 1;
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}
.materials-container .material-item .material-row .delete-btn {
  width: 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  font-size: 40rpx;
  color: #999;
}
.materials-container .add-material-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  color: #1890ff;
  font-size: 28rpx;
  border: 1px dashed #ccc;
}
.materials-container .add-material-btn .iconfont {
  margin-right: 10rpx;
}
.material-selector-popup {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  height: 60vh;
  display: flex;
  flex-direction: column;
  z-index: 100;
}
.material-selector-popup .selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #e5e5e5;
}
.material-selector-popup .selector-header .selector-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.material-selector-popup .selector-header .header-actions {
  display: flex;
  align-items: center;
}
.material-selector-popup .selector-header .close-button {
  font-size: 28rpx;
  color: #666;
}
.material-selector-popup .selector-header .confirm-button-header {
  font-size: 28rpx;
  color: #1890ff;
  font-weight: 500;
  margin-right: 20rpx;
}
.material-selector-popup .selector-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 30rpx;
  -webkit-overflow-scrolling: touch;
}
.material-selector-popup .selector-content .template-search {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
  position: relative;
}
.material-selector-popup .selector-content .template-search .search-input {
  flex: 1;
  height: 70rpx;
  border-radius: 35rpx;
  background: #f5f5f5;
  font-size: 28rpx;
  padding: 0 70rpx 0 30rpx;
  color: #333;
}
.material-selector-popup .selector-content .template-search .search-clear {
  position: absolute;
  right: 130rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
  border-radius: 50%;
  background: #ccc;
  color: #fff;
  font-size: 24rpx;
}
.material-selector-popup .selector-content .template-search .search-btn {
  padding: 0 30rpx;
  font-size: 28rpx;
  color: #1890ff;
  height: 70rpx;
  line-height: 70rpx;
}
.material-selector-popup .selector-content .material-list .material-item-popup {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.material-selector-popup .selector-content .material-list .material-item-popup:last-child {
  border-bottom: none;
}
.material-selector-popup .selector-content .material-list .material-item-popup .material-info {
  flex: 1;
}
.material-selector-popup .selector-content .material-list .material-item-popup .material-info .material-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
  display: block;
}
.material-selector-popup .selector-content .material-list .material-item-popup .material-info .material-type {
  font-size: 24rpx;
  color: #666;
}
.material-selector-popup .selector-content .material-list .material-item-popup .checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 1rpx solid #e5e5e5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.material-selector-popup .selector-content .material-list .material-item-popup .checkbox.checked {
  background-color: #1890ff;
  border-color: #1890ff;
}
.material-selector-popup .selector-content .material-list .material-item-popup .checkbox.checked .iconfont {
  color: #fff;
  font-size: 24rpx;
}
.material-selector-popup .selector-content .empty-tip {
  text-align: center;
  padding: 40rpx 0;
  color: #666;
  font-size: 28rpx;
}
.iconfont {
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon-add:before {
  content: "+";
}
.icon-check:before {
  content: "✓";
}