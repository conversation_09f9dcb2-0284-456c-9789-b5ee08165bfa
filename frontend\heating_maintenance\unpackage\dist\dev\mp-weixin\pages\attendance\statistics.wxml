<view class="statistics-container"><view class="filter-section"><view class="month-selector"><text class="current-month">{{a}}年{{b}}月</text><view class="month-arrows"><uni-icons wx:if="{{d}}" bindclick="{{c}}" u-i="85eb6f3a-0" bind:__l="__l" u-p="{{d}}"></uni-icons><uni-icons wx:if="{{f}}" bindclick="{{e}}" u-i="85eb6f3a-1" bind:__l="__l" u-p="{{f}}"></uni-icons></view></view><view class="staff-selector" bindtap="{{i}}"><text>{{g}}</text><uni-icons wx:if="{{h}}" u-i="85eb6f3a-2" bind:__l="__l" u-p="{{h}}"></uni-icons></view></view><view class="statistics-cards"><view class="stat-card"><view class="stat-value">{{j}}</view><view class="stat-label">正常出勤</view></view><view class="stat-card"><view class="stat-value">{{k}}</view><view class="stat-label">迟到</view></view><view class="stat-card"><view class="stat-value">{{l}}</view><view class="stat-label">早退</view></view><view class="stat-card"><view class="stat-value">{{m}}</view><view class="stat-label">缺勤</view></view></view><view class="main-calendar"><view class="calendar-view"><view class="calendar-header"><view wx:for="{{n}}" wx:for-item="day" wx:key="b" class="header-item">{{day.a}}</view></view><view class="calendar-days"><view wx:for="{{o}}" wx:for-item="day" wx:key="b" class="{{['calendar-day', day.c && 'current-month', day.d && 'today', day.e && 'normal', day.f && 'late', day.g && 'early', day.h && 'absent']}}" bindtap="{{day.i}}"><text class="day-number">{{day.a}}</text></view></view></view></view><view class="charts-section"><view class="section-title"><text>考勤数据分析</text></view><view class="charts-container"><view class="chart-card"><view class="chart-title">月度考勤统计</view><view class="chart-container"><canvas canvas-id="attendanceBarChart" style="width:100%;height:400rpx"></canvas></view></view><view class="chart-card"><view class="chart-title">考勤类型分布</view><view class="chart-container"><canvas canvas-id="attendancePieChart" style="width:100%;height:400rpx"></canvas></view></view></view></view><view class="attendance-records"><view class="record-header"><text>考勤记录</text><view class="header-actions"><text class="export-btn" bindtap="{{p}}">导出统计</text><text class="view-all" bindtap="{{q}}">查看全部</text></view></view><view wx:if="{{r}}" class="record-list"><view wx:for="{{s}}" wx:for-item="record" wx:key="j" class="record-item"><view class="record-date"><text class="day">{{record.a}}</text><text class="week">{{record.b}}</text></view><view class="record-time"><view class="time-item"><text class="time-label">上班打卡</text><text class="{{['time-value', record.d && 'abnormal']}}">{{record.c}}</text><text wx:if="{{record.e}}" class="status-tag late">迟到</text></view><view class="time-item"><text class="time-label">下班打卡</text><text class="{{['time-value', record.g && 'abnormal']}}">{{record.f}}</text><text wx:if="{{record.h}}" class="status-tag early">早退</text></view></view><view class="record-location"><text class="location-label">打卡地点</text><text class="location-value">{{record.i}}</text></view></view></view><view wx:else class="empty-records"><image src="{{t}}" mode="aspectFit"></image><text>暂无考勤记录</text></view></view><uni-popup wx:if="{{E}}" class="r" u-s="{{['d']}}" u-r="staffPopup" u-i="85eb6f3a-3" bind:__l="__l" u-p="{{E}}"><view class="staff-popup"><view class="popup-header"><text>选择人员</text><text bindtap="{{v}}" class="close-btn">关闭</text></view><view class="search-box"><uni-icons wx:if="{{w}}" u-i="85eb6f3a-4,85eb6f3a-3" bind:__l="__l" u-p="{{w}}"></uni-icons><input type="text" placeholder="搜索人员姓名" value="{{x}}" bindinput="{{y}}"/></view><scroll-view scroll-y="true" class="staff-list"><view class="staff-item" bindtap="{{B}}"><text>全部人员</text><uni-icons wx:if="{{z}}" u-i="85eb6f3a-5,85eb6f3a-3" bind:__l="__l" u-p="{{A}}"></uni-icons></view><view wx:for="{{C}}" wx:for-item="staff" wx:key="e" class="staff-item" bindtap="{{staff.f}}"><text>{{staff.a}}</text><uni-icons wx:if="{{staff.b}}" u-i="{{staff.c}}" bind:__l="__l" u-p="{{staff.d}}"></uni-icons></view></scroll-view></view></uni-popup><uni-popup wx:if="{{al}}" class="r" u-s="{{['d']}}" u-r="dayDetailPopup" u-i="85eb6f3a-7" bind:__l="__l" u-p="{{al}}"><view class="day-detail-popup"><view class="popup-header"><text>{{F}} 考勤详情</text><text class="close-btn" bindtap="{{G}}">关闭</text></view><canvas wx:if="{{H}}" canvas-id="dayAttendanceChart" class="day-stats-chart" bindtouchstart="{{I}}"></canvas><view wx:if="{{J}}" class="day-stats-summary"><view class="stat-item normal"><text class="stat-value">{{K}}</text><text class="stat-label">正常</text></view><view class="stat-item late"><text class="stat-value">{{L}}</text><text class="stat-label">迟到</text></view><view class="stat-item early"><text class="stat-value">{{M}}</text><text class="stat-label">早退</text></view><view class="stat-item absent"><text class="stat-value">{{N}}</text><text class="stat-label">缺勤</text></view></view><view wx:if="{{O}}" class="staff-detail-section"><view class="detail-item"><text class="detail-label">姓名</text><text class="detail-value">{{P}}</text></view><view class="detail-item"><text class="detail-label">上班时间</text><view class="detail-value-box"><text class="detail-value">{{Q}}</text><text wx:if="{{R}}" class="status-tag late">迟到</text></view></view><view class="detail-item"><text class="detail-label">下班时间</text><view class="detail-value-box"><text class="detail-value">{{S}}</text><text wx:if="{{T}}" class="status-tag early">早退</text></view></view><view class="detail-item"><text class="detail-label">工作时长</text><text class="detail-value">{{U}}</text></view></view><view wx:if="{{V}}" class="staff-list-section"><text class="section-title">人员考勤列表</text><view wx:if="{{W}}" class="staff-category"><view class="category-header normal"><text>正常考勤</text><text>{{X}}人</text></view><view class="staff-items"><view wx:for="{{Y}}" wx:for-item="staff" wx:key="d" class="staff-item" bindtap="{{staff.e}}"><text class="staff-name">{{staff.a}}</text><text class="staff-time">{{staff.b}} - {{staff.c}}</text></view></view></view><view wx:if="{{Z}}" class="staff-category"><view class="category-header late"><text>迟到人员</text><text>{{aa}}人</text></view><view class="staff-items"><view wx:for="{{ab}}" wx:for-item="staff" wx:key="c" class="staff-item" bindtap="{{staff.d}}"><text class="staff-name">{{staff.a}}</text><text class="staff-time">{{staff.b}}</text></view></view></view><view wx:if="{{ac}}" class="staff-category"><view class="category-header early"><text>早退人员</text><text>{{ad}}人</text></view><view class="staff-items"><view wx:for="{{ae}}" wx:for-item="staff" wx:key="c" class="staff-item" bindtap="{{staff.d}}"><text class="staff-name">{{staff.a}}</text><text class="staff-time">{{staff.b}}</text></view></view></view><view wx:if="{{af}}" class="staff-category"><view class="category-header absent"><text>缺勤人员</text><text>{{ag}}人</text></view><view class="staff-items"><view wx:for="{{ah}}" wx:for-item="staff" wx:key="b" class="staff-item" bindtap="{{staff.c}}"><text class="staff-name">{{staff.a}}</text><text class="staff-time">无考勤记录</text></view></view></view></view><view wx:if="{{ai}}" class="empty-day-detail"><image src="{{aj}}" mode="aspectFit"></image><text>暂无考勤记录</text></view></view></uni-popup><uni-popup wx:if="{{aJ}}" class="r" u-s="{{['d']}}" u-r="exportPopup" u-i="85eb6f3a-8" bind:__l="__l" u-p="{{aJ}}"><view class="export-popup"><view class="popup-header"><text>导出考勤统计</text><text bindtap="{{am}}" class="close-btn">关闭</text></view><view class="export-form"><view class="export-item"><text class="export-label">统计时间范围</text><view class="export-date-range"><view class="date-select" bindtap="{{ap}}"><text>{{an}}</text><uni-icons wx:if="{{ao}}" u-i="85eb6f3a-9,85eb6f3a-8" bind:__l="__l" u-p="{{ao}}"></uni-icons></view><text class="range-separator">至</text><view class="date-select" bindtap="{{as}}"><text>{{aq}}</text><uni-icons wx:if="{{ar}}" u-i="85eb6f3a-10,85eb6f3a-8" bind:__l="__l" u-p="{{ar}}"></uni-icons></view></view></view><view class="export-item"><text class="export-label">导出对象</text><view class="export-radio-group"><view class="radio-item" bindtap="{{av}}"><view class="{{['radio-btn', at && 'active']}}"></view><text>全部人员</text></view><view class="radio-item" bindtap="{{ax}}"><view class="{{['radio-btn', aw && 'active']}}"></view><text>选定人员</text></view></view></view><view wx:if="{{ay}}" class="export-item"><text class="export-label">选择人员</text><view class="selected-staff" bindtap="{{aB}}"><text>已选择 {{az}} 人</text><uni-icons wx:if="{{aA}}" u-i="85eb6f3a-11,85eb6f3a-8" bind:__l="__l" u-p="{{aA}}"></uni-icons></view></view><view class="export-item"><text class="export-label">导出格式</text><view class="export-radio-group"><view class="radio-item" bindtap="{{aD}}"><view class="{{['radio-btn', aC && 'active']}}"></view><text>Excel表格</text></view><view class="radio-item" bindtap="{{aF}}"><view class="{{['radio-btn', aE && 'active']}}"></view><text>PDF文档</text></view></view></view></view><view class="export-footer"><button class="cancel-btn" bindtap="{{aG}}">取消</button><button class="confirm-btn" bindtap="{{aH}}">确认导出</button></view></view></uni-popup><uni-popup wx:if="{{aQ}}" class="r" u-s="{{['d']}}" u-r="multiStaffPopup" u-i="85eb6f3a-12" bind:__l="__l" u-p="{{aQ}}"><view class="staff-popup"><view class="popup-header"><text>选择导出人员</text><text bindtap="{{aK}}" class="close-btn">确定</text></view><view class="search-box"><uni-icons wx:if="{{aL}}" u-i="85eb6f3a-13,85eb6f3a-12" bind:__l="__l" u-p="{{aL}}"></uni-icons><input type="text" placeholder="搜索人员姓名" value="{{aM}}" bindinput="{{aN}}"/></view><scroll-view scroll-y="true" class="staff-list"><view wx:for="{{aO}}" wx:for-item="staff" wx:key="g" class="staff-item" bindtap="{{staff.h}}"><text>{{staff.a}}</text><uni-icons wx:if="{{staff.b}}" u-i="{{staff.c}}" bind:__l="__l" u-p="{{staff.d}}"></uni-icons><uni-icons wx:else u-i="{{staff.e}}" bind:__l="__l" u-p="{{staff.f||''}}"></uni-icons></view></scroll-view></view></uni-popup><uni-calendar wx:if="{{aT}}" class="r" u-r="calendar" bindconfirm="{{aS}}" u-i="85eb6f3a-16" bind:__l="__l" u-p="{{aT}}"></uni-calendar></view>