/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.rules-container {
  padding: 30rpx;
  background-color: #f5f7fa;
  min-height: 100vh;
}
.page-header {
  margin-bottom: 40rpx;
}
.page-header .page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.rules-form {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.rules-form .form-item {
  margin-bottom: 30rpx;
}
.rules-form .form-item .form-label {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 16rpx;
}
.rules-form .form-item .form-input {
  background-color: #f5f7fa;
  border-radius: 8rpx;
  padding: 20rpx;
}
.rules-form .form-item .form-input input {
  width: 100%;
  height: 60rpx;
  font-size: 28rpx;
}
.rules-form .form-item .form-input .picker-value {
  height: 60rpx;
  line-height: 60rpx;
  font-size: 28rpx;
}
.rules-form .form-item .form-desc {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}
.rules-form .form-actions {
  margin-top: 50rpx;
}
.rules-form .form-actions .btn-save {
  width: 100%;
  height: 90rpx;
  background: linear-gradient(135deg, #4483e5, #6a9eef);
  color: #fff;
  font-size: 32rpx;
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.rules-form .form-actions .btn-save:disabled {
  background: linear-gradient(135deg, #ccc, #999);
  opacity: 0.7;
}