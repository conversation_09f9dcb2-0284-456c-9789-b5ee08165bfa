"use strict";
const common_vendor = require("../../../common/vendor.js");
const utils_api = require("../../../utils/api.js");
const _sfc_main = {
  data() {
    return {
      clockInTime: "09:00",
      clockOutTime: "18:00",
      allowedDistance: 500,
      lateThreshold: 15,
      earlyLeaveThreshold: 15,
      loading: false
    };
  },
  onLoad() {
    this.getRules();
  },
  methods: {
    // 获取规则
    getRules() {
      this.loading = true;
      utils_api.attendanceApi.getClockRules().then((res) => {
        if (res.code === 200 && res.data) {
          this.clockInTime = res.data.clockInTime || "09:00";
          this.clockOutTime = res.data.clockOutTime || "18:00";
          this.allowedDistance = res.data.allowedDistance || 500;
          this.lateThreshold = res.data.lateThreshold || 15;
          this.earlyLeaveThreshold = res.data.earlyLeaveThreshold || 15;
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/attendance/admin/rules.vue:65", "获取考勤规则失败:", err);
        common_vendor.index.showToast({
          title: "获取规则失败",
          icon: "none"
        });
      }).finally(() => {
        this.loading = false;
      });
    },
    // 保存规则
    saveRules() {
      if (!this.clockInTime) {
        common_vendor.index.showToast({
          title: "请设置上班时间",
          icon: "none"
        });
        return;
      }
      if (!this.clockOutTime) {
        common_vendor.index.showToast({
          title: "请设置下班时间",
          icon: "none"
        });
        return;
      }
      this.loading = true;
      utils_api.attendanceApi.updateClockRules(this.clockInTime, this.clockOutTime).then((res) => {
        if (res.code === 200) {
          common_vendor.index.showToast({
            title: "保存成功",
            icon: "success"
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1500);
        } else {
          common_vendor.index.showToast({
            title: res.message || "保存失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/attendance/admin/rules.vue:116", "保存考勤规则失败:", err);
        common_vendor.index.showToast({
          title: "保存失败",
          icon: "none"
        });
      }).finally(() => {
        this.loading = false;
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t($data.clockInTime),
    b: $data.clockInTime,
    c: common_vendor.o(($event) => $data.clockInTime = $event.detail.value),
    d: common_vendor.t($data.clockOutTime),
    e: $data.clockOutTime,
    f: common_vendor.o(($event) => $data.clockOutTime = $event.detail.value),
    g: common_vendor.o((...args) => $options.saveRules && $options.saveRules(...args)),
    h: $data.loading
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/attendance/admin/rules.js.map
