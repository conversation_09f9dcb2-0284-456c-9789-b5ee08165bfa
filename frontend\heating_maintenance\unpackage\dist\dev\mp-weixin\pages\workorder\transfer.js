"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const _sfc_main = {
  data() {
    return {
      orderId: null,
      formData: {
        transferReason: "",
        repairUserId: null,
        repairUserName: ""
      },
      repairUsers: [],
      filteredUsers: [],
      searchKeyword: "",
      selectedUser: null,
      rules: {
        transferReason: [
          { required: true, message: "请输入转派原因" }
        ],
        repairUserId: [
          { required: true, message: "请选择转派人员" }
        ]
      }
    };
  },
  onLoad(options) {
    if (options.id) {
      this.orderId = options.id;
      this.loadRepairUsers();
    } else {
      this.showError("缺少工单ID");
      setTimeout(() => {
        common_vendor.index.navigateBack();
      }, 1500);
    }
  },
  methods: {
    // 加载维修人员列表
    loadRepairUsers() {
      utils_api.userApi.getInspectorList({ role: "repair" }).then((res) => {
        if (res.code === 200 && res.data) {
          common_vendor.index.__f__("log", "at pages/workorder/transfer.vue:123", "API返回的人员数据:", JSON.stringify(res.data));
          this.repairUsers = res.data.map((user) => ({
            userId: user.userId || user.id,
            name: user.name || user.userName,
            phone: user.phone || user.phoneNumber || ""
          }));
          this.filteredUsers = [...this.repairUsers];
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/workorder/transfer.vue:135", "获取人员列表失败:", err);
        this.showError("获取人员列表失败");
      });
    },
    // 判断用户是否被选中
    isUserSelected(user) {
      if (!this.selectedUser)
        return false;
      const userIdToCompare = user.userId || user.id;
      const selectedUserId = this.selectedUser.userId || this.selectedUser.id;
      return userIdToCompare === selectedUserId;
    },
    // 处理搜索输入
    handleSearch() {
      if (!this.searchKeyword) {
        this.filteredUsers = [...this.repairUsers];
        return;
      }
      const keyword = this.searchKeyword.toLowerCase();
      this.filteredUsers = this.repairUsers.filter((user) => {
        const name = (user.name || user.userName || "").toLowerCase();
        const phone = (user.phone || "").toLowerCase();
        return name.includes(keyword) || phone.includes(keyword);
      });
    },
    // 清除搜索
    clearSearch() {
      this.searchKeyword = "";
      this.filteredUsers = [...this.repairUsers];
    },
    // 显示人员选择器
    showUserPicker() {
      this.searchKeyword = "";
      this.filteredUsers = [...this.repairUsers];
      this.$refs.userPopup.open();
    },
    // 关闭人员选择器
    closeUserPicker() {
      this.$refs.userPopup.close();
    },
    // 选择人员
    selectUser(user) {
      common_vendor.index.__f__("log", "at pages/workorder/transfer.vue:187", "选择的用户:", JSON.stringify(user));
      this.selectedUser = { ...user };
    },
    // 确认人员选择
    confirmUserSelect() {
      common_vendor.index.__f__("log", "at pages/workorder/transfer.vue:193", "确认选择，当前选中:", JSON.stringify(this.selectedUser));
      if (this.selectedUser) {
        this.formData.repairUserId = this.selectedUser.userId || this.selectedUser.id;
        this.formData.repairUserName = this.selectedUser.name || this.selectedUser.userName;
        common_vendor.index.__f__("log", "at pages/workorder/transfer.vue:199", "更新表单数据:", JSON.stringify(this.formData));
        this.closeUserPicker();
      } else {
        this.showError("请选择一个维修人员");
      }
    },
    // 取消转派
    cancelTransfer() {
      common_vendor.index.navigateBack();
    },
    // 验证表单
    validateForm() {
      let isValid = true;
      for (const key in this.rules) {
        const value = this.formData[key];
        const rules = this.rules[key];
        for (const rule of rules) {
          if (rule.required && !value && value !== 0) {
            this.showError(rule.message);
            isValid = false;
            break;
          }
        }
        if (!isValid)
          break;
      }
      return isValid;
    },
    // 提交转派
    submitTransfer() {
      if (!this.validateForm())
        return;
      common_vendor.index.showModal({
        title: "转派确认",
        content: "该工单仅可转派一次，请确认目标负责人无误后再进行操作。",
        confirmText: "确认转派",
        cancelText: "再次确认",
        success: (res) => {
          if (res.confirm) {
            this.doTransfer();
          }
        }
      });
    },
    // 执行转派操作
    doTransfer() {
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      const params = {
        orderId: this.orderId,
        transferUserId: this.getCurrentUserId(),
        repairUserId: this.formData.repairUserId,
        transferReason: this.formData.transferReason
      };
      common_vendor.index.__f__("log", "at pages/workorder/transfer.vue:266", "提交的参数:", JSON.stringify(params));
      utils_api.workOrderApi.transferOrder(params).then((res) => {
        if (res.code === 200) {
          this.showSuccess("工单已转派");
          setTimeout(() => {
            common_vendor.index.navigateBack({
              delta: 1
            });
          }, 1500);
        } else {
          this.showError(res.message || "提交失败");
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/workorder/transfer.vue:283", "转派失败:", err);
        this.showError("网络异常，请稍后重试");
      }).finally(() => {
        common_vendor.index.hideLoading();
      });
    },
    // 获取当前用户ID
    getCurrentUserId() {
      return common_vendor.index.getStorageSync("userId") || 0;
    },
    // 显示成功提示
    showSuccess(message) {
      common_vendor.index.showToast({
        title: message,
        icon: "success"
      });
    },
    // 显示错误提示
    showError(message) {
      common_vendor.index.showToast({
        title: message,
        icon: "none"
      });
    }
  }
};
if (!Array) {
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  _easycom_uni_popup2();
}
const _easycom_uni_popup = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.formData.transferReason,
    b: common_vendor.o(($event) => $data.formData.transferReason = $event.detail.value),
    c: common_vendor.t($data.formData.repairUserName || "请选择转派人员"),
    d: !$data.formData.repairUserName ? 1 : "",
    e: common_vendor.o((...args) => $options.showUserPicker && $options.showUserPicker(...args)),
    f: common_vendor.o((...args) => $options.cancelTransfer && $options.cancelTransfer(...args)),
    g: common_vendor.o((...args) => $options.submitTransfer && $options.submitTransfer(...args)),
    h: common_vendor.o((...args) => $options.closeUserPicker && $options.closeUserPicker(...args)),
    i: common_vendor.o((...args) => $options.confirmUserSelect && $options.confirmUserSelect(...args)),
    j: common_vendor.o([($event) => $data.searchKeyword = $event.detail.value, (...args) => $options.handleSearch && $options.handleSearch(...args)]),
    k: $data.searchKeyword,
    l: $data.searchKeyword
  }, $data.searchKeyword ? {
    m: common_vendor.o((...args) => $options.clearSearch && $options.clearSearch(...args))
  } : {}, {
    n: common_vendor.f($data.filteredUsers, (user, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(user.name || user.userName),
        b: common_vendor.t(user.phone || ""),
        c: $options.isUserSelected(user)
      }, $options.isUserSelected(user) ? {} : {}, {
        d: index,
        e: $options.isUserSelected(user) ? 1 : "",
        f: common_vendor.o(($event) => $options.selectUser(user), index)
      });
    }),
    o: $data.filteredUsers.length === 0
  }, $data.filteredUsers.length === 0 ? {
    p: common_vendor.t($data.searchKeyword ? "未找到匹配的人员" : "暂无可选择的人员")
  } : {}, {
    q: common_vendor.sr("userPopup", "d1add81a-0"),
    r: common_vendor.p({
      type: "bottom"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/workorder/transfer.js.map
