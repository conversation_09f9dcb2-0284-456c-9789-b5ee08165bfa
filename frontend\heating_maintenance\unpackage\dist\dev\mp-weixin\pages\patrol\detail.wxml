<view class="patrol-detail-container"><view class="detail-card"><view class="card-header"><text class="card-title">{{a}}</text><view class="{{['plan-status', c]}}">{{b}}</view></view><view class="info-group"><view class="info-item"><text class="info-label">计划名称</text><text class="info-value">{{d}}</text></view><view class="info-item"><text class="info-label">计划编号</text><text class="info-value">{{e}}</text></view><view class="info-item"><text class="info-label">巡检类型</text><text class="info-value">{{f}}</text></view><view class="info-item"><text class="info-label">开始时间</text><text class="info-value">{{g}}</text></view><view class="info-item"><text class="info-label">结束时间</text><text class="info-value">{{h}}</text></view><view wx:if="{{i}}" class="info-item"><text class="info-label">巡检周期</text><text class="info-value">{{j}}</text></view><view class="info-item"><text class="info-label">巡检地点</text><text class="info-value">{{k}}</text></view><view class="info-item"><text class="info-label">负责人</text><text class="info-value">{{l}}</text></view></view><view wx:if="{{m}}" class="plan-desc"><text class="desc-title">计划描述：</text><text class="desc-content">{{n}}</text></view></view><view class="detail-card"><view class="card-header"><text class="card-title">巡检任务</text></view><view class="task-list"><view wx:for="{{o}}" wx:for-item="task" wx:key="s" class="task-item" bindtap="{{task.t}}"><view class="task-header"><view class="task-left"><view class="{{['task-status', task.a]}}"></view><text class="task-title">{{task.b}}</text></view><text class="{{['iconfont', task.c]}}"></text></view><view wx:if="{{task.d}}" class="task-details"><view class="detail-row"><text class="detail-label">巡检对象:</text><text class="detail-value">{{task.e}}</text></view><view class="detail-row"><text class="detail-label">巡检点:</text><text class="detail-value">{{task.f}}</text></view><view class="detail-row"><text class="detail-label">巡检内容:</text><text class="detail-value">{{task.g}}</text></view><view class="detail-row"><text class="detail-label">标 准 值:</text><text class="detail-value">{{task.h}} {{task.i}}</text></view><view wx:if="{{task.j}}" class="detail-row"><text class="detail-label">实际值</text><text class="{{['detail-value', task.l && 'value-abnormal']}}">{{task.k}}</text></view><view wx:if="{{task.m}}" class="detail-row"><text class="detail-label">完成时间</text><text class="detail-value">{{task.n}}</text></view><view wx:if="{{task.o}}" class="detail-row"><text class="detail-label">备注</text><text class="detail-value">{{task.p}}</text></view><view wx:if="{{task.q}}" class="task-images"><text class="images-title">巡检照片</text><view class="image-list"><image wx:for="{{task.r}}" wx:for-item="img" wx:key="a" src="{{img.b}}" bindtap="{{img.c}}" mode="aspectFill"></image></view></view></view></view></view></view></view>