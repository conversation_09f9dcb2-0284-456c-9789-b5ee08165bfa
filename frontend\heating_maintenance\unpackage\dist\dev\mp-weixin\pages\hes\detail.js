"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const _sfc_main = {
  data() {
    return {
      stationId: "",
      stationDetail: {},
      // 原始API返回的详情数据
      stationInfo: {
        id: "",
        name: "",
        status: "normal",
        address: "",
        manager: "",
        phone: "",
        startDate: "",
        heatingArea: 0,
        coverCommunities: 0,
        coverUsers: 0
      },
      runningData: [],
      communities: [],
      devices: [],
      alarms: [],
      isLoading: false
    };
  },
  onLoad(options) {
    this.stationId = options.id;
    const eventChannel = this.getOpenerEventChannel();
    eventChannel.on("acceptStationDetail", (data) => {
      common_vendor.index.__f__("log", "at pages/hes/detail.vue:202", "从列表页接收到详情数据:", data);
      this.stationDetail = data;
      this.parseStationDetail();
    });
    if (!this.stationDetail || !this.stationDetail.basic_info) {
      this.loadStationDetail();
    }
  },
  methods: {
    // 加载换热站详情
    loadStationDetail() {
      this.isLoading = true;
      common_vendor.index.showLoading({ title: "加载中..." });
      utils_api.heatingStationApi.getDetail(this.stationId).then((res) => {
        common_vendor.index.__f__("log", "at pages/hes/detail.vue:220", "换热站详情API返回:", res);
        if (res.code === 200 && res.data) {
          this.stationDetail = res.data;
          this.parseStationDetail();
        } else {
          common_vendor.index.showToast({
            title: res.message || "获取详情失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/hes/detail.vue:232", "获取换热站详情失败:", err);
        common_vendor.index.showToast({
          title: "获取详情失败，请重试",
          icon: "none"
        });
      }).finally(() => {
        this.isLoading = false;
        common_vendor.index.hideLoading();
      });
    },
    // 解析站点详情数据
    parseStationDetail() {
      if (!this.stationDetail)
        return;
      const basicInfo = this.stationDetail.basic_info || {};
      const realtimeData = this.stationDetail.realtime_data || {};
      this.stationInfo = {
        id: basicInfo.id || this.stationId,
        name: basicInfo.name || "",
        status: basicInfo.status || "normal",
        address: basicInfo.address || "",
        manager: basicInfo.manager_name || "",
        phone: basicInfo.contact_phone || "",
        startDate: basicInfo.installation_date || "",
        heatingArea: basicInfo.heating_area || 0,
        coverCommunities: basicInfo.community_count || 0,
        coverUsers: basicInfo.user_count || 0
      };
      this.parseRunningData(realtimeData);
      this.parseDevices(realtimeData == null ? void 0 : realtimeData.equipment_status);
      this.loadAlarms();
      this.loadCommunities();
    },
    // 解析运行数据
    parseRunningData(realtimeData) {
      if (!realtimeData)
        return;
      const primary = realtimeData.primary_system || {};
      const secondary = realtimeData.secondary_system || {};
      this.runningData = [
        {
          label: "一次供水温度",
          value: primary.supply_temp ? `${primary.supply_temp}°C` : "--",
          isWarning: primary.supply_temp > 95,
          isDanger: primary.supply_temp > 105
        },
        {
          label: "一次回水温度",
          value: primary.return_temp ? `${primary.return_temp}°C` : "--",
          isWarning: primary.return_temp > 80,
          isDanger: primary.return_temp > 90
        },
        {
          label: "二次供水温度",
          value: secondary.supply_temp ? `${secondary.supply_temp}°C` : "--",
          isWarning: secondary.supply_temp > 75,
          isDanger: secondary.supply_temp > 85
        },
        {
          label: "二次回水温度",
          value: secondary.return_temp ? `${secondary.return_temp}°C` : "--",
          isWarning: secondary.return_temp > 65,
          isDanger: secondary.return_temp > 75
        },
        {
          label: "一次供水压力",
          value: primary.supply_pressure ? `${primary.supply_pressure}MPa` : "--",
          isWarning: primary.supply_pressure > 0.8,
          isDanger: primary.supply_pressure > 1
        },
        {
          label: "一次回水压力",
          value: primary.return_pressure ? `${primary.return_pressure}MPa` : "--",
          isWarning: primary.return_pressure > 0.5,
          isDanger: primary.return_pressure > 0.7
        }
      ];
      if (primary.flow_rate) {
        this.runningData.push({
          label: "流量",
          value: `${primary.flow_rate}m³/h`,
          isWarning: false,
          isDanger: false
        });
      }
      if (primary.power) {
        this.runningData.push({
          label: "热负荷",
          value: `${(primary.power / 1e3).toFixed(2)}MW`,
          isWarning: primary.power > 1800,
          isDanger: primary.power > 2e3
        });
      }
    },
    // 解析设备数据
    parseDevices(equipmentStatus) {
      if (!equipmentStatus)
        return;
      this.devices = [];
      if (equipmentStatus.pumps && equipmentStatus.pumps.length > 0) {
        equipmentStatus.pumps.forEach((pump, index) => {
          this.devices.push({
            deviceId: pump.id || `pump_${index + 1}`,
            name: pump.name || `水泵 ${index + 1}`,
            type: "泵",
            status: pump.status === "running" ? "online" : pump.status === "fault" ? "fault" : "offline",
            alarmCount: 0
            // 告警数可以从另外的接口获取
          });
        });
      }
      if (equipmentStatus.valves && equipmentStatus.valves.length > 0) {
        equipmentStatus.valves.forEach((valve, index) => {
          this.devices.push({
            deviceId: valve.id || `valve_${index + 1}`,
            name: valve.name || `阀门 ${index + 1}`,
            type: "阀门",
            status: valve.status === "normal" ? "online" : "fault",
            alarmCount: 0
          });
        });
      }
    },
    // 加载社区列表
    loadCommunities() {
      if (this.stationDetail && this.stationDetail.communities) {
        this.communities = this.stationDetail.communities.map((item) => ({
          id: item.id,
          name: item.name,
          address: item.address,
          buildingCount: item.building_count,
          userCount: item.user_count
        }));
      } else {
        this.communities = [];
      }
    },
    // 加载告警记录
    loadAlarms() {
      utils_api.heatingStationApi.getAlarmList({ hes_id: this.stationId, limit: 5 }).then((res) => {
        if (res.code === 200 && res.data && res.data.list) {
          this.alarms = res.data.list.map((alarm) => ({
            id: alarm.id,
            title: alarm.alarm_name,
            level: this.getAlarmLevel(alarm.level),
            time: alarm.start_time,
            deviceId: alarm.device_id
          }));
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/hes/detail.vue:413", "获取告警列表失败:", err);
        this.alarms = [];
      });
    },
    // 获取告警级别
    getAlarmLevel(level) {
      if (level === "urgent" || level === "critical")
        return "error";
      if (level === "warning" || level === "notice")
        return "warning";
      return "warning";
    },
    // 刷新数据
    refreshData() {
      common_vendor.index.showLoading({
        title: "刷新中..."
      });
      this.loadStationDetail();
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        "online": "在线",
        "offline": "离线",
        "normal": "正常",
        "warning": "异常",
        "fault": "故障"
      };
      return statusMap[status] || "未知";
    },
    // 格式化日期
    formatDate(dateString) {
      if (!dateString)
        return "未知";
      try {
        const date = new Date(dateString);
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/hes/detail.vue:454", "日期格式化错误:", e);
        return dateString;
      }
    },
    // 导航到设备列表
    navigateToDeviceList() {
      common_vendor.index.navigateTo({
        url: `/pages/device/list?stationId=${this.stationId}`
      });
    },
    // 导航到设备详情
    navigateToDeviceDetail(deviceId) {
      common_vendor.index.navigateTo({
        url: `/pages/device/detail?id=${deviceId}`
      });
    },
    // 导航到告警列表
    navigateToAlarmList() {
      common_vendor.index.navigateTo({
        url: `/pages/hes/alarms?id=${this.stationId}`
      });
    },
    // 导航到远程控制页面
    navigateToControl() {
      common_vendor.index.navigateTo({
        url: `/pages/hes/control?id=${this.stationId}`
      });
    },
    // 导航到运行报表页面
    navigateToReport() {
      common_vendor.index.navigateTo({
        url: `/pages/hes/report?id=${this.stationId}`
      });
    },
    // 导航到维护记录页面
    navigateToMaintenance() {
      common_vendor.index.navigateTo({
        url: `/pages/hes/maintenance?id=${this.stationId}`
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($options.getStatusText($data.stationInfo.status)),
    b: common_vendor.n($data.stationInfo.status),
    c: common_vendor.t($data.stationInfo.name),
    d: common_vendor.t($data.stationInfo.id),
    e: common_vendor.t($data.stationInfo.address),
    f: common_vendor.t($data.stationInfo.manager || "未分配"),
    g: common_vendor.t($data.stationInfo.phone || "未设置"),
    h: common_vendor.t($options.formatDate($data.stationInfo.startDate)),
    i: common_vendor.o((...args) => $options.refreshData && $options.refreshData(...args)),
    j: common_vendor.f($data.runningData, (item, index, i0) => {
      return {
        a: common_vendor.t(item.value),
        b: item.isWarning ? 1 : "",
        c: item.isDanger ? 1 : "",
        d: common_vendor.t(item.label),
        e: index
      };
    }),
    k: common_vendor.t($data.stationInfo.heatingArea || 0),
    l: common_vendor.t($data.stationInfo.coverCommunities || 0),
    m: common_vendor.t($data.stationInfo.coverUsers || 0),
    n: common_vendor.f($data.communities, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: common_vendor.t(item.address),
        c: common_vendor.t(item.buildingCount),
        d: common_vendor.t(item.userCount),
        e: index
      };
    }),
    o: common_vendor.o((...args) => $options.navigateToDeviceList && $options.navigateToDeviceList(...args)),
    p: common_vendor.f($data.devices, (device, index, i0) => {
      return common_vendor.e({
        a: common_vendor.n(device.status),
        b: common_vendor.t(device.name),
        c: common_vendor.t(device.type),
        d: device.alarmCount > 0
      }, device.alarmCount > 0 ? {
        e: common_vendor.t(device.alarmCount)
      } : {}, {
        f: index,
        g: common_vendor.o(($event) => $options.navigateToDeviceDetail(device.deviceId), index)
      });
    }),
    q: $data.devices.length === 0
  }, $data.devices.length === 0 ? {} : {}, {
    r: $data.alarms.length > 0
  }, $data.alarms.length > 0 ? {
    s: common_vendor.o((...args) => $options.navigateToAlarmList && $options.navigateToAlarmList(...args)),
    t: common_vendor.f($data.alarms, (alarm, index, i0) => {
      return {
        a: common_vendor.n(alarm.level),
        b: common_vendor.t(alarm.title),
        c: common_vendor.t(alarm.time),
        d: index
      };
    })
  } : {}, {
    v: common_vendor.o((...args) => $options.navigateToControl && $options.navigateToControl(...args)),
    w: common_vendor.o((...args) => $options.navigateToReport && $options.navigateToReport(...args)),
    x: common_vendor.o((...args) => $options.navigateToMaintenance && $options.navigateToMaintenance(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/hes/detail.js.map
