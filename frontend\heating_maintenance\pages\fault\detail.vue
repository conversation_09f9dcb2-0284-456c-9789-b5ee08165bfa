<template>
	<view class="fault-detail-container">
		<!-- 基本信息卡片 -->
		<view class="detail-card">
			<view class="card-header">
				<text class="card-title">故障信息</text>
				<view class="fault-status" :class="getFaultStatusClass(faultInfo.fault_status)">{{ getStatusText(faultInfo.fault_status) }}</view>
			</view>
			
			<view class="info-group">
				<view class="info-item">
					<text class="info-label">故障编号</text>
					<text class="info-value">{{ faultInfo.fault_no || '暂无' }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">换热站</text>
					<text class="info-value">{{ faultInfo.heat_unit_name || '暂无' }}</text>
				</view>
				<view class="info-item" v-if="faultInfo.address">
					<text class="info-label">详细地址</text>
					<text class="info-value">{{ faultInfo.address }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">故障类型</text>
					<text class="info-value">{{ faultInfo.fault_type || '暂无' }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">故障等级</text>
					<text class="info-value level-tag" :class="getFaultLevelClass(faultInfo.fault_level)">{{ faultInfo.fault_level || '未知' }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">故障来源</text>
					<text class="info-value">{{ faultInfo.fault_source || '暂无' }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">发生时间</text>
					<text class="info-value">{{ faultInfo.occur_time || '暂无' }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">上报人员</text>
					<text class="info-value">{{ faultInfo.report_user_name || '暂无' }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">上报时间</text>
					<text class="info-value">{{ faultInfo.report_time || '暂无' }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">创建时间</text>
					<text class="info-value">{{ faultInfo.created_time || '暂无' }}</text>
				</view>
			</view>
		</view>
		
		<!-- 故障描述 -->
		<view class="detail-card">
			<view class="card-header">
				<text class="card-title">故障描述</text>
			</view>
			<view class="fault-desc">{{ faultInfo.fault_desc || '暂无描述' }}</view>
		</view>
		
		<!-- 图片附件 -->
		<view class="detail-card" v-if="images && images.length > 0">
			<view class="card-header">
				<text class="card-title">图片附件</text>
			</view>
			<view class="image-grid">
				<view class="image-item" v-for="(image, index) in images" :key="index" @click="previewImage(index)">
					<image :src="getFullImageUrl(image)" mode="aspectFill"></image>
				</view>
			</view>
		</view>
		
		<!-- 视频附件 -->
		<view class="detail-card" v-if="video">
			<view class="card-header">
				<text class="card-title">视频附件</text>
			</view>
			<view class="video-container">
				<video :src="getFullVideoUrl(video)" controls
				       @error="onVideoError" 
				       show-center-play-btn="true" 
				       enable-progress-gesture="true"></video>
			</view>
		</view>
		
		<!-- 工单信息卡片 - 优化版 -->
		<view class="detail-card" v-if="workOrderInfo">
			<view class="card-header">
				<text class="card-title">工单信息</text>
				<view class="work-order-status" :class="getWorkOrderStatusClass(workOrderInfo.status)">{{ getWorkOrderStatusText(workOrderInfo.status) }}</view>
			</view>
			
			<view class="info-group">
				<view class="info-item">
					<text class="info-label">工单编号</text>
					<text class="info-value">{{ workOrderInfo.code }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">生成时间</text>
					<text class="info-value">{{ workOrderInfo.create_time }}</text>
				</view>
				<view class="info-item" v-if="workOrderInfo.assignee">
					<text class="info-label">维修人员</text>
					<text class="info-value">{{ workOrderInfo.assignee }}</text>
				</view>
				<view class="info-item" v-if="workOrderInfo.phone">
					<text class="info-label">联系电话</text>
					<text class="info-value">{{ workOrderInfo.phone }}</text>
				</view>
				<view class="info-item" v-if="workOrderInfo.repair_content">
					<text class="info-label">维修内容</text>
					<text class="info-value">{{ workOrderInfo.repair_content }}</text>
				</view>
				<view class="info-item" v-if="workOrderInfo.repair_result">
					<text class="info-label">维修结果</text>
					<text class="info-value">{{ workOrderInfo.repair_result }}</text>
				</view>
				<view class="info-item" v-if="workOrderInfo.repair_materials">
					<text class="info-label">维修耗材</text>
					<text class="info-value">{{ workOrderInfo.repair_materials }}</text>
				</view>
				<view class="info-item" v-if="workOrderInfo.complete_time">
					<text class="info-label">完成时间</text>
					<text class="info-value">{{ workOrderInfo.complete_time }}</text>
				</view>
			</view>
		</view>
		
		<!-- 操作日志 - 优化版 -->
		<view class="detail-card" v-if="operationLogs && operationLogs.length > 0">
			<view class="card-header">
				<text class="card-title">处理记录</text>
			</view>
			<view class="timeline">
				<view class="log-item" v-for="(log, index) in operationLogs" :key="index">
					<view class="timeline-dot" :class="getTimelineDotClass(log.operationType)"></view>
					<view class="timeline-line" v-if="index !== operationLogs.length - 1"></view>
					<view class="log-content">
						<view class="log-header">
							<text class="log-type">{{ log.operationType }}</text>
							<text class="log-time">{{ log.createdAt }}</text>
						</view>
						<view class="log-body">
							<text class="log-desc">{{ log.operationDesc }}</text>
							<text class="log-operator" v-if="log.operatorName">操作人：{{ log.operatorName }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 操作按钮 -->
		 <PermissionCheck permission="fault:fault-handle">
		<view class="action-buttons" v-if="isAdmin && faultInfo.fault_status === '待确认'">
			<button class="btn-confirm" @click="confirmFault">确认故障</button>
			<button class="btn-reject" @click="rejectFault">退回上报</button>
		</view>
		</PermissionCheck>
		<!-- 提示信息 -->
		<view class="status-tip" v-if="faultInfo.fault_status === '已退回'">
			<text>该故障上报已被退回，请检查信息准确性后重新上报。</text>
		</view>
	</view>
</template>

<script>
	import { faultApi } from '@/utils/api.js';
	import uploadUtils from '@/utils/upload.js';
	import PermissionCheck from "@/components/PermissionCheck.vue"; // 导入权限检查组件
	export default {
		components: {
		  PermissionCheck, // 本地注册组件
		},
		data() {
			return {
				faultId: null,
				faultInfo: {},
				images: [],
				video: '',
				loading: true,
				loadError: false,
				isAdmin: true,
				isDev: process.env.NODE_ENV === 'development',
				operationLogs: [],
				// 工单信息
				workOrderInfo: null
			}
		},
		onShow() {
			this.loadFaultDetail();
		},
		onLoad(options) {
			this.faultId = options.id;
			if (this.faultId) {
				this.loadFaultDetail();
			} else {
				uni.showToast({
					title: '参数错误，无法获取故障详情',
					icon: 'none'
				});
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}
		},
		methods: {
			// 加载故障详情
			loadFaultDetail() {
				// 显示加载提示
				uni.showLoading({
					title: '加载中...'
				});
				
				this.loading = true;
				this.loadError = false;
				
				// 调用API获取故障详情
				faultApi.getFaultDetail(this.faultId)
					.then(res => {
						if (res.code === 200) {
							// 处理接口返回的数据结构
							console.log('故障详情数据:', res.data);
							if (res.data && res.data.fault_info) {
								this.faultInfo = res.data.fault_info;
							}
							
							// 处理图片列表
							if (res.data && Array.isArray(res.data.images)) {
								this.images = res.data.images;
							} else {
								this.images = [];
							}
							
							// 处理视频
							if (res.data && res.data.video) {
								this.video = res.data.video;
								console.log('视频地址:', this.video);
							} else {
								this.video = '';
							}
							
							// 处理操作日志
							if (res.data && Array.isArray(res.data.operation_logs)) {
								// 处理操作日志中的日期和操作人信息
								this.operationLogs = res.data.operation_logs.map(log => {
									// 创建新对象避免直接修改原始数据
									const processedLog = {...log};
									
									// 处理操作人ID显示为时间的问题
									if (processedLog.operatorId && !processedLog.operatorName) {
										// 如果没有操作人名称，但有ID，则显示"操作人ID: xxx"
										processedLog.operatorName = "操作人ID: " + processedLog.operatorId;
									}
									
									// 确保日期显示正确
									if (processedLog.createdAt) {
										// 如果createdAt是数字(时间戳)或看起来像数字，尝试格式化
										if (!isNaN(processedLog.createdAt) || /^\d+$/.test(processedLog.createdAt)) {
											try {
												// 尝试将时间戳转换为日期
												const date = new Date(parseInt(processedLog.createdAt));
												if (!isNaN(date.getTime())) {
													processedLog.createdAt = this.formatDateTime(date);
												}
											} catch (e) {
												console.error("日期转换错误:", e);
											}
										}
									}
									
									return processedLog;
								});
							} else {
								this.operationLogs = [];
							}

				
							// 处理工单信息
							if (res.data && res.data.work_order) {
								this.workOrderInfo = res.data.work_order;
							} else {
								this.workOrderInfo = null;
							}
						} else {
							this.loadError = true;
							uni.showToast({
								title: res.message || '获取故障详情失败',
								icon: 'none'
							});
						}
					})
					.catch(err => {
						console.error('获取故障详情异常:', err);
						this.loadError = true;
						uni.showToast({
							title: '网络异常，请稍后重试',
							icon: 'none'
						});
					})
					.finally(() => {
						this.loading = false;
						uni.hideLoading();
					});
			},
			
			// 获取完整的图片URL
			getFullImageUrl(path) {
				console.log(uploadUtils.getFileUrl(path))
				return uploadUtils.getFileUrl(path);
			},
			
			// 获取完整的视频URL
			getFullVideoUrl(path) {
				// 处理视频URL
				if (!path) return '';
				
				try {
					// 特殊处理以@开头的URL
					if (path && path.startsWith('@http')) {
						const cleanUrl = path.substring(1); // 去除@符号
						console.log('视频路径以@http开头，清理后:', cleanUrl);
						return cleanUrl;
					}
					
					// 如果已经是完整URL，直接返回
					if (path.startsWith('http')) {
						console.log('视频已是完整URL，直接返回:', path);
						return path;
					}
					
					// 使用工具方法获取完整URL
					const fullUrl = uploadUtils.getFileUrl(path);
					console.log('处理后的视频URL:', fullUrl);
					
					// 添加时间戳避免缓存问题
					return fullUrl + '?t=' + new Date().getTime();
				} catch (err) {
					console.error('视频URL处理出错:', err);
					return path; // 出错时返回原路径
				}
			},
			
			// 获取状态文本
			getStatusText(status) {
				const statusMap = {
					'待确认': '待确认',
					'已确认': '已确认',
					'已退回': '已退回',
				};
				return statusMap[status] || '未知';
			},
			
			// 获取状态对应的CSS类名
			getFaultStatusClass(status) {
				const statusClassMap = {
					'待确认': 'pending',
					'已确认': 'confirmed',
					'已退回': 'rejected',
					'pending': '待确认',
					'confirmed': '已确认',
					'rejected': '已退回',
				};
				return statusClassMap[status] || 'pending';
			},
			// 格式化日期时间
			formatDateTime(date) {
				if (!date) return "";
				
				// 如果是时间戳（数字）
				if (typeof date === 'number') {
					const d = new Date(date);
					if (!isNaN(d.getTime())) {
						return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, "0")}-${String(d.getDate()).padStart(2, "0")} ${String(d.getHours()).padStart(2, "0")}:${String(d.getMinutes()).padStart(2, "0")}`;
					}
				}
				
				// 如果是Date对象
				if (date instanceof Date) {
					return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")} ${String(date.getHours()).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
				}
				
				// 如果是字符串但可能需要标准化格式
				if (typeof date === 'string') {
					// 尝试解析日期字符串，如果解析失败则返回原始字符串
					try {
						const parsedDate = new Date(date);
						if (!isNaN(parsedDate.getTime())) {
							return `${parsedDate.getFullYear()}-${String(parsedDate.getMonth() + 1).padStart(2, "0")}-${String(parsedDate.getDate()).padStart(2, "0")} ${String(parsedDate.getHours()).padStart(2, "0")}:${String(parsedDate.getMinutes()).padStart(2, "0")}`;
						}
					} catch (e) {
						console.error("日期解析错误:", e);
					}
					
					// 如果解析失败，返回原始字符串
					return date;
				}
				
				// 其他情况，尝试转为字符串
				return String(date);
			},
			// 获取等级对应的CSS类名
			getFaultLevelClass(level) {
				const levelMap = {
					'minor': 'minor',
					'normal': 'normal',
					'critical': 'critical',
					'emergency': 'emergency',
					'轻微': 'minor',
					'一般': 'normal',
					'严重': 'critical',
					'紧急': 'emergency'
				};
				return levelMap[level] || 'normal';
			},
			
			// 获取工单状态文本
			getWorkOrderStatusText(status) {
				const statusMap = {
					'pending': '待接单',
					'in_progress': '处理中',
					'completed': '已完成',
					'待接单': '待接单',
					'处理中': '处理中',
					'已完成': '已完成'
				};
				return statusMap[status] || '未知';
			},
			
			// 获取工单状态对应的CSS类名
			getWorkOrderStatusClass(status) {
				const statusClassMap = {
					'pending': 'pending',
					'in_progress': 'in_progress',
					'completed': 'completed',
					'待接单': 'pending',
					'处理中': 'in_progress',
					'已完成': 'completed'
				};
				return statusClassMap[status] || 'pending';
			},
			
			// 预览图片
			previewImage(index) {
				if (this.images && this.images.length > 0) {
					// 将所有图片路径转换为完整URL
					const fullUrls = this.images.map(path => this.getFullImageUrl(path));
					uni.previewImage({
						urls: fullUrls,
						current: fullUrls[index]
					});
				}
			},
			
			// 确认故障
			confirmFault() {
				const operatorId = uni.getStorageSync('userId') || null; // 获取当前操作员ID
				const targetStatus = '已确认';

				if (!operatorId) {
					uni.showToast({ title: '无法获取操作员信息', icon: 'none' });
					return;
				}

				// 显示 Modal 再次确认
				uni.showModal({
					title: '确认操作',
					content: '您正在确认该故障为有效上报，请确保已核实故障真实性\n（确认后将生成待处理工单）',
					confirmText: '确认',
					cancelText: '取消',
					success: (modalRes) => {
						if (modalRes.confirm) {
							uni.showLoading({
								title: '提交中...'
							});
							
							// 构建请求数据
							const requestData = {
								fault_id: this.faultId,
								operator_id: operatorId,
								fault_status: targetStatus,
								heat_unit_id: this.faultInfo.heat_unit_id // 添加热用户ID
							};
							
							// 调用API
							faultApi.updateFaultStatus(requestData)
								.then(apiRes => {
									uni.hideLoading();
									if (apiRes.code === 200) {
										uni.showToast({
											title: '故障已确认',
											icon: 'success',
											duration: 1500 // 稍长提示时间
										});
										// 确认成功后返回上一页
										setTimeout(() => {
											// 设置一个标记，表示发生了状态变更，需要刷新列表
											getApp().globalData = getApp().globalData || {};
											getApp().globalData.refreshFaultList = true;
											uni.navigateBack();
										}, 1500);
									} else {
										uni.showToast({ title: apiRes.message || '操作失败', icon: 'none' });
									}
								})
								.catch(err => {
									uni.hideLoading();
									console.error('确认故障 API 调用失败:', err);
									uni.showToast({ title: '网络错误，请稍后重试', icon: 'none' });
								});
						}
					} // success end
				}); // showModal end
			},
			
			// 退回故障上报
			rejectFault() {
				const operatorId = uni.getStorageSync('userId') || null; // 获取当前操作员ID
				const targetStatus = '已退回';
				
				if (!operatorId) {
					uni.showToast({ title: '无法获取操作员信息', icon: 'none' });
					return;
				}
				uni.showModal({
					title: '退回上报',
					content: '确认退回此故障上报？',
					confirmText: '确认',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							// 实际应用中这里会调用API退回故障上报
							uni.showLoading({
								title: '提交中...'
							});
							
							// 构建请求数据
							const requestData = {
								fault_id: this.faultId,
								operator_id: operatorId,
								fault_status: targetStatus
							};
							
							// 调用API
							faultApi.updateFaultStatus(requestData)
								.then(apiRes => {
									uni.hideLoading();
									if (apiRes.code === 200) {
										uni.showToast({
											title: '故障已退回',
											icon: 'success',
											duration: 1500 // 稍长提示时间
										});
										// 确认成功后返回上一页
										setTimeout(() => {
											// 设置一个标记，表示发生了状态变更，需要刷新列表
											getApp().globalData = getApp().globalData || {};
											getApp().globalData.refreshFaultList = true;
											uni.navigateBack();
										}, 1500);
									} else {
										uni.showToast({ title: apiRes.message || '操作失败', icon: 'none' });
									}
								})
								.catch(err => {
									uni.hideLoading();
									console.error('故障退回 API 调用失败:', err);
									uni.showToast({ title: '网络错误，请稍后重试', icon: 'none' });
								});
						}
					}
				});
			},
			
			// 视频加载错误处理
			onVideoError(e) {
				console.error('视频加载失败:', e.detail);
				console.error('视频路径:', this.video);
				console.error('处理后URL:', this.getFullVideoUrl(this.video));
				this.videoLoadError = true;
				
				// 尝试加载失败时，记录到日志并显示提示
				uni.showToast({
					title: '视频加载失败，请尝试在浏览器中查看',
					icon: 'none',
					duration: 2000
				});
			},
			
			// 在浏览器中打开视频
			openVideoInBrowser() {
				let url = this.getFullVideoUrl(this.video);
				
				// 检查URL是否有效
				if (!url) {
					uni.showToast({
						title: '视频URL无效',
						icon: 'none'
					});
					return;
				}
				
				console.log('在浏览器中打开视频:', url);
				
				// 使用系统浏览器打开
				// #ifdef APP-PLUS
				plus.runtime.openURL(url, (err) => {
					if (err) {
						console.error('打开浏览器失败:', err);
						uni.showToast({
							title: '打开浏览器失败',
							icon: 'none'
						});
					}
				});
				// #endif
				
				// #ifdef H5
				window.open(url, '_blank');
				// #endif
				
				// #ifdef MP
				uni.setClipboardData({
					data: url,
					success: () => {
						uni.showToast({
							title: 'URL已复制，请在浏览器中打开',
							icon: 'none'
						});
					}
				});
				// #endif
			},
			
			// 获取时间轴点类名
			getTimelineDotClass(operationType) {
				// 基于操作类型的部分关键词匹配
				if (!operationType) return 'dot-unknown';
				
				if (operationType.includes('上报') || operationType.includes('创建')) {
					return 'dot-report';
				} else if (operationType.includes('确认') || operationType.includes('接单')) {
					return 'dot-confirm';
				} else if (operationType.includes('退回') || operationType.includes('拒绝')) {
					return 'dot-reject';
				} else if (operationType.includes('维修') || operationType.includes('处理')) {
					return 'dot-repair';
				} else if (operationType.includes('完成') || operationType.includes('结束')) {
					return 'dot-complete';
				} else if (operationType.includes('转派') || operationType.includes('分配')) {
					return 'dot-assign';
				} else {
					return 'dot-unknown';
				}
			}
		}
	}
</script>

<style lang="scss">
	.fault-detail-container {
		padding: 30rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}
	
	.detail-card {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
		
		.card-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 30rpx;
			
			.card-title {
				font-size: 32rpx;
				font-weight: bold;
				color: $uni-text-color;
				position: relative;
				padding-left: 20rpx;
				
				&::before {
					content: '';
					position: absolute;
					left: 0;
					top: 50%;
					transform: translateY(-50%);
					width: 8rpx;
					height: 32rpx;
					background-color: $uni-color-primary;
					border-radius: 4rpx;
				}
			}
			
			.fault-status, .work-order-status {
				padding: 6rpx 20rpx;
				border-radius: 4rpx;
				font-size: 24rpx;
				
				&.pending {
					background-color: rgba(24, 144, 255, 0.1);
					color: $uni-color-primary;
				}
				
				&.confirmed {
					background-color: rgba(82, 196, 26, 0.1);
					color: $uni-color-success;
				}
				
				&.rejected {
					background-color: rgba(245, 34, 45, 0.1);
					color: $uni-color-error;
				}
				
				&.in_progress {
					background-color: rgba(250, 173, 20, 0.1);
					color: $uni-color-warning;
				}
				
				&.completed {
					background-color: rgba(82, 196, 26, 0.1);
					color: $uni-color-success;
				}
			}
		}
		
		.info-group {
			display: flex;
			flex-direction: column;
			gap: 20rpx;
			
			.info-item {
				display: flex;
				align-items: flex-start;
				
				.info-label {
					min-width: 160rpx;
					font-size: 28rpx;
					color: $uni-text-color-grey;
				}
				
				.info-value {
					flex: 1;
					font-size: 28rpx;
					color: $uni-text-color;
					word-break: break-all;
					
					&.level-tag {
						display: inline-block;
						padding: 4rpx 16rpx;
						border-radius: 4rpx;
						
						&.minor {
							background-color: rgba(24, 144, 255, 0.1);
							color: $uni-color-primary;
						}
						
						&.normal {
							background-color: rgba(82, 196, 26, 0.1);
							color: $uni-color-success;
						}
						
						&.critical {
							background-color: rgba(250, 173, 20, 0.1);
							color: $uni-color-warning;
						}
						
						&.emergency {
							background-color: rgba(245, 34, 45, 0.1);
							color: $uni-color-error;
						}
					}
				}
			}
		}
		
		.fault-desc {
			font-size: 28rpx;
			color: $uni-text-color;
			line-height: 1.6;
		}
		
		.image-grid {
			display: flex;
			flex-wrap: wrap;
			margin: 0 -10rpx;
			
			.image-item {
				width: 33.33%;
				padding: 10rpx;
				box-sizing: border-box;
				
				image {
					width: 100%;
					height: 200rpx;
					border-radius: 8rpx;
					background-color: #f5f5f5;
				}
			}
		}
		
		.video-container {
			margin-top: 20rpx;
			
			video {
				width: 100%;
				height: 400rpx;
				border-radius: 8rpx;
				background-color: #000;
			}
			
			.video-fallback {
				width: 100%;
				height: 400rpx;
				background-color: #f5f5f5;
				border-radius: 8rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				
				.play-icon {
					width: 80rpx;
					height: 80rpx;
					margin-bottom: 20rpx;
				}
				
				.fallback-text {
					font-size: 28rpx;
					color: #666;
					margin-bottom: 20rpx;
				}
				
				.btn-open-browser {
					background-color: $uni-color-primary;
					color: #fff;
					font-size: 28rpx;
					padding: 10rpx 30rpx;
					border-radius: 8rpx;
					margin-top: 20rpx;
				}
			}
		}
		
		.video-debug-info {
			margin-top: 20rpx;
			padding: 20rpx;
			background-color: #f8f8f8;
			border-radius: 8rpx;
			font-size: 24rpx;
			
			.debug-title {
				font-weight: bold;
				margin-bottom: 10rpx;
				display: block;
			}
			
			.debug-text {
				color: #666;
				word-break: break-all;
				display: block;
				margin-bottom: 6rpx;
			}
		}
		
		.btn-view-order {
			margin-top: 20rpx;
			background-color: #f0f8ff;
			border: 1rpx solid $uni-color-primary;
			border-radius: 8rpx;
			padding: 16rpx 0;
			text-align: center;
			
			text {
				color: $uni-color-primary;
				font-size: 28rpx;
			}
		}
	}
	
	.action-buttons {
		display: flex;
		gap: 30rpx;
		margin-top: 50rpx;
		margin-bottom: 30rpx;
		
		button {
			flex: 1;
			margin: 0;
			height: 88rpx;
			line-height: 88rpx;
			font-size: 30rpx;
			border-radius: 8rpx;
			
			&.btn-confirm {
				background-color: $uni-color-primary;
				color: #fff;
			}
			
			&.btn-reject {
				background-color: #fff;
				color: $uni-color-error;
				border: 1rpx solid $uni-color-error;
			}
		}
	}
	
	.status-tip {
		background-color: rgba(245, 34, 45, 0.1);
		padding: 20rpx 30rpx;
		border-radius: 8rpx;
		margin: 30rpx 0;
		
		text {
			font-size: 28rpx;
			color: $uni-color-error;
		}
	}
	
	/* 操作日志样式 - 时间轴效果 */
	.timeline {
		position: relative;
		padding: 20rpx 0;
		
		.log-item {
			position: relative;
			padding-left: 40rpx;
			margin-bottom: 30rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.timeline-dot {
				position: absolute;
				left: 0;
				top: 16rpx;
				width: 20rpx;
				height: 20rpx;
				border-radius: 50%;
				background-color: #666;
				z-index: 2;
				
				&.dot-report {
					background-color: $uni-color-primary;
				}
				
				&.dot-confirm {
					background-color: $uni-color-success;
				}
				
				&.dot-reject {
					background-color: $uni-color-error;
				}
				
				&.dot-repair {
					background-color: $uni-color-warning;
				}
				
				&.dot-complete {
					background-color: $uni-color-success;
				}
				
				&.dot-assign {
					background-color: #8a2be2; // 紫色
				}
			}
			
			.timeline-line {
				position: absolute;
				left: 9rpx;
				top: 36rpx;
				width: 2rpx;
				height: calc(100% + 10rpx);
				background-color: rgba(0, 0, 0, 0.1);
				z-index: 1;
			}
			
			.log-content {
				background-color: rgba(0, 0, 0, 0.02);
				border-radius: 8rpx;
				padding: 16rpx;
				
				.log-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 10rpx;
					
					.log-type {
						font-size: 28rpx;
						font-weight: 500;
						color: $uni-color-primary;
					}
					
					.log-time {
						font-size: 24rpx;
						color: $uni-text-color-grey;
					}
				}
				
				.log-body {
					display: flex;
					flex-direction: column;
					gap: 6rpx;
					
					.log-desc {
						font-size: 26rpx;
						color: $uni-text-color;
						line-height: 1.5;
					}
					
					.log-operator {
						font-size: 24rpx;
						color: $uni-text-color-grey;
						margin-top: 4rpx;
					}
				}
			}
		}
	}
</style> 