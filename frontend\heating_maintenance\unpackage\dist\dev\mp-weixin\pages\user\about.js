"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const utils_upload = require("../../utils/upload.js");
const _sfc_main = {
  data() {
    return {
      systemParams: {
        systemName: "热力维护管理系统",
        systemLogo: "/static/logo.png",
        systemVersions: "V1.0.0",
        buildNumber: "Build 20240502",
        copyright: "Copyright © 2024 陕西杰明新能源有限公司",
        company: "陕西杰明新能源有限公司",
        linkman: "客服",
        mobile: "029-88888888",
        internetAddr: "www.example.com",
        companyAddr: "陕西省西安市",
        intro: "热力维护管理系统是一款专业的热力设备维护管理软件，提供工单管理、巡检管理、设备管理等功能。"
      },
      isLoading: true
    };
  },
  onLoad() {
    this.loadSystemParams();
  },
  methods: {
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr)
        return "";
      const date = new Date(dateStr);
      if (isNaN(date.getTime()))
        return dateStr;
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString();
      const day = date.getDate().toString();
      return `${year}年${month}月${day}日`;
    },
    // 加载系统参数
    loadSystemParams() {
      this.isLoading = true;
      utils_api.systemApi.getSystemParams().then((res) => {
        if (res.code === 200 && res.data) {
          this.systemParams = res.data;
        } else {
          common_vendor.index.__f__("error", "at pages/user/about.vue:135", "获取系统参数失败:", res.message);
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/user/about.vue:139", "获取系统参数异常:", err);
      }).finally(() => {
        this.isLoading = false;
      });
    },
    // 导航到页面
    navigateTo(url) {
      common_vendor.index.navigateTo({
        url
      });
    },
    // 打开网站
    openWebsite() {
      common_vendor.index.showModal({
        title: "打开网站",
        content: "是否打开官方网站？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "打开网站功能开发中...",
              icon: "none"
            });
          }
        }
      });
    },
    // 获取完整的图片URL
    getFullImageUrl(path) {
      common_vendor.index.__f__("log", "at pages/user/about.vue:172", utils_upload.uploadUtils.getFileUrl(path));
      return utils_upload.uploadUtils.getFileUrl(path);
    },
    // 检查更新
    checkUpdate() {
      common_vendor.index.showLoading({
        title: "检查更新中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showModal({
          title: "检查更新",
          content: "当前已是最新版本",
          showCancel: false
        });
      }, 1500);
    },
    // 联系客服
    contactSupport() {
      common_vendor.index.showModal({
        title: "联系客服",
        content: `客服热线：${this.systemParams.mobile}
联系人：${this.systemParams.linkman}
工作时间：周一至周五 9:00-18:00`,
        showCancel: false,
        confirmText: "我知道了"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.isLoading
  }, $data.isLoading ? {} : {
    b: $options.getFullImageUrl($data.systemParams.systemLogo),
    c: common_vendor.t($data.systemParams.systemName),
    d: common_vendor.t($data.systemParams.systemVersions),
    e: common_vendor.t($data.systemParams.systemName),
    f: common_vendor.t($data.systemParams.systemVersions),
    g: common_vendor.t($data.systemParams.buildNumber),
    h: common_vendor.t($options.formatDate($data.systemParams.releaseDate)),
    i: common_vendor.o((...args) => $options.checkUpdate && $options.checkUpdate(...args)),
    j: common_vendor.t($data.systemParams.company),
    k: common_vendor.t($data.systemParams.mobile),
    l: common_vendor.t($data.systemParams.internetAddr),
    m: common_vendor.o((...args) => $options.openWebsite && $options.openWebsite(...args)),
    n: common_vendor.t($data.systemParams.email),
    o: common_vendor.o(($event) => $options.navigateTo("/pages/user/agreement")),
    p: common_vendor.o(($event) => $options.navigateTo("/pages/user/faq")),
    q: common_vendor.o((...args) => $options.contactSupport && $options.contactSupport(...args)),
    r: common_vendor.t($data.systemParams.copyright)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/user/about.js.map
