{"version": 3, "file": "message-settings.js", "sources": ["pages/user/message-settings.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlci9tZXNzYWdlLXNldHRpbmdzLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"message-settings-container\">\r\n\t<!-- \t<view class=\"page-header\">\r\n\t\t\t<text class=\"page-title\">消息设置</text>\r\n\t\t</view> -->\r\n\t\t\r\n\t\t<view class=\"settings-card\">\r\n\t\t\t<!-- <view class=\"settings-section\">\r\n\t\t\t\t<view class=\"section-title\">工单通知</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"setting-item\">\r\n\t\t\t\t\t<view class=\"setting-info\">\r\n\t\t\t\t\t\t<text class=\"setting-name\">新工单通知</text>\r\n\t\t\t\t\t\t<text class=\"setting-desc\">当有新的工单分配给您时通知</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<switch :checked=\"settings.newOrderNotice\" @change=\"toggleSetting('newOrderNotice')\" color=\"#1890ff\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"setting-item\">\r\n\t\t\t\t\t<view class=\"setting-info\">\r\n\t\t\t\t\t\t<text class=\"setting-name\">工单状态变更</text>\r\n\t\t\t\t\t\t<text class=\"setting-desc\">当工单状态发生变化时通知</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<switch :checked=\"settings.orderStatusNotice\" @change=\"toggleSetting('orderStatusNotice')\" color=\"#1890ff\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"setting-item\">\r\n\t\t\t\t\t<view class=\"setting-info\">\r\n\t\t\t\t\t\t<text class=\"setting-name\">工单即将超时</text>\r\n\t\t\t\t\t\t<text class=\"setting-desc\">当工单临近超时时发送提醒</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<switch :checked=\"settings.orderTimeoutNotice\" @change=\"toggleSetting('orderTimeoutNotice')\" color=\"#1890ff\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"settings-section\">\r\n\t\t\t\t<view class=\"section-title\">巡检通知</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"setting-item\">\r\n\t\t\t\t\t<view class=\"setting-info\">\r\n\t\t\t\t\t\t<text class=\"setting-name\">巡检计划提醒</text>\r\n\t\t\t\t\t\t<text class=\"setting-desc\">当有新的巡检计划时通知</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<switch :checked=\"settings.patrolPlanNotice\" @change=\"toggleSetting('patrolPlanNotice')\" color=\"#1890ff\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"setting-item\">\r\n\t\t\t\t\t<view class=\"setting-info\">\r\n\t\t\t\t\t\t<text class=\"setting-name\">巡检计划延期</text>\r\n\t\t\t\t\t\t<text class=\"setting-desc\">当巡检计划被延期时通知</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<switch :checked=\"settings.patrolDelayNotice\" @change=\"toggleSetting('patrolDelayNotice')\" color=\"#1890ff\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view> -->\r\n\t\t\t\r\n\t\t\t<view class=\"settings-section\">\r\n\t\t\t\t<view class=\"section-title\">系统通知</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"setting-item\">\r\n\t\t\t\t\t<view class=\"setting-info\">\r\n\t\t\t\t\t\t<text class=\"setting-name\">系统更新</text>\r\n\t\t\t\t\t\t<text class=\"setting-desc\">当系统有更新时通知</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<switch :checked=\"settings.systemUpdateNotice\" @change=\"toggleSetting('systemUpdateNotice')\" color=\"#1890ff\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"setting-item\">\r\n\t\t\t\t\t<view class=\"setting-info\">\r\n\t\t\t\t\t\t<text class=\"setting-name\">账号安全</text>\r\n\t\t\t\t\t\t<text class=\"setting-desc\">当账号出现安全风险时通知</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<switch :checked=\"settings.accountSecurityNotice\" @change=\"toggleSetting('accountSecurityNotice')\" color=\"#1890ff\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"settings-section\">\r\n\t\t\t\t<view class=\"section-title\">通知方式</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"setting-item\">\r\n\t\t\t\t\t<view class=\"setting-info\">\r\n\t\t\t\t\t\t<text class=\"setting-name\">应用内通知</text>\r\n\t\t\t\t\t\t<text class=\"setting-desc\">在应用内收到通知消息</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<switch :checked=\"settings.inAppNotice\" @change=\"toggleSetting('inAppNotice')\" color=\"#1890ff\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"setting-item\">\r\n\t\t\t\t\t<view class=\"setting-info\">\r\n\t\t\t\t\t\t<text class=\"setting-name\">短信通知</text>\r\n\t\t\t\t\t\t<text class=\"setting-desc\">通过短信接收重要通知</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<switch :checked=\"settings.smsNotice\" @change=\"toggleSetting('smsNotice')\" color=\"#1890ff\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"note-text\">注：短信通知可能会产生额外费用，请谨慎开启</view>\r\n\t\t\r\n\t\t<view class=\"submit-btn\" @click=\"saveSettings\">保存设置</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tsettings: {\r\n\t\t\t\tnewOrderNotice: true,\r\n\t\t\t\torderStatusNotice: true,\r\n\t\t\t\torderTimeoutNotice: true,\r\n\t\t\t\tpatrolPlanNotice: true,\r\n\t\t\t\tpatrolDelayNotice: false,\r\n\t\t\t\tsystemUpdateNotice: true,\r\n\t\t\t\taccountSecurityNotice: true,\r\n\t\t\t\tinAppNotice: true,\r\n\t\t\t\tsmsNotice: false\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\t\t// 加载用户的消息设置\r\n\t\tthis.loadMessageSettings();\r\n\t},\r\n\tmethods: {\r\n\t\t// 加载消息设置\r\n\t\tloadMessageSettings() {\r\n\t\t\t// 从本地存储获取消息设置\r\n\t\t\tconst settings = uni.getStorageSync('messageSettings');\r\n\t\t\tif (settings) {\r\n\t\t\t\tthis.settings = Object.assign({}, this.settings, settings);\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 切换设置状态\r\n\t\ttoggleSetting(key) {\r\n\t\t\tthis.settings[key] = !this.settings[key];\r\n\t\t},\r\n\t\t\r\n\t\t// 保存设置\r\n\t\tsaveSettings() {\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '保存中...'\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 在实际应用中，这里应该调用API保存消息设置\r\n\t\t\t// 这里模拟一个保存成功的情况\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\r\n\t\t\t\t// 保存到本地存储\r\n\t\t\t\tuni.setStorageSync('messageSettings', this.settings);\r\n\t\t\t\t\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '设置已保存',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 返回上一页\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tuni.navigateBack();\r\n\t\t\t\t}, 1500);\r\n\t\t\t}, 1000);\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.message-settings-container {\r\n\tbackground-color: #f5f7fa;\r\n\tmin-height: 100vh;\r\n\tpadding-bottom: 40rpx;\r\n}\r\n\r\n.page-header {\r\n\tbackground-color: #fff;\r\n\tpadding: 30rpx;\r\n\tborder-bottom: 1rpx solid #eee;\r\n\t\r\n\t.page-title {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t}\r\n}\r\n\r\n.settings-card {\r\n\tmargin: 30rpx;\r\n\tbackground-color: #fff;\r\n\tborder-radius: 12rpx;\r\n\tbox-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.05);\r\n\toverflow: hidden;\r\n\t\r\n\t.settings-section {\r\n\t\tpadding: 0 20rpx;\r\n\t\t\r\n\t\t.section-title {\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tpadding: 30rpx 10rpx 20rpx;\r\n\t\t\tcolor: #333;\r\n\t\t\tposition: relative;\r\n\t\t\tdisplay: block;\r\n\t\t\twidth: 100%;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\t&::before {\r\n\t\t\t\tcontent: '';\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\ttop: 50%;\r\n\t\t\t\ttransform: translateY(-50%);\r\n\t\t\t\twidth: 6rpx;\r\n\t\t\t\theight: 30rpx;\r\n\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\tborder-radius: 3rpx;\r\n\t\t\t}\r\n\t\t\tpadding-left: 20rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.setting-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 24rpx 10rpx;\r\n\t\t\tborder-bottom: 1rpx solid rgba(0, 0, 0, 0.05);\r\n\t\t\t\r\n\t\t\t&:last-child {\r\n\t\t\t\tborder-bottom: none;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.setting-info {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\t\r\n\t\t\t\t.setting-name {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.setting-desc {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tswitch {\r\n\t\t\t\ttransform: scale(0.8);\r\n\t\t\t\ttransform-origin: right center;\r\n\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t\t/* 修复在某些机型上的显示问题 */\r\n\t\t\t\tflex-shrink: 0;\r\n\t\t\t\tmin-width: 80rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.note-text {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\tpadding: 0 40rpx;\r\n\tmargin-top: 10rpx;\r\n}\r\n\r\n.submit-btn {\r\n\tmargin: 60rpx 30rpx;\r\n\theight: 90rpx;\r\n\tline-height: 90rpx;\r\n\tbackground-color: $uni-color-primary;\r\n\tcolor: #fff;\r\n\ttext-align: center;\r\n\tborder-radius: 12rpx;\r\n\tfont-size: 32rpx;\r\n\tbox-shadow: 0 6rpx 20rpx rgba(24, 144, 255, 0.3);\r\n\tfont-weight: bold;\r\n\t\r\n\t&:active {\r\n\t\topacity: 0.9;\r\n\t\ttransform: translateY(2rpx);\r\n\t}\r\n}\r\n</style> ", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaan<PERSON>_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/user/message-settings.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAuGA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,UAAU;AAAA,QACT,gBAAgB;AAAA,QAChB,mBAAmB;AAAA,QACnB,oBAAoB;AAAA,QACpB,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,oBAAoB;AAAA,QACpB,uBAAuB;AAAA,QACvB,aAAa;AAAA,QACb,WAAW;AAAA,MACZ;AAAA,IACD;AAAA,EACA;AAAA,EACD,SAAS;AAER,SAAK,oBAAmB;AAAA,EACxB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,sBAAsB;AAErB,YAAM,WAAWA,cAAAA,MAAI,eAAe,iBAAiB;AACrD,UAAI,UAAU;AACb,aAAK,WAAW,OAAO,OAAO,CAAE,GAAE,KAAK,UAAU,QAAQ;AAAA,MAC1D;AAAA,IACA;AAAA;AAAA,IAGD,cAAc,KAAK;AAClB,WAAK,SAAS,GAAG,IAAI,CAAC,KAAK,SAAS,GAAG;AAAA,IACvC;AAAA;AAAA,IAGD,eAAe;AACdA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACR,CAAC;AAID,iBAAW,MAAM;AAChBA,sBAAG,MAAC,YAAW;AAGfA,sBAAAA,MAAI,eAAe,mBAAmB,KAAK,QAAQ;AAEnDA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAGD,mBAAW,MAAM;AAChBA,wBAAG,MAAC,aAAY;AAAA,QAChB,GAAE,IAAI;AAAA,MACP,GAAE,GAAI;AAAA,IACR;AAAA,EACD;AACD;;;;;;;;;;;;;;;ACnKA,GAAG,WAAW,eAAe;"}