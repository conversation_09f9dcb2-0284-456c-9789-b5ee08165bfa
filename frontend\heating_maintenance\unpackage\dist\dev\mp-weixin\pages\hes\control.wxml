<view class="station-control"><view class="tab-section"><view class="tab-bar"><view class="{{['tab-item', a && 'active']}}" bindtap="{{b}}"> 控制视图 </view><view class="{{['tab-item', c && 'active']}}" bindtap="{{d}}"> 工艺视图 </view></view></view><view class="station-selector" bindtap="{{g}}"><view class="station-label">换热站：</view><view class="station-value-wrapper"><text class="{{['station-value-text', f && 'placeholder']}}">{{e}}</text><text class="station-arrow">&gt;</text></view></view><view wx:if="{{h}}" class="station-content"><view wx:if="{{i}}" class="control-view"><view class="card basic-info"><view class="card-title">基本信息</view><view class="info-row"><text class="label">站点名称：</text><text class="value">{{j}}</text></view><view class="info-row"><text class="label">站点地址：</text><text class="value">{{k}}</text></view><view class="info-row"><text class="label">运行状态：</text><text class="{{['value', m]}}">{{l}}</text></view></view><view wx:if="{{n}}" class="card real-time-data"><view class="card-title">实时数据</view><view class="data-grid"><view class="data-item"><text class="data-label">一次供水温度</text><text class="data-value">{{o}}℃</text></view><view class="data-item"><text class="data-label">一次回水温度</text><text class="data-value">{{p}}℃</text></view><view class="data-item"><text class="data-label">一次供水压力</text><text class="data-value">{{q}}MPa</text></view><view class="data-item"><text class="data-label">一次回水压力</text><text class="data-value">{{r}}MPa</text></view></view></view><view wx:else class="card real-time-data"><view class="card-title">实时数据</view><view class="data-grid"><view class="data-item"><text class="data-label">供水温度</text><text class="data-value">{{s}}℃</text></view><view class="data-item"><text class="data-label">回水温度</text><text class="data-value">{{t}}℃</text></view><view class="data-item"><text class="data-label">供水压力</text><text class="data-value">{{v}}MPa</text></view><view class="data-item"><text class="data-label">回水压力</text><text class="data-value">{{w}}MPa</text></view></view></view><view class="card work-mode"><view class="mode-header"><text class="mode-title">运行模式:</text><view class="mode-buttons"><view class="{{['mode-button', x && 'active']}}" bindtap="{{y}}">自动</view><view class="{{['mode-button', z && 'active']}}" bindtap="{{A}}">手动</view></view></view><view class="mode-desc">{{B}}</view></view><view class="card device-control"><view class="card-title">设备控制</view><view class="device-tabs"><view class="{{['device-tab', C && 'active']}}" bindtap="{{D}}">阀门控制</view><view class="{{['device-tab', E && 'active']}}" bindtap="{{F}}">水泵控制</view></view><view wx:if="{{G}}" class="device-section"><view class="device-control-block"><picker bindchange="{{L}}" value="{{M}}" range="{{N}}" range-key="name" disabled="{{O}}"><view class="device-selector"><view class="selector-wrap"><text class="dropdown-label">选择阀门:</text><view class="dropdown-value"><text class="device-name">{{H}}</text><text class="dropdown-arrow">▼</text></view></view><switch checked="{{I}}" bindchange="{{J}}" disabled="{{K}}" color="#0088ff"/></view></picker><view class="device-control-params"><text class="param-label">开度: {{P}}%</text><view class="slider-row"><slider value="{{Q}}" min="0" max="100" bindchange="{{R}}" disabled="{{S}}" activeColor="#0088ff" block-size="20" block-color="#ffffff"/><text class="param-value">{{T}}</text></view><view class="confirm-button-row"><button class="confirm-button" bindtap="{{U}}" disabled="{{V}}">确认</button></view></view></view></view><view wx:if="{{W}}" class="device-section"><view class="device-control-block"><picker bindchange="{{ab}}" value="{{ac}}" range="{{ad}}" range-key="name" disabled="{{ae}}"><view class="device-selector"><view class="selector-wrap"><text class="dropdown-label">选择水泵:</text><view class="dropdown-value"><text class="device-name">{{X}}</text><text class="dropdown-arrow">▼</text></view></view><switch checked="{{Y}}" bindchange="{{Z}}" disabled="{{aa}}" color="#0088ff"/></view></picker><view class="device-control-params"><text class="param-label">频率: {{af}}Hz</text><view class="{{['frequency-control', an && 'disabled']}}"><view class="frequency-display"><input type="digit" class="frequency-input" disabled="{{ag}}" value="{{ah}}" bindinput="{{ai}}"/><text class="frequency-unit">Hz</text></view><view class="frequency-buttons"><view bindtap="{{aj}}" class="{{['frequency-button', 'increase', ak && 'disabled']}}">▲</view><view bindtap="{{al}}" class="{{['frequency-button', 'decrease', am && 'disabled']}}">▼</view></view></view><view class="confirm-button-row"><button class="confirm-button" bindtap="{{ao}}" disabled="{{ap}}">确认</button></view></view></view></view></view><view class="card control-algorithm"><view class="card-title">控制算法</view><view wx:if="{{aq}}" class="algorithm-tip"><text class="tip-text">注意：算法设置仅在自动模式下生效，请先切换到自动模式</text></view><view wx:if="{{ar}}" class="algorithm-tip"><text class="tip-text">注意：该站点当前不在线，无法应用算法设置</text></view><view class="algorithm-selector"><radio-group bindchange="{{av}}"><label wx:for="{{as}}" wx:for-item="algo" wx:key="d" class="algorithm-item"><view class="algorithm-radio"><radio value="{{algo.a}}" checked="{{algo.b}}" color="#0088ff" disabled="{{at}}"/></view><text class="algorithm-label">{{algo.c}}</text></label></radio-group></view><view class="algorithm-desc">{{aw}}</view><view class="algorithm-params"><view class="param-item"><text class="param-label">目标值设定:</text><input type="digit" class="param-input" disabled="{{ax}}" value="{{ay}}" bindinput="{{az}}"/><text class="param-unit">{{aA}}</text></view><button class="apply-button" bindtap="{{aB}}" disabled="{{aC}}">应用</button><view wx:if="{{aD}}" class="button-tip"><text class="tip-text">需要在自动模式下才能应用算法</text></view></view></view></view><view wx:elif="{{aE}}" class="process-view"><view class="process-image"><image src="{{aF}}" mode="widthFix"></image></view><view class="process-legend"><view class="legend-item"><view class="legend-color" style="background-color:#f00"></view><text class="legend-text">供水</text></view><view class="legend-item"><view class="legend-color" style="background-color:#00f"></view><text class="legend-text">回水</text></view><view class="legend-item"><view class="legend-color" style="background-color:#0c0"></view><text class="legend-text">运行设备</text></view></view></view></view><view wx:else class="no-station"><text class="tip">请选择换热站进行控制</text></view><uni-popup wx:if="{{aX}}" class="r" u-s="{{['d']}}" u-r="stationPopup" u-i="4e9888be-0" bind:__l="__l" u-p="{{aX}}"><view class="station-picker"><view class="picker-header"><text class="picker-title">选择换热站</text><view class="picker-close" bindtap="{{aG}}">关闭</view></view><view class="search-bar"><view class="search-input-wrap"><view class="search-icon">🔍</view><input type="text" placeholder="搜索换热站" confirm-type="search" bindconfirm="{{aH}}" class="search-input" value="{{aI}}" bindinput="{{aJ}}"/><view wx:if="{{aK}}" class="search-clear" bindtap="{{aL}}">×</view></view></view><view class="filter-tabs"><view class="{{['filter-tab', aM && 'active']}}" bindtap="{{aN}}">全部</view><view class="{{['filter-tab', aO && 'active']}}" bindtap="{{aP}}">在线</view><view class="{{['filter-tab', aQ && 'active']}}" bindtap="{{aR}}">故障</view><view class="{{['filter-tab', aS && 'active']}}" bindtap="{{aT}}">离线</view></view><scroll-view scroll-y="true" class="station-list"><view wx:if="{{aU}}" class="empty-tip"><text>暂无符合条件的换热站</text></view><view wx:for="{{aV}}" wx:for-item="station" wx:key="g" class="station-item" bindtap="{{station.h}}"><view class="station-item-content"><view class="station-main-info"><text class="station-name">{{station.a}}</text><text class="station-address">{{station.b}}</text></view><view class="station-temp-info"><text class="temp-item">供水温度: {{station.c}}</text><text class="temp-item">回水温度: {{station.d}}</text></view><text class="{{['station-status-tag', station.f]}}">{{station.e}}</text></view></view></scroll-view></view></uni-popup></view>