/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.hes-detail-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.detail-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.detail-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}
.detail-card .card-header .card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  position: relative;
  padding-left: 20rpx;
}
.detail-card .card-header .card-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 8rpx;
  width: 6rpx;
  height: 28rpx;
  background-color: #1890ff;
  border-radius: 3rpx;
}
.detail-card .card-header .hes-status {
  padding: 4rpx 16rpx;
  font-size: 24rpx;
  border-radius: 20rpx;
}
.detail-card .card-header .hes-status.normal {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}
.detail-card .card-header .hes-status.warning {
  background-color: rgba(250, 173, 20, 0.1);
  color: #faad14;
}
.detail-card .card-header .hes-status.fault {
  background-color: rgba(245, 34, 45, 0.1);
  color: #f5222d;
}
.detail-card .card-header .refresh-btn, .detail-card .card-header .view-all {
  font-size: 26rpx;
  color: #1890ff;
}
.info-item {
  display: flex;
  margin-bottom: 20rpx;
}
.info-item .info-label {
  width: 180rpx;
  font-size: 28rpx;
  color: #666;
}
.info-item .info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.data-grid {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 30rpx;
}
.data-grid .data-item {
  width: 33.33%;
  text-align: center;
  margin-bottom: 30rpx;
}
.data-grid .data-item .data-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.data-grid .data-item .data-value.warning {
  color: #faad14;
}
.data-grid .data-item .data-value.danger {
  color: #f5222d;
}
.data-grid .data-item .data-label {
  font-size: 26rpx;
  color: #666;
}
.data-chart {
  border-top: 1rpx solid #eee;
  padding-top: 20rpx;
}
.data-chart .chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.data-chart .chart-header .chart-title {
  font-size: 28rpx;
  font-weight: bold;
}
.data-chart .chart-header .chart-legend {
  display: flex;
}
.data-chart .chart-header .chart-legend .legend-item {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
}
.data-chart .chart-header .chart-legend .legend-item .legend-color {
  width: 20rpx;
  height: 6rpx;
  margin-right: 8rpx;
}
.data-chart .chart-header .chart-legend .legend-item .legend-color.supply {
  background-color: #1890ff;
}
.data-chart .chart-header .chart-legend .legend-item .legend-color.return {
  background-color: #faad14;
}
.data-chart .chart-header .chart-legend .legend-item .legend-text {
  font-size: 24rpx;
  color: #666;
}
.data-chart .chart-placeholder {
  height: 300rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.data-chart .chart-placeholder text {
  font-size: 28rpx;
  color: #666;
}
.heating-area .area-info {
  display: flex;
  margin-bottom: 30rpx;
}
.heating-area .area-info .area-item {
  flex: 1;
  text-align: center;
}
.heating-area .area-info .area-item .area-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
  display: block;
}
.heating-area .area-info .area-item .area-value {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}
.heating-area .community-list {
  border-top: 1rpx solid #eee;
  padding-top: 20rpx;
}
.heating-area .community-list .community-item {
  display: flex;
  flex-direction: column;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}
.heating-area .community-list .community-item:last-child {
  border-bottom: none;
}
.heating-area .community-list .community-item .community-name {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}
.heating-area .community-list .community-item .community-address {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}
.heating-area .community-list .community-item .community-units {
  font-size: 26rpx;
  color: #333;
}
.device-list .device-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}
.device-list .device-item:last-child {
  border-bottom: none;
}
.device-list .device-item .device-status {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}
.device-list .device-item .device-status.online {
  background-color: #52c41a;
}
.device-list .device-item .device-status.offline {
  background-color: #666;
}
.device-list .device-item .device-status.fault {
  background-color: #f5222d;
}
.device-list .device-item .device-info {
  flex: 1;
}
.device-list .device-item .device-info .device-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
}
.device-list .device-item .device-info .device-type {
  font-size: 24rpx;
  color: #666;
}
.device-list .device-item .device-alarm {
  background-color: #f5222d;
  color: #fff;
  border-radius: 16rpx;
  padding: 0 10rpx;
  margin-right: 16rpx;
}
.device-list .device-item .device-alarm .alarm-count {
  font-size: 24rpx;
}
.device-list .device-item .iconfont {
  font-size: 32rpx;
  color: #666;
}
.device-list .empty-tip {
  padding: 40rpx 0;
  text-align: center;
  font-size: 28rpx;
  color: #666;
}
.alarm-list .alarm-item {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #eee;
}
.alarm-list .alarm-item:last-child {
  border-bottom: none;
}
.alarm-list .alarm-item .alarm-level {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}
.alarm-list .alarm-item .alarm-level.warning {
  background-color: #faad14;
}
.alarm-list .alarm-item .alarm-level.error {
  background-color: #f5222d;
}
.alarm-list .alarm-item .alarm-content {
  flex: 1;
}
.alarm-list .alarm-item .alarm-content .alarm-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
}
.alarm-list .alarm-item .alarm-content .alarm-time {
  font-size: 24rpx;
  color: #666;
}
.action-buttons {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  display: flex;
  padding: 20rpx 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.action-buttons .action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #1890ff;
}
.action-buttons .action-btn .iconfont {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}
.action-buttons .action-btn text {
  font-size: 26rpx;
}