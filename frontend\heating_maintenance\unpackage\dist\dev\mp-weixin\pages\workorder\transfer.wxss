/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.transfer-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}
.form-card {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
}
.form-card .form-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}
.form-card .form-item {
  margin-bottom: 30rpx;
}
.form-card .form-item .form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}
.form-card .form-item .form-label.required::before {
  content: "*";
  color: #f5222d;
  margin-right: 6rpx;
}
.form-card .form-item .form-textarea {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}
.form-card .form-item .picker-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  box-sizing: border-box;
}
.form-card .form-item .picker-box .picker-text {
  font-size: 28rpx;
  color: #333;
}
.form-card .form-item .picker-box .picker-text.placeholder {
  color: #999;
}
.form-card .form-item .picker-box .iconfont {
  font-size: 28rpx;
  color: #999;
}
.action-buttons {
  display: flex;
  padding: 20rpx 0;
}
.action-buttons button {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  margin: 0 10rpx;
}
.action-buttons button::after {
  border: none;
}
.action-buttons .btn-cancel {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}
.action-buttons .btn-submit {
  background-color: #1890ff;
  color: #fff;
}
.popup-container {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
}
.popup-container .popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}
.popup-container .popup-header .title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.popup-container .popup-header .cancel, .popup-container .popup-header .confirm {
  font-size: 28rpx;
}
.popup-container .popup-header .cancel {
  color: #999;
}
.popup-container .popup-header .confirm {
  color: #1890ff;
}
.popup-container .search-box {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}
.popup-container .search-box .search-input-wrap {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 20rpx;
  height: 72rpx;
}
.popup-container .search-box .search-input-wrap .iconfont {
  color: #999;
  font-size: 32rpx;
  margin-right: 10rpx;
}
.popup-container .search-box .search-input-wrap .search-input {
  flex: 1;
  height: 72rpx;
  font-size: 28rpx;
}
.popup-container .search-box .search-input-wrap .icon-close {
  padding: 10rpx;
}
.popup-container .popup-body {
  max-height: 60vh;
  padding-bottom: 50rpx;
}
.popup-container .popup-body .user-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  position: relative;
}
.popup-container .popup-body .user-item.active {
  background-color: #e6f7ff;
  border-left: 8rpx solid #1890ff;
}
.popup-container .popup-body .user-item .user-info {
  display: flex;
  flex-direction: column;
}
.popup-container .popup-body .user-item .user-info .user-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: bold;
}
.popup-container .popup-body .user-item .user-info .user-phone {
  font-size: 24rpx;
  color: #999;
}
.popup-container .popup-body .user-item .select-indicator {
  display: flex;
  align-items: center;
  color: #1890ff;
}
.popup-container .popup-body .user-item .select-indicator .select-text {
  font-size: 28rpx;
  color: #1890ff;
  margin-right: 10rpx;
}
.popup-container .popup-body .user-item .select-indicator .iconfont {
  font-size: 40rpx;
  color: #1890ff;
}
.popup-container .popup-body .empty-tip {
  text-align: center;
  color: #999;
  padding: 30rpx;
}