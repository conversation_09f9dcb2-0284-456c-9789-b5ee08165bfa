"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      videoUrl: "",
      loadError: false
    };
  },
  onLoad(options) {
    if (options.url || options.src) {
      this.videoUrl = decodeURIComponent(options.url || options.src);
      common_vendor.index.__f__("log", "at pages/common/video-player.vue:58", "视频播放器: 加载视频URL:", this.videoUrl);
      if (options.title) {
        common_vendor.index.setNavigationBarTitle({
          title: options.title
        });
      }
      common_vendor.index.setNavigationBarColor({
        frontColor: "#ffffff",
        backgroundColor: "#000000"
      });
      this.preloadVideo(this.videoUrl);
    } else {
      common_vendor.index.showToast({
        title: "未提供视频地址",
        icon: "none"
      });
      setTimeout(() => {
        common_vendor.index.navigateBack();
      }, 1500);
    }
  },
  onReady() {
    this.videoContext = common_vendor.index.createVideoContext("videoPlayer", this);
  },
  onShow() {
    setTimeout(() => {
      if (this.videoContext) {
        this.videoContext.play();
      }
    }, 300);
  },
  onHide() {
    if (this.videoContext) {
      this.videoContext.pause();
    }
  },
  onUnload() {
    if (this.videoContext) {
      this.videoContext.stop();
      this.videoContext = null;
    }
  },
  methods: {
    // 视频加载错误处理
    onVideoError(e) {
      common_vendor.index.__f__("error", "at pages/common/video-player.vue:129", "视频播放器: 视频加载失败:", e.detail);
      this.loadError = true;
      common_vendor.index.showToast({
        title: "视频加载失败，请尝试其他方式查看",
        icon: "none",
        duration: 2e3
      });
    },
    // 处理全屏状态变化
    onFullscreenChange(e) {
      common_vendor.index.__f__("log", "at pages/common/video-player.vue:141", "视频全屏状态变化:", e.detail.fullScreen);
      if (!e.detail.fullScreen) {
        setTimeout(() => {
          if (this.videoContext) {
            this.videoContext.play();
            setTimeout(() => this.videoContext.pause(), 10);
          }
        }, 100);
      }
    },
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 预加载视频
    preloadVideo(url) {
    },
    // 在浏览器中打开
    openInBrowser() {
      if (!this.videoUrl) {
        common_vendor.index.showToast({
          title: "视频URL无效",
          icon: "none"
        });
        return;
      }
      common_vendor.index.__f__("log", "at pages/common/video-player.vue:190", "在浏览器中打开视频:", this.videoUrl);
      common_vendor.index.setClipboardData({
        data: this.videoUrl,
        success: () => {
          common_vendor.index.showToast({
            title: "URL已复制，请在浏览器中打开",
            icon: "none"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.videoUrl,
    b: common_vendor.o((...args) => $options.onVideoError && $options.onVideoError(...args)),
    c: common_vendor.o((...args) => $options.onFullscreenChange && $options.onFullscreenChange(...args)),
    d: $data.loadError
  }, $data.loadError ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/common/video-player.js.map
