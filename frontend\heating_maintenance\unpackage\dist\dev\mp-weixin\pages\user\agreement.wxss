/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.agreement-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.nav-bar .back-button {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.nav-bar .back-button .back-icon {
  font-family: "iconfont";
  font-size: 36rpx;
  color: #333;
}
.nav-bar .page-title {
  flex: 1;
  text-align: center;
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-right: 60rpx;
  /* 为了让标题居中 */
}

/* 协议内容 */
.agreement-content {
  flex: 1;
  padding: 30rpx;
}
.agreement-content .agreement-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
}
.agreement-content .agreement-section .section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
  text-align: center;
}
.agreement-content .agreement-section .section-date {
  font-size: 26rpx;
  color: #999;
  display: block;
  margin-bottom: 40rpx;
  text-align: center;
}
.agreement-content .agreement-section .sub-section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin: 40rpx 0 20rpx;
}
.agreement-content .agreement-section .section-paragraph {
  margin-bottom: 20rpx;
}
.agreement-content .agreement-section .section-paragraph .paragraph-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}
.agreement-content .agreement-section .section-end {
  margin-top: 60rpx;
  text-align: center;
}
.agreement-content .agreement-section .section-end .end-text {
  font-size: 28rpx;
  color: #999;
}