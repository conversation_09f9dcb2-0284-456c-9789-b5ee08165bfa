{"version": 3, "file": "records.js", "sources": ["pages/patrol/records.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcGF0cm9sL3JlY29yZHMudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"patrol-records-container\">\r\n    <!-- 错误提示框 -->\r\n    <view class=\"error-box\" v-if=\"showError\">\r\n      <view class=\"error-content\">\r\n        <text class=\"error-icon iconfont icon-warn\"></text>\r\n        <text class=\"error-text\">{{ errorMessage }}</text>\r\n        <button class=\"retry-btn\" @click=\"retryLoading\">重试</button>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 顶部区域 -->\r\n    <view class=\"page-header\">\r\n      <!-- 搜索栏 -->\r\n      <view class=\"search-bar\">\r\n      <!--  <view class=\"search-input-wrapper\">\r\n          <input\r\n            type=\"text\"\r\n            placeholder=\"搜索巡检记录\"\r\n            v-model=\"searchKeyword\"\r\n            confirm-type=\"search\"\r\n            @confirm=\"handleSearch\"\r\n          />\r\n          <text\r\n            class=\"iconfont icon-clear\"\r\n            v-if=\"searchKeyword\"\r\n            @click=\"clearSearch\"\r\n          ></text>\r\n        </view> -->\r\n\r\n        <!-- 筛选选项区域 -->\r\n        <view class=\"filter-options\">\r\n          <view class=\"filter-option\" @click=\"showTimeFilter\">\r\n            <text class=\"option-text\">时间</text>\r\n            <text class=\"option-value\">{{ timeFilterText }}</text>\r\n          </view>\r\n\r\n          <view class=\"filter-option\" @click=\"showStatusFilter\">\r\n            <text class=\"option-text\">状态</text>\r\n            <text class=\"option-value\">{{ statusFilterText }}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 巡检记录列表 -->\r\n    <view class=\"record-list\" v-if=\"patrolRecords.length > 0\">\r\n      <view\r\n        class=\"record-item\"\r\n        v-for=\"(record, index) in patrolRecords\"\r\n        :key=\"index\"\r\n        @click=\"viewRecordDetail(record.id)\"\r\n      >\r\n        <view class=\"record-header\">\r\n          <text class=\"record-title\">{{ record.planName }}</text>\r\n          <view class=\"record-status\" :class=\"record.status\">\r\n            {{ getStatusText(record.status) }}\r\n          </view>\r\n        </view>\r\n        <view class=\"record-info\">\r\n          <view class=\"info-row date-location\">\r\n            <view class=\"info-item\">\r\n\t\t\t\t<text class=\"item-label\">执行日期:</text>\r\n              <text class=\"item-value\">{{ formatDateOnly(record.executionDate) }}</text>\r\n            </view>\r\n            <view class=\"info-item\">\r\n\t\t\t\t<text class=\"item-label\">巡检类型:</text>\r\n              <text class=\"item-value\">{{ record.patrolType || \"未指定\" }}</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"info-row time-row\">\r\n\t\t\t   <view class=\"info-item\">\r\n\t\t\t      <text class=\"item-label\">执行时间:</text>\r\n\t\t\t       <text class=\"item-value\">{{ formatTimeOnly(record.startTime) }} ~{{ formatTimeOnly(record.endTime) }}</text>\r\n               </view>\r\n\t\t  </view>\r\n\r\n          <view class=\"info-row executor-abnormal\">\r\n            <view class=\"info-item\">\r\n\t\t\t <text class=\"item-label\">执行人:</text>\r\n              <text class=\"item-value\">{{ record.executorName || \"未分配\" }}</text>\r\n            </view>\r\n\r\n          <!--  <view class=\"info-item abnormal-item\">\r\n              <text class=\"item-icon iconfont icon-warning\"></text>\r\n              <text class=\"item-value\" :class=\"{ abnormal: record.abnormalCount > 0 }\">\r\n                异常: {{ record.abnormalCount || 0 }}\r\n              </text>\r\n            </view> -->\r\n          </view>\r\n        </view>\r\n\r\n        <view class=\"record-footer\">\r\n          <text class=\"record-time\">{{ formatDateTime(record.createTime) }}</text>\r\n\t\t  \r\n\t\t   <PermissionCheck permission=\"patrol:plans:execute\">\r\n\t\t      <view\r\n\t\t        v-if=\"record.status=='pending' || record.status=='overdue'  \"\r\n\t\t        class=\"view-detail start\"\r\n\t\t        @click.stop=\"startPatrol(record.id,record.patrolPlanId)\">\r\n\t\t        <text class=\"btn-text\">开始巡检</text>\r\n\t\t      </view>\r\n<!-- \t\t      <view\r\n\t\t        v-if=\"record.status === 'progressing'\"\r\n\t\t        class=\"action-button continue\"\r\n\t\t        @click.stop=\"continuePatrolPlan(record.patrolPlanId)\"\r\n\t\t      >\r\n\t\t        <text class=\"iconfont icon-continue\"></text>\r\n\t\t        <text>继续巡检</text>\r\n\t\t      </view> -->\r\n\t\t    </PermissionCheck>\r\n\t\t\t<PermissionCheck permission=\"patrol:record:detail\">\r\n\t\t\t  <view class=\"view-detail view\" @click.stop=\"viewRecordDetail(record.id)\">\r\n\t\t\t\t<text class=\"btn-text\">查看详情</text>\r\n\t\t\t  </view>\r\n\t\t   </PermissionCheck>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\t    <!-- 加载中 -->\r\n<!--    <view class=\"loading-container\" v-if=\"isLoading\">\r\n      <view class=\"loading-spinner\"></view>\r\n      <text class=\"loading-text\">加载中...</text>\r\n    </view> -->\r\n\r\n    <!-- 加载失败 -->\r\n    <view class=\"error-container\" v-if=\"loadError && !isLoading\">\r\n      <text class=\"error-text\">{{ errorMsg || \"加载失败，请重试\" }}</text>\r\n      <button class=\"retry-btn\" @click=\"loadPatrolRecords\">重新加载</button>\r\n    </view>\r\n\r\n    <!-- 空状态 -->\r\n    <view class=\"empty-state\" v-if=\"!isLoading && !loadError && patrolRecords.length === 0\">\r\n      <image class=\"empty-image\" src=\"/static/images/empty.png\" mode=\"aspectFit\"></image>\r\n      <text class=\"empty-text\">{{ emptyText }}</text>\r\n    </view>\r\n\r\n    <!-- 时间筛选弹窗 -->\r\n    <uni-popup ref=\"timeFilterPopup\" type=\"bottom\">\r\n      <view class=\"filter-popup\">\r\n        <view class=\"popup-header\">\r\n          <text class=\"popup-title\">选择时间范围</text>\r\n          <text class=\"popup-close\" @click=\"$refs.timeFilterPopup.close()\">关闭</text>\r\n        </view>\r\n        <view class=\"popup-content\">\r\n          <view\r\n            class=\"filter-option\"\r\n            v-for=\"(option, index) in timeOptions\"\r\n            :key=\"index\"\r\n            :class=\"{ active: timeFilter === option.value }\"\r\n            @click=\"selectTimeFilter(option.value)\"\r\n          >\r\n            <text class=\"option-text\">{{ option.label }}</text>\r\n            <text class=\"iconfont icon-check\" v-if=\"timeFilter === option.value\"></text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </uni-popup>\r\n\r\n    <!-- 状态筛选弹窗 -->\r\n    <uni-popup ref=\"statusFilterPopup\" type=\"bottom\">\r\n      <view class=\"filter-popup\">\r\n        <view class=\"popup-header\">\r\n          <text class=\"popup-title\">选择巡检状态</text>\r\n          <text class=\"popup-close\" @click=\"$refs.statusFilterPopup.close()\">关闭</text>\r\n        </view>\r\n        <view class=\"popup-content\">\r\n          <view\r\n            class=\"filter-option\"\r\n            v-for=\"(option, index) in statusOptions\"\r\n            :key=\"index\"\r\n            :class=\"{ active: statusFilter === option.value }\"\r\n            @click=\"selectStatusFilter(option.value)\"\r\n          >\r\n            <text class=\"option-text\">{{ option.label }}</text>\r\n            <text class=\"iconfont icon-check\" v-if=\"statusFilter === option.value\"></text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </uni-popup>\r\n<!--    <PermissionCheck permission=\"patrol:record:add\">\r\n      <view class=\"floating-btn\" @click=\"goToCreatePatrol\">\r\n        <text class=\"plus-icon\">+</text>\r\n      </view>\r\n    </PermissionCheck> -->\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { patrolApi } from \"@/utils/api.js\";\r\nimport permissionMixin from \"@/utils/permission-mixin.js\"; // 导入权限混入\r\nimport PermissionCheck from \"@/components/PermissionCheck.vue\"; // 导入权限检查组件\r\n\r\nexport default {\r\n  components: {\r\n    PermissionCheck, // 本地注册组件\r\n  },\r\n  mixins: [permissionMixin], // 使用权限混入\r\n  data() {\r\n    return {\r\n      searchKeyword: \"\",\r\n      patrolRecords: [],\r\n      currentPage: 1,\r\n      pageSize: 5,\r\n      hasMore: true,\r\n      isLoading: false,\r\n      loadError: false,\r\n      _needRefresh: false, // 标记是否需要刷新数据\r\n      _lastLoadTime: 0, // 记录上次加载时间\r\n      errorMsg: \"\", // 错误信息\r\n\r\n      // 筛选相关\r\n      timeFilter: \"all\",\r\n      statusFilter: \"all\",\r\n\r\n      // 筛选选项\r\n      timeOptions: [\r\n        { label: \"全部时间\", value: \"all\" },\r\n        { label: \"今天\", value: \"today\" },\r\n        { label: \"本周\", value: \"this_week\" },\r\n        { label: \"本月\", value: \"this_month\" },\r\n        { label: \"上个月\", value: \"last_month\" },\r\n      ],\r\n      statusOptions: [\r\n        { label: \"全部状态\", value: \"all\" },\r\n        { label: \"待执行\", value: \"pending\" },\r\n        { label: \"执行中\", value: \"processing\" },\r\n        { label: \"已完成\", value: \"completed\" },\r\n\t\t{ label: \"已超时\", value: \"overdue\" },\r\n      ],\r\n      showError: false,\r\n      errorMessage: \"\",\r\n    };\r\n  },\r\n  computed: {\r\n    timeFilterText() {\r\n      const option = this.timeOptions.find((item) => item.value === this.timeFilter);\r\n      return option ? option.label : \"全部时间\";\r\n    },\r\n    statusFilterText() {\r\n      const option = this.statusOptions.find((item) => item.value === this.statusFilter);\r\n      return option ? option.label : \"全部状态\";\r\n    },\r\n    emptyText() {\r\n      // 根据筛选条件提供更具体的空状态提示\r\n      if (this.timeFilter !== 'all' || this.statusFilter !== 'all' || this.searchKeyword) {\r\n        return \"没有符合筛选条件的巡检记录\";\r\n      }\r\n      return \"暂无巡检记录\";\r\n    }\r\n  },\r\n  onLoad() {\r\n    this.loadPatrolRecords();\r\n  },\r\n  // 添加页面级下拉刷新\r\n  onPullDownRefresh() {\r\n    this.currentPage = 1;\r\n    this.patrolRecords = [];\r\n    this.hasMore = true;\r\n    this.loadPatrolRecords().then(() => {\r\n      uni.stopPullDownRefresh();\r\n    });\r\n  },\r\n  // 添加上拉加载更多\r\n  onReachBottom() {\r\n    if (this.hasMore && !this.isLoading) {\r\n      this.currentPage++;\r\n      this.loadPatrolRecords(true);\r\n    }\r\n  },\r\n  onShow(){\r\n    // 判断数据是否需要刷新，减少不必要的重复加载\r\n    if (this.patrolRecords.length === 0 || this._needRefresh) {\r\n      this._needRefresh = false;\r\n      this.loadPatrolRecords();\r\n    }\r\n  },\r\n  methods: {\r\n    // 加载巡检记录列表\r\n    async loadPatrolRecords(isLoadMore = false) {\r\n      if (this.isLoading) return;\r\n      \r\n      // 避免短时间内多次调用（防抖）\r\n      const now = Date.now();\r\n      if (!isLoadMore && now - this._lastLoadTime < 300) {\r\n        return;\r\n      }\r\n      this._lastLoadTime = now;\r\n      \r\n      this.isLoading = true;\r\n\r\n      if (!isLoadMore) {\r\n        this.loadError = false;\r\n      }\r\n      // 显示加载提示\r\n\t  uni.showLoading({\r\n\t    title: \"加载中...\",\r\n\t  });\r\n      try {\r\n        // 准备请求参数\r\n        const params = {\r\n          page: this.currentPage,\r\n          pageSize: this.pageSize,\r\n          keyword: this.searchKeyword,\r\n          status: this.statusFilter !== \"all\" ? this.statusFilter : undefined,\r\n          timeRange: this.timeFilter !== \"all\" ? this.timeFilter : undefined,\r\n          // 只请求需要在界面上显示的字段\r\n          fields: \"id,planName,executorName,startTime,endTime,status,patrolType,executionDate,createTime,patrolPlanId\"\r\n        };\r\n\r\n        console.log(\"请求参数:\", params);\r\n\r\n        // 根据时间筛选添加日期参数\r\n        if (this.timeFilter !== \"all\") {\r\n          const dateRange = this.getDateRangeByTimeFilter(this.timeFilter);\r\n          if (dateRange) {\r\n            params.startDate = dateRange.startDate;\r\n            params.endDate = dateRange.endDate;\r\n          }\r\n        }\r\n\r\n        // 添加请求超时处理\r\n        const timeoutPromise = new Promise((_, reject) => {\r\n          setTimeout(() => {\r\n            reject(new Error('请求超时'));\r\n          }, 8000); // 8秒超时\r\n        });\r\n\r\n        // 调用API获取巡检记录列表\r\n        const res = await Promise.race([\r\n          patrolApi.getPatrolRecords(params),\r\n          timeoutPromise\r\n        ]);\r\n        console.log(\"API响应:\", res);\r\n\r\n        if (res.code === 200) {\r\n          // 兼容不同的数据结构\r\n          let recordsList = [];\r\n          let totalPages = 1;\r\n          \r\n          if (Array.isArray(res.data)) {\r\n            // 如果直接返回数组\r\n            recordsList = res.data;\r\n            totalPages = Math.ceil(recordsList.length / this.pageSize);\r\n          } else if (res.data && res.data.list) {\r\n            // 如果返回对象中包含list字段\r\n            recordsList = res.data.list;\r\n            totalPages = res.data.totalPages || Math.ceil(res.data.total / this.pageSize) || 1;\r\n          } else if (res.data) {\r\n            // 其他情况，尝试使用res.data\r\n            recordsList = Array.isArray(res.data) ? res.data : [res.data];\r\n            totalPages = 1;\r\n          }\r\n          \r\n          // 处理记录数据 - 只处理需要显示的字段\r\n          const records = recordsList.map((record) => ({\r\n            id: record.id,\r\n            planName: record.planName,\r\n            patrolPlanId: record.patrolPlanId,\r\n            executorName: record.executorName || \"未分配\",\r\n            startTime: record.startTime,\r\n            endTime: record.endTime,\r\n            status: record.status,\r\n            patrolType: record.patrolType,\r\n            executionDate: record.executionDate,\r\n            createTime: record.createTime\r\n            // 移除abnormalCount字段，页面上没有使用\r\n          }));\r\n\r\n          if (isLoadMore) {\r\n            // 加载更多模式：追加数据\r\n            this.patrolRecords = [...this.patrolRecords, ...records];\r\n          } else {\r\n            // 刷新模式：替换数据\r\n            this.patrolRecords = records;\r\n          }\r\n\r\n          // 判断是否还有更多数据\r\n          this.hasMore = this.currentPage < totalPages;\r\n\t\t  uni.hideLoading();\r\n        } else {\r\n          this.loadError = true;\r\n\t\t  uni.hideLoading();\r\n          uni.showToast({\r\n            title: res.message || \"获取巡检记录失败\",\r\n            icon: \"none\",\r\n            duration: 2000\r\n          });\r\n        }\r\n      } catch (err) {\r\n\t\t  uni.hideLoading();\r\n        console.error(\"获取巡检记录失败:\", err);\r\n        this.loadError = true;\r\n        \r\n        // 针对不同错误类型提供更友好的提示\r\n        if (err.message === '请求超时') {\r\n          this.errorMsg = \"请求超时，请检查网络连接\";\r\n        } else if (err.response && err.response.status === 404) {\r\n          this.errorMsg = \"服务不可用，请稍后再试\";\r\n        } else {\r\n          this.errorMsg = \"网络异常，请稍后重试\";\r\n        }\r\n        \r\n        uni.showToast({\r\n          title: this.errorMsg,\r\n          icon: \"none\",\r\n          duration: 2000\r\n        });\r\n      } finally {\r\n        this.isLoading = false;\r\n      }\r\n    },\r\n\r\n    // 根据时间筛选器获取日期范围\r\n    getDateRangeByTimeFilter(timeFilter) {\r\n      const now = new Date();\r\n      let startDate = null;\r\n      let endDate = null;\r\n\r\n      switch (timeFilter) {\r\n        case \"today\":\r\n          startDate = this.formatDate(now);\r\n          endDate = this.formatDate(now);\r\n          break;\r\n        case \"this_week\":\r\n          // 获取本周一\r\n          const dayOfWeek = now.getDay() || 7; // 将周日的0改为7\r\n          const mondayDate = new Date(now);\r\n          mondayDate.setDate(now.getDate() - dayOfWeek + 1);\r\n          startDate = this.formatDate(mondayDate);\r\n          endDate = this.formatDate(now);\r\n          break;\r\n        case \"this_month\":\r\n          // 获取本月第一天\r\n          const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);\r\n          startDate = this.formatDate(firstDayOfMonth);\r\n          endDate = this.formatDate(now);\r\n          break;\r\n        case \"last_month\":\r\n          // 获取上月第一天\r\n          const firstDayOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);\r\n          // 获取上月最后一天\r\n          const lastDayOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);\r\n          startDate = this.formatDate(firstDayOfLastMonth);\r\n          endDate = this.formatDate(lastDayOfLastMonth);\r\n          break;\r\n        default:\r\n          return null;\r\n      }\r\n\r\n      return { startDate, endDate };\r\n    },\r\n\r\n    // 格式化日期为yyyy-MM-dd格式\r\n    formatDate(date) {\r\n      const year = date.getFullYear();\r\n      const month = (date.getMonth() + 1).toString().padStart(2, \"0\");\r\n      const day = date.getDate().toString().padStart(2, \"0\");\r\n      return `${year}-${month}-${day}`;\r\n    },\r\n\r\n    // 搜索处理\r\n    handleSearch() {\r\n      this.loadPatrolRecords();\r\n    },\r\n\r\n    // 清除搜索关键词\r\n    clearSearch() {\r\n      this.searchKeyword = \"\";\r\n      this.loadPatrolRecords();\r\n    },\r\n\r\n    // 加载更多记录\r\n    loadMoreRecords() {\r\n      if (!this.hasMore || this.isLoading) return;\r\n      this.currentPage++;\r\n      this.loadPatrolRecords(true);\r\n    },\r\n\r\n    // 显示时间筛选弹窗\r\n    showTimeFilter() {\r\n      this.$refs.timeFilterPopup.open();\r\n    },\r\n\r\n    // 显示状态筛选弹窗\r\n    showStatusFilter() {\r\n      this.$refs.statusFilterPopup.open();\r\n    },\r\n\r\n    // 选择时间筛选\r\n    selectTimeFilter(value) {\r\n      // 如果选择的是当前值，不执行刷新\r\n      if (this.timeFilter === value) {\r\n        this.$refs.timeFilterPopup.close();\r\n        return;\r\n      }\r\n      \r\n      this.timeFilter = value;\r\n      this.currentPage = 1;\r\n      this.patrolRecords = [];\r\n      this.hasMore = true;\r\n      this.$refs.timeFilterPopup.close();\r\n      \r\n      // 立即显示加载提示\r\n      uni.showLoading({\r\n        title: \"加载中...\"\r\n      });\r\n      \r\n      // 延迟一下确保弹窗关闭后再加载数据\r\n      setTimeout(() => {\r\n        this.loadPatrolRecords();\r\n      }, 100);\r\n    },\r\n\r\n    // 选择状态筛选\r\n    selectStatusFilter(value) {\r\n      // 如果选择的是当前值，不执行刷新\r\n      if (this.statusFilter === value) {\r\n        this.$refs.statusFilterPopup.close();\r\n        return;\r\n      }\r\n      \r\n      this.statusFilter = value;\r\n      this.currentPage = 1;\r\n      this.patrolRecords = [];\r\n      this.hasMore = true;\r\n      this.$refs.statusFilterPopup.close();\r\n      \r\n      // 立即显示加载提示\r\n      uni.showLoading({\r\n        title: \"加载中...\"\r\n      });\r\n      \r\n      // 延迟一下确保弹窗关闭后再加载数据\r\n      setTimeout(() => {\r\n        this.loadPatrolRecords();\r\n      }, 100);\r\n    },\r\n\r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        pending: \"待执行\",\r\n        processing: \"执行中\",\r\n        completed: \"已完成\",\r\n\t\toverdue: \"已超时\",\r\n      };\r\n      return statusMap[status] || \"未知\";\r\n    },\r\n\r\n    // 格式化日期时间\r\n    formatDateTime(dateTime) {\r\n      if (!dateTime) return \"\";\r\n\r\n      // 处理数组格式的日期时间 [year, month, day]或[year, month, day, hour, minute, second]\r\n      if (Array.isArray(dateTime)) {\r\n        if (dateTime.length >= 3) {\r\n          const year = dateTime[0];\r\n          const month = String(dateTime[1]).padStart(2, \"0\");\r\n          const day = String(dateTime[2]).padStart(2, \"0\");\r\n\r\n          // 如果包含时间部分\r\n          if (dateTime.length >= 5) {\r\n            const hour = String(dateTime[3]).padStart(2, \"0\");\r\n            const minute = String(dateTime[4]).padStart(2, \"0\");\r\n            let second = \"00\";\r\n            if (dateTime.length > 5) second = String(dateTime[5]).padStart(2, \"0\");\r\n            return `${year}-${month}-${day} ${hour}:${minute}:${second}`;\r\n          }\r\n\r\n          return `${year}-${month}-${day}`;\r\n        }\r\n        return dateTime.join(\"-\"); // 如果无法解析，返回用-连接的数组\r\n      }\r\n\r\n      // 确保dateTime是字符串类型\r\n      const dateTimeStr = String(dateTime);\r\n\r\n      // 处理ISO格式日期时间\r\n      if (dateTimeStr.includes(\"T\")) {\r\n        const date = new Date(dateTimeStr);\r\n        const year = date.getFullYear();\r\n        const month = String(date.getMonth() + 1).padStart(2, \"0\");\r\n        const day = String(date.getDate()).padStart(2, \"0\");\r\n        const hours = String(date.getHours()).padStart(2, \"0\");\r\n        const minutes = String(date.getMinutes()).padStart(2, \"0\");\r\n        const seconds = String(date.getSeconds()).padStart(2, \"0\");\r\n        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n      }\r\n\r\n      // 如果是数字类型（时间戳），转换为日期字符串\r\n      if (!isNaN(dateTime) && typeof dateTime === \"number\") {\r\n        const date = new Date(dateTime);\r\n        const year = date.getFullYear();\r\n        const month = String(date.getMonth() + 1).padStart(2, \"0\");\r\n        const day = String(date.getDate()).padStart(2, \"0\");\r\n        const hours = String(date.getHours()).padStart(2, \"0\");\r\n        const minutes = String(date.getMinutes()).padStart(2, \"0\");\r\n        const seconds = String(date.getSeconds()).padStart(2, \"0\");\r\n        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n      }\r\n\r\n      // 对于其他格式的字符串，尝试用Date解析\r\n      try {\r\n        const date = new Date(dateTimeStr);\r\n        if (!isNaN(date.getTime())) {\r\n          const year = date.getFullYear();\r\n          const month = String(date.getMonth() + 1).padStart(2, \"0\");\r\n          const day = String(date.getDate()).padStart(2, \"0\");\r\n          const hours = String(date.getHours()).padStart(2, \"0\");\r\n          const minutes = String(date.getMinutes()).padStart(2, \"0\");\r\n          const seconds = String(date.getSeconds()).padStart(2, \"0\");\r\n\r\n          // 确认是否有时间部分\r\n          if (hours !== \"00\" || minutes !== \"00\" || seconds !== \"00\") {\r\n            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n          } else {\r\n            return `${year}-${month}-${day}`;\r\n          }\r\n        }\r\n\r\n        // 如果Date解析失败，尝试按原样返回或处理特殊格式\r\n        return dateTimeStr;\r\n      } catch (e) {\r\n        console.error(\"格式化日期时间出错:\", e, dateTimeStr);\r\n        return dateTimeStr; // 如果无法格式化，返回原始值\r\n      }\r\n    },\r\n    // 开始巡检\r\n    startPatrol(id,planId) {\r\n        // 标记需要在返回页面时刷新数据\r\n        this._needRefresh = true;\r\n        uni.navigateTo({\r\n        url: `/pages/patrol/execute?id=${id}&&planId=${planId}`,\r\n      });\r\n    },\r\n    // 格式化日期（不含时间）\r\n    formatDateOnly(date) {\r\n      if (!date) return \"\";\r\n\r\n      // 处理数组格式的日期 [year, month, day]\r\n      if (Array.isArray(date)) {\r\n        if (date.length >= 3) {\r\n          const year = date[0];\r\n          const month = String(date[1]).padStart(2, \"0\");\r\n          const day = String(date[2]).padStart(2, \"0\");\r\n          return `${year}-${month}-${day}`;\r\n        }\r\n        return date.join(\"-\"); // 如果无法解析，返回用-连接的数组\r\n      }\r\n\r\n      // 使用formatDateTime函数，但是只返回日期部分\r\n      const fullDateTime = this.formatDateTime(date);\r\n      return fullDateTime.split(\" \")[0];\r\n    },\r\n\r\n    // 格式化时间，只提取时间部分 (HH:MM:SS)\r\n    formatTimeOnly(dateTime) {\r\n      if (!dateTime) return \"\";\r\n\r\n      // 先获取完整的日期时间格式\r\n      const fullDateTime = this.formatDateTime(dateTime);\r\n\r\n      // 如果包含空格（日期和时间分隔符），提取时间部分\r\n      if (fullDateTime.includes(\" \")) {\r\n        return fullDateTime.split(\" \")[1];\r\n      }\r\n\r\n      return fullDateTime; // 如果没有空格，可能只是时间或格式不符合预期，直接返回\r\n    },\r\n\r\n    // 查看记录详情\r\n    viewRecordDetail(id) {\r\n      // 标记需要在返回页面时刷新数据\r\n      this._needRefresh = true;\r\n      uni.navigateTo({\r\n        url: `/pages/patrol/record_detail?id=${id}`,\r\n        success: (res) => {\r\n          console.log(\"跳转成功\", res);\r\n        },\r\n        fail: (err) => {\r\n          console.error(\"跳转失败\", err);\r\n          // 显示错误信息\r\n          uni.showToast({\r\n            title: \"页面跳转失败\",\r\n            icon: \"none\",\r\n            duration: 2000,\r\n          });\r\n        },\r\n      });\r\n    },\r\n    retryLoading() {\r\n      this.showError = false;\r\n      this.errorMsg = \"\"; // 重置错误信息\r\n      uni.showLoading({\r\n        title: \"正在重试...\",\r\n      });\r\n\r\n      setTimeout(() => {\r\n        uni.hideLoading();\r\n        this.loadPatrolRecords();\r\n      }, 1000);\r\n    },\r\n\r\n    // 跳转到提交巡检页面\r\n    goToCreatePatrol() {\r\n      console.log(\"点击提交巡检按钮\");\r\n      uni.showLoading({\r\n        title: \"加载中...\",\r\n      });\r\n\r\n      setTimeout(() => {\r\n        uni.hideLoading();\r\n      }, 500);\r\n\r\n      // 使用绝对路径\r\n      uni.navigateTo({\r\n        url: \"/pages/patrol/create_record\",\r\n        success: () => {\r\n          console.log(\"导航成功\");\r\n        },\r\n        fail: (err) => {\r\n          console.error(\"导航失败:\", err);\r\n          // 显示提示\r\n          uni.showToast({\r\n            title: \"页面跳转失败\",\r\n            icon: \"none\",\r\n            duration: 2000,\r\n          });\r\n        },\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n@import \"../../static/styles/iconfont.scss\";\r\n\r\n.patrol-records-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 100vh;\r\n  background-color: #f7f8fa;\r\n  position: relative;\r\n  width: 100%;\r\n  padding-bottom: 120rpx; // 为底部按钮留出空间\r\n}\r\n\r\n// 页面顶部区域\r\n.page-header {\r\n  background-color: #1890ff;\r\n  padding-bottom: 20rpx;\r\n  position: relative;\r\n  z-index: 100;\r\n  width: 100%;\r\n  top: 0rpx;\r\n  position: sticky;\r\n  z-index: 999;\r\n}\r\n\r\n.header-title {\r\n  font-size: 34rpx;\r\n  font-weight: bold;\r\n  color: #fff;\r\n  text-align: center;\r\n  padding: 30rpx 0 20rpx;\r\n}\r\n\r\n// 搜索栏优化\r\n.search-bar {\r\n  padding: 30rpx 30rpx 20rpx;\r\n\r\n  .search-input-wrapper {\r\n    display: flex;\r\n    align-items: center;\r\n    background-color: rgba(255, 255, 255, 0.9);\r\n    border-radius: 40rpx;\r\n    padding: 12rpx 20rpx;\r\n    margin: 0 auto;\r\n    max-width: 680rpx;\r\n\r\n    .iconfont {\r\n      font-size: 32rpx;\r\n      color: #8a8a8a;\r\n\r\n      &.icon-clear {\r\n        padding: 10rpx;\r\n      }\r\n    }\r\n\r\n    input {\r\n      flex: 1;\r\n      height: 44rpx;\r\n      font-size: 28rpx;\r\n      margin: 0 20rpx;\r\n      color: #333;\r\n    }\r\n  }\r\n\r\n  // 筛选选项区域\r\n  .filter-options {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 0 10rpx;\r\n    max-width: 680rpx;\r\n    margin-left: auto;\r\n    margin-right: auto;\r\n\r\n    .filter-option {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      padding: 6rpx 30rpx;\r\n      background-color: rgba(255, 255, 255, 0.9);\r\n      border-radius: 8rpx;\r\n      flex: 1;\r\n      margin: 0 10rpx;\r\n\r\n      .option-text {\r\n        font-size: 22rpx;\r\n        color: rgba(0, 0, 0, 0.5);\r\n      }\r\n\r\n      .option-value {\r\n        font-size: 24rpx;\r\n        color: #333;\r\n        font-weight: 500;\r\n        margin-top: 2rpx;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 记录列表优化\r\n.record-list {\r\n  padding: 20rpx;\r\n}\r\n\r\n.record-item {\r\n  background-color: #fff;\r\n  border-radius: 16rpx;\r\n  margin-bottom: 30rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);\r\n  transition: all 0.3s ease;\r\n  box-sizing: border-box;\r\n  border-left: 4rpx solid #1890ff;\r\n\r\n  &:active {\r\n    transform: scale(0.98);\r\n    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);\r\n  }\r\n\r\n  &:nth-child(3n + 1) {\r\n    border-left-color: #1890ff;\r\n  }\r\n\r\n  &:nth-child(3n + 2) {\r\n    border-left-color: #52c41a;\r\n  }\r\n\r\n  &:nth-child(3n + 3) {\r\n    border-left-color: #faad14;\r\n  }\r\n\r\n  .record-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding-bottom: 20rpx;\r\n    border-bottom: 1rpx solid #f0f0f0;\r\n\r\n    .record-title {\r\n      font-size: 32rpx;\r\n      font-weight: bold;\r\n      color: #333;\r\n      max-width: 70%;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      white-space: nowrap;\r\n    }\r\n\r\n    .record-status {\r\n      padding: 6rpx 16rpx;\r\n      border-radius: 30rpx;\r\n      font-size: 24rpx;\r\n      min-width: 80rpx;\r\n      text-align: center;\r\n      font-weight: 600;\r\n\r\n      &.pending {\r\n        background-color: rgba(250, 173, 20, 0.15);\r\n        color: #faad14;\r\n      }\r\n\r\n      &.processing {\r\n        background-color: rgba(24, 144, 255, 0.15);\r\n        color: #1890ff;\r\n      }\r\n\r\n      &.completed {\r\n        background-color: rgba(82, 196, 26, 0.15);\r\n        color: #52c41a;\r\n      }\r\n\t  \r\n\t  &.overdue {\r\n\t    background-color: rgba(255, 0, 0, 0.1);\r\n\t    color: #FF0000;\r\n\t  }\r\n    }\r\n  }\r\n\r\n  .record-info {\r\n    padding: 24rpx 0;\r\n\r\n    .info-row {\r\n      display: flex;\r\n      margin-bottom: 20rpx;\r\n      align-items: center;\r\n\r\n      &:last-child {\r\n        margin-bottom: 0;\r\n      }\r\n\r\n      .info-item {\r\n        flex: 1;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .item-icon {\r\n          font-size: 32rpx;\r\n          color: #8a8a8a;\r\n          margin-right: 12rpx;\r\n          min-width: 34rpx;\r\n          text-align: center;\r\n        }\r\n         .item-label {\r\n            width: 130rpx;\r\n            flex-shrink: 0;\r\n            font-size: 26rpx;\r\n            color: #888888;\r\n          }\r\n        .item-value {\r\n          font-size: 28rpx;\r\n          color: #888888;\r\n          word-break: break-all;\r\n          flex: 1;\r\n\r\n          &.abnormal {\r\n            color: #ff4d4f;\r\n            font-weight: bold;\r\n          }\r\n        }\r\n      }\r\n\r\n      &.date-location {\r\n        .info-item:first-child {\r\n          margin-right: 20rpx;\r\n        }\r\n      }\r\n\r\n      &.time-row {\r\n        align-items: center;\r\n\r\n        .time-item {\r\n          flex: none;\r\n          min-width: 150rpx;\r\n        }\r\n\r\n        .time-separator {\r\n          color: #8a8a8a;\r\n          margin: 0 16rpx;\r\n          font-size: 28rpx;\r\n        }\r\n      }\r\n\r\n      &.executor-abnormal {\r\n        .abnormal-item {\r\n          margin-left: 30rpx;\r\n          flex: 0.5;\r\n\r\n          .abnormal {\r\n            color: #ff4d4f;\r\n            font-weight: bold;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .record-footer {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding-top: 20rpx;\r\n    border-top: 1rpx solid #f0f0f0;\r\n    margin-top: 10rpx;\r\n\r\n    .record-time {\r\n      font-size: 24rpx;\r\n      color: #8a8a8a;\r\n    }\r\n\r\n    .view-detail {\r\n      display: flex;\r\n      align-items: center;\r\n      color: #1890ff;\r\n      font-size: 26rpx;\r\n      font-weight: 500;\r\n      background-color: rgba(24, 144, 255, 0.1);\r\n      padding: 8rpx 16rpx;\r\n      border-radius: 30rpx;\r\n\r\n      .btn-text {\r\n        margin-right: 8rpx;\r\n      }\r\n       &.start {\r\n\t\tbackground-color: rgba(24, 144, 255, 0.1);\r\n\t\tcolor: #1890ff;\r\n\t\tmargin-left: 70rpx;\r\n\t  }\r\n       &.view {\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tcolor: #666666;\r\n\t\tmargin-right: 0;\r\n\t\tpadding: 8rpx 20rpx;\r\n\t  }\r\n      .iconfont {\r\n        font-size: 24rpx;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 加载状态\r\n.loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 40vh;\r\n  \r\n  .loading-spinner {\r\n    width: 70rpx;\r\n    height: 70rpx;\r\n    border: 6rpx solid #f3f3f3;\r\n    border-top: 6rpx solid #1890ff;\r\n    border-radius: 50%;\r\n    animation: spin 1s linear infinite;\r\n    margin-bottom: 20rpx;\r\n  }\r\n  .loading-text {\r\n    font-size: 28rpx;\r\n    color: #999;\r\n  }\r\n  @keyframes spin {\r\n    0% {\r\n      transform: rotate(0deg);\r\n    }\r\n    100% {\r\n      transform: rotate(360deg);\r\n    }\r\n  }\r\n}\r\n\r\n// 错误状态\r\n.error-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 50rpx 0;\r\n\r\n  .error-text {\r\n    font-size: 28rpx;\r\n    color: #f5222d;\r\n    margin-bottom: 20rpx;\r\n  }\r\n\r\n  .retry-btn {\r\n    font-size: 28rpx;\r\n    color: #fff;\r\n    background-color: #1890ff;\r\n    padding: 8rpx 30rpx;\r\n    border-radius: 30rpx;\r\n  }\r\n}\r\n\r\n// 空状态\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 100rpx 0;\r\n\r\n  .empty-image {\r\n    width: 200rpx;\r\n    height: 200rpx;\r\n    margin-bottom: 30rpx;\r\n  }\r\n\r\n  .empty-text {\r\n    font-size: 28rpx;\r\n    color: #999;\r\n  }\r\n}\r\n\r\n// 筛选弹窗优化\r\n.filter-popup {\r\n  background-color: #fff;\r\n  border-top-left-radius: 20rpx;\r\n  border-top-right-radius: 20rpx;\r\n  overflow: hidden;\r\n\r\n  .popup-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 30rpx;\r\n    border-bottom: 1rpx solid #f0f0f0;\r\n\r\n    .popup-title {\r\n      font-size: 32rpx;\r\n      font-weight: bold;\r\n      color: #333;\r\n    }\r\n\r\n    .popup-close {\r\n      font-size: 28rpx;\r\n      color: #1890ff;\r\n      padding: 10rpx;\r\n    }\r\n  }\r\n\r\n  .popup-content {\r\n    padding: 10rpx 0;\r\n    max-height: 600rpx;\r\n    overflow-y: auto;\r\n\r\n    .filter-option {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 28rpx 30rpx;\r\n      font-size: 30rpx;\r\n      color: #333;\r\n\r\n      &.active {\r\n        color: #1890ff;\r\n        background-color: rgba(24, 144, 255, 0.05);\r\n\r\n        .option-text {\r\n          font-weight: 500;\r\n        }\r\n      }\r\n\r\n      .icon-check {\r\n        color: #1890ff;\r\n        font-size: 30rpx;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 错误提示框优化\r\n.error-box {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(255, 255, 255, 0.95);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 999;\r\n\r\n  .error-content {\r\n    background-color: #fff;\r\n    padding: 60rpx 50rpx;\r\n    border-radius: 20rpx;\r\n    text-align: center;\r\n    width: 80%;\r\n    max-width: 600rpx;\r\n    box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);\r\n\r\n    .error-icon {\r\n      font-size: 80rpx;\r\n      color: #faad14;\r\n      margin-bottom: 40rpx;\r\n      display: block;\r\n    }\r\n\r\n    .error-text {\r\n      font-size: 32rpx;\r\n      color: #333;\r\n      margin-bottom: 50rpx;\r\n      line-height: 1.6;\r\n      display: block;\r\n    }\r\n\r\n    .retry-btn {\r\n      background-color: #1890ff;\r\n      color: #fff;\r\n      padding: 18rpx 80rpx;\r\n      border-radius: 45rpx;\r\n      font-size: 32rpx;\r\n      margin-top: 20rpx;\r\n      border: none;\r\n      box-shadow: 0 5rpx 15rpx rgba(24, 144, 255, 0.3);\r\n    }\r\n  }\r\n}\r\n\r\n// 浮动提交按钮优化\r\n.floating-btn {\r\n  position: fixed;\r\n  right: 40rpx;\r\n  bottom: 120rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 100rpx;\r\n  height: 100rpx;\r\n  background: linear-gradient(135deg, #1890ff, #096dd9);\r\n  border-radius: 50%;\r\n  box-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.4);\r\n  z-index: 99;\r\n  transition: transform 0.2s, box-shadow 0.2s;\r\n\r\n  &:active {\r\n    transform: scale(0.95);\r\n    box-shadow: 0 3rpx 8rpx rgba(24, 144, 255, 0.3);\r\n  }\r\n\r\n  .plus-icon {\r\n    font-size: 60rpx;\r\n    color: #fff;\r\n    font-weight: bold;\r\n    line-height: 1;\r\n    margin-top: -6rpx; /* 微调居中位置 */\r\n  }\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/patrol/records.vue'\nwx.createPage(MiniProgramPage)"], "names": ["permissionMixin", "uni", "patrolApi"], "mappings": ";;;;;AA+LA,MAAO,kBAAiB,MAAW;AAEnC,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA;AAAA,EACD;AAAA,EACD,QAAQ,CAACA,sBAAAA,eAAe;AAAA;AAAA,EACxB,OAAO;AACL,WAAO;AAAA,MACL,eAAe;AAAA,MACf,eAAe,CAAE;AAAA,MACjB,aAAa;AAAA,MACb,UAAU;AAAA,MACV,SAAS;AAAA,MACT,WAAW;AAAA,MACX,WAAW;AAAA,MACX,cAAc;AAAA;AAAA,MACd,eAAe;AAAA;AAAA,MACf,UAAU;AAAA;AAAA;AAAA,MAGV,YAAY;AAAA,MACZ,cAAc;AAAA;AAAA,MAGd,aAAa;AAAA,QACX,EAAE,OAAO,QAAQ,OAAO,MAAO;AAAA,QAC/B,EAAE,OAAO,MAAM,OAAO,QAAS;AAAA,QAC/B,EAAE,OAAO,MAAM,OAAO,YAAa;AAAA,QACnC,EAAE,OAAO,MAAM,OAAO,aAAc;AAAA,QACpC,EAAE,OAAO,OAAO,OAAO,aAAc;AAAA,MACtC;AAAA,MACD,eAAe;AAAA,QACb,EAAE,OAAO,QAAQ,OAAO,MAAO;AAAA,QAC/B,EAAE,OAAO,OAAO,OAAO,UAAW;AAAA,QAClC,EAAE,OAAO,OAAO,OAAO,aAAc;AAAA,QACrC,EAAE,OAAO,OAAO,OAAO,YAAa;AAAA,QAC1C,EAAE,OAAO,OAAO,OAAO,UAAW;AAAA,MAC7B;AAAA,MACD,WAAW;AAAA,MACX,cAAc;AAAA;EAEjB;AAAA,EACD,UAAU;AAAA,IACR,iBAAiB;AACf,YAAM,SAAS,KAAK,YAAY,KAAK,CAAC,SAAS,KAAK,UAAU,KAAK,UAAU;AAC7E,aAAO,SAAS,OAAO,QAAQ;AAAA,IAChC;AAAA,IACD,mBAAmB;AACjB,YAAM,SAAS,KAAK,cAAc,KAAK,CAAC,SAAS,KAAK,UAAU,KAAK,YAAY;AACjF,aAAO,SAAS,OAAO,QAAQ;AAAA,IAChC;AAAA,IACD,YAAY;AAEV,UAAI,KAAK,eAAe,SAAS,KAAK,iBAAiB,SAAS,KAAK,eAAe;AAClF,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAAA,EACD;AAAA,EACD,SAAS;AACP,SAAK,kBAAiB;AAAA,EACvB;AAAA;AAAA,EAED,oBAAoB;AAClB,SAAK,cAAc;AACnB,SAAK,gBAAgB;AACrB,SAAK,UAAU;AACf,SAAK,oBAAoB,KAAK,MAAM;AAClCC,oBAAG,MAAC,oBAAmB;AAAA,IACzB,CAAC;AAAA,EACF;AAAA;AAAA,EAED,gBAAgB;AACd,QAAI,KAAK,WAAW,CAAC,KAAK,WAAW;AACnC,WAAK;AACL,WAAK,kBAAkB,IAAI;AAAA,IAC7B;AAAA,EACD;AAAA,EACD,SAAQ;AAEN,QAAI,KAAK,cAAc,WAAW,KAAK,KAAK,cAAc;AACxD,WAAK,eAAe;AACpB,WAAK,kBAAiB;AAAA,IACxB;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,MAAM,kBAAkB,aAAa,OAAO;AAC1C,UAAI,KAAK;AAAW;AAGpB,YAAM,MAAM,KAAK;AACjB,UAAI,CAAC,cAAc,MAAM,KAAK,gBAAgB,KAAK;AACjD;AAAA,MACF;AACA,WAAK,gBAAgB;AAErB,WAAK,YAAY;AAEjB,UAAI,CAAC,YAAY;AACf,aAAK,YAAY;AAAA,MACnB;AAEHA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AACE,UAAI;AAEF,cAAM,SAAS;AAAA,UACb,MAAM,KAAK;AAAA,UACX,UAAU,KAAK;AAAA,UACf,SAAS,KAAK;AAAA,UACd,QAAQ,KAAK,iBAAiB,QAAQ,KAAK,eAAe;AAAA,UAC1D,WAAW,KAAK,eAAe,QAAQ,KAAK,aAAa;AAAA;AAAA,UAEzD,QAAQ;AAAA;AAGVA,sBAAA,MAAA,MAAA,OAAA,mCAAY,SAAS,MAAM;AAG3B,YAAI,KAAK,eAAe,OAAO;AAC7B,gBAAM,YAAY,KAAK,yBAAyB,KAAK,UAAU;AAC/D,cAAI,WAAW;AACb,mBAAO,YAAY,UAAU;AAC7B,mBAAO,UAAU,UAAU;AAAA,UAC7B;AAAA,QACF;AAGA,cAAM,iBAAiB,IAAI,QAAQ,CAAC,GAAG,WAAW;AAChD,qBAAW,MAAM;AACf,mBAAO,IAAI,MAAM,MAAM,CAAC;AAAA,UACzB,GAAE,GAAI;AAAA,QACT,CAAC;AAGD,cAAM,MAAM,MAAM,QAAQ,KAAK;AAAA,UAC7BC,UAAS,UAAC,iBAAiB,MAAM;AAAA,UACjC;AAAA,QACF,CAAC;AACDD,sBAAY,MAAA,MAAA,OAAA,mCAAA,UAAU,GAAG;AAEzB,YAAI,IAAI,SAAS,KAAK;AAEpB,cAAI,cAAc,CAAA;AAClB,cAAI,aAAa;AAEjB,cAAI,MAAM,QAAQ,IAAI,IAAI,GAAG;AAE3B,0BAAc,IAAI;AAClB,yBAAa,KAAK,KAAK,YAAY,SAAS,KAAK,QAAQ;AAAA,UAC3D,WAAW,IAAI,QAAQ,IAAI,KAAK,MAAM;AAEpC,0BAAc,IAAI,KAAK;AACvB,yBAAa,IAAI,KAAK,cAAc,KAAK,KAAK,IAAI,KAAK,QAAQ,KAAK,QAAQ,KAAK;AAAA,UACnF,WAAW,IAAI,MAAM;AAEnB,0BAAc,MAAM,QAAQ,IAAI,IAAI,IAAI,IAAI,OAAO,CAAC,IAAI,IAAI;AAC5D,yBAAa;AAAA,UACf;AAGA,gBAAM,UAAU,YAAY,IAAI,CAAC,YAAY;AAAA,YAC3C,IAAI,OAAO;AAAA,YACX,UAAU,OAAO;AAAA,YACjB,cAAc,OAAO;AAAA,YACrB,cAAc,OAAO,gBAAgB;AAAA,YACrC,WAAW,OAAO;AAAA,YAClB,SAAS,OAAO;AAAA,YAChB,QAAQ,OAAO;AAAA,YACf,YAAY,OAAO;AAAA,YACnB,eAAe,OAAO;AAAA,YACtB,YAAY,OAAO;AAAA;AAAA,UAEpB,EAAC;AAEF,cAAI,YAAY;AAEd,iBAAK,gBAAgB,CAAC,GAAG,KAAK,eAAe,GAAG,OAAO;AAAA,iBAClD;AAEL,iBAAK,gBAAgB;AAAA,UACvB;AAGA,eAAK,UAAU,KAAK,cAAc;AACxCA,wBAAG,MAAC,YAAW;AAAA,eACJ;AACL,eAAK,YAAY;AACvBA,wBAAG,MAAC,YAAW;AACTA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,IAAI,WAAW;AAAA,YACtB,MAAM;AAAA,YACN,UAAU;AAAA,UACZ,CAAC;AAAA,QACH;AAAA,MACA,SAAO,KAAK;AAChBA,sBAAG,MAAC,YAAW;AACXA,sBAAc,MAAA,MAAA,SAAA,mCAAA,aAAa,GAAG;AAC9B,aAAK,YAAY;AAGjB,YAAI,IAAI,YAAY,QAAQ;AAC1B,eAAK,WAAW;AAAA,mBACP,IAAI,YAAY,IAAI,SAAS,WAAW,KAAK;AACtD,eAAK,WAAW;AAAA,eACX;AACL,eAAK,WAAW;AAAA,QAClB;AAEAA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,KAAK;AAAA,UACZ,MAAM;AAAA,UACN,UAAU;AAAA,QACZ,CAAC;AAAA,MACH,UAAU;AACR,aAAK,YAAY;AAAA,MACnB;AAAA,IACD;AAAA;AAAA,IAGD,yBAAyB,YAAY;AACnC,YAAM,MAAM,oBAAI;AAChB,UAAI,YAAY;AAChB,UAAI,UAAU;AAEd,cAAQ,YAAU;AAAA,QAChB,KAAK;AACH,sBAAY,KAAK,WAAW,GAAG;AAC/B,oBAAU,KAAK,WAAW,GAAG;AAC7B;AAAA,QACF,KAAK;AAEH,gBAAM,YAAY,IAAI,OAAM,KAAM;AAClC,gBAAM,aAAa,IAAI,KAAK,GAAG;AAC/B,qBAAW,QAAQ,IAAI,QAAO,IAAK,YAAY,CAAC;AAChD,sBAAY,KAAK,WAAW,UAAU;AACtC,oBAAU,KAAK,WAAW,GAAG;AAC7B;AAAA,QACF,KAAK;AAEH,gBAAM,kBAAkB,IAAI,KAAK,IAAI,YAAa,GAAE,IAAI,YAAY,CAAC;AACrE,sBAAY,KAAK,WAAW,eAAe;AAC3C,oBAAU,KAAK,WAAW,GAAG;AAC7B;AAAA,QACF,KAAK;AAEH,gBAAM,sBAAsB,IAAI,KAAK,IAAI,YAAW,GAAI,IAAI,SAAW,IAAE,GAAG,CAAC;AAE7E,gBAAM,qBAAqB,IAAI,KAAK,IAAI,YAAa,GAAE,IAAI,YAAY,CAAC;AACxE,sBAAY,KAAK,WAAW,mBAAmB;AAC/C,oBAAU,KAAK,WAAW,kBAAkB;AAC5C;AAAA,QACF;AACE,iBAAO;AAAA,MACX;AAEA,aAAO,EAAE,WAAW;IACrB;AAAA;AAAA,IAGD,WAAW,MAAM;AACf,YAAM,OAAO,KAAK;AAClB,YAAM,SAAS,KAAK,aAAa,GAAG,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC9D,YAAM,MAAM,KAAK,QAAS,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACrD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,IAC/B;AAAA;AAAA,IAGD,eAAe;AACb,WAAK,kBAAiB;AAAA,IACvB;AAAA;AAAA,IAGD,cAAc;AACZ,WAAK,gBAAgB;AACrB,WAAK,kBAAiB;AAAA,IACvB;AAAA;AAAA,IAGD,kBAAkB;AAChB,UAAI,CAAC,KAAK,WAAW,KAAK;AAAW;AACrC,WAAK;AACL,WAAK,kBAAkB,IAAI;AAAA,IAC5B;AAAA;AAAA,IAGD,iBAAiB;AACf,WAAK,MAAM,gBAAgB;IAC5B;AAAA;AAAA,IAGD,mBAAmB;AACjB,WAAK,MAAM,kBAAkB;IAC9B;AAAA;AAAA,IAGD,iBAAiB,OAAO;AAEtB,UAAI,KAAK,eAAe,OAAO;AAC7B,aAAK,MAAM,gBAAgB;AAC3B;AAAA,MACF;AAEA,WAAK,aAAa;AAClB,WAAK,cAAc;AACnB,WAAK,gBAAgB;AACrB,WAAK,UAAU;AACf,WAAK,MAAM,gBAAgB;AAG3BA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAGD,iBAAW,MAAM;AACf,aAAK,kBAAiB;AAAA,MACvB,GAAE,GAAG;AAAA,IACP;AAAA;AAAA,IAGD,mBAAmB,OAAO;AAExB,UAAI,KAAK,iBAAiB,OAAO;AAC/B,aAAK,MAAM,kBAAkB;AAC7B;AAAA,MACF;AAEA,WAAK,eAAe;AACpB,WAAK,cAAc;AACnB,WAAK,gBAAgB;AACrB,WAAK,UAAU;AACf,WAAK,MAAM,kBAAkB;AAG7BA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAGD,iBAAW,MAAM;AACf,aAAK,kBAAiB;AAAA,MACvB,GAAE,GAAG;AAAA,IACP;AAAA;AAAA,IAGD,cAAc,QAAQ;AACpB,YAAM,YAAY;AAAA,QAChB,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,WAAW;AAAA,QACjB,SAAS;AAAA;AAEL,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,eAAe,UAAU;AACvB,UAAI,CAAC;AAAU,eAAO;AAGtB,UAAI,MAAM,QAAQ,QAAQ,GAAG;AAC3B,YAAI,SAAS,UAAU,GAAG;AACxB,gBAAM,OAAO,SAAS,CAAC;AACvB,gBAAM,QAAQ,OAAO,SAAS,CAAC,CAAC,EAAE,SAAS,GAAG,GAAG;AACjD,gBAAM,MAAM,OAAO,SAAS,CAAC,CAAC,EAAE,SAAS,GAAG,GAAG;AAG/C,cAAI,SAAS,UAAU,GAAG;AACxB,kBAAM,OAAO,OAAO,SAAS,CAAC,CAAC,EAAE,SAAS,GAAG,GAAG;AAChD,kBAAM,SAAS,OAAO,SAAS,CAAC,CAAC,EAAE,SAAS,GAAG,GAAG;AAClD,gBAAI,SAAS;AACb,gBAAI,SAAS,SAAS;AAAG,uBAAS,OAAO,SAAS,CAAC,CAAC,EAAE,SAAS,GAAG,GAAG;AACrE,mBAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM,IAAI,MAAM;AAAA,UAC5D;AAEA,iBAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,QAChC;AACA,eAAO,SAAS,KAAK,GAAG;AAAA,MAC1B;AAGA,YAAM,cAAc,OAAO,QAAQ;AAGnC,UAAI,YAAY,SAAS,GAAG,GAAG;AAC7B,cAAM,OAAO,IAAI,KAAK,WAAW;AACjC,cAAM,OAAO,KAAK;AAClB,cAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,cAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,cAAM,QAAQ,OAAO,KAAK,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AACrD,cAAM,UAAU,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACzD,cAAM,UAAU,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACzD,eAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,OAAO,IAAI,OAAO;AAAA,MAC/D;AAGA,UAAI,CAAC,MAAM,QAAQ,KAAK,OAAO,aAAa,UAAU;AACpD,cAAM,OAAO,IAAI,KAAK,QAAQ;AAC9B,cAAM,OAAO,KAAK;AAClB,cAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,cAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,cAAM,QAAQ,OAAO,KAAK,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AACrD,cAAM,UAAU,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACzD,cAAM,UAAU,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACzD,eAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,OAAO,IAAI,OAAO;AAAA,MAC/D;AAGA,UAAI;AACF,cAAM,OAAO,IAAI,KAAK,WAAW;AACjC,YAAI,CAAC,MAAM,KAAK,QAAS,CAAA,GAAG;AAC1B,gBAAM,OAAO,KAAK;AAClB,gBAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,gBAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,gBAAM,QAAQ,OAAO,KAAK,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AACrD,gBAAM,UAAU,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACzD,gBAAM,UAAU,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AAGzD,cAAI,UAAU,QAAQ,YAAY,QAAQ,YAAY,MAAM;AAC1D,mBAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,OAAO,IAAI,OAAO;AAAA,iBACxD;AACL,mBAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,UAChC;AAAA,QACF;AAGA,eAAO;AAAA,MACT,SAAS,GAAG;AACVA,sBAAA,MAAA,MAAA,SAAA,mCAAc,cAAc,GAAG,WAAW;AAC1C,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAED,YAAY,IAAG,QAAQ;AAEnB,WAAK,eAAe;AACpBA,oBAAAA,MAAI,WAAW;AAAA,QACf,KAAK,4BAA4B,EAAE,YAAY,MAAM;AAAA,MACvD,CAAC;AAAA,IACF;AAAA;AAAA,IAED,eAAe,MAAM;AACnB,UAAI,CAAC;AAAM,eAAO;AAGlB,UAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,YAAI,KAAK,UAAU,GAAG;AACpB,gBAAM,OAAO,KAAK,CAAC;AACnB,gBAAM,QAAQ,OAAO,KAAK,CAAC,CAAC,EAAE,SAAS,GAAG,GAAG;AAC7C,gBAAM,MAAM,OAAO,KAAK,CAAC,CAAC,EAAE,SAAS,GAAG,GAAG;AAC3C,iBAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,QAChC;AACA,eAAO,KAAK,KAAK,GAAG;AAAA,MACtB;AAGA,YAAM,eAAe,KAAK,eAAe,IAAI;AAC7C,aAAO,aAAa,MAAM,GAAG,EAAE,CAAC;AAAA,IACjC;AAAA;AAAA,IAGD,eAAe,UAAU;AACvB,UAAI,CAAC;AAAU,eAAO;AAGtB,YAAM,eAAe,KAAK,eAAe,QAAQ;AAGjD,UAAI,aAAa,SAAS,GAAG,GAAG;AAC9B,eAAO,aAAa,MAAM,GAAG,EAAE,CAAC;AAAA,MAClC;AAEA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,iBAAiB,IAAI;AAEnB,WAAK,eAAe;AACpBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,kCAAkC,EAAE;AAAA,QACzC,SAAS,CAAC,QAAQ;AAChBA,wBAAY,MAAA,MAAA,OAAA,mCAAA,QAAQ,GAAG;AAAA,QACxB;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAc,MAAA,MAAA,SAAA,mCAAA,QAAQ,GAAG;AAEzBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACZ,CAAC;AAAA,QACF;AAAA,MACH,CAAC;AAAA,IACF;AAAA,IACD,eAAe;AACb,WAAK,YAAY;AACjB,WAAK,WAAW;AAChBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACf,aAAK,kBAAiB;AAAA,MACvB,GAAE,GAAI;AAAA,IACR;AAAA;AAAA,IAGD,mBAAmB;AACjBA,oBAAAA,MAAA,MAAA,OAAA,mCAAY,UAAU;AACtBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAAA,MAChB,GAAE,GAAG;AAGNA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,QACL,SAAS,MAAM;AACbA,wBAAAA,MAAY,MAAA,OAAA,mCAAA,MAAM;AAAA,QACnB;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAA,MAAA,MAAA,SAAA,mCAAc,SAAS,GAAG;AAE1BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACZ,CAAC;AAAA,QACF;AAAA,MACH,CAAC;AAAA,IACF;AAAA,EACF;AACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5tBA,GAAG,WAAW,eAAe;"}