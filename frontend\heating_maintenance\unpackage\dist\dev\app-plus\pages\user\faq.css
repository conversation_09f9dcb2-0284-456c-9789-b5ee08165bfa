/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.faq-container {
  background-color: #f5f7fa;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.search-box {
  padding: 0.625rem 0.9375rem;
  background-color: #fff;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.search-box .search-input {
  position: relative;
  height: 2.5rem;
  background-color: #f5f7fa;
  border-radius: 1.25rem;
  display: flex;
  align-items: center;
  padding: 0 0.9375rem;
}
.search-box .search-input .search-icon {
  font-size: 1rem;
  color: #999;
  margin-right: 0.3125rem;
}
.search-box .search-input uni-input {
  flex: 1;
  height: 2.5rem;
  font-size: 0.875rem;
}
.search-box .search-input .clear-icon {
  font-size: 0.875rem;
  color: #999;
  padding: 0.3125rem;
}
.faq-content {
  flex: 1;
  padding: 0.625rem 0;
}
.faq-content .faq-section {
  margin-bottom: 0.625rem;
}
.faq-content .faq-section .group-title {
  position: relative;
  padding: 0.625rem 0.9375rem;
  background-color: #f0f5ff;
  border-left: 0.25rem solid #1890ff;
}
.faq-content .faq-section .group-title .group-title-text {
  font-size: 0.9375rem;
  font-weight: bold;
  color: #333;
}
.faq-content .no-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 3.125rem;
}
.faq-content .no-result uni-image {
  width: 6.25rem;
  height: 6.25rem;
  margin-bottom: 0.625rem;
}
.faq-content .no-result uni-text {
  font-size: 0.875rem;
  color: #999;
}
.faq-item {
  background-color: #fff;
  margin-bottom: 0.0625rem;
}
.faq-item .faq-question {
  padding: 0.9375rem;
  display: flex;
  align-items: flex-start;
  position: relative;
}
.faq-item .faq-question .question-marker {
  color: #1890ff;
  font-weight: bold;
  font-size: 0.9375rem;
  margin-right: 0.625rem;
  flex-shrink: 0;
}
.faq-item .faq-question .question-text {
  flex: 1;
  font-size: 0.875rem;
  color: #333;
  line-height: 1.6;
}
.faq-item .faq-question .question-arrow {
  font-size: 0.75rem;
  color: #999;
  margin-left: 0.625rem;
  transition: transform 0.3s;
  transform: rotate(90deg);
}
.faq-item .faq-question .question-arrow.arrow-up {
  transform: rotate(-90deg);
}
.faq-item .faq-answer {
  padding: 0 0.9375rem 0.9375rem;
  display: flex;
  align-items: flex-start;
  background-color: #f9f9f9;
}
.faq-item .faq-answer .answer-marker {
  color: #f5222d;
  font-weight: bold;
  font-size: 0.9375rem;
  margin-right: 0.625rem;
  flex-shrink: 0;
}
.faq-item .faq-answer .answer-text {
  flex: 1;
  font-size: 0.875rem;
  color: #666;
  line-height: 1.8;
}
.feedback-section {
  padding: 0.9375rem;
  background-color: #fff;
  border-top: 0.03125rem solid #eee;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
  margin-top: 0.625rem;
}
.feedback-section .feedback-text {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.625rem;
}
.feedback-section .feedback-btn {
  padding: 0.46875rem 1.875rem;
  background-color: #1890ff;
  color: #fff;
  font-size: 0.875rem;
  border-radius: 1.25rem;
}
.feedback-section .feedback-btn:active {
  opacity: 0.8;
}