{"version": 3, "file": "profile.js", "sources": ["pages/user/profile.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlci9wcm9maWxlLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"profile-container\">\r\n\t\t<view class=\"form-card\">\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"form-label\">姓名</text>\r\n\t\t\t\t<view class=\"form-input\">\r\n\t\t\t\t\t<input type=\"text\" v-model=\"userInfo.name\" placeholder=\"请输入姓名\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- <view class=\"form-item\">\r\n\t\t\t\t<text class=\"form-label\">工号</text>\r\n\t\t\t\t<view class=\"form-input\">\r\n\t\t\t\t\t<text class=\"input-readonly\">{{ userInfo.id }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view> -->\r\n\t\t\t<!-- <view class=\"form-item\">\r\n\t\t\t\t<text class=\"form-label\">账号</text>\r\n\t\t\t\t<view class=\"form-input\">\r\n\t\t\t\t\t<text class=\"input-readonly\">{{userInfo.username}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view> -->\r\n\t\t\t\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"form-label\">手机号码</text>\r\n\t\t\t\t<view class=\"form-input\">\r\n\t\t\t\t\t<input type=\"number\" v-model=\"userInfo.phone\" placeholder=\"请输入手机号码\" maxlength=\"11\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"form-label\">邮箱</text>\r\n\t\t\t\t<view class=\"form-input\">\r\n\t\t\t\t\t<input type=\"text\" v-model=\"userInfo.email\" placeholder=\"请输入邮箱\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\r\n\t\t\t\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"form-label\">部门</text>\r\n\t\t\t\t<view class=\"form-input\">\r\n\t\t\t\t\t<text class=\"input-readonly\">{{ userInfo.department }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"submit-btn\" @click=\"saveProfile\">保存修改</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { userApi } from '../../utils/api';\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tuserInfo: {\r\n\t\t\t\tname: '',\r\n\t\t\t\tid: '',\r\n\t\t\t\tphone: '',\r\n\t\t\t\temail: '',\r\n\t\t\t\trole: '',\r\n\t\t\t\tdepartment: ''\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\t\t// 初始加载用户信息\r\n\t\tthis.loadUserInfo();\r\n\t},\r\n\tonShow() {\r\n\t\t// 每次显示页面时都重新加载用户信息，确保数据最新\r\n\t\tthis.loadUserInfo();\r\n\t},\r\n\tmethods: {\r\n\t\t// 加载用户信息\r\n\t\tloadUserInfo() {\r\n\t\t\t// 从本地存储获取用户信息\r\n\t\t\tconst userInfo = uni.getStorageSync('userInfo');\r\n\t\t\tconst userId = uni.getStorageSync('userId');\r\n\t\t\tconst userRole = uni.getStorageSync('userRole');\r\n\t\t\t\r\n\t\t\tif (userInfo) {\r\n\t\t\t\tconsole.log('本地存储的用户信息:', userInfo);\r\n\t\t\t\t\r\n\t\t\t\t// 更新数据 - 检查是否有user字段\r\n\t\t\t\tconst userData = userInfo.user;\r\n\t\t\t\tthis.userInfo = {\r\n\t\t\t\t\tname: userData.name,\r\n\t\t\t\t\tphone: userData.phone || '',\r\n\t\t\t\t\temail: userData.email || '',\r\n\t\t\t\t\trole: this.getRoleName(userRole) || '普通用户',\r\n\t\t\t\t\tdepartment: userData.department || '未分配'\r\n\t\t\t\t};\r\n\t\t\t} else {\r\n\t\t\t\t// 如果本地没有用户信息，从服务器获取\r\n\t\t\t\tthis.fetchUserInfoFromServer();\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 从服务器获取用户信息\r\n\t\tfetchUserInfoFromServer() {\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中...'\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\tuserApi.getUserInfo()\r\n\t\t\t\t.then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\t\t// 存储用户信息\r\n\t\t\t\t\t\tuni.setStorageSync('userInfo', res.data);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 更新数据 - 检查是否有user字段\r\n\t\t\t\t\t\tconst userData = res.data.user || res.data;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tthis.userInfo = {\r\n\t\t\t\t\t\t\tname: userData.name || userData.username || '用户',\r\n\t\t\t\t\t\t\tid: userData.id || res.data.userId || 'USER-' + Date.now(),\r\n\t\t\t\t\t\t\tphone: userData.phone || '',\r\n\t\t\t\t\t\t\temail: userData.email || '',\r\n\t\t\t\t\t\t\trole: this.getRoleName(userData.role || res.data.role) || '普通用户',\r\n\t\t\t\t\t\t\tdepartment: userData.department || '未分配'\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.showError('获取用户信息失败');\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tconsole.error('获取用户信息失败:', err);\r\n\t\t\t\t\tthis.showError('网络错误，请稍后重试');\r\n\t\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 角色名称转换\r\n\t\tgetRoleName(role) {\r\n\t\t\tconst roleMap = {\r\n\t\t\t\t'admin': '系统管理员',\r\n\t\t\t\t'manager': '主管',\r\n\t\t\t\t'engineer': '维修工程师',\r\n\t\t\t\t'operator': '操作员',\r\n\t\t\t\t'user': '普通用户'\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\treturn roleMap[role] || role || '普通用户';\r\n\t\t},\r\n\t\t\r\n\t\t// 保存用户资料\r\n\t\tsaveProfile() {\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '保存中...'\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 验证输入\r\n\t\t\tif (!this.userInfo.name || this.userInfo.name.trim() === '') {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tthis.showError('姓名不能为空');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (this.userInfo.phone && !/^1\\d{10}$/.test(this.userInfo.phone)) {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tthis.showError('请输入正确的手机号码');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (this.userInfo.email && !/^[\\w-]+(\\.[\\w-]+)*@[\\w-]+(\\.[\\w-]+)+$/.test(this.userInfo.email)) {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tthis.showError('请输入正确的邮箱地址');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 准备更新的数据\r\n\t\t\tconst updateData = {\r\n\t\t\t\tname: this.userInfo.name,\r\n\t\t\t\tphone: this.userInfo.phone,\r\n\t\t\t\temail: this.userInfo.email\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 调用API保存用户信息\r\n\t\t\tuserApi.updateUserInfo(updateData)\r\n\t\t\t\t.then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\t\t// 更新本地存储\r\n\t\t\t\t\t\tconst userInfo = uni.getStorageSync('userInfo') || {};\r\n\t\t\t\t\t\t// 检查是否有user字段\r\n\t\t\t\t\t\tif (userInfo.user) {\r\n\t\t\t\t\t\t\tuserInfo.user.name = this.userInfo.name;\r\n\t\t\t\t\t\t\tuserInfo.user.phone = this.userInfo.phone;\r\n\t\t\t\t\t\t\tuserInfo.user.email = this.userInfo.email;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuserInfo.name = this.userInfo.name;\r\n\t\t\t\t\t\t\tuserInfo.phone = this.userInfo.phone;\r\n\t\t\t\t\t\t\tuserInfo.email = this.userInfo.email;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tuni.setStorageSync('userInfo', userInfo);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '保存成功',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 返回上一页\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.navigateBack();\r\n\t\t\t\t\t\t}, 1500);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.showError(res.message || '保存失败');\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tconsole.error('保存用户资料失败:', err);\r\n\t\t\t\t\tthis.showError('网络错误，请稍后重试');\r\n\t\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 显示错误提示\r\n\t\tshowError(message) {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: message,\r\n\t\t\t\ticon: 'none'\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.profile-container {\r\n\tbackground-color: #f5f7fa;\r\n\tmin-height: 100vh;\r\n\tpadding-bottom: 40rpx;\r\n}\r\n\r\n.form-card {\r\n\tmargin: 30rpx;\r\n\tbackground-color: #fff;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 20rpx;\r\n\tbox-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.05);\r\n\t\r\n\t.form-item {\r\n\t\tpadding: 24rpx 20rpx;\r\n\t\tborder-bottom: 1rpx solid rgba(0, 0, 0, 0.05);\r\n\t\t\r\n\t\t&:last-child {\r\n\t\t\tborder-bottom: none;\r\n\t\t}\r\n\t\t\r\n\t\t.form-label {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #666;\r\n\t\t\tmargin-bottom: 16rpx;\r\n\t\t\tdisplay: block;\r\n\t\t}\r\n\t\t\r\n\t\t.form-input {\r\n\t\t\tinput {\r\n\t\t\t\theight: 80rpx;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.input-readonly {\r\n\t\t\t\theight: 80rpx;\r\n\t\t\t\tline-height: 80rpx;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #999;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.submit-btn {\r\n\tmargin: 60rpx 30rpx;\r\n\theight: 90rpx;\r\n\tline-height: 90rpx;\r\n\tbackground-color: $uni-color-primary;\r\n\tcolor: #fff;\r\n\ttext-align: center;\r\n\tborder-radius: 12rpx;\r\n\tfont-size: 32rpx;\r\n\tbox-shadow: 0 6rpx 20rpx rgba(24, 144, 255, 0.3);\r\n\tfont-weight: bold;\r\n\t\r\n\t&:active {\r\n\t\topacity: 0.9;\r\n\t\ttransform: translateY(2rpx);\r\n\t}\r\n}\r\n</style> ", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaan<PERSON>_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/user/profile.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "userApi"], "mappings": ";;;AAoDA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,UAAU;AAAA,QACT,MAAM;AAAA,QACN,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,YAAY;AAAA,MACb;AAAA,IACD;AAAA,EACA;AAAA,EACD,SAAS;AAER,SAAK,aAAY;AAAA,EACjB;AAAA,EACD,SAAS;AAER,SAAK,aAAY;AAAA,EACjB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,eAAe;AAEd,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC/BA,oBAAG,MAAC,eAAe,QAAQ;AAC1C,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAE9C,UAAI,UAAU;AACbA,sBAAA,MAAA,MAAA,OAAA,gCAAY,cAAc,QAAQ;AAGlC,cAAM,WAAW,SAAS;AAC1B,aAAK,WAAW;AAAA,UACf,MAAM,SAAS;AAAA,UACf,OAAO,SAAS,SAAS;AAAA,UACzB,OAAO,SAAS,SAAS;AAAA,UACzB,MAAM,KAAK,YAAY,QAAQ,KAAK;AAAA,UACpC,YAAY,SAAS,cAAc;AAAA;aAE9B;AAEN,aAAK,wBAAuB;AAAA,MAC7B;AAAA,IACA;AAAA;AAAA,IAGD,0BAA0B;AACzBA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACR,CAAC;AAEDC,gBAAAA,QAAQ,YAAY,EAClB,KAAK,SAAO;AACZD,sBAAG,MAAC,YAAW;AACf,YAAI,IAAI,SAAS,KAAK;AAErBA,wBAAAA,MAAI,eAAe,YAAY,IAAI,IAAI;AAGvC,gBAAM,WAAW,IAAI,KAAK,QAAQ,IAAI;AAEtC,eAAK,WAAW;AAAA,YACf,MAAM,SAAS,QAAQ,SAAS,YAAY;AAAA,YAC5C,IAAI,SAAS,MAAM,IAAI,KAAK,UAAU,UAAU,KAAK,IAAK;AAAA,YAC1D,OAAO,SAAS,SAAS;AAAA,YACzB,OAAO,SAAS,SAAS;AAAA,YACzB,MAAM,KAAK,YAAY,SAAS,QAAQ,IAAI,KAAK,IAAI,KAAK;AAAA,YAC1D,YAAY,SAAS,cAAc;AAAA;eAE9B;AACN,eAAK,UAAU,UAAU;AAAA,QAC1B;AAAA,OACA,EACA,MAAM,SAAO;AACbA,sBAAG,MAAC,YAAW;AACfA,4EAAc,aAAa,GAAG;AAC9B,aAAK,UAAU,YAAY;AAAA,MAC5B,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,YAAY,MAAM;AACjB,YAAM,UAAU;AAAA,QACf,SAAS;AAAA,QACT,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA;AAGT,aAAO,QAAQ,IAAI,KAAK,QAAQ;AAAA,IAChC;AAAA;AAAA,IAGD,cAAc;AACbA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACR,CAAC;AAGD,UAAI,CAAC,KAAK,SAAS,QAAQ,KAAK,SAAS,KAAK,KAAO,MAAI,IAAI;AAC5DA,sBAAG,MAAC,YAAW;AACf,aAAK,UAAU,QAAQ;AACvB;AAAA,MACD;AAEA,UAAI,KAAK,SAAS,SAAS,CAAC,YAAY,KAAK,KAAK,SAAS,KAAK,GAAG;AAClEA,sBAAG,MAAC,YAAW;AACf,aAAK,UAAU,YAAY;AAC3B;AAAA,MACD;AAEA,UAAI,KAAK,SAAS,SAAS,CAAC,wCAAwC,KAAK,KAAK,SAAS,KAAK,GAAG;AAC9FA,sBAAG,MAAC,YAAW;AACf,aAAK,UAAU,YAAY;AAC3B;AAAA,MACD;AAGA,YAAM,aAAa;AAAA,QAClB,MAAM,KAAK,SAAS;AAAA,QACpB,OAAO,KAAK,SAAS;AAAA,QACrB,OAAO,KAAK,SAAS;AAAA;AAItBC,gBAAO,QAAC,eAAe,UAAU,EAC/B,KAAK,SAAO;AACZD,sBAAG,MAAC,YAAW;AAEf,YAAI,IAAI,SAAS,KAAK;AAErB,gBAAM,WAAWA,cAAG,MAAC,eAAe,UAAU,KAAK,CAAA;AAEnD,cAAI,SAAS,MAAM;AAClB,qBAAS,KAAK,OAAO,KAAK,SAAS;AACnC,qBAAS,KAAK,QAAQ,KAAK,SAAS;AACpC,qBAAS,KAAK,QAAQ,KAAK,SAAS;AAAA,iBAC9B;AACN,qBAAS,OAAO,KAAK,SAAS;AAC9B,qBAAS,QAAQ,KAAK,SAAS;AAC/B,qBAAS,QAAQ,KAAK,SAAS;AAAA,UAChC;AACAA,wBAAAA,MAAI,eAAe,YAAY,QAAQ;AAEvCA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAGD,qBAAW,MAAM;AAChBA,0BAAG,MAAC,aAAY;AAAA,UAChB,GAAE,IAAI;AAAA,eACD;AACN,eAAK,UAAU,IAAI,WAAW,MAAM;AAAA,QACrC;AAAA,OACA,EACA,MAAM,SAAO;AACbA,sBAAG,MAAC,YAAW;AACfA,4EAAc,aAAa,GAAG;AAC9B,aAAK,UAAU,YAAY;AAAA,MAC5B,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,UAAU,SAAS;AAClBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;AClOA,GAAG,WAAW,eAAe;"}