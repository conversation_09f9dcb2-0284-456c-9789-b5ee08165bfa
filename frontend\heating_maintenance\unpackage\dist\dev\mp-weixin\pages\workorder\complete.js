"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const utils_upload = require("../../utils/upload.js");
const _sfc_main = {
  data() {
    return {
      orderId: null,
      formData: {
        repairContent: "",
        repairResult: "",
        repairDate: "",
        repairTime: "",
        repairDateTime: "",
        attachment: []
      },
      repairMaterialsList: [
        { name: "", quantity: "" }
      ],
      rules: {
        repairContent: [
          { required: true, message: "请输入维修内容" }
        ],
        repairResult: [
          { required: true, message: "请输入维修结果" }
        ],
        repairDate: [
          { required: true, message: "请选择维修日期" }
        ],
        repairTime: [
          { required: true, message: "请选择维修时间" }
        ]
      },
      dateTimeArray: [[], [], [], [], [], []],
      dateTimeIndex: [0, 0, 0, 0, 0, 0],
      imageList: [],
      // 本地临时路径
      serverImageList: [],
      // 服务器路径
      videoPath: "",
      // 本地临时路径
      serverVideoPath: "",
      // 服务器路径
      isUploading: false,
      // 新增材料相关数据
      materialOptions: [],
      // 材料选项列表
      materialSearchKeyword: "",
      // 材料搜索关键词
      filteredMaterials: [],
      // 过滤后的材料列表
      currentMaterialIndex: -1,
      // 当前正在编辑的材料索引
      tempSelectedMaterial: null,
      // 临时选中的材料
      isPopupOpen: false
      // 弹窗状态
    };
  },
  onLoad(options) {
    if (options.id) {
      this.orderId = options.id;
    } else {
      this.showError("缺少工单ID");
      setTimeout(() => {
        common_vendor.index.navigateBack();
      }, 1500);
    }
    this.initDateTimePicker();
    const now = /* @__PURE__ */ new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const day = String(now.getDate()).padStart(2, "0");
    const hours = String(now.getHours()).padStart(2, "0");
    const minutes = String(now.getMinutes()).padStart(2, "0");
    this.formData.repairDate = `${year}-${month}-${day}`;
    this.formData.repairTime = `${hours}:${minutes}:00`;
    this.updateRepairDateTime();
    this.loadMaterialsList();
  },
  methods: {
    // 添加维修耗材
    addMaterial() {
      this.repairMaterialsList.push({ name: "", quantity: "" });
    },
    // 删除维修耗材
    removeMaterial(index) {
      if (this.repairMaterialsList.length > 1) {
        this.repairMaterialsList.splice(index, 1);
      } else {
        this.repairMaterialsList = [{ name: "", quantity: "" }];
      }
    },
    // 获取耗材数据（JSON格式）
    getRepairMaterialsQuantity() {
      const result = {};
      this.repairMaterialsList.forEach((item) => {
        if (item.name && item.name.trim()) {
          result[item.name.trim()] = item.quantity || 0;
        }
      });
      return result;
    },
    // 加载材料列表
    loadMaterialsList() {
      common_vendor.index.showLoading({
        title: "加载材料列表..."
      });
      utils_api.materialsApi.getMaterialsList().then((res) => {
        common_vendor.index.hideLoading();
        if (res.code === 200 && res.data) {
          this.materialOptions = res.data;
          this.filteredMaterials = [...res.data];
        } else {
          this.showError("获取材料列表失败");
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/workorder/complete.vue:286", "获取材料列表失败:", err);
        this.showError("获取材料列表失败，请重试");
      });
    },
    // 显示材料选择器
    showMaterialSelectorPopup(index) {
      this.currentMaterialIndex = index;
      this.tempSelectedMaterial = null;
      const currentMaterial = this.repairMaterialsList[index];
      if (currentMaterial && currentMaterial.name) {
        const foundMaterial = this.materialOptions.find((m) => m.name === currentMaterial.name);
        if (foundMaterial) {
          this.tempSelectedMaterial = foundMaterial;
        }
      }
      this.materialSearchKeyword = "";
      this.filteredMaterials = [...this.materialOptions];
      this.isPopupOpen = true;
      this.$refs.materialSelector.open();
    },
    // 隐藏材料选择器
    hideMaterialSelector() {
      this.isPopupOpen = false;
      this.$refs.materialSelector.close();
    },
    // 选择材料
    selectMaterial(material) {
      this.tempSelectedMaterial = material;
    },
    // 确认材料选择
    confirmMaterialSelection() {
      if (this.tempSelectedMaterial && this.currentMaterialIndex !== -1) {
        this.repairMaterialsList[this.currentMaterialIndex].name = this.tempSelectedMaterial.name;
      }
      this.hideMaterialSelector();
    },
    // 搜索材料
    handleMaterialSearch() {
      if (!this.materialSearchKeyword || this.materialSearchKeyword.trim() === "") {
        this.filteredMaterials = [...this.materialOptions];
        return;
      }
      const keyword = this.materialSearchKeyword.toLowerCase().trim();
      this.filteredMaterials = this.materialOptions.filter(
        (material) => material.name.toLowerCase().includes(keyword) || material.material_type && material.material_type.toLowerCase().includes(keyword)
      );
    },
    // 清空材料搜索
    clearMaterialSearch() {
      this.materialSearchKeyword = "";
      this.filteredMaterials = [...this.materialOptions];
    },
    // 初始化日期时间选择器
    initDateTimePicker() {
      const date = /* @__PURE__ */ new Date();
      const currentYear = date.getFullYear();
      const years = [];
      for (let i = currentYear - 2; i <= currentYear + 2; i++) {
        years.push(i + "年");
      }
      const months = [];
      for (let i = 1; i <= 12; i++) {
        months.push(i + "月");
      }
      const days = [];
      for (let i = 1; i <= 31; i++) {
        days.push(i + "日");
      }
      const hours = [];
      for (let i = 0; i <= 23; i++) {
        hours.push(i + "时");
      }
      const minutes = [];
      for (let i = 0; i <= 59; i++) {
        minutes.push(i + "分");
      }
      const seconds = [];
      for (let i = 0; i <= 59; i++) {
        seconds.push(i + "秒");
      }
      this.dateTimeArray = [years, months, days, hours, minutes, seconds];
      const yearIndex = 2;
      const monthIndex = date.getMonth();
      const dayIndex = date.getDate() - 1;
      const hourIndex = date.getHours();
      const minuteIndex = date.getMinutes();
      const secondIndex = date.getSeconds();
      this.dateTimeIndex = [yearIndex, monthIndex, dayIndex, hourIndex, minuteIndex, secondIndex];
    },
    // 处理日期时间选择器变化
    onDateTimeChange(e) {
      const values = e.detail.value;
      this.dateTimeIndex = values;
      const year = parseInt(this.dateTimeArray[0][values[0]]);
      const month = String(parseInt(this.dateTimeArray[1][values[1]])).padStart(2, "0");
      const day = String(parseInt(this.dateTimeArray[2][values[2]])).padStart(2, "0");
      const hour = String(parseInt(this.dateTimeArray[3][values[3]])).padStart(2, "0");
      const minute = String(parseInt(this.dateTimeArray[4][values[4]])).padStart(2, "0");
      const second = String(parseInt(this.dateTimeArray[5][values[5]])).padStart(2, "0");
      this.formData.repairDate = `${year}-${month}-${day}`;
      this.formData.repairTime = `${hour}:${minute}:${second}`;
      this.updateRepairDateTime();
    },
    // 处理日期时间选择器列变化
    onColumnChange(e) {
      let column = e.detail.column;
      let value = e.detail.value;
      if (column === 0 || column === 1) {
        const year = parseInt(this.dateTimeArray[0][this.dateTimeIndex[0]]);
        const month = parseInt(this.dateTimeArray[1][value === void 0 ? this.dateTimeIndex[1] : value]);
        const daysInMonth = new Date(year, month, 0).getDate();
        const days = [];
        for (let i = 1; i <= daysInMonth; i++) {
          days.push(i + "日");
        }
        this.dateTimeArray[2] = days;
        if (this.dateTimeIndex[2] >= daysInMonth) {
          this.dateTimeIndex[2] = daysInMonth - 1;
        }
      }
    },
    // 更新合并后的日期时间
    updateRepairDateTime() {
      if (this.formData.repairDate && this.formData.repairTime) {
        const dateTimeStr = `${this.formData.repairDate} ${this.formData.repairTime}`;
        this.formData.repairDateTime = dateTimeStr;
      }
    },
    // 获取一年前的日期
    getOneYearAgo() {
      const date = /* @__PURE__ */ new Date();
      date.setFullYear(date.getFullYear() - 1);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    // 获取当前日期
    getCurrentDate() {
      const date = /* @__PURE__ */ new Date();
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    // 选择图片
    chooseImage() {
      common_vendor.index.chooseImage({
        count: 6 - this.imageList.length,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          common_vendor.index.showLoading({
            title: "上传中...",
            mask: true
          });
          const token = common_vendor.index.getStorageSync("token");
          if (!token) {
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "请先登录",
              icon: "none"
            });
            return;
          }
          const uploadPromises = res.tempFilePaths.map((path) => {
            return utils_upload.uploadUtils.uploadImage(path).then((serverPath) => {
              this.imageList.push(path);
              this.serverImageList.push(serverPath);
              return serverPath;
            });
          });
          Promise.all(uploadPromises).then(() => {
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "上传成功",
              icon: "success"
            });
          }).catch((err) => {
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: err.message || "上传失败",
              icon: "none"
            });
          });
        }
      });
    },
    // 预览图片
    previewImage(index) {
      const previewUrls = this.imageList.map((path, i) => {
        if (this.serverImageList[i]) {
          return utils_upload.uploadUtils.getFileUrl(this.serverImageList[i]);
        }
        return path;
      });
      common_vendor.index.previewImage({
        urls: previewUrls,
        current: previewUrls[index]
      });
    },
    // 删除图片
    deleteImage(index) {
      this.imageList.splice(index, 1);
      this.serverImageList.splice(index, 1);
    },
    // 选择视频
    chooseVideo() {
      common_vendor.index.chooseVideo({
        sourceType: ["album", "camera"],
        maxDuration: 60,
        camera: "back",
        success: (res) => {
          common_vendor.index.showLoading({
            title: "上传中...",
            mask: true
          });
          const token = common_vendor.index.getStorageSync("token");
          if (!token) {
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "请先登录",
              icon: "none"
            });
            return;
          }
          utils_upload.uploadUtils.uploadVideo(res.tempFilePath).then((serverPath) => {
            this.videoPath = res.tempFilePath;
            this.serverVideoPath = serverPath;
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "上传成功",
              icon: "success"
            });
          }).catch((err) => {
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: err.message || "上传失败",
              icon: "none"
            });
          });
        }
      });
    },
    // 获取视频URL
    getVideoUrl() {
      if (this.serverVideoPath && this.serverVideoPath.startsWith("@http")) {
        const cleanUrl = this.serverVideoPath.substring(1);
        common_vendor.index.__f__("log", "at pages/workorder/complete.vue:612", "视频路径以@http开头，清理后:", cleanUrl);
        return cleanUrl;
      }
      if (this.serverVideoPath) {
        if (this.serverVideoPath.startsWith("http")) {
          return this.serverVideoPath;
        }
        const url = utils_upload.uploadUtils.getFileUrl(this.serverVideoPath);
        common_vendor.index.__f__("log", "at pages/workorder/complete.vue:623", "处理后的视频URL:", url);
        return url;
      }
      common_vendor.index.__f__("log", "at pages/workorder/complete.vue:628", "使用本地视频路径:", this.videoPath);
      return this.videoPath;
    },
    // 视频加载错误处理
    onVideoError(e) {
      common_vendor.index.__f__("error", "at pages/workorder/complete.vue:634", "视频加载失败:", e.detail);
      common_vendor.index.__f__("error", "at pages/workorder/complete.vue:635", "视频路径:", this.videoPath);
      common_vendor.index.__f__("error", "at pages/workorder/complete.vue:636", "服务器视频路径:", this.serverVideoPath);
      common_vendor.index.__f__("error", "at pages/workorder/complete.vue:637", "处理后URL:", this.getVideoUrl());
      common_vendor.index.showToast({
        title: "视频加载失败，请尝试其他方式查看",
        icon: "none",
        duration: 2e3
      });
    },
    // 删除视频
    deleteVideo() {
      this.videoPath = "";
      this.serverVideoPath = "";
    },
    // 取消完成
    cancelComplete() {
      common_vendor.index.navigateBack();
    },
    // 验证表单
    validateForm() {
      let isValid = true;
      if (!this.formData.repairDate) {
        this.showError("请选择维修日期");
        return false;
      }
      if (!this.formData.repairTime) {
        this.showError("请选择维修时间");
        return false;
      }
      for (const key in this.rules) {
        if (key === "repairDate" || key === "repairTime")
          continue;
        const value = this.formData[key];
        const rules = this.rules[key];
        for (const rule of rules) {
          if (rule.required && !value) {
            this.showError(rule.message);
            isValid = false;
            break;
          }
        }
        if (!isValid)
          break;
      }
      return isValid;
    },
    // 上传附件
    uploadAttachments() {
      return new Promise((resolve) => {
        const attachments = [];
        this.serverImageList.forEach((serverPath) => {
          attachments.push({
            file_type: "image",
            file_path: serverPath
          });
        });
        if (this.serverVideoPath) {
          attachments.push({
            file_type: "video",
            file_path: this.serverVideoPath
          });
        }
        resolve(attachments);
      });
    },
    // 提交表单
    submitComplete() {
      if (!this.validateForm())
        return;
      common_vendor.index.showModal({
        title: "确认完成",
        content: "是否确认完成工单？完成后将无法修改",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({
              title: "提交中..."
            });
            this.uploadAttachments().then((attachments) => {
              this.submitOrderComplete(attachments);
            }).catch((err) => {
              common_vendor.index.hideLoading();
              this.showError("附件处理失败: " + err);
            });
          }
        }
      });
    },
    // 提交工单完成
    submitOrderComplete(attachments) {
      const repairMaterialsQuantity = this.getRepairMaterialsQuantity();
      const params = {
        order_id: this.orderId,
        repair_user_id: this.getCurrentUserId(),
        order_status: "已完成",
        repair_content: this.formData.repairContent,
        repair_result: this.formData.repairResult,
        repair_materials_quantity: repairMaterialsQuantity,
        repair_time: this.formData.repairDateTime,
        attachment: attachments
      };
      common_vendor.index.__f__("log", "at pages/workorder/complete.vue:758", "提交参数:", params);
      utils_api.workOrderApi.completeOrder(params).then((res) => {
        common_vendor.index.hideLoading();
        if (res.code === 200) {
          this.showSuccess("工单已完成");
          setTimeout(() => {
            common_vendor.index.navigateBack({
              delta: 1
            });
          }, 1500);
        } else {
          this.showError(res.message || "提交失败");
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        this.showError("网络异常，请稍后重试");
        common_vendor.index.__f__("error", "at pages/workorder/complete.vue:777", "完成工单失败:", err);
      });
    },
    // 获取当前用户ID
    getCurrentUserId() {
      const userId = common_vendor.index.getStorageSync("userId");
      return userId || 1;
    },
    // 显示成功提示
    showSuccess(message) {
      common_vendor.index.showToast({
        title: message,
        icon: "success"
      });
    },
    // 显示错误提示
    showError(message) {
      common_vendor.index.showToast({
        title: message,
        icon: "none"
      });
    }
  }
};
if (!Array) {
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  _easycom_uni_popup2();
}
const _easycom_uni_popup = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.formData.repairContent,
    b: common_vendor.o(($event) => $data.formData.repairContent = $event.detail.value),
    c: $data.formData.repairResult,
    d: common_vendor.o(($event) => $data.formData.repairResult = $event.detail.value),
    e: common_vendor.f($data.repairMaterialsList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name || "请选择耗材"),
        b: common_vendor.o(($event) => $options.showMaterialSelectorPopup(index), index),
        c: item.quantity,
        d: common_vendor.o(($event) => item.quantity = $event.detail.value, index),
        e: common_vendor.o(($event) => $options.removeMaterial(index), index),
        f: index
      };
    }),
    f: common_vendor.o((...args) => $options.addMaterial && $options.addMaterial(...args)),
    g: common_vendor.t($data.formData.repairDate),
    h: common_vendor.t($data.formData.repairTime),
    i: common_vendor.o((...args) => $options.onDateTimeChange && $options.onDateTimeChange(...args)),
    j: common_vendor.o((...args) => $options.onColumnChange && $options.onColumnChange(...args)),
    k: $data.dateTimeIndex,
    l: $data.dateTimeArray,
    m: common_vendor.f($data.imageList, (image, index, i0) => {
      return {
        a: image,
        b: common_vendor.o(($event) => $options.previewImage(index), index),
        c: common_vendor.o(($event) => $options.deleteImage(index), index),
        d: index
      };
    }),
    n: $data.imageList.length < 6
  }, $data.imageList.length < 6 ? {
    o: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args))
  } : {}, {
    p: $data.videoPath
  }, $data.videoPath ? {
    q: $options.getVideoUrl(),
    r: common_vendor.o((...args) => $options.onVideoError && $options.onVideoError(...args)),
    s: common_vendor.o((...args) => $options.deleteVideo && $options.deleteVideo(...args))
  } : {}, {
    t: !$data.videoPath
  }, !$data.videoPath ? {
    v: common_vendor.o((...args) => $options.chooseVideo && $options.chooseVideo(...args))
  } : {}, {
    w: common_vendor.o((...args) => $options.cancelComplete && $options.cancelComplete(...args)),
    x: common_vendor.o((...args) => $options.submitComplete && $options.submitComplete(...args)),
    y: common_vendor.o((...args) => $options.confirmMaterialSelection && $options.confirmMaterialSelection(...args)),
    z: common_vendor.o((...args) => $options.hideMaterialSelector && $options.hideMaterialSelector(...args)),
    A: common_vendor.o([($event) => $data.materialSearchKeyword = $event.detail.value, (...args) => $options.handleMaterialSearch && $options.handleMaterialSearch(...args)]),
    B: $data.materialSearchKeyword,
    C: $data.materialSearchKeyword
  }, $data.materialSearchKeyword ? {
    D: common_vendor.o((...args) => $options.clearMaterialSearch && $options.clearMaterialSearch(...args))
  } : {}, {
    E: common_vendor.o((...args) => $options.handleMaterialSearch && $options.handleMaterialSearch(...args)),
    F: common_vendor.f($data.filteredMaterials, (material, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(material.name),
        b: common_vendor.t(material.material_type),
        c: $data.tempSelectedMaterial && $data.tempSelectedMaterial.id === material.id
      }, $data.tempSelectedMaterial && $data.tempSelectedMaterial.id === material.id ? {} : {}, {
        d: $data.tempSelectedMaterial && $data.tempSelectedMaterial.id === material.id ? 1 : "",
        e: material.id,
        f: common_vendor.o(($event) => $options.selectMaterial(material), material.id)
      });
    }),
    G: $data.filteredMaterials.length === 0
  }, $data.filteredMaterials.length === 0 ? {} : {}, {
    H: common_vendor.sr("materialSelector", "0307937e-0"),
    I: common_vendor.p({
      type: "bottom"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/workorder/complete.js.map
