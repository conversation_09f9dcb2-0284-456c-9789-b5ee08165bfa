"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const CustomTabBar = () => "../../components/CustomTabBar.js";
const _sfc_main = {
  components: {
    CustomTabBar
  },
  data() {
    return {
      searchKeyword: "",
      currentStatus: "all",
      statusTabs: [
        { name: "全部", value: "all", count: 0 },
        { name: "在线", value: "online", count: 0 },
        { name: "离线", value: "offline", count: 0 },
        { name: "故障", value: "fault", count: 0 }
      ],
      deviceList: [],
      page: 1,
      pageSize: 10,
      isLoading: false,
      noMoreData: false,
      filterOptions: {
        type: "",
        area: "",
        sortBy: "installTime",
        sortOrder: "desc"
      },
      deviceTypes: [
        { name: "全部", value: "" },
        { name: "泵类", value: "pump" },
        { name: "阀门", value: "valve" },
        { name: "传感器", value: "sensor" },
        { name: "控制器", value: "controller" }
      ],
      deviceAreas: [
        { name: "全部", value: "" },
        { name: "东区", value: "east" },
        { name: "西区", value: "west" },
        { name: "南区", value: "south" },
        { name: "北区", value: "north" }
      ],
      sortOptions: [
        { name: "安装时间", value: "installTime" },
        { name: "维护周期", value: "maintainPeriod" },
        { name: "告警数量", value: "alarmCount" }
      ]
    };
  },
  onLoad() {
    this.loadDeviceList();
    this.loadDeviceStats();
  },
  methods: {
    // 加载设备列表
    loadDeviceList(append = false) {
      if (this.isLoading)
        return;
      this.isLoading = true;
      ({
        page: this.page,
        pageSize: this.pageSize,
        status: this.currentStatus === "all" ? "" : this.currentStatus,
        keyword: this.searchKeyword,
        ...this.filterOptions
      });
      setTimeout(() => {
        const mockData = this.getMockDeviceList();
        if (append) {
          this.deviceList = [...this.deviceList, ...mockData];
        } else {
          this.deviceList = mockData;
        }
        this.noMoreData = mockData.length < this.pageSize;
        this.isLoading = false;
      }, 500);
    },
    // 加载设备统计
    loadDeviceStats() {
      setTimeout(() => {
        const stats = {
          total: 10,
          online: 5,
          offline: 3,
          fault: 2
        };
        this.statusTabs[0].count = stats.total;
        this.statusTabs[1].count = stats.online;
        this.statusTabs[2].count = stats.offline;
        this.statusTabs[3].count = stats.fault;
      }, 300);
    },
    // 搜索设备
    handleSearch() {
      this.page = 1;
      this.noMoreData = false;
      this.loadDeviceList();
    },
    // 清除搜索
    clearSearch() {
      this.searchKeyword = "";
      this.handleSearch();
    },
    // 切换状态选项卡
    switchStatus(status) {
      if (this.currentStatus === status)
        return;
      this.currentStatus = status;
      this.page = 1;
      this.noMoreData = false;
      this.loadDeviceList();
    },
    // 加载更多设备
    loadMoreDevices() {
      if (this.isLoading || this.noMoreData)
        return;
      this.page++;
      this.loadDeviceList(true);
    },
    // 显示筛选弹窗
    showFilterModal() {
      this.$refs.filterPopup.open();
    },
    // 选择筛选选项
    selectFilterOption(field, value) {
      this.filterOptions[field] = value;
    },
    // 重置筛选条件
    resetFilter() {
      this.filterOptions = {
        type: "",
        area: "",
        sortBy: "installTime",
        sortOrder: "desc"
      };
    },
    // 取消筛选
    cancelFilter() {
      this.$refs.filterPopup.close();
    },
    // 应用筛选
    applyFilter() {
      this.page = 1;
      this.noMoreData = false;
      this.loadDeviceList();
      this.$refs.filterPopup.close();
    },
    // 跳转到设备详情
    navigateToDetail(deviceId) {
      common_vendor.index.navigateTo({
        url: `/pages/device/detail?id=${deviceId}`
      });
    },
    // 跳转到添加设备
    navigateToAdd() {
      common_vendor.index.navigateTo({
        url: "/pages/device/add"
      });
    },
    // 格式化日期
    formatDate(dateString) {
      if (!dateString)
        return "未知";
      const date = new Date(dateString);
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    },
    // 获取模拟设备列表
    getMockDeviceList() {
      return [
        {
          deviceId: "dev_001",
          name: "1号循环泵",
          type: "pump",
          model: "XGZ-2023",
          status: "online",
          location: {
            building: "3号楼",
            floor: "1层",
            room: "泵房",
            coordinates: {
              lat: 39.9042,
              lng: 116.4074
            }
          },
          lastMaintenance: "2023-12-01 10:15:00",
          nextMaintenance: "2023-12-16 10:15:00",
          alarmCount: 0
        },
        {
          deviceId: "dev_002",
          name: "2号阀门",
          type: "valve",
          model: "DFV-2022",
          status: "online",
          location: {
            building: "3号楼",
            floor: "1层",
            room: "阀门间",
            coordinates: {
              lat: 39.9043,
              lng: 116.4075
            }
          },
          lastMaintenance: "2023-12-02 22:30:00",
          nextMaintenance: "2023-12-16 22:30:00",
          alarmCount: 0
        },
        {
          deviceId: "dev_003",
          name: "3号控制器",
          type: "controller",
          model: "CTL-2023",
          status: "fault",
          location: {
            building: "4号楼",
            floor: "1层",
            room: "控制室",
            coordinates: {
              lat: 39.9044,
              lng: 116.4076
            }
          },
          lastMaintenance: "2023-12-03 14:20:00",
          nextMaintenance: "2023-12-17 14:20:00",
          alarmCount: 2
        },
        {
          deviceId: "dev_004",
          name: "温度传感器",
          type: "sensor",
          model: "TSR-100",
          status: "offline",
          location: {
            building: "4号楼",
            floor: "2层",
            room: "机房",
            coordinates: {
              lat: 39.9045,
              lng: 116.4077
            }
          },
          lastMaintenance: "2023-12-04 09:10:00",
          nextMaintenance: "2023-12-18 09:10:00",
          alarmCount: 0
        },
        {
          deviceId: "dev_005",
          name: "3号阀门",
          type: "valve",
          model: "DFV-2022",
          status: "online",
          location: {
            building: "5号楼",
            floor: "1层",
            room: "阀门间",
            coordinates: {
              lat: 39.9046,
              lng: 116.4078
            }
          },
          lastMaintenance: "2023-12-05 19:20:00",
          nextMaintenance: "2023-12-19 19:20:00",
          alarmCount: 0
        }
      ];
    }
  }
};
if (!Array) {
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  _easycom_uni_popup2();
}
const _easycom_uni_popup = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o([($event) => $data.searchKeyword = $event.detail.value, (...args) => $options.handleSearch && $options.handleSearch(...args)]),
    b: $data.searchKeyword,
    c: $data.searchKeyword
  }, $data.searchKeyword ? {
    d: common_vendor.o((...args) => $options.clearSearch && $options.clearSearch(...args))
  } : {}, {
    e: common_vendor.o((...args) => $options.showFilterModal && $options.showFilterModal(...args)),
    f: common_vendor.f($data.statusTabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab.name),
        b: common_vendor.t(tab.count),
        c: index,
        d: $data.currentStatus === tab.value ? 1 : "",
        e: common_vendor.o(($event) => $options.switchStatus(tab.value), index)
      };
    }),
    g: $data.deviceList.length > 0
  }, $data.deviceList.length > 0 ? common_vendor.e({
    h: common_vendor.f($data.deviceList, (device, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.n(device.status),
        b: common_vendor.t(device.name),
        c: common_vendor.t(device.type),
        d: common_vendor.t(device.model || "未知型号"),
        e: common_vendor.t(device.location ? `${device.location.building} ${device.location.room}` : "位置未知"),
        f: common_vendor.t($options.formatDate(device.lastMaintenance)),
        g: common_vendor.t($options.formatDate(device.nextMaintenance)),
        h: device.alarmCount > 0
      }, device.alarmCount > 0 ? {
        i: common_vendor.t(device.alarmCount)
      } : {}, {
        j: device.deviceId,
        k: common_vendor.o(($event) => $options.navigateToDetail(device.deviceId), device.deviceId)
      });
    }),
    i: $data.isLoading
  }, $data.isLoading ? {} : {}, {
    j: $data.noMoreData && !$data.isLoading
  }, $data.noMoreData && !$data.isLoading ? {} : {}) : {
    k: common_assets._imports_0$1
  }, {
    l: common_vendor.o((...args) => $options.loadMoreDevices && $options.loadMoreDevices(...args)),
    m: common_vendor.o((...args) => $options.resetFilter && $options.resetFilter(...args)),
    n: common_vendor.f($data.deviceTypes, (type, index, i0) => {
      return {
        a: common_vendor.t(type.name),
        b: index,
        c: $data.filterOptions.type === type.value ? 1 : "",
        d: common_vendor.o(($event) => $options.selectFilterOption("type", type.value), index)
      };
    }),
    o: common_vendor.f($data.deviceAreas, (area, index, i0) => {
      return {
        a: common_vendor.t(area.name),
        b: index,
        c: $data.filterOptions.area === area.value ? 1 : "",
        d: common_vendor.o(($event) => $options.selectFilterOption("area", area.value), index)
      };
    }),
    p: common_vendor.f($data.sortOptions, (sort, index, i0) => {
      return {
        a: common_vendor.t(sort.name),
        b: index,
        c: $data.filterOptions.sortBy === sort.value ? 1 : "",
        d: common_vendor.o(($event) => $options.selectFilterOption("sortBy", sort.value), index)
      };
    }),
    q: common_vendor.o((...args) => $options.cancelFilter && $options.cancelFilter(...args)),
    r: common_vendor.o((...args) => $options.applyFilter && $options.applyFilter(...args)),
    s: common_vendor.sr("filterPopup", "55605091-0"),
    t: common_vendor.p({
      type: "bottom"
    }),
    v: common_vendor.o((...args) => $options.navigateToAdd && $options.navigateToAdd(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/device/list.js.map
