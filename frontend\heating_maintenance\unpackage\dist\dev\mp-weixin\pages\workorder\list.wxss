/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.workorder-list-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* Tab切换 */
.tab-container {
  display: flex;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 999;
}
.tab-container .tab-item {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 30rpx;
  color: #666;
  position: relative;
}
.tab-container .tab-item.active {
  color: #007aff;
  font-weight: 500;
}
.tab-container .tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #007aff;
  border-radius: 2rpx;
}

/* 顶部筛选 */
.filter-section-seach {
  background-color: #ffffff;
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: -webkit-sticky;
  position: sticky;
  height: 75rpx;
  top: 165rpx;
  z-index: 999;
}
.filter-section-seach .date-filter,
.filter-section-seach .status-filter {
  flex: 1;
  height: 70rpx;
}
.filter-section-seach .date-filter .date-picker,
.filter-section-seach .date-filter .status-picker,
.filter-section-seach .status-filter .date-picker,
.filter-section-seach .status-filter .status-picker {
  display: flex;
  align-items: center;
  height: 70rpx;
  border: 1px solid #e5e5e5;
  border-radius: 6rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
}
.filter-section-seach .date-filter .date-picker .date-text,
.filter-section-seach .date-filter .date-picker .status-text,
.filter-section-seach .date-filter .status-picker .date-text,
.filter-section-seach .date-filter .status-picker .status-text,
.filter-section-seach .status-filter .date-picker .date-text,
.filter-section-seach .status-filter .date-picker .status-text,
.filter-section-seach .status-filter .status-picker .date-text,
.filter-section-seach .status-filter .status-picker .status-text {
  flex: 1;
}
.filter-section-seach .date-filter .date-picker .iconfont,
.filter-section-seach .date-filter .status-picker .iconfont,
.filter-section-seach .status-filter .date-picker .iconfont,
.filter-section-seach .status-filter .status-picker .iconfont {
  margin-left: 10rpx;
  color: #999;
}
.filter-section-seach .date-filter {
  margin-right: 20rpx;
}
.filter-section-seach .status-filter {
  margin-right: 20rpx;
}

/* 筛选条件区域 */
.filter-condition {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 0 30rpx 20rpx;
}
.filter-condition .condition-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #f5f5f5;
  margin-bottom: 20rpx;
}
.filter-condition .condition-header .condition-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}
.filter-condition .condition-header .close-icon {
  font-size: 40rpx;
  color: #999;
  padding: 0 10rpx;
}
.filter-condition .filter-section {
  margin-bottom: 20rpx;
}
.filter-condition .filter-section .section-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.filter-condition .filter-section .section-header .blue-bar {
  width: 6rpx;
  height: 30rpx;
  background-color: #007aff;
  margin-right: 15rpx;
  border-radius: 3rpx;
}
.filter-condition .filter-section .section-header .section-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.filter-condition .filter-section .filter-options {
  display: flex;
  flex-wrap: wrap;
}
.filter-condition .filter-section .filter-options .filter-option {
  padding: 10rpx 24rpx;
  margin-right: 20rpx;
  margin-bottom: 15rpx;
  font-size: 26rpx;
  color: #666;
  background-color: #f5f7fa;
  border-radius: 30rpx;
}
.filter-condition .filter-section .filter-options .filter-option.active {
  color: #fff;
  background-color: #007aff;
}
.filter-condition .filter-section .date-range .date-picker {
  height: 70rpx;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.filter-condition .filter-section .date-range .date-picker .date-text {
  font-size: 26rpx;
  color: #666;
}
.filter-condition .filter-actions {
  display: flex;
  margin-top: 30rpx;
}
.filter-condition .filter-actions button {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  border-radius: 40rpx;
}
.filter-condition .filter-actions button.filter-reset {
  color: #666;
  background-color: #f5f5f5;
  margin-right: 20rpx;
}
.filter-condition .filter-actions button.filter-confirm {
  color: #fff;
  background-color: #007aff;
}

/* 工单列表 */
.workorder-list .workorder-item {
  margin: 20rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.workorder-list .workorder-item .workorder-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.workorder-list .workorder-item .workorder-header .workorder-code {
  font-size: 28rpx;
  color: #666;
}
.workorder-list .workorder-item .workorder-header .status-tag {
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}
.workorder-list .workorder-item .workorder-header .status-tag.pending {
  background-color: #fff7e6;
  color: #fa8c16;
}
.workorder-list .workorder-item .workorder-header .status-tag.processing {
  background-color: #e6f7ff;
  color: #1890ff;
}
.workorder-list .workorder-item .workorder-header .status-tag.completed {
  background-color: #f6ffed;
  color: #52c41a;
}
.workorder-list .workorder-item .workorder-header .status-tag.transferred {
  background-color: #f2f4f8;
  color: #6777ef;
}
.workorder-list .workorder-item .workorder-header .status-tag.cancelled {
  background-color: #f5f5f5;
  color: #999;
}
.workorder-list .workorder-item .workorder-info {
  margin-bottom: 20rpx;
}
.workorder-list .workorder-item .workorder-info .info-item {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.workorder-list .workorder-item .workorder-info .info-item .info-label {
  font-size: 26rpx;
  color: #999;
  width: 150rpx;
}
.workorder-list .workorder-item .workorder-info .info-item .location {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.workorder-list .workorder-item .workorder-info .info-item .fault-type {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}
.workorder-list .workorder-item .workorder-info .info-item .fault-level {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}
.workorder-list .workorder-item .workorder-info .info-item .level-tag {
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 24rpx;
}
.workorder-list .workorder-item .workorder-info .info-item .level-tag.notice {
  background-color: #e6f7ff;
  color: #1890ff;
}
.workorder-list .workorder-item .workorder-info .info-item .level-tag.normal {
  background-color: #e6f7ff;
  color: #1890ff;
}
.workorder-list .workorder-item .workorder-info .info-item .level-tag.important {
  background-color: #fff7e6;
  color: #fa8c16;
}
.workorder-list .workorder-item .workorder-info .info-item .level-tag.critical {
  background-color: #fff1f0;
  color: #f5222d;
}
.workorder-list .workorder-item .workorder-info .info-item .level-tag.default {
  background-color: #f5f5f5;
  color: #999;
}
.workorder-list .workorder-item .workorder-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.workorder-list .workorder-item .workorder-footer .workorder-meta {
  display: flex;
  flex-direction: column;
}
.workorder-list .workorder-item .workorder-footer .workorder-meta .workorder-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}
.workorder-list .workorder-item .workorder-footer .workorder-actions {
  display: flex;
  align-items: center;
}
.workorder-list .workorder-item .workorder-footer .workorder-actions .action-button {
  padding: 10rpx 24rpx;
  margin-left: 20rpx;
  font-size: 24rpx;
  color: #666;
  background-color: #f5f7fa;
  border-radius: 30rpx;
}
.workorder-list .workorder-item .workorder-footer .workorder-actions .action-button.primary {
  color: #fff;
  background-color: #007aff;
}

/* 加载状态 */
.loading-more,
.no-more {
  text-align: center;
  padding: 30rpx;
  font-size: 26rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-state .empty-image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 30rpx;
}
.empty-state .empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}
.empty-state .refresh-button {
  font-size: 28rpx;
  color: #007aff;
  background-color: #fff;
  border: 2rpx solid #007aff;
  border-radius: 40rpx;
  padding: 10rpx 60rpx;
}

/* 重置按钮 */
.refresh-button {
  font-size: 28rpx;
  background-color: #fff;
  border-radius: 5rpx;
  align-items: center;
  height: 72rpx;
  border-radius: 6rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  position: relative;
}