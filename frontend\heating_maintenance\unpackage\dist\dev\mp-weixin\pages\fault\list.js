"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const common_assets = require("../../common/assets.js");
const PermissionCheck = () => "../../components/PermissionCheck.js";
const _sfc_main = {
  components: {
    PermissionCheck
    // 本地注册组件
  },
  data() {
    const now = /* @__PURE__ */ new Date();
    now.getFullYear();
    (now.getMonth() + 1).toString().padStart(2, "0");
    now.getDate().toString().padStart(2, "0");
    return {
      faultList: [],
      isLoading: false,
      loadError: false,
      // 当前激活的标签页
      activeTab: "all",
      // 'all', 'pending', 'confirmed', 'returned'
      // 筛选条件
      filterDate: "",
      currentStatus: "",
      // 分页相关
      page: 1,
      pageSize: 5,
      hasMore: true
    };
  },
  onLoad() {
    this.page = 1;
    this.faultList = [];
    this.hasMore = true;
    this.loadFaultList().then(() => {
      common_vendor.index.stopPullDownRefresh();
    });
  },
  onShow() {
    const app = getApp();
    const needRefresh = app.globalData && app.globalData.refreshFaultList;
    if (needRefresh) {
      this.page = 1;
      this.faultList = [];
      this.hasMore = true;
      app.globalData.refreshFaultList = false;
      this.loadFaultList();
    } else {
      if (this.faultList.length === 0) {
        this.loadFaultList();
      }
    }
  },
  // 下拉刷新
  onPullDownRefresh() {
    this.page = 1;
    this.faultList = [];
    this.hasMore = true;
    this.loadFaultList().then(() => {
      common_vendor.index.stopPullDownRefresh();
    });
  },
  // 上拉加载更多
  onReachBottom() {
    if (this.hasMore && !this.isLoading) {
      this.page++;
      this.loadFaultList(true);
    }
  },
  methods: {
    // 切换标签页
    switchTab(tab) {
      if (this.activeTab !== tab) {
        this.activeTab = tab;
        this.page = 1;
        this.faultList = [];
        this.hasMore = true;
        switch (tab) {
          case "all":
            this.currentStatus = "";
            break;
          case "pending":
            this.currentStatus = "待确认";
            break;
          case "confirmed":
            this.currentStatus = "已确认";
            break;
          case "returned":
            this.currentStatus = "已退回";
            break;
        }
        this.loadFaultList();
      }
    },
    // 格式化时间戳为 yyyy-MM-dd HH:mm:ss 格式
    formatTimestamp(timestamp) {
      if (!timestamp)
        return "";
      const ts = typeof timestamp === "string" ? Number(timestamp) : timestamp;
      const date = new Date(ts.toString().length === 13 ? ts : ts * 1e3);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      const hours = date.getHours().toString().padStart(2, "0");
      const minutes = date.getMinutes().toString().padStart(2, "0");
      const seconds = date.getSeconds().toString().padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    // 加载故障列表
    async loadFaultList(isLoadMore = false) {
      if (this.isLoading)
        return;
      this.isLoading = true;
      if (!isLoadMore) {
        this.loadError = false;
      }
      try {
        const params = {
          page: this.page,
          pageSize: this.pageSize
        };
        if (this.filterDate) {
          params.date = this.filterDate;
        }
        if (this.currentStatus) {
          params.status = this.currentStatus;
        }
        const heatUnitId = common_vendor.index.getStorageSync("heatUnitId");
        common_vendor.index.__f__("log", "at pages/fault/list.vue:283", "heatUnitId==", heatUnitId);
        if (!heatUnitId) {
          common_vendor.index.showToast({
            title: "无项目访问权限",
            icon: "none"
          });
          this.loadError = true;
          this.isLoading = false;
          return;
        }
        params.heatUnitId = heatUnitId;
        const res = await utils_api.faultApi.getFaultList(params);
        if (res.code === 200) {
          const { list, total, totalPages } = res.data;
          if (isLoadMore) {
            this.faultList = [...this.faultList, ...list];
          } else {
            this.faultList = list;
          }
          this.hasMore = this.page < totalPages;
        } else {
          this.loadError = true;
          common_vendor.index.showToast({
            title: res.message || "获取故障列表失败",
            icon: "none"
          });
        }
      } catch (err) {
        common_vendor.index.__f__("error", "at pages/fault/list.vue:322", "获取故障列表异常:", err);
        this.loadError = true;
        common_vendor.index.showToast({
          title: "网络异常，请稍后重试",
          icon: "none"
        });
      } finally {
        this.isLoading = false;
      }
    },
    // 刷新
    async refreshData() {
      this.page = 1;
      this.hasMore = true;
      this.filterDate = "";
      this.faultList = [];
      this.activeTab = "all";
      this.currentStatus = "";
      this.loadFaultList();
    },
    // 查看故障详情
    viewFaultDetail(faultId) {
      common_vendor.index.navigateTo({
        url: `/pages/fault/detail?id=${faultId}`
      });
    },
    // 导航到故障上报页面
    navigateToReport() {
      common_vendor.index.navigateTo({
        url: "/pages/fault/createfault"
      });
    },
    // 日期选择变更
    onDateChange(e) {
      this.filterDate = e.detail.value;
      this.page = 1;
      this.faultList = [];
      this.hasMore = true;
      this.loadFaultList();
    },
    // 获取故障状态对应的样式类
    getFaultStatusClass(status) {
      const statusClassMap = {
        待确认: "pending",
        已确认: "confirmed",
        已完成: "completed",
        已退回: "returned"
      };
      return statusClassMap[status] || "default";
    },
    // 获取故障等级对应的样式类
    getFaultLevelClass(level) {
      const levelClassMap = {
        提示: "notice",
        一般: "normal",
        重要: "important",
        严重: "serious ",
        紧急: "critical"
      };
      return levelClassMap[level] || "default";
    }
  }
};
if (!Array) {
  const _component_PermissionCheck = common_vendor.resolveComponent("PermissionCheck");
  _component_PermissionCheck();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.activeTab === "all" ? 1 : "",
    b: common_vendor.o(($event) => $options.switchTab("all")),
    c: $data.activeTab === "pending" ? 1 : "",
    d: common_vendor.o(($event) => $options.switchTab("pending")),
    e: $data.activeTab === "confirmed" ? 1 : "",
    f: common_vendor.o(($event) => $options.switchTab("confirmed")),
    g: $data.activeTab === "returned" ? 1 : "",
    h: common_vendor.o(($event) => $options.switchTab("returned")),
    i: common_vendor.t($data.filterDate || "选择日期"),
    j: $data.filterDate,
    k: common_vendor.o((...args) => $options.onDateChange && $options.onDateChange(...args)),
    l: common_vendor.o((...args) => $options.refreshData && $options.refreshData(...args)),
    m: $data.faultList.length > 0
  }, $data.faultList.length > 0 ? {
    n: common_vendor.f($data.faultList, (fault, k0, i0) => {
      return {
        a: common_vendor.t(fault.heat_unit_name),
        b: common_vendor.t(fault.fault_status),
        c: common_vendor.n($options.getFaultStatusClass(fault.fault_status)),
        d: common_vendor.t(fault.fault_desc),
        e: common_vendor.t($options.formatTimestamp(fault.occur_time)),
        f: common_vendor.t(fault.fault_level),
        g: common_vendor.n($options.getFaultLevelClass(fault.fault_level)),
        h: common_vendor.t(fault.report_user_name),
        i: common_vendor.t($options.formatTimestamp(fault.report_time)),
        j: fault.fault_id,
        k: common_vendor.o(($event) => $options.viewFaultDetail(fault.fault_id), fault.fault_id)
      };
    })
  } : {}, {
    o: !$data.isLoading && $data.faultList.length === 0
  }, !$data.isLoading && $data.faultList.length === 0 ? {
    p: common_assets._imports_0$5,
    q: common_vendor.o((...args) => $options.refreshData && $options.refreshData(...args))
  } : {}, {
    r: $data.isLoading
  }, $data.isLoading ? {} : {}, {
    s: $data.loadError && !$data.isLoading
  }, $data.loadError && !$data.isLoading ? {
    t: common_vendor.o((...args) => $options.loadFaultList && $options.loadFaultList(...args))
  } : {}, {
    v: common_vendor.o((...args) => $options.navigateToReport && $options.navigateToReport(...args)),
    w: common_vendor.p({
      permission: "fault:report:create"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/fault/list.js.map
