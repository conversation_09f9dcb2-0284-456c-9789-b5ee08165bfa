
package com.heating.service.impl;

import com.heating.dto.order.AcceptOrderRequest;
import com.heating.dto.order.CompleteOrderRequest;
import com.heating.dto.order.CreateOrderRequest;
import com.heating.dto.order.OrderAttachmentRequest;
import com.heating.dto.order.TransferOrderRequest;
import com.heating.dto.order.WorkOrderBaseInfoResponse;
import com.heating.dto.order.WorkOrderDetailResponse;
import com.heating.dto.order.WorkOrderMessageResponse;
import com.heating.entity.fault.TFaultAttachment;
import com.heating.entity.order.TOperationLog;
import com.heating.entity.order.TWorkOrder;
import com.heating.entity.order.TWorkOrderAttachment;
import com.heating.entity.order.TWorkOrderMaterial;
import com.heating.repository.OperationLogRepository;
import com.heating.repository.WorkOrderAttachmentRepository;
import com.heating.repository.WorkOrderMaterialRepository;
import com.heating.repository.WorkOrderRepository;
import com.heating.service.WorkOrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.ArrayList;
import java.util.stream.Collectors;
import java.util.HashMap;

@Service
public class WorkOrderServiceImpl implements WorkOrderService {
    
    private static final Logger logger = LoggerFactory.getLogger(WorkOrderServiceImpl.class);
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    @Autowired
    private WorkOrderRepository workOrderRepository;
    
    @Autowired
    private WorkOrderAttachmentRepository attachmentRepository;
    
    @Autowired
    private OperationLogRepository operationLogRepository;
    
    @Autowired
    private WorkOrderMaterialRepository workOrderMaterialRepository;
 
    /**
     * 完成工单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void completeOrder(CompleteOrderRequest request) {
        logger.info("开始处理工单完成请求: orderId={}", request.getOrderId());
        try {
            // 获取工单
            Optional<TWorkOrder> result = workOrderRepository.findById(request.getOrderId());
            if (result.isEmpty()) {
                logger.warn("工单完成失败：工单不存在 orderId={}", request.getOrderId());
                return;
            }
            // 更新工单
            TWorkOrder workOrder = result.get();
            workOrder.setOrderStatus("已完成");
            workOrder.setRepairUserId(request.getRepairUserId());
            workOrder.setRepairContent(request.getRepairContent());
            workOrder.setRepairResult(request.getRepairResult());
            
            // 处理维修耗材及数量，将Map转为JSON字符串
            if (request.getRepairMaterialsQuantity() != null && !request.getRepairMaterialsQuantity().isEmpty()) {
                try {
                    // 使用Jackson将Map转换为JSON字符串
                    com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
                    String materialsQuantityJson = objectMapper.writeValueAsString(request.getRepairMaterialsQuantity());
                    workOrder.setRepairMaterialsQuantity(materialsQuantityJson);
                    
                    // 保存耗材信息到工单耗材表
                    saveMaterialsToDatabase(request.getOrderId(), request.getRepairMaterialsQuantity());
                } catch (Exception e) {
                    logger.error("维修耗材及数量JSON转换失败: {}", e.getMessage());
                }
            }

            // string 转 LocalDateTime
            LocalDateTime repairTime = LocalDateTime.parse(request.getRepairTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            workOrder.setRepairTime(repairTime);
            workOrder.setUpdatedAt(LocalDateTime.now());
            workOrderRepository.save(workOrder);
            
            // 保存维修附件
            if (request.getAttachment() != null && !request.getAttachment().isEmpty()) {
                List<OrderAttachmentRequest> attachmentList = request.getAttachment();
                for (OrderAttachmentRequest attachmentRequest : attachmentList) {
                    TWorkOrderAttachment attachment = new TWorkOrderAttachment();
                    attachment.setWorkOrderId(request.getOrderId());
                    attachment.setFileType(attachmentRequest.getFileType());
                    attachment.setFilePath(attachmentRequest.getFilePath());
                    attachment.setCreatedAt(LocalDateTime.now());
                    attachmentRepository.save(attachment);
                }
            }

            // 记录操作日志
            TOperationLog log = new TOperationLog();
            log.setWorkOrderId(workOrder.getId()); 
            log.setOperationType("工单完成");
            log.setOperationDesc("维修人员完成工单");
            log.setOperatorId(workOrder.getRepairUserId());
            log.setCreatedAt(LocalDateTime.now());
            operationLogRepository.save(log);
            
            logger.info("工单处理完成: orderId={}", request.getOrderId());
        } catch (Exception e) {
            logger.error("工单处理失败: orderId={}, error={}", request.getOrderId(), e.getMessage()); 
        }
    }

    /**
     * 保存耗材信息到工单耗材表
     * 
     * @param workOrderId 工单ID
     * @param materialsQuantity 耗材及数量Map
     */
    private void saveMaterialsToDatabase(Long workOrderId, Map<String, Integer> materialsQuantity) {
        logger.info("开始保存工单耗材信息: workOrderId={}, 耗材数量={}", workOrderId, materialsQuantity.size());
        try {
            // 遍历耗材Map，保存到工单耗材表
            for (Map.Entry<String, Integer> entry : materialsQuantity.entrySet()) {
                String materialName = entry.getKey();
                Integer quantity = entry.getValue();
                
                TWorkOrderMaterial material = new TWorkOrderMaterial();
                material.setWorkOrderId(workOrderId);
                material.setMaterialName(materialName);
                material.setQuantityUsed(new BigDecimal(quantity));
                material.setRecordedAt(LocalDateTime.now());
                
                workOrderMaterialRepository.save(material);
            }
            logger.info("工单耗材信息保存成功: workOrderId={}", workOrderId);
        } catch (Exception e) {
            logger.error("保存工单耗材信息失败: workOrderId={}, error={}", workOrderId, e.getMessage());
            throw e; // 抛出异常以便事务回滚
        }
    }

    /**
     * 获取工单列表
     * @param date 日期过滤
     * @param status 状态过滤
     * @param userId 用户ID过滤
     * @param orderNo 工单号过滤，现在已不使用，传入null
     * @param role 用户角色，现在已不使用，传入null
     * @param limit 限制返回记录数
     * @param page 页码
     * @param pageSize 每页数量
     * @param type 工单类型，'my'表示我的工单，'pending'表示待接单工单
     * @param heatUnitId 用户项目权限，逗号分隔的项目ID字符串
     * @return 工单列表和分页信息
     */
    @Override
    public Map<String, Object> getWorkOrderList(Date date, String status, Long userId, String orderNo, String role, Integer limit, Integer page, Integer pageSize, String type, String heatUnitId) {
        try {
            // 设置默认的空字符串，避免SQL查询出错
            String safeOrderNo = orderNo == null ? "" : orderNo;
            
            // 如果userId为null，则返回空结果
            if (userId == null) {
                Map<String, Object> emptyResult = new HashMap<>();
                emptyResult.put("list", new ArrayList<>());
                emptyResult.put("total", 0);
                emptyResult.put("page", page != null ? page : 1);
                emptyResult.put("pageSize", pageSize != null ? pageSize : 10);
                emptyResult.put("totalPages", 0);
                return emptyResult;
            }

            List<Map<String, Object>> results = new ArrayList<>();
            long total = 0;
            
            // 参数处理
            page = (page == null || page < 1) ? 1 : page;
            pageSize = (pageSize == null || pageSize < 1) ? 10 : pageSize;
            
            // 计算偏移量
            int offset = (page - 1) * pageSize;
            
            // 处理工单类型
            if ("my".equals(type)) {
                // 我的工单：查询用户负责的工单（repair_user_id = userId）
                if (limit != null) {
                    results = workOrderRepository.findMyWorkOrdersWithLimit(date, status, userId, safeOrderNo, limit, heatUnitId);
                    total = results.size();
                } else {
                    results = workOrderRepository.findMyWorkOrdersWithPaging(date, status, userId, offset, pageSize, heatUnitId);
                    total = workOrderRepository.countMyWorkOrders(date, status, userId, safeOrderNo, heatUnitId);
                }
            } else if ("pending".equals(type)) {
                // 待接单工单：查询状态为"待接单"的工单
                if (limit != null) {
                    results = workOrderRepository.findPendingWorkOrdersWithLimit(date, userId, safeOrderNo, limit, heatUnitId);
                    total = results.size();
                } else {
                    results = workOrderRepository.findPendingWorkOrdersWithPaging(date, userId, safeOrderNo, offset, pageSize, heatUnitId);
                    total = workOrderRepository.countPendingWorkOrders(date, userId, safeOrderNo, heatUnitId, false);
                }
            } else {
                // 默认查询逻辑
                if (limit != null) {
                    // 确保参数顺序与方法签名匹配
                    results = workOrderRepository.findWorkOrderListWithLimit(status, limit, heatUnitId);
                    total = results.size(); // 当使用limit时，总数就是结果集大小
                } else {
                    // 使用新的查询方法，返回transfer_user_id或repair_user_id匹配用户ID的工单
                    results = workOrderRepository.findWorkOrderListWithTransferInfo(date, status, userId, safeOrderNo, offset, pageSize, heatUnitId);
                    total = workOrderRepository.countWorkOrderListWithTransferInfo(date, status, userId, safeOrderNo, heatUnitId);
                }
            }

            // 计算总页数
            int totalPages = (int) Math.ceil((double) total / pageSize);
            
            // 格式化返回数据
            List<WorkOrderBaseInfoResponse> workOrders = new ArrayList<>();
            for (Map<String, Object> result : results) {
                WorkOrderBaseInfoResponse response = new WorkOrderBaseInfoResponse();
                response.setOrderId((Long) result.get("orderId"));
                response.setOrderNo((String) result.get("orderNo"));
                response.setHeatUnitName((String) result.get("heatUnitName"));
                response.setFaultType((String) result.get("faultType"));
                response.setFaultLevel((String) result.get("faultLevel"));
                response.setOrderStatus((String) result.get("orderStatus"));
                response.setCreatedTime((String) result.get("createdTime"));
                
                // 如果结果中包含转派相关字段，则设置到返回对象中
                if (result.containsKey("repairUserId")) {
                    response.setRepairUserId((Long) result.get("repairUserId"));
                }
                if (result.containsKey("transferUserId")) {
                    response.setTransferUserId((Integer) result.get("transferUserId"));
                }
                
                workOrders.add(response);
            }
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("list", workOrders);
            result.put("total", total);
            result.put("page", page);
            result.put("pageSize", pageSize);
            result.put("totalPages", totalPages);
            
            logger.info("查询工单列表完成: 当前页={}, 每页数量={}, 总记录数={}, 总页数={}", 
                       page, pageSize, total, totalPages);
            return result;
        } catch (Exception e) {
            logger.error("查询工单列表失败: error={}", e.getMessage());
            throw new RuntimeException("获取工单列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取工单详情
     */
    @Override
    public WorkOrderDetailResponse getWorkOrderDetail(long orderId) {
        try {
            Optional<Map<String, Object>> result = workOrderRepository.findWorkOrderDetail(orderId); 
            if (result.isEmpty()) {   
                logger.warn("工单详情查询失败：工单不存在 orderId={}", orderId);
                return null;
            } 
            Map<String, Object> workOrderMap = result.get();

            List<TFaultAttachment> faultAttachmentList = workOrderRepository.findFaultAttachments(orderId); 
             // 遍历faultAttachmentList，将每个TFaultAttachment转换为Attachments
             List<WorkOrderDetailResponse.AttachmentDto> faultAttachments = faultAttachmentList.stream().map(attachment -> {
                 WorkOrderDetailResponse.AttachmentDto attachments = new WorkOrderDetailResponse.AttachmentDto(attachment.getFileType(), attachment.getFilePath()); 
                return attachments;
             }).collect(Collectors.toList());

            List<TWorkOrderAttachment> workOrderAttachmentList = workOrderRepository.findWorkOrderAttachments(orderId);
             // 遍历workOrderAttachmentList，将每个TWorkOrderAttachment转换为Attachments
             List<WorkOrderDetailResponse.AttachmentDto> workOrderAttachments = workOrderAttachmentList.stream().map(attachment -> {
                WorkOrderDetailResponse.AttachmentDto attachments = new WorkOrderDetailResponse.AttachmentDto(attachment.getFileType(), attachment.getFilePath());
                return attachments;
             }).collect(Collectors.toList());

             // 遍历operationLogsList，将每个Map转换为OperationLogs
             List<Map<String, Object>> operationLogsList = workOrderRepository.findOperationLogs(orderId);
             List<WorkOrderDetailResponse.OperationLogDto> operationLogs = operationLogsList.stream().map(log -> {
                String createdAtStr = "";
                Object createdAt = log.get("createdAt");
                
                // 处理不同类型的createdAt
                if (createdAt instanceof LocalDateTime) {
                    // 如果是LocalDateTime类型
                    createdAtStr = ((LocalDateTime) createdAt).format(DATE_TIME_FORMATTER);
                } else if (createdAt instanceof String) {
                    // 如果是String类型，直接使用
                    createdAtStr = (String) createdAt;
                } else if (createdAt instanceof java.sql.Timestamp) {
                    // 如果是Timestamp类型，转换为LocalDateTime
                    createdAtStr = ((java.sql.Timestamp) createdAt).toLocalDateTime().format(DATE_TIME_FORMATTER);
                } else if (createdAt != null) {
                    // 如果是其他类型，转换为字符串
                    createdAtStr = createdAt.toString();
                }
                
                WorkOrderDetailResponse.OperationLogDto operationLog = new WorkOrderDetailResponse.OperationLogDto(
                    (String) log.get("operationType"),
                    (String) log.get("operationDesc"),
                    (String) log.get("operatorName"),
                    createdAtStr
                );
                return operationLog;
             }).collect(Collectors.toList());
 
            WorkOrderDetailResponse workOrderDetail = new WorkOrderDetailResponse(
                (String) workOrderMap.get("orderNo"),
                (Long) workOrderMap.get("faultId"),
                (String) workOrderMap.get("heatUnitName"),
                (Long) workOrderMap.get("repairUserId"),
                (String) workOrderMap.get("repairUserName"),
                (Integer) workOrderMap.get("transferUserId"),
                (String) workOrderMap.get("transferUserName"),
                (String) workOrderMap.get("transferReason"),
                (String) workOrderMap.get("transferTime"),
                (String) workOrderMap.get("repairContent"),
                (String) workOrderMap.get("repairResult"),
                (String) workOrderMap.get("address"),
                (String) workOrderMap.get("orderStatus"),
                (String) workOrderMap.get("faultType"),
                (String) workOrderMap.get("faultLevel"),
                (String) workOrderMap.get("faultDesc"),
                (String) workOrderMap.get("repairTime"),
                (String) workOrderMap.get("occurTime"),
                processRepairMaterialsQuantity((String) workOrderMap.get("repairMaterialsQuantity")),
                (String) workOrderMap.get("createdTime"),
                (String) workOrderMap.get("updatedTime"), 
                faultAttachments,
                workOrderAttachments,
                operationLogs
            );

            return workOrderDetail;
        } catch (Exception e) {
            logger.error("查询工单详情失败: orderId={}, error={}", orderId, e.getMessage());
            return null;
        }
    }
     
    /**
     * 创建工单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createWorkOrder(CreateOrderRequest request) {
        logger.info("开始创建工单: orderNo={}", request.getOrderNo());
        // 检查工单是否存在
        if (workOrderRepository.findByOrderNo(request.getOrderNo()).isPresent()) {
            throw new RuntimeException("工单已存在: " + request.getOrderNo());
        }   
        try {
            // 创建工单对象
            TWorkOrder workOrder = new TWorkOrder();
            workOrder.setOrderNo(request.getOrderNo());
            workOrder.setFaultId(request.getFaultId());
            workOrder.setRepairUserId(request.getRepairUserId());
            workOrder.setRepairContent(request.getRepairContent());
            workOrder.setOrderStatus(request.getOrderStatus());
            workOrder.setCreatedAt(LocalDateTime.now());
            workOrder.setUpdatedAt(LocalDateTime.now());
            
            // 保存工单
            workOrderRepository.save(workOrder);
            
            // 记录操作日志
            TOperationLog log = new TOperationLog();
            log.setWorkOrderId(workOrder.getId()); 
            log.setOperationType("工单创建");
            log.setOperationDesc("创建新工单");
            log.setOperatorId(workOrder.getRepairUserId()); // 假设创建者是维修人员
            log.setCreatedAt(LocalDateTime.now());
            operationLogRepository.save(log);
            
            logger.info("工单创建成功: id={}, orderNo={}", workOrder.getId(), workOrder.getOrderNo());
        } catch (Exception e) {
            logger.error("工单创建失败: orderNo={}, error={}", request.getOrderNo(), e.getMessage());
            throw new RuntimeException("工单创建失败: " + e.getMessage());
        }
    }

    

    @Override
    public void updateWorkOrderStatus(AcceptOrderRequest request) {
        logger.info("开始处理工单状态修改请求: orderId={}", request.getOrderId());  
        try {
            // 获取工单
            Optional<TWorkOrder> result = workOrderRepository.findById(request.getOrderId());
            if (result.isEmpty()) {
                logger.warn("工单状态修改失败：工单不存在 orderId={}", request.getOrderId());
                return;
            }

            // 更新工单状态
            TWorkOrder workOrder = result.get();
            workOrder.setOrderStatus(request.getOrderStatus());
            workOrder.setRepairUserId(request.getRepairUserId());
            workOrder.setUpdatedAt(LocalDateTime.now());
            workOrderRepository.save(workOrder);
            
            // 记录操作日志
            TOperationLog log = new TOperationLog();
            log.setWorkOrderId(workOrder.getId());
            log.setOperationType("工单状态修改");
            log.setOperationDesc("工单状态:" + request.getOrderStatus());
            log.setOperatorId(workOrder.getRepairUserId());
            log.setCreatedAt(LocalDateTime.now());
            operationLogRepository.save(log);

            logger.info("工单状态修改成功: orderId={}", request.getOrderId());  
        } catch (Exception e) {
            logger.error("工单状态修改失败: orderId={}, error={}", request.getOrderId(), e.getMessage());
        }
    }

    /**
     * 处理维修耗材数量JSON字符串，转换为Map
     */
    private Map<String, Integer> processRepairMaterialsQuantity(String repairMaterialsQuantityJson) {
        if (repairMaterialsQuantityJson == null || repairMaterialsQuantityJson.isEmpty()) {
            return null;
        }
        
        try {
            com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
            return objectMapper.readValue(repairMaterialsQuantityJson, 
                   new com.fasterxml.jackson.core.type.TypeReference<Map<String, Integer>>() {});
        } catch (Exception e) {
            logger.error("维修耗材数量JSON解析失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 获取工单消息
     * 从工单信息表中获取状态为 '待接单' 的记录列表
     * @param userId 用户ID
     * @param role 用户角色，将来可用于基于角色的消息过滤
     * @param heatUnitId 用户项目权限，逗号分隔的项目ID字符串
     * @return 待接单的工单消息列表
     */
    @Override
    public List<WorkOrderMessageResponse> getWorkOrderMessages(Long userId, String role, String heatUnitId) {
        logger.info("开始获取工单消息（待接单状态的工单）: userId={}, role={}, heatUnitId={}", userId, role, heatUnitId);
        try {
            // 如果userId为null，则返回空列表
            if (userId == null) {
                logger.info("未提供用户ID，返回空列表");
                return new ArrayList<>();
            }
            
            // 查询待接单工单，基于用户的项目权限(heatUnitId)过滤
            List<Map<String, Object>> pendingOrders;
            
            pendingOrders = workOrderRepository.findWorkOrdersByStatus("待接单", heatUnitId);
            
            // 将查询结果转换为DTO
            List<WorkOrderMessageResponse> messages = new ArrayList<>();
            for (Map<String, Object> order : pendingOrders) {
                WorkOrderMessageResponse message = new WorkOrderMessageResponse(
                    (Long) order.get("id"),
                    (String) order.get("faultSource"),
                    (String) order.get("faultDesc"),
                    (String) order.get("orderNo"),
                     (String) order.get("heatUnitName"),
                    // 转换时间格式，处理java.sql.Timestamp类型
                    order.get("createdTime") instanceof String 
                        ? LocalDateTime.parse((String) order.get("createdTime"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"))
                        : (order.get("createdTime") instanceof java.sql.Timestamp)
                            ? ((java.sql.Timestamp) order.get("createdTime")).toLocalDateTime()
                            : (LocalDateTime) order.get("createdTime"),
                    order.get("updatedTime") instanceof String
                        ? LocalDateTime.parse((String) order.get("updatedTime"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"))
                        : (order.get("updatedTime") instanceof java.sql.Timestamp)
                            ? ((java.sql.Timestamp) order.get("updatedTime")).toLocalDateTime()
                            : (LocalDateTime) order.get("updatedTime")
                );
                messages.add(message);
            }
            
            logger.info("获取工单消息成功，共找到{}条待接单工单", messages.size());
            return messages;
        } catch (Exception e) {
            logger.error("获取工单消息失败: {}", e.getMessage());
            throw new RuntimeException("获取工单消息失败: " + e.getMessage());
        }
    }

    /**
     * 转派工单
     * @param request 转派工单请求
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transferOrder(TransferOrderRequest request) {
        logger.info("开始处理工单转派请求: orderId={}", request.getOrderId());
        try {
            // 获取工单
            Optional<TWorkOrder> result = workOrderRepository.findById(request.getOrderId());
            if (result.isEmpty()) {
                logger.warn("工单转派失败：工单不存在 orderId={}", request.getOrderId());
                throw new RuntimeException("工单不存在");
            }
            
            // 更新工单
            TWorkOrder workOrder = result.get();
            
            // 更新转派相关字段
            workOrder.setRepairUserId(request.getRepairUserId());
            workOrder.setTransferUserId(request.getTransferUserId().intValue());
            workOrder.setTransferReason(request.getTransferReason());   
            workOrder.setTransferTime(LocalDateTime.now());
            workOrder.setUpdatedAt(LocalDateTime.now());
            
            // 保存工单
            workOrderRepository.save(workOrder);
            
            // 记录操作日志
            TOperationLog log = new TOperationLog();
            log.setWorkOrderId(workOrder.getId());
            log.setOperationType("工单转派");
            log.setOperationDesc("工单已转派给ID为" + request.getRepairUserId() + "的维修人员");
            log.setOperatorId(request.getTransferUserId());
            log.setCreatedAt(LocalDateTime.now());
            operationLogRepository.save(log);
            
            logger.info("工单转派成功: orderId={}, 转派人ID={}, 目标维修人员ID={}", 
                request.getOrderId(), request.getTransferUserId(), request.getRepairUserId());
        } catch (Exception e) {
            logger.error("工单转派失败: orderId={}, error={}", request.getOrderId(), e.getMessage());
            throw e; // 抛出异常以便事务回滚
        }
    }

    /**
     * 获取工单统计数据
     * @param userId 用户ID
     * @param heatUnitId 用户项目权限，逗号分隔的项目ID字符串
     * @return 工单统计数据，包括我的工单数、待处理工单数、待接单工单数
     */
    @Override
    public Map<String, Object> getWorkOrderStats(Long userId, String heatUnitId) {
        logger.info("开始获取工单统计数据: userId={}, heatUnitId={}", userId, heatUnitId);
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 如果userId为null，则返回默认值
            if (userId == null) {
                logger.info("未提供用户ID，返回默认统计数据");
                stats.put("myWorkOrders", 0);
                stats.put("processingOrders", 0);
                stats.put("pendingOrders", 0);
                return stats;
            }
            
            // 获取用户角色
            String userRole = workOrderRepository.getUserRole(userId);
            logger.info("用户角色: {}", userRole);
            
            // 根据用户角色区分不同的统计逻辑
            if ("admin".equals(userRole) || "manager".equals(userRole)) {
                // 管理员或主管角色
                logger.info("管理员或主管角色统计逻辑");
                
                // 1. 我的工单数 - 查询用户负责的工单（repair_user_id = userId）的数量
                long myWorkOrders = workOrderRepository.countMyWorkOrders(null, null, userId, "", heatUnitId);
                
                // 2. 待处理工单数 - 用户权限中的所有小区范围内所有已接单但未完成的工单
                long processingOrders = workOrderRepository.countAllProcessingWorkOrders(heatUnitId);
                
                // 3. 待接单工单数 - 用户权限中所属小区中未被接单的工单
                long pendingOrders = workOrderRepository.countPendingWorkOrders(null, null, "", heatUnitId, true);
                
                // 设置返回数据
                stats.put("myWorkOrders", myWorkOrders);
                stats.put("processingOrders", processingOrders);
                stats.put("pendingOrders", pendingOrders);
            } else {
                // 普通用户角色
                logger.info("普通用户角色统计逻辑");
                
                // 1. 我的工单数 - 查询用户负责的工单（repair_user_id = userId）的数量
                long myWorkOrders = workOrderRepository.countMyWorkOrders(null, null, userId, "", heatUnitId);
                
                // 2. 待处理工单数 - 查询用户负责且状态为"处理中"的工单数量
                long processingOrders = workOrderRepository.countMyWorkOrders(null, "处理中", userId, "", heatUnitId);
                
                // 3. 待接单工单数 - 查询状态为"待接单"的工单数量
                long pendingOrders = workOrderRepository.countPendingWorkOrders(null, userId, "", heatUnitId, false);
                
                // 设置返回数据
                stats.put("myWorkOrders", myWorkOrders);
                stats.put("processingOrders", processingOrders);
                stats.put("pendingOrders", pendingOrders);
            }
            
            logger.info("工单统计数据获取成功: 我的工单数={}, 待处理工单数={}, 待接单工单数={}", 
                       stats.get("myWorkOrders"), stats.get("processingOrders"), stats.get("pendingOrders"));
            return stats;
        } catch (Exception e) {
            logger.error("获取工单统计数据失败: {}", e.getMessage());
            // 出错时返回默认值
            stats.put("myWorkOrders", 0);
            stats.put("processingOrders", 0);
            stats.put("pendingOrders", 0);
            return stats;
        }
    }

} 

