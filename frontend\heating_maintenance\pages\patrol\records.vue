<template>
  <view class="patrol-records-container">
    <!-- 错误提示框 -->
    <view class="error-box" v-if="showError">
      <view class="error-content">
        <text class="error-icon iconfont icon-warn"></text>
        <text class="error-text">{{ errorMessage }}</text>
        <button class="retry-btn" @click="retryLoading">重试</button>
      </view>
    </view>

    <!-- 顶部区域 -->
    <view class="page-header">
      <!-- 搜索栏 -->
      <view class="search-bar">
      <!--  <view class="search-input-wrapper">
          <input
            type="text"
            placeholder="搜索巡检记录"
            v-model="searchKeyword"
            confirm-type="search"
            @confirm="handleSearch"
          />
          <text
            class="iconfont icon-clear"
            v-if="searchKeyword"
            @click="clearSearch"
          ></text>
        </view> -->

        <!-- 筛选选项区域 -->
        <view class="filter-options">
          <view class="filter-option" @click="showTimeFilter">
            <text class="option-text">时间</text>
            <text class="option-value">{{ timeFilterText }}</text>
          </view>

          <view class="filter-option" @click="showStatusFilter">
            <text class="option-text">状态</text>
            <text class="option-value">{{ statusFilterText }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 巡检记录列表 -->
    <view class="record-list" v-if="patrolRecords.length > 0">
      <view
        class="record-item"
        v-for="(record, index) in patrolRecords"
        :key="index"
        @click="viewRecordDetail(record.id)"
      >
        <view class="record-header">
          <text class="record-title">{{ record.planName }}</text>
          <view class="record-status" :class="record.status">
            {{ getStatusText(record.status) }}
          </view>
        </view>
        <view class="record-info">
          <view class="info-row date-location">
            <view class="info-item">
				<text class="item-label">执行日期:</text>
              <text class="item-value">{{ formatDateOnly(record.executionDate) }}</text>
            </view>
            <view class="info-item">
				<text class="item-label">巡检类型:</text>
              <text class="item-value">{{ record.patrolType || "未指定" }}</text>
            </view>
          </view>
          <view class="info-row time-row">
			   <view class="info-item">
			      <text class="item-label">执行时间:</text>
			       <text class="item-value">{{ formatTimeOnly(record.startTime) }} ~{{ formatTimeOnly(record.endTime) }}</text>
               </view>
		  </view>

          <view class="info-row executor-abnormal">
            <view class="info-item">
			 <text class="item-label">执行人:</text>
              <text class="item-value">{{ record.executorName || "未分配" }}</text>
            </view>

          <!--  <view class="info-item abnormal-item">
              <text class="item-icon iconfont icon-warning"></text>
              <text class="item-value" :class="{ abnormal: record.abnormalCount > 0 }">
                异常: {{ record.abnormalCount || 0 }}
              </text>
            </view> -->
          </view>
        </view>

        <view class="record-footer">
          <text class="record-time">{{ formatDateTime(record.createTime) }}</text>
		  
		   <PermissionCheck permission="patrol:plans:execute">
		      <view
		        v-if="record.status=='pending' || record.status=='overdue'  "
		        class="view-detail start"
		        @click.stop="startPatrol(record.id,record.patrolPlanId)">
		        <text class="btn-text">开始巡检</text>
		      </view>
<!-- 		      <view
		        v-if="record.status === 'progressing'"
		        class="action-button continue"
		        @click.stop="continuePatrolPlan(record.patrolPlanId)"
		      >
		        <text class="iconfont icon-continue"></text>
		        <text>继续巡检</text>
		      </view> -->
		    </PermissionCheck>
			<PermissionCheck permission="patrol:record:detail">
			  <view class="view-detail view" @click.stop="viewRecordDetail(record.id)">
				<text class="btn-text">查看详情</text>
			  </view>
		   </PermissionCheck>
        </view>
      </view>
    </view>
	    <!-- 加载中 -->
<!--    <view class="loading-container" v-if="isLoading">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view> -->

    <!-- 加载失败 -->
    <view class="error-container" v-if="loadError && !isLoading">
      <text class="error-text">{{ errorMsg || "加载失败，请重试" }}</text>
      <button class="retry-btn" @click="loadPatrolRecords">重新加载</button>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-if="!isLoading && !loadError && patrolRecords.length === 0">
      <image class="empty-image" src="/static/images/empty.png" mode="aspectFit"></image>
      <text class="empty-text">{{ emptyText }}</text>
    </view>

    <!-- 时间筛选弹窗 -->
    <uni-popup ref="timeFilterPopup" type="bottom">
      <view class="filter-popup">
        <view class="popup-header">
          <text class="popup-title">选择时间范围</text>
          <text class="popup-close" @click="$refs.timeFilterPopup.close()">关闭</text>
        </view>
        <view class="popup-content">
          <view
            class="filter-option"
            v-for="(option, index) in timeOptions"
            :key="index"
            :class="{ active: timeFilter === option.value }"
            @click="selectTimeFilter(option.value)"
          >
            <text class="option-text">{{ option.label }}</text>
            <text class="iconfont icon-check" v-if="timeFilter === option.value"></text>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 状态筛选弹窗 -->
    <uni-popup ref="statusFilterPopup" type="bottom">
      <view class="filter-popup">
        <view class="popup-header">
          <text class="popup-title">选择巡检状态</text>
          <text class="popup-close" @click="$refs.statusFilterPopup.close()">关闭</text>
        </view>
        <view class="popup-content">
          <view
            class="filter-option"
            v-for="(option, index) in statusOptions"
            :key="index"
            :class="{ active: statusFilter === option.value }"
            @click="selectStatusFilter(option.value)"
          >
            <text class="option-text">{{ option.label }}</text>
            <text class="iconfont icon-check" v-if="statusFilter === option.value"></text>
          </view>
        </view>
      </view>
    </uni-popup>
<!--    <PermissionCheck permission="patrol:record:add">
      <view class="floating-btn" @click="goToCreatePatrol">
        <text class="plus-icon">+</text>
      </view>
    </PermissionCheck> -->
  </view>
</template>

<script>
import { patrolApi } from "@/utils/api.js";
import permissionMixin from "@/utils/permission-mixin.js"; // 导入权限混入
import PermissionCheck from "@/components/PermissionCheck.vue"; // 导入权限检查组件

export default {
  components: {
    PermissionCheck, // 本地注册组件
  },
  mixins: [permissionMixin], // 使用权限混入
  data() {
    return {
      searchKeyword: "",
      patrolRecords: [],
      currentPage: 1,
      pageSize: 5,
      hasMore: true,
      isLoading: false,
      loadError: false,
      _needRefresh: false, // 标记是否需要刷新数据
      _lastLoadTime: 0, // 记录上次加载时间
      errorMsg: "", // 错误信息

      // 筛选相关
      timeFilter: "all",
      statusFilter: "all",

      // 筛选选项
      timeOptions: [
        { label: "全部时间", value: "all" },
        { label: "今天", value: "today" },
        { label: "本周", value: "this_week" },
        { label: "本月", value: "this_month" },
        { label: "上个月", value: "last_month" },
      ],
      statusOptions: [
        { label: "全部状态", value: "all" },
        { label: "待执行", value: "pending" },
        { label: "执行中", value: "processing" },
        { label: "已完成", value: "completed" },
		{ label: "已超时", value: "overdue" },
      ],
      showError: false,
      errorMessage: "",
    };
  },
  computed: {
    timeFilterText() {
      const option = this.timeOptions.find((item) => item.value === this.timeFilter);
      return option ? option.label : "全部时间";
    },
    statusFilterText() {
      const option = this.statusOptions.find((item) => item.value === this.statusFilter);
      return option ? option.label : "全部状态";
    },
    emptyText() {
      // 根据筛选条件提供更具体的空状态提示
      if (this.timeFilter !== 'all' || this.statusFilter !== 'all' || this.searchKeyword) {
        return "没有符合筛选条件的巡检记录";
      }
      return "暂无巡检记录";
    }
  },
  onLoad() {
    this.loadPatrolRecords();
  },
  // 添加页面级下拉刷新
  onPullDownRefresh() {
    this.currentPage = 1;
    this.patrolRecords = [];
    this.hasMore = true;
    this.loadPatrolRecords().then(() => {
      uni.stopPullDownRefresh();
    });
  },
  // 添加上拉加载更多
  onReachBottom() {
    if (this.hasMore && !this.isLoading) {
      this.currentPage++;
      this.loadPatrolRecords(true);
    }
  },
  onShow(){
    // 判断数据是否需要刷新，减少不必要的重复加载
    if (this.patrolRecords.length === 0 || this._needRefresh) {
      this._needRefresh = false;
      this.loadPatrolRecords();
    }
  },
  methods: {
    // 加载巡检记录列表
    async loadPatrolRecords(isLoadMore = false) {
      if (this.isLoading) return;
      
      // 避免短时间内多次调用（防抖）
      const now = Date.now();
      if (!isLoadMore && now - this._lastLoadTime < 300) {
        return;
      }
      this._lastLoadTime = now;
      
      this.isLoading = true;

      if (!isLoadMore) {
        this.loadError = false;
      }
      // 显示加载提示
	  uni.showLoading({
	    title: "加载中...",
	  });
      try {
        // 准备请求参数
        const params = {
          page: this.currentPage,
          pageSize: this.pageSize,
          keyword: this.searchKeyword,
          status: this.statusFilter !== "all" ? this.statusFilter : undefined,
          timeRange: this.timeFilter !== "all" ? this.timeFilter : undefined,
          // 只请求需要在界面上显示的字段
          fields: "id,planName,executorName,startTime,endTime,status,patrolType,executionDate,createTime,patrolPlanId"
        };

        console.log("请求参数:", params);

        // 根据时间筛选添加日期参数
        if (this.timeFilter !== "all") {
          const dateRange = this.getDateRangeByTimeFilter(this.timeFilter);
          if (dateRange) {
            params.startDate = dateRange.startDate;
            params.endDate = dateRange.endDate;
          }
        }
		// 添加用户的项目权限IDs (必须参数)
		const heatUnitId = uni.getStorageSync('heatUnitId');
		
		// 设置热用户ID参数
		params.heatUnitId = heatUnitId;
        // 添加请求超时处理
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => {
            reject(new Error('请求超时'));
          }, 8000); // 8秒超时
        });

        // 调用API获取巡检记录列表
        const res = await Promise.race([
          patrolApi.getPatrolRecords(params),
          timeoutPromise
        ]);
        console.log("API响应:", res);

        if (res.code === 200) {
          // 兼容不同的数据结构
          let recordsList = [];
          let totalPages = 1;
          
          if (Array.isArray(res.data)) {
            // 如果直接返回数组
            recordsList = res.data;
            totalPages = Math.ceil(recordsList.length / this.pageSize);
          } else if (res.data && res.data.list) {
            // 如果返回对象中包含list字段
            recordsList = res.data.list;
            totalPages = res.data.totalPages || Math.ceil(res.data.total / this.pageSize) || 1;
          } else if (res.data) {
            // 其他情况，尝试使用res.data
            recordsList = Array.isArray(res.data) ? res.data : [res.data];
            totalPages = 1;
          }
          
          // 处理记录数据 - 只处理需要显示的字段
          const records = recordsList.map((record) => ({
            id: record.id,
            planName: record.planName,
            patrolPlanId: record.patrolPlanId,
            executorName: record.executorName || "未分配",
            startTime: record.startTime,
            endTime: record.endTime,
            status: record.status,
            patrolType: record.patrolType,
            executionDate: record.executionDate,
            createTime: record.createTime
            // 移除abnormalCount字段，页面上没有使用
          }));

          if (isLoadMore) {
            // 加载更多模式：追加数据
            this.patrolRecords = [...this.patrolRecords, ...records];
          } else {
            // 刷新模式：替换数据
            this.patrolRecords = records;
          }

          // 判断是否还有更多数据
          this.hasMore = this.currentPage < totalPages;
		  uni.hideLoading();
        } else {
          this.loadError = true;
		  uni.hideLoading();
          uni.showToast({
            title: res.message || "获取巡检记录失败",
            icon: "none",
            duration: 2000
          });
        }
      } catch (err) {
		  uni.hideLoading();
        console.error("获取巡检记录失败:", err);
        this.loadError = true;
        
        // 针对不同错误类型提供更友好的提示
        if (err.message === '请求超时') {
          this.errorMsg = "请求超时，请检查网络连接";
        } else if (err.response && err.response.status === 404) {
          this.errorMsg = "服务不可用，请稍后再试";
        } else {
          this.errorMsg = "网络异常，请稍后重试";
        }
        
        uni.showToast({
          title: this.errorMsg,
          icon: "none",
          duration: 2000
        });
      } finally {
        this.isLoading = false;
      }
    },

    // 根据时间筛选器获取日期范围
    getDateRangeByTimeFilter(timeFilter) {
      const now = new Date();
      let startDate = null;
      let endDate = null;

      switch (timeFilter) {
        case "today":
          startDate = this.formatDate(now);
          endDate = this.formatDate(now);
          break;
        case "this_week":
          // 获取本周一
          const dayOfWeek = now.getDay() || 7; // 将周日的0改为7
          const mondayDate = new Date(now);
          mondayDate.setDate(now.getDate() - dayOfWeek + 1);
          startDate = this.formatDate(mondayDate);
          endDate = this.formatDate(now);
          break;
        case "this_month":
          // 获取本月第一天
          const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
          startDate = this.formatDate(firstDayOfMonth);
          endDate = this.formatDate(now);
          break;
        case "last_month":
          // 获取上月第一天
          const firstDayOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
          // 获取上月最后一天
          const lastDayOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);
          startDate = this.formatDate(firstDayOfLastMonth);
          endDate = this.formatDate(lastDayOfLastMonth);
          break;
        default:
          return null;
      }

      return { startDate, endDate };
    },

    // 格式化日期为yyyy-MM-dd格式
    formatDate(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      return `${year}-${month}-${day}`;
    },

    // 搜索处理
    handleSearch() {
      this.loadPatrolRecords();
    },

    // 清除搜索关键词
    clearSearch() {
      this.searchKeyword = "";
      this.loadPatrolRecords();
    },

    // 加载更多记录
    loadMoreRecords() {
      if (!this.hasMore || this.isLoading) return;
      this.currentPage++;
      this.loadPatrolRecords(true);
    },

    // 显示时间筛选弹窗
    showTimeFilter() {
      this.$refs.timeFilterPopup.open();
    },

    // 显示状态筛选弹窗
    showStatusFilter() {
      this.$refs.statusFilterPopup.open();
    },

    // 选择时间筛选
    selectTimeFilter(value) {
      // 如果选择的是当前值，不执行刷新
      if (this.timeFilter === value) {
        this.$refs.timeFilterPopup.close();
        return;
      }
      
      this.timeFilter = value;
      this.currentPage = 1;
      this.patrolRecords = [];
      this.hasMore = true;
      this.$refs.timeFilterPopup.close();
      
      // 立即显示加载提示
      uni.showLoading({
        title: "加载中..."
      });
      
      // 延迟一下确保弹窗关闭后再加载数据
      setTimeout(() => {
        this.loadPatrolRecords();
      }, 100);
    },

    // 选择状态筛选
    selectStatusFilter(value) {
      // 如果选择的是当前值，不执行刷新
      if (this.statusFilter === value) {
        this.$refs.statusFilterPopup.close();
        return;
      }
      
      this.statusFilter = value;
      this.currentPage = 1;
      this.patrolRecords = [];
      this.hasMore = true;
      this.$refs.statusFilterPopup.close();
      
      // 立即显示加载提示
      uni.showLoading({
        title: "加载中..."
      });
      
      // 延迟一下确保弹窗关闭后再加载数据
      setTimeout(() => {
        this.loadPatrolRecords();
      }, 100);
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        pending: "待执行",
        processing: "执行中",
        completed: "已完成",
		overdue: "已超时",
      };
      return statusMap[status] || "未知";
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return "";

      // 处理数组格式的日期时间 [year, month, day]或[year, month, day, hour, minute, second]
      if (Array.isArray(dateTime)) {
        if (dateTime.length >= 3) {
          const year = dateTime[0];
          const month = String(dateTime[1]).padStart(2, "0");
          const day = String(dateTime[2]).padStart(2, "0");

          // 如果包含时间部分
          if (dateTime.length >= 5) {
            const hour = String(dateTime[3]).padStart(2, "0");
            const minute = String(dateTime[4]).padStart(2, "0");
            let second = "00";
            if (dateTime.length > 5) second = String(dateTime[5]).padStart(2, "0");
            return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
          }

          return `${year}-${month}-${day}`;
        }
        return dateTime.join("-"); // 如果无法解析，返回用-连接的数组
      }

      // 确保dateTime是字符串类型
      const dateTimeStr = String(dateTime);

      // 处理ISO格式日期时间
      if (dateTimeStr.includes("T")) {
        const date = new Date(dateTimeStr);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        const seconds = String(date.getSeconds()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      }

      // 如果是数字类型（时间戳），转换为日期字符串
      if (!isNaN(dateTime) && typeof dateTime === "number") {
        const date = new Date(dateTime);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        const seconds = String(date.getSeconds()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      }

      // 对于其他格式的字符串，尝试用Date解析
      try {
        const date = new Date(dateTimeStr);
        if (!isNaN(date.getTime())) {
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, "0");
          const day = String(date.getDate()).padStart(2, "0");
          const hours = String(date.getHours()).padStart(2, "0");
          const minutes = String(date.getMinutes()).padStart(2, "0");
          const seconds = String(date.getSeconds()).padStart(2, "0");

          // 确认是否有时间部分
          if (hours !== "00" || minutes !== "00" || seconds !== "00") {
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
          } else {
            return `${year}-${month}-${day}`;
          }
        }

        // 如果Date解析失败，尝试按原样返回或处理特殊格式
        return dateTimeStr;
      } catch (e) {
        console.error("格式化日期时间出错:", e, dateTimeStr);
        return dateTimeStr; // 如果无法格式化，返回原始值
      }
    },
    // 开始巡检
    startPatrol(id,planId) {
        // 标记需要在返回页面时刷新数据
        this._needRefresh = true;
        uni.navigateTo({
        url: `/pages/patrol/execute?id=${id}&&planId=${planId}`,
      });
    },
    // 格式化日期（不含时间）
    formatDateOnly(date) {
      if (!date) return "";

      // 处理数组格式的日期 [year, month, day]
      if (Array.isArray(date)) {
        if (date.length >= 3) {
          const year = date[0];
          const month = String(date[1]).padStart(2, "0");
          const day = String(date[2]).padStart(2, "0");
          return `${year}-${month}-${day}`;
        }
        return date.join("-"); // 如果无法解析，返回用-连接的数组
      }

      // 使用formatDateTime函数，但是只返回日期部分
      const fullDateTime = this.formatDateTime(date);
      return fullDateTime.split(" ")[0];
    },

    // 格式化时间，只提取时间部分 (HH:MM:SS)
    formatTimeOnly(dateTime) {
      if (!dateTime) return "";

      // 先获取完整的日期时间格式
      const fullDateTime = this.formatDateTime(dateTime);

      // 如果包含空格（日期和时间分隔符），提取时间部分
      if (fullDateTime.includes(" ")) {
        return fullDateTime.split(" ")[1];
      }

      return fullDateTime; // 如果没有空格，可能只是时间或格式不符合预期，直接返回
    },

    // 查看记录详情
    viewRecordDetail(id) {
      // 标记需要在返回页面时刷新数据
      this._needRefresh = true;
      uni.navigateTo({
        url: `/pages/patrol/record_detail?id=${id}`,
        success: (res) => {
          console.log("跳转成功", res);
        },
        fail: (err) => {
          console.error("跳转失败", err);
          // 显示错误信息
          uni.showToast({
            title: "页面跳转失败",
            icon: "none",
            duration: 2000,
          });
        },
      });
    },
    retryLoading() {
      this.showError = false;
      this.errorMsg = ""; // 重置错误信息
      uni.showLoading({
        title: "正在重试...",
      });

      setTimeout(() => {
        uni.hideLoading();
        this.loadPatrolRecords();
      }, 1000);
    },

    // 跳转到提交巡检页面
    goToCreatePatrol() {
      console.log("点击提交巡检按钮");
      uni.showLoading({
        title: "加载中...",
      });

      setTimeout(() => {
        uni.hideLoading();
      }, 500);

      // 使用绝对路径
      uni.navigateTo({
        url: "/pages/patrol/create_record",
        success: () => {
          console.log("导航成功");
        },
        fail: (err) => {
          console.error("导航失败:", err);
          // 显示提示
          uni.showToast({
            title: "页面跳转失败",
            icon: "none",
            duration: 2000,
          });
        },
      });
    },
  },
};
</script>

<style lang="scss">
@import "../../static/styles/iconfont.scss";

.patrol-records-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f7f8fa;
  position: relative;
  width: 100%;
  padding-bottom: 120rpx; // 为底部按钮留出空间
}

// 页面顶部区域
.page-header {
  background-color: #1890ff;
  padding-bottom: 20rpx;
  position: relative;
  z-index: 100;
  width: 100%;
  top: 0rpx;
  position: sticky;
  z-index: 999;
}

.header-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #fff;
  text-align: center;
  padding: 30rpx 0 20rpx;
}

// 搜索栏优化
.search-bar {
  padding: 30rpx 30rpx 20rpx;

  .search-input-wrapper {
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 40rpx;
    padding: 12rpx 20rpx;
    margin: 0 auto;
    max-width: 680rpx;

    .iconfont {
      font-size: 32rpx;
      color: #8a8a8a;

      &.icon-clear {
        padding: 10rpx;
      }
    }

    input {
      flex: 1;
      height: 44rpx;
      font-size: 28rpx;
      margin: 0 20rpx;
      color: #333;
    }
  }

  // 筛选选项区域
  .filter-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10rpx;
    max-width: 680rpx;
    margin-left: auto;
    margin-right: auto;

    .filter-option {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 6rpx 30rpx;
      background-color: rgba(255, 255, 255, 0.9);
      border-radius: 8rpx;
      flex: 1;
      margin: 0 10rpx;

      .option-text {
        font-size: 22rpx;
        color: rgba(0, 0, 0, 0.5);
      }

      .option-value {
        font-size: 24rpx;
        color: #333;
        font-weight: 500;
        margin-top: 2rpx;
      }
    }
  }
}

// 记录列表优化
.record-list {
  padding: 20rpx;
}

.record-item {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  box-sizing: border-box;
  border-left: 4rpx solid #1890ff;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  }

  &:nth-child(3n + 1) {
    border-left-color: #1890ff;
  }

  &:nth-child(3n + 2) {
    border-left-color: #52c41a;
  }

  &:nth-child(3n + 3) {
    border-left-color: #faad14;
  }

  .record-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .record-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      max-width: 70%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .record-status {
      padding: 6rpx 16rpx;
      border-radius: 30rpx;
      font-size: 24rpx;
      min-width: 80rpx;
      text-align: center;
      font-weight: 600;

      &.pending {
        background-color: rgba(250, 173, 20, 0.15);
        color: #faad14;
      }

      &.processing {
        background-color: rgba(24, 144, 255, 0.15);
        color: #1890ff;
      }

      &.completed {
        background-color: rgba(82, 196, 26, 0.15);
        color: #52c41a;
      }
	  
	  &.overdue {
	    background-color: rgba(255, 0, 0, 0.1);
	    color: #FF0000;
	  }
    }
  }

  .record-info {
    padding: 24rpx 0;

    .info-row {
      display: flex;
      margin-bottom: 20rpx;
      align-items: center;

      &:last-child {
        margin-bottom: 0;
      }

      .info-item {
        flex: 1;
        display: flex;
        align-items: center;

        .item-icon {
          font-size: 32rpx;
          color: #8a8a8a;
          margin-right: 12rpx;
          min-width: 34rpx;
          text-align: center;
        }
         .item-label {
            width: 130rpx;
            flex-shrink: 0;
            font-size: 26rpx;
            color: #888888;
          }
        .item-value {
          font-size: 28rpx;
          color: #888888;
          word-break: break-all;
          flex: 1;

          &.abnormal {
            color: #ff4d4f;
            font-weight: bold;
          }
        }
      }

      &.date-location {
        .info-item:first-child {
          margin-right: 20rpx;
        }
      }

      &.time-row {
        align-items: center;

        .time-item {
          flex: none;
          min-width: 150rpx;
        }

        .time-separator {
          color: #8a8a8a;
          margin: 0 16rpx;
          font-size: 28rpx;
        }
      }

      &.executor-abnormal {
        .abnormal-item {
          margin-left: 30rpx;
          flex: 0.5;

          .abnormal {
            color: #ff4d4f;
            font-weight: bold;
          }
        }
      }
    }
  }

  .record-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20rpx;
    border-top: 1rpx solid #f0f0f0;
    margin-top: 10rpx;

    .record-time {
      font-size: 24rpx;
      color: #8a8a8a;
    }

    .view-detail {
      display: flex;
      align-items: center;
      color: #1890ff;
      font-size: 26rpx;
      font-weight: 500;
      background-color: rgba(24, 144, 255, 0.1);
      padding: 8rpx 16rpx;
      border-radius: 30rpx;

      .btn-text {
        margin-right: 8rpx;
      }
       &.start {
		background-color: rgba(24, 144, 255, 0.1);
		color: #1890ff;
		margin-left: 70rpx;
	  }
       &.view {
		background-color: #f5f5f5;
		color: #666666;
		margin-right: 0;
		padding: 8rpx 20rpx;
	  }
      .iconfont {
        font-size: 24rpx;
      }
    }
  }
}

// 加载状态
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 40vh;
  
  .loading-spinner {
    width: 70rpx;
    height: 70rpx;
    border: 6rpx solid #f3f3f3;
    border-top: 6rpx solid #1890ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
  }
  .loading-text {
    font-size: 28rpx;
    color: #999;
  }
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}

// 错误状态
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 50rpx 0;

  .error-text {
    font-size: 28rpx;
    color: #f5222d;
    margin-bottom: 20rpx;
  }

  .retry-btn {
    font-size: 28rpx;
    color: #fff;
    background-color: #1890ff;
    padding: 8rpx 30rpx;
    border-radius: 30rpx;
  }
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .empty-image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

// 筛选弹窗优化
.filter-popup {
  background-color: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  overflow: hidden;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .popup-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .popup-close {
      font-size: 28rpx;
      color: #1890ff;
      padding: 10rpx;
    }
  }

  .popup-content {
    padding: 10rpx 0;
    max-height: 600rpx;
    overflow-y: auto;

    .filter-option {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 28rpx 30rpx;
      font-size: 30rpx;
      color: #333;

      &.active {
        color: #1890ff;
        background-color: rgba(24, 144, 255, 0.05);

        .option-text {
          font-weight: 500;
        }
      }

      .icon-check {
        color: #1890ff;
        font-size: 30rpx;
      }
    }
  }
}

// 错误提示框优化
.error-box {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;

  .error-content {
    background-color: #fff;
    padding: 60rpx 50rpx;
    border-radius: 20rpx;
    text-align: center;
    width: 80%;
    max-width: 600rpx;
    box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);

    .error-icon {
      font-size: 80rpx;
      color: #faad14;
      margin-bottom: 40rpx;
      display: block;
    }

    .error-text {
      font-size: 32rpx;
      color: #333;
      margin-bottom: 50rpx;
      line-height: 1.6;
      display: block;
    }

    .retry-btn {
      background-color: #1890ff;
      color: #fff;
      padding: 18rpx 80rpx;
      border-radius: 45rpx;
      font-size: 32rpx;
      margin-top: 20rpx;
      border: none;
      box-shadow: 0 5rpx 15rpx rgba(24, 144, 255, 0.3);
    }
  }
}

// 浮动提交按钮优化
.floating-btn {
  position: fixed;
  right: 40rpx;
  bottom: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #1890ff, #096dd9);
  border-radius: 50%;
  box-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.4);
  z-index: 99;
  transition: transform 0.2s, box-shadow 0.2s;

  &:active {
    transform: scale(0.95);
    box-shadow: 0 3rpx 8rpx rgba(24, 144, 255, 0.3);
  }

  .plus-icon {
    font-size: 60rpx;
    color: #fff;
    font-weight: bold;
    line-height: 1;
    margin-top: -6rpx; /* 微调居中位置 */
  }
}
</style>
