/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.faq-container {
  background-color: #f5f7fa;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.search-box {
  padding: 20rpx 30rpx;
  background-color: #fff;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.search-box .search-input {
  position: relative;
  height: 80rpx;
  background-color: #f5f7fa;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
}
.search-box .search-input .search-icon {
  font-size: 32rpx;
  color: #999;
  margin-right: 10rpx;
}
.search-box .search-input input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
}
.search-box .search-input .clear-icon {
  font-size: 28rpx;
  color: #999;
  padding: 10rpx;
}
.faq-content {
  flex: 1;
  padding: 20rpx 0;
}
.faq-content .faq-section {
  margin-bottom: 20rpx;
}
.faq-content .faq-section .group-title {
  position: relative;
  padding: 20rpx 30rpx;
  background-color: #f0f5ff;
  border-left: 8rpx solid #1890ff;
}
.faq-content .faq-section .group-title .group-title-text {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}
.faq-content .no-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 100rpx;
}
.faq-content .no-result image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.faq-content .no-result text {
  font-size: 28rpx;
  color: #999;
}
.faq-item {
  background-color: #fff;
  margin-bottom: 2rpx;
}
.faq-item .faq-question {
  padding: 30rpx;
  display: flex;
  align-items: flex-start;
  position: relative;
}
.faq-item .faq-question .question-marker {
  color: #1890ff;
  font-weight: bold;
  font-size: 30rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}
.faq-item .faq-question .question-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}
.faq-item .faq-question .question-arrow {
  font-size: 24rpx;
  color: #999;
  margin-left: 20rpx;
  transition: transform 0.3s;
  transform: rotate(90deg);
}
.faq-item .faq-question .question-arrow.arrow-up {
  transform: rotate(-90deg);
}
.faq-item .faq-answer {
  padding: 0 30rpx 30rpx;
  display: flex;
  align-items: flex-start;
  background-color: #f9f9f9;
}
.faq-item .faq-answer .answer-marker {
  color: #f5222d;
  font-weight: bold;
  font-size: 30rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}
.faq-item .faq-answer .answer-text {
  flex: 1;
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
}
.feedback-section {
  padding: 30rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
  margin-top: 20rpx;
}
.feedback-section .feedback-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}
.feedback-section .feedback-btn {
  padding: 15rpx 60rpx;
  background-color: #1890ff;
  color: #fff;
  font-size: 28rpx;
  border-radius: 40rpx;
}
.feedback-section .feedback-btn:active {
  opacity: 0.8;
}