{"version": 3, "file": "index.js", "sources": ["pages/home/<USER>", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaG9tZS9pbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <BaseTabBar>\r\n\t  <view class=\"top-style\">\r\n\t\t<view class=\"top-bg\"></view>\r\n\t\t<view class=\"top-circles\">\r\n\t\t\t<view class=\"circle circle-1\"></view>\r\n\t\t\t<view class=\"circle circle-2\"></view>\r\n\t\t</view>\r\n\t\t<!-- <view class=\"top-wave\"></view> -->\r\n\t  </view>\r\n    <view class=\"home-container\">\r\n      <!-- 权限检查调试区域 -->\r\n    <!--  <view class=\"debug-section\" v-if=\"showDebug\">\r\n        <view class=\"section-title\">权限检查调试</view>\r\n        <view class=\"debug-grid\">\r\n        \r\n          <view class=\"debug-row\">\r\n            <text class=\"debug-label\">kebab-case:</text>\r\n            <permission-check permission=\"home:patrol-plans\">\r\n              <view class=\"debug-success\">✓ 有权限 (kebab-case 正常)</view>\r\n            </permission-check>\r\n          </view>\r\n\r\n\r\n          <view class=\"debug-row\">\r\n            <text class=\"debug-label\">PascalCase:</text>\r\n            <PermissionCheck permission=\"home:patrol-plans\">\r\n              <view class=\"debug-success\">✓ 有权限 (PascalCase 正常)</view>\r\n            </PermissionCheck>\r\n          </view>\r\n\r\n        \r\n          <view class=\"debug-row\">\r\n            <text class=\"debug-label\">无权限测试:</text>\r\n            <permission-check permission=\"invalid:permission\" :show-tip=\"true\">\r\n              <view class=\"debug-success\">这不应该显示</view>\r\n            </permission-check>\r\n          </view>\r\n        </view>\r\n        <button class=\"debug-toggle\" @click=\"showDebug = false\">关闭调试区域</button>\r\n        <button class=\"debug-toggle\" @click=\"refreshPermissions\">刷新权限</button>\r\n      </view> -->\r\n      \r\n      <!-- 顶部状态栏 -->\r\n      <view class=\"status-bar\">\r\n        <view class=\"weather-info\">\r\n          <text class=\"temp\">23°C</text>\r\n          <text class=\"city\">西安市</text>\r\n        </view>\r\n        <view class=\"date-info\">\r\n          <text>{{ currentDate }}</text>\r\n          <text>{{ currentWeekDay }}</text>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 数据概览 -->\r\n\t   <PermissionCheck permission=\"home:data-show\">\r\n\t\t  <view class=\"data-overview\">\t \r\n\t\t\t<view class=\"section-title\">数据概览</view>\r\n\t\t\t<view class=\"stat-cards\">\r\n\t\t\t  <view class=\"stat-card\">\r\n\t\t\t\t<text class=\"stat-value\">{{ statsData.heatUnitCount }}</text>\r\n\t\t\t\t<text class=\"stat-label\">热用户总数</text>\r\n\t\t\t  </view>\r\n\t\t\t  <view class=\"stat-card\">\r\n\t\t\t\t<text class=\"stat-value\">{{ statsData.hesOnlineRate }}</text>\r\n\t\t\t\t<text class=\"stat-label\">换热站在线率</text>\r\n\t\t\t  </view>\r\n\t\t\t  <view class=\"stat-card\">\r\n\t\t\t\t<text class=\"stat-value\">{{ statsData.weeklyAlarms }}</text>\r\n\t\t\t\t<text class=\"stat-label\">本周故障数</text>\r\n\t\t\t  </view>\r\n\t\t\t</view>\r\n\t\t  </view>\r\n      </PermissionCheck>\r\n\t\r\n      <!-- 快捷功能 -->\r\n      <view class=\"quick-access\">\r\n        <view class=\"section-title\">快捷功能</view>\r\n        <view class=\"quick-grid\">\r\n          <!-- 巡检计划 - 用于测试的kebab-case形式 -->\r\n          <PermissionCheck permission=\"home:patrol-plans\">\r\n            <view class=\"quick-item\" @click=\"navTo('/pages/patrol/plans')\">\r\n              <view class=\"quick-icon-wrapper\">\r\n                <image class=\"quick-icon\" src=\"/static/icons/patrol.png\"></image>\r\n              </view>\r\n              <text class=\"quick-text\">巡检计划</text>\r\n            </view>\r\n          </PermissionCheck>\r\n\r\n          <!-- 巡检记录 -->\r\n          <PermissionCheck permission=\"home:patrol-record\">\r\n            <view class=\"quick-item\" @click=\"navTo('/pages/patrol/records')\">\r\n              <view class=\"quick-icon-wrapper\">\r\n                <image class=\"quick-icon\" src=\"/static/icons/record.png\"></image>\r\n              </view>\r\n              <text class=\"quick-text\">巡检工单</text>\r\n            </view>\r\n          </PermissionCheck>\r\n\r\n          <!-- 故障上报 -->\r\n          <PermissionCheck permission=\"home:fault-report\">\r\n            <view class=\"quick-item\" @click=\"navTo('/pages/fault/report')\">\r\n              <view class=\"quick-icon-wrapper\">\r\n                <image class=\"quick-icon\" src=\"/static/icons/fault.png\"></image>\r\n              </view>\r\n              <text class=\"quick-text\">故障上报</text>\r\n            </view>\r\n          </PermissionCheck>\r\n\r\n          <!-- 故障列表 -->\r\n          <PermissionCheck permission=\"home:fault-list\">\r\n            <view class=\"quick-item\" @click=\"navigateTo('/pages/fault/list')\">\r\n              <view class=\"quick-icon-wrapper\">\r\n                <image class=\"quick-icon\" src=\"/static/icons/list.png\"></image>\r\n              </view>\r\n              <text class=\"quick-text\">故障列表</text>\r\n            </view>\r\n          </PermissionCheck>\r\n\r\n          <!-- 换热站控制 -->\r\n          <PermissionCheck permission=\"home:hes-control\">\r\n            <view class=\"quick-item\" @click=\"navigateTo('/pages/hes/control')\">\r\n              <view class=\"quick-icon-wrapper\">\r\n                <image class=\"quick-icon\" src=\"/static/icons/control.png\"></image>\r\n              </view>\r\n              <text class=\"quick-text\">换热站控制</text>\r\n            </view>\r\n          </PermissionCheck>\r\n\r\n          <!-- 阀门控制 -->\r\n          <PermissionCheck permission=\"home:valves-control\">\r\n            <view class=\"quick-item\" @click=\"navigateTo('/pages/valves/control')\">\r\n              <view class=\"quick-icon-wrapper\">\r\n                <image class=\"quick-icon\" src=\"/static/icons/valve.png\"></image>\r\n              </view>\r\n              <text class=\"quick-text\">阀门控制</text>\r\n            </view>\r\n          </PermissionCheck>\r\n\r\n          <!-- 室温上报 -->\r\n          <PermissionCheck permission=\"home:temp-report\">\r\n            <view class=\"quick-item\" @click=\"showTempReportModal\">\r\n              <view class=\"quick-icon-wrapper\">\r\n                <text class=\"iconfont icon-temperature quick-icon-font\"></text>\r\n              </view>\r\n              <text class=\"quick-text\">室温上报</text>\r\n            </view>\r\n          </PermissionCheck>\r\n\t\t   <PermissionCheck permission=\"home:workorder-list\">\r\n\t\t\t\t <view class=\"quick-item\" @click=\"navigateTo('/pages/workorder/list')\">\r\n\t\t\t\t  <view class=\"quick-icon-wrapper\">\r\n\t\t\t\t   <text class=\"iconfont icon-temperature quick-icon-font\"></text>\r\n\t\t\t\t  </view>\r\n\t\t\t\t  <text class=\"item-text\">维修工单</text>\r\n\t\t\t\t</view>\r\n\t\t   </PermissionCheck>\r\n\t\t   \r\n          <!-- 缴费统计 -->\r\n          <PermissionCheck permission=\"home:payment\">\r\n            <view class=\"quick-item\" @click=\"navigateTo('/pages/payment/stats')\">\r\n              <view class=\"quick-icon-wrapper\">\r\n                <image class=\"quick-icon\" src=\"/static/icons/stats.png\"></image>\r\n              </view>\r\n              <text class=\"quick-text\">缴费统计</text>\r\n            </view>\r\n          </PermissionCheck>\r\n\r\n          <!-- 人员打卡 -->\r\n          <PermissionCheck permission=\"home:attendance-clock\">\r\n            <view class=\"quick-item\" @click=\"navTo('/pages/attendance/clock-in')\">\r\n              <view class=\"quick-icon-wrapper\">\r\n                <text class=\"iconfont icon-checkin quick-icon-font\"></text>\r\n              </view>\r\n              <text class=\"quick-text\">人员打卡</text>\r\n            </view>\r\n          </PermissionCheck>\r\n\r\n          <!-- 考勤统计 -->\r\n        <!--  <PermissionCheck permission=\"home:attendance\">\r\n            <view class=\"quick-item\" @click=\"navTo('/pages/attendance/statistics')\">\r\n              <view class=\"quick-icon-wrapper\">\r\n                <text class=\"iconfont icon-chart-bar quick-icon-font\"></text>\r\n              </view>\r\n              <text class=\"quick-text\">考勤统计</text>\r\n            </view>\r\n          </PermissionCheck> -->\r\n\t\t  \r\n\t\t <PermissionCheck permission=\"home:device-list\">\r\n\t\t   <view class=\"quick-item\" @click=\"navTo('/pages/device/list')\">\r\n\t\t     <view class=\"quick-icon-wrapper\">\r\n\t\t       <text class=\"iconfont icon-chart-bar quick-icon-font\"></text>\r\n\t\t     </view>\r\n\t\t     <text class=\"quick-text\">设备管理</text>\r\n\t\t   </view>\r\n\t\t </PermissionCheck> \r\n        </view>\r\n      </view>\r\n\r\n      <!-- 最近工单 - 只在有数据时显示 -->\r\n\t   <PermissionCheck permission=\"home:workorder-list\">\r\n\t\t   <view class=\"recent-tasks repair-tasks\" v-if=\"recentTasks.length > 0\">\r\n\t\t\t<view class=\"section-header\">\r\n\t\t\t  <view class=\"section-title\">\r\n                最近维修工单\r\n              </view>\r\n\t\t\t  <text class=\"view-more repair-more\" @click=\"navigateTo('/pages/workorder/list')\">查看更多</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"task-list repair-list\">\r\n\t\t\t  <view\r\n\t\t\t\tclass=\"task-item repair-item\"\r\n\t\t\t\tv-for=\"(task, index) in recentTasks\"\r\n\t\t\t\t:key=\"index\"\r\n\t\t\t\t@click=\"viewOrderDetail(task.id)\"\r\n\t\t\t  >\r\n\t\t\t\t<view class=\"task-header repair-item-header\">\r\n\t\t\t\t  <text class=\"task-code\">{{ task.code }}</text>\r\n\t\t\t\t  <view class=\"status-tag repair-status\" :class=\"task.status\">{{ task.statusText }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"task-info\">\r\n\t\t\t\t  <text class=\"task-title repair-item-title\">{{ task.title }}</text>\r\n\t\t\t\t  <text class=\"task-desc\">{{ task.desc }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"task-footer\">\r\n\t\t\t\t  <text class=\"task-time\">{{ task.time }}</text>\r\n\t\t\t\t  <view class=\"task-action repair-action\">查看详情</view>\r\n\t\t\t\t</view>\r\n\t\t\t  </view>\r\n\t\t\t</view>\r\n\t\t  </view>\r\n      </PermissionCheck>\r\n\t  <!-- 最近巡检工单 - 只在有数据时显示 -->\r\n\t   <PermissionCheck permission=\"home:patrol-record\">\r\n\t  \t\t  <view class=\"recent-tasks patrol-tasks\" v-if=\"patrolTasks.length > 0\">\r\n\t  \t\t\t<view class=\"section-header\">\r\n\t  \t\t\t  <view class=\"section-title\">\r\n                    最近巡检工单\r\n                  </view>\r\n\t  \t\t\t  <text class=\"view-more patrol-more\" @click=\"navigateTo('/pages/patrol/records')\">查看更多</text>\r\n\t  \t\t\t</view>\r\n\r\n\t  \t\t\t<view class=\"task-list patrol-list\">\r\n\t  \t\t\t  <view\r\n\t  \t\t\t\tclass=\"task-item patrol-item\"\r\n\t  \t\t\t\tv-for=\"(task, index) in patrolTasks\"\r\n\t  \t\t\t\t:key=\"index\"\r\n\t  \t\t\t\t@click=\"viewPatrolDetail(task.id)\"\r\n\t  \t\t\t  >\r\n\t  \t\t\t\t<view class=\"task-header patrol-item-header\">\r\n\t  \t\t\t\t  <text class=\"task-code\">{{ task.code }}</text>\r\n\t  \t\t\t\t  <view class=\"status-tag patrol-status\" :class=\"task.status\">{{ task.statusText }}</view>\r\n\t  \t\t\t\t</view>\r\n\t  \t\t\t\t<view class=\"task-info\">\r\n\t  \t\t\t\t  <text class=\"task-title patrol-item-title\">{{ task.title }}</text>\r\n\t  \t\t\t\t  <text class=\"task-desc\">{{ task.desc }}</text>\r\n\t  \t\t\t\t</view>\r\n\t  \t\t\t\t<view class=\"task-footer\">\r\n\t  \t\t\t\t  <text class=\"task-time\">{{ task.time }}</text>\r\n\t  \t\t\t\t  <view class=\"task-action patrol-action\">查看详情</view>\r\n\t  \t\t\t\t</view>\r\n\t  \t\t\t  </view>\r\n\t  \t\t\t</view>\r\n\t  \t\t  </view>\r\n\t  </PermissionCheck>\r\n\r\n\t  <!-- 底部空白区域，确保内容不被底部导航栏遮挡 -->\r\n\t  <view class=\"bottom-space\"></view>\r\n\t  \r\n      <!-- 室温上报弹窗 -->\r\n      <uni-popup ref=\"tempReportPopup\" type=\"center\">\r\n        <view class=\"temp-report-modal\">\r\n          <view class=\"modal-title\">室温上报</view>\r\n          <view class=\"modal-content\">\r\n            <view class=\"form-item\">\r\n              <text class=\"form-label\">热用户名称</text>\r\n              <picker\r\n                class=\"form-input\"\r\n                @change=\"handleHeatUnitChange\"\r\n                :value=\"heatUnitIndex\"\r\n                :range=\"heatUnitOptions\"\r\n              >\r\n                <view class=\"picker-text\">{{ heatUnitOptions[heatUnitIndex] }}</view>\r\n              </picker>\r\n            </view>\r\n\r\n            <view class=\"form-item\">\r\n              <text class=\"form-label\">楼栋号</text>\r\n              <input\r\n                class=\"form-input\"\r\n                type=\"text\"\r\n                v-model=\"buildingNo\"\r\n                placeholder=\"请输入楼栋号，如：1号楼\"\r\n              />\r\n            </view>\r\n\r\n            <view class=\"form-item\">\r\n              <text class=\"form-label\">单元号</text>\r\n              <input\r\n                class=\"form-input\"\r\n                type=\"text\"\r\n                v-model=\"unitNo\"\r\n                placeholder=\"请输入单元号，如：1单元\"\r\n              />\r\n            </view>\r\n\r\n            <view class=\"form-item\">\r\n              <text class=\"form-label\">户号</text>\r\n              <input\r\n                class=\"form-input\"\r\n                type=\"text\"\r\n                v-model=\"roomNo\"\r\n                placeholder=\"请输入户号，如：101\"\r\n              />\r\n            </view>\r\n\r\n            <view class=\"form-item\">\r\n              <text class=\"form-label\">当前室内温度</text>\r\n              <view class=\"temp-slider-container\">\r\n                <slider\r\n                  @change=\"handleTempChange\"\r\n                  :value=\"reportTemp\"\r\n                  :min=\"10\"\r\n                  :max=\"35\"\r\n                  show-value\r\n                />\r\n                <text class=\"temp-value\">{{ reportTemp }}°C</text>\r\n              </view>\r\n            </view>\r\n\r\n            <view class=\"form-item\">\r\n              <text class=\"form-label\">当前室外温度</text>\r\n              <view class=\"outdoor-temp\">{{ outdoorTemp }}°C</view>\r\n            </view>\r\n\r\n            <view class=\"form-item\">\r\n              <text class=\"form-label\">图片上传</text>\r\n              <view class=\"upload-container\">\r\n                <view class=\"upload-list\">\r\n                  <view\r\n                    class=\"upload-item\"\r\n                    v-for=\"(item, index) in uploadImages\"\r\n                    :key=\"index\"\r\n                  >\r\n                    <image class=\"upload-image\" :src=\"item\" mode=\"aspectFill\"></image>\r\n                    <text class=\"upload-delete\" @click=\"deleteImage(index)\">×</text>\r\n                  </view>\r\n                  <view\r\n                    class=\"upload-button\"\r\n                    @click=\"chooseImage\"\r\n                    v-if=\"uploadImages.length < 3\"\r\n                  >\r\n                    <text class=\"upload-icon\">+</text>\r\n                    <text class=\"upload-text\">上传图片</text>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n\r\n            <view class=\"form-item\">\r\n              <text class=\"form-label\">备注信息</text>\r\n              <textarea\r\n                class=\"form-textarea\"\r\n                placeholder=\"请输入备注信息(选填)\"\r\n                v-model=\"reportRemark\"\r\n              />\r\n            </view>\r\n          </view>\r\n          <view class=\"modal-footer\">\r\n            <button class=\"btn-cancel\" @click=\"cancelTempReport\">取消</button>\r\n            <button class=\"btn-submit\" @click=\"submitTempReport\">提交</button>\r\n          </view>\r\n        </view>\r\n      </uni-popup>\r\n      \r\n      <!-- 底部安全区域，确保在iPhone等设备上有足够的底部间距 -->\r\n      <view class=\"safe-area-bottom\"></view>\r\n    </view>\r\n  </BaseTabBar>\r\n</template>\r\n\r\n<script>\r\nimport { heatUnitApi, temperatureReportApi, homeApi, patrolApi } from \"@/utils/api.js\";\r\nimport PermissionCheck from \"@/components/PermissionCheck.vue\"; // 导入权限检查组件\r\nimport BaseTabBar from \"@/components/BaseTabBar.vue\";\r\n\r\nexport default {\r\n  components: {\r\n    PermissionCheck, // 本地注册组件\r\n    BaseTabBar,\r\n  },\r\n  data() {\r\n    return {\r\n      title: \"智慧供暖运维系统\",\r\n      statsData: {\r\n        heatUnitCount: 356,\r\n        hesOnlineRate: \"98.5%\",\r\n        weeklyAlarms: 12,\r\n      },\r\n      recentTasks: [],\r\n      patrolTasks: [], // 巡检工单列表\r\n      // 室温上报相关数据\r\n      heatUnitList: [], // 热用户列表数据\r\n      heatUnitOptions: [], // 热用户名称列表\r\n      heatUnitIndex: 0,\r\n      buildingNo: \"\",\r\n      unitNo: \"\",\r\n      roomNo: \"\",\r\n      reportTemp: 22,\r\n      reportRemark: \"\",\r\n      outdoorTemp: 0,\r\n      uploadImages: [],\r\n      latitude: null,\r\n      longitude: null,\r\n      timer: null,\r\n      showDebug: true,\r\n    };\r\n  },\r\n  computed: {\r\n    // 直接从$store.state获取状态，不使用mapState\r\n    // ...mapState('attendance', [\r\n    //   'isUploadingLocation', // 是否正在上传位置\r\n    //   'clockInTime',         // 上班打卡时间\r\n    //   'clockOutTime'         // 下班打卡时间\r\n    // ]),\r\n    isUploadingLocation() {\r\n      return this.$store.state.attendance.isUploadingLocation;\r\n    },\r\n    clockInTime() {\r\n      return this.$store.state.attendance.clockInTime;\r\n    },\r\n    clockOutTime() {\r\n      return this.$store.state.attendance.clockOutTime;\r\n    },\r\n    currentDate() {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = String(now.getMonth() + 1).padStart(2, \"0\");\r\n      const day = String(now.getDate()).padStart(2, \"0\");\r\n      return `${year}年${month}月${day}日`;\r\n    },\r\n    currentWeekDay() {\r\n      const weekDays = [\r\n        \"星期日\",\r\n        \"星期一\",\r\n        \"星期二\",\r\n        \"星期三\",\r\n        \"星期四\",\r\n        \"星期五\",\r\n        \"星期六\",\r\n      ];\r\n      return weekDays[new Date().getDay()];\r\n    },\r\n  },\r\n  onLoad() {\r\n    // 页面加载时获取数据\r\n    this.loadHomeData();\r\n    // 启动定时器，每分钟更新一次时间\r\n    this.timer = setInterval(() => {\r\n      this.$forceUpdate();\r\n    }, 60000);\r\n\r\n    // 延迟检查考勤状态，确保网络和其他依赖已准备好\r\n    setTimeout(() => {\r\n      // 检查今日打卡状态并启动位置上传（如果已上班打卡）\r\n      this.checkAttendanceStatus();\r\n    }, 1000);\r\n  },\r\n  onUnload() {\r\n    // 页面卸载时清除定时器\r\n    if (this.timer) {\r\n      clearInterval(this.timer);\r\n      this.timer = null;\r\n    }\r\n  },\r\n  onShow() {\r\n    // 页面显示时获取数据\r\n    this.loadHomeData();\r\n    \r\n    // 延迟检查考勤状态，确保网络和其他依赖已准备好\r\n    setTimeout(() => {\r\n      // 检查今日打卡状态并启动位置上传（如果已上班打卡）\r\n      this.checkAttendanceStatus();\r\n    }, 1000);\r\n  },\r\n  methods: {\r\n    // 检查今日打卡状态\r\n    checkAttendanceStatus() {\r\n      //console.log('检查今日打卡状态...');\r\n\r\n      // 获取是否开启定位上传的设置\r\n      const isPositioning = uni.getStorageSync('isPositioning') || 0;\r\n      // 获取定位上传周期配置（单位：分钟）\r\n      const positionCycle = uni.getStorageSync('positionCycle') || 1;\r\n      console.log('定位上传设置:', isPositioning, '定位上传周期:', positionCycle, '分钟');\r\n\r\n      // 只有当开启了定位上传功能，才执行后续的定位上传逻辑\r\n      if (isPositioning === 1) {\r\n        console.log('已开启定位上传功能，将按照', positionCycle, '分钟的周期进行定位上传');\r\n        // 直接使用$store.dispatch调用actions\r\n        this.$store.dispatch('attendance/getTodayClockRecord')\r\n          .then((data) => {\r\n            // 获取最新状态\r\n            const clockIn = this.$store.state.attendance.clockInTime;\r\n            const clockOut = this.$store.state.attendance.clockOutTime;\r\n            const isUploading = this.$store.state.attendance.isUploadingLocation;\r\n\r\n            console.log('获取今日打卡记录成功:', {\r\n              '上班打卡': clockIn || '未打卡',\r\n              '下班打卡': clockOut || '未打卡',\r\n              '是否上传位置': isUploading ? '是' : '否',\r\n              '定位上传周期': positionCycle + '分钟'\r\n            });\r\n          })\r\n          .catch(err => {\r\n            console.error('获取今日打卡记录失败:', err);\r\n            // 发生错误时，确保停止位置上传，避免异常情况下继续上传\r\n            this.$store.dispatch('attendance/stopLocationUpload');\r\n          });\r\n      } else {\r\n        console.log('未开启定位上传功能，不进行位置上传');\r\n        // 确保停止位置上传\r\n        this.$store.dispatch('attendance/stopLocationUpload');\r\n      }\r\n    },\r\n    \r\n    // 启动位置上传\r\n    startLocationUpload() {\r\n      this.$store.dispatch('attendance/startLocationUpload');\r\n    },\r\n    \r\n    // 停止位置上传\r\n    stopLocationUpload() {\r\n      this.$store.dispatch('attendance/stopLocationUpload');\r\n    },\r\n    \r\n    // 加载首页数据\r\n    loadHomeData() {\r\n      // 获取最近工单数据\r\n      this.getRecentOrders();\r\n\r\n      // 获取统计数据\r\n      this.getStatisticsData();\r\n      \r\n      // 获取最近巡检工单数据\r\n      this.getRecentPatrolTasks();\r\n    },\r\n\r\n    // 获取最近工单\r\n    getRecentOrders() {\r\n      homeApi\r\n        .getRecentOrders(3)\r\n        .then((res) => {\r\n          if (res.code === 200 && Array.isArray(res.data.list)) {\r\n            // 获取当前用户ID\r\n            const currentUserId = uni.getStorageSync(\"userId\");\r\n            \r\n            // 格式化工单数据\r\n            this.recentTasks = res.data.list.map((item) => {\r\n              // 检查是否已转派给当前用户\r\n              let statusText = item.orderStatus;\r\n              if (item.transferUserId && item.transferUserId === currentUserId) {\r\n                statusText = \"已转派\";\r\n              }\r\n              \r\n              return {\r\n                id: item.orderId,\r\n                code: item.orderNo,\r\n                title: `${item.heatUnitName} ${item.faultType}`,\r\n                desc: item.faultLevel,\r\n                time: item.createdTime,\r\n                status: this.getStatusClass(statusText), // 使用可能已修改的状态文本来获取样式类\r\n                statusText: statusText, // 使用可能已修改的状态文本\r\n                transferUserId: item.transferUserId // 保存转派用户ID以备后续使用\r\n              };\r\n            });\r\n          } else {\r\n            console.error(\"获取最近工单失败:\", res);\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          console.error(\"获取最近工单异常:\", err);\r\n        });\r\n    },\r\n\r\n    // 获取统计数据\r\n    getStatisticsData() {\r\n      homeApi\r\n        .getHeatUnitCount()\r\n        .then((res) => {\r\n          if (res.code === 200 && res.data) {\r\n            // 更新统计数据\r\n            this.statsData.heatUnitCount = res.data.count || 0;\r\n          } else {\r\n            console.error(\"获取热用户统计数据失败:\", res);\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          console.error(\"获取热用户统计数据异常:\", err);\r\n        });\r\n\r\n      homeApi\r\n        .getHeatUnitOnlineRate()\r\n        .then((res) => {\r\n          if (res.code === 200 && res.data) {\r\n            // 更新统计数据\r\n            this.statsData.hesOnlineRate = res.data.onlineRate || \"0%\";\r\n          } else {\r\n            console.error(\"获取换热站在线率统计数据失败:\", res);\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          console.error(\"获取换热站在线率统计数据异常:\", err);\r\n        });\r\n\r\n      homeApi\r\n        .getWeeklyFaultCount()\r\n        .then((res) => {\r\n          if (res.code === 200 && res.data) {\r\n            // 更新统计数据\r\n            this.statsData.weeklyAlarms = res.data.count || 0;\r\n          } else {\r\n            console.error(\"获取本周故障告警统计数据失败:\", res);\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          console.error(\"获取本周故障告警统计数据异常:\", err);\r\n        });\r\n    },\r\n\r\n    // 根据工单状态获取状态样式类\r\n    getStatusClass(status) {\r\n      switch (status) {\r\n        case \"待接单\":\r\n          return \"warning\";\r\n        case \"进行中\":\r\n        case \"维修中\":\r\n          return \"primary\";\r\n        case \"已完成\":\r\n          return \"success\";\r\n        case \"已转派\":\r\n          return \"info\"; // 为已转派状态添加一个特殊的样式类\r\n        default:\r\n          return \"error\";\r\n      }\r\n    },\r\n\r\n    // 页面导航\r\n    navTo(url) {\r\n      uni.navigateTo({\r\n        url,\r\n      });\r\n    },\r\n\r\n    // 兼容旧方法\r\n    navigateTo(url) {\r\n      this.navTo(url);\r\n    },\r\n\r\n    // 获取热用户列表\r\n    getHeatUnitList() {\r\n      return new Promise((resolve, reject) => {\r\n        heatUnitApi\r\n          .getList()\r\n          .then((res) => {\r\n            console.log(\"热用户列表接口响应:\", res);\r\n            if (Array.isArray(res.data)) {\r\n              this.heatUnitList = res.data;\r\n              this.heatUnitOptions = this.heatUnitList.map((item) => item.name);\r\n              if (this.heatUnitList.length === 0) {\r\n                reject(new Error(\"暂无热用户数据\"));\r\n              } else {\r\n                resolve();\r\n              }\r\n            } else {\r\n              reject(new Error(res.message || \"获取热用户列表失败\"));\r\n            }\r\n          })\r\n          .catch((err) => {\r\n            console.error(\"请求失败详情:\", err);\r\n            reject(new Error(\"网络请求失败，请检查网络连接\"));\r\n          });\r\n      });\r\n    },\r\n\r\n    // 显示室温上报弹窗\r\n    async showTempReportModal() {\r\n      let loadingShown = false;\r\n      try {\r\n        loadingShown = true;\r\n        uni.showLoading({\r\n          title: \"加载中...\",\r\n          mask: true,\r\n        });\r\n\r\n        // 先获取热用户列表\r\n        await this.getHeatUnitList();\r\n\r\n        // 并行获取室外温度和位置信息\r\n        await Promise.all([this.getOutdoorTemperature(), this.getLocation()]);\r\n\r\n        // 数据加载成功后打开弹窗\r\n        this.$refs.tempReportPopup.open();\r\n      } catch (error) {\r\n        console.error(\"加载数据失败:\", error);\r\n        uni.showToast({\r\n          title: error.message || \"加载失败，请重试\",\r\n          icon: \"none\",\r\n          duration: 3000,\r\n        });\r\n      } finally {\r\n        if (loadingShown) {\r\n          uni.hideLoading();\r\n        }\r\n      }\r\n    },\r\n\r\n    handleTempChange(e) {\r\n      this.reportTemp = e.detail.value;\r\n    },\r\n\r\n    // 获取室外温度\r\n    getOutdoorTemperature() {\r\n      return new Promise((resolve, reject) => {\r\n        // 模拟获取室外温度\r\n        setTimeout(() => {\r\n          const temp = (Math.random() * 10 + 15).toFixed(1);\r\n          this.outdoorTemp = temp;\r\n          resolve(temp);\r\n        }, 500);\r\n      });\r\n    },\r\n\r\n    // 获取位置信息\r\n    getLocation() {\r\n      return new Promise((resolve, reject) => {\r\n        uni.getLocation({\r\n          type: \"gcj02\",\r\n          isHighAccuracy: true,\r\n          highAccuracyExpireTime: 3000,\r\n          success: (res) => {\r\n            this.latitude = res.latitude;\r\n            this.longitude = res.longitude;\r\n            resolve(res);\r\n          },\r\n          fail: (err) => {\r\n            console.error(\"获取位置失败:\", err);\r\n            // 使用默认坐标（西安市中心）\r\n            this.latitude = 34.343147;\r\n            this.longitude = 108.939621;\r\n\r\n            // 根据错误类型显示不同提示\r\n            let errorMsg = \"获取位置失败\";\r\n            if (err.errMsg.includes(\"permission\")) {\r\n              errorMsg = \"请授权位置权限\";\r\n            } else if (err.errMsg.includes(\"timeout\")) {\r\n              errorMsg = \"获取位置超时\";\r\n            }\r\n\r\n            uni.showToast({\r\n              title: errorMsg,\r\n              icon: \"none\",\r\n            });\r\n\r\n            resolve({\r\n              latitude: this.latitude,\r\n              longitude: this.longitude,\r\n            });\r\n          },\r\n        });\r\n      });\r\n    },\r\n\r\n    // 选择图片\r\n    chooseImage() {\r\n      uni.chooseImage({\r\n        count: 3 - this.uploadImages.length,\r\n        sizeType: [\"compressed\"],\r\n        sourceType: [\"camera\", \"album\"],\r\n        success: (res) => {\r\n          this.uploadImages = [...this.uploadImages, ...res.tempFilePaths];\r\n        },\r\n      });\r\n    },\r\n\r\n    // 删除图片\r\n    deleteImage(index) {\r\n      this.uploadImages.splice(index, 1);\r\n    },\r\n\r\n    // 取消室温上报\r\n    cancelTempReport() {\r\n      this.resetForm();\r\n      this.$refs.tempReportPopup.close();\r\n    },\r\n\r\n    // 重置表单\r\n    resetForm() {\r\n      this.heatUnitIndex = 0;\r\n      this.buildingNo = \"\";\r\n      this.unitNo = \"\";\r\n      this.roomNo = \"\";\r\n      this.reportTemp = 22;\r\n      this.reportRemark = \"\";\r\n      this.uploadImages = [];\r\n    },\r\n\r\n    // 上传图片\r\n    async uploadImage(filePath) {\r\n      return new Promise((resolve, reject) => {\r\n        uni.uploadFile({\r\n          url: \"/api/upload/image\",\r\n          filePath: filePath,\r\n          name: \"file\",\r\n          header: {\r\n            Authorization: \"Bearer \" + uni.getStorageSync(\"token\"),\r\n          },\r\n          success: (res) => {\r\n            try {\r\n              const data = JSON.parse(res.data);\r\n              if (data.code === 200) {\r\n                resolve(data.data.url);\r\n              } else {\r\n                reject(new Error(data.message || \"上传失败\"));\r\n              }\r\n            } catch (e) {\r\n              reject(new Error(\"解析响应失败\"));\r\n            }\r\n          },\r\n          fail: (err) => {\r\n            reject(new Error(\"网络请求失败\"));\r\n          },\r\n        });\r\n      });\r\n    },\r\n\r\n    // 提交室温上报\r\n    async submitTempReport() {\r\n      try {\r\n        // 表单验证\r\n        if (this.heatUnitOptions.length === 0) {\r\n          throw new Error(\"请等待热用户数据加载\");\r\n        }\r\n\r\n        const validations = [\r\n          { value: this.buildingNo.trim(), message: \"请输入楼栋号\" },\r\n          { value: this.unitNo.trim(), message: \"请输入单元号\" },\r\n          { value: this.roomNo.trim(), message: \"请输入户号\" },\r\n        ];\r\n\r\n        for (const validation of validations) {\r\n          if (!validation.value) {\r\n            throw new Error(validation.message);\r\n          }\r\n        }\r\n\r\n        uni.showLoading({\r\n          title: \"正在提交\",\r\n          mask: true,\r\n        });\r\n\r\n        // 上传图片\r\n        const uploadedImages = [];\r\n        if (this.uploadImages.length > 0) {\r\n          try {\r\n            for (const filePath of this.uploadImages) {\r\n              const imageUrl = await this.uploadImage(filePath);\r\n              uploadedImages.push(imageUrl);\r\n            }\r\n          } catch (error) {\r\n            throw new Error(\"图片上传失败：\" + error.message);\r\n          }\r\n        }\r\n\r\n        // 构建提交数据\r\n        const reportData = {\r\n          heat_unit_name: this.heatUnitOptions[this.heatUnitIndex],\r\n          building_no: this.buildingNo.trim(),\r\n          unit_no: this.unitNo.trim(),\r\n          room_no: this.roomNo.trim(),\r\n          indoor_temp: this.reportTemp,\r\n          outdoor_temp: this.outdoorTemp,\r\n          latitude: this.latitude,\r\n          longitude: this.longitude,\r\n          images: uploadedImages,\r\n          videos: [], // Assuming no videos are uploaded in this context\r\n          remark: this.reportRemark.trim(),\r\n          report_user_id: uni.getStorageSync(\"userId\"), // Assuming user_id is stored in local storage\r\n        };\r\n\r\n        // 使用 temperatureReportApi 提交数据\r\n        const response = await temperatureReportApi.submit(reportData);\r\n\r\n        if (response.code === 200) {\r\n          uni.showToast({\r\n            title: \"室温上报成功\",\r\n            icon: \"success\",\r\n          });\r\n\r\n          this.resetForm();\r\n          this.$refs.tempReportPopup.close();\r\n        } else {\r\n          throw new Error(response.message || \"提交失败\");\r\n        }\r\n      } catch (error) {\r\n        console.error(\"提交失败:\", error);\r\n        // 更详细的错误信息显示\r\n        let errorMsg = \"提交失败，请重试\";\r\n\r\n        if (error.errMsg) {\r\n          // 网络错误\r\n          if (error.errMsg.includes(\"timeout\")) {\r\n            errorMsg = \"请求超时，请检查网络连接\";\r\n          } else if (error.errMsg.includes(\"network\")) {\r\n            errorMsg = \"网络连接异常，请检查网络\";\r\n          }\r\n        } else if (error.message) {\r\n          // 服务器返回的错误\r\n          errorMsg = error.message;\r\n        }\r\n\r\n        uni.showToast({\r\n          title: errorMsg,\r\n          icon: \"none\",\r\n          duration: 2000,\r\n        });\r\n      } finally {\r\n        uni.hideLoading();\r\n      }\r\n    },\r\n\r\n    handleHeatUnitChange(e) {\r\n      this.heatUnitIndex = e.detail.value;\r\n    },\r\n\r\n    // 查看工单详情\r\n    viewOrderDetail(id) {\r\n      uni.navigateTo({\r\n        url: `/pages/workorder/detail?id=${id}`,\r\n      });\r\n    },\r\n\r\n    // 获取最近巡检工单数据\r\n    getRecentPatrolTasks() {\r\n      patrolApi\r\n        .getLimitedPatrolRecords(3)\r\n        .then((res) => {\r\n          if (res.code === 200 && Array.isArray(res.data)) {\r\n            // 格式化巡检工单数据\r\n\t\t\t//console.log(res.data)\r\n            this.patrolTasks = res.data.map((item) => {\r\n              return {\r\n                id: item.id,\r\n                code: `${item.patrolType}`,\r\n                title: item.planName || \"巡检计划\",\r\n                desc: `执行人:${item.executorName || \"未指定\"}`,\r\n                time: this.formatPatrolDateTime(item.executionDate || item.createTime),\r\n                status: this.getPatrolStatusClass(item.status),\r\n                statusText: this.getPatrolStatusText(item.status),\r\n              };\r\n            });\r\n          } else {\r\n            console.error(\"获取最近巡检工单失败:\", res);\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          console.error(\"获取最近巡检工单异常:\", err);\r\n        });\r\n    },\r\n    \r\n    // 获取巡检状态文本\r\n    getPatrolStatusText(status) {\r\n      switch (status) {\r\n        case \"pending\":\r\n          return \"待执行\";\r\n        case \"processing\":\r\n          return \"执行中\";\r\n        case \"completed\":\r\n          return \"已完成\";\r\n        case \"overdue\":\r\n          return \"已超时\";\r\n        default:\r\n          return status || \"未知\";\r\n      }\r\n    },\r\n    \r\n    // 获取巡检状态样式类\r\n    getPatrolStatusClass(status) {\r\n      switch (status) {\r\n        case \"pending\":\r\n          return \"warning\";\r\n        case \"processing\":\r\n          return \"primary\";\r\n        case \"completed\":\r\n          return \"success\";\r\n        case \"overdue\":\r\n          return \"error\";\r\n        default:\r\n          return \"error\";\r\n      }\r\n    },\r\n    \r\n    // 格式化巡检工单日期时间\r\n    formatPatrolDateTime(dateTime) {\r\n      if (!dateTime) return \"\";\r\n      \r\n      // 处理数组格式的日期时间\r\n      if (Array.isArray(dateTime)) {\r\n        if (dateTime.length >= 3) {\r\n          const year = dateTime[0];\r\n          const month = String(dateTime[1]).padStart(2, \"0\");\r\n          const day = String(dateTime[2]).padStart(2, \"0\");\r\n          return `${year}-${month}-${day}`;\r\n        }\r\n        return dateTime.join(\"-\");\r\n      }\r\n      \r\n      // 处理字符串格式\r\n      const dateTimeStr = String(dateTime);\r\n      \r\n      // 如果包含T，说明是ISO格式\r\n      if (dateTimeStr.includes(\"T\")) {\r\n        const date = new Date(dateTimeStr);\r\n        const year = date.getFullYear();\r\n        const month = String(date.getMonth() + 1).padStart(2, \"0\");\r\n        const day = String(date.getDate()).padStart(2, \"0\");\r\n        return `${year}-${month}-${day}`;\r\n      }\r\n      \r\n      // 如果是数字类型（时间戳）\r\n      if (!isNaN(dateTime) && typeof dateTime === \"number\") {\r\n        const date = new Date(dateTime);\r\n        const year = date.getFullYear();\r\n        const month = String(date.getMonth() + 1).padStart(2, \"0\");\r\n        const day = String(date.getDate()).padStart(2, \"0\");\r\n        return `${year}-${month}-${day}`;\r\n      }\r\n      \r\n      // 如果包含空格，可能是完整的日期时间格式，只取日期部分\r\n      if (dateTimeStr.includes(\" \")) {\r\n        return dateTimeStr.split(\" \")[0];\r\n      }\r\n      \r\n      return dateTimeStr;\r\n    },\r\n\r\n    // 新增方法：查看巡检工单详情\r\n    viewPatrolDetail(id) {\r\n      uni.navigateTo({\r\n        url: `/pages/patrol/record_detail?id=${id}`,\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.home-container {\r\n  padding: 0 30rpx;\r\n  box-sizing: border-box;\r\n  padding-bottom: 120rpx; /* 增加底部padding，避免内容被底部导航栏遮挡 */\r\n}\r\n\r\n/* 顶部样式增强 */\r\n.top-style {\r\n\theight: 80rpx;\r\n\twidth: 100%;\r\n\tpadding: 0;\r\n\tbackground: linear-gradient(135deg, #35a6c8, #1e88e5);\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n\tz-index: 10;\r\n\tposition: sticky;\r\n\ttop: 0;\r\n\tz-index: 999;\r\n}\r\n\r\n.top-bg {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground: linear-gradient(135deg, #35a6c8, #1e88e5);\r\n\topacity: 0.9;\r\n\tz-index: 1;\r\n}\r\n\r\n.top-circles {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tz-index: 2;\r\n}\r\n\r\n.circle {\r\n\tposition: absolute;\r\n\tborder-radius: 50%;\r\n\tbackground: rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.circle-1 {\r\n\twidth: 200rpx;\r\n\theight: 200rpx;\r\n\ttop: -80rpx;\r\n\tright: 60rpx;\r\n}\r\n\r\n.circle-2 {\r\n\twidth: 300rpx;\r\n\theight: 300rpx;\r\n\ttop: 20rpx;\r\n\tleft: -120rpx;\r\n}\r\n\r\n.top-wave {\r\n\tposition: absolute;\r\n\tbottom: -2rpx;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 40rpx;\r\n\tbackground: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23ffffff' fill-opacity='1' d='M0,192L48,176C96,160,192,128,288,138.7C384,149,480,203,576,208C672,213,768,171,864,154.7C960,139,1056,149,1152,154.7C1248,160,1344,160,1392,160L1440,160L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E\");\r\n\tbackground-size: 100% 100%;\r\n\tz-index: 3;\r\n}\r\n\r\n/* 调试区域样式 */\r\n.debug-section {\r\n  background-color: #f8f9fa;\r\n  border: 2rpx solid #dee2e6;\r\n  border-radius: 12rpx;\r\n  padding: 20rpx;\r\n  margin-bottom: 30rpx;\r\n\r\n  .debug-grid {\r\n    margin: 20rpx 0;\r\n  }\r\n\r\n  .debug-row {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 16rpx;\r\n    padding: 10rpx 0;\r\n    border-bottom: 1rpx solid #eee;\r\n  }\r\n\r\n  .debug-label {\r\n    width: 200rpx;\r\n    font-size: 26rpx;\r\n    color: #666;\r\n    font-weight: bold;\r\n  }\r\n\r\n  .debug-success {\r\n    color: #52c41a;\r\n    font-weight: bold;\r\n  }\r\n\r\n  .debug-toggle {\r\n    font-size: 24rpx;\r\n    padding: 10rpx 20rpx;\r\n    background: #eee;\r\n    border: none;\r\n    border-radius: 8rpx;\r\n  }\r\n}\r\n\r\n.status-bar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 30rpx;\r\n  margin-top: 10rpx;\r\n  .weather-info {\r\n    display: flex;\r\n    flex-direction: column;\r\n\r\n    .temp {\r\n      font-size: 36rpx;\r\n      font-weight: bold;\r\n    }\r\n\r\n    .city {\r\n      font-size: 24rpx;\r\n      color: $uni-text-color-grey;\r\n    }\r\n  }\r\n\r\n  .date-info {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: flex-end;\r\n\r\n    text {\r\n      font-size: 24rpx;\r\n      color: $uni-text-color-grey;\r\n    }\r\n  }\r\n}\r\n\r\n.section-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 20rpx;\r\n  position: relative;\r\n  padding-left: 20rpx;\r\n\r\n  &::before {\r\n    content: \"\";\r\n    position: absolute;\r\n    left: 0;\r\n    top: 8rpx;\r\n    width: 8rpx;\r\n    height: 32rpx;\r\n    background-color: $uni-color-primary;\r\n    border-radius: 4rpx;\r\n  }\r\n}\r\n\r\n.stat-cards {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 30rpx;\r\n\r\n  .stat-card {\r\n    width: 30%;\r\n    height: 160rpx;\r\n    background-color: #fff;\r\n    border-radius: 8rpx;\r\n    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-items: center;\r\n\r\n    .stat-value {\r\n      font-size: 40rpx;\r\n      font-weight: bold;\r\n      color: $uni-color-primary;\r\n      margin-bottom: 10rpx;\r\n    }\r\n\r\n    .stat-label {\r\n      font-size: 24rpx;\r\n      color: $uni-text-color-grey;\r\n    }\r\n  }\r\n}\r\n\r\n.quick-access {\r\n  margin-bottom: 40rpx;\r\n\r\n  .section-title {\r\n    margin-bottom: 30rpx;\r\n  }\r\n}\r\n\r\n.quick-grid {\r\n  display: flex !important;\r\n  flex-wrap: wrap !important;\r\n  justify-content: flex-start !important;\r\n  align-items: flex-start !important;\r\n  background-color: #fff;\r\n  border-radius: 20rpx;\r\n  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.05);\r\n  padding: 30rpx 20rpx 10rpx;\r\n  position: relative;\r\n\r\n  &::before {\r\n    content: \"\";\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    height: 200rpx;\r\n    background: linear-gradient(\r\n      180deg,\r\n      rgba(245, 247, 250, 0.8) 0%,\r\n      rgba(255, 255, 255, 0) 100%\r\n    );\r\n    border-radius: 20rpx 20rpx 0 0;\r\n    z-index: 0;\r\n  }\r\n}\r\n\r\n.quick-item {\r\n  width: 25% !important;\r\n  box-sizing: border-box !important;\r\n  display: flex !important;\r\n  flex-direction: column !important;\r\n  align-items: center !important;\r\n  justify-content: flex-start !important;\r\n  margin-bottom: 30rpx !important;\r\n  position: relative !important;\r\n  z-index: 1 !important;\r\n  padding: 10rpx !important;\r\n  transition: all 0.3s ease;\r\n\r\n  &:active {\r\n    transform: translateY(6rpx) scale(0.95);\r\n  }\r\n\r\n  .quick-icon-wrapper {\r\n    width: 110rpx !important;\r\n    height: 110rpx !important;\r\n    margin-bottom: 16rpx !important;\r\n    border-radius: 28rpx !important;\r\n    overflow: hidden !important;\r\n    position: relative !important;\r\n    display: flex !important;\r\n    justify-content: center !important;\r\n    align-items: center !important;\r\n    box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.15) !important;\r\n  }\r\n\r\n  .quick-icon {\r\n    width: 60% !important;\r\n    height: 60% !important;\r\n    object-fit: contain !important;\r\n    filter: brightness(0) invert(1) !important;\r\n  }\r\n\r\n  .quick-icon-font {\r\n    font-size: 55rpx !important;\r\n    color: #ffffff !important;\r\n    text-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2) !important;\r\n  }\r\n\r\n  .quick-text {\r\n    font-size: 26rpx !important;\r\n    color: $uni-text-color !important;\r\n    font-weight: 500 !important;\r\n    line-height: 1.4 !important;\r\n    text-align: center !important;\r\n    letter-spacing: 1rpx !important;\r\n    margin-top: 6rpx !important;\r\n  }\r\n}\r\n\r\n/* 快捷图标样式 - 确保每个图标有独特颜色 */\r\n.quick-item:nth-of-type(1) .quick-icon-wrapper {\r\n  background: linear-gradient(135deg, #6a9eef, #4483e5) !important;\r\n}\r\n\r\n.quick-item:nth-of-type(2) .quick-icon-wrapper {\r\n  background: linear-gradient(135deg, #60c6a8, #3aaf8f) !important;\r\n}\r\n\r\n.quick-item:nth-of-type(3) .quick-icon-wrapper {\r\n  background: linear-gradient(135deg, #f3a768, #ee8c3c) !important;\r\n}\r\n\r\n.quick-item:nth-of-type(4) .quick-icon-wrapper {\r\n  background: linear-gradient(135deg, #e47474, #e05555) !important;\r\n}\r\n\r\n.quick-item:nth-of-type(5) .quick-icon-wrapper {\r\n  background: linear-gradient(135deg, #8387ea, #5a5fd3) !important;\r\n}\r\n\r\n.quick-item:nth-of-type(6) .quick-icon-wrapper {\r\n  background: linear-gradient(135deg, #55c1e3, #35a6c8) !important;\r\n}\r\n\r\n.quick-item:nth-of-type(7) .quick-icon-wrapper {\r\n  background: linear-gradient(135deg, #e680b3, #d65698) !important;\r\n}\r\n\r\n.quick-item:nth-of-type(8) .quick-icon-wrapper {\r\n  background: linear-gradient(135deg, #9dc75b, #7bae35) !important;\r\n}\r\n\r\n.quick-item:nth-of-type(9) .quick-icon-wrapper {\r\n  background: linear-gradient(135deg, #6a9eef, #4483e5) !important;\r\n}\r\n\r\n.quick-item:nth-of-type(10) .quick-icon-wrapper {\r\n  background: linear-gradient(135deg, #60c6a8, #3aaf8f) !important;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n\r\n  .view-more {\r\n    font-size: 24rpx;\r\n    color: $uni-color-primary;\r\n  }\r\n}\r\n\r\n.task-list {\r\n  .task-item {\r\n    background-color: #fff;\r\n    border-radius: 8rpx;\r\n    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);\r\n    padding: 24rpx;\r\n    margin-bottom: 20rpx;\r\n    position: relative;\r\n    overflow: hidden;\r\n    display: flex;\r\n    flex-direction: column;\r\n    width: 100%;\r\n    box-sizing: border-box;\r\n\r\n    &:active {\r\n      background-color: #f9f9f9;\r\n    }\r\n    \r\n    &:last-child {\r\n      margin-bottom: 30rpx; /* 确保最后一个项目底部有足够的间距 */\r\n    }\r\n\r\n    .task-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 16rpx;\r\n      width: 100%;\r\n\r\n      .task-code {\r\n        font-size: 28rpx;\r\n        color: $uni-text-color-grey;\r\n        flex: 1;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n      \r\n      .status-tag {\r\n        flex-shrink: 0;\r\n        min-width: 80rpx;\r\n        text-align: center;\r\n      }\r\n    }\r\n\r\n    .task-info {\r\n      margin-bottom: 16rpx;\r\n      width: 100%;\r\n\r\n      .task-title {\r\n        font-size: 32rpx;\r\n        font-weight: bold;\r\n        color: $uni-text-color;\r\n        margin-bottom: 8rpx;\r\n        display: block;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .task-desc {\r\n        font-size: 28rpx;\r\n        color: $uni-text-color-grey;\r\n        display: block;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n    }\r\n\r\n    .task-footer {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      width: 100%;\r\n\r\n      .task-time {\r\n        font-size: 24rpx;\r\n        color: $uni-text-color-grey;\r\n        flex: 1;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .task-action {\r\n        font-size: 24rpx;\r\n        color: $uni-color-primary;\r\n        flex-shrink: 0;\r\n        margin-left: 10rpx;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.status-tag {\r\n  display: inline-block;\r\n  padding: 6rpx 16rpx;\r\n  font-size: 24rpx;\r\n  border-radius: 30rpx;\r\n  min-width: 80rpx;\r\n  text-align: center;\r\n  font-weight: 600;\r\n\r\n  &.primary {\r\n    background-color: rgba(24, 144, 255, 0.15);\r\n    color: #1890ff;\r\n  }\r\n\r\n  &.success {\r\n    background-color: rgba(82, 196, 26, 0.15);\r\n    color: #52c41a;\r\n  }\r\n\r\n  &.warning {\r\n    background-color: rgba(250, 173, 20, 0.15);\r\n    color: #faad14;\r\n  }\r\n\r\n  &.error {\r\n    background-color: rgba(255, 0, 0, 0.1);\r\n    color: #FF0000;\r\n  }\r\n  \r\n  &.info {\r\n    background-color: rgba(144, 147, 153, 0.1);\r\n    color: #909399; /* 使用灰色调表示已转派状态 */\r\n  }\r\n}\r\n\r\n.temp-report-modal {\r\n  background-color: #fff;\r\n  width: 650rpx;\r\n  border-radius: 16rpx;\r\n  overflow: hidden;\r\n\r\n  .modal-title {\r\n    text-align: center;\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n    padding: 30rpx 0;\r\n    border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .modal-content {\r\n    padding: 30rpx;\r\n    max-height: 800rpx;\r\n    overflow-y: auto;\r\n\r\n    .form-item {\r\n      margin-bottom: 30rpx;\r\n\r\n      .form-label {\r\n        display: block;\r\n        font-size: 28rpx;\r\n        color: $uni-text-color;\r\n        margin-bottom: 16rpx;\r\n      }\r\n\r\n      .form-input {\r\n        width: 100%;\r\n        height: 80rpx;\r\n        line-height: 80rpx;\r\n        padding: 0 20rpx;\r\n        border-radius: 8rpx;\r\n        background-color: #f5f5f5;\r\n        box-sizing: border-box;\r\n\r\n        .picker-text {\r\n          font-size: 28rpx;\r\n        }\r\n      }\r\n\r\n      .temp-slider-container {\r\n        margin-top: 20rpx;\r\n        position: relative;\r\n\r\n        .temp-value {\r\n          position: absolute;\r\n          right: 0;\r\n          top: -50rpx;\r\n          font-size: 32rpx;\r\n          color: $uni-color-primary;\r\n          font-weight: bold;\r\n        }\r\n      }\r\n\r\n      .outdoor-temp {\r\n        font-size: 32rpx;\r\n        color: $uni-color-primary;\r\n        font-weight: bold;\r\n        padding: 10rpx 0;\r\n      }\r\n\r\n      .upload-container {\r\n        .upload-list {\r\n          display: flex;\r\n          flex-wrap: wrap;\r\n          margin: 0 -10rpx;\r\n\r\n          .upload-item,\r\n          .upload-button {\r\n            width: 180rpx;\r\n            height: 180rpx;\r\n            margin: 10rpx;\r\n            border-radius: 8rpx;\r\n            overflow: hidden;\r\n            position: relative;\r\n          }\r\n\r\n          .upload-item {\r\n            .upload-image {\r\n              width: 100%;\r\n              height: 100%;\r\n              object-fit: cover;\r\n            }\r\n\r\n            .upload-delete {\r\n              position: absolute;\r\n              top: 0;\r\n              right: 0;\r\n              width: 40rpx;\r\n              height: 40rpx;\r\n              background-color: rgba(0, 0, 0, 0.5);\r\n              color: #fff;\r\n              text-align: center;\r\n              line-height: 40rpx;\r\n              font-size: 24rpx;\r\n              z-index: 1;\r\n            }\r\n          }\r\n\r\n          .upload-button {\r\n            display: flex;\r\n            flex-direction: column;\r\n            justify-content: center;\r\n            align-items: center;\r\n            background-color: #f5f5f5;\r\n            border: 1rpx dashed #ddd;\r\n\r\n            .upload-icon {\r\n              font-size: 60rpx;\r\n              color: #999;\r\n              line-height: 1;\r\n              margin-bottom: 10rpx;\r\n            }\r\n\r\n            .upload-text {\r\n              font-size: 24rpx;\r\n              color: #999;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .form-textarea {\r\n        width: 100%;\r\n        height: 160rpx;\r\n        padding: 20rpx;\r\n        border-radius: 8rpx;\r\n        background-color: #f5f5f5;\r\n        box-sizing: border-box;\r\n        font-size: 28rpx;\r\n      }\r\n    }\r\n  }\r\n\r\n  .modal-footer {\r\n    display: flex;\r\n    border-top: 1rpx solid rgba(0, 0, 0, 0.1);\r\n\r\n    button {\r\n      flex: 1;\r\n      border: none;\r\n      height: 100rpx;\r\n      line-height: 100rpx;\r\n      font-size: 32rpx;\r\n      border-radius: 0;\r\n\r\n      &::after {\r\n        border: none;\r\n      }\r\n    }\r\n\r\n    .btn-cancel {\r\n      background-color: #f5f5f5;\r\n      color: $uni-text-color;\r\n    }\r\n\r\n    .btn-submit {\r\n      background-color: $uni-color-primary;\r\n      color: #fff;\r\n    }\r\n  }\r\n}\r\n\r\n/* 无权限提示样式 */\r\n.no-permission {\r\n  height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #f5f5f5;\r\n  padding: 0 40rpx;\r\n\r\n  .no-permission-text {\r\n    font-size: 32rpx;\r\n    color: #999;\r\n    text-align: center;\r\n    margin-bottom: 30rpx;\r\n  }\r\n}\r\n\r\n/* 确保组件不生成额外DOM */\r\n::v-deep PermissionCheck {\r\n  display: contents !important;\r\n}\r\n\r\n.recent-tasks {\r\n  // margin-bottom: 10rpx;\r\n  position: relative; /* 确保定位正确 */\r\n  z-index: 1; /* 确保层级正确 */\r\n}\r\n\r\n.bottom-space {\r\n  height: 20rpx; /* 底部空白区域高度 */\r\n  width: 100%;\r\n}\r\n\r\n.safe-area-bottom {\r\n  height: env(safe-area-inset-bottom, 0);\r\n  width: 100%;\r\n}\r\n\r\n/* 巡检工单特殊样式 */\r\n.patrol-tasks {\r\n  margin-top: 30rpx;\r\n  margin-bottom: 40rpx;\r\n}\r\n\r\n\r\n\r\n.patrol-more {\r\n  color: #3aaf8f;\r\n}\r\n\r\n.patrol-list {\r\n  background: linear-gradient(180deg, rgba(96, 198, 168, 0.05));\r\n  padding: 10rpx;\r\n  border-radius: 0 0 12rpx 12rpx;\r\n}\r\n\r\n.patrol-item {\r\n  background: #fff;\r\n  border-left: 8rpx solid #3aaf8f;\r\n  border-radius: 4rpx 8rpx 8rpx 4rpx;\r\n  box-shadow: 0 4rpx 16rpx rgba(58, 175, 143, 0.1);\r\n  transition: all 0.3s ease;\r\n  \r\n  &:active {\r\n    transform: translateY(2rpx);\r\n    box-shadow: 0 2rpx 8rpx rgba(58, 175, 143, 0.1);\r\n  }\r\n}\r\n\r\n.patrol-item-header {\r\n  padding-bottom: 16rpx;\r\n  border-bottom: 1rpx dashed rgba(58, 175, 143, 0.2);\r\n}\r\n\r\n.patrol-item-title {\r\n  color: #3aaf8f;\r\n  font-weight: 600;\r\n}\r\n\r\n.patrol-status {\r\n  &.primary {\r\n    background-color: rgba(58, 175, 143, 0.15);\r\n    color: #3aaf8f;\r\n  }\r\n}\r\n\r\n.patrol-action {\r\n  color: #3aaf8f;\r\n  font-weight: 600;\r\n  background-color: rgba(58, 175, 143, 0.1);\r\n  padding: 4rpx 16rpx;\r\n  border-radius: 20rpx;\r\n}\r\n\r\n/* 维修工单特殊样式 */\r\n.repair-tasks {\r\n  margin-top: 30rpx;\r\n  margin-bottom: 40rpx;\r\n}\r\n\r\n.repair-more {\r\n  color: #e05555;\r\n}\r\n\r\n.repair-list {\r\n  background: linear-gradient(180deg, rgba(228, 116, 116, 0.05));\r\n  padding: 10rpx;\r\n  border-radius: 0 0 12rpx 12rpx;\r\n}\r\n\r\n.repair-item {\r\n  background: #fff;\r\n  border-left: 8rpx solid #e05555;\r\n  border-radius: 4rpx 8rpx 8rpx 4rpx;\r\n  box-shadow: 0 4rpx 16rpx rgba(224, 85, 85, 0.1);\r\n  transition: all 0.3s ease;\r\n  \r\n  &:active {\r\n    transform: translateY(2rpx);\r\n    box-shadow: 0 2rpx 8rpx rgba(224, 85, 85, 0.1);\r\n  }\r\n}\r\n\r\n.repair-item-header {\r\n  padding-bottom: 16rpx;\r\n  border-bottom: 1rpx dashed rgba(224, 85, 85, 0.2);\r\n}\r\n\r\n.repair-item-title {\r\n  color: #e05555;\r\n  font-weight: 600;\r\n}\r\n\r\n.repair-status {\r\n  &.primary {\r\n    background-color: rgba(224, 85, 85, 0.15);\r\n    color: #e05555;\r\n  }\r\n  \r\n  &.warning {\r\n    background-color: rgba(250, 173, 20, 0.15);\r\n    color: #faad14;\r\n  }\r\n}\r\n\r\n.repair-action {\r\n  color: #e05555;\r\n  font-weight: 600;\r\n  background-color: rgba(58, 175, 143, 0.1);\r\n  padding: 4rpx 16rpx;\r\n  border-radius: 20rpx;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/home/<USER>'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "homeApi", "heatUnitApi", "temperatureReportApi", "patrolApi"], "mappings": ";;;;AA+XA,MAAO,kBAAiB,MAAW;AACnC,mBAAmB,MAAW;AAE9B,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA;AAAA,IACA;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,OAAO;AAAA,MACP,WAAW;AAAA,QACT,eAAe;AAAA,QACf,eAAe;AAAA,QACf,cAAc;AAAA,MACf;AAAA,MACD,aAAa,CAAE;AAAA,MACf,aAAa,CAAE;AAAA;AAAA;AAAA,MAEf,cAAc,CAAE;AAAA;AAAA,MAChB,iBAAiB,CAAE;AAAA;AAAA,MACnB,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,aAAa;AAAA,MACb,cAAc,CAAE;AAAA,MAChB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,OAAO;AAAA,MACP,WAAW;AAAA;EAEd;AAAA,EACD,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOR,sBAAsB;AACpB,aAAO,KAAK,OAAO,MAAM,WAAW;AAAA,IACrC;AAAA,IACD,cAAc;AACZ,aAAO,KAAK,OAAO,MAAM,WAAW;AAAA,IACrC;AAAA,IACD,eAAe;AACb,aAAO,KAAK,OAAO,MAAM,WAAW;AAAA,IACrC;AAAA,IACD,cAAc;AACZ,YAAM,MAAM,oBAAI;AAChB,YAAM,OAAO,IAAI;AACjB,YAAM,QAAQ,OAAO,IAAI,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACxD,YAAM,MAAM,OAAO,IAAI,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AACjD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,IAC/B;AAAA,IACD,iBAAiB;AACf,YAAM,WAAW;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAEF,aAAO,UAAS,oBAAI,QAAO,OAAQ,CAAA;AAAA,IACpC;AAAA,EACF;AAAA,EACD,SAAS;AAEP,SAAK,aAAY;AAEjB,SAAK,QAAQ,YAAY,MAAM;AAC7B,WAAK,aAAY;AAAA,IAClB,GAAE,GAAK;AAGR,eAAW,MAAM;AAEf,WAAK,sBAAqB;AAAA,IAC3B,GAAE,GAAI;AAAA,EACR;AAAA,EACD,WAAW;AAET,QAAI,KAAK,OAAO;AACd,oBAAc,KAAK,KAAK;AACxB,WAAK,QAAQ;AAAA,IACf;AAAA,EACD;AAAA,EACD,SAAS;AAEP,SAAK,aAAY;AAGjB,eAAW,MAAM;AAEf,WAAK,sBAAqB;AAAA,IAC3B,GAAE,GAAI;AAAA,EACR;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,wBAAwB;AAItB,YAAM,gBAAgBA,cAAG,MAAC,eAAe,eAAe,KAAK;AAE7D,YAAM,gBAAgBA,cAAG,MAAC,eAAe,eAAe,KAAK;AAC7DA,0BAAA,MAAA,OAAA,+BAAY,WAAW,eAAe,WAAW,eAAe,IAAI;AAGpE,UAAI,kBAAkB,GAAG;AACvBA,sBAAA,MAAA,MAAA,OAAA,+BAAY,iBAAiB,eAAe,aAAa;AAEzD,aAAK,OAAO,SAAS,gCAAgC,EAClD,KAAK,CAAC,SAAS;AAEd,gBAAM,UAAU,KAAK,OAAO,MAAM,WAAW;AAC7C,gBAAM,WAAW,KAAK,OAAO,MAAM,WAAW;AAC9C,gBAAM,cAAc,KAAK,OAAO,MAAM,WAAW;AAEjDA,wBAAAA,MAAA,MAAA,OAAA,+BAAY,eAAe;AAAA,YACzB,QAAQ,WAAW;AAAA,YACnB,QAAQ,YAAY;AAAA,YACpB,UAAU,cAAc,MAAM;AAAA,YAC9B,UAAU,gBAAgB;AAAA,UAC5B,CAAC;AAAA,SACF,EACA,MAAM,SAAO;AACZA,wBAAc,MAAA,MAAA,SAAA,+BAAA,eAAe,GAAG;AAEhC,eAAK,OAAO,SAAS,+BAA+B;AAAA,QACtD,CAAC;AAAA,aACE;AACLA,sBAAAA,MAAA,MAAA,OAAA,+BAAY,mBAAmB;AAE/B,aAAK,OAAO,SAAS,+BAA+B;AAAA,MACtD;AAAA,IACD;AAAA;AAAA,IAGD,sBAAsB;AACpB,WAAK,OAAO,SAAS,gCAAgC;AAAA,IACtD;AAAA;AAAA,IAGD,qBAAqB;AACnB,WAAK,OAAO,SAAS,+BAA+B;AAAA,IACrD;AAAA;AAAA,IAGD,eAAe;AAEb,WAAK,gBAAe;AAGpB,WAAK,kBAAiB;AAGtB,WAAK,qBAAoB;AAAA,IAC1B;AAAA;AAAA,IAGD,kBAAkB;AAChBC,gBAAM,QACH,gBAAgB,CAAC,EACjB,KAAK,CAAC,QAAQ;AACb,YAAI,IAAI,SAAS,OAAO,MAAM,QAAQ,IAAI,KAAK,IAAI,GAAG;AAEpD,gBAAM,gBAAgBD,cAAAA,MAAI,eAAe,QAAQ;AAGjD,eAAK,cAAc,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS;AAE7C,gBAAI,aAAa,KAAK;AACtB,gBAAI,KAAK,kBAAkB,KAAK,mBAAmB,eAAe;AAChE,2BAAa;AAAA,YACf;AAEA,mBAAO;AAAA,cACL,IAAI,KAAK;AAAA,cACT,MAAM,KAAK;AAAA,cACX,OAAO,GAAG,KAAK,YAAY,IAAI,KAAK,SAAS;AAAA,cAC7C,MAAM,KAAK;AAAA,cACX,MAAM,KAAK;AAAA,cACX,QAAQ,KAAK,eAAe,UAAU;AAAA;AAAA,cACtC;AAAA;AAAA,cACA,gBAAgB,KAAK;AAAA;AAAA;UAEzB,CAAC;AAAA,eACI;AACLA,wBAAA,MAAA,MAAA,SAAA,+BAAc,aAAa,GAAG;AAAA,QAChC;AAAA,OACD,EACA,MAAM,CAAC,QAAQ;AACdA,sBAAA,MAAA,MAAA,SAAA,+BAAc,aAAa,GAAG;AAAA,MAChC,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,oBAAoB;AAClBC,gBAAM,QACH,iBAAiB,EACjB,KAAK,CAAC,QAAQ;AACb,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAEhC,eAAK,UAAU,gBAAgB,IAAI,KAAK,SAAS;AAAA,eAC5C;AACLD,wBAAc,MAAA,MAAA,SAAA,+BAAA,gBAAgB,GAAG;AAAA,QACnC;AAAA,OACD,EACA,MAAM,CAAC,QAAQ;AACdA,sBAAA,MAAA,MAAA,SAAA,+BAAc,gBAAgB,GAAG;AAAA,MACnC,CAAC;AAEHC,gBAAM,QACH,sBAAsB,EACtB,KAAK,CAAC,QAAQ;AACb,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAEhC,eAAK,UAAU,gBAAgB,IAAI,KAAK,cAAc;AAAA,eACjD;AACLD,wBAAc,MAAA,MAAA,SAAA,+BAAA,mBAAmB,GAAG;AAAA,QACtC;AAAA,OACD,EACA,MAAM,CAAC,QAAQ;AACdA,0EAAc,mBAAmB,GAAG;AAAA,MACtC,CAAC;AAEHC,gBAAM,QACH,oBAAoB,EACpB,KAAK,CAAC,QAAQ;AACb,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAEhC,eAAK,UAAU,eAAe,IAAI,KAAK,SAAS;AAAA,eAC3C;AACLD,wBAAc,MAAA,MAAA,SAAA,+BAAA,mBAAmB,GAAG;AAAA,QACtC;AAAA,OACD,EACA,MAAM,CAAC,QAAQ;AACdA,0EAAc,mBAAmB,GAAG;AAAA,MACtC,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,eAAe,QAAQ;AACrB,cAAQ,QAAM;AAAA,QACZ,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AAAA,QACL,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACX;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,KAAK;AACTA,oBAAAA,MAAI,WAAW;AAAA,QACb;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,WAAW,KAAK;AACd,WAAK,MAAM,GAAG;AAAA,IACf;AAAA;AAAA,IAGD,kBAAkB;AAChB,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCE,kBAAU,YACP,QAAQ,EACR,KAAK,CAAC,QAAQ;AACbF,wBAAA,MAAA,MAAA,OAAA,+BAAY,cAAc,GAAG;AAC7B,cAAI,MAAM,QAAQ,IAAI,IAAI,GAAG;AAC3B,iBAAK,eAAe,IAAI;AACxB,iBAAK,kBAAkB,KAAK,aAAa,IAAI,CAAC,SAAS,KAAK,IAAI;AAChE,gBAAI,KAAK,aAAa,WAAW,GAAG;AAClC,qBAAO,IAAI,MAAM,SAAS,CAAC;AAAA,mBACtB;AACL;YACF;AAAA,iBACK;AACL,mBAAO,IAAI,MAAM,IAAI,WAAW,WAAW,CAAC;AAAA,UAC9C;AAAA,SACD,EACA,MAAM,CAAC,QAAQ;AACdA,wBAAA,MAAA,MAAA,SAAA,+BAAc,WAAW,GAAG;AAC5B,iBAAO,IAAI,MAAM,gBAAgB,CAAC;AAAA,QACpC,CAAC;AAAA,MACL,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,sBAAsB;AAC1B,UAAI,eAAe;AACnB,UAAI;AACF,uBAAe;AACfA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAGD,cAAM,KAAK;AAGX,cAAM,QAAQ,IAAI,CAAC,KAAK,sBAAqB,GAAI,KAAK,YAAa,CAAA,CAAC;AAGpE,aAAK,MAAM,gBAAgB;MAC3B,SAAO,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,+BAAA,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,UACN,UAAU;AAAA,QACZ,CAAC;AAAA,MACH,UAAU;AACR,YAAI,cAAc;AAChBA,wBAAG,MAAC,YAAW;AAAA,QACjB;AAAA,MACF;AAAA,IACD;AAAA,IAED,iBAAiB,GAAG;AAClB,WAAK,aAAa,EAAE,OAAO;AAAA,IAC5B;AAAA;AAAA,IAGD,wBAAwB;AACtB,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEtC,mBAAW,MAAM;AACf,gBAAM,QAAQ,KAAK,OAAM,IAAK,KAAK,IAAI,QAAQ,CAAC;AAChD,eAAK,cAAc;AACnB,kBAAQ,IAAI;AAAA,QACb,GAAE,GAAG;AAAA,MACR,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,cAAc;AACZ,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,sBAAAA,MAAI,YAAY;AAAA,UACd,MAAM;AAAA,UACN,gBAAgB;AAAA,UAChB,wBAAwB;AAAA,UACxB,SAAS,CAAC,QAAQ;AAChB,iBAAK,WAAW,IAAI;AACpB,iBAAK,YAAY,IAAI;AACrB,oBAAQ,GAAG;AAAA,UACZ;AAAA,UACD,MAAM,CAAC,QAAQ;AACbA,0BAAA,MAAA,MAAA,SAAA,+BAAc,WAAW,GAAG;AAE5B,iBAAK,WAAW;AAChB,iBAAK,YAAY;AAGjB,gBAAI,WAAW;AACf,gBAAI,IAAI,OAAO,SAAS,YAAY,GAAG;AACrC,yBAAW;AAAA,YACb,WAAW,IAAI,OAAO,SAAS,SAAS,GAAG;AACzC,yBAAW;AAAA,YACb;AAEAA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAED,oBAAQ;AAAA,cACN,UAAU,KAAK;AAAA,cACf,WAAW,KAAK;AAAA,YAClB,CAAC;AAAA,UACF;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,cAAc;AACZA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO,IAAI,KAAK,aAAa;AAAA,QAC7B,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,UAAU,OAAO;AAAA,QAC9B,SAAS,CAAC,QAAQ;AAChB,eAAK,eAAe,CAAC,GAAG,KAAK,cAAc,GAAG,IAAI,aAAa;AAAA,QAChE;AAAA,MACH,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,YAAY,OAAO;AACjB,WAAK,aAAa,OAAO,OAAO,CAAC;AAAA,IAClC;AAAA;AAAA,IAGD,mBAAmB;AACjB,WAAK,UAAS;AACd,WAAK,MAAM,gBAAgB;IAC5B;AAAA;AAAA,IAGD,YAAY;AACV,WAAK,gBAAgB;AACrB,WAAK,aAAa;AAClB,WAAK,SAAS;AACd,WAAK,SAAS;AACd,WAAK,aAAa;AAClB,WAAK,eAAe;AACpB,WAAK,eAAe;IACrB;AAAA;AAAA,IAGD,MAAM,YAAY,UAAU;AAC1B,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK;AAAA,UACL;AAAA,UACA,MAAM;AAAA,UACN,QAAQ;AAAA,YACN,eAAe,YAAYA,oBAAI,eAAe,OAAO;AAAA,UACtD;AAAA,UACD,SAAS,CAAC,QAAQ;AAChB,gBAAI;AACF,oBAAM,OAAO,KAAK,MAAM,IAAI,IAAI;AAChC,kBAAI,KAAK,SAAS,KAAK;AACrB,wBAAQ,KAAK,KAAK,GAAG;AAAA,qBAChB;AACL,uBAAO,IAAI,MAAM,KAAK,WAAW,MAAM,CAAC;AAAA,cAC1C;AAAA,YACF,SAAS,GAAG;AACV,qBAAO,IAAI,MAAM,QAAQ,CAAC;AAAA,YAC5B;AAAA,UACD;AAAA,UACD,MAAM,CAAC,QAAQ;AACb,mBAAO,IAAI,MAAM,QAAQ,CAAC;AAAA,UAC3B;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,mBAAmB;AACvB,UAAI;AAEF,YAAI,KAAK,gBAAgB,WAAW,GAAG;AACrC,gBAAM,IAAI,MAAM,YAAY;AAAA,QAC9B;AAEA,cAAM,cAAc;AAAA,UAClB,EAAE,OAAO,KAAK,WAAW,KAAM,GAAE,SAAS,SAAU;AAAA,UACpD,EAAE,OAAO,KAAK,OAAO,KAAM,GAAE,SAAS,SAAU;AAAA,UAChD,EAAE,OAAO,KAAK,OAAO,KAAM,GAAE,SAAS,QAAS;AAAA;AAGjD,mBAAW,cAAc,aAAa;AACpC,cAAI,CAAC,WAAW,OAAO;AACrB,kBAAM,IAAI,MAAM,WAAW,OAAO;AAAA,UACpC;AAAA,QACF;AAEAA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAGD,cAAM,iBAAiB,CAAA;AACvB,YAAI,KAAK,aAAa,SAAS,GAAG;AAChC,cAAI;AACF,uBAAW,YAAY,KAAK,cAAc;AACxC,oBAAM,WAAW,MAAM,KAAK,YAAY,QAAQ;AAChD,6BAAe,KAAK,QAAQ;AAAA,YAC9B;AAAA,UACA,SAAO,OAAO;AACd,kBAAM,IAAI,MAAM,YAAY,MAAM,OAAO;AAAA,UAC3C;AAAA,QACF;AAGA,cAAM,aAAa;AAAA,UACjB,gBAAgB,KAAK,gBAAgB,KAAK,aAAa;AAAA,UACvD,aAAa,KAAK,WAAW,KAAM;AAAA,UACnC,SAAS,KAAK,OAAO,KAAM;AAAA,UAC3B,SAAS,KAAK,OAAO,KAAM;AAAA,UAC3B,aAAa,KAAK;AAAA,UAClB,cAAc,KAAK;AAAA,UACnB,UAAU,KAAK;AAAA,UACf,WAAW,KAAK;AAAA,UAChB,QAAQ;AAAA,UACR,QAAQ,CAAE;AAAA;AAAA,UACV,QAAQ,KAAK,aAAa,KAAM;AAAA,UAChC,gBAAgBA,cAAAA,MAAI,eAAe,QAAQ;AAAA;AAAA;AAI7C,cAAM,WAAW,MAAMG,UAAAA,qBAAqB,OAAO,UAAU;AAE7D,YAAI,SAAS,SAAS,KAAK;AACzBH,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAED,eAAK,UAAS;AACd,eAAK,MAAM,gBAAgB;eACtB;AACL,gBAAM,IAAI,MAAM,SAAS,WAAW,MAAM;AAAA,QAC5C;AAAA,MACA,SAAO,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,+BAAc,SAAS,KAAK;AAE5B,YAAI,WAAW;AAEf,YAAI,MAAM,QAAQ;AAEhB,cAAI,MAAM,OAAO,SAAS,SAAS,GAAG;AACpC,uBAAW;AAAA,UACb,WAAW,MAAM,OAAO,SAAS,SAAS,GAAG;AAC3C,uBAAW;AAAA,UACb;AAAA,mBACS,MAAM,SAAS;AAExB,qBAAW,MAAM;AAAA,QACnB;AAEAA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACZ,CAAC;AAAA,MACH,UAAU;AACRA,sBAAG,MAAC,YAAW;AAAA,MACjB;AAAA,IACD;AAAA,IAED,qBAAqB,GAAG;AACtB,WAAK,gBAAgB,EAAE,OAAO;AAAA,IAC/B;AAAA;AAAA,IAGD,gBAAgB,IAAI;AAClBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,8BAA8B,EAAE;AAAA,MACvC,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,uBAAuB;AACrBI,gBAAQ,UACL,wBAAwB,CAAC,EACzB,KAAK,CAAC,QAAQ;AACb,YAAI,IAAI,SAAS,OAAO,MAAM,QAAQ,IAAI,IAAI,GAAG;AAG/C,eAAK,cAAc,IAAI,KAAK,IAAI,CAAC,SAAS;AACxC,mBAAO;AAAA,cACL,IAAI,KAAK;AAAA,cACT,MAAM,GAAG,KAAK,UAAU;AAAA,cACxB,OAAO,KAAK,YAAY;AAAA,cACxB,MAAM,OAAO,KAAK,gBAAgB,KAAK;AAAA,cACvC,MAAM,KAAK,qBAAqB,KAAK,iBAAiB,KAAK,UAAU;AAAA,cACrE,QAAQ,KAAK,qBAAqB,KAAK,MAAM;AAAA,cAC7C,YAAY,KAAK,oBAAoB,KAAK,MAAM;AAAA;UAEpD,CAAC;AAAA,eACI;AACLJ,wBAAc,MAAA,MAAA,SAAA,+BAAA,eAAe,GAAG;AAAA,QAClC;AAAA,OACD,EACA,MAAM,CAAC,QAAQ;AACdA,sBAAA,MAAA,MAAA,SAAA,+BAAc,eAAe,GAAG;AAAA,MAClC,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,oBAAoB,QAAQ;AAC1B,cAAQ,QAAM;AAAA,QACZ,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO,UAAU;AAAA,MACrB;AAAA,IACD;AAAA;AAAA,IAGD,qBAAqB,QAAQ;AAC3B,cAAQ,QAAM;AAAA,QACZ,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACX;AAAA,IACD;AAAA;AAAA,IAGD,qBAAqB,UAAU;AAC7B,UAAI,CAAC;AAAU,eAAO;AAGtB,UAAI,MAAM,QAAQ,QAAQ,GAAG;AAC3B,YAAI,SAAS,UAAU,GAAG;AACxB,gBAAM,OAAO,SAAS,CAAC;AACvB,gBAAM,QAAQ,OAAO,SAAS,CAAC,CAAC,EAAE,SAAS,GAAG,GAAG;AACjD,gBAAM,MAAM,OAAO,SAAS,CAAC,CAAC,EAAE,SAAS,GAAG,GAAG;AAC/C,iBAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,QAChC;AACA,eAAO,SAAS,KAAK,GAAG;AAAA,MAC1B;AAGA,YAAM,cAAc,OAAO,QAAQ;AAGnC,UAAI,YAAY,SAAS,GAAG,GAAG;AAC7B,cAAM,OAAO,IAAI,KAAK,WAAW;AACjC,cAAM,OAAO,KAAK;AAClB,cAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,cAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,eAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,MAChC;AAGA,UAAI,CAAC,MAAM,QAAQ,KAAK,OAAO,aAAa,UAAU;AACpD,cAAM,OAAO,IAAI,KAAK,QAAQ;AAC9B,cAAM,OAAO,KAAK;AAClB,cAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,cAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,eAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,MAChC;AAGA,UAAI,YAAY,SAAS,GAAG,GAAG;AAC7B,eAAO,YAAY,MAAM,GAAG,EAAE,CAAC;AAAA,MACjC;AAEA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,iBAAiB,IAAI;AACnBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,kCAAkC,EAAE;AAAA,MAC3C,CAAC;AAAA,IACF;AAAA,EACF;AACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5hCA,GAAG,WAAW,eAAe;"}