"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      searchKeyword: "",
      faqs: [
        {
          categoryId: "account",
          groupTitle: "账号和登录",
          items: [
            {
              question: "如何修改密码？",
              answer: "您可以在个人中心 -> 安全设置 -> 密码修改中修改您的账号密码。系统会要求您输入当前密码和新密码进行验证。",
              isOpen: false
            },
            {
              question: "忘记密码怎么办？",
              answer: '如果您忘记了登录密码，请点击登录页面的"忘记密码"按钮，按照提示使用手机号码接收验证码进行密码重置。如果您没有绑定手机号，请联系系统管理员重置密码。',
              isOpen: false
            },
            {
              question: "如何更换手机号？",
              answer: "您可以在个人中心 -> 个人设置 -> 账号绑定中更换您的手机号。系统会向新手机号发送验证码进行验证。",
              isOpen: false
            }
          ]
        },
        {
          categoryId: "workorder",
          groupTitle: "工单处理",
          items: [
            {
              question: "如何接受工单？",
              answer: '工单列表中显示的"待接单"工单，点击进入工单详情后，点击底部的"接单"按钮即可接受工单。接受后，工单状态会变为"已接单"。',
              isOpen: false
            },
            {
              question: "如何完成工单？",
              answer: '在工单详情页面，点击"完成工单"按钮，填写维修内容、维修结果、使用的耗材及数量，选择维修时间并上传相关附件，点击提交即可完成工单。',
              isOpen: false
            },
            {
              question: "如何转派工单？",
              answer: '在工单详情页面，点击"转派工单"按钮，选择要转派的人员，填写转派原因，点击确认即可转派工单。',
              isOpen: false
            },
            {
              question: "如何查看历史工单？",
              answer: '在工单列表页面，可以通过顶部的状态筛选查看不同状态的工单。选择"已完成"标签即可查看历史完成的工单。',
              isOpen: false
            }
          ]
        },
        {
          categoryId: "patrol",
          groupTitle: "巡检管理",
          items: [
            {
              question: "如何开始巡检任务？",
              answer: '在巡检计划列表中，点击"开始巡检"按钮，系统会生成巡检任务单，按照巡检点顺序进行巡检。',
              isOpen: false
            },
            {
              question: "巡检过程中如何报告问题？",
              answer: '在巡检执行过程中，如发现问题，可以点击巡检项旁的"报告问题"按钮，填写问题描述并上传照片，系统会自动生成故障工单。',
              isOpen: false
            },
            {
              question: "如何查看历史巡检记录？",
              answer: "在巡检记录页面，可以查看所有已完成的巡检任务记录，包括巡检时间、巡检人员、巡检路线等信息。",
              isOpen: false
            }
          ]
        },
        {
          categoryId: "system",
          groupTitle: "系统使用",
          items: [
            {
              question: "如何更新APP？",
              answer: '当有新版本时，系统会自动提示更新。您也可以在"我的" -> "关于系统"中检查更新，如有新版本会显示"立即更新"按钮。',
              isOpen: false
            },
            {
              question: "系统支持哪些附件格式？",
              answer: "系统支持上传图片(JPG, PNG, GIF)和视频(MP4, MOV)格式的附件，单个附件大小不超过20MB。",
              isOpen: false
            },
            {
              question: "如何清理缓存？",
              answer: '在"我的" -> "设置" -> "清除缓存"中可以清理应用缓存数据，释放手机存储空间。注意：清除缓存不会删除您的账号信息和历史数据。',
              isOpen: false
            }
          ]
        }
      ],
      // 搜索结果的展开状态
      searchResults: []
    };
  },
  computed: {
    // 所有FAQ分组
    allFaqs() {
      return this.faqs;
    },
    // 搜索结果
    filteredFaqs() {
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase();
        const result = [];
        this.faqs.forEach((group) => {
          group.items.forEach((item) => {
            if (item.question.toLowerCase().includes(keyword) || item.answer.toLowerCase().includes(keyword)) {
              result.push({
                ...item,
                isOpen: false
                // 默认不展开
              });
            }
          });
        });
        this.searchResults = result;
        return result;
      }
      return [];
    }
  },
  methods: {
    // 切换问题答案展示（非搜索模式）
    toggleAnswer(groupIndex, itemIndex) {
      this.faqs[groupIndex].items[itemIndex].isOpen = !this.faqs[groupIndex].items[itemIndex].isOpen;
    },
    // 切换搜索结果中的问题答案展示
    toggleSearchAnswer(index) {
      if (this.searchKeyword && this.filteredFaqs[index]) {
        this.filteredFaqs[index].isOpen = !this.filteredFaqs[index].isOpen;
      }
    },
    // 重置所有问题的展开状态
    resetOpenState() {
      this.faqs.forEach((group) => {
        group.items.forEach((item) => {
          item.isOpen = false;
        });
      });
      if (this.searchResults) {
        this.searchResults.forEach((item) => {
          item.isOpen = false;
        });
      }
    },
    // 搜索FAQ
    searchFaq() {
      this.resetOpenState();
    },
    // 清除搜索
    clearSearch() {
      this.searchKeyword = "";
      this.resetOpenState();
    },
    // 联系客服
    contactSupport() {
      common_vendor.index.showModal({
        title: "联系客服",
        content: "客服热线：029-85396651\n工作时间：周一至周五 9:00-18:00",
        showCancel: false,
        confirmText: "我知道了"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o([($event) => $data.searchKeyword = $event.detail.value, (...args) => $options.searchFaq && $options.searchFaq(...args)]),
    b: $data.searchKeyword,
    c: $data.searchKeyword
  }, $data.searchKeyword ? {
    d: common_vendor.o((...args) => $options.clearSearch && $options.clearSearch(...args))
  } : {}, {
    e: $options.filteredFaqs.length === 0 && $data.searchKeyword
  }, $options.filteredFaqs.length === 0 && $data.searchKeyword ? {
    f: common_assets._imports_0$1
  } : {}, {
    g: $data.searchKeyword
  }, $data.searchKeyword ? {
    h: common_vendor.f($options.filteredFaqs, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.question),
        b: item.isOpen ? 1 : "",
        c: common_vendor.o(($event) => $options.toggleSearchAnswer(index), index),
        d: item.isOpen
      }, item.isOpen ? {
        e: common_vendor.t(item.answer)
      } : {}, {
        f: index
      });
    })
  } : {
    i: common_vendor.f($options.allFaqs, (group, groupIndex, i0) => {
      return {
        a: common_vendor.t(group.groupTitle),
        b: common_vendor.f(group.items, (item, itemIndex, i1) => {
          return common_vendor.e({
            a: common_vendor.t(item.question),
            b: item.isOpen ? 1 : "",
            c: common_vendor.o(($event) => $options.toggleAnswer(groupIndex, itemIndex), itemIndex),
            d: item.isOpen
          }, item.isOpen ? {
            e: common_vendor.t(item.answer)
          } : {}, {
            f: itemIndex
          });
        }),
        c: groupIndex
      };
    })
  }, {
    j: common_vendor.o((...args) => $options.contactSupport && $options.contactSupport(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/user/faq.js.map
