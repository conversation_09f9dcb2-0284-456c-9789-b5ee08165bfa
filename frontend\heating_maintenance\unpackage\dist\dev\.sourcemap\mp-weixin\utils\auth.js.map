{"version": 3, "file": "auth.js", "sources": ["utils/auth.js"], "sourcesContent": ["// utils/auth.js\r\n\r\n/**\r\n * 获取本地存储的用户权限编码列表\r\n * @returns {Array<string>} 用户权限编码列表 (例如: ['user:list', 'user:create', 'dashboard:view'])\r\n *                         如果未找到或格式不正确，则返回空数组。\r\n */\r\nfunction getStoredUserPermissions() {\r\n    const permissions = uni.getStorageSync('userPermissions');\r\n    // 确保返回的是一个数组\r\n    if (Array.isArray(permissions)) {\r\n      return permissions;\r\n    }\r\n    // console.warn('从本地存储获取的用户权限非数组格式:', permissions);\r\n    return [];\r\n  }\r\n\r\n/**\r\n * 获取本地存储的系统权限映射表\r\n * @returns {Object} 权限映射表 (例如: {'user:list': {id:1, name:'用户列表', menu:'用户管理', path:'/pages/user'}})\r\n *                  如果未找到或格式不正确，则返回空对象。\r\n */\r\nfunction getStoredPermissionsMap() {\r\n  const permissionsMap = uni.getStorageSync('permissionsMap');\r\n  // 确保返回的是一个对象\r\n  if (permissionsMap && typeof permissionsMap === 'object') {\r\n    return permissionsMap;\r\n  }\r\n  // console.warn('从本地存储获取的权限映射表非对象格式:', permissionsMap);\r\n  return {};\r\n}\r\n\r\n/**\r\n * 获取用户的认证令牌\r\n * @returns {string} 用户的认证令牌，如果不存在则返回空字符串\r\n */\r\nexport function getToken() {\r\n  return uni.getStorageSync('token') || '';\r\n}\r\n  \r\n/**\r\n * 检查当前登录用户是否拥有特定的权限编码\r\n * @param {string} requiredPermissionCode - 需要检查的权限编码 (例如: 'user:view')\r\n * @returns {boolean} - true 如果用户拥有该权限, false 则表示没有。\r\n */\r\nexport function hasPermission(requiredPermissionCode) {\r\n  // 如果没有提供需要检查的权限码，可以根据业务逻辑决定是允许还是拒绝。\r\n  // 这里我们假设：如果一个操作/页面没有明确要求权限，则默认允许。\r\n  if (!requiredPermissionCode) {\r\n    return true; \r\n  }\r\n  const userPermissions = getStoredUserPermissions();\r\n  return userPermissions.includes(requiredPermissionCode);\r\n}\r\n\r\n/**\r\n * 获取权限相关的详细信息\r\n * @param {string} permissionCode - 权限编码\r\n * @returns {Object|null} 权限详细信息对象，不存在时返回null\r\n */\r\nexport function getPermissionInfo(permissionCode) {\r\n  if (!permissionCode) {\r\n    return null;\r\n  }\r\n  const permissionsMap = getStoredPermissionsMap();\r\n  return permissionsMap[permissionCode] || null;\r\n}\r\n\r\n/**\r\n * 获取当前用户有权限访问的菜单列表\r\n * @returns {Array} 用户有权限的菜单列表，包含菜单名称和路径\r\n */\r\nexport function getAuthorizedMenus() {\r\n  const userPermissions = getStoredUserPermissions();\r\n  const permissionsMap = getStoredPermissionsMap();\r\n  const menuMap = {}; // 用于去重\r\n  \r\n  // 遍历用户拥有的权限编码\r\n  userPermissions.forEach(code => {\r\n    const permInfo = permissionsMap[code];\r\n    if (permInfo) {\r\n      // 使用menuName作为key进行去重\r\n      menuMap[permInfo.menu] = {\r\n        name: permInfo.menu,\r\n        path: permInfo.path\r\n      };\r\n    }\r\n  });\r\n  \r\n  // 转换为数组\r\n  return Object.values(menuMap);\r\n}\r\n  \r\n/**\r\n * 检查用户是否有权访问某个页面或执行某个操作，并在无权限时进行处理。\r\n * @param {string} requiredPermissionCode - 访问页面或执行操作所需的权限编码。\r\n * @param {boolean} [redirectUnauthorized=true] - 如果无权限，是否自动重定向到登录页。\r\n * @returns {boolean} - true 表示有权限, false 表示无权限。\r\n */\r\nexport function checkAccess(requiredPermissionCode, redirectUnauthorized = true) {\r\n  if (!hasPermission(requiredPermissionCode)) {\r\n    const userPermissions = getStoredUserPermissions(); // 获取当前用户权限用于日志\r\n    console.warn(`权限不足: 需要权限 '${requiredPermissionCode}', 用户当前权限: ${JSON.stringify(userPermissions)}`);\r\n    \r\n    uni.showToast({\r\n      title: '您没有足够的权限访问此页面或执行此操作。',\r\n      icon: 'none',\r\n      duration: 2500\r\n    });\r\n\r\n    if (redirectUnauthorized) {\r\n      // 无权限时，可以跳转到登录页、首页或专门的无权限提示页面。\r\n      // 这里我们重定向到登录页。\r\n      setTimeout(() => {\r\n        uni.reLaunch({\r\n          url: '/pages/user/login' // 修改为您的登录页路径\r\n          // 或者跳转到自定义的无权限页面: url: '/pages/common/no-permission'\r\n        });\r\n      }, 2500);\r\n    }\r\n    return false;\r\n  }\r\n  return true;\r\n}\r\n\r\n/**\r\n * 页面访问守卫函数。\r\n * 在页面的 onLoad 生命周期函数中调用，以控制页面访问权限。\r\n * @param {string} requiredPermissionCode - 访问此页面所需的权限编码。\r\n *                                         这个编码应该与您在 `t_sys_permission` 表中定义的 `permission_code` 相对应。\r\n * @returns {boolean} - true 表示允许访问页面, false 表示不允许且已处理跳转。\r\n */\r\nexport function guardPageAccess(requiredPermissionCode) {\r\n  if (!checkAccess(requiredPermissionCode, true)) {\r\n    // checkAccess 函数内部已经处理了提示和跳转逻辑。\r\n    // 返回 false 可以让调用方知道访问被拒绝，以便可能停止后续的页面加载逻辑。\r\n    return false;\r\n  }\r\n  return true;\r\n}"], "names": ["uni"], "mappings": ";;AAOA,SAAS,2BAA2B;AAChC,QAAM,cAAcA,cAAAA,MAAI,eAAe,iBAAiB;AAExD,MAAI,MAAM,QAAQ,WAAW,GAAG;AAC9B,WAAO;AAAA,EACR;AAED,SAAO;AACR;AAqBI,SAAS,WAAW;AACzB,SAAOA,oBAAI,eAAe,OAAO,KAAK;AACxC;AAOO,SAAS,cAAc,wBAAwB;AAGpD,MAAI,CAAC,wBAAwB;AAC3B,WAAO;AAAA,EACR;AACD,QAAM,kBAAkB;AACxB,SAAO,gBAAgB,SAAS,sBAAsB;AACxD;AA8CO,SAAS,YAAY,wBAAwB,uBAAuB,MAAM;AAC/E,MAAI,CAAC,cAAc,sBAAsB,GAAG;AAC1C,UAAM,kBAAkB;AACxBA,kBAAAA,4CAAa,eAAe,sBAAsB,cAAc,KAAK,UAAU,eAAe,CAAC,EAAE;AAEjGA,kBAAAA,MAAI,UAAU;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AAAA,IAChB,CAAK;AAED,QAAI,sBAAsB;AAGxB,iBAAW,MAAM;AACfA,sBAAAA,MAAI,SAAS;AAAA,UACX,KAAK;AAAA;AAAA;AAAA,QAEf,CAAS;AAAA,MACF,GAAE,IAAI;AAAA,IACR;AACD,WAAO;AAAA,EACR;AACD,SAAO;AACT;AASO,SAAS,gBAAgB,wBAAwB;AACtD,MAAI,CAAC,YAAY,wBAAwB,IAAI,GAAG;AAG9C,WAAO;AAAA,EACR;AACD,SAAO;AACT;;;;"}