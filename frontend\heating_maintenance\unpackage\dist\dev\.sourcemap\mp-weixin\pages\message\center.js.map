{"version": 3, "file": "center.js", "sources": ["pages/message/center.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbWVzc2FnZS9jZW50ZXIudnVl"], "sourcesContent": ["<template>\n  <BaseTabBar>\n    <view class=\"message-center-container\">\n      <!-- 消息分类 -->\n      <view class=\"message-tabs\">\n        <!-- 遍历消息标签，使用PermissionCheck组件根据权限控制显示 -->\n        <template v-for=\"(tab, index) in messageTabs\">\n          <!-- 对于没有设置权限的标签，直接显示 -->\n          <view\n            v-if=\"!tab.permissions\"\n            :key=\"'tab-' + index\"\n            class=\"message-tab\"\n            :class=\"{ active: currentTab === tab.value }\"\n            @click=\"switchTab(tab.value)\"\n          >\n            {{ tab.label }}\n            <text class=\"badge\" v-if=\"tab.count > 0\">{{ tab.count }}</text>\n          </view>\n          \n          <!-- 对于设置了权限的标签，使用PermissionCheck进行权限控制 -->\n          <PermissionCheck v-else :permission=\"tab.permissions\" :key=\"'perm-tab-' + index\">\n            <view\n              class=\"message-tab\"\n              :class=\"{ active: currentTab === tab.value }\"\n              @click=\"switchTab(tab.value)\"\n            >\n              {{ tab.label }}\n              <text class=\"badge\" v-if=\"tab.count > 0\">{{ tab.count }}</text>\n            </view>\n          </PermissionCheck>\n        </template>\n      </view>\n\n      <!-- 批量操作工具栏 -->\n  <!--    <view class=\"action-toolbar\">\n        <template v-if=\"currentTab === 'all'\">\n          <view class=\"action-filter\" @click=\"toggleTimeFilterPanel\">\n            <text>按时间</text>\n            <text class=\"action-arrow\">▼</text>\n          </view>\n          <view class=\"action-filter\" @click=\"toggleFilterPanel\">\n            <text>按紧急程度</text>\n            <text class=\"action-arrow\">▼</text>\n          </view>\n        </template>\n        <template v-else>\n          <template v-if=\"!getTabByValue(currentTab)?.permissions || hasPermission(getTabByValue(currentTab).permissions)\">\n          <view class=\"action-btn\" @click=\"markAllRead\">全部已读</view>\n           <view class=\"action-btn\" @click=\"deleteSelected\">删除</view>\n\t\t\t<view class=\"action-btn\"></view>\n            <view class=\"action-filter\" @click=\"toggleFilterPanel\">\n              <text>按紧急程度</text>\n              <text class=\"action-arrow\">▼</text>\n            </view>\n          </template>\n        </template>\n      </view -->\n\n      <!-- 筛选面板 -->\n      <view class=\"filter-panel\" v-if=\"showFilterPanel\">\n        <view\n          class=\"filter-item\"\n          v-for=\"(item, index) in priorityOptions\"\n          :key=\"index\"\n          :class=\"{ active: selectedPriority === item.value }\"\n          @click=\"selectPriority(item.value)\"\n        >\n          {{ item.label }}\n        </view>\n      </view>\n\n      <!-- 时间筛选面板 -->\n      <view class=\"filter-panel\" v-if=\"showTimeFilterPanel\">\n        <view\n          class=\"filter-item\"\n          v-for=\"(item, index) in timeFilterOptions\"\n          :key=\"index\"\n          :class=\"{ active: selectedTimeFilter === item.value }\"\n          @click=\"selectTimeFilter(item.value)\"\n        >\n          {{ item.label }}\n        </view>\n      </view>\n\n      <!-- 消息列表 -->\n      <view class=\"message-list\">\n        <view\n          class=\"message-card\"\n          v-for=\"message in filteredMessages\"\n          :key=\"message.id\"\n          :class=\"{ unread: !message.read, [message.priority]: true }\"\n          @click=\"viewMessageDetail(message)\"\n        >\n          <view class=\"message-header\">\n            <view class=\"message-title\">\n              <text class=\"priority-dot\" :class=\"message.priority\"></text>\n              <text>{{ message.title }}</text>\n            </view>\n            <view class=\"message-time\">{{ message.time }}</view>\n          </view>\n          <view class=\"message-content\">{{ message.content }}</view>\n          <view class=\"message-footer\">\n            <view class=\"message-type\">{{ getTypeText(message.type) }}</view>\n            <view class=\"message-actions\">\n              <template v-if=\"message.type === 'alarm'\">\n                <view\n                  class=\"action-item ignore-btn\"\n                  @click.stop=\"ignoreAlarm(message.id)\"\n                >\n                  忽略\n                </view>\n              </template>\n              <template v-else>\n                <view class=\"action-item\" @click.stop=\"viewMessageDetail(message)\">\n                  查看\n                </view>\n              </template>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 空状态 -->\n      <view class=\"empty-state\" v-if=\"filteredMessages.length === 0\">\n        <image class=\"empty-icon\" src=\"/static/icons/empty.png\"></image>\n        <text class=\"empty-text\">暂无消息</text>\n      </view>\n\n      <!-- 加载更多 -->\n      <view\n        class=\"load-more\"\n        v-if=\"hasMore && filteredMessages.length > 0\"\n        @click=\"loadMore\"\n        >加载更多</view\n      >\n      <view class=\"no-more\" v-if=\"!hasMore && filteredMessages.length > 0\"\n        >没有更多消息了</view\n      >\n    </view>\n  </BaseTabBar>\n</template>\n\n<script>\nimport { alarmApi, heatUnitApi, faultApi, patrolApi, workOrderApi } from \"@/utils/api.js\";\nimport BaseTabBar from \"@/components/BaseTabBar.vue\";\nimport PermissionCheck from \"@/components/PermissionCheck.vue\"; // 导入权限检查组件\nimport { hasPermission } from \"@/utils/auth.js\"; // 导入权限检查函数\n\nexport default {\n  components: {\n    BaseTabBar,\n\tPermissionCheck\n  },\n  data() {\n    return {\n      currentTab: \"all\",\n      showFilterPanel: false,\n      selectedPriority: \"all\",\n\n      // 新增时间筛选相关数据\n      showTimeFilterPanel: false,\n      selectedTimeFilter: \"all\",\n      timeFilterOptions: [\n        { label: \"全部时间\", value: \"all\" },\n        { label: \"今天\", value: \"today\" },\n        { label: \"昨天\", value: \"yesterday\" },\n        { label: \"本周\", value: \"thisWeek\" },\n        { label: \"本月\", value: \"thisMonth\" },\n      ],\n\n      messageTabs: [\n        { label: \"全部\", value: \"all\", count: 0 ,permissions:\"\"}, // 全部消息不需要特定权限\n        { label: \"巡检\", value: \"inspection\", count: 0 ,permissions:\"message:patrol-message\"}, // 需要巡检记录权限\n        { label: \"维修\", value: \"workOrder\", count: 0 ,permissions:\"message:workorder-message\"}, // 需要工单列表权限\n        { label: \"告警\", value: \"alarm\", count: 0 ,permissions:\"message:alarm-message\"}, // 需要告警列表权限\n        { label: \"故障\", value: \"fault\", count: 0 ,permissions:\"message:fault-message\"}, // 需要故障列表权限\n        { label: \"系统\", value: \"system\", count: 0 ,permissions:\"message:system-message\"}, // 系统消息不需要特定权限\n      ],\n\n      priorityOptions: [\n        { label: \"全部\", value: \"all\" },\n        { label: \"紧急\", value: \"urgent\" },\n        { label: \"重要\", value: \"important\" },\n        { label: \"常规\", value: \"normal\" },\n      ],\n\n      messages: [],\n\n      page: 1,\n      pageSize: 10,\n      hasMore: false,\n      isLoading: false,\n    };\n  },\n  computed: {\n    filteredMessages() {\n      let result = [...this.messages];\n\n      // 首先根据权限过滤消息类型\n      result = result.filter(msg => {\n        // 如果是全部标签，则需要检查每种消息类型的权限\n        if (this.currentTab === 'all') {\n          // 系统消息和没有权限要求的消息类型直接显示\n          if (msg.type === 'system' || !this.getTabByValue(msg.type)?.permissions) {\n            return true; // 系统消息和无权限要求的消息类型总是显示\n          }\n          // 检查用户是否有查看该类型消息的权限\n          const tab = this.getTabByValue(msg.type);\n          return tab && (!tab.permissions || this.hasPermission(tab.permissions)); // 有权限才显示\n        } else {\n          // 当前已选中特定标签，已经通过UI层面的权限控制，无需再次过滤\n          return msg.type === this.currentTab; // 当前标签的消息\n        }\n      });\n\n      // 按优先级过滤\n      if (this.selectedPriority !== \"all\") {\n        result = result.filter((msg) => msg.priority === this.selectedPriority);\n      }\n\n      // 添加时间筛选逻辑\n      if (this.selectedTimeFilter !== \"all\") {\n        const now = new Date();\n        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n        const yesterday = new Date(today);\n        yesterday.setDate(yesterday.getDate() - 1);\n\n        const startOfWeek = new Date(today);\n        startOfWeek.setDate(today.getDate() - today.getDay()); // 从周日开始\n\n        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);\n\n        result = result.filter((msg) => {\n          const messageDate = new Date(\n            msg.time.replace(/(\\d{4}-\\d{2}-\\d{2}) (\\d{2}:\\d{2})/, \"$1T$2:00\")\n          );\n\n          switch (this.selectedTimeFilter) {\n            case \"today\":\n              return messageDate >= today;\n            case \"yesterday\":\n              return messageDate >= yesterday && messageDate < today;\n            case \"thisWeek\":\n              return messageDate >= startOfWeek;\n            case \"thisMonth\":\n              return messageDate >= startOfMonth;\n            default:\n              return true;\n          }\n        });\n      }\n\n      result.sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime());\n\n      return result;\n    },\n  },\n  onLoad() {\n    uni.$on(\"newMessageReceived\", this.handleNewMessages);\n    \n    // 检查用户是否有权限查看默认标签\n    const defaultTab = this.currentTab;\n    const tabConfig = this.getTabByValue(defaultTab);\n    \n    // 如果默认标签需要权限且用户没有该权限，则切换到用户有权限的标签\n    if (tabConfig && tabConfig.permissions && !this.hasPermission(tabConfig.permissions)) {\n      console.log(`用户无权限查看默认标签 ${defaultTab}，尝试切换到有权限的标签`);\n      \n      // 查找用户有权限的第一个标签\n      const permittedTab = this.messageTabs.find(tab => !tab.permissions || this.hasPermission(tab.permissions));\n      \n      if (permittedTab) {\n        console.log(`自动切换到标签: ${permittedTab.value}`);\n        this.currentTab = permittedTab.value;\n      }\n    }\n    \n    this.loadInitialMessages();\n  },\n  onShow() {\n    //清理缓存数据\n    this.messages = [];\n    // 通过 api.js 获取消息\n    this.fetchAllMessages();\n    this.updateAllTabCounts();\n  },\n  onUnload() {\n    uni.$off(\"newMessageReceived\", this.handleNewMessages);\n  },\n  methods: {\n    // 根据值获取对应的标签配置\n    getTabByValue(value) {\n      return this.messageTabs.find(tab => tab.value === value);\n    },\n    \n    // 检查是否有权限\n    hasPermission(permission) {\n      return hasPermission(permission);\n    },\n\n    handleNewMessages(payload) {\n      console.log(\"消息中心：收到新消息事件\", payload);\n      const { type, data } = payload;\n      \n      // 获取前端消息类型\n      const frontendType = this.mapBackendChineseTypeToFrontend(type);\n      \n      // 检查用户是否有权限查看此类消息\n      const tab = this.getTabByValue(frontendType);\n      if (tab && tab.permissions && !this.hasPermission(tab.permissions)) {\n        console.log(`消息中心：用户无权限查看 ${type} 类型消息，已忽略`);\n        return; // 无权限则不处理这类消息\n      }\n\n      if (data && Array.isArray(data) && data.length > 0) {\n        const newMessages = data\n          .map((msg) => this.transformMessageFormat(msg, type))\n          .filter((msg) => msg !== null);\n\n        const existingIds = new Set(this.messages.map((m) => m.id));\n        const uniqueNewMessages = newMessages.filter((nm) => !existingIds.has(nm.id));\n\n        if (uniqueNewMessages.length > 0) {\n          // 添加消息到列表\n          this.messages.unshift(...uniqueNewMessages);\n          this.updateTabCounts(type, uniqueNewMessages.length);\n          uni.showToast({\n            title: `收到 ${\n              uniqueNewMessages.length\n            } 条新的${this.mapBackendChineseTypeToFrontend(type)}消息`,\n            icon: \"none\",\n          });\n        }\n      }\n    },\n\n    transformMessageFormat(msg, backendChineseType) {\n      if (!msg || !msg.id) return null;\n\n      let frontendType = this.mapBackendChineseTypeToFrontend(backendChineseType);\n      let title = `新${backendChineseType}消息`;\n      let content = \"详情请点击查看\";\n      let priority = \"normal\";\n      let timeValue = null;\n      let heatUnitName = \"\";\n      let heatUnitId = -1;\n      let alarmId = -1;\n      let faultLevel = \"一般\";\n\n      switch (backendChineseType) {\n        case \"巡检\":\n          title = msg.name || title;\n          content = `请执行巡检任务: ${msg.name || \"未指定\"}`;\n          priority = \"normal\";\n          break;\n        case \"告警\":\n          title = msg.heatUnitName;\n          content = msg.alarmDesc || content;\n          timeValue = msg.alarmDt;\n          priority = \"urgent\";\n          heatUnitName = msg.heatUnitName || \"\";\n          heatUnitId = msg.heatUnitId;\n          alarmId = msg.id;\n          faultLevel = msg.faultLevels || \"一般\";\n          break;\n        case \"故障\":\n          title = msg.heatUnitName;\n          content = msg.faultDesc || content;\n          timeValue = msg.occurTime;\n          priority = \"important\";\n          heatUnitName = msg.heatUnitName || \"\";\n          faultLevel = msg.faultLevel || \"一般\";\n          break;\n        case \"维修\":\n          title = `${msg.heatUnitName}`;\n          content = msg.fault_desc\n            ? `来源[${msg.fault_source || \"未知\"}]：${msg.fault_desc}`\n            : content;\n          timeValue = msg.createdTime;\n          priority = \"important\";\n          heatUnitName = msg.heatUnitName || \"\";\n          faultLevel = msg.faultLevel || \"一般\";\n          break;\n      }\n\n      let timeString = \"\";\n      if (typeof timeValue === \"string\") {\n        timeString = timeValue;\n      } else if (timeValue instanceof Date) {\n        timeString = timeValue.toISOString();\n      } else if (Array.isArray(timeValue) && timeValue.length >= 5) {\n        try {\n          const [year, month, day, hour, minute] = timeValue;\n          const isoMonth = String(month).padStart(2, \"0\");\n          const isoDay = String(day).padStart(2, \"0\");\n          const isoHour = String(hour).padStart(2, \"0\");\n          const isoMinute = String(minute).padStart(2, \"0\");\n          const isoSeconds =\n            timeValue.length > 5 ? String(timeValue[5]).padStart(2, \"0\") : \"00\";\n          timeString = `${year}-${isoMonth}-${isoDay}T${isoHour}:${isoMinute}:${isoSeconds}`;\n        } catch (e) {\n          console.error(\"消息中心：处理时间数组时出错:\", timeValue, e);\n          timeString = new Date().toISOString();\n        }\n      } else if (timeValue) {\n        timeString = String(timeValue);\n      } else {\n        timeString = new Date().toISOString();\n        console.warn(`消息 [${frontendType}-${msg.id}] 缺少有效时间，使用当前时间。`);\n      }\n\n      return {\n        id: `${frontendType}-${msg.id}`,\n        title: title,\n        content: content,\n        time: timeString.replace(\"T\", \" \").substring(0, 16),\n        type: frontendType,\n        priority: priority,\n        read: false,\n        relatedId: msg.id,\n        heatUnitName: heatUnitName,\n        heatUnitId: heatUnitId,\n        alarmId: alarmId,\n        faultLevel: faultLevel,\n      };\n    },\n\n    mapBackendChineseTypeToFrontend(backendType) {\n      const map = {\n        巡检: \"inspection\",\n        告警: \"alarm\",\n        故障: \"fault\",\n        维修: \"workOrder\",\n      };\n      return map[backendType] || \"system\";\n    },\n\n    updateTabCounts(backendChineseType, countIncrement) {\n      const frontendType = this.mapBackendChineseTypeToFrontend(backendChineseType);\n      const tab = this.messageTabs.find((t) => t.value === frontendType);\n      if (tab) {\n        tab.count = (tab.count || 0) + countIncrement;\n      }\n      const allTab = this.messageTabs.find((t) => t.value === \"all\");\n      if (allTab) {\n        allTab.count = (allTab.count || 0) + countIncrement;\n      }\n    },\n\n    updateAllTabCounts() {\n      const counts = {\n        all: 0,\n        inspection: 0,\n        workOrder: 0,\n        alarm: 0,\n        fault: 0,\n        system: 0,\n      };\n      \n      // 首先过滤出用户有权限查看的消息类型\n      const permittedTypes = this.messageTabs\n        .filter(tab => !tab.permissions || this.hasPermission(tab.permissions))\n        .map(tab => tab.value);\n      \n      // 添加系统类型（无需权限）\n      if (!permittedTypes.includes('system')) {\n        permittedTypes.push('system');\n      }\n      \n      // 统计各类型消息数量\n      this.messages.forEach((msg) => {\n        // 只统计用户有权限查看的消息类型\n        if (permittedTypes.includes(msg.type)) {\n          counts.all++;\n          if (counts[msg.type] !== undefined) {\n            counts[msg.type]++;\n          } else {\n            counts.system++;\n          }\n        }\n      });\n      \n      // 更新标签计数\n      this.messageTabs.forEach((tab) => {\n        // 只更新用户有权限查看的标签计数\n        if (!tab.permissions || this.hasPermission(tab.permissions)) {\n          tab.count = counts[tab.value] || 0;\n        } else {\n          tab.count = 0; // 无权限的标签计数设为0\n        }\n      });\n    },\n\n    loadInitialMessages() {\n      if (this.isLoading) return;\n      this.isLoading = true;\n      console.log(\"加载初始消息列表 - 第1页\");\n\n      this.messages = [];\n      this.selectedPriority = \"all\";\n      this.selectedTimeFilter = \"all\";\n      this.updateAllTabCounts();\n      this.isLoading = false;\n    },\n\n    loadMore() {\n      if (this.isLoading || !this.hasMore) return;\n      this.page++;\n      this.isLoading = true;\n      console.log(`加载更多消息 - 第${this.page}页`);\n\n      setTimeout(() => {\n        this.hasMore = false;\n        this.isLoading = false;\n      }, 500);\n    },\n\n    switchTab(tab) {\n      // 检查是否有权限切换到该标签\n      const tabConfig = this.getTabByValue(tab);\n      \n      // 如果标签需要权限且用户没有该权限，则不允许切换\n      if (tabConfig && tabConfig.permissions && !this.hasPermission(tabConfig.permissions)) {\n        uni.showToast({\n          title: '您没有权限查看此类消息',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      this.currentTab = tab;\n      console.log(`切换到标签: ${tab}`);\n\n      // Reset filters\n      this.showFilterPanel = false;\n      this.showTimeFilterPanel = false;\n\n      // Optionally reset filter values when switching tabs\n      // this.selectedPriority = 'all';\n      // this.selectedTimeFilter = 'all';\n    },\n\n    toggleFilterPanel() {\n      this.showFilterPanel = !this.showFilterPanel;\n      if (this.showFilterPanel) {\n        this.showTimeFilterPanel = false;\n      }\n    },\n\n    toggleTimeFilterPanel() {\n      this.showTimeFilterPanel = !this.showTimeFilterPanel;\n      if (this.showTimeFilterPanel) {\n        this.showFilterPanel = false;\n      }\n    },\n\n    selectPriority(priority) {\n      this.selectedPriority = priority;\n      this.showFilterPanel = false;\n    },\n\n    selectTimeFilter(timeFilter) {\n      this.selectedTimeFilter = timeFilter;\n      this.showTimeFilterPanel = false;\n    },\n\n    markAllRead() {\n      this.messages.forEach((msg) => {\n        msg.read = true;\n      });\n\n      uni.showToast({\n        title: \"已全部标为已读\",\n        icon: \"success\",\n      });\n    },\n\n    deleteSelected() {\n      uni.showModal({\n        title: \"确认操作\",\n        content: \"确定要删除当前筛选出的所有消息吗？\",\n        success: (res) => {\n          if (res.confirm) {\n            const idsToDelete = this.filteredMessages.map((m) => m.id);\n            this.messages = this.messages.filter((msg) => !idsToDelete.includes(msg.id));\n\n            this.updateAllTabCounts();\n\n            uni.showToast({\n              title: \"删除成功\",\n              icon: \"success\",\n            });\n          }\n        },\n      });\n    },\n\n    viewMessageDetail(message) {\n      if (!message) return;\n\n      if (!message.read && message.type !== \"alarm\") {\n        message.read = true;\n      }\n\n      if (message.type === \"alarm\") {\n        this.showAlarmConfirmation(message);\n      } else {\n        let targetUrl = \"\";\n        const relatedId = message.relatedId || \"\";\n\n        switch (message.type) {\n          case \"fault\":\n            targetUrl = `/pages/fault/detail?id=${relatedId}`;\n            break;\n          case \"workOrder\":\n            targetUrl = `/pages/workorder/detail?id=${relatedId}`;\n            break;\n          case \"inspection\":\n            targetUrl = `/pages/patrol/record_detail?id=${relatedId}`;\n            break;\n        }\n\n        if (targetUrl && relatedId) {\n          console.log(`导航到: ${targetUrl}`);\n          uni.navigateTo({\n            url: targetUrl,\n            fail: (err) => {\n              console.error(`导航失败: ${JSON.stringify(err)}`);\n              uni.showToast({ title: \"页面跳转失败\", icon: \"none\" });\n            },\n          });\n        } else {\n          console.log(\"无明确跳转目标或缺少 relatedId，显示 Modal\", message);\n          uni.showModal({\n            title: message.title,\n            content: message.content,\n            showCancel: false,\n          });\n        }\n      }\n    },\n\n    showAlarmConfirmation(message) {\n      uni.showModal({\n        title: \"告警确认\",\n        content: `检测到告警：\\n${message.content}\\n\\n是否确认此告警并直接上报故障？`,\n        confirmText: \"确认上报\",\n        cancelText: \"取消\",\n        success: async (res) => {\n          if (res.confirm) {\n            uni.showLoading({ title: \"处理中...\" });\n            let heatUnitId = null;\n            let reportUserId = uni.getStorageSync(\"userId\") || null; // 尝试从缓存获取用户ID\n            if (!reportUserId) {\n              uni.hideLoading();\n              uni.showToast({ title: \"无法获取用户信息，请重新登录\", icon: \"none\" });\n              return;\n            }\n\n            try {\n              // 第一步：准备并调用故障上报接口\n              const reportData = {\n                heat_unit_id: message.heatUnitId, // 可能为 null\n                alarm_id: message.alarmId,\n                fault_type: \"设备故障\", // 固定值\n                fault_level: message.faultLevel || \"一般\", // 使用告警等级\n                fault_desc: message.content, // 使用告警描述\n                fault_source: \"系统检测\", // 固定值\n                occur_time: message.time ? `${message.time}:00` : null, // 格式化时间，确保秒存在\n                report_user_id: reportUserId,\n                attachment: [], // 无附件\n              };\n              console.log(\"准备直接上报故障数据:\", reportData);\n              const reportRes = await faultApi.reportFault(reportData);\n              uni.hideLoading();\n              if (reportRes.code === 200) {\n                uni.showToast({ title: \"故障已成功上报\", icon: \"success\" });\n\n                // 第二步：确认告警状态\n                const alarmStatusRes = await alarmApi.updateAlarmStatus(\n                  message.relatedId,\n                  1\n                ); // 1: 已确认\n                if (alarmStatusRes.code !== 200) {\n                  throw new Error(alarmStatusRes.message || \"确认告警状态失败\");\n                }\n                console.log(`告警 ${message.relatedId} 状态已更新为已确认`);\n\n                // 可选：上报成功后从消息列表移除该告警\n                const index = this.messages.findIndex((m) => m.id === message.id);\n                if (index > -1) {\n                  this.messages.splice(index, 1);\n                  this.updateAllTabCounts();\n                }\n              } else {\n                throw new Error(reportRes.message || \"故障上报失败\");\n              }\n            } catch (error) {\n              uni.hideLoading();\n              console.error(\"确认告警并上报故障过程中出错:\", error);\n              uni.showToast({ title: error.message || \"操作失败，请重试\", icon: \"none\" });\n            }\n          } // 点击了确认\n        },\n      });\n    },\n\n    ignoreAlarm(id) {\n      const message = this.messages.find((msg) => msg.id === id);\n      if (!message || message.type !== \"alarm\") return;\n\n      uni.showModal({\n        title: \"确认忽略\",\n        content: \"确定要忽略这条告警信息吗？忽略后将不再提醒。\",\n        success: (res) => {\n          if (res.confirm) {\n            console.log(\n              `准备忽略告警，前端消息ID: ${id}, 后端告警ID: ${message.relatedId}`\n            );\n            uni.showLoading({ title: \"处理中...\" });\n            alarmApi\n              .updateAlarmStatus(message.relatedId, 3)\n              .then((apiRes) => {\n                uni.hideLoading();\n                if (apiRes.code === 200) {\n                  uni.showToast({ title: \"告警已忽略\", icon: \"success\" });\n                  const index = this.messages.findIndex((m) => m.id === id);\n                  if (index > -1) {\n                    this.messages.splice(index, 1);\n                    this.updateAllTabCounts();\n                  }\n                } else {\n                  uni.showToast({ title: apiRes.message || \"忽略失败\", icon: \"none\" });\n                }\n              })\n              .catch((err) => {\n                uni.hideLoading();\n                console.error(\"忽略告警API调用失败:\", err);\n                uni.showToast({ title: \"操作失败，请重试\", icon: \"none\" });\n              });\n          }\n        },\n      });\n    },\n\n    getTypeText(type) {\n      const typeMap = {\n        alarm: \"告警\",\n        workOrder: \"维修\",\n        inspection: \"巡检\",\n        fault: \"故障\",\n        system: \"系统\",\n      };\n      return typeMap[type] || \"其他\";\n    },\n\n    async fetchAllMessages() {\n      console.log(\"[MessageCenter] Fetching all messages onShow...\");\n      uni.showLoading({ title: \"加载中...\" });\n\n      try {\n        // 获取用户ID和角色\n        const userId = uni.getStorageSync(\"userId\");\n        const userRole = uni.getStorageSync(\"userRole\");\n        \n        console.log(`[MessageCenter] 当前用户ID: ${userId}, 角色: ${userRole}`);\n        \n        if (!userId) {\n          console.warn(\"[MessageCenter] 未获取到用户ID，可能需要重新登录\");\n        }\n\n        // 定义消息获取器，包含API方法、消息类型和权限要求\n        const messageFetchers = [\n          { \n            api: () => patrolApi.getPatrolPlansMessages(userId, userRole), // 修改为传递userId和userRole参数\n            type: \"巡检\", \n            backendTypeKey: \"巡检\",\n            permission: \"message:patrol-message\" // 修改为正确的巡检消息权限\n          },\n          { \n            api: () => alarmApi.getAlarmList(userId, userRole), // 修改为传递userId和userRole参数\n            type: \"告警\", \n            backendTypeKey: \"告警\",\n            permission: \"message:alarm-message\" // 告警消息权限\n          },\n          { \n            api: () => faultApi.getFaultMessages(userId, userRole), // 修改为传递userId和userRole参数\n            type: \"故障\", \n            backendTypeKey: \"故障\",\n            permission: \"message:fault-message\" // 修改为正确的故障消息权限\n          },\n          {\n            api: () => workOrderApi.getWorkOrderMessages(userId, userRole), // 修改为传递userId和userRole参数\n            type: \"维修\",\n            backendTypeKey: \"维修\",\n            permission: \"message:workorder-message\" // 修改为正确的工单消息权限\n          },\n        ];\n\n        let allNewMessages = [];\n\n        for (const fetcher of messageFetchers) {\n          try {\n            // 检查用户是否有权限获取此类消息\n            if (fetcher.permission && !this.hasPermission(fetcher.permission)) {\n              console.log(`[MessageCenter] 用户无权限获取 ${fetcher.type} 消息，已跳过`);\n              continue; // 无权限则跳过此类消息的获取\n            }\n            \n            console.log(`[MessageCenter] Fetching ${fetcher.type} messages...`);\n            // 对于需要传递参数的API调用，使用函数调用方式\n            const res = typeof fetcher.api === 'function' ? await fetcher.api() : await fetcher.api();\n            console.log(\n              `[MessageCenter] Fetched ${fetcher.type} messages response:`,\n              res\n            );\n            if (res && res.code === 200 && res.data && Array.isArray(res.data)) {\n              const transformed = res.data\n                .map((msg) => this.transformMessageFormat(msg, fetcher.backendTypeKey))\n                .filter((msg) => msg !== null);\n              allNewMessages.push(...transformed);\n              console.log(\n                `[MessageCenter] Transformed ${fetcher.type} messages:`,\n                transformed.length\n              );\n            } else {\n              console.warn(\n                `[MessageCenter] Failed to fetch or no data for ${fetcher.type} messages:`,\n                res\n              );\n            }\n          } catch (error) {\n            console.error(\n              `[MessageCenter] Error fetching ${fetcher.type} messages:`,\n              error\n            );\n          }\n        }\n\n        if (allNewMessages.length > 0) {\n          const existingIds = new Set(this.messages.map((m) => m.id));\n          const uniqueNewMessages = allNewMessages.filter(\n            (nm) => !existingIds.has(nm.id)\n          );\n\n          if (uniqueNewMessages.length > 0) {\n            this.messages.unshift(...uniqueNewMessages);\n            // Sort messages by time after adding new ones\n            this.messages.sort(\n              (a, b) => new Date(b.time).getTime() - new Date(a.time).getTime()\n            );\n            console.log(\n              `[MessageCenter] Added ${uniqueNewMessages.length} new unique messages to the list.`\n            );\n            uni.showToast({\n              title: `加载了 ${uniqueNewMessages.length} 条新消息`,\n              icon: \"none\",\n            });\n          } else {\n            console.log(\"[MessageCenter] No new unique messages to add.\");\n          }\n        } else {\n          console.log(\"[MessageCenter] No messages fetched from APIs.\");\n        }\n      } catch (error) {\n        console.error(\"[MessageCenter] Error in fetchAllMessages:\", error);\n        uni.showToast({ title: \"加载消息失败\", icon: \"none\" });\n      } finally {\n        this.updateAllTabCounts();\n        this.isLoading = false;\n        uni.hideLoading();\n        console.log(\"[MessageCenter] fetchAllMessages completed.\");\n      }\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\">\n.message-center-container {\n  padding: 0rpx 20rpx 20rpx 20rpx;\n  box-sizing: border-box;\n}\n\n.message-tabs {\n  display: flex;\n  background-color: #fff;\n  // border-radius: 8rpx;\n  margin-bottom: 20rpx;\n  overflow-x: auto;\n  white-space: nowrap;\n  position: sticky;\n  top: 0rpx;\n  z-index: 999;\n  \n  .message-tab {\n    flex: 1;\n    min-width: 120rpx;\n    text-align: center;\n    padding: 20rpx 0;\n    font-size: 28rpx;\n    position: relative;\n\n    .badge {\n      position: absolute;\n      top: 10rpx;\n      right: 10rpx;\n      background-color: $uni-color-error;\n      color: #fff;\n      font-size: 20rpx;\n      border-radius: 20rpx;\n      padding: 2rpx 10rpx;\n      min-width: 20rpx;\n      height: 20rpx;\n      line-height: 20rpx;\n      text-align: center;\n    }\n\n    &.active {\n      color: $uni-color-primary;\n      font-weight: bold;\n\n      &::after {\n        content: \"\";\n        position: absolute;\n        bottom: 0;\n        left: 50%;\n        transform: translateX(-50%);\n        width: 40rpx;\n        height: 4rpx;\n        background-color: $uni-color-primary;\n      }\n    }\n  }\n}\n\n.action-toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20rpx;\n  background-color: #fff;\n  border-radius: 8rpx;\n  padding: 16rpx 20rpx;\n\n  .action-btn {\n    font-size: 26rpx;\n    color: $uni-color-primary;\n    padding: 6rpx 16rpx;\n  }\n\n  .action-filter {\n    display: flex;\n    align-items: center;\n    font-size: 26rpx;\n    color: $uni-text-color;\n\n    .action-arrow {\n      margin-left: 10rpx;\n      font-size: 24rpx;\n    }\n  }\n}\n\n.filter-panel {\n  background-color: #fff;\n  border-radius: 8rpx;\n  padding: 20rpx;\n  margin-bottom: 20rpx;\n\n  .filter-item {\n    padding: 16rpx 0;\n    font-size: 28rpx;\n    border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);\n\n    &:last-child {\n      border-bottom: none;\n    }\n\n    &.active {\n      color: $uni-color-primary;\n    }\n  }\n}\n\n.message-list {\n\t\n  .message-card {\n    background-color: #fff;\n    border-radius: 8rpx;\n    padding: 24rpx;\n    margin-bottom: 20rpx;\n    border-left: 6rpx solid transparent;\n\n    &.unread {\n      background-color: rgba(24, 144, 255, 0.05);\n    }\n\n    &.urgent {\n      border-left-color: $uni-color-error;\n    }\n\n    &.important {\n      border-left-color: $uni-color-warning;\n    }\n\n    &.normal {\n      border-left-color: $uni-color-primary;\n    }\n\n    .message-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 16rpx;\n\n      .message-title {\n        font-size: 32rpx;\n        font-weight: bold;\n        display: flex;\n        align-items: center;\n\n        .priority-dot {\n          width: 16rpx;\n          height: 16rpx;\n          border-radius: 50%;\n          margin-right: 12rpx;\n\n          &.urgent {\n            background-color: $uni-color-error;\n          }\n\n          &.important {\n            background-color: $uni-color-warning;\n          }\n\n          &.normal {\n            background-color: $uni-color-primary;\n          }\n        }\n      }\n\n      .message-time {\n        font-size: 24rpx;\n        color: $uni-text-color-grey;\n      }\n    }\n\n    .message-content {\n      font-size: 28rpx;\n      line-height: 1.5;\n      margin-bottom: 16rpx;\n    }\n\n    .message-footer {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n\n      .message-type {\n        font-size: 24rpx;\n        color: $uni-text-color-grey;\n        background-color: #f5f5f5;\n        padding: 4rpx 16rpx;\n        border-radius: 4rpx;\n      }\n\n      .message-actions {\n        display: flex;\n\n        .action-item {\n          font-size: 24rpx;\n          color: $uni-color-primary;\n          margin-left: 20rpx;\n        }\n      }\n    }\n  }\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 100rpx 0;\n\n  .empty-icon {\n    width: 200rpx;\n    height: 200rpx;\n    margin-bottom: 30rpx;\n  }\n\n  .empty-text {\n    font-size: 28rpx;\n    color: $uni-text-color-grey;\n  }\n}\n\n.load-more,\n.no-more {\n  text-align: center;\n  padding: 20rpx 0;\n  font-size: 26rpx;\n  color: $uni-text-color-grey;\n}\n\n.message-actions .action-item.ignore-btn {\n  color: $uni-color-warning;\n}\n.message-actions .action-item.delete-btn {\n  color: $uni-color-error;\n}\n</style>\n", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/message/center.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "hasPermission", "faultApi", "alarmApi", "patrolApi", "workOrderApi"], "mappings": ";;;;;AAgJA,mBAAmB,MAAW;AAC9B,MAAO,kBAAiB,MAAW;AAGnC,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,IACH;AAAA,EACE;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,kBAAkB;AAAA;AAAA,MAGlB,qBAAqB;AAAA,MACrB,oBAAoB;AAAA,MACpB,mBAAmB;AAAA,QACjB,EAAE,OAAO,QAAQ,OAAO,MAAO;AAAA,QAC/B,EAAE,OAAO,MAAM,OAAO,QAAS;AAAA,QAC/B,EAAE,OAAO,MAAM,OAAO,YAAa;AAAA,QACnC,EAAE,OAAO,MAAM,OAAO,WAAY;AAAA,QAClC,EAAE,OAAO,MAAM,OAAO,YAAa;AAAA,MACpC;AAAA,MAED,aAAa;AAAA,QACX,EAAE,OAAO,MAAM,OAAO,OAAO,OAAO,GAAG,aAAY,GAAE;AAAA;AAAA,QACrD,EAAE,OAAO,MAAM,OAAO,cAAc,OAAO,GAAG,aAAY,yBAAwB;AAAA;AAAA,QAClF,EAAE,OAAO,MAAM,OAAO,aAAa,OAAO,GAAG,aAAY,4BAA2B;AAAA;AAAA,QACpF,EAAE,OAAO,MAAM,OAAO,SAAS,OAAO,GAAG,aAAY,wBAAuB;AAAA;AAAA,QAC5E,EAAE,OAAO,MAAM,OAAO,SAAS,OAAO,GAAG,aAAY,wBAAuB;AAAA;AAAA,QAC5E,EAAE,OAAO,MAAM,OAAO,UAAU,OAAO,GAAG,aAAY,yBAAwB;AAAA;AAAA,MAC/E;AAAA,MAED,iBAAiB;AAAA,QACf,EAAE,OAAO,MAAM,OAAO,MAAO;AAAA,QAC7B,EAAE,OAAO,MAAM,OAAO,SAAU;AAAA,QAChC,EAAE,OAAO,MAAM,OAAO,YAAa;AAAA,QACnC,EAAE,OAAO,MAAM,OAAO,SAAU;AAAA,MACjC;AAAA,MAED,UAAU,CAAE;AAAA,MAEZ,MAAM;AAAA,MACN,UAAU;AAAA,MACV,SAAS;AAAA,MACT,WAAW;AAAA;EAEd;AAAA,EACD,UAAU;AAAA,IACR,mBAAmB;AACjB,UAAI,SAAS,CAAC,GAAG,KAAK,QAAQ;AAG9B,eAAS,OAAO,OAAO,SAAO;;AAE5B,YAAI,KAAK,eAAe,OAAO;AAE7B,cAAI,IAAI,SAAS,YAAY,GAAC,UAAK,cAAc,IAAI,IAAI,MAA3B,mBAA8B,cAAa;AACvE,mBAAO;AAAA,UACT;AAEA,gBAAM,MAAM,KAAK,cAAc,IAAI,IAAI;AACvC,iBAAO,QAAQ,CAAC,IAAI,eAAe,KAAK,cAAc,IAAI,WAAW;AAAA,eAChE;AAEL,iBAAO,IAAI,SAAS,KAAK;AAAA,QAC3B;AAAA,MACF,CAAC;AAGD,UAAI,KAAK,qBAAqB,OAAO;AACnC,iBAAS,OAAO,OAAO,CAAC,QAAQ,IAAI,aAAa,KAAK,gBAAgB;AAAA,MACxE;AAGA,UAAI,KAAK,uBAAuB,OAAO;AACrC,cAAM,MAAM,oBAAI;AAChB,cAAM,QAAQ,IAAI,KAAK,IAAI,YAAW,GAAI,IAAI,SAAU,GAAE,IAAI,QAAS,CAAA;AACvE,cAAM,YAAY,IAAI,KAAK,KAAK;AAChC,kBAAU,QAAQ,UAAU,QAAU,IAAE,CAAC;AAEzC,cAAM,cAAc,IAAI,KAAK,KAAK;AAClC,oBAAY,QAAQ,MAAM,QAAU,IAAE,MAAM,OAAM,CAAE;AAEpD,cAAM,eAAe,IAAI,KAAK,IAAI,YAAa,GAAE,IAAI,YAAY,CAAC;AAElE,iBAAS,OAAO,OAAO,CAAC,QAAQ;AAC9B,gBAAM,cAAc,IAAI;AAAA,YACtB,IAAI,KAAK,QAAQ,qCAAqC,UAAU;AAAA;AAGlE,kBAAQ,KAAK,oBAAkB;AAAA,YAC7B,KAAK;AACH,qBAAO,eAAe;AAAA,YACxB,KAAK;AACH,qBAAO,eAAe,aAAa,cAAc;AAAA,YACnD,KAAK;AACH,qBAAO,eAAe;AAAA,YACxB,KAAK;AACH,qBAAO,eAAe;AAAA,YACxB;AACE,qBAAO;AAAA,UACX;AAAA,QACF,CAAC;AAAA,MACH;AAEA,aAAO,KAAK,CAAC,GAAG,MAAM,IAAI,KAAK,EAAE,IAAI,EAAE,QAAO,IAAK,IAAI,KAAK,EAAE,IAAI,EAAE,QAAO,CAAE;AAE7E,aAAO;AAAA,IACR;AAAA,EACF;AAAA,EACD,SAAS;AACPA,kBAAAA,MAAI,IAAI,sBAAsB,KAAK,iBAAiB;AAGpD,UAAM,aAAa,KAAK;AACxB,UAAM,YAAY,KAAK,cAAc,UAAU;AAG/C,QAAI,aAAa,UAAU,eAAe,CAAC,KAAK,cAAc,UAAU,WAAW,GAAG;AACpFA,0BAAY,MAAA,OAAA,mCAAA,eAAe,UAAU,cAAc;AAGnD,YAAM,eAAe,KAAK,YAAY,KAAK,SAAO,CAAC,IAAI,eAAe,KAAK,cAAc,IAAI,WAAW,CAAC;AAEzG,UAAI,cAAc;AAChBA,4BAAY,MAAA,OAAA,mCAAA,YAAY,aAAa,KAAK,EAAE;AAC5C,aAAK,aAAa,aAAa;AAAA,MACjC;AAAA,IACF;AAEA,SAAK,oBAAmB;AAAA,EACzB;AAAA,EACD,SAAS;AAEP,SAAK,WAAW;AAEhB,SAAK,iBAAgB;AACrB,SAAK,mBAAkB;AAAA,EACxB;AAAA,EACD,WAAW;AACTA,kBAAAA,MAAI,KAAK,sBAAsB,KAAK,iBAAiB;AAAA,EACtD;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,cAAc,OAAO;AACnB,aAAO,KAAK,YAAY,KAAK,SAAO,IAAI,UAAU,KAAK;AAAA,IACxD;AAAA;AAAA,IAGD,cAAc,YAAY;AACxB,aAAOC,WAAAA,cAAc,UAAU;AAAA,IAChC;AAAA,IAED,kBAAkB,SAAS;AACzBD,oBAAA,MAAA,MAAA,OAAA,mCAAY,gBAAgB,OAAO;AACnC,YAAM,EAAE,MAAM,SAAS;AAGvB,YAAM,eAAe,KAAK,gCAAgC,IAAI;AAG9D,YAAM,MAAM,KAAK,cAAc,YAAY;AAC3C,UAAI,OAAO,IAAI,eAAe,CAAC,KAAK,cAAc,IAAI,WAAW,GAAG;AAClEA,4BAAY,MAAA,OAAA,mCAAA,gBAAgB,IAAI,WAAW;AAC3C;AAAA,MACF;AAEA,UAAI,QAAQ,MAAM,QAAQ,IAAI,KAAK,KAAK,SAAS,GAAG;AAClD,cAAM,cAAc,KACjB,IAAI,CAAC,QAAQ,KAAK,uBAAuB,KAAK,IAAI,CAAC,EACnD,OAAO,CAAC,QAAQ,QAAQ,IAAI;AAE/B,cAAM,cAAc,IAAI,IAAI,KAAK,SAAS,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;AAC1D,cAAM,oBAAoB,YAAY,OAAO,CAAC,OAAO,CAAC,YAAY,IAAI,GAAG,EAAE,CAAC;AAE5E,YAAI,kBAAkB,SAAS,GAAG;AAEhC,eAAK,SAAS,QAAQ,GAAG,iBAAiB;AAC1C,eAAK,gBAAgB,MAAM,kBAAkB,MAAM;AACnDA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,MACL,kBAAkB,MAClB,OAAK,KAAK,gCAAgC,IAAI,CAAC;AAAA,YACjD,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACD;AAAA,IAED,uBAAuB,KAAK,oBAAoB;AAC9C,UAAI,CAAC,OAAO,CAAC,IAAI;AAAI,eAAO;AAE5B,UAAI,eAAe,KAAK,gCAAgC,kBAAkB;AAC1E,UAAI,QAAQ,IAAI,kBAAkB;AAClC,UAAI,UAAU;AACd,UAAI,WAAW;AACf,UAAI,YAAY;AAChB,UAAI,eAAe;AACnB,UAAI,aAAa;AACjB,UAAI,UAAU;AACd,UAAI,aAAa;AAEjB,cAAQ,oBAAkB;AAAA,QACxB,KAAK;AACH,kBAAQ,IAAI,QAAQ;AACpB,oBAAU,YAAY,IAAI,QAAQ,KAAK;AACvC,qBAAW;AACX;AAAA,QACF,KAAK;AACH,kBAAQ,IAAI;AACZ,oBAAU,IAAI,aAAa;AAC3B,sBAAY,IAAI;AAChB,qBAAW;AACX,yBAAe,IAAI,gBAAgB;AACnC,uBAAa,IAAI;AACjB,oBAAU,IAAI;AACd,uBAAa,IAAI,eAAe;AAChC;AAAA,QACF,KAAK;AACH,kBAAQ,IAAI;AACZ,oBAAU,IAAI,aAAa;AAC3B,sBAAY,IAAI;AAChB,qBAAW;AACX,yBAAe,IAAI,gBAAgB;AACnC,uBAAa,IAAI,cAAc;AAC/B;AAAA,QACF,KAAK;AACH,kBAAQ,GAAG,IAAI,YAAY;AAC3B,oBAAU,IAAI,aACV,MAAM,IAAI,gBAAgB,IAAI,KAAK,IAAI,UAAU,KACjD;AACJ,sBAAY,IAAI;AAChB,qBAAW;AACX,yBAAe,IAAI,gBAAgB;AACnC,uBAAa,IAAI,cAAc;AAC/B;AAAA,MACJ;AAEA,UAAI,aAAa;AACjB,UAAI,OAAO,cAAc,UAAU;AACjC,qBAAa;AAAA,MACf,WAAW,qBAAqB,MAAM;AACpC,qBAAa,UAAU;iBACd,MAAM,QAAQ,SAAS,KAAK,UAAU,UAAU,GAAG;AAC5D,YAAI;AACF,gBAAM,CAAC,MAAM,OAAO,KAAK,MAAM,MAAM,IAAI;AACzC,gBAAM,WAAW,OAAO,KAAK,EAAE,SAAS,GAAG,GAAG;AAC9C,gBAAM,SAAS,OAAO,GAAG,EAAE,SAAS,GAAG,GAAG;AAC1C,gBAAM,UAAU,OAAO,IAAI,EAAE,SAAS,GAAG,GAAG;AAC5C,gBAAM,YAAY,OAAO,MAAM,EAAE,SAAS,GAAG,GAAG;AAChD,gBAAM,aACJ,UAAU,SAAS,IAAI,OAAO,UAAU,CAAC,CAAC,EAAE,SAAS,GAAG,GAAG,IAAI;AACjE,uBAAa,GAAG,IAAI,IAAI,QAAQ,IAAI,MAAM,IAAI,OAAO,IAAI,SAAS,IAAI,UAAU;AAAA,QAClF,SAAS,GAAG;AACVA,gFAAc,mBAAmB,WAAW,CAAC;AAC7C,wBAAa,oBAAI,QAAO;QAC1B;AAAA,MACF,WAAW,WAAW;AACpB,qBAAa,OAAO,SAAS;AAAA,aACxB;AACL,sBAAa,oBAAI,QAAO;AACxBA,sBAAAA,MAAa,MAAA,QAAA,mCAAA,OAAO,YAAY,IAAI,IAAI,EAAE,kBAAkB;AAAA,MAC9D;AAEA,aAAO;AAAA,QACL,IAAI,GAAG,YAAY,IAAI,IAAI,EAAE;AAAA,QAC7B;AAAA,QACA;AAAA,QACA,MAAM,WAAW,QAAQ,KAAK,GAAG,EAAE,UAAU,GAAG,EAAE;AAAA,QAClD,MAAM;AAAA,QACN;AAAA,QACA,MAAM;AAAA,QACN,WAAW,IAAI;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;IAEH;AAAA,IAED,gCAAgC,aAAa;AAC3C,YAAM,MAAM;AAAA,QACV,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA;AAEN,aAAO,IAAI,WAAW,KAAK;AAAA,IAC5B;AAAA,IAED,gBAAgB,oBAAoB,gBAAgB;AAClD,YAAM,eAAe,KAAK,gCAAgC,kBAAkB;AAC5E,YAAM,MAAM,KAAK,YAAY,KAAK,CAAC,MAAM,EAAE,UAAU,YAAY;AACjE,UAAI,KAAK;AACP,YAAI,SAAS,IAAI,SAAS,KAAK;AAAA,MACjC;AACA,YAAM,SAAS,KAAK,YAAY,KAAK,CAAC,MAAM,EAAE,UAAU,KAAK;AAC7D,UAAI,QAAQ;AACV,eAAO,SAAS,OAAO,SAAS,KAAK;AAAA,MACvC;AAAA,IACD;AAAA,IAED,qBAAqB;AACnB,YAAM,SAAS;AAAA,QACb,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,OAAO;AAAA,QACP,OAAO;AAAA,QACP,QAAQ;AAAA;AAIV,YAAM,iBAAiB,KAAK,YACzB,OAAO,SAAO,CAAC,IAAI,eAAe,KAAK,cAAc,IAAI,WAAW,CAAC,EACrE,IAAI,SAAO,IAAI,KAAK;AAGvB,UAAI,CAAC,eAAe,SAAS,QAAQ,GAAG;AACtC,uBAAe,KAAK,QAAQ;AAAA,MAC9B;AAGA,WAAK,SAAS,QAAQ,CAAC,QAAQ;AAE7B,YAAI,eAAe,SAAS,IAAI,IAAI,GAAG;AACrC,iBAAO;AACP,cAAI,OAAO,IAAI,IAAI,MAAM,QAAW;AAClC,mBAAO,IAAI,IAAI;AAAA,iBACV;AACL,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF,CAAC;AAGD,WAAK,YAAY,QAAQ,CAAC,QAAQ;AAEhC,YAAI,CAAC,IAAI,eAAe,KAAK,cAAc,IAAI,WAAW,GAAG;AAC3D,cAAI,QAAQ,OAAO,IAAI,KAAK,KAAK;AAAA,eAC5B;AACL,cAAI,QAAQ;AAAA,QACd;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,sBAAsB;AACpB,UAAI,KAAK;AAAW;AACpB,WAAK,YAAY;AACjBA,oBAAAA,MAAY,MAAA,OAAA,mCAAA,gBAAgB;AAE5B,WAAK,WAAW;AAChB,WAAK,mBAAmB;AACxB,WAAK,qBAAqB;AAC1B,WAAK,mBAAkB;AACvB,WAAK,YAAY;AAAA,IAClB;AAAA,IAED,WAAW;AACT,UAAI,KAAK,aAAa,CAAC,KAAK;AAAS;AACrC,WAAK;AACL,WAAK,YAAY;AACjBA,0BAAY,MAAA,OAAA,mCAAA,aAAa,KAAK,IAAI,GAAG;AAErC,iBAAW,MAAM;AACf,aAAK,UAAU;AACf,aAAK,YAAY;AAAA,MAClB,GAAE,GAAG;AAAA,IACP;AAAA,IAED,UAAU,KAAK;AAEb,YAAM,YAAY,KAAK,cAAc,GAAG;AAGxC,UAAI,aAAa,UAAU,eAAe,CAAC,KAAK,cAAc,UAAU,WAAW,GAAG;AACpFA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,WAAK,aAAa;AAClBA,oBAAY,MAAA,MAAA,OAAA,mCAAA,UAAU,GAAG,EAAE;AAG3B,WAAK,kBAAkB;AACvB,WAAK,sBAAsB;AAAA,IAK5B;AAAA,IAED,oBAAoB;AAClB,WAAK,kBAAkB,CAAC,KAAK;AAC7B,UAAI,KAAK,iBAAiB;AACxB,aAAK,sBAAsB;AAAA,MAC7B;AAAA,IACD;AAAA,IAED,wBAAwB;AACtB,WAAK,sBAAsB,CAAC,KAAK;AACjC,UAAI,KAAK,qBAAqB;AAC5B,aAAK,kBAAkB;AAAA,MACzB;AAAA,IACD;AAAA,IAED,eAAe,UAAU;AACvB,WAAK,mBAAmB;AACxB,WAAK,kBAAkB;AAAA,IACxB;AAAA,IAED,iBAAiB,YAAY;AAC3B,WAAK,qBAAqB;AAC1B,WAAK,sBAAsB;AAAA,IAC5B;AAAA,IAED,cAAc;AACZ,WAAK,SAAS,QAAQ,CAAC,QAAQ;AAC7B,YAAI,OAAO;AAAA,MACb,CAAC;AAEDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IAED,iBAAiB;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,kBAAM,cAAc,KAAK,iBAAiB,IAAI,CAAC,MAAM,EAAE,EAAE;AACzD,iBAAK,WAAW,KAAK,SAAS,OAAO,CAAC,QAAQ,CAAC,YAAY,SAAS,IAAI,EAAE,CAAC;AAE3E,iBAAK,mBAAkB;AAEvBA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACD;AAAA,MACH,CAAC;AAAA,IACF;AAAA,IAED,kBAAkB,SAAS;AACzB,UAAI,CAAC;AAAS;AAEd,UAAI,CAAC,QAAQ,QAAQ,QAAQ,SAAS,SAAS;AAC7C,gBAAQ,OAAO;AAAA,MACjB;AAEA,UAAI,QAAQ,SAAS,SAAS;AAC5B,aAAK,sBAAsB,OAAO;AAAA,aAC7B;AACL,YAAI,YAAY;AAChB,cAAM,YAAY,QAAQ,aAAa;AAEvC,gBAAQ,QAAQ,MAAI;AAAA,UAClB,KAAK;AACH,wBAAY,0BAA0B,SAAS;AAC/C;AAAA,UACF,KAAK;AACH,wBAAY,8BAA8B,SAAS;AACnD;AAAA,UACF,KAAK;AACH,wBAAY,kCAAkC,SAAS;AACvD;AAAA,QACJ;AAEA,YAAI,aAAa,WAAW;AAC1BA,wBAAA,MAAA,MAAA,OAAA,mCAAY,QAAQ,SAAS,EAAE;AAC/BA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,YACL,MAAM,CAAC,QAAQ;AACbA,4BAAAA,wDAAc,SAAS,KAAK,UAAU,GAAG,CAAC,EAAE;AAC5CA,4BAAG,MAAC,UAAU,EAAE,OAAO,UAAU,MAAM,OAAK,CAAG;AAAA,YAChD;AAAA,UACH,CAAC;AAAA,eACI;AACLA,wBAAY,MAAA,MAAA,OAAA,mCAAA,iCAAiC,OAAO;AACpDA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,QAAQ;AAAA,YACf,SAAS,QAAQ;AAAA,YACjB,YAAY;AAAA,UACd,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACD;AAAA,IAED,sBAAsB,SAAS;AAC7BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,EAAW,QAAQ,OAAO;AAAA;AAAA;AAAA,QACnC,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AAEnC,gBAAI,eAAeA,cAAG,MAAC,eAAe,QAAQ,KAAK;AACnD,gBAAI,CAAC,cAAc;AACjBA,4BAAG,MAAC,YAAW;AACfA,4BAAG,MAAC,UAAU,EAAE,OAAO,kBAAkB,MAAM,OAAO,CAAC;AACvD;AAAA,YACF;AAEA,gBAAI;AAEF,oBAAM,aAAa;AAAA,gBACjB,cAAc,QAAQ;AAAA;AAAA,gBACtB,UAAU,QAAQ;AAAA,gBAClB,YAAY;AAAA;AAAA,gBACZ,aAAa,QAAQ,cAAc;AAAA;AAAA,gBACnC,YAAY,QAAQ;AAAA;AAAA,gBACpB,cAAc;AAAA;AAAA,gBACd,YAAY,QAAQ,OAAO,GAAG,QAAQ,IAAI,QAAQ;AAAA;AAAA,gBAClD,gBAAgB;AAAA,gBAChB,YAAY,CAAE;AAAA;AAAA;AAEhBA,4BAAA,MAAA,MAAA,OAAA,mCAAY,eAAe,UAAU;AACrC,oBAAM,YAAY,MAAME,UAAAA,SAAS,YAAY,UAAU;AACvDF,4BAAG,MAAC,YAAW;AACf,kBAAI,UAAU,SAAS,KAAK;AAC1BA,8BAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,UAAU,CAAC;AAGnD,sBAAM,iBAAiB,MAAMG,UAAAA,SAAS;AAAA,kBACpC,QAAQ;AAAA,kBACR;AAAA,gBACF;AACA,oBAAI,eAAe,SAAS,KAAK;AAC/B,wBAAM,IAAI,MAAM,eAAe,WAAW,UAAU;AAAA,gBACtD;AACAH,oCAAY,MAAA,OAAA,mCAAA,MAAM,QAAQ,SAAS,YAAY;AAG/C,sBAAM,QAAQ,KAAK,SAAS,UAAU,CAAC,MAAM,EAAE,OAAO,QAAQ,EAAE;AAChE,oBAAI,QAAQ,IAAI;AACd,uBAAK,SAAS,OAAO,OAAO,CAAC;AAC7B,uBAAK,mBAAkB;AAAA,gBACzB;AAAA,qBACK;AACL,sBAAM,IAAI,MAAM,UAAU,WAAW,QAAQ;AAAA,cAC/C;AAAA,YACA,SAAO,OAAO;AACdA,4BAAG,MAAC,YAAW;AACfA,oFAAc,mBAAmB,KAAK;AACtCA,kCAAI,UAAU,EAAE,OAAO,MAAM,WAAW,YAAY,MAAM,OAAO,CAAC;AAAA,YACpE;AAAA,UACF;AAAA,QACD;AAAA,MACH,CAAC;AAAA,IACF;AAAA,IAED,YAAY,IAAI;AACd,YAAM,UAAU,KAAK,SAAS,KAAK,CAAC,QAAQ,IAAI,OAAO,EAAE;AACzD,UAAI,CAAC,WAAW,QAAQ,SAAS;AAAS;AAE1CA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,gCAAY;AAAA,cAAA;AAAA,cAAA;AAAA,cACV,kBAAkB,EAAE,aAAa,QAAQ,SAAS;AAAA;AAEpDA,0BAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AACnCG,sBAAO,SACJ,kBAAkB,QAAQ,WAAW,CAAC,EACtC,KAAK,CAAC,WAAW;AAChBH,4BAAG,MAAC,YAAW;AACf,kBAAI,OAAO,SAAS,KAAK;AACvBA,8BAAG,MAAC,UAAU,EAAE,OAAO,SAAS,MAAM,UAAQ,CAAG;AACjD,sBAAM,QAAQ,KAAK,SAAS,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE;AACxD,oBAAI,QAAQ,IAAI;AACd,uBAAK,SAAS,OAAO,OAAO,CAAC;AAC7B,uBAAK,mBAAkB;AAAA,gBACzB;AAAA,qBACK;AACLA,oCAAI,UAAU,EAAE,OAAO,OAAO,WAAW,QAAQ,MAAM,OAAO,CAAC;AAAA,cACjE;AAAA,aACD,EACA,MAAM,CAAC,QAAQ;AACdA,4BAAG,MAAC,YAAW;AACfA,oFAAc,gBAAgB,GAAG;AACjCA,4BAAG,MAAC,UAAU,EAAE,OAAO,YAAY,MAAM,OAAK,CAAG;AAAA,YACnD,CAAC;AAAA,UACL;AAAA,QACD;AAAA,MACH,CAAC;AAAA,IACF;AAAA,IAED,YAAY,MAAM;AAChB,YAAM,UAAU;AAAA,QACd,OAAO;AAAA,QACP,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,QAAQ;AAAA;AAEV,aAAO,QAAQ,IAAI,KAAK;AAAA,IACzB;AAAA,IAED,MAAM,mBAAmB;AACvBA,oBAAAA,MAAY,MAAA,OAAA,mCAAA,iDAAiD;AAC7DA,oBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AAEnC,UAAI;AAEF,cAAM,SAASA,cAAAA,MAAI,eAAe,QAAQ;AAC1C,cAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAE9CA,4BAAA,MAAA,OAAA,mCAAY,2BAA2B,MAAM,SAAS,QAAQ,EAAE;AAEhE,YAAI,CAAC,QAAQ;AACXA,wBAAAA,MAAA,MAAA,QAAA,mCAAa,mCAAmC;AAAA,QAClD;AAGA,cAAM,kBAAkB;AAAA,UACtB;AAAA,YACE,KAAK,MAAMI,UAAS,UAAC,uBAAuB,QAAQ,QAAQ;AAAA;AAAA,YAC5D,MAAM;AAAA,YACN,gBAAgB;AAAA,YAChB,YAAY;AAAA;AAAA,UACb;AAAA,UACD;AAAA,YACE,KAAK,MAAMD,UAAQ,SAAC,aAAa,QAAQ,QAAQ;AAAA;AAAA,YACjD,MAAM;AAAA,YACN,gBAAgB;AAAA,YAChB,YAAY;AAAA;AAAA,UACb;AAAA,UACD;AAAA,YACE,KAAK,MAAMD,UAAQ,SAAC,iBAAiB,QAAQ,QAAQ;AAAA;AAAA,YACrD,MAAM;AAAA,YACN,gBAAgB;AAAA,YAChB,YAAY;AAAA;AAAA,UACb;AAAA,UACD;AAAA,YACE,KAAK,MAAMG,UAAY,aAAC,qBAAqB,QAAQ,QAAQ;AAAA;AAAA,YAC7D,MAAM;AAAA,YACN,gBAAgB;AAAA,YAChB,YAAY;AAAA;AAAA,UACb;AAAA;AAGH,YAAI,iBAAiB,CAAA;AAErB,mBAAW,WAAW,iBAAiB;AACrC,cAAI;AAEF,gBAAI,QAAQ,cAAc,CAAC,KAAK,cAAc,QAAQ,UAAU,GAAG;AACjEL,kCAAA,MAAA,OAAA,mCAAY,2BAA2B,QAAQ,IAAI,SAAS;AAC5D;AAAA,YACF;AAEAA,gCAAA,MAAA,OAAA,mCAAY,4BAA4B,QAAQ,IAAI,cAAc;AAElE,kBAAM,MAAM,OAAO,QAAQ,QAAQ,aAAa,MAAM,QAAQ,IAAI,IAAI,MAAM,QAAQ,IAAG;AACvFA,gCAAY;AAAA,cAAA;AAAA,cAAA;AAAA,cACV,2BAA2B,QAAQ,IAAI;AAAA,cACvC;AAAA;AAEF,gBAAI,OAAO,IAAI,SAAS,OAAO,IAAI,QAAQ,MAAM,QAAQ,IAAI,IAAI,GAAG;AAClE,oBAAM,cAAc,IAAI,KACrB,IAAI,CAAC,QAAQ,KAAK,uBAAuB,KAAK,QAAQ,cAAc,CAAC,EACrE,OAAO,CAAC,QAAQ,QAAQ,IAAI;AAC/B,6BAAe,KAAK,GAAG,WAAW;AAClCA,kCAAY;AAAA,gBAAA;AAAA,gBAAA;AAAA,gBACV,+BAA+B,QAAQ,IAAI;AAAA,gBAC3C,YAAY;AAAA;mBAET;AACLA,kCAAa;AAAA,gBAAA;AAAA,gBAAA;AAAA,gBACX,kDAAkD,QAAQ,IAAI;AAAA,gBAC9D;AAAA;YAEJ;AAAA,UACA,SAAO,OAAO;AACdA,gCAAc;AAAA,cAAA;AAAA,cAAA;AAAA,cACZ,kCAAkC,QAAQ,IAAI;AAAA,cAC9C;AAAA;UAEJ;AAAA,QACF;AAEA,YAAI,eAAe,SAAS,GAAG;AAC7B,gBAAM,cAAc,IAAI,IAAI,KAAK,SAAS,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;AAC1D,gBAAM,oBAAoB,eAAe;AAAA,YACvC,CAAC,OAAO,CAAC,YAAY,IAAI,GAAG,EAAE;AAAA;AAGhC,cAAI,kBAAkB,SAAS,GAAG;AAChC,iBAAK,SAAS,QAAQ,GAAG,iBAAiB;AAE1C,iBAAK,SAAS;AAAA,cACZ,CAAC,GAAG,MAAM,IAAI,KAAK,EAAE,IAAI,EAAE,QAAO,IAAK,IAAI,KAAK,EAAE,IAAI,EAAE,QAAQ;AAAA;AAElEA,gCAAY;AAAA,cAAA;AAAA,cAAA;AAAA,cACV,yBAAyB,kBAAkB,MAAM;AAAA;AAEnDA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO,OAAO,kBAAkB,MAAM;AAAA,cACtC,MAAM;AAAA,YACR,CAAC;AAAA,iBACI;AACLA,0BAAAA,MAAA,MAAA,OAAA,mCAAY,gDAAgD;AAAA,UAC9D;AAAA,eACK;AACLA,wBAAAA,MAAY,MAAA,OAAA,mCAAA,gDAAgD;AAAA,QAC9D;AAAA,MACA,SAAO,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,mCAAc,8CAA8C,KAAK;AACjEA,sBAAG,MAAC,UAAU,EAAE,OAAO,UAAU,MAAM,OAAK,CAAG;AAAA,MACjD,UAAU;AACR,aAAK,mBAAkB;AACvB,aAAK,YAAY;AACjBA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAA,MAAA,OAAA,mCAAY,6CAA6C;AAAA,MAC3D;AAAA,IACD;AAAA,EACF;AACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC12BA,GAAG,WAAW,eAAe;"}