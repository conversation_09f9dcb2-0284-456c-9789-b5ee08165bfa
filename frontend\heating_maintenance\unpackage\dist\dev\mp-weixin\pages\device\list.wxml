<view class="device-container"><view class="search-filter-bar"><view class="search-box"><text class="iconfont icon-search"></text><input type="text" placeholder="搜索设备名称或型号" bindinput="{{a}}" value="{{b}}"/><text wx:if="{{c}}" class="iconfont icon-clear" bindtap="{{d}}"></text></view><view class="filter-btn" bindtap="{{e}}"><text class="iconfont icon-filter"></text><text>筛选</text></view></view><view class="status-tabs"><view wx:for="{{f}}" wx:for-item="tab" wx:key="c" class="{{['status-tab', tab.d && 'active']}}" bindtap="{{tab.e}}"><text>{{tab.a}}</text><text class="tab-count">({{tab.b}})</text></view></view><scroll-view scroll-y="true" class="device-list" bindscrolltolower="{{l}}"><view wx:if="{{g}}"><view wx:for="{{h}}" wx:for-item="device" wx:key="j" class="device-card" bindtap="{{device.k}}"><view class="{{['device-status', device.a]}}"></view><view class="device-info"><view class="device-name-type"><text class="device-name">{{device.b}}</text><text class="device-type">{{device.c}}</text></view><view class="device-model">{{device.d}}</view><view class="device-location"><text class="iconfont icon-location"></text><text>{{device.e}}</text></view></view><view class="device-metrics"><view class="metric-item"><text class="metric-label">上次维护</text><text class="metric-value">{{device.f}}</text></view><view class="metric-item"><text class="metric-label">下次维护</text><text class="metric-value">{{device.g}}</text></view><view wx:if="{{device.h}}" class="metric-item"><text class="metric-label">告警数</text><text class="metric-value alarm">{{device.i}}</text></view></view><view class="device-action"><text class="iconfont icon-detail"></text></view></view><view wx:if="{{i}}" class="loading-more"><text>加载中...</text></view><view wx:if="{{j}}" class="no-more"><text>没有更多数据了</text></view></view><view wx:else class="empty-list"><image src="{{k}}" mode="aspectFit"></image><text>暂无设备数据</text></view></scroll-view><uni-popup wx:if="{{t}}" class="r" u-s="{{['d']}}" u-r="filterPopup" u-i="55605091-0" bind:__l="__l" u-p="{{t}}"><view class="filter-modal"><view class="filter-header"><text class="filter-title">筛选条件</text><text class="filter-reset" bindtap="{{m}}">重置</text></view><view class="filter-content"><view class="filter-section"><text class="filter-section-title">设备类型</text><view class="filter-options"><view wx:for="{{n}}" wx:for-item="type" wx:key="b" class="{{['filter-option', type.c && 'selected']}}" bindtap="{{type.d}}">{{type.a}}</view></view></view><view class="filter-section"><text class="filter-section-title">设备区域</text><view class="filter-options"><view wx:for="{{o}}" wx:for-item="area" wx:key="b" class="{{['filter-option', area.c && 'selected']}}" bindtap="{{area.d}}">{{area.a}}</view></view></view><view class="filter-section"><text class="filter-section-title">排序方式</text><view class="filter-options"><view wx:for="{{p}}" wx:for-item="sort" wx:key="b" class="{{['filter-option', sort.c && 'selected']}}" bindtap="{{sort.d}}">{{sort.a}}</view></view></view></view><view class="filter-footer"><button class="btn-cancel" bindtap="{{q}}">取消</button><button class="btn-apply" bindtap="{{r}}">确认</button></view></view></uni-popup><view class="fab-button" bindtap="{{v}}"><text class="iconfont icon-add"></text></view></view>