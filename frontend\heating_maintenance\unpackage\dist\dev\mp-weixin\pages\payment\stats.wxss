/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.payment-stats-container {
  padding: 30rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}
.page-header {
  margin-bottom: 30rpx;
}
.page-header .page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.query-form {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.query-form .form-item {
  margin-bottom: 20rpx;
}
.query-form .form-item .form-label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.query-form .form-item .form-picker {
  height: 80rpx;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
}
.query-form .form-item .form-picker .picker-text {
  font-size: 28rpx;
  color: #333;
}
.query-form .form-item .date-range {
  display: flex;
  align-items: center;
}
.query-form .form-item .date-range .date-picker {
  flex: 1;
  height: 80rpx;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
}
.query-form .form-item .date-range .date-picker .picker-text {
  font-size: 28rpx;
  color: #333;
}
.query-form .form-item .date-range .date-separator {
  margin: 0 20rpx;
  color: #999;
}
.query-form .query-btn {
  background-color: #1890ff;
  color: #fff;
  border-radius: 8rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  margin-top: 20rpx;
}
.query-form .query-btn:active {
  background-color: #0e80eb;
}
.stats-cards {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx 30rpx;
}
.stats-cards .stats-card {
  width: calc(50% - 20rpx);
  margin: 0 10rpx 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.stats-cards .stats-card .stats-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 10rpx;
}
.stats-cards .stats-card .stats-label {
  font-size: 24rpx;
  color: #666;
}
.stats-cards .stats-card:nth-child(3) .stats-value {
  color: #faad14;
}
.stats-cards .stats-card:nth-child(4) .stats-value {
  color: #52c41a;
}
.loading-container {
  padding: 40rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
.chart-container {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.chart-container .chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.chart-container .chart-header .chart-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.chart-container .chart-header .chart-tabs {
  display: flex;
}
.chart-container .chart-header .chart-tabs .chart-tab {
  font-size: 28rpx;
  color: #666;
  margin-left: 30rpx;
  padding-bottom: 10rpx;
  position: relative;
}
.chart-container .chart-header .chart-tabs .chart-tab.active {
  color: #1890ff;
}
.chart-container .chart-header .chart-tabs .chart-tab.active:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4rpx;
  background-color: #1890ff;
  border-radius: 2rpx;
}
.chart-container .chart-content {
  min-height: 500rpx;
}
.chart-container .chart-content .chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.chart-container .chart-content .chart-placeholder .trend-chart-mock {
  width: 100%;
  height: 500rpx;
  padding: 20rpx 0;
}
.chart-container .chart-content .chart-placeholder .trend-chart-mock .chart-legend {
  display: flex;
  justify-content: center;
  margin-bottom: 20rpx;
}
.chart-container .chart-content .chart-placeholder .trend-chart-mock .chart-legend .legend-item {
  display: flex;
  align-items: center;
  margin: 0 20rpx;
}
.chart-container .chart-content .chart-placeholder .trend-chart-mock .chart-legend .legend-item .legend-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
}
.chart-container .chart-content .chart-placeholder .trend-chart-mock .chart-legend .legend-item .legend-text {
  font-size: 24rpx;
  color: #666;
}
.chart-container .chart-content .chart-placeholder .trend-chart-mock .chart-bars {
  flex: 1;
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  height: 400rpx;
}
.chart-container .chart-content .chart-placeholder .trend-chart-mock .chart-bars .chart-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 16.6666666667%;
}
.chart-container .chart-content .chart-placeholder .trend-chart-mock .chart-bars .chart-bar .bar-label {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}
.chart-container .chart-content .chart-placeholder .trend-chart-mock .chart-bars .chart-bar .bar-wrapper {
  width: 60rpx;
  height: 300rpx;
  display: flex;
  flex-direction: column-reverse;
  position: relative;
}
.chart-container .chart-content .chart-placeholder .trend-chart-mock .chart-bars .chart-bar .bar-wrapper .bar-value {
  position: absolute;
  bottom: 0;
  width: 100%;
  transition: height 0.5s ease;
}
.chart-container .chart-content .chart-placeholder .trend-chart-mock .chart-bars .chart-bar .bar-wrapper .bar-value.paid {
  background-color: #1890ff;
  border-radius: 8rpx 8rpx 0 0;
  z-index: 2;
}
.chart-container .chart-content .chart-placeholder .trend-chart-mock .chart-bars .chart-bar .bar-wrapper .bar-value.unpaid {
  background-color: #faad14;
  border-radius: 0 0 8rpx 8rpx;
  z-index: 1;
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock {
  width: 100%;
  height: 500rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock .pie-chart {
  width: 300rpx;
  height: 300rpx;
  position: relative;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 30rpx;
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock .pie-chart .pie-slice {
  position: absolute;
  width: 100%;
  height: 100%;
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock .pie-chart .pie-slice.paid {
  background-color: #1890ff;
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock .pie-chart .pie-slice.unpaid {
  background-color: #faad14;
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock .pie-chart .pie-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200rpx;
  height: 200rpx;
  background-color: white;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.05);
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock .pie-chart .pie-center .pie-percent {
  font-size: 40rpx;
  font-weight: bold;
  color: #1890ff;
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock .pie-chart .pie-center .pie-label {
  font-size: 24rpx;
  color: #666;
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock .pie-legend {
  display: flex;
  justify-content: center;
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock .pie-legend .legend-item {
  display: flex;
  align-items: center;
  margin: 0 30rpx;
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock .pie-legend .legend-item .legend-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock .pie-legend .legend-item .legend-info {
  display: flex;
  flex-direction: column;
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock .pie-legend .legend-item .legend-info .legend-text {
  font-size: 24rpx;
  color: #666;
}
.chart-container .chart-content .chart-placeholder .pie-chart-mock .pie-legend .legend-item .legend-info .legend-value {
  font-size: 22rpx;
  color: #999;
}
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-container .empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.empty-container .empty-text {
  font-size: 28rpx;
  color: #999;
}