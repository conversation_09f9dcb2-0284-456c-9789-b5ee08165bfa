/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.change-password-container {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding-bottom: 40rpx;
}
.page-header {
  background-color: #fff;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}
.page-header .page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.form-card {
  margin: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.05);
}
.form-card .form-item {
  padding: 24rpx 20rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}
.form-card .form-item:last-child {
  border-bottom: none;
}
.form-card .form-item .form-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}
.form-card .form-item .form-input input {
  height: 80rpx;
  font-size: 32rpx;
  color: #333;
  width: 100%;
}
.form-card .form-item .password-rules {
  display: flex;
  flex-wrap: wrap;
  margin-top: 16rpx;
}
.form-card .form-item .password-rules .rule-item {
  font-size: 24rpx;
  color: #999;
  margin-right: 30rpx;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
}
.form-card .form-item .password-rules .rule-item::before {
  content: "";
  display: inline-block;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #e8e8e8;
  margin-right: 8rpx;
}
.form-card .form-item .password-rules .rule-item.rule-passed {
  color: #52c41a;
}
.form-card .form-item .password-rules .rule-item.rule-passed::before {
  background-color: #52c41a;
}
.form-card .form-item .confirm-tip {
  margin-top: 16rpx;
}
.form-card .form-item .confirm-tip .tip-error {
  font-size: 24rpx;
  color: #f5222d;
}
.submit-btn {
  margin: 60rpx 30rpx;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #1890ff;
  color: #fff;
  text-align: center;
  border-radius: 12rpx;
  font-size: 32rpx;
  box-shadow: 0 6rpx 20rpx rgba(24, 144, 255, 0.3);
  font-weight: bold;
}
.submit-btn:active {
  opacity: 0.9;
  transform: translateY(2rpx);
}
.submit-btn.btn-disabled {
  background-color: #ccc;
  box-shadow: none;
}
.help-tip {
  padding: 0 40rpx;
  margin-top: 40rpx;
}
.help-tip .tip-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}
.help-tip .tip-content {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 10rpx;
  display: block;
  line-height: 1.6;
}