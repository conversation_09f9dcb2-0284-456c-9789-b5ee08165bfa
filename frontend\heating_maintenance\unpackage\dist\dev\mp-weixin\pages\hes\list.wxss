/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.hes-list-container {
  padding: 20rpx;
  box-sizing: border-box;
  background-color: #f5f7fa;
  min-height: 100vh;
}
.search-bar {
  margin-bottom: 20rpx;
}
.search-bar .search-input-wrapper {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.search-bar .search-input-wrapper input {
  flex: 1;
  height: 40rpx;
  font-size: 28rpx;
  margin: 0 10rpx;
}
.search-bar .search-input-wrapper .iconfont {
  font-size: 32rpx;
  color: #999;
}
.filter-tabs {
  display: flex;
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 4rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.filter-tabs .filter-tab {
  flex: 1;
  text-align: center;
  padding: 16rpx 0;
  font-size: 28rpx;
  position: relative;
  color: #666;
  transition: all 0.3s;
  border-radius: 8rpx;
}
.filter-tabs .filter-tab.active {
  color: #fff;
  background-color: #1890ff;
  font-weight: 500;
}
.hes-modern-list .hes-card {
  border-radius: 12rpx;
  padding: 20rpx 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}
.hes-modern-list .hes-card.yellow-card {
  background-color: #ffd466;
  color: #333;
}
.hes-modern-list .hes-card.green-card {
  background-color: #2dcea3;
  color: #fff;
}
.hes-modern-list .hes-card.blue-card {
  background-color: #4fb5ee;
  color: #fff;
}
.hes-modern-list .hes-card.gray-card {
  background-color: #a0a0a0;
  color: #fff;
}
.hes-modern-list .hes-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}
.hes-modern-list .hes-card .card-header .station-name {
  font-size: 32rpx;
  font-weight: 600;
  max-width: 60%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.hes-modern-list .hes-card .card-header .connection-time {
  font-size: 24rpx;
  opacity: 0.9;
}
.hes-modern-list .hes-card .operation-mode {
  font-size: 28rpx;
  margin-bottom: 12rpx;
}
.hes-modern-list .hes-card .temperature-info {
  margin-bottom: 16rpx;
}
.hes-modern-list .hes-card .temperature-info .network-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.hes-modern-list .hes-card .temperature-info .network-row .network-label {
  font-size: 28rpx;
  min-width: 90rpx;
}
.hes-modern-list .hes-card .temperature-info .network-row .temp-value {
  font-size: 28rpx;
  font-weight: 500;
  margin-right: 20rpx;
}
.hes-modern-list .hes-card .temperature-info .network-row .second-label {
  margin-left: 10rpx;
}
.hes-modern-list .hes-card .card-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.hes-modern-list .hes-card .card-actions .status-badge {
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  background-color: rgba(255, 255, 255, 0.25);
}
.hes-modern-list .hes-card .card-actions .action-buttons {
  display: flex;
}
.hes-modern-list .hes-card .card-actions .action-buttons .action-btn {
  padding: 6rpx 30rpx;
  margin-left: 16rpx;
  font-size: 26rpx;
  border-radius: 30rpx;
  background-color: rgba(255, 255, 255, 0.25);
}
.load-more,
.no-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 26rpx;
  color: #999;
}