<view class="all-records-container"><view class="filter-bar"><view class="date-range-selector" bindtap="{{c}}"><text>{{a}}</text><uni-icons wx:if="{{b}}" u-i="1b919886-0" bind:__l="__l" u-p="{{b}}"></uni-icons></view><view class="staff-selector" bindtap="{{f}}"><text>{{d}}</text><uni-icons wx:if="{{e}}" u-i="1b919886-1" bind:__l="__l" u-p="{{e}}"></uni-icons></view><view class="status-selector" bindtap="{{i}}"><text>{{g}}</text><uni-icons wx:if="{{h}}" u-i="1b919886-2" bind:__l="__l" u-p="{{h}}"></uni-icons></view></view><view class="search-bar"><uni-icons wx:if="{{j}}" u-i="1b919886-3" bind:__l="__l" u-p="{{j}}"></uni-icons><input type="text" placeholder="搜索人员姓名" value="{{k}}" bindinput="{{l}}"/><text wx:if="{{m}}" bindtap="{{n}}" class="clear-btn">清除</text></view><view class="statistics-info"><text>共 {{o}} 条记录</text></view><view class="records-list"><view wx:if="{{p}}"><view wx:for="{{q}}" wx:for-item="group" wx:key="d" class="record-group"><view class="group-header"><text class="date">{{group.a}}</text><text class="week">{{group.b}}</text></view><view wx:for="{{group.c}}" wx:for-item="record" wx:key="k" class="record-item"><view class="staff-info"><image src="{{record.a}}" mode="aspectFill" class="avatar"></image><view class="staff-details"><text class="staff-name">{{record.b}}</text><text class="staff-dept">{{record.c}}</text></view></view><view class="record-details"><view class="time-record"><view class="time-item"><text class="time-label">上班打卡</text><text class="{{['time-value', record.e && 'abnormal']}}">{{record.d}}</text><text wx:if="{{record.f}}" class="status-tag late">迟到</text></view><view class="time-item"><text class="time-label">下班打卡</text><text class="{{['time-value', record.h && 'abnormal']}}">{{record.g}}</text><text wx:if="{{record.i}}" class="status-tag early">早退</text></view></view><view class="location-info"><text class="location-label">打卡地点</text><text class="location-value">{{record.j}}</text></view></view></view></view><view wx:if="{{r}}" class="load-more" bindtap="{{s}}"><text>加载更多</text></view><view wx:else class="no-more"><text>没有更多数据了</text></view></view><view wx:else class="empty-records"><image src="{{t}}" mode="aspectFit"></image><text>暂无考勤记录</text></view></view><uni-popup wx:if="{{G}}" class="r" u-s="{{['d']}}" u-r="datePopup" u-i="1b919886-4" bind:__l="__l" u-p="{{G}}"><view class="date-popup"><view class="popup-header"><text>选择日期范围</text><text bindtap="{{v}}" class="close-btn">关闭</text></view><view class="date-options"><view wx:for="{{w}}" wx:for-item="option" wx:key="e" class="{{['date-option', option.f && 'active']}}" bindtap="{{option.g}}"><text>{{option.a}}</text><uni-icons wx:if="{{option.b}}" u-i="{{option.c}}" bind:__l="__l" u-p="{{option.d}}"></uni-icons></view><view class="custom-date-range"><text>自定义日期范围</text><view class="date-inputs"><picker mode="date" value="{{y}}" bindchange="{{z}}" class="date-picker"><view class="date-input"><text>{{x}}</text></view></picker><text class="date-separator">至</text><picker mode="date" value="{{B}}" bindchange="{{C}}" class="date-picker"><view class="date-input"><text>{{A}}</text></view></picker></view><view class="custom-date-actions"><button class="btn-apply" bindtap="{{D}}" disabled="{{E}}">应用</button></view></view></view></view></uni-popup><uni-popup wx:if="{{Q}}" class="r" u-s="{{['d']}}" u-r="staffPopup" u-i="1b919886-6" bind:__l="__l" u-p="{{Q}}"><view class="staff-popup"><view class="popup-header"><text>选择人员</text><text bindtap="{{H}}" class="close-btn">关闭</text></view><view class="search-box"><uni-icons wx:if="{{I}}" u-i="1b919886-7,1b919886-6" bind:__l="__l" u-p="{{I}}"></uni-icons><input type="text" placeholder="搜索人员姓名" value="{{J}}" bindinput="{{K}}"/></view><scroll-view scroll-y="true" class="staff-list"><view class="staff-item" bindtap="{{N}}"><view class="staff-info"><text class="staff-name">全部人员</text></view><uni-icons wx:if="{{L}}" u-i="1b919886-8,1b919886-6" bind:__l="__l" u-p="{{M}}"></uni-icons></view><view wx:for="{{O}}" wx:for-item="staff" wx:key="e" class="staff-item" bindtap="{{staff.f}}"><view class="staff-info"><text class="staff-name">{{staff.a}}</text></view><uni-icons wx:if="{{staff.b}}" u-i="{{staff.c}}" bind:__l="__l" u-p="{{staff.d}}"></uni-icons></view></scroll-view></view></uni-popup><uni-popup wx:if="{{X}}" class="r" u-s="{{['d']}}" u-r="statusPopup" u-i="1b919886-10" bind:__l="__l" u-p="{{X}}"><view class="status-popup"><view class="popup-header"><text>选择状态</text><text bindtap="{{R}}" class="close-btn">关闭</text></view><view class="status-list"><view class="status-item" bindtap="{{U}}"><text>全部状态</text><uni-icons wx:if="{{S}}" u-i="1b919886-11,1b919886-10" bind:__l="__l" u-p="{{T}}"></uni-icons></view><view wx:for="{{V}}" wx:for-item="status" wx:key="e" class="status-item" bindtap="{{status.f}}"><text>{{status.a}}</text><uni-icons wx:if="{{status.b}}" u-i="{{status.c}}" bind:__l="__l" u-p="{{status.d}}"></uni-icons></view></view></view></uni-popup></view>