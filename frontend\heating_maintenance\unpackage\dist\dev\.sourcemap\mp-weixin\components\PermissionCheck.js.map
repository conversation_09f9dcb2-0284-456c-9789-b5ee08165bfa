{"version": 3, "file": "PermissionCheck.js", "sources": ["components/PermissionCheck.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RTovdGFpYm9fY29tcGFueS90Yl9wcm9qZWN0L3NoYWFueGlfamllbWluZ19uZXdfZW5lcmd5X2NvbXBhbnkvNC1Tb3VyY2UvYXBwL2Zyb250ZW5kL2hlYXRpbmdfbWFpbnRlbmFuY2UvY29tcG9uZW50cy9QZXJtaXNzaW9uQ2hlY2sudnVl"], "sourcesContent": ["<template>\r\n  <!-- 有权限时渲染内容 -->\r\n  <template v-if=\"hasRequiredPermission\">\r\n    <slot></slot>\r\n  </template>\r\n\r\n  <!-- 无权限且需要显示提示时 -->\r\n  <template v-else-if=\"showTip\">\r\n    <view class=\"no-permission-item\">\r\n      <text class=\"no-permission-text\">无权限</text>\r\n    </view>\r\n  </template>\r\n\r\n  <!-- 无权限且不需要提示时什么都不渲染 -->\r\n</template>\r\n\r\n<script>\r\nimport { hasPermission } from \"@/utils/auth.js\";\r\n\r\nexport default {\r\n  name: \"PermissionCheck\",\r\n  // 移除函数式组件标记，使用普通组件以确保正确渲染\r\n  // functional: true,\r\n  props: {\r\n    // 必需的权限编码，可以是单个权限编码字符串或权限编码数组（满足其中一个即可）\r\n    permission: {\r\n      type: [String, Array],\r\n      required: true,\r\n    },\r\n    // 是否显示无权限提示，默认不显示\r\n    showTip: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      // 在这里可以添加组件状态\r\n    };\r\n  },\r\n  computed: {\r\n    hasRequiredPermission() {\r\n      const result = Array.isArray(this.permission)\r\n        ? this.permission.some((code) => hasPermission(code))\r\n        : hasPermission(this.permission);\r\n     // console.log(\"检查结果:\", result ? \"有权限\" : \"无权限\");\r\n      return result;\r\n    },\r\n  },\r\n  mounted() {\r\n   // console.log(\"PermissionCheck组件已挂载:\", this.permission);\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.no-permission-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  opacity: 0.5;\r\n}\r\n\r\n.no-permission-text {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  text-align: center;\r\n}\r\n\r\n/* 确保permission-check组件继承其父容器的布局特性 */\r\n:deep(.permission-check) {\r\n  display: inherit;\r\n  width: inherit;\r\n  height: inherit;\r\n  flex: inherit;\r\n  position: inherit;\r\n}\r\n</style>\r\n", "import Component from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/components/PermissionCheck.vue'\nwx.createComponent(Component)"], "names": ["hasPermission"], "mappings": ";;;AAmBA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA;AAAA;AAAA,EAGN,OAAO;AAAA;AAAA,IAEL,YAAY;AAAA,MACV,MAAM,CAAC,QAAQ,KAAK;AAAA,MACpB,UAAU;AAAA,IACX;AAAA;AAAA,IAED,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,EACF;AAAA,EACD,OAAO;AACL,WAAO;AAAA;AAAA;EAGR;AAAA,EACD,UAAU;AAAA,IACR,wBAAwB;AACtB,YAAM,SAAS,MAAM,QAAQ,KAAK,UAAU,IACxC,KAAK,WAAW,KAAK,CAAC,SAASA,WAAAA,cAAc,IAAI,CAAC,IAClDA,yBAAc,KAAK,UAAU;AAEjC,aAAO;AAAA,IACR;AAAA,EACF;AAAA,EACD,UAAU;AAAA,EAET;AACH;;;;;;;;;ACnDA,GAAG,gBAAgB,SAAS;"}