{"version": 3, "file": "all-records.js", "sources": ["pages/attendance/all-records.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYXR0ZW5kYW5jZS9hbGwtcmVjb3Jkcy52dWU"], "sourcesContent": ["<template>\n\t<view class=\"all-records-container\">\n\t\t<!-- 顶部筛选栏 -->\n\t\t<view class=\"filter-bar\">\n\t\t\t<view class=\"date-range-selector\" @click=\"openDatePicker\">\n\t\t\t\t<text>{{ displayDateRange }}</text>\n\t\t\t\t<uni-icons type=\"bottom\" size=\"14\" color=\"#666\"></uni-icons>\n\t\t\t</view>\n\t\t\t<view class=\"staff-selector\" @click=\"openStaffSelector\">\n\t\t\t\t<text>{{ selectedStaff ? selectedStaff.name : '全部人员' }}</text>\n\t\t\t\t<uni-icons type=\"bottom\" size=\"14\" color=\"#666\"></uni-icons>\n\t\t\t</view>\n\t\t\t<view class=\"status-selector\" @click=\"openStatusSelector\">\n\t\t\t\t<text>{{ selectedStatus ? statusTypes[selectedStatus].label : '全部状态' }}</text>\n\t\t\t\t<uni-icons type=\"bottom\" size=\"14\" color=\"#666\"></uni-icons>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 搜索栏 -->\n\t\t<view class=\"search-bar\">\n\t\t\t<uni-icons type=\"search\" size=\"16\" color=\"#999\"></uni-icons>\n\t\t\t<input type=\"text\" v-model=\"searchKeyword\" placeholder=\"搜索人员姓名\" />\n\t\t\t<text v-if=\"searchKeyword\" @click=\"clearSearch\" class=\"clear-btn\">清除</text>\n\t\t</view>\n\t\t\n\t\t<!-- 统计信息 -->\n\t\t<view class=\"statistics-info\">\n\t\t\t<text>共 {{ totalRecords }} 条记录</text>\n\t\t\t<!-- <view class=\"status-count\">\n\t\t\t\t<view class=\"status-item\">\n\t\t\t\t\t<text class=\"normal\">正常: {{ statistics.normalCount }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"status-item\">\n\t\t\t\t\t<text class=\"late\">迟到: {{ statistics.lateCount }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"status-item\">\n\t\t\t\t\t<text class=\"early\">早退: {{ statistics.earlyCount }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"status-item\">\n\t\t\t\t\t<text class=\"absent\">缺勤: {{ statistics.absentCount }}</text>\n\t\t\t\t</view>\n\t\t\t</view> -->\n\t\t</view>\n\t\t\n\t\t<!-- 记录列表 -->\n\t\t<view class=\"records-list\">\n\t\t\t<view v-if=\"records.length > 0\">\n\t\t\t\t<view v-for=\"(group, index) in groupedRecords\" :key=\"index\" class=\"record-group\">\n\t\t\t\t\t<view class=\"group-header\">\n\t\t\t\t\t\t<text class=\"date\">{{ group.date }}</text>\n\t\t\t\t\t\t<text class=\"week\">{{ group.week }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-for=\"(record, recordIndex) in group.records\" :key=\"recordIndex\" class=\"record-item\">\n\t\t\t\t\t\t<view class=\"staff-info\">\n\t\t\t\t\t\t\t<image :src=\"record.avatar\" mode=\"aspectFill\" class=\"avatar\"></image>\n\t\t\t\t\t\t\t<view class=\"staff-details\">\n\t\t\t\t\t\t\t\t<text class=\"staff-name\">{{ record.staffName }}</text>\n\t\t\t\t\t\t\t\t<text class=\"staff-dept\">{{ record.department }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"record-details\">\n\t\t\t\t\t\t\t<view class=\"time-record\">\n\t\t\t\t\t\t\t\t<view class=\"time-item\">\n\t\t\t\t\t\t\t\t\t<text class=\"time-label\">上班打卡</text>\n\t\t\t\t\t\t\t\t\t<text class=\"time-value\" :class=\"{'abnormal': record.clockInStatus !== 'normal'}\">\n\t\t\t\t\t\t\t\t\t\t{{ record.clockInTime }}\n\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t\t<text v-if=\"record.clockInStatus === 'late'\" class=\"status-tag late\">迟到</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"time-item\">\n\t\t\t\t\t\t\t\t\t<text class=\"time-label\">下班打卡</text>\n\t\t\t\t\t\t\t\t\t<text class=\"time-value\" :class=\"{'abnormal': record.clockOutStatus !== 'normal'}\">\n\t\t\t\t\t\t\t\t\t\t{{ record.clockOutTime }}\n\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t\t<text v-if=\"record.clockOutStatus === 'early'\" class=\"status-tag early\">早退</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"location-info\">\n\t\t\t\t\t\t\t\t<text class=\"location-label\">打卡地点</text>\n\t\t\t\t\t\t\t\t<text class=\"location-value\">{{ record.location }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 加载更多 -->\n\t\t\t\t<view v-if=\"hasMoreData\" class=\"load-more\" @click=\"loadMoreRecords\">\n\t\t\t\t\t<text>加载更多</text>\n\t\t\t\t</view>\n\t\t\t\t<view v-else class=\"no-more\">\n\t\t\t\t\t<text>没有更多数据了</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view v-else class=\"empty-records\">\n\t\t\t\t<image src=\"/static/images/empty.png\" mode=\"aspectFit\"></image>\n\t\t\t\t<text>暂无考勤记录</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 日期选择器弹窗 -->\n\t\t<uni-popup ref=\"datePopup\" type=\"bottom\">\n\t\t\t<view class=\"date-popup\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<text>选择日期范围</text>\n\t\t\t\t\t<text @click=\"closeDatePicker\" class=\"close-btn\">关闭</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"date-options\">\n\t\t\t\t\t<view \n\t\t\t\t\t\tv-for=\"(option, index) in dateOptions\" \n\t\t\t\t\t\t:key=\"index\" \n\t\t\t\t\t\tclass=\"date-option\" \n\t\t\t\t\t\t:class=\"{ active: dateRange === option.value }\"\n\t\t\t\t\t\t@click=\"selectDateRange(option.value)\">\n\t\t\t\t\t\t<text>{{ option.label }}</text>\n\t\t\t\t\t\t<uni-icons v-if=\"dateRange === option.value\" type=\"checkmarkempty\" size=\"18\" color=\"#007AFF\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"custom-date-range\">\n\t\t\t\t\t\t<text>自定义日期范围</text>\n\t\t\t\t\t\t<view class=\"date-inputs\">\n\t\t\t\t\t\t\t<picker mode=\"date\" :value=\"customDateRange.start\" @change=\"onStartDateChange\" class=\"date-picker\">\n\t\t\t\t\t\t\t\t<view class=\"date-input\">\n\t\t\t\t\t\t\t\t\t<text>{{ customDateRange.start ? customDateRange.start : '开始日期' }}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t\t<text class=\"date-separator\">至</text>\n\t\t\t\t\t\t\t<picker mode=\"date\" :value=\"customDateRange.end\" @change=\"onEndDateChange\" class=\"date-picker\">\n\t\t\t\t\t\t\t\t<view class=\"date-input\">\n\t\t\t\t\t\t\t\t\t<text>{{ customDateRange.end ? customDateRange.end : '结束日期' }}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"custom-date-actions\">\n\t\t\t\t\t\t\t<button class=\"btn-apply\" @click=\"applyCustomDateRange\" :disabled=\"!isCustomDateRangeValid\">应用</button>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t\t\n\t\t<!-- 人员选择器弹窗 -->\n\t\t<uni-popup ref=\"staffPopup\" type=\"bottom\">\n\t\t\t<view class=\"staff-popup\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<text>选择人员</text>\n\t\t\t\t\t<text @click=\"closeStaffSelector\" class=\"close-btn\">关闭</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"search-box\">\n\t\t\t\t\t<uni-icons type=\"search\" size=\"16\" color=\"#999\"></uni-icons>\n\t\t\t\t\t<input type=\"text\" v-model=\"staffSearchKeyword\" placeholder=\"搜索人员姓名\" />\n\t\t\t\t</view>\n\t\t\t\t<scroll-view scroll-y=\"true\" class=\"staff-list\">\n\t\t\t\t\t<view class=\"staff-item\" @click=\"selectStaff(null)\">\n\t\t\t\t\t\t<view class=\"staff-info\">\n\t\t\t\t\t\t\t<text class=\"staff-name\">全部人员</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<uni-icons v-if=\"!selectedStaff\" type=\"checkmarkempty\" size=\"18\" color=\"#007AFF\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view \n\t\t\t\t\t\tv-for=\"(staff, index) in filteredStaffList\" \n\t\t\t\t\t\t:key=\"index\" \n\t\t\t\t\t\tclass=\"staff-item\"\n\t\t\t\t\t\t@click=\"selectStaff(staff)\">\n\t\t\t\t\t\t<view class=\"staff-info\">\n\t\t\t\t\t\t\t<text class=\"staff-name\">{{ staff.name }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<uni-icons \n\t\t\t\t\t\t\tv-if=\"selectedStaff && selectedStaff.id === staff.id\" \n\t\t\t\t\t\t\ttype=\"checkmarkempty\" \n\t\t\t\t\t\t\tsize=\"18\" \n\t\t\t\t\t\t\tcolor=\"#007AFF\">\n\t\t\t\t\t\t</uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t\t\n\t\t<!-- 状态选择器弹窗 -->\n\t\t<uni-popup ref=\"statusPopup\" type=\"bottom\">\n\t\t\t<view class=\"status-popup\">\n\t\t\t\t<view class=\"popup-header\">\n\t\t\t\t\t<text>选择状态</text>\n\t\t\t\t\t<text @click=\"closeStatusSelector\" class=\"close-btn\">关闭</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"status-list\">\n\t\t\t\t\t<view class=\"status-item\" @click=\"selectStatus(null)\">\n\t\t\t\t\t\t<text>全部状态</text>\n\t\t\t\t\t\t<uni-icons v-if=\"!selectedStatus\" type=\"checkmarkempty\" size=\"18\" color=\"#007AFF\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view \n\t\t\t\t\t\tv-for=\"(status, key) in statusTypes\" \n\t\t\t\t\t\t:key=\"key\"\n\t\t\t\t\t\tclass=\"status-item\"\n\t\t\t\t\t\t@click=\"selectStatus(key)\">\n\t\t\t\t\t\t<text>{{ status.label }}</text>\n\t\t\t\t\t\t<uni-icons \n\t\t\t\t\t\t\tv-if=\"selectedStatus === key\" \n\t\t\t\t\t\t\ttype=\"checkmarkempty\" \n\t\t\t\t\t\t\tsize=\"18\" \n\t\t\t\t\t\t\tcolor=\"#007AFF\">\n\t\t\t\t\t\t</uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t</view>\n</template>\n\n<script>\nimport { attendanceApi, userApi } from '@/utils/api.js';\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tdateRange: 'thisMonth',\n\t\t\tcustomDateRange: {\n\t\t\t\tstart: '',\n\t\t\t\tend: ''\n\t\t\t},\n\t\t\tdateOptions: [\n\t\t\t\t{ label: '本月', value: 'thisMonth' },\n\t\t\t\t{ label: '上月', value: 'lastMonth' },\n\t\t\t\t{ label: '近7天', value: 'last7Days' },\n\t\t\t\t{ label: '近30天', value: 'last30Days' }\n\t\t\t],\n\t\t\tselectedStaff: null,\n\t\t\tstaffSearchKeyword: '',\n\t\t\tstaffList: [],\n\t\t\tselectedStatus: null,\n\t\t\tstatusTypes: {\n\t\t\t\tnormal: { label: '正常', color: '#4cd964' },\n\t\t\t\tlate: { label: '迟到', color: '#f0ad4e' },\n\t\t\t\tearly: { label: '早退', color: '#5bc0de' },\n\t\t\t\tabsent: { label: '缺勤', color: '#dd524d' }\n\t\t\t},\n\t\t\tsearchKeyword: '',\n\t\t\tcurrentDatePickerType: 'start',\n\t\t\tpage: 1,\n\t\t\tpageSize: 20,\n\t\t\thasMoreData: true,\n\t\t\ttotalRecords: 0,\n\t\t\tstatistics: {\n\t\t\t\tnormalCount: 0,\n\t\t\t\tlateCount: 0,\n\t\t\t\tearlyCount: 0,\n\t\t\t\tabsentCount: 0\n\t\t\t},\n\t\t\trecords: [],\n\t\t\tisLoading: false\n\t\t};\n\t},\n\tcomputed: {\n\t\tdisplayDateRange() {\n\t\t\tswitch (this.dateRange) {\n\t\t\t\tcase 'thisMonth':\n\t\t\t\t\treturn '本月';\n\t\t\t\tcase 'lastMonth':\n\t\t\t\t\treturn '上月';\n\t\t\t\tcase 'last7Days':\n\t\t\t\t\treturn '近7天';\n\t\t\t\tcase 'last30Days':\n\t\t\t\t\treturn '近30天';\n\t\t\t\tcase 'custom':\n\t\t\t\t\treturn `${this.customDateRange.start} 至 ${this.customDateRange.end}`;\n\t\t\t\tdefault:\n\t\t\t\t\treturn '选择日期';\n\t\t\t}\n\t\t},\n\t\tfilteredStaffList() {\n\t\t\tif (!this.staffSearchKeyword) return this.staffList;\n\t\t\t\n\t\t\treturn this.staffList.filter(staff => \n\t\t\t\tstaff.name.includes(this.staffSearchKeyword) || \n\t\t\t\t(staff.department && staff.department.includes(this.staffSearchKeyword))\n\t\t\t);\n\t\t},\n\t\tgroupedRecords() {\n\t\t\tconst groups = {};\n\t\t\t\n\t\t\tthis.records.forEach(record => {\n\t\t\t\tif (!groups[record.date]) {\n\t\t\t\t\tgroups[record.date] = {\n\t\t\t\t\t\tdate: record.date,\n\t\t\t\t\t\tweek: record.week,\n\t\t\t\t\t\trecords: []\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tgroups[record.date].records.push(record);\n\t\t\t});\n\t\t\t\n\t\t\treturn Object.values(groups);\n\t\t},\n\t\tisCustomDateRangeValid() {\n\t\t\treturn this.customDateRange.start && this.customDateRange.end;\n\t\t}\n\t},\n\tonLoad() {\n\t\tthis.loadStaffList();\n\t\tthis.loadRecords();\n\t},\n\tmethods: {\n\t\t// 加载员工列表\n\t\tloadStaffList() {\n\t\t\t// 先尝试使用考勤API获取员工列表\n\t\t\tattendanceApi.getAllStaff().then(res => {\n\t\t\t\tconsole.log('获取员工列表结果:', res);\n\t\t\t\tif (res.code === 200 && res.data && res.data.length > 0) {\n\t\t\t\t\tthis.staffList = res.data;\n\t\t\t\t} else {\n\t\t\t\t\t// 如果考勤API没有返回员工列表或返回空列表，尝试使用用户API\n\t\t\t\t\tthis.loadStaffListFallback();\n\t\t\t\t}\n\t\t\t}).catch(err => {\n\t\t\t\tconsole.error('获取员工列表失败:', err);\n\t\t\t\t// 降级方案：使用用户API获取人员列表\n\t\t\t\tthis.loadStaffListFallback();\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 降级方案：使用用户API获取员工列表\n\t\tloadStaffListFallback() {\n\t\t\tuserApi.getInspectorList().then(userRes => {\n\t\t\t\tconsole.log('降级获取员工列表结果:', userRes);\n\t\t\t\tif (userRes.code === 200 && userRes.data) {\n\t\t\t\t\t// 转换数据结构以匹配考勤API的格式\n\t\t\t\t\tthis.staffList = userRes.data.map(user => ({\n\t\t\t\t\t\tid: user.id,\n\t\t\t\t\t\tname: user.name,\n\t\t\t\t\t\tdepartment: user.department || '未知部门'\n\t\t\t\t\t}));\n\t\t\t\t} else {\n\t\t\t\t\t// 如果API都失败，使用默认数据\n\t\t\t\t\tthis.staffList = [\n\t\t\t\t\t\t{ id: '001', name: '张工', department: '运维部' },\n\t\t\t\t\t\t{ id: '002', name: '李工', department: '工程部' },\n\t\t\t\t\t\t{ id: '003', name: '王工', department: '管理部' },\n\t\t\t\t\t\t{ id: '004', name: '赵工', department: '技术部' },\n\t\t\t\t\t\t{ id: '005', name: '刘工', department: '客服部' }\n\t\t\t\t\t];\n\t\t\t\t}\n\t\t\t}).catch(err => {\n\t\t\t\tconsole.error('降级获取员工列表失败:', err);\n\t\t\t\t// 使用默认数据\n\t\t\t\tthis.staffList = [\n\t\t\t\t\t{ id: '001', name: '张工', department: '运维部' },\n\t\t\t\t\t{ id: '002', name: '李工', department: '工程部' },\n\t\t\t\t\t{ id: '003', name: '王工', department: '管理部' },\n\t\t\t\t\t{ id: '004', name: '赵工', department: '技术部' },\n\t\t\t\t\t{ id: '005', name: '刘工', department: '客服部' }\n\t\t\t\t];\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 获取日期范围参数\n\t\tgetDateRangeParams() {\n\t\t\tconst now = new Date();\n\t\t\tconst year = now.getFullYear();\n\t\t\tconst month = now.getMonth() + 1;\n\t\t\tconst day = now.getDate();\n\t\t\t\n\t\t\tlet startDate = '';\n\t\t\tlet endDate = '';\n\t\t\t\n\t\t\tswitch (this.dateRange) {\n\t\t\t\tcase 'thisMonth':\n\t\t\t\t\tstartDate = `${year}-${String(month).padStart(2, '0')}-01`;\n\t\t\t\t\tendDate = this.getLastDayOfMonth(year, month);\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'lastMonth':\n\t\t\t\t\tconst lastMonth = month === 1 ? 12 : month - 1;\n\t\t\t\t\tconst lastMonthYear = month === 1 ? year - 1 : year;\n\t\t\t\t\tstartDate = `${lastMonthYear}-${String(lastMonth).padStart(2, '0')}-01`;\n\t\t\t\t\tendDate = this.getLastDayOfMonth(lastMonthYear, lastMonth);\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'last7Days':\n\t\t\t\t\tconst last7Date = new Date(now);\n\t\t\t\t\tlast7Date.setDate(day - 6);\n\t\t\t\t\tstartDate = this.formatDate(last7Date);\n\t\t\t\t\tendDate = this.formatDate(now);\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'last30Days':\n\t\t\t\t\tconst last30Date = new Date(now);\n\t\t\t\t\tlast30Date.setDate(day - 29);\n\t\t\t\t\tstartDate = this.formatDate(last30Date);\n\t\t\t\t\tendDate = this.formatDate(now);\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'custom':\n\t\t\t\t\tstartDate = this.customDateRange.start;\n\t\t\t\t\tendDate = this.customDateRange.end;\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t\t\n\t\t\treturn { startDate, endDate };\n\t\t},\n\t\t\n\t\t// 获取月份的最后一天\n\t\tgetLastDayOfMonth(year, month) {\n\t\t\tconst lastDay = new Date(year, month, 0).getDate();\n\t\t\treturn `${year}-${String(month).padStart(2, '0')}-${String(lastDay).padStart(2, '0')}`;\n\t\t},\n\t\t\n\t\t// 格式化日期为YYYY-MM-DD\n\t\tformatDate(date) {\n\t\t\tconst year = date.getFullYear();\n\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\n\t\t\tconst day = String(date.getDate()).padStart(2, '0');\n\t\t\treturn `${year}-${month}-${day}`;\n\t\t},\n\t\t\n\t\t// 加载考勤记录\n\t\tloadRecords() {\n\t\t\tif (this.isLoading) return;\n\t\t\t\n\t\t\tthis.isLoading = true;\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '加载数据中...'\n\t\t\t});\n\t\t\t\n\t\t\t// 准备查询参数\n\t\t\tconst { startDate, endDate } = this.getDateRangeParams();\n\t\t\t\n\t\t\tconst params = {\n\t\t\t\tpage: this.page,\n\t\t\t\tpageSize: this.pageSize,\n\t\t\t\tstartDate,\n\t\t\t\tendDate\n\t\t\t};\n\t\t\t\n\t\t\tconsole.log('查询参数:', params);\n\t\t\t\n\t\t\t// 添加人员ID参数\n\t\t\tif (this.selectedStaff) {\n\t\t\t\tparams.userId = this.selectedStaff.id;\n\t\t\t}\n\t\t\t\n\t\t\t// 添加状态参数\n\t\t\tif (this.selectedStatus) {\n\t\t\t\tparams.status = this.selectedStatus;\n\t\t\t}\n\t\t\t\n\t\t\t// 添加搜索关键词\n\t\t\tif (this.searchKeyword) {\n\t\t\t\tparams.keyword = this.searchKeyword;\n\t\t\t}\n\t\t\t\n\t\t\t// 调用考勤API获取记录\n\t\t\tattendanceApi.getRecords(params).then(res => {\n\t\t\t\tif (res.code === 200 && res.data) {\n\t\t\t\t\t// 解析返回的数据\n\t\t\t\t\tconst summary = res.data.summary || {};\n\t\t\t\t\tthis.statistics = {\n\t\t\t\t\t\tnormalCount: summary.workdays ? (summary.workdays - summary.late - summary.absent - summary.early) : 0,\n\t\t\t\t\t\tlateCount: summary.late || 0,\n\t\t\t\t\t\tearlyCount: summary.early || 0,\n\t\t\t\t\t\tabsentCount: summary.absent || 0\n\t\t\t\t\t};\n\t\t\t\t\t\n\t\t\t\t\tthis.totalRecords = res.data.records ? res.data.records.length : 0;\n\t\t\t\t\t\n\t\t\t\t\t// 格式化记录数据\n\t\t\t\t\tconst formattedRecords = this.processAttendanceRecords(res.data.records || []);\n\t\t\t\t\t\n\t\t\t\t\t// 第一页数据直接赋值，加载更多时追加\n\t\t\t\t\tif (this.page === 1) {\n\t\t\t\t\t\tthis.records = formattedRecords;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.records = [...this.records, ...formattedRecords];\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 判断是否还有更多数据\n\t\t\t\t\tthis.hasMoreData = formattedRecords.length === this.pageSize;\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.message || '获取考勤记录失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}).catch(err => {\n\t\t\t\tconsole.error('获取考勤记录失败:', err);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '获取考勤记录失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}).finally(() => {\n\t\t\t\tuni.hideLoading();\n\t\t\t\tthis.isLoading = false;\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 处理考勤记录数据\n\t\tprocessAttendanceRecords(records) {\n\t\t\tif (!Array.isArray(records) || records.length === 0) {\n\t\t\t\treturn [];\n\t\t\t}\n\t\t\t\n\t\t\t// 检查记录格式并适配\n\t\t\tconst firstRecord = records[0];\n\t\t\t\n\t\t\t// 如果记录已包含上下班打卡时间字段，直接使用\n\t\t\tif (firstRecord.clockInTime !== undefined || firstRecord.clockOutTime !== undefined) {\n\t\t\t\treturn records.map(item => {\n\t\t\t\t\t// 获取用户信息\n\t\t\t\t\tconst user = item.user || {};\n\t\t\t\t\t\n\t\t\t\t\treturn {\n\t\t\t\t\t\tid: item.id,\n\t\t\t\t\t\tdate: item.date || this.formatDateFromString(item.clockTime || item.createTime),\n\t\t\t\t\t\tweek: item.week || this.formatWeekFromString(item.clockTime || item.createTime),\n\t\t\t\t\t\tstaffName: user.name || '未知',\n\t\t\t\t\t\tdepartment: user.department || '未知部门',\n\t\t\t\t\t\tavatar: user.avatar || '/static/images/avatar.jpg',\n\t\t\t\t\t\tclockInTime: item.clockInTime || '未打卡',\n\t\t\t\t\t\tclockInStatus: item.clockInStatus || 'normal',\n\t\t\t\t\t\tclockOutTime: item.clockOutTime || '未打卡',\n\t\t\t\t\t\tclockOutStatus: item.clockOutStatus || 'normal',\n\t\t\t\t\t\tlocation: item.location || this.formatLocation(item)\n\t\t\t\t\t};\n\t\t\t\t});\n\t\t\t}\n\t\t\t\n\t\t\t// 处理旧格式数据（向后兼容）\n\t\t\t// 按日期和用户ID分组\n\t\t\tconst recordsByDateAndUser = {};\n\t\t\t\n\t\t\trecords.forEach(record => {\n\t\t\t\tif (!record.clockTime) {\n\t\t\t\t\tconsole.warn('记录缺少clockTime字段:', record);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconst date = new Date(record.clockTime);\n\t\t\t\tconst dateStr = date.toISOString().split('T')[0]; // YYYY-MM-DD\n\t\t\t\tconst userId = record.userId || (record.user ? record.user.id : 'unknown');\n\t\t\t\tconst key = `${dateStr}-${userId}`;\n\t\t\t\t\n\t\t\t\tif (!recordsByDateAndUser[key]) {\n\t\t\t\t\t// 获取用户信息\n\t\t\t\t\tconst user = record.user || {};\n\t\t\t\t\t\n\t\t\t\t\trecordsByDateAndUser[key] = {\n\t\t\t\t\t\tid: record.id,\n\t\t\t\t\t\tdate: this.formatDateFromString(record.clockTime),\n\t\t\t\t\t\tweek: this.formatWeekFromString(record.clockTime),\n\t\t\t\t\t\tstaffName: user.name || record.userName || '未知',\n\t\t\t\t\t\tdepartment: user.department || record.department || '未知部门',\n\t\t\t\t\t\tavatar: user.avatar || record.avatar || '/static/images/avatar.jpg',\n\t\t\t\t\t\tclockInTime: '未打卡',\n\t\t\t\t\t\tclockOutTime: '未打卡',\n\t\t\t\t\t\tclockInStatus: 'normal',\n\t\t\t\t\t\tclockOutStatus: 'normal',\n\t\t\t\t\t\tuserId: userId,\n\t\t\t\t\t\tlocation: record.location || (record.latitude && record.longitude ? \n\t\t\t\t\t\t\t`${record.latitude.toFixed(6)}, ${record.longitude.toFixed(6)}` : '未知位置')\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 根据打卡类型设置上下班时间\n\t\t\t\tif (record.clockType === 'checkin') {\n\t\t\t\t\trecordsByDateAndUser[key].clockInTime = this.formatTime(record.clockTime);\n\t\t\t\t\trecordsByDateAndUser[key].clockInStatus = record.status || 'normal';\n\t\t\t\t} else if (record.clockType === 'checkout') {\n\t\t\t\t\trecordsByDateAndUser[key].clockOutTime = this.formatTime(record.clockTime);\n\t\t\t\t\trecordsByDateAndUser[key].clockOutStatus = record.status || 'normal';\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\t// 转换为数组并返回\n\t\t\treturn Object.values(recordsByDateAndUser);\n\t\t},\n\t\t\n\t\t// 格式化时间\n\t\tformatTime(timeString) {\n\t\t\tif (!timeString) return '未打卡';\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst date = new Date(timeString);\n\t\t\t\tconst hours = String(date.getHours()).padStart(2, '0');\n\t\t\t\tconst minutes = String(date.getMinutes()).padStart(2, '0');\n\t\t\t\treturn `${hours}:${minutes}`;\n\t\t\t} catch (e) {\n\t\t\t\treturn timeString;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 加载更多记录\n\t\tloadMoreRecords() {\n\t\t\tif (!this.hasMoreData || this.isLoading) return;\n\t\t\t\n\t\t\tthis.page++;\n\t\t\tthis.loadRecords();\n\t\t},\n\t\t\n\t\t// 搜索相关\n\t\tclearSearch() {\n\t\t\tthis.searchKeyword = '';\n\t\t\tthis.page = 1;\n\t\t\tthis.loadRecords();\n\t\t},\n\t\t\n\t\t// 日期选择相关\n\t\topenDatePicker() {\n\t\t\tthis.$refs.datePopup.open();\n\t\t},\n\t\t\n\t\tcloseDatePicker() {\n\t\t\tthis.$refs.datePopup.close();\n\t\t},\n\t\t\n\t\tselectDateRange(range) {\n\t\t\tthis.dateRange = range;\n\t\t\tthis.closeDatePicker();\n\t\t\tthis.page = 1;\n\t\t\tthis.loadRecords();\n\t\t},\n\t\t\n\t\tonStartDateChange(e) {\n\t\t\tthis.customDateRange.start = e.detail.value;\n\t\t},\n\t\t\n\t\tonEndDateChange(e) {\n\t\t\tthis.customDateRange.end = e.detail.value;\n\t\t},\n\t\t\n\t\tapplyCustomDateRange() {\n\t\t\tthis.dateRange = 'custom';\n\t\t\tthis.closeDatePicker();\n\t\t\tthis.page = 1;\n\t\t\tthis.loadRecords();\n\t\t},\n\t\t\n\t\t// 人员选择相关\n\t\topenStaffSelector() {\n\t\t\tthis.$refs.staffPopup.open();\n\t\t},\n\t\t\n\t\tcloseStaffSelector() {\n\t\t\tthis.$refs.staffPopup.close();\n\t\t},\n\t\t\n\t\tselectStaff(staff) {\n\t\t\tthis.selectedStaff = staff;\n\t\t\tthis.closeStaffSelector();\n\t\t\tthis.page = 1;\n\t\t\tthis.loadRecords();\n\t\t},\n\t\t\n\t\t// 状态选择相关\n\t\topenStatusSelector() {\n\t\t\tthis.$refs.statusPopup.open();\n\t\t},\n\t\t\n\t\tcloseStatusSelector() {\n\t\t\tthis.$refs.statusPopup.close();\n\t\t},\n\t\t\n\t\tselectStatus(status) {\n\t\t\tthis.selectedStatus = status;\n\t\t\tthis.closeStatusSelector();\n\t\t\tthis.page = 1;\n\t\t\tthis.loadRecords();\n\t\t},\n\t\t\n\t\t// 从字符串格式化日期为YYYY-MM-DD\n\t\tformatDateFromString(dateString) {\n\t\t\tif (!dateString) return '';\n\t\t\ttry {\n\t\t\t\tconst date = new Date(dateString);\n\t\t\t\treturn this.formatDate(date);\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('日期格式化错误:', e);\n\t\t\t\treturn '';\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 从字符串格式化星期\n\t\tformatWeekFromString(dateString) {\n\t\t\tif (!dateString) return '';\n\t\t\ttry {\n\t\t\t\tconst date = new Date(dateString);\n\t\t\t\tconst weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];\n\t\t\t\treturn weekDays[date.getDay()];\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('星期格式化错误:', e);\n\t\t\t\treturn '';\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 格式化位置信息\n\t\tformatLocation(record) {\n\t\t\tif (!record) return '未知位置';\n\t\t\tif (record.location) return record.location;\n\t\t\tif (record.latitude && record.longitude) {\n\t\t\t\treturn `${record.latitude.toFixed(6)}, ${record.longitude.toFixed(6)}`;\n\t\t\t}\n\t\t\treturn '未知位置';\n\t\t}\n\t}\n};\n</script>\n\n<style lang=\"scss\">\n.all-records-container {\n\tpadding: 20rpx;\n\tbackground-color: #f5f5f5;\n\tmin-height: 100vh;\n}\n\n// 顶部筛选栏\n.filter-bar {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\tpadding: 20rpx 0;\n\t\n\t.date-range-selector, .staff-selector, .status-selector {\n\t\tflex: 1;\n\t\tbackground-color: #fff;\n\t\tpadding: 15rpx 20rpx;\n\t\tborder-radius: 10rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tfont-size: 26rpx;\n\t\tmargin-right: 10rpx;\n\t\tbox-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);\n\t\t\n\t\t&:last-child {\n\t\t\tmargin-right: 0;\n\t\t}\n\t\t\n\t\ttext {\n\t\t\twidth: 80%;\n\t\t\twhite-space: nowrap;\n\t\t\toverflow: hidden;\n\t\t\ttext-overflow: ellipsis;\n\t\t}\n\t}\n}\n\n// 搜索栏\n.search-bar {\n\tbackground-color: #fff;\n\tborder-radius: 10rpx;\n\tpadding: 15rpx 20rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 20rpx;\n\tbox-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);\n\t\n\tinput {\n\t\tflex: 1;\n\t\theight: 60rpx;\n\t\tmargin: 0 10rpx;\n\t\tfont-size: 28rpx;\n\t}\n\t\n\t.clear-btn {\n\t\tfont-size: 26rpx;\n\t\tcolor: #999;\n\t}\n}\n\n// 统计信息\n.statistics-info {\n\tbackground-color: #fff;\n\tborder-radius: 10rpx;\n\tpadding: 20rpx;\n\tmargin-bottom: 20rpx;\n\tdisplay: flex;\n\tflex-direction: column;\n\tbox-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);\n\t\n\ttext {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tmargin-bottom: 10rpx;\n\t}\n\t\n\t.status-count {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\t\n\t\t.status-item {\n\t\t\tmargin-right: 20rpx;\n\t\t\t\n\t\t\ttext {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\n\t\t\t\t&.normal {\n\t\t\t\t\tcolor: #4cd964;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&.late {\n\t\t\t\t\tcolor: #f0ad4e;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&.early {\n\t\t\t\t\tcolor: #5bc0de;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&.absent {\n\t\t\t\t\tcolor: #dd524d;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n// 记录列表\n.records-list {\n\tbackground-color: #fff;\n\tborder-radius: 10rpx;\n\tpadding: 20rpx;\n\tbox-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);\n\t\n\t.record-group {\n\t\tmargin-bottom: 30rpx;\n\t\t\n\t\t.group-header {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tpadding: 15rpx 10rpx;\n\t\t\tborder-bottom: 1rpx solid #f5f5f5;\n\t\t\tbackground-color: #f9f9f9;\n\t\t\tborder-radius: 8rpx 8rpx 0 0;\n\t\t\t\n\t\t\t.date {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: bold;\n\t\t\t\tcolor: #333;\n\t\t\t}\n\t\t\t\n\t\t\t.week {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tcolor: #666;\n\t\t\t\tmargin-left: 10rpx;\n\t\t\t\tbackground-color: #eaeaea;\n\t\t\t\tpadding: 2rpx 10rpx;\n\t\t\t\tborder-radius: 4rpx;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.record-item {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\tpadding: 20rpx 10rpx;\n\t\t\tborder-bottom: 1rpx solid #f5f5f5;\n\t\t\t\n\t\t\t&:last-child {\n\t\t\t\tborder-bottom: none;\n\t\t\t}\n\t\t\t\n\t\t\t.staff-info {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t\n\t\t\t\t.avatar {\n\t\t\t\t\twidth: 80rpx;\n\t\t\t\t\theight: 80rpx;\n\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\tborder: 2rpx solid #eaeaea;\n\t\t\t\t\tbox-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.staff-details {\n\t\t\t\t\tmargin-left: 20rpx;\n\t\t\t\t\t\n\t\t\t\t\t.staff-name {\n\t\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\t\tcolor: #333;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.staff-dept {\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tcolor: #666;\n\t\t\t\t\t\tmargin-top: 5rpx;\n\t\t\t\t\t\tbackground-color: #f5f5f5;\n\t\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\t\tpadding: 2rpx 10rpx;\n\t\t\t\t\t\tborder-radius: 4rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.record-details {\n\t\t\t\tpadding-left: 100rpx;\n\t\t\t\t\n\t\t\t\t.time-record {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex-wrap: wrap;\n\t\t\t\t\t\n\t\t\t\t\t.time-item {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tmargin-right: 30rpx;\n\t\t\t\t\t\tmargin-bottom: 15rpx;\n\t\t\t\t\t\t\n\t\t\t\t\t\t.time-label {\n\t\t\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\t\t\tcolor: #666;\n\t\t\t\t\t\t\tmargin-right: 10rpx;\n\t\t\t\t\t\t\tbackground-color: #f9f9f9;\n\t\t\t\t\t\t\tpadding: 4rpx 10rpx;\n\t\t\t\t\t\t\tborder-radius: 4rpx;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t.time-value {\n\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\tcolor: #333;\n\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t&.abnormal {\n\t\t\t\t\t\t\t\tcolor: #f0ad4e;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t.status-tag {\n\t\t\t\t\t\t\tfont-size: 22rpx;\n\t\t\t\t\t\t\tpadding: 4rpx 12rpx;\n\t\t\t\t\t\t\tborder-radius: 20rpx;\n\t\t\t\t\t\t\tmargin-left: 10rpx;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t&.late {\n\t\t\t\t\t\t\t\tbackground-color: #fef0e5;\n\t\t\t\t\t\t\t\tcolor: #f0ad4e;\n\t\t\t\t\t\t\t\tborder: 1rpx solid #f0ad4e;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t&.early {\n\t\t\t\t\t\t\t\tbackground-color: #e5f5fa;\n\t\t\t\t\t\t\t\tcolor: #5bc0de;\n\t\t\t\t\t\t\t\tborder: 1rpx solid #5bc0de;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.location-info {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex-wrap: wrap;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tmargin-top: 5rpx;\n\t\t\t\t\t\n\t\t\t\t\t.location-label {\n\t\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\t\tcolor: #666;\n\t\t\t\t\t\tmargin-right: 10rpx;\n\t\t\t\t\t\tbackground-color: #f9f9f9;\n\t\t\t\t\t\tpadding: 4rpx 10rpx;\n\t\t\t\t\t\tborder-radius: 4rpx;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.location-value {\n\t\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\t\tcolor: #666;\n\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\tword-break: break-all;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.load-more, .no-more {\n\t\ttext-align: center;\n\t\tpadding: 30rpx 0;\n\t\t\n\t\ttext {\n\t\t\tfont-size: 26rpx;\n\t\t\tcolor: #999;\n\t\t}\n\t}\n\t\n\t.empty-records {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: 60rpx 0;\n\t\t\n\t\timage {\n\t\t\twidth: 200rpx;\n\t\t\theight: 200rpx;\n\t\t\tmargin-bottom: 20rpx;\n\t\t}\n\t\t\n\t\ttext {\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #999;\n\t\t}\n\t}\n}\n\n// 弹窗样式\n.date-popup, .staff-popup, .status-popup {\n\tbackground-color: #fff;\n\tborder-top-left-radius: 20rpx;\n\tborder-top-right-radius: 20rpx;\n\t\n\t.popup-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 30rpx;\n\t\tborder-bottom: 1rpx solid #f5f5f5;\n\t\t\n\t\ttext {\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: bold;\n\t\t}\n\t\t\n\t\t.close-btn {\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #007AFF;\n\t\t}\n\t}\n}\n\n// 日期选择器\n.date-popup {\n\t.date-options {\n\t\tpadding: 0 30rpx 30rpx;\n\t\t\n\t\t.date-option {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\tpadding: 20rpx 0;\n\t\t\tborder-bottom: 1rpx solid #f5f5f5;\n\t\t\t\n\t\t\ttext {\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tcolor: #333;\n\t\t\t}\n\t\t\t\n\t\t\t&.active text {\n\t\t\t\tcolor: #007AFF;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.custom-date-range {\n\t\t\tpadding: 20rpx 0;\n\t\t\t\n\t\t\ttext {\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tcolor: #333;\n\t\t\t\tdisplay: block;\n\t\t\t\tmargin-bottom: 15rpx;\n\t\t\t}\n\t\t\t\n\t\t\t.date-inputs {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\t\n\t\t\t\t.date-picker {\n\t\t\t\t\tflex: 1;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.date-input {\n\t\t\t\t\tbackground-color: #f5f5f5;\n\t\t\t\t\tpadding: 15rpx 20rpx;\n\t\t\t\t\tborder-radius: 10rpx;\n\t\t\t\t\t\n\t\t\t\t\ttext {\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tcolor: #666;\n\t\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.date-separator {\n\t\t\t\t\tmargin: 0 20rpx;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: #999;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.custom-date-actions {\n\t\t\t\tmargin-top: 20rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: flex-end;\n\t\t\t\t\n\t\t\t\t.btn-apply {\n\t\t\t\t\tbackground-color: #007AFF;\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tpadding: 10rpx 30rpx;\n\t\t\t\t\tborder-radius: 8rpx;\n\t\t\t\t\t\n\t\t\t\t\t&:disabled {\n\t\t\t\t\t\tbackground-color: #cccccc;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n// 人员选择器\n.staff-popup {\n\t.search-box {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbackground-color: #f5f5f5;\n\t\tmargin: 20rpx 30rpx;\n\t\tpadding: 10rpx 20rpx;\n\t\tborder-radius: 10rpx;\n\t\t\n\t\tinput {\n\t\t\tflex: 1;\n\t\t\theight: 60rpx;\n\t\t\tmargin-left: 10rpx;\n\t\t\tfont-size: 28rpx;\n\t\t}\n\t}\n\t\n\t.staff-list {\n\t\tmax-height: 600rpx;\n\t\tpadding: 0 15rpx;\n\t\t\n\t\t.staff-item {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\tpadding: 20rpx 15rpx;\n\t\t\tborder-bottom: 1rpx solid #f5f5f5;\n\t\t\tmargin-bottom: 5rpx;\n\t\t\tborder-radius: 8rpx;\n\t\t\t\n\t\t\t&:active {\n\t\t\t\tbackground-color: #f9f9f9;\n\t\t\t}\n\t\t\t\n\t\t\t.staff-info {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\t\n\t\t\t\t.staff-name {\n\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.staff-dept {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tcolor: #666;\n\t\t\t\t\tmargin-top: 6rpx;\n\t\t\t\t\tbackground-color: #f5f5f5;\n\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\tpadding: 2rpx 10rpx;\n\t\t\t\t\tborder-radius: 4rpx;\n\t\t\t\t\tmax-width: 400rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n// 状态选择器\n.status-popup {\n\t.status-list {\n\t\tpadding-bottom: 30rpx;\n\t\t\n\t\t.status-item {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\tpadding: 20rpx 30rpx;\n\t\t\tborder-bottom: 1rpx solid #f5f5f5;\n\t\t\t\n\t\t\ttext {\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tcolor: #333;\n\t\t\t}\n\t\t}\n\t}\n}\n</style> ", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/attendance/all-records.vue'\nwx.createPage(MiniProgramPage)"], "names": ["attendanceApi", "uni", "userApi"], "mappings": ";;;;AAkNA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,WAAW;AAAA,MACX,iBAAiB;AAAA,QAChB,OAAO;AAAA,QACP,KAAK;AAAA,MACL;AAAA,MACD,aAAa;AAAA,QACZ,EAAE,OAAO,MAAM,OAAO,YAAa;AAAA,QACnC,EAAE,OAAO,MAAM,OAAO,YAAa;AAAA,QACnC,EAAE,OAAO,OAAO,OAAO,YAAa;AAAA,QACpC,EAAE,OAAO,QAAQ,OAAO,aAAa;AAAA,MACrC;AAAA,MACD,eAAe;AAAA,MACf,oBAAoB;AAAA,MACpB,WAAW,CAAE;AAAA,MACb,gBAAgB;AAAA,MAChB,aAAa;AAAA,QACZ,QAAQ,EAAE,OAAO,MAAM,OAAO,UAAW;AAAA,QACzC,MAAM,EAAE,OAAO,MAAM,OAAO,UAAW;AAAA,QACvC,OAAO,EAAE,OAAO,MAAM,OAAO,UAAW;AAAA,QACxC,QAAQ,EAAE,OAAO,MAAM,OAAO,UAAU;AAAA,MACxC;AAAA,MACD,eAAe;AAAA,MACf,uBAAuB;AAAA,MACvB,MAAM;AAAA,MACN,UAAU;AAAA,MACV,aAAa;AAAA,MACb,cAAc;AAAA,MACd,YAAY;AAAA,QACX,aAAa;AAAA,QACb,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,aAAa;AAAA,MACb;AAAA,MACD,SAAS,CAAE;AAAA,MACX,WAAW;AAAA;EAEZ;AAAA,EACD,UAAU;AAAA,IACT,mBAAmB;AAClB,cAAQ,KAAK,WAAS;AAAA,QACrB,KAAK;AACJ,iBAAO;AAAA,QACR,KAAK;AACJ,iBAAO;AAAA,QACR,KAAK;AACJ,iBAAO;AAAA,QACR,KAAK;AACJ,iBAAO;AAAA,QACR,KAAK;AACJ,iBAAO,GAAG,KAAK,gBAAgB,KAAK,MAAM,KAAK,gBAAgB,GAAG;AAAA,QACnE;AACC,iBAAO;AAAA,MACT;AAAA,IACA;AAAA,IACD,oBAAoB;AACnB,UAAI,CAAC,KAAK;AAAoB,eAAO,KAAK;AAE1C,aAAO,KAAK,UAAU;AAAA,QAAO,WAC5B,MAAM,KAAK,SAAS,KAAK,kBAAkB,KAC1C,MAAM,cAAc,MAAM,WAAW,SAAS,KAAK,kBAAkB;AAAA;IAEvE;AAAA,IACD,iBAAiB;AAChB,YAAM,SAAS,CAAA;AAEf,WAAK,QAAQ,QAAQ,YAAU;AAC9B,YAAI,CAAC,OAAO,OAAO,IAAI,GAAG;AACzB,iBAAO,OAAO,IAAI,IAAI;AAAA,YACrB,MAAM,OAAO;AAAA,YACb,MAAM,OAAO;AAAA,YACb,SAAS,CAAC;AAAA;QAEZ;AACA,eAAO,OAAO,IAAI,EAAE,QAAQ,KAAK,MAAM;AAAA,MACxC,CAAC;AAED,aAAO,OAAO,OAAO,MAAM;AAAA,IAC3B;AAAA,IACD,yBAAyB;AACxB,aAAO,KAAK,gBAAgB,SAAS,KAAK,gBAAgB;AAAA,IAC3D;AAAA,EACA;AAAA,EACD,SAAS;AACR,SAAK,cAAa;AAClB,SAAK,YAAW;AAAA,EAChB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,gBAAgB;AAEfA,gBAAAA,cAAc,YAAW,EAAG,KAAK,SAAO;AACvCC,sBAAA,MAAA,MAAA,OAAA,2CAAY,aAAa,GAAG;AAC5B,YAAI,IAAI,SAAS,OAAO,IAAI,QAAQ,IAAI,KAAK,SAAS,GAAG;AACxD,eAAK,YAAY,IAAI;AAAA,eACf;AAEN,eAAK,sBAAqB;AAAA,QAC3B;AAAA,MACD,CAAC,EAAE,MAAM,SAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,2CAAc,aAAa,GAAG;AAE9B,aAAK,sBAAqB;AAAA,MAC3B,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,wBAAwB;AACvBC,gBAAAA,QAAQ,iBAAgB,EAAG,KAAK,aAAW;AAC1CD,sBAAY,MAAA,MAAA,OAAA,2CAAA,eAAe,OAAO;AAClC,YAAI,QAAQ,SAAS,OAAO,QAAQ,MAAM;AAEzC,eAAK,YAAY,QAAQ,KAAK,IAAI,WAAS;AAAA,YAC1C,IAAI,KAAK;AAAA,YACT,MAAM,KAAK;AAAA,YACX,YAAY,KAAK,cAAc;AAAA,UAC/B,EAAC;AAAA,eACI;AAEN,eAAK,YAAY;AAAA,YAChB,EAAE,IAAI,OAAO,MAAM,MAAM,YAAY,MAAO;AAAA,YAC5C,EAAE,IAAI,OAAO,MAAM,MAAM,YAAY,MAAO;AAAA,YAC5C,EAAE,IAAI,OAAO,MAAM,MAAM,YAAY,MAAO;AAAA,YAC5C,EAAE,IAAI,OAAO,MAAM,MAAM,YAAY,MAAO;AAAA,YAC5C,EAAE,IAAI,OAAO,MAAM,MAAM,YAAY,MAAM;AAAA;QAE7C;AAAA,MACD,CAAC,EAAE,MAAM,SAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,2CAAc,eAAe,GAAG;AAEhC,aAAK,YAAY;AAAA,UAChB,EAAE,IAAI,OAAO,MAAM,MAAM,YAAY,MAAO;AAAA,UAC5C,EAAE,IAAI,OAAO,MAAM,MAAM,YAAY,MAAO;AAAA,UAC5C,EAAE,IAAI,OAAO,MAAM,MAAM,YAAY,MAAO;AAAA,UAC5C,EAAE,IAAI,OAAO,MAAM,MAAM,YAAY,MAAO;AAAA,UAC5C,EAAE,IAAI,OAAO,MAAM,MAAM,YAAY,MAAM;AAAA;MAE7C,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,qBAAqB;AACpB,YAAM,MAAM,oBAAI;AAChB,YAAM,OAAO,IAAI;AACjB,YAAM,QAAQ,IAAI,SAAQ,IAAK;AAC/B,YAAM,MAAM,IAAI;AAEhB,UAAI,YAAY;AAChB,UAAI,UAAU;AAEd,cAAQ,KAAK,WAAS;AAAA,QACrB,KAAK;AACJ,sBAAY,GAAG,IAAI,IAAI,OAAO,KAAK,EAAE,SAAS,GAAG,GAAG,CAAC;AACrD,oBAAU,KAAK,kBAAkB,MAAM,KAAK;AAC5C;AAAA,QACD,KAAK;AACJ,gBAAM,YAAY,UAAU,IAAI,KAAK,QAAQ;AAC7C,gBAAM,gBAAgB,UAAU,IAAI,OAAO,IAAI;AAC/C,sBAAY,GAAG,aAAa,IAAI,OAAO,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC;AAClE,oBAAU,KAAK,kBAAkB,eAAe,SAAS;AACzD;AAAA,QACD,KAAK;AACJ,gBAAM,YAAY,IAAI,KAAK,GAAG;AAC9B,oBAAU,QAAQ,MAAM,CAAC;AACzB,sBAAY,KAAK,WAAW,SAAS;AACrC,oBAAU,KAAK,WAAW,GAAG;AAC7B;AAAA,QACD,KAAK;AACJ,gBAAM,aAAa,IAAI,KAAK,GAAG;AAC/B,qBAAW,QAAQ,MAAM,EAAE;AAC3B,sBAAY,KAAK,WAAW,UAAU;AACtC,oBAAU,KAAK,WAAW,GAAG;AAC7B;AAAA,QACD,KAAK;AACJ,sBAAY,KAAK,gBAAgB;AACjC,oBAAU,KAAK,gBAAgB;AAC/B;AAAA,MACF;AAEA,aAAO,EAAE,WAAW;IACpB;AAAA;AAAA,IAGD,kBAAkB,MAAM,OAAO;AAC9B,YAAM,UAAU,IAAI,KAAK,MAAM,OAAO,CAAC,EAAE;AACzC,aAAO,GAAG,IAAI,IAAI,OAAO,KAAK,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,OAAO,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,IACpF;AAAA;AAAA,IAGD,WAAW,MAAM;AAChB,YAAM,OAAO,KAAK;AAClB,YAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,YAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,IAC9B;AAAA;AAAA,IAGD,cAAc;AACb,UAAI,KAAK;AAAW;AAEpB,WAAK,YAAY;AACjBA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACR,CAAC;AAGD,YAAM,EAAE,WAAW,QAAU,IAAE,KAAK,mBAAkB;AAEtD,YAAM,SAAS;AAAA,QACd,MAAM,KAAK;AAAA,QACX,UAAU,KAAK;AAAA,QACf;AAAA,QACA;AAAA;AAGDA,oBAAA,MAAA,MAAA,OAAA,2CAAY,SAAS,MAAM;AAG3B,UAAI,KAAK,eAAe;AACvB,eAAO,SAAS,KAAK,cAAc;AAAA,MACpC;AAGA,UAAI,KAAK,gBAAgB;AACxB,eAAO,SAAS,KAAK;AAAA,MACtB;AAGA,UAAI,KAAK,eAAe;AACvB,eAAO,UAAU,KAAK;AAAA,MACvB;AAGAD,gBAAAA,cAAc,WAAW,MAAM,EAAE,KAAK,SAAO;AAC5C,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAEjC,gBAAM,UAAU,IAAI,KAAK,WAAW,CAAA;AACpC,eAAK,aAAa;AAAA,YACjB,aAAa,QAAQ,WAAY,QAAQ,WAAW,QAAQ,OAAO,QAAQ,SAAS,QAAQ,QAAS;AAAA,YACrG,WAAW,QAAQ,QAAQ;AAAA,YAC3B,YAAY,QAAQ,SAAS;AAAA,YAC7B,aAAa,QAAQ,UAAU;AAAA;AAGhC,eAAK,eAAe,IAAI,KAAK,UAAU,IAAI,KAAK,QAAQ,SAAS;AAGjE,gBAAM,mBAAmB,KAAK,yBAAyB,IAAI,KAAK,WAAW,CAAA,CAAE;AAG7E,cAAI,KAAK,SAAS,GAAG;AACpB,iBAAK,UAAU;AAAA,iBACT;AACN,iBAAK,UAAU,CAAC,GAAG,KAAK,SAAS,GAAG,gBAAgB;AAAA,UACrD;AAGA,eAAK,cAAc,iBAAiB,WAAW,KAAK;AAAA,eAC9C;AACNC,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,IAAI,WAAW;AAAA,YACtB,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACD,CAAC,EAAE,MAAM,SAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,2CAAc,aAAa,GAAG;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF,CAAC,EAAE,QAAQ,MAAM;AAChBA,sBAAG,MAAC,YAAW;AACf,aAAK,YAAY;AAAA,MAClB,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,yBAAyB,SAAS;AACjC,UAAI,CAAC,MAAM,QAAQ,OAAO,KAAK,QAAQ,WAAW,GAAG;AACpD,eAAO;MACR;AAGA,YAAM,cAAc,QAAQ,CAAC;AAG7B,UAAI,YAAY,gBAAgB,UAAa,YAAY,iBAAiB,QAAW;AACpF,eAAO,QAAQ,IAAI,UAAQ;AAE1B,gBAAM,OAAO,KAAK,QAAQ;AAE1B,iBAAO;AAAA,YACN,IAAI,KAAK;AAAA,YACT,MAAM,KAAK,QAAQ,KAAK,qBAAqB,KAAK,aAAa,KAAK,UAAU;AAAA,YAC9E,MAAM,KAAK,QAAQ,KAAK,qBAAqB,KAAK,aAAa,KAAK,UAAU;AAAA,YAC9E,WAAW,KAAK,QAAQ;AAAA,YACxB,YAAY,KAAK,cAAc;AAAA,YAC/B,QAAQ,KAAK,UAAU;AAAA,YACvB,aAAa,KAAK,eAAe;AAAA,YACjC,eAAe,KAAK,iBAAiB;AAAA,YACrC,cAAc,KAAK,gBAAgB;AAAA,YACnC,gBAAgB,KAAK,kBAAkB;AAAA,YACvC,UAAU,KAAK,YAAY,KAAK,eAAe,IAAI;AAAA;QAErD,CAAC;AAAA,MACF;AAIA,YAAM,uBAAuB,CAAA;AAE7B,cAAQ,QAAQ,YAAU;AACzB,YAAI,CAAC,OAAO,WAAW;AACtBA,uFAAa,oBAAoB,MAAM;AACvC;AAAA,QACD;AAEA,cAAM,OAAO,IAAI,KAAK,OAAO,SAAS;AACtC,cAAM,UAAU,KAAK,YAAW,EAAG,MAAM,GAAG,EAAE,CAAC;AAC/C,cAAM,SAAS,OAAO,WAAW,OAAO,OAAO,OAAO,KAAK,KAAK;AAChE,cAAM,MAAM,GAAG,OAAO,IAAI,MAAM;AAEhC,YAAI,CAAC,qBAAqB,GAAG,GAAG;AAE/B,gBAAM,OAAO,OAAO,QAAQ;AAE5B,+BAAqB,GAAG,IAAI;AAAA,YAC3B,IAAI,OAAO;AAAA,YACX,MAAM,KAAK,qBAAqB,OAAO,SAAS;AAAA,YAChD,MAAM,KAAK,qBAAqB,OAAO,SAAS;AAAA,YAChD,WAAW,KAAK,QAAQ,OAAO,YAAY;AAAA,YAC3C,YAAY,KAAK,cAAc,OAAO,cAAc;AAAA,YACpD,QAAQ,KAAK,UAAU,OAAO,UAAU;AAAA,YACxC,aAAa;AAAA,YACb,cAAc;AAAA,YACd,eAAe;AAAA,YACf,gBAAgB;AAAA,YAChB;AAAA,YACA,UAAU,OAAO,aAAa,OAAO,YAAY,OAAO,YACvD,GAAG,OAAO,SAAS,QAAQ,CAAC,CAAC,KAAK,OAAO,UAAU,QAAQ,CAAC,CAAC,KAAK;AAAA;QAErE;AAGA,YAAI,OAAO,cAAc,WAAW;AACnC,+BAAqB,GAAG,EAAE,cAAc,KAAK,WAAW,OAAO,SAAS;AACxE,+BAAqB,GAAG,EAAE,gBAAgB,OAAO,UAAU;AAAA,QAC5D,WAAW,OAAO,cAAc,YAAY;AAC3C,+BAAqB,GAAG,EAAE,eAAe,KAAK,WAAW,OAAO,SAAS;AACzE,+BAAqB,GAAG,EAAE,iBAAiB,OAAO,UAAU;AAAA,QAC7D;AAAA,MACD,CAAC;AAGD,aAAO,OAAO,OAAO,oBAAoB;AAAA,IACzC;AAAA;AAAA,IAGD,WAAW,YAAY;AACtB,UAAI,CAAC;AAAY,eAAO;AAExB,UAAI;AACH,cAAM,OAAO,IAAI,KAAK,UAAU;AAChC,cAAM,QAAQ,OAAO,KAAK,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AACrD,cAAM,UAAU,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACzD,eAAO,GAAG,KAAK,IAAI,OAAO;AAAA,MAC3B,SAAS,GAAG;AACX,eAAO;AAAA,MACR;AAAA,IACA;AAAA;AAAA,IAGD,kBAAkB;AACjB,UAAI,CAAC,KAAK,eAAe,KAAK;AAAW;AAEzC,WAAK;AACL,WAAK,YAAW;AAAA,IAChB;AAAA;AAAA,IAGD,cAAc;AACb,WAAK,gBAAgB;AACrB,WAAK,OAAO;AACZ,WAAK,YAAW;AAAA,IAChB;AAAA;AAAA,IAGD,iBAAiB;AAChB,WAAK,MAAM,UAAU;IACrB;AAAA,IAED,kBAAkB;AACjB,WAAK,MAAM,UAAU;IACrB;AAAA,IAED,gBAAgB,OAAO;AACtB,WAAK,YAAY;AACjB,WAAK,gBAAe;AACpB,WAAK,OAAO;AACZ,WAAK,YAAW;AAAA,IAChB;AAAA,IAED,kBAAkB,GAAG;AACpB,WAAK,gBAAgB,QAAQ,EAAE,OAAO;AAAA,IACtC;AAAA,IAED,gBAAgB,GAAG;AAClB,WAAK,gBAAgB,MAAM,EAAE,OAAO;AAAA,IACpC;AAAA,IAED,uBAAuB;AACtB,WAAK,YAAY;AACjB,WAAK,gBAAe;AACpB,WAAK,OAAO;AACZ,WAAK,YAAW;AAAA,IAChB;AAAA;AAAA,IAGD,oBAAoB;AACnB,WAAK,MAAM,WAAW;IACtB;AAAA,IAED,qBAAqB;AACpB,WAAK,MAAM,WAAW;IACtB;AAAA,IAED,YAAY,OAAO;AAClB,WAAK,gBAAgB;AACrB,WAAK,mBAAkB;AACvB,WAAK,OAAO;AACZ,WAAK,YAAW;AAAA,IAChB;AAAA;AAAA,IAGD,qBAAqB;AACpB,WAAK,MAAM,YAAY;IACvB;AAAA,IAED,sBAAsB;AACrB,WAAK,MAAM,YAAY;IACvB;AAAA,IAED,aAAa,QAAQ;AACpB,WAAK,iBAAiB;AACtB,WAAK,oBAAmB;AACxB,WAAK,OAAO;AACZ,WAAK,YAAW;AAAA,IAChB;AAAA;AAAA,IAGD,qBAAqB,YAAY;AAChC,UAAI,CAAC;AAAY,eAAO;AACxB,UAAI;AACH,cAAM,OAAO,IAAI,KAAK,UAAU;AAChC,eAAO,KAAK,WAAW,IAAI;AAAA,MAC5B,SAAS,GAAG;AACXA,sFAAc,YAAY,CAAC;AAC3B,eAAO;AAAA,MACR;AAAA,IACA;AAAA;AAAA,IAGD,qBAAqB,YAAY;AAChC,UAAI,CAAC;AAAY,eAAO;AACxB,UAAI;AACH,cAAM,OAAO,IAAI,KAAK,UAAU;AAChC,cAAM,WAAW,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAC1D,eAAO,SAAS,KAAK,OAAM,CAAE;AAAA,MAC9B,SAAS,GAAG;AACXA,sFAAc,YAAY,CAAC;AAC3B,eAAO;AAAA,MACR;AAAA,IACA;AAAA;AAAA,IAGD,eAAe,QAAQ;AACtB,UAAI,CAAC;AAAQ,eAAO;AACpB,UAAI,OAAO;AAAU,eAAO,OAAO;AACnC,UAAI,OAAO,YAAY,OAAO,WAAW;AACxC,eAAO,GAAG,OAAO,SAAS,QAAQ,CAAC,CAAC,KAAK,OAAO,UAAU,QAAQ,CAAC,CAAC;AAAA,MACrE;AACA,aAAO;AAAA,IACR;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvrBA,GAAG,WAAW,eAAe;"}