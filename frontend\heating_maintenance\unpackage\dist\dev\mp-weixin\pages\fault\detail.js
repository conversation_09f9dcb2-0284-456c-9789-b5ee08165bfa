"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const utils_upload = require("../../utils/upload.js");
const PermissionCheck = () => "../../components/PermissionCheck.js";
const _sfc_main = {
  components: {
    PermissionCheck
    // 本地注册组件
  },
  data() {
    return {
      faultId: null,
      faultInfo: {},
      images: [],
      video: "",
      loading: true,
      loadError: false,
      isAdmin: true,
      isDev: true,
      operationLogs: [],
      // 工单信息
      workOrderInfo: null
    };
  },
  onShow() {
    this.loadFaultDetail();
  },
  onLoad(options) {
    this.faultId = options.id;
    if (this.faultId) {
      this.loadFaultDetail();
    } else {
      common_vendor.index.showToast({
        title: "参数错误，无法获取故障详情",
        icon: "none"
      });
      setTimeout(() => {
        common_vendor.index.navigateBack();
      }, 1500);
    }
  },
  methods: {
    // 加载故障详情
    loadFaultDetail() {
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      this.loading = true;
      this.loadError = false;
      utils_api.faultApi.getFaultDetail(this.faultId).then((res) => {
        if (res.code === 200) {
          common_vendor.index.__f__("log", "at pages/fault/detail.vue:223", "故障详情数据:", res.data);
          if (res.data && res.data.fault_info) {
            this.faultInfo = res.data.fault_info;
          }
          if (res.data && Array.isArray(res.data.images)) {
            this.images = res.data.images;
          } else {
            this.images = [];
          }
          if (res.data && res.data.video) {
            this.video = res.data.video;
            common_vendor.index.__f__("log", "at pages/fault/detail.vue:238", "视频地址:", this.video);
          } else {
            this.video = "";
          }
          if (res.data && Array.isArray(res.data.operation_logs)) {
            this.operationLogs = res.data.operation_logs.map((log) => {
              const processedLog = { ...log };
              if (processedLog.operatorId && !processedLog.operatorName) {
                processedLog.operatorName = "操作人ID: " + processedLog.operatorId;
              }
              if (processedLog.createdAt) {
                if (!isNaN(processedLog.createdAt) || /^\d+$/.test(processedLog.createdAt)) {
                  try {
                    const date = new Date(parseInt(processedLog.createdAt));
                    if (!isNaN(date.getTime())) {
                      processedLog.createdAt = this.formatDateTime(date);
                    }
                  } catch (e) {
                    common_vendor.index.__f__("error", "at pages/fault/detail.vue:267", "日期转换错误:", e);
                  }
                }
              }
              return processedLog;
            });
          } else {
            this.operationLogs = [];
          }
          if (res.data && res.data.work_order) {
            this.workOrderInfo = res.data.work_order;
          } else {
            this.workOrderInfo = null;
          }
        } else {
          this.loadError = true;
          common_vendor.index.showToast({
            title: res.message || "获取故障详情失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/fault/detail.vue:294", "获取故障详情异常:", err);
        this.loadError = true;
        common_vendor.index.showToast({
          title: "网络异常，请稍后重试",
          icon: "none"
        });
      }).finally(() => {
        this.loading = false;
        common_vendor.index.hideLoading();
      });
    },
    // 获取完整的图片URL
    getFullImageUrl(path) {
      common_vendor.index.__f__("log", "at pages/fault/detail.vue:309", utils_upload.uploadUtils.getFileUrl(path));
      return utils_upload.uploadUtils.getFileUrl(path);
    },
    // 获取完整的视频URL
    getFullVideoUrl(path) {
      if (!path)
        return "";
      try {
        if (path && path.startsWith("@http")) {
          const cleanUrl = path.substring(1);
          common_vendor.index.__f__("log", "at pages/fault/detail.vue:322", "视频路径以@http开头，清理后:", cleanUrl);
          return cleanUrl;
        }
        if (path.startsWith("http")) {
          common_vendor.index.__f__("log", "at pages/fault/detail.vue:328", "视频已是完整URL，直接返回:", path);
          return path;
        }
        const fullUrl = utils_upload.uploadUtils.getFileUrl(path);
        common_vendor.index.__f__("log", "at pages/fault/detail.vue:334", "处理后的视频URL:", fullUrl);
        return fullUrl + "?t=" + (/* @__PURE__ */ new Date()).getTime();
      } catch (err) {
        common_vendor.index.__f__("error", "at pages/fault/detail.vue:339", "视频URL处理出错:", err);
        return path;
      }
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        "待确认": "待确认",
        "已确认": "已确认",
        "已退回": "已退回"
      };
      return statusMap[status] || "未知";
    },
    // 获取状态对应的CSS类名
    getFaultStatusClass(status) {
      const statusClassMap = {
        "待确认": "pending",
        "已确认": "confirmed",
        "已退回": "rejected",
        "pending": "待确认",
        "confirmed": "已确认",
        "rejected": "已退回"
      };
      return statusClassMap[status] || "pending";
    },
    // 格式化日期时间
    formatDateTime(date) {
      if (!date)
        return "";
      if (typeof date === "number") {
        const d = new Date(date);
        if (!isNaN(d.getTime())) {
          return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, "0")}-${String(d.getDate()).padStart(2, "0")} ${String(d.getHours()).padStart(2, "0")}:${String(d.getMinutes()).padStart(2, "0")}`;
        }
      }
      if (date instanceof Date) {
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")} ${String(date.getHours()).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
      }
      if (typeof date === "string") {
        try {
          const parsedDate = new Date(date);
          if (!isNaN(parsedDate.getTime())) {
            return `${parsedDate.getFullYear()}-${String(parsedDate.getMonth() + 1).padStart(2, "0")}-${String(parsedDate.getDate()).padStart(2, "0")} ${String(parsedDate.getHours()).padStart(2, "0")}:${String(parsedDate.getMinutes()).padStart(2, "0")}`;
          }
        } catch (e) {
          common_vendor.index.__f__("error", "at pages/fault/detail.vue:392", "日期解析错误:", e);
        }
        return date;
      }
      return String(date);
    },
    // 获取等级对应的CSS类名
    getFaultLevelClass(level) {
      const levelMap = {
        "minor": "minor",
        "normal": "normal",
        "critical": "critical",
        "emergency": "emergency",
        "轻微": "minor",
        "一般": "normal",
        "严重": "critical",
        "紧急": "emergency"
      };
      return levelMap[level] || "normal";
    },
    // 获取工单状态文本
    getWorkOrderStatusText(status) {
      const statusMap = {
        "pending": "待接单",
        "in_progress": "处理中",
        "completed": "已完成",
        "待接单": "待接单",
        "处理中": "处理中",
        "已完成": "已完成"
      };
      return statusMap[status] || "未知";
    },
    // 获取工单状态对应的CSS类名
    getWorkOrderStatusClass(status) {
      const statusClassMap = {
        "pending": "pending",
        "in_progress": "in_progress",
        "completed": "completed",
        "待接单": "pending",
        "处理中": "in_progress",
        "已完成": "completed"
      };
      return statusClassMap[status] || "pending";
    },
    // 预览图片
    previewImage(index) {
      if (this.images && this.images.length > 0) {
        const fullUrls = this.images.map((path) => this.getFullImageUrl(path));
        common_vendor.index.previewImage({
          urls: fullUrls,
          current: fullUrls[index]
        });
      }
    },
    // 确认故障
    confirmFault() {
      const operatorId = common_vendor.index.getStorageSync("userId") || null;
      const targetStatus = "已确认";
      if (!operatorId) {
        common_vendor.index.showToast({ title: "无法获取操作员信息", icon: "none" });
        return;
      }
      common_vendor.index.showModal({
        title: "确认操作",
        content: "您正在确认该故障为有效上报，请确保已核实故障真实性\n（确认后将生成待处理工单）",
        confirmText: "确认",
        cancelText: "取消",
        success: (modalRes) => {
          if (modalRes.confirm) {
            common_vendor.index.showLoading({
              title: "提交中..."
            });
            const requestData = {
              fault_id: this.faultId,
              operator_id: operatorId,
              fault_status: targetStatus,
              heat_unit_id: this.faultInfo.heat_unit_id
              // 添加热用户ID
            };
            utils_api.faultApi.updateFaultStatus(requestData).then((apiRes) => {
              common_vendor.index.hideLoading();
              if (apiRes.code === 200) {
                common_vendor.index.showToast({
                  title: "故障已确认",
                  icon: "success",
                  duration: 1500
                  // 稍长提示时间
                });
                setTimeout(() => {
                  getApp().globalData = getApp().globalData || {};
                  getApp().globalData.refreshFaultList = true;
                  common_vendor.index.navigateBack();
                }, 1500);
              } else {
                common_vendor.index.showToast({ title: apiRes.message || "操作失败", icon: "none" });
              }
            }).catch((err) => {
              common_vendor.index.hideLoading();
              common_vendor.index.__f__("error", "at pages/fault/detail.vue:508", "确认故障 API 调用失败:", err);
              common_vendor.index.showToast({ title: "网络错误，请稍后重试", icon: "none" });
            });
          }
        }
        // success end
      });
    },
    // 退回故障上报
    rejectFault() {
      const operatorId = common_vendor.index.getStorageSync("userId") || null;
      const targetStatus = "已退回";
      if (!operatorId) {
        common_vendor.index.showToast({ title: "无法获取操作员信息", icon: "none" });
        return;
      }
      common_vendor.index.showModal({
        title: "退回上报",
        content: "确认退回此故障上报？",
        confirmText: "确认",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({
              title: "提交中..."
            });
            const requestData = {
              fault_id: this.faultId,
              operator_id: operatorId,
              fault_status: targetStatus
            };
            utils_api.faultApi.updateFaultStatus(requestData).then((apiRes) => {
              common_vendor.index.hideLoading();
              if (apiRes.code === 200) {
                common_vendor.index.showToast({
                  title: "故障已退回",
                  icon: "success",
                  duration: 1500
                  // 稍长提示时间
                });
                setTimeout(() => {
                  getApp().globalData = getApp().globalData || {};
                  getApp().globalData.refreshFaultList = true;
                  common_vendor.index.navigateBack();
                }, 1500);
              } else {
                common_vendor.index.showToast({ title: apiRes.message || "操作失败", icon: "none" });
              }
            }).catch((err) => {
              common_vendor.index.hideLoading();
              common_vendor.index.__f__("error", "at pages/fault/detail.vue:567", "故障退回 API 调用失败:", err);
              common_vendor.index.showToast({ title: "网络错误，请稍后重试", icon: "none" });
            });
          }
        }
      });
    },
    // 视频加载错误处理
    onVideoError(e) {
      common_vendor.index.__f__("error", "at pages/fault/detail.vue:577", "视频加载失败:", e.detail);
      common_vendor.index.__f__("error", "at pages/fault/detail.vue:578", "视频路径:", this.video);
      common_vendor.index.__f__("error", "at pages/fault/detail.vue:579", "处理后URL:", this.getFullVideoUrl(this.video));
      this.videoLoadError = true;
      common_vendor.index.showToast({
        title: "视频加载失败，请尝试在浏览器中查看",
        icon: "none",
        duration: 2e3
      });
    },
    // 在浏览器中打开视频
    openVideoInBrowser() {
      let url = this.getFullVideoUrl(this.video);
      if (!url) {
        common_vendor.index.showToast({
          title: "视频URL无效",
          icon: "none"
        });
        return;
      }
      common_vendor.index.__f__("log", "at pages/fault/detail.vue:603", "在浏览器中打开视频:", url);
      common_vendor.index.setClipboardData({
        data: url,
        success: () => {
          common_vendor.index.showToast({
            title: "URL已复制，请在浏览器中打开",
            icon: "none"
          });
        }
      });
    },
    // 获取时间轴点类名
    getTimelineDotClass(operationType) {
      if (!operationType)
        return "dot-unknown";
      if (operationType.includes("上报") || operationType.includes("创建")) {
        return "dot-report";
      } else if (operationType.includes("确认") || operationType.includes("接单")) {
        return "dot-confirm";
      } else if (operationType.includes("退回") || operationType.includes("拒绝")) {
        return "dot-reject";
      } else if (operationType.includes("维修") || operationType.includes("处理")) {
        return "dot-repair";
      } else if (operationType.includes("完成") || operationType.includes("结束")) {
        return "dot-complete";
      } else if (operationType.includes("转派") || operationType.includes("分配")) {
        return "dot-assign";
      } else {
        return "dot-unknown";
      }
    }
  }
};
if (!Array) {
  const _component_PermissionCheck = common_vendor.resolveComponent("PermissionCheck");
  _component_PermissionCheck();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($options.getStatusText($data.faultInfo.fault_status)),
    b: common_vendor.n($options.getFaultStatusClass($data.faultInfo.fault_status)),
    c: common_vendor.t($data.faultInfo.fault_no || "暂无"),
    d: common_vendor.t($data.faultInfo.heat_unit_name || "暂无"),
    e: $data.faultInfo.address
  }, $data.faultInfo.address ? {
    f: common_vendor.t($data.faultInfo.address)
  } : {}, {
    g: common_vendor.t($data.faultInfo.fault_type || "暂无"),
    h: common_vendor.t($data.faultInfo.fault_level || "未知"),
    i: common_vendor.n($options.getFaultLevelClass($data.faultInfo.fault_level)),
    j: common_vendor.t($data.faultInfo.fault_source || "暂无"),
    k: common_vendor.t($data.faultInfo.occur_time || "暂无"),
    l: common_vendor.t($data.faultInfo.report_user_name || "暂无"),
    m: common_vendor.t($data.faultInfo.report_time || "暂无"),
    n: common_vendor.t($data.faultInfo.created_time || "暂无"),
    o: common_vendor.t($data.faultInfo.fault_desc || "暂无描述"),
    p: $data.images && $data.images.length > 0
  }, $data.images && $data.images.length > 0 ? {
    q: common_vendor.f($data.images, (image, index, i0) => {
      return {
        a: $options.getFullImageUrl(image),
        b: index,
        c: common_vendor.o(($event) => $options.previewImage(index), index)
      };
    })
  } : {}, {
    r: $data.video
  }, $data.video ? {
    s: $options.getFullVideoUrl($data.video),
    t: common_vendor.o((...args) => $options.onVideoError && $options.onVideoError(...args))
  } : {}, {
    v: $data.workOrderInfo
  }, $data.workOrderInfo ? common_vendor.e({
    w: common_vendor.t($options.getWorkOrderStatusText($data.workOrderInfo.status)),
    x: common_vendor.n($options.getWorkOrderStatusClass($data.workOrderInfo.status)),
    y: common_vendor.t($data.workOrderInfo.code),
    z: common_vendor.t($data.workOrderInfo.create_time),
    A: $data.workOrderInfo.assignee
  }, $data.workOrderInfo.assignee ? {
    B: common_vendor.t($data.workOrderInfo.assignee)
  } : {}, {
    C: $data.workOrderInfo.phone
  }, $data.workOrderInfo.phone ? {
    D: common_vendor.t($data.workOrderInfo.phone)
  } : {}, {
    E: $data.workOrderInfo.repair_content
  }, $data.workOrderInfo.repair_content ? {
    F: common_vendor.t($data.workOrderInfo.repair_content)
  } : {}, {
    G: $data.workOrderInfo.repair_result
  }, $data.workOrderInfo.repair_result ? {
    H: common_vendor.t($data.workOrderInfo.repair_result)
  } : {}, {
    I: $data.workOrderInfo.repair_materials
  }, $data.workOrderInfo.repair_materials ? {
    J: common_vendor.t($data.workOrderInfo.repair_materials)
  } : {}, {
    K: $data.workOrderInfo.complete_time
  }, $data.workOrderInfo.complete_time ? {
    L: common_vendor.t($data.workOrderInfo.complete_time)
  } : {}) : {}, {
    M: $data.operationLogs && $data.operationLogs.length > 0
  }, $data.operationLogs && $data.operationLogs.length > 0 ? {
    N: common_vendor.f($data.operationLogs, (log, index, i0) => {
      return common_vendor.e({
        a: common_vendor.n($options.getTimelineDotClass(log.operationType)),
        b: index !== $data.operationLogs.length - 1
      }, index !== $data.operationLogs.length - 1 ? {} : {}, {
        c: common_vendor.t(log.operationType),
        d: common_vendor.t(log.createdAt),
        e: common_vendor.t(log.operationDesc),
        f: log.operatorName
      }, log.operatorName ? {
        g: common_vendor.t(log.operatorName)
      } : {}, {
        h: index
      });
    })
  } : {}, {
    O: $data.isAdmin && $data.faultInfo.fault_status === "待确认"
  }, $data.isAdmin && $data.faultInfo.fault_status === "待确认" ? {
    P: common_vendor.o((...args) => $options.confirmFault && $options.confirmFault(...args)),
    Q: common_vendor.o((...args) => $options.rejectFault && $options.rejectFault(...args))
  } : {}, {
    R: common_vendor.p({
      permission: "fault:fault-handle"
    }),
    S: $data.faultInfo.fault_status === "已退回"
  }, $data.faultInfo.fault_status === "已退回" ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/fault/detail.js.map
