/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.fault-list-container {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* Tab切换 */
.tab-container {
  display: flex;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 999;
}
.tab-container .tab-item {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 30rpx;
  color: #666;
  position: relative;
}
.tab-container .tab-item.active {
  color: #007aff;
  font-weight: 500;
}
.tab-container .tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #007aff;
  border-radius: 2rpx;
}
.filter-section {
  background-color: #ffffff;
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
  position: -webkit-sticky;
  position: sticky;
  top: 80rpx;
  z-index: 998;
}
.filter-section .date-filter {
  flex: 1;
  height: 70rpx;
}
.filter-section .date-filter .date-picker {
  display: flex;
  align-items: center;
  height: 70rpx;
  border: 1px solid #e5e5e5;
  border-radius: 6rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
}
.filter-section .date-filter .date-picker .date-text {
  flex: 1;
}
.filter-section .date-filter .date-picker .iconfont {
  margin-left: 10rpx;
  color: #999;
}
.filter-section .date-filter {
  margin-right: 20rpx;
}
.fault-list {
  padding: 0 30rpx;
}
.fault-list .fault-item {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}
.fault-list .fault-item .fault-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #f0f0f0;
}
.fault-list .fault-item .fault-header .heat-unit-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}
.fault-list .fault-item .fault-header .fault-status {
  padding: 6rpx 16rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
}
.fault-list .fault-item .fault-header .fault-status.pending {
  background-color: #fff7e6;
  color: #fa8c16;
}
.fault-list .fault-item .fault-header .fault-status.confirmed {
  background-color: #e6f7ff;
  color: #1890ff;
}
.fault-list .fault-item .fault-header .fault-status.completed {
  background-color: #f6ffed;
  color: #52c41a;
}
.fault-list .fault-item .fault-header .fault-status.returned {
  background-color: #fff1f0;
  color: #f5222d;
}
.fault-list .fault-item .fault-header .fault-status.default {
  background-color: #f5f5f5;
  color: #999;
}
.fault-list .fault-item .fault-content {
  padding: 30rpx;
}
.fault-list .fault-item .fault-content .fault-desc {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 20rpx;
}
.fault-list .fault-item .fault-content .fault-info {
  margin-bottom: 20rpx;
}
.fault-list .fault-item .fault-content .fault-info .info-item {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.fault-list .fault-item .fault-content .fault-info .info-item .info-label {
  font-size: 26rpx;
  color: #999;
  width: 150rpx;
}
.fault-list .fault-item .fault-content .fault-info .info-item .info-value {
  font-size: 26rpx;
  color: #333;
}
.fault-list .fault-item .fault-content .fault-info .info-item .level-tag {
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 24rpx;
}
.fault-list .fault-item .fault-content .fault-info .info-item .level-tag.notice {
  background-color: #e6f7ff;
  color: #1890ff;
}
.fault-list .fault-item .fault-content .fault-info .info-item .level-tag.normal {
  background-color: #e6f7ff;
  color: #1890ff;
}
.fault-list .fault-item .fault-content .fault-info .info-item .level-tag.important {
  background-color: #fff7e6;
  color: #fa8c16;
}
.fault-list .fault-item .fault-content .fault-info .info-item .level-tag.serious {
  background-color: #ffebee;
  color: #EF5350;
}
.fault-list .fault-item .fault-content .fault-info .info-item .level-tag.critical {
  background-color: #fff1f0;
  color: #f5222d;
}
.fault-list .fault-item .fault-content .fault-info .info-item .level-tag.default {
  background-color: #f5f5f5;
  color: #999;
}
.fault-list .fault-item .fault-content .fault-footer {
  display: flex;
  justify-content: flex-end;
}
.fault-list .fault-item .fault-content .fault-footer .report-info {
  font-size: 24rpx;
  color: #999;
}
.fault-list .fault-item .fault-content .fault-footer .report-info .reporter {
  margin-right: 10rpx;
}

/* 重置按钮 */
.refresh-button {
  font-size: 28rpx;
  background-color: #fff;
  border-radius: 5rpx;
  align-items: center;
  height: 72rpx;
  border-radius: 6rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  position: relative;
  min-width: 80rpx;
}
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-state .empty-image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 30rpx;
}
.empty-state .empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}
.empty-state .refresh-button {
  font-size: 28rpx;
  color: #007aff;
  background-color: #fff;
  border: 2rpx solid #007aff;
  border-radius: 40rpx;
  padding: 10rpx 60rpx;
}
.loading-container {
  display: flex;
  justify-content: center;
  padding: 30rpx 0;
}
.loading-container .loading-text {
  font-size: 28rpx;
  color: #999;
}
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 50rpx 0;
}
.error-container .error-text {
  font-size: 28rpx;
  color: #f5222d;
  margin-bottom: 20rpx;
}
.error-container .retry-btn {
  font-size: 28rpx;
  color: #fff;
  background-color: #1890ff;
  padding: 8rpx 30rpx;
  border-radius: 30rpx;
}
.floating-button {
  position: fixed;
  right: 30rpx;
  bottom: 30rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #0088ff;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 136, 255, 0.4);
  z-index: 10;
}
.floating-button .plus-icon {
  font-size: 60rpx;
  color: #fff;
  font-weight: normal;
  line-height: 60rpx;
  margin-top: -3rpx;
}
.heat-unit-filter-section {
  background-color: #ffffff;
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
}
.heat-unit-filter {
  flex: 1;
  height: 70rpx;
}
.heat-unit-filter .heat-unit-picker {
  display: flex;
  align-items: center;
  height: 70rpx;
  border: 1px solid #e5e5e5;
  border-radius: 6rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
}
.heat-unit-filter .heat-unit-picker .heat-unit-text {
  flex: 1;
}
.heat-unit-filter .heat-unit-picker .iconfont {
  margin-left: 10rpx;
  color: #999;
}