"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const common_assets = require("../../common/assets.js");
const PermissionCheck = () => "../../components/PermissionCheck.js";
const BaseTabBar = () => "../../components/BaseTabBar.js";
const _sfc_main = {
  components: {
    PermissionCheck,
    // 本地注册组件
    BaseTabBar
  },
  data() {
    return {
      title: "智慧供暖运维系统",
      statsData: {
        heatUnitCount: 356,
        hesOnlineRate: "98.5%",
        weeklyAlarms: 12
      },
      recentTasks: [],
      patrolTasks: [],
      // 巡检工单列表
      // 室温上报相关数据
      heatUnitList: [],
      // 热用户列表数据
      heatUnitOptions: [],
      // 热用户名称列表
      heatUnitIndex: 0,
      buildingNo: "",
      unitNo: "",
      roomNo: "",
      reportTemp: 22,
      reportRemark: "",
      outdoorTemp: 0,
      uploadImages: [],
      latitude: null,
      longitude: null,
      timer: null,
      showDebug: true
    };
  },
  computed: {
    // 直接从$store.state获取状态，不使用mapState
    // ...mapState('attendance', [
    //   'isUploadingLocation', // 是否正在上传位置
    //   'clockInTime',         // 上班打卡时间
    //   'clockOutTime'         // 下班打卡时间
    // ]),
    isUploadingLocation() {
      return this.$store.state.attendance.isUploadingLocation;
    },
    clockInTime() {
      return this.$store.state.attendance.clockInTime;
    },
    clockOutTime() {
      return this.$store.state.attendance.clockOutTime;
    },
    currentDate() {
      const now = /* @__PURE__ */ new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, "0");
      const day = String(now.getDate()).padStart(2, "0");
      return `${year}年${month}月${day}日`;
    },
    currentWeekDay() {
      const weekDays = [
        "星期日",
        "星期一",
        "星期二",
        "星期三",
        "星期四",
        "星期五",
        "星期六"
      ];
      return weekDays[(/* @__PURE__ */ new Date()).getDay()];
    }
  },
  onLoad() {
    this.loadHomeData();
    this.timer = setInterval(() => {
      this.$forceUpdate();
    }, 6e4);
    setTimeout(() => {
      this.checkAttendanceStatus();
    }, 1e3);
  },
  onUnload() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  },
  onShow() {
    this.loadHomeData();
    setTimeout(() => {
      this.checkAttendanceStatus();
    }, 1e3);
  },
  methods: {
    // 检查今日打卡状态
    checkAttendanceStatus() {
      const isPositioning = common_vendor.index.getStorageSync("isPositioning") || 0;
      const positionCycle = common_vendor.index.getStorageSync("positionCycle") || 1;
      common_vendor.index.__f__("log", "at pages/home/<USER>", "定位上传设置:", isPositioning, "定位上传周期:", positionCycle, "分钟");
      if (isPositioning === 1) {
        common_vendor.index.__f__("log", "at pages/home/<USER>", "已开启定位上传功能，将按照", positionCycle, "分钟的周期进行定位上传");
        this.$store.dispatch("attendance/getTodayClockRecord").then((data) => {
          const clockIn = this.$store.state.attendance.clockInTime;
          const clockOut = this.$store.state.attendance.clockOutTime;
          const isUploading = this.$store.state.attendance.isUploadingLocation;
          common_vendor.index.__f__("log", "at pages/home/<USER>", "获取今日打卡记录成功:", {
            "上班打卡": clockIn || "未打卡",
            "下班打卡": clockOut || "未打卡",
            "是否上传位置": isUploading ? "是" : "否",
            "定位上传周期": positionCycle + "分钟"
          });
        }).catch((err) => {
          common_vendor.index.__f__("error", "at pages/home/<USER>", "获取今日打卡记录失败:", err);
          this.$store.dispatch("attendance/stopLocationUpload");
        });
      } else {
        common_vendor.index.__f__("log", "at pages/home/<USER>", "未开启定位上传功能，不进行位置上传");
        this.$store.dispatch("attendance/stopLocationUpload");
      }
    },
    // 启动位置上传
    startLocationUpload() {
      this.$store.dispatch("attendance/startLocationUpload");
    },
    // 停止位置上传
    stopLocationUpload() {
      this.$store.dispatch("attendance/stopLocationUpload");
    },
    // 加载首页数据
    loadHomeData() {
      this.getRecentOrders();
      this.getStatisticsData();
      this.getRecentPatrolTasks();
    },
    // 获取最近工单
    getRecentOrders() {
      utils_api.homeApi.getRecentOrders(3).then((res) => {
        if (res.code === 200 && Array.isArray(res.data.list)) {
          const currentUserId = common_vendor.index.getStorageSync("userId");
          this.recentTasks = res.data.list.map((item) => {
            let statusText = item.orderStatus;
            if (item.transferUserId && item.transferUserId === currentUserId) {
              statusText = "已转派";
            }
            return {
              id: item.orderId,
              code: item.orderNo,
              title: `${item.heatUnitName} ${item.faultType}`,
              desc: item.faultLevel,
              time: item.createdTime,
              status: this.getStatusClass(statusText),
              // 使用可能已修改的状态文本来获取样式类
              statusText,
              // 使用可能已修改的状态文本
              transferUserId: item.transferUserId
              // 保存转派用户ID以备后续使用
            };
          });
        } else {
          common_vendor.index.__f__("error", "at pages/home/<USER>", "获取最近工单失败:", res);
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/home/<USER>", "获取最近工单异常:", err);
      });
    },
    // 获取统计数据
    getStatisticsData() {
      utils_api.homeApi.getHeatUnitCount().then((res) => {
        if (res.code === 200 && res.data) {
          this.statsData.heatUnitCount = res.data.count || 0;
        } else {
          common_vendor.index.__f__("error", "at pages/home/<USER>", "获取热用户统计数据失败:", res);
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/home/<USER>", "获取热用户统计数据异常:", err);
      });
      utils_api.homeApi.getHeatUnitOnlineRate().then((res) => {
        if (res.code === 200 && res.data) {
          this.statsData.hesOnlineRate = res.data.onlineRate || "0%";
        } else {
          common_vendor.index.__f__("error", "at pages/home/<USER>", "获取换热站在线率统计数据失败:", res);
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/home/<USER>", "获取换热站在线率统计数据异常:", err);
      });
      utils_api.homeApi.getWeeklyFaultCount().then((res) => {
        if (res.code === 200 && res.data) {
          this.statsData.weeklyAlarms = res.data.count || 0;
        } else {
          common_vendor.index.__f__("error", "at pages/home/<USER>", "获取本周故障告警统计数据失败:", res);
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/home/<USER>", "获取本周故障告警统计数据异常:", err);
      });
    },
    // 根据工单状态获取状态样式类
    getStatusClass(status) {
      switch (status) {
        case "待接单":
          return "warning";
        case "进行中":
        case "维修中":
          return "primary";
        case "已完成":
          return "success";
        case "已转派":
          return "info";
        default:
          return "error";
      }
    },
    // 页面导航
    navTo(url) {
      common_vendor.index.navigateTo({
        url
      });
    },
    // 兼容旧方法
    navigateTo(url) {
      this.navTo(url);
    },
    // 获取热用户列表
    getHeatUnitList() {
      return new Promise((resolve, reject) => {
        utils_api.heatUnitApi.getList().then((res) => {
          common_vendor.index.__f__("log", "at pages/home/<USER>", "热用户列表接口响应:", res);
          if (Array.isArray(res.data)) {
            this.heatUnitList = res.data;
            this.heatUnitOptions = this.heatUnitList.map((item) => item.name);
            if (this.heatUnitList.length === 0) {
              reject(new Error("暂无热用户数据"));
            } else {
              resolve();
            }
          } else {
            reject(new Error(res.message || "获取热用户列表失败"));
          }
        }).catch((err) => {
          common_vendor.index.__f__("error", "at pages/home/<USER>", "请求失败详情:", err);
          reject(new Error("网络请求失败，请检查网络连接"));
        });
      });
    },
    // 显示室温上报弹窗
    async showTempReportModal() {
      let loadingShown = false;
      try {
        loadingShown = true;
        common_vendor.index.showLoading({
          title: "加载中...",
          mask: true
        });
        await this.getHeatUnitList();
        await Promise.all([this.getOutdoorTemperature(), this.getLocation()]);
        this.$refs.tempReportPopup.open();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/home/<USER>", "加载数据失败:", error);
        common_vendor.index.showToast({
          title: error.message || "加载失败，请重试",
          icon: "none",
          duration: 3e3
        });
      } finally {
        if (loadingShown) {
          common_vendor.index.hideLoading();
        }
      }
    },
    handleTempChange(e) {
      this.reportTemp = e.detail.value;
    },
    // 获取室外温度
    getOutdoorTemperature() {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          const temp = (Math.random() * 10 + 15).toFixed(1);
          this.outdoorTemp = temp;
          resolve(temp);
        }, 500);
      });
    },
    // 获取位置信息
    getLocation() {
      return new Promise((resolve, reject) => {
        common_vendor.index.getLocation({
          type: "gcj02",
          isHighAccuracy: true,
          highAccuracyExpireTime: 3e3,
          success: (res) => {
            this.latitude = res.latitude;
            this.longitude = res.longitude;
            resolve(res);
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/home/<USER>", "获取位置失败:", err);
            this.latitude = 34.343147;
            this.longitude = 108.939621;
            let errorMsg = "获取位置失败";
            if (err.errMsg.includes("permission")) {
              errorMsg = "请授权位置权限";
            } else if (err.errMsg.includes("timeout")) {
              errorMsg = "获取位置超时";
            }
            common_vendor.index.showToast({
              title: errorMsg,
              icon: "none"
            });
            resolve({
              latitude: this.latitude,
              longitude: this.longitude
            });
          }
        });
      });
    },
    // 选择图片
    chooseImage() {
      common_vendor.index.chooseImage({
        count: 3 - this.uploadImages.length,
        sizeType: ["compressed"],
        sourceType: ["camera", "album"],
        success: (res) => {
          this.uploadImages = [...this.uploadImages, ...res.tempFilePaths];
        }
      });
    },
    // 删除图片
    deleteImage(index) {
      this.uploadImages.splice(index, 1);
    },
    // 取消室温上报
    cancelTempReport() {
      this.resetForm();
      this.$refs.tempReportPopup.close();
    },
    // 重置表单
    resetForm() {
      this.heatUnitIndex = 0;
      this.buildingNo = "";
      this.unitNo = "";
      this.roomNo = "";
      this.reportTemp = 22;
      this.reportRemark = "";
      this.uploadImages = [];
    },
    // 上传图片
    async uploadImage(filePath) {
      return new Promise((resolve, reject) => {
        common_vendor.index.uploadFile({
          url: "/api/upload/image",
          filePath,
          name: "file",
          header: {
            Authorization: "Bearer " + common_vendor.index.getStorageSync("token")
          },
          success: (res) => {
            try {
              const data = JSON.parse(res.data);
              if (data.code === 200) {
                resolve(data.data.url);
              } else {
                reject(new Error(data.message || "上传失败"));
              }
            } catch (e) {
              reject(new Error("解析响应失败"));
            }
          },
          fail: (err) => {
            reject(new Error("网络请求失败"));
          }
        });
      });
    },
    // 提交室温上报
    async submitTempReport() {
      try {
        if (this.heatUnitOptions.length === 0) {
          throw new Error("请等待热用户数据加载");
        }
        const validations = [
          { value: this.buildingNo.trim(), message: "请输入楼栋号" },
          { value: this.unitNo.trim(), message: "请输入单元号" },
          { value: this.roomNo.trim(), message: "请输入户号" }
        ];
        for (const validation of validations) {
          if (!validation.value) {
            throw new Error(validation.message);
          }
        }
        common_vendor.index.showLoading({
          title: "正在提交",
          mask: true
        });
        const uploadedImages = [];
        if (this.uploadImages.length > 0) {
          try {
            for (const filePath of this.uploadImages) {
              const imageUrl = await this.uploadImage(filePath);
              uploadedImages.push(imageUrl);
            }
          } catch (error) {
            throw new Error("图片上传失败：" + error.message);
          }
        }
        const reportData = {
          heat_unit_name: this.heatUnitOptions[this.heatUnitIndex],
          building_no: this.buildingNo.trim(),
          unit_no: this.unitNo.trim(),
          room_no: this.roomNo.trim(),
          indoor_temp: this.reportTemp,
          outdoor_temp: this.outdoorTemp,
          latitude: this.latitude,
          longitude: this.longitude,
          images: uploadedImages,
          videos: [],
          // Assuming no videos are uploaded in this context
          remark: this.reportRemark.trim(),
          report_user_id: common_vendor.index.getStorageSync("userId")
          // Assuming user_id is stored in local storage
        };
        const response = await utils_api.temperatureReportApi.submit(reportData);
        if (response.code === 200) {
          common_vendor.index.showToast({
            title: "室温上报成功",
            icon: "success"
          });
          this.resetForm();
          this.$refs.tempReportPopup.close();
        } else {
          throw new Error(response.message || "提交失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/home/<USER>", "提交失败:", error);
        let errorMsg = "提交失败，请重试";
        if (error.errMsg) {
          if (error.errMsg.includes("timeout")) {
            errorMsg = "请求超时，请检查网络连接";
          } else if (error.errMsg.includes("network")) {
            errorMsg = "网络连接异常，请检查网络";
          }
        } else if (error.message) {
          errorMsg = error.message;
        }
        common_vendor.index.showToast({
          title: errorMsg,
          icon: "none",
          duration: 2e3
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    handleHeatUnitChange(e) {
      this.heatUnitIndex = e.detail.value;
    },
    // 查看工单详情
    viewOrderDetail(id) {
      common_vendor.index.navigateTo({
        url: `/pages/workorder/detail?id=${id}`
      });
    },
    // 获取最近巡检工单数据
    getRecentPatrolTasks() {
      utils_api.patrolApi.getLimitedPatrolRecords(3).then((res) => {
        if (res.code === 200 && Array.isArray(res.data)) {
          this.patrolTasks = res.data.map((item) => {
            return {
              id: item.id,
              code: `${item.patrolType}`,
              title: item.planName || "巡检计划",
              desc: `执行人:${item.executorName || "未指定"}`,
              time: this.formatPatrolDateTime(item.executionDate || item.createTime),
              status: this.getPatrolStatusClass(item.status),
              statusText: this.getPatrolStatusText(item.status)
            };
          });
        } else {
          common_vendor.index.__f__("error", "at pages/home/<USER>", "获取最近巡检工单失败:", res);
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/home/<USER>", "获取最近巡检工单异常:", err);
      });
    },
    // 获取巡检状态文本
    getPatrolStatusText(status) {
      switch (status) {
        case "pending":
          return "待执行";
        case "processing":
          return "执行中";
        case "completed":
          return "已完成";
        case "overdue":
          return "已超时";
        default:
          return status || "未知";
      }
    },
    // 获取巡检状态样式类
    getPatrolStatusClass(status) {
      switch (status) {
        case "pending":
          return "warning";
        case "processing":
          return "primary";
        case "completed":
          return "success";
        case "overdue":
          return "error";
        default:
          return "error";
      }
    },
    // 格式化巡检工单日期时间
    formatPatrolDateTime(dateTime) {
      if (!dateTime)
        return "";
      if (Array.isArray(dateTime)) {
        if (dateTime.length >= 3) {
          const year = dateTime[0];
          const month = String(dateTime[1]).padStart(2, "0");
          const day = String(dateTime[2]).padStart(2, "0");
          return `${year}-${month}-${day}`;
        }
        return dateTime.join("-");
      }
      const dateTimeStr = String(dateTime);
      if (dateTimeStr.includes("T")) {
        const date = new Date(dateTimeStr);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        return `${year}-${month}-${day}`;
      }
      if (!isNaN(dateTime) && typeof dateTime === "number") {
        const date = new Date(dateTime);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        return `${year}-${month}-${day}`;
      }
      if (dateTimeStr.includes(" ")) {
        return dateTimeStr.split(" ")[0];
      }
      return dateTimeStr;
    },
    // 新增方法：查看巡检工单详情
    viewPatrolDetail(id) {
      common_vendor.index.navigateTo({
        url: `/pages/patrol/record_detail?id=${id}`
      });
    }
  }
};
if (!Array) {
  const _component_PermissionCheck = common_vendor.resolveComponent("PermissionCheck");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  const _component_BaseTabBar = common_vendor.resolveComponent("BaseTabBar");
  (_component_PermissionCheck + _easycom_uni_popup2 + _component_BaseTabBar)();
}
const _easycom_uni_popup = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($options.currentDate),
    b: common_vendor.t($options.currentWeekDay),
    c: common_vendor.t($data.statsData.heatUnitCount),
    d: common_vendor.t($data.statsData.hesOnlineRate),
    e: common_vendor.t($data.statsData.weeklyAlarms),
    f: common_vendor.p({
      permission: "home:data-show"
    }),
    g: common_assets._imports_0,
    h: common_vendor.o(($event) => $options.navTo("/pages/patrol/plans")),
    i: common_vendor.p({
      permission: "home:patrol-plans"
    }),
    j: common_assets._imports_1,
    k: common_vendor.o(($event) => $options.navTo("/pages/patrol/records")),
    l: common_vendor.p({
      permission: "home:patrol-record"
    }),
    m: common_assets._imports_2,
    n: common_vendor.o(($event) => $options.navTo("/pages/fault/report")),
    o: common_vendor.p({
      permission: "home:fault-report"
    }),
    p: common_assets._imports_3,
    q: common_vendor.o(($event) => $options.navigateTo("/pages/fault/list")),
    r: common_vendor.p({
      permission: "home:fault-list"
    }),
    s: common_assets._imports_4,
    t: common_vendor.o(($event) => $options.navigateTo("/pages/hes/control")),
    v: common_vendor.p({
      permission: "home:hes-control"
    }),
    w: common_assets._imports_5,
    x: common_vendor.o(($event) => $options.navigateTo("/pages/valves/control")),
    y: common_vendor.p({
      permission: "home:valves-control"
    }),
    z: common_vendor.o((...args) => $options.showTempReportModal && $options.showTempReportModal(...args)),
    A: common_vendor.p({
      permission: "home:temp-report"
    }),
    B: common_vendor.o(($event) => $options.navigateTo("/pages/workorder/list")),
    C: common_vendor.p({
      permission: "home:workorder-list"
    }),
    D: common_assets._imports_6,
    E: common_vendor.o(($event) => $options.navigateTo("/pages/payment/stats")),
    F: common_vendor.p({
      permission: "home:payment"
    }),
    G: common_vendor.o(($event) => $options.navTo("/pages/attendance/clock-in")),
    H: common_vendor.p({
      permission: "home:attendance-clock"
    }),
    I: common_vendor.o(($event) => $options.navTo("/pages/device/list")),
    J: common_vendor.p({
      permission: "home:device-list"
    }),
    K: $data.recentTasks.length > 0
  }, $data.recentTasks.length > 0 ? {
    L: common_vendor.o(($event) => $options.navigateTo("/pages/workorder/list")),
    M: common_vendor.f($data.recentTasks, (task, index, i0) => {
      return {
        a: common_vendor.t(task.code),
        b: common_vendor.t(task.statusText),
        c: common_vendor.n(task.status),
        d: common_vendor.t(task.title),
        e: common_vendor.t(task.desc),
        f: common_vendor.t(task.time),
        g: index,
        h: common_vendor.o(($event) => $options.viewOrderDetail(task.id), index)
      };
    })
  } : {}, {
    N: common_vendor.p({
      permission: "home:workorder-list"
    }),
    O: $data.patrolTasks.length > 0
  }, $data.patrolTasks.length > 0 ? {
    P: common_vendor.o(($event) => $options.navigateTo("/pages/patrol/records")),
    Q: common_vendor.f($data.patrolTasks, (task, index, i0) => {
      return {
        a: common_vendor.t(task.code),
        b: common_vendor.t(task.statusText),
        c: common_vendor.n(task.status),
        d: common_vendor.t(task.title),
        e: common_vendor.t(task.desc),
        f: common_vendor.t(task.time),
        g: index,
        h: common_vendor.o(($event) => $options.viewPatrolDetail(task.id), index)
      };
    })
  } : {}, {
    R: common_vendor.p({
      permission: "home:patrol-record"
    }),
    S: common_vendor.t($data.heatUnitOptions[$data.heatUnitIndex]),
    T: common_vendor.o((...args) => $options.handleHeatUnitChange && $options.handleHeatUnitChange(...args)),
    U: $data.heatUnitIndex,
    V: $data.heatUnitOptions,
    W: $data.buildingNo,
    X: common_vendor.o(($event) => $data.buildingNo = $event.detail.value),
    Y: $data.unitNo,
    Z: common_vendor.o(($event) => $data.unitNo = $event.detail.value),
    aa: $data.roomNo,
    ab: common_vendor.o(($event) => $data.roomNo = $event.detail.value),
    ac: common_vendor.o((...args) => $options.handleTempChange && $options.handleTempChange(...args)),
    ad: $data.reportTemp,
    ae: common_vendor.t($data.reportTemp),
    af: common_vendor.t($data.outdoorTemp),
    ag: common_vendor.f($data.uploadImages, (item, index, i0) => {
      return {
        a: item,
        b: common_vendor.o(($event) => $options.deleteImage(index), index),
        c: index
      };
    }),
    ah: $data.uploadImages.length < 3
  }, $data.uploadImages.length < 3 ? {
    ai: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args))
  } : {}, {
    aj: $data.reportRemark,
    ak: common_vendor.o(($event) => $data.reportRemark = $event.detail.value),
    al: common_vendor.o((...args) => $options.cancelTempReport && $options.cancelTempReport(...args)),
    am: common_vendor.o((...args) => $options.submitTempReport && $options.submitTempReport(...args)),
    an: common_vendor.sr("tempReportPopup", "6213ef90-15,6213ef90-0"),
    ao: common_vendor.p({
      type: "center"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/home/<USER>
