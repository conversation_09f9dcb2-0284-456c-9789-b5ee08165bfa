<view class="fault-report-container"><view class="form-card"><view class="form-group"><view class="form-item"><text class="form-label required">热用户</text><view class="form-picker-container"><picker class="form-picker" bindchange="{{c}}" value="{{d}}" range="{{e}}" range-key="name"><view class="{{['picker-text', b && 'placeholder']}}">{{a}}</view></picker></view></view><view class="form-item"><text class="form-label required">详细住户</text><view class="{{['address-inputs', p && 'error']}}"><view class="{{['address-input-item', h && 'error']}}"><input class="form-input" type="number" placeholder="楼号" maxlength="2" value="{{f}}" bindinput="{{g}}"/><text class="address-input-label">号楼</text></view><text class="address-separator">-</text><view class="{{['address-input-item', k && 'error']}}"><input class="form-input" type="number" placeholder="单元" maxlength="2" value="{{i}}" bindinput="{{j}}"/><text class="address-input-label">单元</text></view><text class="address-separator">-</text><view class="{{['address-input-item', 'room-input', o && 'error']}}"><input class="form-input" type="number" placeholder="房号" maxlength="4" bindblur="{{l}}" bindinput="{{m}}" value="{{n}}"/><text class="address-input-label">房号</text></view></view><text class="address-hint">请输入楼号、单元号和房号 (可选)</text></view><view class="form-item"><text class="form-label required">故障类型</text><view class="form-picker-container"><picker class="form-picker" bindchange="{{s}}" value="{{t}}" range="{{v}}"><view class="{{['picker-text', r && 'placeholder']}}">{{q}}</view></picker></view></view><view class="form-item"><text class="form-label required">故障等级</text><view class="level-options"><view wx:for="{{w}}" wx:for-item="level" wx:key="b" class="{{['level-option', level.c && 'active']}}" bindtap="{{level.d}}"><text>{{level.a}}</text></view></view></view><view class="form-item"><text class="form-label required">故障描述</text><block wx:if="{{r0}}"><textarea class="form-textarea" placeholder="请详细描述故障情况，便于维修人员了解问题" maxlength="200" value="{{x}}" bindinput="{{y}}"></textarea></block><text class="textarea-counter">{{z}}/200</text></view></view></view><view class="form-card"><view class="form-header"><text class="form-title">图片附件</text><text class="form-subtitle">上传故障现场图片（最多6张）</text></view><view class="upload-area"><view class="image-list"><view wx:for="{{A}}" wx:for-item="image" wx:key="d" class="image-item"><image src="{{image.a}}" mode="aspectFill" bindtap="{{image.b}}"></image><text class="delete-icon" bindtap="{{image.c}}">×</text></view><view wx:if="{{B}}" class="upload-item" bindtap="{{C}}"><text class="iconfont icon-camera"></text><text>上传图片</text></view></view></view></view><view class="form-card"><view class="form-header"><text class="form-title">视频附件</text><text class="form-subtitle">上传故障现场视频（最多1个）</text></view><view class="upload-area"><view wx:if="{{D}}" class="video-container"><video src="{{E}}" controls object-fit="fill" initial-time="0" show-fullscreen-btn="true" show-play-btn="true" show-center-play-btn="true" enable-progress-gesture="true" auto-pause-if-navigate="true" auto-pause-if-open-native="true" codec="h264" binderror="{{F}}" bindloadedmetadata="{{G}}"></video><text class="delete-icon" bindtap="{{H}}">×</text><view wx:if="{{I}}" class="video-actions"><view class="video-action-btn" bindtap="{{J}}"> 在浏览器中打开 </view></view></view><view wx:if="{{K}}" class="upload-item" bindtap="{{L}}"><text class="iconfont icon-video"></text><text>上传视频</text></view></view></view><view class="form-card"><view class="form-group"><view class="form-item"><text class="form-label required">发生时间</text><view class="form-picker-container"><picker class="form-picker" mode="date" value="{{O}}" start="{{P}}" end="{{Q}}" bindchange="{{R}}"><view class="{{['picker-text', N && 'placeholder']}}">{{M}}</view></picker></view></view><view class="form-item"><text class="form-label"></text><view class="form-picker-container"><picker class="form-picker" mode="time" value="{{U}}" bindchange="{{V}}"><view class="{{['picker-text', T && 'placeholder']}}">{{S}}</view></picker></view></view></view></view><view class="submit-btn-container"><button class="submit-btn" bindtap="{{W}}">提交故障上报</button></view></view>