"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const common_assets = require("../../common/assets.js");
const PermissionCheck = () => "../../components/PermissionCheck.js";
const _sfc_main = {
  components: {
    PermissionCheck
    // 本地注册组件
  },
  data() {
    return {
      searchKeyword: "",
      patrolPlans: [],
      currentPage: 1,
      pageSize: 10,
      hasMore: true,
      isLoading: false,
      loadError: false,
      // 筛选相关
      statusFilter: "all",
      timeFilter: "all",
      typeFilter: "all",
      // 筛选选项
      statusOptions: [
        { label: "全部", value: "all" },
        { label: "待执行", value: "pending" },
        { label: "进行中", value: "processing" },
        { label: "已完成", value: "completed" }
      ],
      timeOptions: [
        { label: "全部时间", value: "all" },
        { label: "今天", value: "today" },
        { label: "本周", value: "this_week" },
        { label: "本月", value: "this_month" },
        { label: "上个月", value: "last_month" }
      ],
      typeOptions: [
        { label: "全部类型", value: "all" }
      ],
      patrolTypeOptions: []
      // 存储从后端获取的巡检类型选项
    };
  },
  computed: {
    statusFilterText() {
      const option = this.statusOptions.find((item) => item.value === this.statusFilter);
      return option ? option.label : "全部";
    },
    timeFilterText() {
      const option = this.timeOptions.find((item) => item.value === this.timeFilter);
      return option ? option.label : "全部时间";
    },
    typeFilterText() {
      const option = this.typeOptions.find((item) => item.value === this.typeFilter);
      return option ? option.label : "全部类型";
    }
  },
  onPullDownRefresh() {
    common_vendor.index.__f__("log", "at pages/patrol/plans.vue:274", "执行下拉刷新！");
    this.currentPage = 1;
    this.patrolPlans = [];
    this.hasMore = true;
    this.loadPatrolPlans().then(() => {
      common_vendor.index.stopPullDownRefresh();
    });
  },
  // 上拉加载更多
  onReachBottom() {
    common_vendor.index.__f__("log", "at pages/patrol/plans.vue:284", "执行上拉加载！");
    if (this.hasMore && !this.isLoading) {
      this.currentPage++;
      this.loadPatrolPlans(true);
    }
  },
  onLoad() {
    this.loadPatrolPlans();
    this.loadPatrolTypeOptions();
  },
  methods: {
    // 加载巡检计划列表
    async loadPatrolPlans(isLoadMore = false) {
      if (this.isLoading)
        return;
      this.isLoading = true;
      if (!isLoadMore) {
        this.loadError = false;
      }
      try {
        const params = {
          page: this.currentPage,
          pageSize: this.pageSize,
          name: this.searchKeyword,
          status: this.statusFilter !== "all" ? this.statusFilter : null,
          type: this.typeFilter !== "all" ? this.typeFilter : null,
          timeRange: this.timeFilter !== "all" ? this.timeFilter : null
        };
        const heatUnitId = common_vendor.index.getStorageSync("heatUnitId");
        params.heatUnitId = heatUnitId;
        const res = await utils_api.patrolApi.getPlanList(params);
        common_vendor.index.__f__("log", "at pages/patrol/plans.vue:322", "API响应:", res);
        if (res.code === 200) {
          let planList = [];
          let totalPages = 1;
          if (Array.isArray(res.data)) {
            planList = res.data;
            totalPages = Math.ceil(planList.length / this.pageSize);
          } else if (res.data && res.data.list) {
            planList = res.data.list;
            totalPages = res.data.totalPages || Math.ceil(res.data.total / this.pageSize) || 1;
          } else if (res.data) {
            planList = Array.isArray(res.data) ? res.data : [res.data];
            totalPages = 1;
          }
          const formattedPlans = planList.map((item) => {
            var _a;
            return {
              id: item.id,
              title: item.name,
              code: item.planNo || item.plan_no,
              type: item.patrolType || item.patrol_type,
              startTime: item.startDate || item.start_date,
              endTime: item.endDate || item.end_date,
              location: item.locations,
              manager: ((_a = item.executors) == null ? void 0 : _a.join("、")) || "未分配",
              status: item.status,
              isPlay: item.isPlay
            };
          });
          if (isLoadMore) {
            this.patrolPlans = [...this.patrolPlans, ...formattedPlans];
          } else {
            this.patrolPlans = formattedPlans;
          }
          this.hasMore = this.currentPage < totalPages;
        } else {
          this.loadError = true;
          common_vendor.index.showToast({
            title: res.message || "获取巡检计划失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/patrol/plans.vue:374", "获取巡检计划列表失败:", error);
        this.loadError = true;
        common_vendor.index.showToast({
          title: "网络异常，请稍后重试",
          icon: "none"
        });
      } finally {
        this.isLoading = false;
      }
    },
    // 搜索处理
    handleSearch() {
      this.currentPage = 1;
      this.patrolPlans = [];
      this.hasMore = true;
      this.loadPatrolPlans();
    },
    // 加载巡检类型选项
    loadPatrolTypeOptions() {
      "../../utils/api.js".then(({ dictApi }) => {
        dictApi.getDictDataByDictId(11).then((res) => {
          if (res.code === 200 && res.data) {
            this.patrolTypeOptions = res.data.map((item) => ({
              label: item.name,
              value: item.name
            }));
            this.typeOptions = [
              { label: "全部类型", value: "all" },
              ...this.patrolTypeOptions
            ];
            if (this.patrolTypeOptions.length === 0) {
              this.typeOptions = [
                { label: "全部类型", value: "all" },
                { label: "日常巡检", value: "日常巡检" },
                { label: "设备巡检", value: "设备巡检" },
                { label: "管道巡检", value: "管道巡检" },
                { label: "阀门巡检", value: "阀门巡检" },
                { label: "换热站巡检", value: "换热站巡检" }
              ];
            }
          }
        }).catch((err) => {
          common_vendor.index.__f__("error", "at pages/patrol/plans.vue:424", "获取巡检类型选项失败:", err);
          this.typeOptions = [
            { label: "全部类型", value: "all" },
            { label: "日常巡检", value: "日常巡检" },
            { label: "设备巡检", value: "设备巡检" },
            { label: "管道巡检", value: "管道巡检" },
            { label: "阀门巡检", value: "阀门巡检" },
            { label: "换热站巡检", value: "换热站巡检" }
          ];
        });
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/patrol/plans.vue:436", "导入dictApi失败:", err);
        this.typeOptions = [
          { label: "全部类型", value: "all" },
          { label: "日常巡检", value: "日常巡检" },
          { label: "设备巡检", value: "设备巡检" },
          { label: "管道巡检", value: "管道巡检" },
          { label: "阀门巡检", value: "阀门巡检" },
          { label: "换热站巡检", value: "换热站巡检" }
        ];
      });
    },
    // 清除搜索关键词
    clearSearch() {
      this.searchKeyword = "";
      this.currentPage = 1;
      this.patrolPlans = [];
      this.hasMore = true;
      this.loadPatrolPlans();
    },
    // 显示状态筛选弹窗
    showStatusFilter() {
      this.$refs.statusFilterPopup.open();
    },
    // 显示时间筛选弹窗
    showTimeFilter() {
      this.$refs.timeFilterPopup.open();
    },
    // 显示类型筛选弹窗
    showTypeFilter() {
      this.$refs.typeFilterPopup.open();
    },
    // 选择状态筛选
    selectStatusFilter(value) {
      this.statusFilter = value;
      this.currentPage = 1;
      this.patrolPlans = [];
      this.hasMore = true;
      this.loadPatrolPlans();
      this.$refs.statusFilterPopup.close();
    },
    // 选择时间筛选
    selectTimeFilter(value) {
      this.timeFilter = value;
      this.currentPage = 1;
      this.patrolPlans = [];
      this.hasMore = true;
      this.loadPatrolPlans();
      this.$refs.timeFilterPopup.close();
    },
    // 选择类型筛选
    selectTypeFilter(value) {
      this.typeFilter = value;
      this.currentPage = 1;
      this.patrolPlans = [];
      this.hasMore = true;
      this.loadPatrolPlans();
      this.$refs.typeFilterPopup.close();
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        pending: "待执行",
        processing: "进行中",
        completed: "已完成"
      };
      return statusMap[status] || "未知";
    },
    // 导航到巡检计划详情
    navigateToPlanDetail(id) {
      common_vendor.index.navigateTo({
        url: `/pages/patrol/detail?id=${id}`
      });
    },
    // 导航到创建巡检计划
    navigateToCreatePlan() {
      common_vendor.index.navigateTo({
        url: "/pages/patrol/create"
      });
    },
    // 开始巡检
    startPatrolPlan(id) {
      common_vendor.index.navigateTo({
        url: `/pages/patrol/execute?id=${id}`
      });
    },
    // 继续巡检
    continuePatrolPlan(id) {
      common_vendor.index.navigateTo({
        url: `/pages/patrol/execute?id=${id}`
      });
    },
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr)
        return "";
      const date = new Date(dateStr);
      if (isNaN(date.getTime()))
        return dateStr;
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      return `${year}-${month}-${day}`;
    }
  }
};
if (!Array) {
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  const _component_PermissionCheck = common_vendor.resolveComponent("PermissionCheck");
  (_easycom_uni_popup2 + _component_PermissionCheck)();
}
const _easycom_uni_popup = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    b: $data.searchKeyword,
    c: common_vendor.o(($event) => $data.searchKeyword = $event.detail.value),
    d: $data.searchKeyword
  }, $data.searchKeyword ? {
    e: common_vendor.o((...args) => $options.clearSearch && $options.clearSearch(...args))
  } : {}, {
    f: common_vendor.t($options.statusFilterText),
    g: common_vendor.o((...args) => $options.showStatusFilter && $options.showStatusFilter(...args)),
    h: common_vendor.t($options.timeFilterText),
    i: common_vendor.o((...args) => $options.showTimeFilter && $options.showTimeFilter(...args)),
    j: common_vendor.t($options.typeFilterText),
    k: common_vendor.o((...args) => $options.showTypeFilter && $options.showTypeFilter(...args)),
    l: $data.patrolPlans.length > 0
  }, $data.patrolPlans.length > 0 ? {
    m: common_vendor.f($data.patrolPlans, (plan, index, i0) => {
      return {
        a: common_vendor.t(plan.title),
        b: common_vendor.t($options.getStatusText(plan.status)),
        c: common_vendor.n(plan.status),
        d: common_vendor.t(plan.code),
        e: common_vendor.t($options.formatDate(plan.startTime)),
        f: common_vendor.t($options.formatDate(plan.endTime)),
        g: common_vendor.t(plan.type),
        h: common_vendor.t(plan.location),
        i: common_vendor.o(($event) => $options.navigateToPlanDetail(plan.id), index),
        j: index,
        k: common_vendor.o(($event) => $options.navigateToPlanDetail(plan.id), index)
      };
    })
  } : {}, {
    n: $data.isLoading
  }, $data.isLoading ? {} : {}, {
    o: $data.loadError && !$data.isLoading
  }, $data.loadError && !$data.isLoading ? {
    p: common_vendor.o((...args) => $options.loadPatrolPlans && $options.loadPatrolPlans(...args))
  } : {}, {
    q: !$data.isLoading && $data.patrolPlans.length === 0
  }, !$data.isLoading && $data.patrolPlans.length === 0 ? {
    r: common_assets._imports_0$1
  } : {}, {
    s: common_vendor.o(($event) => _ctx.$refs.statusFilterPopup.close()),
    t: common_vendor.f($data.statusOptions, (option, index, i0) => {
      return {
        a: common_vendor.t(option.label),
        b: index,
        c: $data.statusFilter === option.value ? 1 : "",
        d: common_vendor.o(($event) => $options.selectStatusFilter(option.value), index)
      };
    }),
    v: common_vendor.sr("statusFilterPopup", "597a6f7d-0"),
    w: common_vendor.p({
      type: "bottom"
    }),
    x: common_vendor.o(($event) => _ctx.$refs.timeFilterPopup.close()),
    y: common_vendor.f($data.timeOptions, (option, index, i0) => {
      return {
        a: common_vendor.t(option.label),
        b: index,
        c: $data.timeFilter === option.value ? 1 : "",
        d: common_vendor.o(($event) => $options.selectTimeFilter(option.value), index)
      };
    }),
    z: common_vendor.sr("timeFilterPopup", "597a6f7d-1"),
    A: common_vendor.p({
      type: "bottom"
    }),
    B: common_vendor.o(($event) => _ctx.$refs.typeFilterPopup.close()),
    C: common_vendor.f($data.typeOptions, (option, index, i0) => {
      return {
        a: common_vendor.t(option.label),
        b: index,
        c: $data.typeFilter === option.value ? 1 : "",
        d: common_vendor.o(($event) => $options.selectTypeFilter(option.value), index)
      };
    }),
    D: common_vendor.sr("typeFilterPopup", "597a6f7d-2"),
    E: common_vendor.p({
      type: "bottom"
    }),
    F: common_vendor.o((...args) => $options.navigateToCreatePlan && $options.navigateToCreatePlan(...args)),
    G: common_vendor.p({
      permission: "patrol:plans:add"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/patrol/plans.js.map
