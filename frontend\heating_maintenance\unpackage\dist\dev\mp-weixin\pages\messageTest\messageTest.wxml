<view class="container data-v-577bde04"><view class="title data-v-577bde04">消息服务测试页面</view><view class="status data-v-577bde04">当前服务状态：<text class="{{['data-v-577bde04', b && 'running', c && 'stopped']}}">{{a}}</text></view><button class="data-v-577bde04" type="primary" bindtap="{{d}}" disabled="{{e}}">启动消息服务</button><button class="data-v-577bde04" type="warn" bindtap="{{f}}" disabled="{{g}}">停止消息服务</button><view class="tips data-v-577bde04"><text class="data-v-577bde04">请打开浏览器控制台查看服务运行日志和接口调用情况。</text></view></view>