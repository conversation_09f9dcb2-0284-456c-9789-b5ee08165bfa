<view class="uni-calendar data-v-3099330a"><view wx:if="{{a}}" class="{{['uni-calendar__mask', 'data-v-3099330a', b && 'uni-calendar--mask-show']}}" bindtap="{{c}}"></view><view wx:if="{{d}}" class="{{['uni-calendar__content', 'data-v-3099330a', B && 'uni-calendar--fixed', C && 'uni-calendar--ani-show']}}"><view wx:if="{{e}}" class="uni-calendar__header uni-calendar--fixed-top data-v-3099330a"><view class="uni-calendar__header-btn-box data-v-3099330a" bindtap="{{g}}"><text class="uni-calendar__header-text uni-calendar--fixed-width data-v-3099330a">{{f}}</text></view><view class="uni-calendar__header-btn-box data-v-3099330a" bindtap="{{i}}"><text class="uni-calendar__header-text uni-calendar--fixed-width data-v-3099330a">{{h}}</text></view></view><view class="uni-calendar__header data-v-3099330a"><view class="uni-calendar__header-btn-box data-v-3099330a" catchtap="{{j}}"><view class="uni-calendar__header-btn uni-calendar--left data-v-3099330a"></view></view><picker class="data-v-3099330a" mode="date" value="{{l}}" fields="month" bindchange="{{m}}"><text class="uni-calendar__header-text data-v-3099330a">{{k}}</text></picker><view class="uni-calendar__header-btn-box data-v-3099330a" catchtap="{{n}}"><view class="uni-calendar__header-btn uni-calendar--right data-v-3099330a"></view></view><text class="uni-calendar__backtoday data-v-3099330a" bindtap="{{p}}">{{o}}</text></view><view class="uni-calendar__box data-v-3099330a"><view wx:if="{{q}}" class="uni-calendar__box-bg data-v-3099330a"><text class="uni-calendar__box-bg-text data-v-3099330a">{{r}}</text></view><view class="uni-calendar__weeks data-v-3099330a"><view class="uni-calendar__weeks-day data-v-3099330a"><text class="uni-calendar__weeks-day-text data-v-3099330a">{{s}}</text></view><view class="uni-calendar__weeks-day data-v-3099330a"><text class="uni-calendar__weeks-day-text data-v-3099330a">{{t}}</text></view><view class="uni-calendar__weeks-day data-v-3099330a"><text class="uni-calendar__weeks-day-text data-v-3099330a">{{v}}</text></view><view class="uni-calendar__weeks-day data-v-3099330a"><text class="uni-calendar__weeks-day-text data-v-3099330a">{{w}}</text></view><view class="uni-calendar__weeks-day data-v-3099330a"><text class="uni-calendar__weeks-day-text data-v-3099330a">{{x}}</text></view><view class="uni-calendar__weeks-day data-v-3099330a"><text class="uni-calendar__weeks-day-text data-v-3099330a">{{y}}</text></view><view class="uni-calendar__weeks-day data-v-3099330a"><text class="uni-calendar__weeks-day-text data-v-3099330a">{{z}}</text></view></view><view wx:for="{{A}}" wx:for-item="item" wx:key="b" class="uni-calendar__weeks data-v-3099330a"><view wx:for="{{item.a}}" wx:for-item="weeks" wx:key="d" class="uni-calendar__weeks-item data-v-3099330a"><calendar-item wx:if="{{weeks.c}}" class="uni-calendar-item--hook data-v-3099330a" bindchange="{{weeks.a}}" u-i="{{weeks.b}}" bind:__l="__l" u-p="{{weeks.c}}"></calendar-item></view></view></view></view></view>