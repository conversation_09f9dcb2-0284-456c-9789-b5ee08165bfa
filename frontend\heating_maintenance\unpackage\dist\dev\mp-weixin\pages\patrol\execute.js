"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const utils_upload = require("../../utils/upload.js");
const _sfc_main = {
  data() {
    return {
      planId: "",
      planTasks: [],
      currentTaskIndex: 0,
      taskResult: {
        actualValue: "",
        checkResult: "normal",
        remark: "",
        images: []
      },
      isLoading: false,
      isSkipping: false,
      isCompleting: false,
      completedResults: []
      // 存储所有已完成的巡检结果
    };
  },
  computed: {
    currentTask() {
      return this.planTasks[this.currentTaskIndex] || {};
    },
    progressPercent() {
      if (!this.planTasks.length)
        return 0;
      const completedTasks = this.planTasks.filter((task) => task.status === "completed").length;
      return Math.floor(completedTasks / this.planTasks.length * 100);
    }
  },
  onLoad(options) {
    this.planId = options.planId;
    this.id = options.id;
    common_vendor.index.__f__("log", "at pages/patrol/execute.vue:197", "记录id=", options.id);
    common_vendor.index.__f__("log", "at pages/patrol/execute.vue:198", "计划id=", options.planId);
    this.loadPlanTasks();
  },
  methods: {
    // 格式化日期时间为 YYYY-MM-DD HH:MM:SS
    formatDateTime(dateStr) {
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    // 加载巡检任务列表
    loadPlanTasks() {
      this.isLoading = true;
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      utils_api.patrolApi.getItemList({
        planId: this.planId
      }).then((res) => {
        if (res.data && Array.isArray(res.data)) {
          this.planTasks = res.data.map((item) => ({
            ...item,
            status: "pending",
            // 初始状态都设为待处理
            completedTime: null
          }));
          this.findNextPendingTask();
        } else {
          common_vendor.index.showToast({
            title: "获取数据格式错误",
            icon: "none"
          });
        }
        this.isLoading = false;
        common_vendor.index.hideLoading();
      }).catch((err) => {
        common_vendor.index.__f__("warn", "at pages/patrol/execute.vue:243", "获取巡检任务列表失败", err);
        this.isLoading = false;
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "加载任务失败，请重试",
          icon: "none"
        });
      });
    },
    // 查找下一个待处理任务
    findNextPendingTask() {
      const pendingTaskIndex = this.planTasks.findIndex((task) => task.status === "pending");
      if (pendingTaskIndex !== -1) {
        this.currentTaskIndex = pendingTaskIndex;
        this.resetTaskResult();
      }
    },
    // 重置当前任务的录入结果
    resetTaskResult() {
      this.taskResult = {
        actualValue: "",
        checkResult: "normal",
        remark: "",
        images: []
      };
    },
    // 选择图片
    uploadImage() {
      common_vendor.index.chooseImage({
        count: 3 - this.taskResult.images.length,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          common_vendor.index.showLoading({
            title: "上传中...",
            mask: true
          });
          const token = common_vendor.index.getStorageSync("token");
          if (!token) {
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "请先登录",
              icon: "none"
            });
            return;
          }
          const uploadPromises = res.tempFilePaths.map((path) => {
            return utils_upload.uploadUtils.uploadImage(path).then((serverPath) => {
              this.taskResult.images.push({
                url: path,
                serverUrl: serverPath
              });
              return serverPath;
            });
          });
          Promise.all(uploadPromises).then(() => {
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "上传成功",
              icon: "success"
            });
          }).catch((err) => {
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: err.message || "上传失败",
              icon: "none"
            });
          });
        }
      });
    },
    // 删除图片
    deleteImage(index) {
      this.taskResult.images.splice(index, 1);
    },
    // 预览图片
    previewImage(url) {
      const previewUrls = this.taskResult.images.map((img) => img.url);
      common_vendor.index.previewImage({
        urls: previewUrls,
        current: url
      });
    },
    // 上一个任务
    prevTask() {
      if (this.currentTaskIndex > 0) {
        this.saveCurrentInputToTemp();
        this.currentTaskIndex--;
        this.restoreInputFromTemp();
      }
    },
    // 跳过任务
    skipTask() {
      this.isSkipping = true;
      this.$refs.skipPopup.open();
    },
    // 取消跳过
    cancelSkip() {
      this.$refs.skipPopup.close();
      this.isSkipping = false;
    },
    // 确认跳过
    confirmSkip() {
      this.$refs.skipPopup.close();
      this.isSkipping = false;
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      const skippedResult = {
        itemId: this.currentTask.id,
        actualValue: "",
        checkResult: "normal",
        remark: "已跳过",
        images: [],
        skipped: true
      };
      this.planTasks[this.currentTaskIndex] = {
        ...this.planTasks[this.currentTaskIndex],
        status: "skipped",
        skippedTime: this.formatDateTime((/* @__PURE__ */ new Date()).toISOString())
      };
      const existingIndex = this.completedResults.findIndex((item) => item.itemId === skippedResult.itemId);
      if (existingIndex !== -1) {
        this.completedResults[existingIndex] = skippedResult;
      } else {
        this.completedResults.push(skippedResult);
      }
      common_vendor.index.hideLoading();
      common_vendor.index.showToast({
        title: "已跳过此项",
        icon: "none"
      });
      if (this.currentTaskIndex < this.planTasks.length - 1) {
        this.currentTaskIndex++;
        this.resetTaskResult();
      } else {
        this.isCompleting = true;
        this.$refs.completePopup.open();
      }
    },
    // 提交任务
    submitTask() {
      if (!this.taskResult.actualValue) {
        common_vendor.index.showToast({
          title: "请输入实际值",
          icon: "none"
        });
        return;
      }
      const processedImages = this.taskResult.images.map((img) => ({
        url: img.url,
        serverUrl: img.serverUrl
      }));
      const currentResult = {
        itemId: this.currentTask.id,
        actualValue: this.taskResult.actualValue,
        checkResult: this.taskResult.checkResult,
        remark: this.taskResult.remark || "",
        images: processedImages || []
      };
      this.planTasks[this.currentTaskIndex] = {
        ...this.planTasks[this.currentTaskIndex],
        actualValue: this.taskResult.actualValue,
        checkResult: this.taskResult.checkResult,
        remark: this.taskResult.remark,
        images: processedImages,
        status: "completed",
        completedTime: this.formatDateTime((/* @__PURE__ */ new Date()).toISOString())
      };
      const existingIndex = this.completedResults.findIndex((item) => item.itemId === currentResult.itemId);
      if (existingIndex !== -1) {
        this.completedResults[existingIndex] = currentResult;
      } else {
        this.completedResults.push(currentResult);
      }
      common_vendor.index.hideLoading();
      if (this.currentTaskIndex < this.planTasks.length - 1) {
        this.saveCurrentInputToTemp();
        this.currentTaskIndex++;
        this.restoreInputFromTemp();
      } else {
        this.isCompleting = true;
        this.$refs.completePopup.open();
      }
    },
    // 保存当前输入到临时存储
    saveCurrentInputToTemp() {
      const tempData = {
        itemId: this.currentTask.id,
        actualValue: this.taskResult.actualValue,
        checkResult: this.taskResult.checkResult,
        remark: this.taskResult.remark,
        images: this.taskResult.images
      };
      const taskIndex = this.planTasks.findIndex((task) => task.id === this.currentTask.id);
      if (taskIndex !== -1) {
        this.planTasks[taskIndex].tempData = tempData;
      }
    },
    // 从临时存储恢复输入
    restoreInputFromTemp() {
      this.resetTaskResult();
      const currentTask = this.planTasks[this.currentTaskIndex];
      if (currentTask.status === "completed") {
        this.taskResult.actualValue = currentTask.actualValue || "";
        this.taskResult.checkResult = currentTask.checkResult || "normal";
        this.taskResult.remark = currentTask.remark || "";
        this.taskResult.images = currentTask.images || [];
      } else if (currentTask.tempData) {
        this.taskResult.actualValue = currentTask.tempData.actualValue || "";
        this.taskResult.checkResult = currentTask.tempData.checkResult || "normal";
        this.taskResult.remark = currentTask.tempData.remark || "";
        this.taskResult.images = currentTask.tempData.images || [];
      }
    },
    // 取消完成
    cancelComplete() {
      this.isCompleting = false;
      this.$refs.completePopup.close();
    },
    // 确认完成
    confirmComplete() {
      this.$refs.completePopup.close();
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      const pendingTasks = this.planTasks.filter((task) => task.status === "pending");
      if (pendingTasks.length > 0) {
        common_vendor.index.hideLoading();
        common_vendor.index.showModal({
          title: "提示",
          content: `还有${pendingTasks.length}个巡检项未完成，是否继续提交？`,
          success: (res) => {
            if (res.confirm) {
              this.submitAllResults();
            }
          }
        });
        return;
      }
      this.submitAllResults();
    },
    // 提交所有巡检结果
    submitAllResults() {
      var _a;
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      const startTimeISO = ((_a = this.planTasks[0]) == null ? void 0 : _a.completedTime) || (/* @__PURE__ */ new Date()).toISOString();
      const endTimeISO = (/* @__PURE__ */ new Date()).toISOString();
      const submitData = {
        patrol_plan_id: this.planId,
        id: this.id,
        executor_id: common_vendor.index.getStorageSync("userId") || 1,
        // 从缓存获取当前登录用户ID，如果没有则默认为1
        start_time: this.formatDateTime(startTimeISO),
        // 格式化为 YYYY-MM-DD HH:MM:SS
        end_time: this.formatDateTime(endTimeISO),
        // 格式化为 YYYY-MM-DD HH:MM:SS
        status: "completed",
        remark: "巡检完成",
        patrol_results: this.completedResults.map((item) => {
          return {
            patrol_item_id: item.itemId,
            check_result: item.checkResult === "abnormal" ? "abnormal" : "normal",
            param_value: item.actualValue,
            description: item.remark || "",
            images: item.images.map((img) => img.serverUrl || img.url),
            // 优先使用服务器路径
            latitude: 0,
            // 此处可以添加定位功能获取实际经纬度
            longitude: 0
          };
        })
      };
      utils_api.patrolApi.submitPatrolRecord(submitData).then((res) => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "巡检已完成",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack({
            delta: 2
          });
        }, 1500);
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/patrol/execute.vue:628", "提交巡检记录失败:", err);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "提交失败，请重试",
          icon: "none"
        });
      });
    },
    // 获取重要性类
    getImportanceClass(importance) {
      switch (importance) {
        case "normal":
          return "normal-importance";
        case "important":
          return "important-importance";
        case "critical":
          return "critical-importance";
        default:
          return "normal-importance";
      }
    },
    // 获取重要性标签
    getImportanceLabel(importance) {
      switch (importance) {
        case "normal":
          return "普通";
        case "important":
          return "重要";
        case "critical":
          return "关键";
        default:
          return "普通";
      }
    },
    // 选择类型输入
    onSelectionChange(e) {
      this.taskResult.actualValue = e.detail.value;
    },
    // 检查结果输入
    onCheckResultChange(result) {
      this.taskResult.checkResult = result;
    }
  }
};
if (!Array) {
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  _easycom_uni_popup2();
}
const _easycom_uni_popup = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.isLoading
  }, $data.isLoading ? {} : common_vendor.e({
    b: $options.progressPercent + "%",
    c: common_vendor.t($data.currentTaskIndex + 1),
    d: common_vendor.t($data.planTasks.length),
    e: common_vendor.t($options.progressPercent),
    f: common_vendor.t($options.currentTask.itemName),
    g: common_vendor.t($options.currentTask.deviceName),
    h: common_vendor.t($options.currentTask.categoryName),
    i: common_vendor.t($options.currentTask.description),
    j: common_vendor.t($options.currentTask.checkMethod),
    k: $options.currentTask.unit
  }, $options.currentTask.unit ? {
    l: common_vendor.t($options.currentTask.unit)
  } : {}, {
    m: common_vendor.t($options.getImportanceLabel($options.currentTask.importance)),
    n: common_vendor.n($options.getImportanceClass($options.currentTask.importance)),
    o: $options.currentTask.paramType === "numeric"
  }, $options.currentTask.paramType === "numeric" ? {
    p: common_vendor.t($options.currentTask.unit)
  } : {}, {
    q: $options.currentTask.paramType === "numeric" || $options.currentTask.paramType === "selection"
  }, $options.currentTask.paramType === "numeric" || $options.currentTask.paramType === "selection" ? {
    r: $data.taskResult.actualValue,
    s: common_vendor.o(($event) => $data.taskResult.actualValue = $event.detail.value)
  } : {
    t: $data.taskResult.actualValue,
    v: common_vendor.o(($event) => $data.taskResult.actualValue = $event.detail.value)
  }, {
    w: $data.taskResult.checkResult === "normal" ? 1 : "",
    x: common_vendor.o(($event) => $options.onCheckResultChange("normal")),
    y: $data.taskResult.checkResult === "abnormal" ? 1 : "",
    z: common_vendor.o(($event) => $options.onCheckResultChange("abnormal")),
    A: $data.taskResult.remark,
    B: common_vendor.o(($event) => $data.taskResult.remark = $event.detail.value),
    C: common_vendor.f($data.taskResult.images, (image, index, i0) => {
      return {
        a: image.url,
        b: common_vendor.o(($event) => $options.previewImage(image.url), index),
        c: common_vendor.o(($event) => $options.deleteImage(index), index),
        d: index
      };
    }),
    D: $data.taskResult.images.length < 3
  }, $data.taskResult.images.length < 3 ? {
    E: common_vendor.o((...args) => $options.uploadImage && $options.uploadImage(...args))
  } : {}, {
    F: $data.currentTaskIndex > 0
  }, $data.currentTaskIndex > 0 ? {
    G: common_vendor.o((...args) => $options.prevTask && $options.prevTask(...args))
  } : {}, {
    H: $data.currentTaskIndex < $data.planTasks.length - 1
  }, $data.currentTaskIndex < $data.planTasks.length - 1 ? {
    I: common_vendor.o((...args) => $options.skipTask && $options.skipTask(...args))
  } : {}, {
    J: common_vendor.t($data.currentTaskIndex < $data.planTasks.length - 1 ? "下一项" : "完成"),
    K: common_vendor.o((...args) => $options.submitTask && $options.submitTask(...args))
  }), {
    L: common_vendor.o((...args) => $options.cancelSkip && $options.cancelSkip(...args)),
    M: common_vendor.o((...args) => $options.confirmSkip && $options.confirmSkip(...args)),
    N: common_vendor.sr("skipPopup", "a661c770-0"),
    O: common_vendor.p({
      type: "center"
    }),
    P: common_vendor.o((...args) => $options.cancelComplete && $options.cancelComplete(...args)),
    Q: common_vendor.o((...args) => $options.confirmComplete && $options.confirmComplete(...args)),
    R: common_vendor.sr("completePopup", "a661c770-1"),
    S: common_vendor.p({
      type: "center"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/patrol/execute.js.map
