<view class="valve-detail-page"><view class="header"><view class="back-button" bindtap="{{a}}"><text class="iconfont icon-arrow-left"></text></view><text class="page-title">阀门详情</text></view><view wx:if="{{b}}" class="loading-container"><uni-load-more wx:if="{{c}}" u-i="63d56152-0" bind:__l="__l" u-p="{{c}}"></uni-load-more></view><view wx:elif="{{d}}" class="valve-content"><view class="status-card"><view class="{{['status-icon', f]}}"><text class="{{['iconfont', e]}}"></text></view><view class="status-info"><view class="status-header"><text class="valve-name">{{g}}</text><view class="{{['status-tag', i]}}">{{h}}</view></view><view class="status-detail"><view class="detail-item"><text class="detail-label">当前开度</text><text class="detail-value">{{j}}%</text></view><view class="detail-item"><text class="detail-label">上次操作</text><text class="detail-value">{{k}} {{l}}</text></view><view class="detail-item"><text class="detail-label">操作人员</text><text class="detail-value">{{m}}</text></view></view></view></view><view class="control-panel"><view class="panel-title">控制面板</view><view class="opening-control"><text class="control-label">开度调节：{{n}}%</text><slider class="opening-slider" value="{{o}}" min="{{0}}" max="{{100}}" step="{{1}}" bindchange="{{p}}" show-value/></view><view class="control-buttons"><button class="control-btn set-btn" bindtap="{{q}}">设置开度</button><button class="control-btn open-btn" bindtap="{{r}}">一键开启</button><button class="control-btn close-btn" bindtap="{{s}}">一键关闭</button></view></view><view class="operation-records"><view class="records-title">操作记录</view><view class="timeline"><view wx:for="{{t}}" wx:for-item="record" wx:key="d" class="timeline-item"><view class="timeline-dot"></view><view class="timeline-content"><text class="timeline-time">{{record.a}}</text><text class="timeline-text">{{record.b}}</text><text class="timeline-operator">操作人：{{record.c}}</text></view></view></view></view></view><uni-popup wx:if="{{z}}" class="r" u-s="{{['d']}}" u-r="confirmPopup" u-i="63d56152-1" bind:__l="__l" u-p="{{z}}"><uni-popup-dialog wx:if="{{x}}" bindconfirm="{{v}}" bindclose="{{w}}" u-i="63d56152-2,63d56152-1" bind:__l="__l" u-p="{{x}}"></uni-popup-dialog></uni-popup><uni-popup wx:if="{{C}}" class="r" u-s="{{['d']}}" u-r="resultPopup" u-i="63d56152-3" bind:__l="__l" u-p="{{C}}"><uni-popup-message wx:if="{{A}}" u-i="63d56152-4,63d56152-3" bind:__l="__l" u-p="{{A}}"></uni-popup-message></uni-popup></view>