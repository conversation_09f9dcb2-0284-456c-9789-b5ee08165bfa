2025-09-02T09:00:44.366+08:00  INFO 1248 --- [main] com.heating.HeatingApplication           : Starting HeatingApplication using Java 21.0.7 with PID 1248 (E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend\target\classes started by A in E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend)
2025-09-02T09:00:44.394+08:00 DEBUG 1248 --- [main] com.heating.HeatingApplication           : Running with Spring Boot v3.2.3, Spring v6.1.4
2025-09-02T09:00:44.395+08:00  INFO 1248 --- [main] com.heating.HeatingApplication           : No active profile set, falling back to 1 default profile: "default"
2025-09-02T09:00:45.020+08:00  INFO 1248 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-02T09:00:45.170+08:00  INFO 1248 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 143 ms. Found 35 JPA repository interfaces.
2025-09-02T09:00:45.728+08:00  INFO 1248 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8889 (http)
2025-09-02T09:00:45.737+08:00  INFO 1248 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-02T09:00:45.737+08:00  INFO 1248 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-09-02T09:00:45.786+08:00  INFO 1248 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-02T09:00:45.786+08:00  INFO 1248 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1356 ms
2025-09-02T09:00:45.842+08:00 DEBUG 1248 --- [main] c.h.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-09-02T09:00:45.938+08:00  INFO 1248 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-02T09:00:45.988+08:00  INFO 1248 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-02T09:00:46.016+08:00  INFO 1248 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-02T09:00:46.101+08:00  INFO 1248 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Starting...
2025-09-02T09:00:47.639+08:00  INFO 1248 --- [main] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@1e258d3b
2025-09-02T09:00:47.641+08:00  INFO 1248 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Start completed.
2025-09-02T09:00:47.964+08:00  INFO 1248 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-02T09:00:49.735+08:00  INFO 1248 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-02T09:00:52.703+08:00  INFO 1248 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-02T09:00:53.438+08:00  INFO 1248 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-02T09:00:55.524+08:00  INFO 1248 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源跨域访问: /uploads/**
2025-09-02T09:00:55.609+08:00  INFO 1248 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源映射: /uploads/** -> file:/home/<USER>/work/web/uploads/
2025-09-02T09:00:55.648+08:00  INFO 1248 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3c913e84, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2de3095c, org.springframework.security.web.context.SecurityContextHolderFilter@4f50d590, org.springframework.security.web.header.HeaderWriterFilter@1c25b58d, org.springframework.web.filter.CorsFilter@323fe1f5, org.springframework.security.web.authentication.logout.LogoutFilter@3fa87583, com.heating.filter.JwtAuthenticationFilter@28be7fec, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4e59830b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@487d9291, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@57d5b677, org.springframework.security.web.access.ExceptionTranslationFilter@1d0ed7d8, org.springframework.security.web.access.intercept.AuthorizationFilter@528a643c]
2025-09-02T09:00:55.905+08:00  INFO 1248 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8889 (http) with context path ''
2025-09-02T09:00:55.913+08:00  INFO 1248 --- [main] com.heating.HeatingApplication           : Started HeatingApplication in 11.888 seconds (process running for 12.546)
2025-09-02T09:03:33.184+08:00  WARN 1248 --- [MyHikariPool housekeeper] com.zaxxer.hikari.pool.PoolBase          : MyHikariPool - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@52097f23 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-09-02T09:03:35.557+08:00  WARN 1248 --- [MyHikariPool housekeeper] com.zaxxer.hikari.pool.PoolBase          : MyHikariPool - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@61b9abf1 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-09-02T09:03:40.057+08:00  WARN 1248 --- [MyHikariPool housekeeper] com.zaxxer.hikari.pool.PoolBase          : MyHikariPool - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@1e258d3b (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-09-02T09:03:43.280+08:00  WARN 1248 --- [MyHikariPool housekeeper] com.zaxxer.hikari.pool.PoolBase          : MyHikariPool - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5a9f3512 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-09-02T09:03:44.503+08:00  WARN 1248 --- [MyHikariPool housekeeper] com.zaxxer.hikari.pool.PoolBase          : MyHikariPool - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@3534cc23 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-09-02T09:13:50.052+08:00  INFO 1248 --- [http-nio-8889-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-02T09:13:50.052+08:00  INFO 1248 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-02T09:13:50.053+08:00  INFO 1248 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-09-02T09:13:50.073+08:00 DEBUG 1248 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/dict/data/11
2025-09-02T09:13:50.073+08:00 DEBUG 1248 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/patrols/plans/list
2025-09-02T09:13:50.087+08:00 DEBUG 1248 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:13:50.087+08:00 DEBUG 1248 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:13:50.087+08:00 DEBUG 1248 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:13:50.087+08:00 DEBUG 1248 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:13:50.136+08:00 DEBUG 1248 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/dict/data/11
2025-09-02T09:13:50.136+08:00 DEBUG 1248 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/patrols/plans/list
2025-09-02T09:13:50.178+08:00  INFO 1248 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 巡检计划查询 - 项目权限参数: 5
2025-09-02T09:13:55.277+08:00 DEBUG 1248 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing OPTIONS /api/auth/login
2025-09-02T09:13:55.278+08:00 DEBUG 1248 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-09-02T09:13:55.294+08:00 DEBUG 1248 --- [http-nio-8889-exec-4] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/api/auth/login?continue to session
2025-09-02T09:13:55.295+08:00 DEBUG 1248 --- [http-nio-8889-exec-4] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-09-02T09:13:55.302+08:00 DEBUG 1248 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing OPTIONS /error
2025-09-02T09:13:55.302+08:00 DEBUG 1248 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-09-02T09:13:55.302+08:00 DEBUG 1248 --- [http-nio-8889-exec-4] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/error?continue to session
2025-09-02T09:13:55.302+08:00 DEBUG 1248 --- [http-nio-8889-exec-4] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-09-02T09:13:56.514+08:00  INFO 1248 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-02T09:13:56.515+08:00  INFO 1248 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown initiated...
2025-09-02T09:13:56.685+08:00  INFO 1248 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown completed.
2025-09-02T09:14:04.217+08:00  INFO 17916 --- [main] com.heating.HeatingApplication           : Starting HeatingApplication using Java 21.0.7 with PID 17916 (E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend\target\classes started by A in E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend)
2025-09-02T09:14:04.218+08:00 DEBUG 17916 --- [main] com.heating.HeatingApplication           : Running with Spring Boot v3.2.3, Spring v6.1.4
2025-09-02T09:14:04.219+08:00  INFO 17916 --- [main] com.heating.HeatingApplication           : No active profile set, falling back to 1 default profile: "default"
2025-09-02T09:14:04.789+08:00  INFO 17916 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-02T09:14:04.920+08:00  INFO 17916 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 126 ms. Found 35 JPA repository interfaces.
2025-09-02T09:14:05.405+08:00  INFO 17916 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8889 (http)
2025-09-02T09:14:05.413+08:00  INFO 17916 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-02T09:14:05.413+08:00  INFO 17916 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-09-02T09:14:05.457+08:00  INFO 17916 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-02T09:14:05.458+08:00  INFO 17916 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1208 ms
2025-09-02T09:14:05.509+08:00 DEBUG 17916 --- [main] c.h.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-09-02T09:14:05.606+08:00  INFO 17916 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-02T09:14:05.656+08:00  INFO 17916 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-02T09:14:05.683+08:00  INFO 17916 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-02T09:14:05.767+08:00  INFO 17916 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Starting...
2025-09-02T09:14:06.354+08:00  INFO 17916 --- [main] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@2976ef56
2025-09-02T09:14:06.355+08:00  INFO 17916 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Start completed.
2025-09-02T09:14:06.610+08:00  INFO 17916 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-02T09:14:07.791+08:00  INFO 17916 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-02T09:14:08.392+08:00  INFO 17916 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-02T09:14:08.582+08:00  INFO 17916 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-02T09:14:10.226+08:00  INFO 17916 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源跨域访问: /uploads/**
2025-09-02T09:14:10.327+08:00  INFO 17916 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源映射: /uploads/** -> file:/home/<USER>/work/web/uploads/
2025-09-02T09:14:10.370+08:00  INFO 17916 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@ba28d1b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@397c53e7, org.springframework.security.web.context.SecurityContextHolderFilter@378d8d1b, org.springframework.security.web.header.HeaderWriterFilter@519cb1bd, org.springframework.web.filter.CorsFilter@7565ce3d, org.springframework.security.web.authentication.logout.LogoutFilter@1dcc501f, com.heating.filter.JwtAuthenticationFilter@57186526, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@48fd7749, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3a9fd60c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@107947a2, org.springframework.security.web.access.ExceptionTranslationFilter@51145fdc, org.springframework.security.web.access.intercept.AuthorizationFilter@2e607b8]
2025-09-02T09:14:10.619+08:00  INFO 17916 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8889 (http) with context path ''
2025-09-02T09:14:10.635+08:00  INFO 17916 --- [main] com.heating.HeatingApplication           : Started HeatingApplication in 6.743 seconds (process running for 7.277)
2025-09-02T09:14:10.870+08:00  INFO 17916 --- [http-nio-8889-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-02T09:14:10.870+08:00  INFO 17916 --- [http-nio-8889-exec-2] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-02T09:14:10.871+08:00  INFO 17916 --- [http-nio-8889-exec-2] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-09-02T09:14:10.884+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/auth/login
2025-09-02T09:14:10.897+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/auth/login
2025-09-02T09:14:11.447+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-09-02T09:14:11.452+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/auth/user-info
2025-09-02T09:14:11.453+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:11.453+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:11.471+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/auth/user-info
2025-09-02T09:14:12.800+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=5
2025-09-02T09:14:12.801+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:12.801+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:12.801+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:14:12.802+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:12.802+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:12.802+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:14:12.803+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:12.803+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:12.805+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:14:12.805+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:14:12.806+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:12.806+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:12.806+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:12.806+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:12.809+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:14:12.809+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:14:12.810+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:12.810+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:12.810+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:14:12.811+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:14:12.811+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=5
2025-09-02T09:14:12.812+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:14:12.812+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:14:12.813+08:00  INFO 17916 --- [http-nio-8889-exec-3] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:14:12.813+08:00  INFO 17916 --- [http-nio-8889-exec-3] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:14:12.813+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:14:12.814+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:14:12.818+08:00  INFO 17916 --- [http-nio-8889-exec-7] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T09:14:12.818+08:00  INFO 17916 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T09:14:12.819+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=5
2025-09-02T09:14:12.819+08:00  INFO 17916 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T09:14:12.819+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:14:12.890+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=22.9°C
2025-09-02T09:14:12.894+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:14:12.894+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:14:12.895+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:12.895+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:12.897+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:12.898+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:12.899+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:14:12.900+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:14:12.900+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:14:12.900+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:14:12.933+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-09-02T09:14:12.938+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=5
2025-09-02T09:14:12.938+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=22.9°C
2025-09-02T09:14:12.938+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:12.938+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:12.941+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=5
2025-09-02T09:14:12.942+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=5
2025-09-02T09:14:12.942+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:14:12.975+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-09-02T09:14:13.044+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:14:13.044+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:13.044+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:13.047+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:14:13.091+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:14:13.091+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:13.091+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:13.093+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:14:13.094+08:00  INFO 17916 --- [http-nio-8889-exec-8] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:14:13.094+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:14:13.811+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T09:14:13.811+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:13.811+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:13.815+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T09:14:13.816+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T09:14:13.863+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing POST /api/patrols/records/list
2025-09-02T09:14:13.864+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:13.864+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:13.866+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured POST /api/patrols/records/list
2025-09-02T09:14:13.868+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.heating.controller.PatrolController    : 获取巡检记录列表请求: heatUnitId=5, executorId=6, status=null
2025-09-02T09:14:13.869+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 获取巡检记录列表: PatrolRecordListRequest(status=null, executorId=6, startDate=null, endDate=null, page=1, pageSize=5, heatUnitId=5)
2025-09-02T09:14:13.870+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 巡检记录查询 - 项目权限参数: 5
2025-09-02T09:14:13.870+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 项目权限过滤，允许的项目ID: [5]
2025-09-02T09:14:13.870+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 根据执行人ID和项目权限过滤巡检记录: executorId=6, heatUnitIds=[5]
2025-09-02T09:14:14.107+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T09:14:14.108+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:14.108+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:14.111+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T09:14:14.111+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T09:14:14.195+08:00  INFO 17916 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:14:14.198+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:14:14.199+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:14.199+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:14.201+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:14:14.202+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T09:14:14.202+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T09:14:14.202+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T09:14:15.229+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=5
2025-09-02T09:14:15.230+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:14:15.231+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:15.231+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:15.232+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:14:15.232+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:14:15.232+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:15.232+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:15.232+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:15.232+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:15.232+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:15.232+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:15.235+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:14:15.236+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:14:15.236+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:14:15.236+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:15.237+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:15.237+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=5
2025-09-02T09:14:15.237+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:14:15.238+08:00  INFO 17916 --- [http-nio-8889-exec-3] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:14:15.238+08:00  INFO 17916 --- [http-nio-8889-exec-3] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:14:15.238+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=5
2025-09-02T09:14:15.238+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:14:15.239+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:14:15.240+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:14:15.240+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:14:15.314+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=22.9°C
2025-09-02T09:14:15.315+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-09-02T09:14:15.485+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:14:15.487+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:14:15.487+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:15.488+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:15.490+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:14:15.491+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T09:14:15.491+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T09:14:15.491+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T09:14:15.869+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing POST /api/patrols/plans/list
2025-09-02T09:14:15.869+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:15.869+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:15.871+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured POST /api/patrols/plans/list
2025-09-02T09:14:15.874+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 巡检计划查询 - 项目权限参数: 5
2025-09-02T09:14:15.884+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/dict/data/11
2025-09-02T09:14:15.884+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:15.884+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:15.887+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/dict/data/11
2025-09-02T09:14:16.245+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T09:14:16.246+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:16.246+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:16.248+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T09:14:16.249+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T09:14:16.872+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:14:16.931+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=5
2025-09-02T09:14:16.932+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:14:16.932+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:16.932+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:16.932+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:16.932+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:16.933+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:14:16.934+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:16.934+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:16.934+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=5
2025-09-02T09:14:16.935+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=5
2025-09-02T09:14:16.935+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:14:16.935+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:14:16.935+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:16.935+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:16.936+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:14:16.937+08:00  INFO 17916 --- [http-nio-8889-exec-9] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:14:16.937+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:14:16.936+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:14:16.937+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:14:16.938+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:16.938+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:16.939+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:14:16.940+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:14:16.940+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:16.940+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:16.944+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:14:16.944+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:14:16.944+08:00  INFO 17916 --- [http-nio-8889-exec-3] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:14:16.944+08:00  INFO 17916 --- [http-nio-8889-exec-3] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:14:16.945+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T09:14:16.945+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T09:14:16.946+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T09:14:17.002+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-09-02T09:14:17.015+08:00  INFO 17916 --- [http-nio-8889-exec-3] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=22.9°C
2025-09-02T09:14:17.951+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T09:14:17.951+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:17.951+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:17.953+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T09:14:17.954+08:00  INFO 17916 --- [http-nio-8889-exec-7] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T09:14:18.247+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:14:30.615+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/stats?uid=6&heatUnitId=5
2025-09-02T09:14:30.616+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:30.616+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:30.619+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/stats?uid=6&heatUnitId=5
2025-09-02T09:14:30.619+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.controller.WorkOrderController       : 获取工单统计数据: uid=6, heatUnitId=5
2025-09-02T09:14:30.620+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.WorkOrderServiceImpl    : 开始获取工单统计数据: userId=6, heatUnitId=5
2025-09-02T09:14:30.691+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.WorkOrderServiceImpl    : 用户角色: admin
2025-09-02T09:14:30.691+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.WorkOrderServiceImpl    : 管理员或主管角色统计逻辑
2025-09-02T09:14:30.795+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.WorkOrderServiceImpl    : 工单统计数据获取成功: 我的工单数=0, 待处理工单数=0, 待接单工单数=0
2025-09-02T09:14:32.067+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing OPTIONS /api/auth/login
2025-09-02T09:14:32.068+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-09-02T09:14:32.084+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/api/auth/login?continue to session
2025-09-02T09:14:32.084+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-09-02T09:14:32.089+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing OPTIONS /error
2025-09-02T09:14:32.089+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-09-02T09:14:32.089+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/error?continue to session
2025-09-02T09:14:32.089+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-09-02T09:14:32.827+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing POST /api/auth/login
2025-09-02T09:14:32.827+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured POST /api/auth/login
2025-09-02T09:14:33.167+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-09-02T09:14:33.171+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/auth/user-info
2025-09-02T09:14:33.171+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:33.171+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:33.173+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/auth/user-info
2025-09-02T09:14:34.184+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=1
2025-09-02T09:14:34.185+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:34.185+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:34.186+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:14:34.186+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:34.186+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:34.186+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=1
2025-09-02T09:14:34.187+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=1
2025-09-02T09:14:34.187+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:14:34.188+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:14:34.188+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:34.188+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:34.188+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:14:34.190+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:14:34.190+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:34.190+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:34.190+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:14:34.190+08:00  INFO 17916 --- [http-nio-8889-exec-1] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:14:34.190+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:14:34.192+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:14:34.192+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:14:34.192+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:34.192+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:34.193+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:14:34.194+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:14:34.194+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:34.194+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:34.194+08:00  INFO 17916 --- [http-nio-8889-exec-7] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T09:14:34.194+08:00  INFO 17916 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T09:14:34.194+08:00  INFO 17916 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T09:14:34.196+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:14:34.196+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:14:34.196+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:14:34.259+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:14:34.259+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T09:14:34.259+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:34.259+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:34.262+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:14:34.262+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:14:34.262+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:34.262+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:34.262+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:14:34.262+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:14:34.264+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:14:34.265+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=22.9°C
2025-09-02T09:14:34.267+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=1
2025-09-02T09:14:34.268+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:34.268+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:34.270+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=1
2025-09-02T09:14:34.271+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=1
2025-09-02T09:14:34.271+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:14:34.303+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=22.9°C
2025-09-02T09:14:34.306+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T09:14:34.435+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:14:34.435+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:34.435+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:34.437+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:14:34.458+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:14:34.458+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:34.458+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:34.460+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:14:34.460+08:00  INFO 17916 --- [http-nio-8889-exec-9] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:14:34.460+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:14:35.117+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing POST /api/patrols/plans/list
2025-09-02T09:14:35.118+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:35.118+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:35.120+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured POST /api/patrols/plans/list
2025-09-02T09:14:35.120+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 巡检计划查询 - 项目权限参数: 1
2025-09-02T09:14:35.130+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/dict/data/11
2025-09-02T09:14:35.131+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:35.131+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:35.132+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/dict/data/11
2025-09-02T09:14:35.208+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T09:14:35.209+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:35.209+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:35.211+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T09:14:35.211+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T09:14:35.456+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T09:14:35.457+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:35.457+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:35.458+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T09:14:35.459+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T09:14:35.516+08:00  INFO 17916 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:14:35.518+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:14:35.518+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:35.518+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:35.520+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:14:35.520+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T09:14:35.520+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T09:14:35.520+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T09:14:36.845+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:14:37.128+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=1
2025-09-02T09:14:37.128+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:37.128+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:37.129+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:14:37.129+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:37.129+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:37.130+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:14:37.130+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:37.130+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:14:37.130+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:37.131+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:37.131+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:37.132+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=1
2025-09-02T09:14:37.132+08:00  INFO 17916 --- [http-nio-8889-exec-3] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=1
2025-09-02T09:14:37.132+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:14:37.133+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:14:37.133+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:14:37.134+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:14:37.134+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:14:37.135+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:37.135+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:37.135+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:37.135+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:37.138+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:14:37.138+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:14:37.138+08:00  INFO 17916 --- [http-nio-8889-exec-4] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:14:37.138+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:14:37.138+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T09:14:37.138+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T09:14:37.139+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T09:14:37.140+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:14:37.140+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:14:37.140+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:14:37.200+08:00  INFO 17916 --- [http-nio-8889-exec-3] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T09:14:37.217+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=22.9°C
2025-09-02T09:14:37.901+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing POST /api/patrols/records/list
2025-09-02T09:14:37.901+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:37.901+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:37.903+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured POST /api/patrols/records/list
2025-09-02T09:14:37.904+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.heating.controller.PatrolController    : 获取巡检记录列表请求: heatUnitId=1, executorId=6, status=null
2025-09-02T09:14:37.904+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取巡检记录列表: PatrolRecordListRequest(status=null, executorId=6, startDate=null, endDate=null, page=1, pageSize=5, heatUnitId=1)
2025-09-02T09:14:37.904+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 巡检记录查询 - 项目权限参数: 1
2025-09-02T09:14:37.904+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 项目权限过滤，允许的项目ID: [1]
2025-09-02T09:14:37.904+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 根据执行人ID和项目权限过滤巡检记录: executorId=6, heatUnitIds=[1]
2025-09-02T09:14:38.138+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T09:14:38.138+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:38.138+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:38.140+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T09:14:38.140+08:00  INFO 17916 --- [http-nio-8889-exec-7] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T09:14:38.460+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:14:40.951+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing POST /api/patrols/records/list
2025-09-02T09:14:40.952+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:14:40.952+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:14:40.954+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured POST /api/patrols/records/list
2025-09-02T09:14:40.954+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.heating.controller.PatrolController    : 获取巡检记录列表请求: heatUnitId=1, executorId=6, status=null
2025-09-02T09:14:40.954+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 获取巡检记录列表: PatrolRecordListRequest(status=null, executorId=6, startDate=null, endDate=null, page=1, pageSize=5, heatUnitId=1)
2025-09-02T09:14:40.954+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 巡检记录查询 - 项目权限参数: 1
2025-09-02T09:14:40.954+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 项目权限过滤，允许的项目ID: [1]
2025-09-02T09:14:40.954+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 根据执行人ID和项目权限过滤巡检记录: executorId=6, heatUnitIds=[1]
2025-09-02T09:15:20.821+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=1
2025-09-02T09:15:20.821+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:15:20.821+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:15:20.822+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:15:20.822+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:15:20.823+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:15:20.824+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:15:20.824+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:15:20.824+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:15:20.827+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:15:20.827+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:15:20.827+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:15:20.827+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:15:20.827+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:15:20.827+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:15:20.828+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:15:20.828+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:15:20.828+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:15:20.828+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=1
2025-09-02T09:15:20.829+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=1
2025-09-02T09:15:20.829+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:15:20.831+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:15:20.832+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:15:20.834+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:15:20.834+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:15:20.834+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:15:20.835+08:00  INFO 17916 --- [http-nio-8889-exec-3] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:15:20.835+08:00  INFO 17916 --- [http-nio-8889-exec-3] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:15:20.835+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T09:15:20.835+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T09:15:20.835+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T09:15:20.835+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:15:20.835+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:15:20.904+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T09:15:20.904+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=22.9°C
2025-09-02T09:15:21.763+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing POST /api/patrols/plans/list
2025-09-02T09:15:21.763+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:15:21.763+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:15:21.765+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured POST /api/patrols/plans/list
2025-09-02T09:15:21.765+08:00  INFO 17916 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 巡检计划查询 - 项目权限参数: 1
2025-09-02T09:15:21.776+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/dict/data/11
2025-09-02T09:15:21.776+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:15:21.776+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:15:21.778+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/dict/data/11
2025-09-02T09:15:21.841+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T09:15:21.842+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:15:21.842+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:15:21.843+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T09:15:21.844+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T09:15:22.182+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:15:28.051+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/plans/1
2025-09-02T09:15:28.051+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:15:28.051+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:15:28.052+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/plans/1
2025-09-02T09:15:28.053+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.heating.controller.PatrolController    : 获取巡检计划详情: 1
2025-09-02T09:15:28.323+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 获取巡检计划详情: TPatrolPlan(id=1, planNo=20250825102456, name=测试巡检, patrolType=日常巡检, startDate=2025-08-26, endDate=2025-08-28, executorIds=[9], scheduleType=daily, scheduleInterval=null, scheduleWeekDays=[], scheduleMonthDays=[], deviceIds=[1, 2], status=completed, locations=联合新村, heatUnitId=1, createTime=2025-08-25T10:24:57, updateTime=2025-08-29T01:00)
2025-09-02T09:15:39.981+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=1
2025-09-02T09:15:39.982+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:15:39.982+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:15:39.982+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:15:39.982+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:15:39.982+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:15:39.983+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:15:39.983+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:15:39.983+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:15:39.983+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=1
2025-09-02T09:15:39.984+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:15:39.984+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=1
2025-09-02T09:15:39.984+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:15:39.985+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:15:39.985+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:15:39.985+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:15:39.985+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:15:39.986+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:15:39.986+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:15:39.986+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:15:39.986+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:15:39.986+08:00  INFO 17916 --- [http-nio-8889-exec-4] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:15:39.986+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:15:39.986+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:15:39.987+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:15:39.988+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:15:39.988+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:15:39.989+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:15:39.989+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:15:39.989+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:15:39.989+08:00  INFO 17916 --- [http-nio-8889-exec-3] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T09:15:39.989+08:00  INFO 17916 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T09:15:39.989+08:00  INFO 17916 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T09:15:40.056+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T09:15:40.060+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=22.9°C
2025-09-02T09:15:40.992+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T09:15:40.993+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:15:40.993+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:15:40.994+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T09:15:40.995+08:00  INFO 17916 --- [http-nio-8889-exec-7] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T09:15:41.340+08:00  INFO 17916 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:15:45.046+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/patrols/records/list
2025-09-02T09:15:45.046+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:15:45.046+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:15:45.048+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/patrols/records/list
2025-09-02T09:15:45.049+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.heating.controller.PatrolController    : 获取巡检记录列表请求: heatUnitId=1, executorId=6, status=null
2025-09-02T09:15:45.049+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取巡检记录列表: PatrolRecordListRequest(status=null, executorId=6, startDate=null, endDate=null, page=1, pageSize=5, heatUnitId=1)
2025-09-02T09:15:45.049+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 巡检记录查询 - 项目权限参数: 1
2025-09-02T09:15:45.049+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 项目权限过滤，允许的项目ID: [1]
2025-09-02T09:15:45.049+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 根据执行人ID和项目权限过滤巡检记录: executorId=6, heatUnitIds=[1]
2025-09-02T09:15:48.459+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=1
2025-09-02T09:15:48.460+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:15:48.460+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:15:48.460+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:15:48.460+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:15:48.460+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:15:48.461+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:15:48.461+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:15:48.461+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:15:48.463+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:15:48.463+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:15:48.463+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:15:48.463+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:15:48.463+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:15:48.464+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:15:48.464+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:15:48.464+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=1
2025-09-02T09:15:48.464+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=1
2025-09-02T09:15:48.464+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:15:48.465+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:15:48.466+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T09:15:48.466+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T09:15:48.466+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T09:15:48.466+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:15:48.466+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:15:48.466+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:15:48.468+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:15:48.468+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:15:48.468+08:00  INFO 17916 --- [http-nio-8889-exec-5] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:15:48.468+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:15:48.468+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:15:48.469+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:15:48.469+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:15:48.543+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T09:15:48.549+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=22.9°C
2025-09-02T09:15:49.478+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T09:15:49.478+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:15:49.478+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:15:49.480+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T09:15:49.480+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T09:15:49.881+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:16:04.406+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing POST /api/patrols/plans/list
2025-09-02T09:16:04.407+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:04.407+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:04.408+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured POST /api/patrols/plans/list
2025-09-02T09:16:04.410+08:00  INFO 17916 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 巡检计划查询 - 项目权限参数: 1
2025-09-02T09:16:04.421+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/dict/data/11
2025-09-02T09:16:04.421+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:04.422+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:04.423+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/dict/data/11
2025-09-02T09:16:05.697+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=1
2025-09-02T09:16:05.697+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:05.697+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:05.698+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:16:05.698+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:05.698+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:05.698+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:16:05.698+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:05.698+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:05.699+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=1
2025-09-02T09:16:05.699+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:16:05.699+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:16:05.699+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:05.699+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:05.699+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=1
2025-09-02T09:16:05.700+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:16:05.700+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:16:05.700+08:00  INFO 17916 --- [http-nio-8889-exec-1] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:16:05.700+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:16:05.701+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:16:05.701+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:16:05.701+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:05.701+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:05.702+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:16:05.702+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:05.702+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:05.703+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:16:05.703+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:16:05.704+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:16:05.704+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:16:05.704+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T09:16:05.704+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T09:16:05.704+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T09:16:05.766+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T09:16:05.777+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=22.9°C
2025-09-02T09:16:06.717+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T09:16:06.718+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:06.718+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:06.719+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T09:16:06.720+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T09:16:07.030+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:16:07.099+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing POST /api/patrols/records/list
2025-09-02T09:16:07.099+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:07.099+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:07.101+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured POST /api/patrols/records/list
2025-09-02T09:16:07.101+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.heating.controller.PatrolController    : 获取巡检记录列表请求: heatUnitId=1, executorId=6, status=null
2025-09-02T09:16:07.101+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取巡检记录列表: PatrolRecordListRequest(status=null, executorId=6, startDate=null, endDate=null, page=1, pageSize=5, heatUnitId=1)
2025-09-02T09:16:07.101+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 巡检记录查询 - 项目权限参数: 1
2025-09-02T09:16:07.102+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 项目权限过滤，允许的项目ID: [1]
2025-09-02T09:16:07.102+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 根据执行人ID和项目权限过滤巡检记录: executorId=6, heatUnitIds=[1]
2025-09-02T09:16:16.452+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=1
2025-09-02T09:16:16.452+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:16.452+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:16.453+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:16:16.453+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:16:16.453+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:16.454+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:16.454+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:16.454+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:16.454+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=1
2025-09-02T09:16:16.454+08:00  INFO 17916 --- [http-nio-8889-exec-3] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=1
2025-09-02T09:16:16.454+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:16:16.456+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:16:16.455+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:16:16.456+08:00  INFO 17916 --- [http-nio-8889-exec-2] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:16:16.456+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:16:16.456+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:16.456+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:16.456+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:16:16.456+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:16:16.457+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:16.457+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:16.457+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:16:16.457+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:16.457+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:16.458+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:16:16.459+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:16:16.459+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:16:16.459+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T09:16:16.459+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T09:16:16.459+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T09:16:16.459+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:16:16.459+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:16:16.520+08:00  INFO 17916 --- [http-nio-8889-exec-3] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T09:16:16.523+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=22.9°C
2025-09-02T09:16:17.466+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T09:16:17.466+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:17.466+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:17.468+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T09:16:17.468+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T09:16:17.777+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:16:18.315+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/stats?uid=6&heatUnitId=1
2025-09-02T09:16:18.315+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:18.315+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:18.316+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/stats?uid=6&heatUnitId=1
2025-09-02T09:16:18.317+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.h.controller.WorkOrderController       : 获取工单统计数据: uid=6, heatUnitId=1
2025-09-02T09:16:18.317+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.h.service.impl.WorkOrderServiceImpl    : 开始获取工单统计数据: userId=6, heatUnitId=1
2025-09-02T09:16:18.384+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.h.service.impl.WorkOrderServiceImpl    : 用户角色: admin
2025-09-02T09:16:18.384+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.h.service.impl.WorkOrderServiceImpl    : 管理员或主管角色统计逻辑
2025-09-02T09:16:18.483+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.h.service.impl.WorkOrderServiceImpl    : 工单统计数据获取成功: 我的工单数=1, 待处理工单数=1, 待接单工单数=0
2025-09-02T09:16:22.566+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing OPTIONS /api/auth/login
2025-09-02T09:16:22.567+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-09-02T09:16:22.568+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/api/auth/login?continue to session
2025-09-02T09:16:22.568+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-09-02T09:16:22.568+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing OPTIONS /error
2025-09-02T09:16:22.568+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-09-02T09:16:22.568+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/error?continue to session
2025-09-02T09:16:22.568+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-09-02T09:16:36.437+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing POST /api/auth/login
2025-09-02T09:16:36.437+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured POST /api/auth/login
2025-09-02T09:16:36.807+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-09-02T09:16:36.811+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/auth/user-info
2025-09-02T09:16:36.811+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:36.811+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:36.813+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/auth/user-info
2025-09-02T09:16:37.825+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T09:16:37.825+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:37.825+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:37.827+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:16:37.827+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T09:16:37.827+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:37.827+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:37.827+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-09-02T09:16:37.827+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:16:37.829+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:16:37.829+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:16:37.829+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:37.829+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:37.831+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:16:37.832+08:00  INFO 17916 --- [http-nio-8889-exec-7] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:16:37.832+08:00  INFO 17916 --- [http-nio-8889-exec-7] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:16:37.832+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:16:37.832+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:37.832+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:37.834+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:16:37.835+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:16:37.835+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:37.835+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:37.837+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:16:37.837+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:16:37.837+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:37.837+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:37.837+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T09:16:37.837+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T09:16:37.837+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T09:16:37.838+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:16:37.839+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:16:37.839+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:16:37.896+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T09:16:37.898+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:16:37.898+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:37.898+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:37.900+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:16:37.900+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:16:37.900+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:16:37.907+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T09:16:37.907+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:37.907+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:37.909+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T09:16:37.909+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-09-02T09:16:37.909+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:16:37.915+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=22.9°C
2025-09-02T09:16:37.918+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:16:37.918+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:37.918+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:37.921+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:16:37.938+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=22.9°C
2025-09-02T09:16:37.956+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T09:16:38.086+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:16:38.087+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:38.087+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:38.089+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:16:38.105+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:16:38.106+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:38.106+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:38.108+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:16:38.108+08:00  INFO 17916 --- [http-nio-8889-exec-9] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:16:38.108+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:16:38.802+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/patrols/records/list
2025-09-02T09:16:38.802+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:38.802+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:38.804+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/patrols/records/list
2025-09-02T09:16:38.805+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.heating.controller.PatrolController    : 获取巡检记录列表请求: heatUnitId=0, executorId=6, status=null
2025-09-02T09:16:38.805+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取巡检记录列表: PatrolRecordListRequest(status=null, executorId=6, startDate=null, endDate=null, page=1, pageSize=5, heatUnitId=0)
2025-09-02T09:16:38.805+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 巡检记录查询 - 项目权限参数: 0
2025-09-02T09:16:38.805+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 项目权限包含0，获取全部项目的巡检记录
2025-09-02T09:16:38.805+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 根据执行人ID和项目权限过滤巡检记录: executorId=6, heatUnitIds=null
2025-09-02T09:16:38.838+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T09:16:38.838+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:38.838+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:38.840+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T09:16:38.840+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T09:16:39.128+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T09:16:39.128+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:39.128+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:39.130+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T09:16:39.131+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T09:16:39.142+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:16:39.144+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:16:39.144+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:39.144+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:39.146+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:16:39.146+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T09:16:39.146+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T09:16:39.146+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T09:16:40.522+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:16:40.877+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T09:16:40.877+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:40.877+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:40.879+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T09:16:40.879+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-09-02T09:16:40.879+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:16:40.881+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:16:40.882+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:40.882+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:40.884+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:16:40.885+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:16:40.885+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:40.885+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:40.886+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:16:40.887+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:40.887+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:16:40.887+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:40.887+08:00  INFO 17916 --- [http-nio-8889-exec-7] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:16:40.887+08:00  INFO 17916 --- [http-nio-8889-exec-7] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:16:40.889+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:16:40.893+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:16:40.893+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:40.893+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:40.895+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:16:40.895+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:16:40.896+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:16:40.895+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:16:40.896+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:40.896+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:40.899+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:16:40.900+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T09:16:40.900+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T09:16:40.900+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T09:16:40.956+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T09:16:40.977+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=22.9°C
2025-09-02T09:16:41.810+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing POST /api/patrols/plans/list
2025-09-02T09:16:41.810+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:41.810+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:41.812+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured POST /api/patrols/plans/list
2025-09-02T09:16:41.813+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 巡检计划查询 - 项目权限参数: 0
2025-09-02T09:16:41.824+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/dict/data/11
2025-09-02T09:16:41.824+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:41.824+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:41.828+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/dict/data/11
2025-09-02T09:16:41.915+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T09:16:41.916+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:41.916+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:41.920+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T09:16:41.920+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T09:16:42.499+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:16:43.907+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T09:16:43.907+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:16:43.907+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:43.907+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:43.907+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:43.908+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:43.909+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:16:43.909+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:43.909+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:43.909+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:16:43.917+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:16:43.917+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:16:43.917+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:16:43.917+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:43.917+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:43.917+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:43.917+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:43.917+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:43.918+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:43.919+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T09:16:43.919+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-09-02T09:16:43.919+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:16:43.923+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:16:43.923+08:00  INFO 17916 --- [http-nio-8889-exec-5] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:16:43.923+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:16:43.924+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:16:43.924+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:16:43.924+08:00  INFO 17916 --- [http-nio-8889-exec-7] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T09:16:43.924+08:00  INFO 17916 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T09:16:43.924+08:00  INFO 17916 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T09:16:43.924+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:16:43.924+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:16:43.933+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:16:43.996+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T09:16:44.014+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=22.9°C
2025-09-02T09:16:44.513+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/patrols/records/list
2025-09-02T09:16:44.513+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:44.513+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:44.516+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/patrols/records/list
2025-09-02T09:16:44.517+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.heating.controller.PatrolController    : 获取巡检记录列表请求: heatUnitId=0, executorId=6, status=null
2025-09-02T09:16:44.517+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取巡检记录列表: PatrolRecordListRequest(status=null, executorId=6, startDate=null, endDate=null, page=1, pageSize=5, heatUnitId=0)
2025-09-02T09:16:44.517+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 巡检记录查询 - 项目权限参数: 0
2025-09-02T09:16:44.517+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 项目权限包含0，获取全部项目的巡检记录
2025-09-02T09:16:44.517+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 根据执行人ID和项目权限过滤巡检记录: executorId=6, heatUnitIds=null
2025-09-02T09:16:44.920+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T09:16:44.920+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:44.920+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:44.921+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T09:16:44.922+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T09:16:45.362+08:00  INFO 17916 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:16:50.488+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T09:16:50.489+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:50.489+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:50.489+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:16:50.489+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:50.489+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:50.490+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:16:50.490+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:50.490+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:50.490+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T09:16:50.491+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-09-02T09:16:50.491+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:16:50.491+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:16:50.491+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:50.491+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:50.492+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:16:50.492+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:16:50.493+08:00  INFO 17916 --- [http-nio-8889-exec-10] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:16:50.493+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:16:50.493+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:16:50.494+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:16:50.494+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:50.494+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:16:50.494+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:50.494+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:50.494+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:50.496+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:16:50.496+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:16:50.496+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T09:16:50.496+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T09:16:50.496+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T09:16:50.497+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:16:50.497+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:16:50.558+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T09:16:50.562+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=22.9°C
2025-09-02T09:16:51.506+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T09:16:51.506+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:51.506+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:51.508+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T09:16:51.508+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T09:16:51.798+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:16:54.047+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/patrols/records/list
2025-09-02T09:16:54.049+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:54.049+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:54.051+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/patrols/records/list
2025-09-02T09:16:54.052+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.heating.controller.PatrolController    : 获取巡检记录列表请求: heatUnitId=0, executorId=6, status=null
2025-09-02T09:16:54.052+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取巡检记录列表: PatrolRecordListRequest(status=null, executorId=6, startDate=null, endDate=null, page=1, pageSize=5, heatUnitId=0)
2025-09-02T09:16:54.052+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 巡检记录查询 - 项目权限参数: 0
2025-09-02T09:16:54.052+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 项目权限包含0，获取全部项目的巡检记录
2025-09-02T09:16:54.052+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 根据执行人ID和项目权限过滤巡检记录: executorId=6, heatUnitIds=null
2025-09-02T09:16:58.507+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T09:16:58.507+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:58.507+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:58.507+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:16:58.507+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:58.508+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:58.508+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:16:58.509+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T09:16:58.509+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:58.509+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:58.509+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:16:58.509+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-09-02T09:16:58.509+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:16:58.510+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:16:58.511+08:00  INFO 17916 --- [http-nio-8889-exec-4] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:16:58.511+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:16:58.511+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:16:58.511+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:58.511+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:58.512+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:16:58.512+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:58.512+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:58.513+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:16:58.513+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:16:58.514+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:58.514+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:58.514+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:16:58.514+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T09:16:58.514+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T09:16:58.514+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T09:16:58.515+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:16:58.516+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:16:58.516+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:16:58.582+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=22.9°C
2025-09-02T09:16:58.582+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T09:16:59.224+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing POST /api/patrols/plans/list
2025-09-02T09:16:59.224+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:59.224+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:59.226+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured POST /api/patrols/plans/list
2025-09-02T09:16:59.226+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 巡检计划查询 - 项目权限参数: 0
2025-09-02T09:16:59.234+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/dict/data/11
2025-09-02T09:16:59.234+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:59.234+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:59.236+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/dict/data/11
2025-09-02T09:16:59.519+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T09:16:59.519+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:16:59.519+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:16:59.521+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T09:16:59.521+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T09:16:59.882+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:17:01.029+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/plans/5
2025-09-02T09:17:01.029+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:17:01.029+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:17:01.031+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/plans/5
2025-09-02T09:17:01.031+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.heating.controller.PatrolController    : 获取巡检计划详情: 5
2025-09-02T09:17:01.265+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取巡检计划详情: TPatrolPlan(id=5, planNo=20250825104358, name=测试, patrolType=日常巡检, startDate=2025-08-26, endDate=2025-08-30, executorIds=[13], scheduleType=daily, scheduleInterval=null, scheduleWeekDays=[], scheduleMonthDays=[], deviceIds=[1, 2], status=completed, locations=联合新村, heatUnitId=1, createTime=2025-08-25T10:43:59, updateTime=2025-08-31T01:00)
2025-09-02T09:17:06.562+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/plans/1
2025-09-02T09:17:06.562+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:17:06.562+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:17:06.563+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/plans/1
2025-09-02T09:17:06.564+08:00  INFO 17916 --- [http-nio-8889-exec-3] c.heating.controller.PatrolController    : 获取巡检计划详情: 1
2025-09-02T09:17:06.791+08:00  INFO 17916 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 获取巡检计划详情: TPatrolPlan(id=1, planNo=20250825102456, name=测试巡检, patrolType=日常巡检, startDate=2025-08-26, endDate=2025-08-28, executorIds=[9], scheduleType=daily, scheduleInterval=null, scheduleWeekDays=[], scheduleMonthDays=[], deviceIds=[1, 2], status=completed, locations=联合新村, heatUnitId=1, createTime=2025-08-25T10:24:57, updateTime=2025-08-29T01:00)
2025-09-02T09:18:02.686+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T09:18:02.686+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:02.686+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:02.687+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:18:02.687+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:02.687+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:02.688+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T09:18:02.688+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:18:02.688+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-09-02T09:18:02.688+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:18:02.688+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:02.688+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:02.689+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:18:02.690+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:18:02.690+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:02.690+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:02.691+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:18:02.691+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:18:02.691+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:02.691+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:02.691+08:00  INFO 17916 --- [http-nio-8889-exec-6] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:18:02.691+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:18:02.692+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:18:02.693+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:18:02.693+08:00  INFO 17916 --- [http-nio-8889-exec-3] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T09:18:02.693+08:00  INFO 17916 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T09:18:02.693+08:00  INFO 17916 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T09:18:02.693+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:18:02.693+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:02.694+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:02.695+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:18:02.695+08:00  INFO 17916 --- [http-nio-8889-exec-7] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:18:02.695+08:00  INFO 17916 --- [http-nio-8889-exec-7] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:18:02.755+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T09:18:02.788+08:00  INFO 17916 --- [http-nio-8889-exec-7] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=22.9°C
2025-09-02T09:18:03.611+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/stats?uid=6&heatUnitId=0
2025-09-02T09:18:03.611+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:03.611+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:03.613+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/stats?uid=6&heatUnitId=0
2025-09-02T09:18:03.613+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.h.controller.WorkOrderController       : 获取工单统计数据: uid=6, heatUnitId=0
2025-09-02T09:18:03.613+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.h.service.impl.WorkOrderServiceImpl    : 开始获取工单统计数据: userId=6, heatUnitId=0
2025-09-02T09:18:03.679+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.h.service.impl.WorkOrderServiceImpl    : 用户角色: admin
2025-09-02T09:18:03.679+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.h.service.impl.WorkOrderServiceImpl    : 管理员或主管角色统计逻辑
2025-09-02T09:18:03.702+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T09:18:03.703+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:03.703+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:03.704+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T09:18:03.704+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T09:18:03.782+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.h.service.impl.WorkOrderServiceImpl    : 工单统计数据获取成功: 我的工单数=1, 待处理工单数=1, 待接单工单数=0
2025-09-02T09:18:04.001+08:00  INFO 17916 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:18:05.091+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing OPTIONS /api/auth/login
2025-09-02T09:18:05.091+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-09-02T09:18:05.092+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/api/auth/login?continue to session
2025-09-02T09:18:05.092+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-09-02T09:18:05.092+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing OPTIONS /error
2025-09-02T09:18:05.092+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-09-02T09:18:05.092+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/error?continue to session
2025-09-02T09:18:05.092+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-09-02T09:18:10.203+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing POST /api/auth/login
2025-09-02T09:18:10.203+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured POST /api/auth/login
2025-09-02T09:18:10.593+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-09-02T09:18:10.596+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/auth/user-info
2025-09-02T09:18:10.596+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:10.596+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:10.598+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/auth/user-info
2025-09-02T09:18:11.617+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=9&role=repair%2Cinspector&heatUnitId=1
2025-09-02T09:18:11.618+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:11.618+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:11.619+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:18:11.619+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=9&role=repair%2Cinspector&heatUnitId=1
2025-09-02T09:18:11.619+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:11.619+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:11.620+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=9, limit=3, page=1, pageSize=10, type=null, heatUnitId=1
2025-09-02T09:18:11.620+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:18:11.621+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:18:11.621+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:18:11.621+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:11.621+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:11.623+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:18:11.623+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:18:11.623+08:00  INFO 17916 --- [http-nio-8889-exec-5] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:18:11.623+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:11.623+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:18:11.623+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:11.625+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=9&role=repair%2Cinspector&limit=3
2025-09-02T09:18:11.626+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:11.626+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:11.626+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:18:11.627+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=9&role=repair%2Cinspector&limit=3
2025-09-02T09:18:11.628+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=9, role=repair,inspector, limit=3
2025-09-02T09:18:11.628+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=9, role=repair,inspector, limit=3
2025-09-02T09:18:11.628+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 以普通用户权限获取巡检工单, userId=9
2025-09-02T09:18:11.629+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:18:11.629+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:11.629+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:11.630+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:18:11.631+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:18:11.631+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:18:11.688+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T09:18:11.690+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:18:11.690+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:11.691+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:11.692+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:18:11.693+08:00  INFO 17916 --- [http-nio-8889-exec-3] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:18:11.693+08:00  INFO 17916 --- [http-nio-8889-exec-3] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:18:11.697+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=22.9°C
2025-09-02T09:18:11.699+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=9&role=repair%2Cinspector&heatUnitId=1
2025-09-02T09:18:11.699+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:18:11.699+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:11.699+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:11.699+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:11.700+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:11.701+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:18:11.701+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=9&role=repair%2Cinspector&heatUnitId=1
2025-09-02T09:18:11.702+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=9, limit=3, page=1, pageSize=10, type=null, heatUnitId=1
2025-09-02T09:18:11.702+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:18:11.726+08:00  INFO 17916 --- [http-nio-8889-exec-3] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=22.9°C
2025-09-02T09:18:11.735+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T09:18:11.858+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:18:11.858+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:11.858+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:11.860+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:18:11.884+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:18:11.884+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:11.884+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:11.885+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:18:11.886+08:00  INFO 17916 --- [http-nio-8889-exec-2] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:18:11.886+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:18:12.505+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing POST /api/patrols/records/list
2025-09-02T09:18:12.505+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:12.505+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:12.507+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured POST /api/patrols/records/list
2025-09-02T09:18:12.509+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.heating.controller.PatrolController    : 获取巡检记录列表请求: heatUnitId=1, executorId=9, status=null
2025-09-02T09:18:12.509+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取巡检记录列表: PatrolRecordListRequest(status=null, executorId=9, startDate=null, endDate=null, page=1, pageSize=5, heatUnitId=1)
2025-09-02T09:18:12.509+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 巡检记录查询 - 项目权限参数: 1
2025-09-02T09:18:12.509+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 项目权限过滤，允许的项目ID: [1]
2025-09-02T09:18:12.509+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 根据执行人ID和项目权限过滤巡检记录: executorId=9, heatUnitIds=[1]
2025-09-02T09:18:12.631+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=9
2025-09-02T09:18:12.631+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:12.631+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:12.632+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=9
2025-09-02T09:18:12.633+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 9
2025-09-02T09:18:12.914+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=9
2025-09-02T09:18:12.914+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:12.914+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:12.916+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=9
2025-09-02T09:18:12.916+08:00  INFO 17916 --- [http-nio-8889-exec-3] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 9
2025-09-02T09:18:12.964+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:18:12.965+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=9&role=repair%2Cinspector&limit=3
2025-09-02T09:18:12.965+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:12.965+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:12.966+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=9&role=repair%2Cinspector&limit=3
2025-09-02T09:18:12.967+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=9, role=repair,inspector, limit=3
2025-09-02T09:18:12.967+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=9, role=repair,inspector, limit=3
2025-09-02T09:18:12.967+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 以普通用户权限获取巡检工单, userId=9
2025-09-02T09:18:14.236+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:18:25.217+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=9&role=repair%2Cinspector&heatUnitId=1
2025-09-02T09:18:25.217+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:25.217+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:25.217+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:18:25.217+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:25.218+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:25.218+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:18:25.218+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:25.218+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:25.218+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=9&role=repair%2Cinspector&heatUnitId=1
2025-09-02T09:18:25.219+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=9, limit=3, page=1, pageSize=10, type=null, heatUnitId=1
2025-09-02T09:18:25.219+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:18:25.219+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:18:25.219+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:18:25.219+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:25.219+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:25.220+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:18:25.220+08:00  INFO 17916 --- [http-nio-8889-exec-5] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:18:25.220+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:18:25.221+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=9&role=repair%2Cinspector&limit=3
2025-09-02T09:18:25.221+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:18:25.221+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:25.221+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:25.222+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:18:25.222+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:25.222+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:25.223+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=9&role=repair%2Cinspector&limit=3
2025-09-02T09:18:25.223+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=9, role=repair,inspector, limit=3
2025-09-02T09:18:25.223+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=9, role=repair,inspector, limit=3
2025-09-02T09:18:25.223+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 以普通用户权限获取巡检工单, userId=9
2025-09-02T09:18:25.224+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:18:25.225+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:18:25.225+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:18:25.293+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=22.9°C
2025-09-02T09:18:25.293+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T09:18:26.256+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=9
2025-09-02T09:18:26.256+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:26.256+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:26.258+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=9
2025-09-02T09:18:26.258+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 9
2025-09-02T09:18:26.354+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing POST /api/patrols/plans/list
2025-09-02T09:18:26.354+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:26.354+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:26.355+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured POST /api/patrols/plans/list
2025-09-02T09:18:26.356+08:00  INFO 17916 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 巡检计划查询 - 项目权限参数: 1
2025-09-02T09:18:26.368+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/dict/data/11
2025-09-02T09:18:26.368+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:26.368+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:26.370+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/dict/data/11
2025-09-02T09:18:26.599+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:18:33.108+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/list
2025-09-02T09:18:33.108+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:33.108+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:33.108+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/dict/data/11
2025-09-02T09:18:33.108+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:33.108+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:33.110+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/list
2025-09-02T09:18:33.110+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/dict/data/11
2025-09-02T09:18:33.110+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.heating.controller.HeatUnitController  : Accessing GET /api/heatunits/list
2025-09-02T09:18:33.182+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.heating.controller.HeatUnitController  : Retrieved 14 heat units
2025-09-02T09:18:33.313+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/devices/heat-unit/1
2025-09-02T09:18:33.314+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:33.314+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:33.315+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/devices/heat-unit/1
2025-09-02T09:18:33.316+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.h.service.impl.DeviceServiceImpl       : 获取热用户ID:1的设备列表
2025-09-02T09:18:33.317+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing POST /api/auth/user/list
2025-09-02T09:18:33.317+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:33.317+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:33.320+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured POST /api/auth/user/list
2025-09-02T09:18:46.611+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=9&role=repair%2Cinspector&heatUnitId=1
2025-09-02T09:18:46.612+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:46.612+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:46.612+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:18:46.612+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:46.612+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:46.613+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:18:46.614+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:46.614+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:46.614+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=9&role=repair%2Cinspector&heatUnitId=1
2025-09-02T09:18:46.614+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:18:46.614+08:00  INFO 17916 --- [http-nio-8889-exec-7] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=9, limit=3, page=1, pageSize=10, type=null, heatUnitId=1
2025-09-02T09:18:46.614+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:18:46.614+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:18:46.614+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:46.614+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:46.615+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:18:46.616+08:00  INFO 17916 --- [http-nio-8889-exec-6] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:18:46.616+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:18:46.616+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=9&role=repair%2Cinspector&limit=3
2025-09-02T09:18:46.616+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:46.616+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:46.616+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:18:46.617+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:18:46.617+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:46.617+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:46.617+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=9&role=repair%2Cinspector&limit=3
2025-09-02T09:18:46.618+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=9, role=repair,inspector, limit=3
2025-09-02T09:18:46.618+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=9, role=repair,inspector, limit=3
2025-09-02T09:18:46.618+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 以普通用户权限获取巡检工单, userId=9
2025-09-02T09:18:46.618+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:18:46.619+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:18:46.619+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:18:46.689+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=22.9°C
2025-09-02T09:18:46.691+08:00  INFO 17916 --- [http-nio-8889-exec-7] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T09:18:47.622+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=9
2025-09-02T09:18:47.622+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:47.622+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:47.624+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=9
2025-09-02T09:18:47.624+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 9
2025-09-02T09:18:47.971+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:18:56.492+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing POST /api/patrols/plans/list
2025-09-02T09:18:56.492+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:56.492+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:56.493+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured POST /api/patrols/plans/list
2025-09-02T09:18:56.494+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 巡检计划查询 - 项目权限参数: 1
2025-09-02T09:18:56.504+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/dict/data/11
2025-09-02T09:18:56.504+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:18:56.505+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:18:56.506+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/dict/data/11
2025-09-02T09:19:02.253+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/plans/5
2025-09-02T09:19:02.253+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:19:02.253+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:19:02.255+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/plans/5
2025-09-02T09:19:02.255+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.heating.controller.PatrolController    : 获取巡检计划详情: 5
2025-09-02T09:19:02.488+08:00  INFO 17916 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 获取巡检计划详情: TPatrolPlan(id=5, planNo=20250825104358, name=测试, patrolType=日常巡检, startDate=2025-08-26, endDate=2025-08-30, executorIds=[13], scheduleType=daily, scheduleInterval=null, scheduleWeekDays=[], scheduleMonthDays=[], deviceIds=[1, 2], status=completed, locations=联合新村, heatUnitId=1, createTime=2025-08-25T10:43:59, updateTime=2025-08-31T01:00)
2025-09-02T09:19:04.646+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=9&role=repair%2Cinspector&heatUnitId=1
2025-09-02T09:19:04.647+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:19:04.647+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:19:04.647+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:19:04.647+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:19:04.647+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:19:04.648+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=9&role=repair%2Cinspector&heatUnitId=1
2025-09-02T09:19:04.649+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:19:04.649+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:19:04.649+08:00  INFO 17916 --- [http-nio-8889-exec-3] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=9, limit=3, page=1, pageSize=10, type=null, heatUnitId=1
2025-09-02T09:19:04.649+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:19:04.649+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:19:04.649+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:19:04.649+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:19:04.650+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:19:04.650+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:19:04.650+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=9&role=repair%2Cinspector&limit=3
2025-09-02T09:19:04.651+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:19:04.651+08:00  INFO 17916 --- [http-nio-8889-exec-7] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:19:04.651+08:00  INFO 17916 --- [http-nio-8889-exec-7] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:19:04.651+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:19:04.651+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:19:04.651+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:19:04.652+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:19:04.652+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:19:04.652+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:19:04.653+08:00 DEBUG 17916 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=9&role=repair%2Cinspector&limit=3
2025-09-02T09:19:04.653+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=9, role=repair,inspector, limit=3
2025-09-02T09:19:04.653+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=9, role=repair,inspector, limit=3
2025-09-02T09:19:04.653+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 以普通用户权限获取巡检工单, userId=9
2025-09-02T09:19:04.653+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:19:04.653+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:19:04.653+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:19:04.727+08:00  INFO 17916 --- [http-nio-8889-exec-3] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T09:19:04.727+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=22.9°C
2025-09-02T09:19:05.655+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=9
2025-09-02T09:19:05.655+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:19:05.655+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:19:05.657+08:00 DEBUG 17916 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=9
2025-09-02T09:19:05.657+08:00  INFO 17916 --- [http-nio-8889-exec-9] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 9
2025-09-02T09:19:05.668+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing POST /api/patrols/records/list
2025-09-02T09:19:05.668+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:19:05.668+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:19:05.669+08:00 DEBUG 17916 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured POST /api/patrols/records/list
2025-09-02T09:19:05.670+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.heating.controller.PatrolController    : 获取巡检记录列表请求: heatUnitId=1, executorId=9, status=null
2025-09-02T09:19:05.670+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 获取巡检记录列表: PatrolRecordListRequest(status=null, executorId=9, startDate=null, endDate=null, page=1, pageSize=5, heatUnitId=1)
2025-09-02T09:19:05.670+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 巡检记录查询 - 项目权限参数: 1
2025-09-02T09:19:05.670+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 项目权限过滤，允许的项目ID: [1]
2025-09-02T09:19:05.670+08:00  INFO 17916 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 根据执行人ID和项目权限过滤巡检记录: executorId=9, heatUnitIds=[1]
2025-09-02T09:19:05.930+08:00  INFO 17916 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:19:50.098+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=9&role=repair%2Cinspector&heatUnitId=1
2025-09-02T09:19:50.098+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:19:50.098+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:19:50.099+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:19:50.099+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:19:50.099+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:19:50.100+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:19:50.100+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:19:50.100+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:19:50.100+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=9&role=repair%2Cinspector&heatUnitId=1
2025-09-02T09:19:50.101+08:00 DEBUG 17916 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:19:50.101+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:19:50.101+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:19:50.102+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:19:50.102+08:00 DEBUG 17916 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:19:50.101+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=9, limit=3, page=1, pageSize=10, type=null, heatUnitId=1
2025-09-02T09:19:50.102+08:00  INFO 17916 --- [http-nio-8889-exec-5] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:19:50.102+08:00 DEBUG 17916 --- [http-nio-8889-exec-4] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:19:50.102+08:00  INFO 17916 --- [http-nio-8889-exec-5] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:19:50.103+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=9&role=repair%2Cinspector&limit=3
2025-09-02T09:19:50.103+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:19:50.103+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:19:50.104+08:00 DEBUG 17916 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:19:50.104+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:19:50.104+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:19:50.104+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:19:50.105+08:00 DEBUG 17916 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=9&role=repair%2Cinspector&limit=3
2025-09-02T09:19:50.105+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=9, role=repair,inspector, limit=3
2025-09-02T09:19:50.105+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=9, role=repair,inspector, limit=3
2025-09-02T09:19:50.105+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 以普通用户权限获取巡检工单, userId=9
2025-09-02T09:19:50.106+08:00 DEBUG 17916 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:19:50.106+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:19:50.106+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:19:50.168+08:00  INFO 17916 --- [http-nio-8889-exec-4] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T09:19:50.208+08:00  INFO 17916 --- [http-nio-8889-exec-2] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=22.9°C
2025-09-02T09:19:51.110+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=9
2025-09-02T09:19:51.110+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:19:51.110+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:19:51.112+08:00 DEBUG 17916 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=9
2025-09-02T09:19:51.112+08:00  INFO 17916 --- [http-nio-8889-exec-7] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 9
2025-09-02T09:19:51.451+08:00  INFO 17916 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:19:51.648+08:00  INFO 17916 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-02T09:19:51.649+08:00  INFO 17916 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown initiated...
2025-09-02T09:19:51.850+08:00  INFO 17916 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown completed.
2025-09-02T09:20:02.114+08:00  INFO 3204 --- [main] com.heating.HeatingApplication           : Starting HeatingApplication using Java 21.0.7 with PID 3204 (E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend\target\classes started by A in E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend)
2025-09-02T09:20:02.115+08:00 DEBUG 3204 --- [main] com.heating.HeatingApplication           : Running with Spring Boot v3.2.3, Spring v6.1.4
2025-09-02T09:20:02.115+08:00  INFO 3204 --- [main] com.heating.HeatingApplication           : No active profile set, falling back to 1 default profile: "default"
2025-09-02T09:20:02.736+08:00  INFO 3204 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-02T09:20:02.846+08:00  INFO 3204 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 103 ms. Found 35 JPA repository interfaces.
2025-09-02T09:20:03.346+08:00  INFO 3204 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8889 (http)
2025-09-02T09:20:03.401+08:00  INFO 3204 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1253 ms
2025-09-02T09:20:03.452+08:00 DEBUG 3204 --- [main] c.h.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-09-02T09:20:03.545+08:00  INFO 3204 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-02T09:20:03.596+08:00  INFO 3204 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-02T09:20:03.623+08:00  INFO 3204 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-02T09:20:03.705+08:00  INFO 3204 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Starting...
2025-09-02T09:20:03.840+08:00  INFO 3204 --- [main] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@6cc8c13c
2025-09-02T09:20:03.841+08:00  INFO 3204 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Start completed.
2025-09-02T09:20:03.871+08:00  WARN 3204 --- [main] org.hibernate.dialect.Dialect            : HHH000511: The 5.6.17 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
2025-09-02T09:50:23.431+08:00  INFO 3520 --- [main] com.heating.HeatingApplication           : Starting HeatingApplication using Java 21.0.7 with PID 3520 (E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend\target\classes started by A in E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend)
2025-09-02T09:50:23.432+08:00 DEBUG 3520 --- [main] com.heating.HeatingApplication           : Running with Spring Boot v3.2.3, Spring v6.1.4
2025-09-02T09:50:23.432+08:00  INFO 3520 --- [main] com.heating.HeatingApplication           : No active profile set, falling back to 1 default profile: "default"
2025-09-02T09:50:23.992+08:00  INFO 3520 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-02T09:50:24.101+08:00  INFO 3520 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 103 ms. Found 35 JPA repository interfaces.
2025-09-02T09:50:24.577+08:00  INFO 3520 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8889 (http)
2025-09-02T09:50:24.586+08:00  INFO 3520 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-02T09:50:24.586+08:00  INFO 3520 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-09-02T09:50:24.633+08:00  INFO 3520 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-02T09:50:24.634+08:00  INFO 3520 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1171 ms
2025-09-02T09:50:24.683+08:00 DEBUG 3520 --- [main] c.h.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-09-02T09:50:24.775+08:00  INFO 3520 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-02T09:50:24.823+08:00  INFO 3520 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-02T09:50:24.850+08:00  INFO 3520 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-02T09:50:24.933+08:00  INFO 3520 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Starting...
2025-09-02T09:50:25.846+08:00  INFO 3520 --- [main] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@4045fd1f
2025-09-02T09:50:25.848+08:00  INFO 3520 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Start completed.
2025-09-02T09:50:26.155+08:00  INFO 3520 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-02T09:50:27.306+08:00  INFO 3520 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-02T09:50:27.989+08:00  INFO 3520 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-02T09:50:28.187+08:00  INFO 3520 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-02T09:50:29.847+08:00  INFO 3520 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源跨域访问: /uploads/**
2025-09-02T09:50:29.937+08:00  INFO 3520 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源映射: /uploads/** -> file:/home/<USER>/work/web/uploads/
2025-09-02T09:50:29.978+08:00  INFO 3520 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@29031a38, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2e607b8, org.springframework.security.web.context.SecurityContextHolderFilter@51145fdc, org.springframework.security.web.header.HeaderWriterFilter@c624fdc, org.springframework.web.filter.CorsFilter@3db1ebbb, org.springframework.security.web.authentication.logout.LogoutFilter@32a826f, com.heating.filter.JwtAuthenticationFilter@76216830, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@519cb1bd, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3cceea18, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@8289fba, org.springframework.security.web.access.ExceptionTranslationFilter@22360505, org.springframework.security.web.access.intercept.AuthorizationFilter@454048e]
2025-09-02T09:50:30.237+08:00  INFO 3520 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8889 (http) with context path ''
2025-09-02T09:50:30.245+08:00  INFO 3520 --- [main] com.heating.HeatingApplication           : Started HeatingApplication in 7.117 seconds (process running for 7.621)
2025-09-02T09:52:25.664+08:00  WARN 3520 --- [MyHikariPool housekeeper] com.zaxxer.hikari.pool.PoolBase          : MyHikariPool - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@6352af32 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-09-02T09:52:35.690+08:00  WARN 3520 --- [MyHikariPool housekeeper] com.zaxxer.hikari.pool.PoolBase          : MyHikariPool - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@4045fd1f (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-09-02T09:52:45.708+08:00  WARN 3520 --- [MyHikariPool housekeeper] com.zaxxer.hikari.pool.PoolBase          : MyHikariPool - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@4f57e624 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-09-02T09:52:45.708+08:00  WARN 3520 --- [MyHikariPool housekeeper] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Thread starvation or clock leap detected (housekeeper delta=49s725ms61µs100ns).
2025-09-02T09:52:55.738+08:00  WARN 3520 --- [MyHikariPool housekeeper] com.zaxxer.hikari.pool.PoolBase          : MyHikariPool - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@759e66d5 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-09-02T09:53:05.756+08:00  WARN 3520 --- [MyHikariPool housekeeper] com.zaxxer.hikari.pool.PoolBase          : MyHikariPool - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@35796086 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-09-02T09:56:10.332+08:00  INFO 3520 --- [http-nio-8889-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-02T09:56:10.333+08:00  INFO 3520 --- [http-nio-8889-exec-2] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-02T09:56:10.334+08:00  INFO 3520 --- [http-nio-8889-exec-2] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-09-02T09:56:10.358+08:00 DEBUG 3520 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/plans/messages?uid=6
2025-09-02T09:56:10.379+08:00 DEBUG 3520 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:56:10.379+08:00 DEBUG 3520 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:56:10.456+08:00 DEBUG 3520 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/plans/messages?uid=6
2025-09-02T09:56:10.473+08:00  INFO 3520 --- [http-nio-8889-exec-2] c.heating.controller.PatrolController    : 获取巡检提醒消息: uid=6, role=null
2025-09-02T09:56:10.473+08:00  INFO 3520 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息开始: userId=6, role=null
2025-09-02T09:56:10.656+08:00  INFO 3520 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 非管理员角色，查询到0个用户6的待执行或已超时巡检记录
2025-09-02T09:56:10.656+08:00  INFO 3520 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息完成，共0条消息
2025-09-02T09:56:10.687+08:00 DEBUG 3520 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T09:56:10.688+08:00 DEBUG 3520 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:56:10.688+08:00 DEBUG 3520 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:56:10.691+08:00 DEBUG 3520 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T09:56:10.698+08:00  INFO 3520 --- [http-nio-8889-exec-1] com.heating.controller.AlarmController   : 获取告警消息: uid=6, role=admin, heatUnitId=0
2025-09-02T09:56:10.698+08:00  INFO 3520 --- [http-nio-8889-exec-1] c.heating.service.impl.AlarmServiceImpl  : 开始获取告警消息列表: userId=6, role=admin, heatUnitId=0
2025-09-02T09:56:10.738+08:00  INFO 3520 --- [http-nio-8889-exec-1] c.heating.service.impl.AlarmServiceImpl  : 获取告警消息列表成功，共0条
2025-09-02T09:56:10.764+08:00 DEBUG 3520 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T09:56:10.764+08:00 DEBUG 3520 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:56:10.764+08:00 DEBUG 3520 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:56:10.768+08:00 DEBUG 3520 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T09:56:10.772+08:00  INFO 3520 --- [http-nio-8889-exec-3] com.heating.controller.FaultController   : 获取故障消息: uid=6, role=admin, heatUnitId=0
2025-09-02T09:56:10.772+08:00  INFO 3520 --- [http-nio-8889-exec-3] c.heating.service.impl.FaultServiceImpl  : 开始获取故障消息列表: userId=6, role=admin, heatUnitId=0
2025-09-02T09:56:10.832+08:00 DEBUG 3520 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T09:56:10.832+08:00 DEBUG 3520 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:56:10.832+08:00 DEBUG 3520 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:56:10.835+08:00 DEBUG 3520 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T09:56:10.836+08:00  INFO 3520 --- [http-nio-8889-exec-4] c.h.controller.WorkOrderController       : 获取工单消息: uid=6, role=admin, heatUnitId=0
2025-09-02T09:56:10.836+08:00  INFO 3520 --- [http-nio-8889-exec-4] c.h.service.impl.WorkOrderServiceImpl    : 开始获取工单消息（待接单状态的工单）: userId=6, role=admin, heatUnitId=0
2025-09-02T09:56:10.937+08:00  INFO 3520 --- [http-nio-8889-exec-4] c.h.service.impl.WorkOrderServiceImpl    : 获取工单消息成功，共找到0条待接单工单
2025-09-02T09:56:38.452+08:00 DEBUG 3520 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/plans/messages?uid=6
2025-09-02T09:56:38.452+08:00 DEBUG 3520 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:56:38.453+08:00 DEBUG 3520 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:56:38.455+08:00 DEBUG 3520 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/plans/messages?uid=6
2025-09-02T09:56:38.456+08:00  INFO 3520 --- [http-nio-8889-exec-8] c.heating.controller.PatrolController    : 获取巡检提醒消息: uid=6, role=null
2025-09-02T09:56:38.456+08:00  INFO 3520 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息开始: userId=6, role=null
2025-09-02T09:56:38.533+08:00  INFO 3520 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 非管理员角色，查询到0个用户6的待执行或已超时巡检记录
2025-09-02T09:56:38.533+08:00  INFO 3520 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息完成，共0条消息
2025-09-02T09:56:38.538+08:00 DEBUG 3520 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T09:56:38.538+08:00 DEBUG 3520 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:56:38.538+08:00 DEBUG 3520 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:56:38.541+08:00 DEBUG 3520 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T09:56:38.542+08:00  INFO 3520 --- [http-nio-8889-exec-6] com.heating.controller.AlarmController   : 获取告警消息: uid=6, role=admin, heatUnitId=0
2025-09-02T09:56:38.542+08:00  INFO 3520 --- [http-nio-8889-exec-6] c.heating.service.impl.AlarmServiceImpl  : 开始获取告警消息列表: userId=6, role=admin, heatUnitId=0
2025-09-02T09:56:38.582+08:00  INFO 3520 --- [http-nio-8889-exec-6] c.heating.service.impl.AlarmServiceImpl  : 获取告警消息列表成功，共0条
2025-09-02T09:56:38.587+08:00 DEBUG 3520 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T09:56:38.587+08:00 DEBUG 3520 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:56:38.587+08:00 DEBUG 3520 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:56:38.590+08:00 DEBUG 3520 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T09:56:38.591+08:00  INFO 3520 --- [http-nio-8889-exec-7] com.heating.controller.FaultController   : 获取故障消息: uid=6, role=admin, heatUnitId=0
2025-09-02T09:56:38.591+08:00  INFO 3520 --- [http-nio-8889-exec-7] c.heating.service.impl.FaultServiceImpl  : 开始获取故障消息列表: userId=6, role=admin, heatUnitId=0
2025-09-02T09:56:38.635+08:00 DEBUG 3520 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T09:56:38.636+08:00 DEBUG 3520 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:56:38.636+08:00 DEBUG 3520 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:56:38.638+08:00 DEBUG 3520 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T09:56:38.639+08:00  INFO 3520 --- [http-nio-8889-exec-5] c.h.controller.WorkOrderController       : 获取工单消息: uid=6, role=admin, heatUnitId=0
2025-09-02T09:56:38.639+08:00  INFO 3520 --- [http-nio-8889-exec-5] c.h.service.impl.WorkOrderServiceImpl    : 开始获取工单消息（待接单状态的工单）: userId=6, role=admin, heatUnitId=0
2025-09-02T09:56:38.679+08:00  INFO 3520 --- [http-nio-8889-exec-5] c.h.service.impl.WorkOrderServiceImpl    : 获取工单消息成功，共找到0条待接单工单
2025-09-02T09:57:26.961+08:00  INFO 3520 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-02T09:57:26.962+08:00  INFO 3520 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown initiated...
2025-09-02T09:57:27.147+08:00  INFO 3520 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown completed.
2025-09-02T09:57:33.751+08:00  INFO 9756 --- [main] com.heating.HeatingApplication           : Starting HeatingApplication using Java 21.0.7 with PID 9756 (E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend\target\classes started by A in E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend)
2025-09-02T09:57:33.752+08:00 DEBUG 9756 --- [main] com.heating.HeatingApplication           : Running with Spring Boot v3.2.3, Spring v6.1.4
2025-09-02T09:57:33.752+08:00  INFO 9756 --- [main] com.heating.HeatingApplication           : No active profile set, falling back to 1 default profile: "default"
2025-09-02T09:57:34.380+08:00  INFO 9756 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-02T09:57:34.492+08:00  INFO 9756 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 106 ms. Found 35 JPA repository interfaces.
2025-09-02T09:57:35.055+08:00  INFO 9756 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8889 (http)
2025-09-02T09:57:35.069+08:00  INFO 9756 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-02T09:57:35.069+08:00  INFO 9756 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-09-02T09:57:35.119+08:00  INFO 9756 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-02T09:57:35.120+08:00  INFO 9756 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1335 ms
2025-09-02T09:57:35.184+08:00 DEBUG 9756 --- [main] c.h.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-09-02T09:57:35.282+08:00  INFO 9756 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-02T09:57:35.331+08:00  INFO 9756 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-02T09:57:35.357+08:00  INFO 9756 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-02T09:57:35.449+08:00  INFO 9756 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Starting...
2025-09-02T09:57:40.113+08:00  INFO 9756 --- [main] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@2976ef56
2025-09-02T09:57:40.114+08:00  INFO 9756 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Start completed.
2025-09-02T09:57:40.380+08:00  INFO 9756 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-02T09:57:41.565+08:00  INFO 9756 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-02T09:57:42.254+08:00  INFO 9756 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-02T09:57:42.442+08:00  INFO 9756 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-02T09:57:44.091+08:00  INFO 9756 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源跨域访问: /uploads/**
2025-09-02T09:57:44.175+08:00  INFO 9756 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源映射: /uploads/** -> file:/home/<USER>/work/web/uploads/
2025-09-02T09:57:44.213+08:00  INFO 9756 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@ba28d1b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@397c53e7, org.springframework.security.web.context.SecurityContextHolderFilter@378d8d1b, org.springframework.security.web.header.HeaderWriterFilter@519cb1bd, org.springframework.web.filter.CorsFilter@7565ce3d, org.springframework.security.web.authentication.logout.LogoutFilter@1dcc501f, com.heating.filter.JwtAuthenticationFilter@2871ac91, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@48fd7749, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3a9fd60c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@107947a2, org.springframework.security.web.access.ExceptionTranslationFilter@51145fdc, org.springframework.security.web.access.intercept.AuthorizationFilter@2e607b8]
2025-09-02T09:57:44.466+08:00  INFO 9756 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8889 (http) with context path ''
2025-09-02T09:57:44.475+08:00  INFO 9756 --- [main] com.heating.HeatingApplication           : Started HeatingApplication in 11.06 seconds (process running for 11.586)
2025-09-02T09:58:35.705+08:00  INFO 9756 --- [http-nio-8889-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-02T09:58:35.705+08:00  INFO 9756 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-02T09:58:35.706+08:00  INFO 9756 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-09-02T09:58:35.720+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing OPTIONS /api/auth/login
2025-09-02T09:58:35.735+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-09-02T09:58:35.748+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/api/auth/login?continue to session
2025-09-02T09:58:35.748+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-09-02T09:58:35.755+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing OPTIONS /error
2025-09-02T09:58:35.756+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-09-02T09:58:35.756+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/error?continue to session
2025-09-02T09:58:35.756+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-09-02T09:58:37.442+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing POST /api/auth/login
2025-09-02T09:58:37.443+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured POST /api/auth/login
2025-09-02T09:58:38.033+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-09-02T09:58:38.037+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/auth/user-info
2025-09-02T09:58:38.038+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:38.038+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:38.056+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/auth/user-info
2025-09-02T09:58:39.308+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T09:58:39.309+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:39.309+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:39.309+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:58:39.310+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:39.311+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:39.312+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:58:39.312+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:39.312+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:58:39.312+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:39.313+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:39.314+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:39.315+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:58:39.315+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:58:39.315+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:58:39.315+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:39.315+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:39.315+08:00  INFO 9756 --- [http-nio-8889-exec-7] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:58:39.316+08:00  INFO 9756 --- [http-nio-8889-exec-7] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:58:39.316+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:58:39.316+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:58:39.316+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:39.316+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:39.318+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T09:58:39.318+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:58:39.319+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:58:39.321+08:00  INFO 9756 --- [http-nio-8889-exec-9] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:58:39.321+08:00  INFO 9756 --- [http-nio-8889-exec-9] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:58:39.325+08:00  INFO 9756 --- [http-nio-8889-exec-8] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T09:58:39.325+08:00  INFO 9756 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T09:58:39.326+08:00  INFO 9756 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T09:58:39.326+08:00  INFO 9756 --- [http-nio-8889-exec-6] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-09-02T09:58:39.326+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:58:39.399+08:00  INFO 9756 --- [http-nio-8889-exec-9] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=23.1°C
2025-09-02T09:58:39.403+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:58:39.404+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:39.404+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:39.407+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:58:39.409+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:39.409+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:39.410+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:58:39.412+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:58:39.413+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:58:39.413+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:58:39.447+08:00  INFO 9756 --- [http-nio-8889-exec-6] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T09:58:39.451+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T09:58:39.451+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:39.451+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:39.453+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=23.1°C
2025-09-02T09:58:39.456+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T09:58:39.457+08:00  INFO 9756 --- [http-nio-8889-exec-4] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-09-02T09:58:39.457+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:58:39.494+08:00  INFO 9756 --- [http-nio-8889-exec-4] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T09:58:39.578+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:58:39.579+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:39.579+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:39.583+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:58:39.618+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:58:39.618+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:39.618+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:39.620+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:58:39.621+08:00  INFO 9756 --- [http-nio-8889-exec-9] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:58:39.621+08:00  INFO 9756 --- [http-nio-8889-exec-9] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:58:40.110+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/plans/messages?uid=6
2025-09-02T09:58:40.111+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:40.111+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:40.114+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/plans/messages?uid=6
2025-09-02T09:58:40.115+08:00  INFO 9756 --- [http-nio-8889-exec-3] c.heating.controller.PatrolController    : 获取巡检提醒消息: uid=6
2025-09-02T09:58:40.115+08:00  INFO 9756 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息开始: userId=6
2025-09-02T09:58:40.201+08:00  INFO 9756 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 查询到0个用户6的待执行或已超时巡检记录
2025-09-02T09:58:40.201+08:00  INFO 9756 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息完成，共0条消息
2025-09-02T09:58:40.206+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T09:58:40.206+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:40.206+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:40.209+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T09:58:40.209+08:00  INFO 9756 --- [http-nio-8889-exec-6] com.heating.controller.AlarmController   : 获取告警消息: uid=6, role=admin, heatUnitId=0
2025-09-02T09:58:40.210+08:00  INFO 9756 --- [http-nio-8889-exec-6] c.heating.service.impl.AlarmServiceImpl  : 开始获取告警消息列表: userId=6, role=admin, heatUnitId=0
2025-09-02T09:58:40.249+08:00  INFO 9756 --- [http-nio-8889-exec-6] c.heating.service.impl.AlarmServiceImpl  : 获取告警消息列表成功，共0条
2025-09-02T09:58:40.253+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T09:58:40.254+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:40.254+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:40.256+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T09:58:40.257+08:00  INFO 9756 --- [http-nio-8889-exec-10] com.heating.controller.FaultController   : 获取故障消息: uid=6, role=admin, heatUnitId=0
2025-09-02T09:58:40.258+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.heating.service.impl.FaultServiceImpl  : 开始获取故障消息列表: userId=6, role=admin, heatUnitId=0
2025-09-02T09:58:40.308+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T09:58:40.308+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:40.308+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:40.310+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T09:58:40.312+08:00  INFO 9756 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 获取工单消息: uid=6, role=admin, heatUnitId=0
2025-09-02T09:58:40.312+08:00  INFO 9756 --- [http-nio-8889-exec-1] c.h.service.impl.WorkOrderServiceImpl    : 开始获取工单消息（待接单状态的工单）: userId=6, role=admin, heatUnitId=0
2025-09-02T09:58:40.319+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T09:58:40.319+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:40.319+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:40.321+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T09:58:40.322+08:00  INFO 9756 --- [http-nio-8889-exec-4] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T09:58:40.388+08:00  INFO 9756 --- [http-nio-8889-exec-1] c.h.service.impl.WorkOrderServiceImpl    : 获取工单消息成功，共找到0条待接单工单
2025-09-02T09:58:40.582+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T09:58:40.583+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:40.583+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:40.585+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T09:58:40.586+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T09:58:40.796+08:00  INFO 9756 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:58:40.799+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:58:40.799+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:40.800+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:40.802+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:58:40.803+08:00  INFO 9756 --- [http-nio-8889-exec-7] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T09:58:40.803+08:00  INFO 9756 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T09:58:40.803+08:00  INFO 9756 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T09:58:42.271+08:00  INFO 9756 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:58:45.857+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T09:58:45.858+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:45.858+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:45.858+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:58:45.859+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:45.859+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:45.859+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:58:45.859+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:45.859+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:45.860+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T09:58:45.861+08:00  INFO 9756 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-09-02T09:58:45.861+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:58:45.862+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:58:45.863+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:58:45.863+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:58:45.863+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:45.863+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:45.863+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:45.863+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:58:45.864+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:45.864+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:45.864+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:45.865+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:58:45.865+08:00  INFO 9756 --- [http-nio-8889-exec-3] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:58:45.865+08:00  INFO 9756 --- [http-nio-8889-exec-3] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:58:45.866+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:58:45.866+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T09:58:45.866+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T09:58:45.867+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T09:58:45.868+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:58:45.869+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:58:45.869+08:00  INFO 9756 --- [http-nio-8889-exec-1] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:58:45.869+08:00  INFO 9756 --- [http-nio-8889-exec-1] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:58:45.937+08:00  INFO 9756 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T09:58:45.943+08:00  INFO 9756 --- [http-nio-8889-exec-1] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=23.1°C
2025-09-02T09:58:46.866+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T09:58:46.866+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:46.866+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:46.874+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T09:58:46.875+08:00  INFO 9756 --- [http-nio-8889-exec-4] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T09:58:47.341+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T09:58:57.242+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/plans/messages?uid=6
2025-09-02T09:58:57.243+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:57.243+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:57.245+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/plans/messages?uid=6
2025-09-02T09:58:57.246+08:00  INFO 9756 --- [http-nio-8889-exec-8] c.heating.controller.PatrolController    : 获取巡检提醒消息: uid=6
2025-09-02T09:58:57.246+08:00  INFO 9756 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息开始: userId=6
2025-09-02T09:58:57.322+08:00  INFO 9756 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 查询到0个用户6的待执行或已超时巡检记录
2025-09-02T09:58:57.322+08:00  INFO 9756 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息完成，共0条消息
2025-09-02T09:58:57.326+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T09:58:57.327+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:57.327+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:57.329+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T09:58:57.330+08:00  INFO 9756 --- [http-nio-8889-exec-5] com.heating.controller.AlarmController   : 获取告警消息: uid=6, role=admin, heatUnitId=0
2025-09-02T09:58:57.330+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.heating.service.impl.AlarmServiceImpl  : 开始获取告警消息列表: userId=6, role=admin, heatUnitId=0
2025-09-02T09:58:57.408+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.heating.service.impl.AlarmServiceImpl  : 获取告警消息列表成功，共0条
2025-09-02T09:58:57.413+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T09:58:57.414+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:57.414+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:57.416+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T09:58:57.417+08:00  INFO 9756 --- [http-nio-8889-exec-7] com.heating.controller.FaultController   : 获取故障消息: uid=6, role=admin, heatUnitId=0
2025-09-02T09:58:57.417+08:00  INFO 9756 --- [http-nio-8889-exec-7] c.heating.service.impl.FaultServiceImpl  : 开始获取故障消息列表: userId=6, role=admin, heatUnitId=0
2025-09-02T09:58:57.499+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T09:58:57.499+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:57.499+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:57.501+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T09:58:57.502+08:00  INFO 9756 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 获取工单消息: uid=6, role=admin, heatUnitId=0
2025-09-02T09:58:57.502+08:00  INFO 9756 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 开始获取工单消息（待接单状态的工单）: userId=6, role=admin, heatUnitId=0
2025-09-02T09:58:57.544+08:00  INFO 9756 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 获取工单消息成功，共找到0条待接单工单
2025-09-02T09:58:58.452+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T09:58:58.452+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T09:58:58.452+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:58.452+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:58.452+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:58.452+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:58.453+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T09:58:58.454+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:58.454+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:58.454+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T09:58:58.455+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:58.455+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:58.455+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:58:58.456+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:58.456+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:58.458+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T09:58:58.459+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T09:58:58.460+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T09:58:58.460+08:00  INFO 9756 --- [http-nio-8889-exec-4] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T09:58:58.460+08:00  INFO 9756 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T09:58:58.460+08:00  INFO 9756 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T09:58:58.460+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:58.461+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:58.463+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T09:58:58.464+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T09:58:58.464+08:00  INFO 9756 --- [http-nio-8889-exec-9] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T09:58:58.464+08:00  INFO 9756 --- [http-nio-8889-exec-9] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T09:58:58.464+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T09:58:58.465+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T09:58:58.465+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T09:58:58.465+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T09:58:58.466+08:00  INFO 9756 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-09-02T09:58:58.466+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T09:58:58.538+08:00  INFO 9756 --- [http-nio-8889-exec-1] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T09:58:58.541+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=23.1°C
2025-09-02T09:58:59.465+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T09:58:59.465+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T09:58:59.465+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T09:58:59.468+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T09:58:59.468+08:00  INFO 9756 --- [http-nio-8889-exec-8] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T09:58:59.918+08:00  INFO 9756 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T10:00:00.302+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-09-02T10:00:00.302+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:00:00.303+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:00:00.305+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-09-02T10:00:00.307+08:00  INFO 9756 --- [http-nio-8889-exec-6] com.heating.controller.FaultController   : 获取故障列表: status=null, date=null, heatUnitId=0, page=1, pageSize=5
2025-09-02T10:00:00.308+08:00  INFO 9756 --- [http-nio-8889-exec-6] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-09-02T10:00:00.308+08:00  INFO 9756 --- [http-nio-8889-exec-6] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-09-02T10:00:02.057+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T10:00:02.057+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:00:02.057+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:00:02.058+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T10:00:02.059+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:00:02.059+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:00:02.059+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T10:00:02.059+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T10:00:02.060+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:00:02.060+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:00:02.060+08:00  INFO 9756 --- [http-nio-8889-exec-9] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-09-02T10:00:02.060+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T10:00:02.061+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T10:00:02.061+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:00:02.061+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:00:02.062+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T10:00:02.062+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:00:02.062+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:00:02.063+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T10:00:02.064+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T10:00:02.064+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:00:02.064+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:00:02.065+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T10:00:02.065+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T10:00:02.066+08:00  INFO 9756 --- [http-nio-8889-exec-4] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T10:00:02.066+08:00  INFO 9756 --- [http-nio-8889-exec-4] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T10:00:02.066+08:00  INFO 9756 --- [http-nio-8889-exec-2] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T10:00:02.066+08:00  INFO 9756 --- [http-nio-8889-exec-2] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T10:00:02.067+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T10:00:02.068+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T10:00:02.068+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T10:00:02.068+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T10:00:02.068+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T10:00:02.135+08:00  INFO 9756 --- [http-nio-8889-exec-9] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T10:00:02.139+08:00  INFO 9756 --- [http-nio-8889-exec-2] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=23.1°C
2025-09-02T10:00:03.072+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T10:00:03.072+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:00:03.072+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:00:03.074+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T10:00:03.074+08:00  INFO 9756 --- [http-nio-8889-exec-7] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T10:00:03.555+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T10:00:05.024+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/plans/messages?uid=6
2025-09-02T10:00:05.024+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:00:05.024+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:00:05.026+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/plans/messages?uid=6
2025-09-02T10:00:05.027+08:00  INFO 9756 --- [http-nio-8889-exec-1] c.heating.controller.PatrolController    : 获取巡检提醒消息: uid=6
2025-09-02T10:00:05.027+08:00  INFO 9756 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息开始: userId=6
2025-09-02T10:00:05.110+08:00  INFO 9756 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 查询到0个用户6的待执行或已超时巡检记录
2025-09-02T10:00:05.110+08:00  INFO 9756 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息完成，共0条消息
2025-09-02T10:00:05.114+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:00:05.115+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:00:05.115+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:00:05.116+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:00:05.117+08:00  INFO 9756 --- [http-nio-8889-exec-10] com.heating.controller.AlarmController   : 获取告警消息: uid=6, role=admin, heatUnitId=0
2025-09-02T10:00:05.117+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.heating.service.impl.AlarmServiceImpl  : 开始获取告警消息列表: userId=6, role=admin, heatUnitId=0
2025-09-02T10:00:05.193+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.heating.service.impl.AlarmServiceImpl  : 获取告警消息列表成功，共0条
2025-09-02T10:00:05.197+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:00:05.197+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:00:05.197+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:00:05.199+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:00:05.199+08:00  INFO 9756 --- [http-nio-8889-exec-6] com.heating.controller.FaultController   : 获取故障消息: uid=6, role=admin, heatUnitId=0
2025-09-02T10:00:05.199+08:00  INFO 9756 --- [http-nio-8889-exec-6] c.heating.service.impl.FaultServiceImpl  : 开始获取故障消息列表: userId=6, role=admin, heatUnitId=0
2025-09-02T10:00:05.279+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:00:05.279+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:00:05.279+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:00:05.281+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:00:05.281+08:00  INFO 9756 --- [http-nio-8889-exec-9] c.h.controller.WorkOrderController       : 获取工单消息: uid=6, role=admin, heatUnitId=0
2025-09-02T10:00:05.281+08:00  INFO 9756 --- [http-nio-8889-exec-9] c.h.service.impl.WorkOrderServiceImpl    : 开始获取工单消息（待接单状态的工单）: userId=6, role=admin, heatUnitId=0
2025-09-02T10:00:05.320+08:00  INFO 9756 --- [http-nio-8889-exec-9] c.h.service.impl.WorkOrderServiceImpl    : 获取工单消息成功，共找到0条待接单工单
2025-09-02T10:00:10.465+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T10:00:10.465+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T10:00:10.466+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:00:10.466+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:00:10.466+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:00:10.466+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:00:10.466+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T10:00:10.466+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:00:10.466+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:00:10.467+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T10:00:10.468+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:00:10.468+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:00:10.468+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T10:00:10.468+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T10:00:10.468+08:00  INFO 9756 --- [http-nio-8889-exec-8] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T10:00:10.468+08:00  INFO 9756 --- [http-nio-8889-exec-8] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T10:00:10.469+08:00  INFO 9756 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-09-02T10:00:10.469+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T10:00:10.469+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T10:00:10.470+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:00:10.470+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:00:10.471+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T10:00:10.472+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T10:00:10.473+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T10:00:10.474+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:00:10.474+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:00:10.475+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T10:00:10.476+08:00  INFO 9756 --- [http-nio-8889-exec-7] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T10:00:10.476+08:00  INFO 9756 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T10:00:10.476+08:00  INFO 9756 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T10:00:10.476+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T10:00:10.477+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T10:00:10.477+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T10:00:10.549+08:00  INFO 9756 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T10:00:10.554+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=23.1°C
2025-09-02T10:00:11.474+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T10:00:11.475+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:00:11.475+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:00:11.476+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T10:00:11.477+08:00  INFO 9756 --- [http-nio-8889-exec-1] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T10:00:11.991+08:00  INFO 9756 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T10:00:14.568+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/plans/messages?uid=6
2025-09-02T10:00:14.569+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:00:14.569+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:00:14.573+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/plans/messages?uid=6
2025-09-02T10:00:14.574+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.heating.controller.PatrolController    : 获取巡检提醒消息: uid=6
2025-09-02T10:00:14.574+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息开始: userId=6
2025-09-02T10:00:14.657+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 查询到0个用户6的待执行或已超时巡检记录
2025-09-02T10:00:14.657+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息完成，共0条消息
2025-09-02T10:00:14.661+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:00:14.661+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:00:14.661+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:00:14.663+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:00:14.664+08:00  INFO 9756 --- [http-nio-8889-exec-6] com.heating.controller.AlarmController   : 获取告警消息: uid=6, role=admin, heatUnitId=0
2025-09-02T10:00:14.664+08:00  INFO 9756 --- [http-nio-8889-exec-6] c.heating.service.impl.AlarmServiceImpl  : 开始获取告警消息列表: userId=6, role=admin, heatUnitId=0
2025-09-02T10:00:14.740+08:00  INFO 9756 --- [http-nio-8889-exec-6] c.heating.service.impl.AlarmServiceImpl  : 获取告警消息列表成功，共0条
2025-09-02T10:00:14.744+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:00:14.745+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:00:14.745+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:00:14.746+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:00:14.747+08:00  INFO 9756 --- [http-nio-8889-exec-9] com.heating.controller.FaultController   : 获取故障消息: uid=6, role=admin, heatUnitId=0
2025-09-02T10:00:14.747+08:00  INFO 9756 --- [http-nio-8889-exec-9] c.heating.service.impl.FaultServiceImpl  : 开始获取故障消息列表: userId=6, role=admin, heatUnitId=0
2025-09-02T10:00:14.826+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:00:14.826+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:00:14.826+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:00:14.829+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:00:14.830+08:00  INFO 9756 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 获取工单消息: uid=6, role=admin, heatUnitId=0
2025-09-02T10:00:14.830+08:00  INFO 9756 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 开始获取工单消息（待接单状态的工单）: userId=6, role=admin, heatUnitId=0
2025-09-02T10:00:14.867+08:00  INFO 9756 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 获取工单消息成功，共找到0条待接单工单
2025-09-02T10:01:17.205+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/plans/messages?uid=6
2025-09-02T10:01:17.205+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:01:17.205+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:01:17.207+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/plans/messages?uid=6
2025-09-02T10:01:17.207+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.heating.controller.PatrolController    : 获取巡检提醒消息: uid=6
2025-09-02T10:01:17.207+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息开始: userId=6
2025-09-02T10:01:17.282+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 查询到0个用户6的待执行或已超时巡检记录
2025-09-02T10:01:17.282+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息完成，共0条消息
2025-09-02T10:01:17.288+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:01:17.288+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:01:17.288+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:01:17.290+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:01:17.291+08:00  INFO 9756 --- [http-nio-8889-exec-6] com.heating.controller.AlarmController   : 获取告警消息: uid=6, role=admin, heatUnitId=0
2025-09-02T10:01:17.291+08:00  INFO 9756 --- [http-nio-8889-exec-6] c.heating.service.impl.AlarmServiceImpl  : 开始获取告警消息列表: userId=6, role=admin, heatUnitId=0
2025-09-02T10:01:17.367+08:00  INFO 9756 --- [http-nio-8889-exec-6] c.heating.service.impl.AlarmServiceImpl  : 获取告警消息列表成功，共0条
2025-09-02T10:01:17.373+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:01:17.373+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:01:17.373+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:01:17.375+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:01:17.376+08:00  INFO 9756 --- [http-nio-8889-exec-9] com.heating.controller.FaultController   : 获取故障消息: uid=6, role=admin, heatUnitId=0
2025-09-02T10:01:17.376+08:00  INFO 9756 --- [http-nio-8889-exec-9] c.heating.service.impl.FaultServiceImpl  : 开始获取故障消息列表: userId=6, role=admin, heatUnitId=0
2025-09-02T10:01:17.458+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:01:17.458+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:01:17.458+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:01:17.462+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:01:17.462+08:00  INFO 9756 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 获取工单消息: uid=6, role=admin, heatUnitId=0
2025-09-02T10:01:17.462+08:00  INFO 9756 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 开始获取工单消息（待接单状态的工单）: userId=6, role=admin, heatUnitId=0
2025-09-02T10:01:17.502+08:00  INFO 9756 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 获取工单消息成功，共找到0条待接单工单
2025-09-02T10:01:26.352+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T10:01:26.353+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:01:26.353+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:01:26.353+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T10:01:26.353+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:01:26.353+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:01:26.355+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T10:01:26.355+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T10:01:26.355+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T10:01:26.355+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:01:26.355+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:01:26.356+08:00  INFO 9756 --- [http-nio-8889-exec-4] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-09-02T10:01:26.356+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T10:01:26.356+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T10:01:26.357+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:01:26.357+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:01:26.357+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T10:01:26.358+08:00  INFO 9756 --- [http-nio-8889-exec-8] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T10:01:26.358+08:00  INFO 9756 --- [http-nio-8889-exec-8] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T10:01:26.359+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T10:01:26.361+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T10:01:26.361+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:01:26.362+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:01:26.364+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T10:01:26.364+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:01:26.364+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:01:26.364+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T10:01:26.365+08:00  INFO 9756 --- [http-nio-8889-exec-1] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T10:01:26.365+08:00  INFO 9756 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T10:01:26.365+08:00  INFO 9756 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T10:01:26.367+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T10:01:26.368+08:00  INFO 9756 --- [http-nio-8889-exec-7] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T10:01:26.368+08:00  INFO 9756 --- [http-nio-8889-exec-7] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T10:01:26.441+08:00  INFO 9756 --- [http-nio-8889-exec-4] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T10:01:26.443+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T10:01:26.443+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:01:26.444+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:01:26.444+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T10:01:26.444+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:01:26.444+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:01:26.445+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T10:01:26.446+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T10:01:26.446+08:00  INFO 9756 --- [http-nio-8889-exec-6] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-09-02T10:01:26.446+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T10:01:26.446+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T10:01:26.446+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T10:01:26.480+08:00  INFO 9756 --- [http-nio-8889-exec-7] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=23.1°C
2025-09-02T10:01:26.481+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T10:01:26.482+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:01:26.482+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:01:26.484+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T10:01:26.492+08:00  INFO 9756 --- [http-nio-8889-exec-6] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T10:01:26.518+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=23.1°C
2025-09-02T10:01:26.641+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T10:01:26.641+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:01:26.641+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:01:26.643+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T10:01:26.670+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T10:01:26.670+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:01:26.670+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:01:26.672+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T10:01:26.672+08:00  INFO 9756 --- [http-nio-8889-exec-4] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T10:01:26.672+08:00  INFO 9756 --- [http-nio-8889-exec-4] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T10:01:27.367+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T10:01:27.367+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:01:27.367+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:01:27.369+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T10:01:27.369+08:00  INFO 9756 --- [http-nio-8889-exec-3] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T10:01:27.672+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T10:01:27.672+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:01:27.672+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:01:27.674+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T10:01:27.674+08:00  INFO 9756 --- [http-nio-8889-exec-7] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T10:01:28.087+08:00  INFO 9756 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T10:01:28.089+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T10:01:28.089+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:01:28.089+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:01:28.090+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T10:01:28.091+08:00  INFO 9756 --- [http-nio-8889-exec-6] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T10:01:28.091+08:00  INFO 9756 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T10:01:28.091+08:00  INFO 9756 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T10:01:30.671+08:00  INFO 9756 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T10:02:29.594+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/plans/messages?uid=6
2025-09-02T10:02:29.595+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:02:29.595+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:02:29.597+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/plans/messages?uid=6
2025-09-02T10:02:29.597+08:00  INFO 9756 --- [http-nio-8889-exec-4] c.heating.controller.PatrolController    : 获取巡检提醒消息: uid=6
2025-09-02T10:02:29.597+08:00  INFO 9756 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息开始: userId=6
2025-09-02T10:02:29.673+08:00  INFO 9756 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 查询到0个用户6的待执行或已超时巡检记录
2025-09-02T10:02:29.673+08:00  INFO 9756 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息完成，共0条消息
2025-09-02T10:02:29.677+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:02:29.677+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:02:29.677+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:02:29.679+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:02:29.679+08:00  INFO 9756 --- [http-nio-8889-exec-3] com.heating.controller.AlarmController   : 获取告警消息: uid=6, role=admin, heatUnitId=0
2025-09-02T10:02:29.679+08:00  INFO 9756 --- [http-nio-8889-exec-3] c.heating.service.impl.AlarmServiceImpl  : 开始获取告警消息列表: userId=6, role=admin, heatUnitId=0
2025-09-02T10:02:29.755+08:00  INFO 9756 --- [http-nio-8889-exec-3] c.heating.service.impl.AlarmServiceImpl  : 获取告警消息列表成功，共0条
2025-09-02T10:02:29.759+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:02:29.759+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:02:29.759+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:02:29.760+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:02:29.761+08:00  INFO 9756 --- [http-nio-8889-exec-7] com.heating.controller.FaultController   : 获取故障消息: uid=6, role=admin, heatUnitId=0
2025-09-02T10:02:29.761+08:00  INFO 9756 --- [http-nio-8889-exec-7] c.heating.service.impl.FaultServiceImpl  : 开始获取故障消息列表: userId=6, role=admin, heatUnitId=0
2025-09-02T10:02:29.845+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:02:29.846+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:02:29.846+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:02:29.847+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:02:29.848+08:00  INFO 9756 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 获取工单消息: uid=6, role=admin, heatUnitId=0
2025-09-02T10:02:29.848+08:00  INFO 9756 --- [http-nio-8889-exec-1] c.h.service.impl.WorkOrderServiceImpl    : 开始获取工单消息（待接单状态的工单）: userId=6, role=admin, heatUnitId=0
2025-09-02T10:02:29.922+08:00  INFO 9756 --- [http-nio-8889-exec-1] c.h.service.impl.WorkOrderServiceImpl    : 获取工单消息成功，共找到0条待接单工单
2025-09-02T10:02:31.005+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T10:02:31.005+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:02:31.005+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:02:31.008+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T10:02:31.008+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:02:31.008+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:02:31.009+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T10:02:31.010+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T10:02:31.010+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T10:02:31.010+08:00  INFO 9756 --- [http-nio-8889-exec-6] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-09-02T10:02:31.010+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T10:02:31.010+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:02:31.010+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:02:31.012+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T10:02:31.013+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:02:31.013+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:02:31.013+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T10:02:31.013+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T10:02:31.013+08:00  INFO 9756 --- [http-nio-8889-exec-10] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T10:02:31.013+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:02:31.013+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T10:02:31.013+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:02:31.014+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T10:02:31.015+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:02:31.015+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:02:31.015+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T10:02:31.015+08:00  INFO 9756 --- [http-nio-8889-exec-2] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T10:02:31.015+08:00  INFO 9756 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T10:02:31.015+08:00  INFO 9756 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T10:02:31.017+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T10:02:31.017+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T10:02:31.017+08:00  INFO 9756 --- [http-nio-8889-exec-8] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T10:02:31.017+08:00  INFO 9756 --- [http-nio-8889-exec-8] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T10:02:31.084+08:00  INFO 9756 --- [http-nio-8889-exec-6] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T10:02:31.091+08:00  INFO 9756 --- [http-nio-8889-exec-8] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=23.1°C
2025-09-02T10:02:32.029+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T10:02:32.029+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:02:32.029+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:02:32.031+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T10:02:32.031+08:00  INFO 9756 --- [http-nio-8889-exec-4] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T10:02:32.502+08:00  INFO 9756 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T10:02:34.790+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-09-02T10:02:34.791+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:02:34.791+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:02:34.792+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-09-02T10:02:34.793+08:00  INFO 9756 --- [http-nio-8889-exec-3] com.heating.controller.FaultController   : 获取故障列表: status=null, date=null, heatUnitId=0, page=1, pageSize=5
2025-09-02T10:02:34.793+08:00  INFO 9756 --- [http-nio-8889-exec-3] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-09-02T10:02:34.793+08:00  INFO 9756 --- [http-nio-8889-exec-3] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-09-02T10:02:36.319+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T10:02:36.319+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:02:36.319+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:02:36.320+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T10:02:36.321+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:02:36.321+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:02:36.321+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T10:02:36.321+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:02:36.321+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:02:36.322+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T10:02:36.322+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T10:02:36.322+08:00  INFO 9756 --- [http-nio-8889-exec-7] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-09-02T10:02:36.322+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T10:02:36.323+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T10:02:36.323+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T10:02:36.323+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:02:36.323+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:02:36.323+08:00  INFO 9756 --- [http-nio-8889-exec-6] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T10:02:36.323+08:00  INFO 9756 --- [http-nio-8889-exec-6] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T10:02:36.325+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T10:02:36.325+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T10:02:36.326+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:02:36.326+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:02:36.328+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T10:02:36.328+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T10:02:36.329+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:02:36.329+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T10:02:36.329+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T10:02:36.329+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T10:02:36.329+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:02:36.332+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T10:02:36.333+08:00  INFO 9756 --- [http-nio-8889-exec-9] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T10:02:36.333+08:00  INFO 9756 --- [http-nio-8889-exec-9] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T10:02:36.397+08:00  INFO 9756 --- [http-nio-8889-exec-7] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T10:02:36.407+08:00  INFO 9756 --- [http-nio-8889-exec-9] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=23.1°C
2025-09-02T10:02:37.146+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/plans/messages?uid=6
2025-09-02T10:02:37.146+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:02:37.146+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:02:37.149+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/plans/messages?uid=6
2025-09-02T10:02:37.149+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.heating.controller.PatrolController    : 获取巡检提醒消息: uid=6
2025-09-02T10:02:37.149+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息开始: userId=6
2025-09-02T10:02:37.227+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 查询到0个用户6的待执行或已超时巡检记录
2025-09-02T10:02:37.227+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息完成，共0条消息
2025-09-02T10:02:37.231+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:02:37.231+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:02:37.231+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:02:37.233+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:02:37.233+08:00  INFO 9756 --- [http-nio-8889-exec-4] com.heating.controller.AlarmController   : 获取告警消息: uid=6, role=admin, heatUnitId=0
2025-09-02T10:02:37.233+08:00  INFO 9756 --- [http-nio-8889-exec-4] c.heating.service.impl.AlarmServiceImpl  : 开始获取告警消息列表: userId=6, role=admin, heatUnitId=0
2025-09-02T10:02:37.271+08:00  INFO 9756 --- [http-nio-8889-exec-4] c.heating.service.impl.AlarmServiceImpl  : 获取告警消息列表成功，共0条
2025-09-02T10:02:37.275+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:02:37.275+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:02:37.275+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:02:37.277+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:02:37.278+08:00  INFO 9756 --- [http-nio-8889-exec-2] com.heating.controller.FaultController   : 获取故障消息: uid=6, role=admin, heatUnitId=0
2025-09-02T10:02:37.278+08:00  INFO 9756 --- [http-nio-8889-exec-2] c.heating.service.impl.FaultServiceImpl  : 开始获取故障消息列表: userId=6, role=admin, heatUnitId=0
2025-09-02T10:02:37.332+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T10:02:37.332+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:02:37.332+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:02:37.334+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T10:02:37.334+08:00  INFO 9756 --- [http-nio-8889-exec-3] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T10:02:37.357+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:02:37.357+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:02:37.357+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:02:37.359+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:02:37.359+08:00  INFO 9756 --- [http-nio-8889-exec-7] c.h.controller.WorkOrderController       : 获取工单消息: uid=6, role=admin, heatUnitId=0
2025-09-02T10:02:37.359+08:00  INFO 9756 --- [http-nio-8889-exec-7] c.h.service.impl.WorkOrderServiceImpl    : 开始获取工单消息（待接单状态的工单）: userId=6, role=admin, heatUnitId=0
2025-09-02T10:02:37.397+08:00  INFO 9756 --- [http-nio-8889-exec-7] c.h.service.impl.WorkOrderServiceImpl    : 获取工单消息成功，共找到0条待接单工单
2025-09-02T10:02:37.784+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T10:10:20.961+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/plans/messages?uid=6
2025-09-02T10:10:20.961+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:10:20.961+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:10:20.963+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/plans/messages?uid=6
2025-09-02T10:10:20.964+08:00  INFO 9756 --- [http-nio-8889-exec-2] c.heating.controller.PatrolController    : 获取巡检提醒消息: uid=6
2025-09-02T10:10:20.964+08:00  INFO 9756 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息开始: userId=6
2025-09-02T10:10:21.049+08:00  INFO 9756 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 查询到0个用户6的待执行或已超时巡检记录
2025-09-02T10:10:21.049+08:00  INFO 9756 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息完成，共0条消息
2025-09-02T10:10:21.056+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:10:21.056+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:10:21.056+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:10:21.058+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:10:21.058+08:00  INFO 9756 --- [http-nio-8889-exec-7] com.heating.controller.FaultController   : 获取故障消息: uid=6, role=admin, heatUnitId=0
2025-09-02T10:10:21.058+08:00  INFO 9756 --- [http-nio-8889-exec-7] c.heating.service.impl.FaultServiceImpl  : 开始获取故障消息列表: userId=6, role=admin, heatUnitId=0
2025-09-02T10:10:21.141+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:10:21.141+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:10:21.141+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:10:21.143+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:10:21.144+08:00  INFO 9756 --- [http-nio-8889-exec-3] c.h.controller.WorkOrderController       : 获取工单消息: uid=6, role=admin, heatUnitId=0
2025-09-02T10:10:21.144+08:00  INFO 9756 --- [http-nio-8889-exec-3] c.h.service.impl.WorkOrderServiceImpl    : 开始获取工单消息（待接单状态的工单）: userId=6, role=admin, heatUnitId=0
2025-09-02T10:10:21.217+08:00  INFO 9756 --- [http-nio-8889-exec-3] c.h.service.impl.WorkOrderServiceImpl    : 获取工单消息成功，共找到0条待接单工单
2025-09-02T10:10:23.412+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/plans/messages?uid=6
2025-09-02T10:10:23.413+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:10:23.413+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:10:23.415+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/plans/messages?uid=6
2025-09-02T10:10:23.415+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.heating.controller.PatrolController    : 获取巡检提醒消息: uid=6
2025-09-02T10:10:23.415+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息开始: userId=6
2025-09-02T10:10:23.490+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 查询到0个用户6的待执行或已超时巡检记录
2025-09-02T10:10:23.490+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息完成，共0条消息
2025-09-02T10:10:23.496+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:10:23.496+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:10:23.496+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:10:23.498+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:10:23.498+08:00  INFO 9756 --- [http-nio-8889-exec-9] com.heating.controller.AlarmController   : 获取告警消息: uid=6, role=admin, heatUnitId=0
2025-09-02T10:10:23.498+08:00  INFO 9756 --- [http-nio-8889-exec-9] c.heating.service.impl.AlarmServiceImpl  : 开始获取告警消息列表: userId=6, role=admin, heatUnitId=0
2025-09-02T10:10:23.574+08:00  INFO 9756 --- [http-nio-8889-exec-9] c.heating.service.impl.AlarmServiceImpl  : 获取告警消息列表成功，共0条
2025-09-02T10:10:23.579+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:10:23.580+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:10:23.580+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:10:23.581+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:10:23.582+08:00  INFO 9756 --- [http-nio-8889-exec-8] com.heating.controller.FaultController   : 获取故障消息: uid=6, role=admin, heatUnitId=0
2025-09-02T10:10:23.582+08:00  INFO 9756 --- [http-nio-8889-exec-8] c.heating.service.impl.FaultServiceImpl  : 开始获取故障消息列表: userId=6, role=admin, heatUnitId=0
2025-09-02T10:10:23.663+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:10:23.664+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:10:23.664+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:10:23.666+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:10:23.666+08:00  INFO 9756 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 获取工单消息: uid=6, role=admin, heatUnitId=0
2025-09-02T10:10:23.666+08:00  INFO 9756 --- [http-nio-8889-exec-1] c.h.service.impl.WorkOrderServiceImpl    : 开始获取工单消息（待接单状态的工单）: userId=6, role=admin, heatUnitId=0
2025-09-02T10:10:23.740+08:00  INFO 9756 --- [http-nio-8889-exec-1] c.h.service.impl.WorkOrderServiceImpl    : 获取工单消息成功，共找到0条待接单工单
2025-09-02T10:10:53.410+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T10:10:53.411+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:10:53.411+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:10:53.411+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T10:10:53.411+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:10:53.412+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:10:53.412+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T10:10:53.412+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:10:53.412+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T10:10:53.412+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:10:53.413+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-09-02T10:10:53.413+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T10:10:53.413+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T10:10:53.414+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T10:10:53.414+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:10:53.414+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:10:53.414+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T10:10:53.415+08:00  INFO 9756 --- [http-nio-8889-exec-6] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T10:10:53.415+08:00  INFO 9756 --- [http-nio-8889-exec-6] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T10:10:53.418+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T10:10:53.418+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T10:10:53.418+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:10:53.419+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:10:53.421+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T10:10:53.421+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T10:10:53.421+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:10:53.421+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:10:53.421+08:00  INFO 9756 --- [http-nio-8889-exec-7] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T10:10:53.421+08:00  INFO 9756 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T10:10:53.421+08:00  INFO 9756 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T10:10:53.424+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T10:10:53.425+08:00  INFO 9756 --- [http-nio-8889-exec-3] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T10:10:53.425+08:00  INFO 9756 --- [http-nio-8889-exec-3] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T10:10:53.488+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T10:10:53.490+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T10:10:53.490+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:10:53.490+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:10:53.492+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T10:10:53.493+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T10:10:53.493+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T10:10:53.497+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T10:10:53.497+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:10:53.498+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:10:53.499+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T10:10:53.500+08:00  INFO 9756 --- [http-nio-8889-exec-9] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-09-02T10:10:53.500+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T10:10:53.524+08:00  INFO 9756 --- [http-nio-8889-exec-3] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=23.2°C
2025-09-02T10:10:53.527+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T10:10:53.527+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:10:53.527+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:10:53.529+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T10:10:53.532+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=23.2°C
2025-09-02T10:10:53.538+08:00  INFO 9756 --- [http-nio-8889-exec-9] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T10:10:53.670+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T10:10:53.671+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:10:53.671+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:10:53.672+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T10:10:53.738+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T10:10:53.738+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:10:53.738+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:10:53.740+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T10:10:53.740+08:00  INFO 9756 --- [http-nio-8889-exec-10] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T10:10:53.740+08:00  INFO 9756 --- [http-nio-8889-exec-10] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T10:10:54.439+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T10:10:54.439+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:10:54.439+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:10:54.441+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T10:10:54.441+08:00  INFO 9756 --- [http-nio-8889-exec-2] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T10:10:54.740+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T10:10:54.740+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:10:54.741+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:10:54.743+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T10:10:54.743+08:00  INFO 9756 --- [http-nio-8889-exec-3] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T10:10:54.923+08:00  INFO 9756 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T10:10:54.925+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T10:10:54.925+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:10:54.926+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:10:54.928+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T10:10:54.928+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T10:10:54.929+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T10:10:54.929+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T10:10:56.497+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T10:11:26.377+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/plans/messages?uid=6
2025-09-02T10:11:26.377+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:11:26.377+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:11:26.380+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/plans/messages?uid=6
2025-09-02T10:11:26.380+08:00  INFO 9756 --- [http-nio-8889-exec-9] c.heating.controller.PatrolController    : 获取巡检提醒消息: uid=6
2025-09-02T10:11:26.380+08:00  INFO 9756 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息开始: userId=6
2025-09-02T10:11:26.455+08:00  INFO 9756 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 查询到0个用户6的待执行或已超时巡检记录
2025-09-02T10:11:26.455+08:00  INFO 9756 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息完成，共0条消息
2025-09-02T10:11:26.459+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:11:26.460+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:11:26.460+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:11:26.461+08:00 DEBUG 9756 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:11:26.462+08:00  INFO 9756 --- [http-nio-8889-exec-8] com.heating.controller.AlarmController   : 获取告警消息: uid=6, role=admin, heatUnitId=0
2025-09-02T10:11:26.462+08:00  INFO 9756 --- [http-nio-8889-exec-8] c.heating.service.impl.AlarmServiceImpl  : 开始获取告警消息列表: userId=6, role=admin, heatUnitId=0
2025-09-02T10:11:26.538+08:00  INFO 9756 --- [http-nio-8889-exec-8] c.heating.service.impl.AlarmServiceImpl  : 获取告警消息列表成功，共0条
2025-09-02T10:11:26.542+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:11:26.542+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:11:26.542+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:11:26.543+08:00 DEBUG 9756 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:11:26.544+08:00  INFO 9756 --- [http-nio-8889-exec-4] com.heating.controller.FaultController   : 获取故障消息: uid=6, role=admin, heatUnitId=0
2025-09-02T10:11:26.544+08:00  INFO 9756 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : 开始获取故障消息列表: userId=6, role=admin, heatUnitId=0
2025-09-02T10:11:26.623+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:11:26.624+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:11:26.624+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:11:26.626+08:00 DEBUG 9756 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-09-02T10:11:26.626+08:00  INFO 9756 --- [http-nio-8889-exec-6] c.h.controller.WorkOrderController       : 获取工单消息: uid=6, role=admin, heatUnitId=0
2025-09-02T10:11:26.626+08:00  INFO 9756 --- [http-nio-8889-exec-6] c.h.service.impl.WorkOrderServiceImpl    : 开始获取工单消息（待接单状态的工单）: userId=6, role=admin, heatUnitId=0
2025-09-02T10:11:26.701+08:00  INFO 9756 --- [http-nio-8889-exec-6] c.h.service.impl.WorkOrderServiceImpl    : 获取工单消息成功，共找到0条待接单工单
2025-09-02T10:11:27.059+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T10:11:27.061+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-09-02T10:11:27.061+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:11:27.061+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:11:27.061+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:11:27.061+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:11:27.062+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-09-02T10:11:27.062+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-09-02T10:11:27.063+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:11:27.063+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:11:27.063+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:11:27.063+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:11:27.064+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T10:11:27.066+08:00 DEBUG 9756 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-09-02T10:11:27.067+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:11:27.067+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:11:27.067+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing POST /api/weather/latest
2025-09-02T10:11:27.065+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-09-02T10:11:27.067+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:11:27.067+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:11:27.066+08:00 DEBUG 9756 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-09-02T10:11:27.068+08:00 DEBUG 9756 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-09-02T10:11:27.068+08:00 DEBUG 9756 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured POST /api/weather/latest
2025-09-02T10:11:27.069+08:00  INFO 9756 --- [http-nio-8889-exec-3] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-09-02T10:11:27.069+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.heating.controller.WeatherController   : 接收获取气象数据请求，地点: 西安
2025-09-02T10:11:27.069+08:00  INFO 9756 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-09-02T10:11:27.069+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.h.service.impl.WeatherServiceImpl      : 获取气象数据，地点: 西安
2025-09-02T10:11:27.069+08:00  INFO 9756 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-09-02T10:11:27.066+08:00 DEBUG 9756 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-09-02T10:11:27.069+08:00  INFO 9756 --- [http-nio-8889-exec-2] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-09-02T10:11:27.069+08:00  INFO 9756 --- [http-nio-8889-exec-2] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-09-02T10:11:27.070+08:00  INFO 9756 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-09-02T10:11:27.070+08:00 DEBUG 9756 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-09-02T10:11:27.107+08:00  INFO 9756 --- [http-nio-8889-exec-5] c.h.service.impl.WeatherServiceImpl      : 成功获取气象数据: 气象站=西安, 温度=23.2°C
2025-09-02T10:11:27.144+08:00  INFO 9756 --- [http-nio-8889-exec-1] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-09-02T10:11:28.070+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-09-02T10:11:28.070+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-09-02T10:11:28.070+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-09-02T10:11:28.072+08:00 DEBUG 9756 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-09-02T10:11:28.072+08:00  INFO 9756 --- [http-nio-8889-exec-9] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-09-02T10:11:28.561+08:00  INFO 9756 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-09-02T10:11:32.338+08:00  INFO 9756 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-02T10:11:32.340+08:00  INFO 9756 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown initiated...
2025-09-02T10:11:32.607+08:00  INFO 9756 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown completed.
