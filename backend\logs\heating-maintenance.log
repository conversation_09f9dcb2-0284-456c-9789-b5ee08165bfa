2025-09-02T09:00:44.366+08:00  INFO 1248 --- [main] com.heating.HeatingApplication           : Starting HeatingApplication using Java 21.0.7 with PID 1248 (E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend\target\classes started by A in E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend)
2025-09-02T09:00:44.394+08:00 DEBUG 1248 --- [main] com.heating.HeatingApplication           : Running with Spring Boot v3.2.3, Spring v6.1.4
2025-09-02T09:00:44.395+08:00  INFO 1248 --- [main] com.heating.HeatingApplication           : No active profile set, falling back to 1 default profile: "default"
2025-09-02T09:00:45.020+08:00  INFO 1248 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-02T09:00:45.170+08:00  INFO 1248 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 143 ms. Found 35 JPA repository interfaces.
2025-09-02T09:00:45.728+08:00  INFO 1248 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8889 (http)
2025-09-02T09:00:45.737+08:00  INFO 1248 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-02T09:00:45.737+08:00  INFO 1248 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-09-02T09:00:45.786+08:00  INFO 1248 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-02T09:00:45.786+08:00  INFO 1248 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1356 ms
2025-09-02T09:00:45.842+08:00 DEBUG 1248 --- [main] c.h.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-09-02T09:00:45.938+08:00  INFO 1248 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-02T09:00:45.988+08:00  INFO 1248 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-02T09:00:46.016+08:00  INFO 1248 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-02T09:00:46.101+08:00  INFO 1248 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Starting...
2025-09-02T09:00:47.639+08:00  INFO 1248 --- [main] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@1e258d3b
2025-09-02T09:00:47.641+08:00  INFO 1248 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Start completed.
2025-09-02T09:00:47.964+08:00  INFO 1248 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-02T09:00:49.735+08:00  INFO 1248 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-02T09:00:52.703+08:00  INFO 1248 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-02T09:00:53.438+08:00  INFO 1248 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-02T09:00:55.524+08:00  INFO 1248 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源跨域访问: /uploads/**
2025-09-02T09:00:55.609+08:00  INFO 1248 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源映射: /uploads/** -> file:/home/<USER>/work/web/uploads/
2025-09-02T09:00:55.648+08:00  INFO 1248 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3c913e84, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2de3095c, org.springframework.security.web.context.SecurityContextHolderFilter@4f50d590, org.springframework.security.web.header.HeaderWriterFilter@1c25b58d, org.springframework.web.filter.CorsFilter@323fe1f5, org.springframework.security.web.authentication.logout.LogoutFilter@3fa87583, com.heating.filter.JwtAuthenticationFilter@28be7fec, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4e59830b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@487d9291, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@57d5b677, org.springframework.security.web.access.ExceptionTranslationFilter@1d0ed7d8, org.springframework.security.web.access.intercept.AuthorizationFilter@528a643c]
2025-09-02T09:00:55.905+08:00  INFO 1248 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8889 (http) with context path ''
2025-09-02T09:00:55.913+08:00  INFO 1248 --- [main] com.heating.HeatingApplication           : Started HeatingApplication in 11.888 seconds (process running for 12.546)
