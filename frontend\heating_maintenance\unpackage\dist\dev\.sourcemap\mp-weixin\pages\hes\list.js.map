{"version": 3, "file": "list.js", "sources": ["pages/hes/list.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaGVzL2xpc3QudnVl"], "sourcesContent": ["<template>\r\n  <BaseTabBar>\r\n    <view class=\"hes-list-container\">\r\n      <!-- 搜索栏 -->\r\n      <view class=\"search-bar\">\r\n        <view class=\"search-input-wrapper\">\r\n          <text class=\"iconfont icon-search\"></text>\r\n          <input\r\n            type=\"text\"\r\n            placeholder=\"搜索换热站名称/编号\"\r\n            v-model=\"searchKeyword\"\r\n            confirm-type=\"search\"\r\n            @confirm=\"handleSearch\"\r\n          />\r\n          <text\r\n            class=\"iconfont icon-clear\"\r\n            v-if=\"searchKeyword\"\r\n            @click=\"clearSearch\"\r\n          ></text>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 状态筛选 -->\r\n      <view class=\"filter-tabs\">\r\n        <view\r\n          class=\"filter-tab\"\r\n          v-for=\"(tab, index) in statusTabs\"\r\n          :key=\"index\"\r\n          :class=\"{ active: currentTab === tab.value }\"\r\n          @click=\"switchTab(tab.value)\"\r\n        >\r\n          {{ tab.label }}\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 换热站列表 - 新设计 -->\r\n      <view class=\"hes-modern-list\">\r\n        <view\r\n          class=\"hes-card\"\r\n          v-for=\"(station, index) in stationList\"\r\n          :key=\"index\"\r\n          :class=\"getCardColorClass(station)\"\r\n          @click=\"navigateToDetail(station.id)\"\r\n        >\r\n          <!-- 站点名称和时间信息 -->\r\n          <view class=\"card-header\">\r\n            <view class=\"station-name\">{{ station.name }} [{{ station.id }}]</view>\r\n            <view class=\"connection-time\">{{\r\n              formatDateTime(station.last_connect_time)\r\n            }}</view>\r\n          </view>\r\n\r\n          <!-- 运行模式 -->\r\n          <view class=\"operation-mode\">\r\n            控制模式：{{ station.operation_mode === \"fixed\" ? \"定频\" : \"变频\" }}\r\n          </view>\r\n\r\n          <!-- 温度信息 - 显示在一行 -->\r\n          <view class=\"temperature-info\">\r\n            <view class=\"network-row\">\r\n              <text class=\"network-label\">一次网:</text>\r\n              <text class=\"temp-value\"\r\n                >{{ station.primary_supply_temp || \"--\" }}/{{\r\n                  station.primary_return_temp || \"--\"\r\n                }}°C</text\r\n              >\r\n              <text class=\"network-label second-label\">二次网:</text>\r\n              <text class=\"temp-value\"\r\n                >{{ station.secondary_supply_temp || \"--\" }}/{{\r\n                  station.secondary_return_temp || \"--\"\r\n                }}°C</text\r\n              >\r\n            </view>\r\n          </view>\r\n\r\n          <!-- 操作按钮 -->\r\n          <view class=\"card-actions\">\r\n            <view class=\"status-badge\" :class=\"station.status\">\r\n              {{ getStatusText(station.status) }}\r\n            </view>\r\n            <view class=\"action-buttons\">\r\n              <view\r\n                class=\"action-btn control\"\r\n                @click.stop=\"navigateToControl(station.id)\"\r\n              >\r\n                控制\r\n              </view>\r\n              <view class=\"action-btn detail\" @click.stop=\"navigateToDetail(station.id)\">\r\n                详情\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 加载更多 -->\r\n      <view class=\"load-more\" v-if=\"hasMore\" @click=\"loadMore\">加载更多</view>\r\n      <view class=\"no-more\" v-else>没有更多数据了</view>\r\n    </view>\r\n  </BaseTabBar>\r\n</template>\r\n\r\n<script>\r\nimport { heatingStationApi } from \"@/utils/api.js\";\r\nimport BaseTabBar from \"@/components/BaseTabBar.vue\";\r\n\r\nexport default {\r\n  components: {\r\n    BaseTabBar,\r\n  },\r\n  data() {\r\n    return {\r\n      searchKeyword: \"\",\r\n      currentTab: \"all\",\r\n      statusTabs: [\r\n        { label: \"全部\", value: \"all\" },\r\n        { label: \"正常\", value: \"online\" },\r\n        { label: \"异常\", value: \"warning\" },\r\n        { label: \"故障\", value: \"fault\" },\r\n      ],\r\n      stationList: [],\r\n      page: 1,\r\n      pageSize: 10,\r\n      hasMore: true,\r\n      loading: false,\r\n    };\r\n  },\r\n  onLoad() {\r\n    this.loadStationList();\r\n  },\r\n  methods: {\r\n    // 加载换热站列表\r\n    loadStationList() {\r\n      if (this.loading) return;\r\n      this.loading = true;\r\n\r\n      // 构建请求参数\r\n      const params = {\r\n        status: this.currentTab !== \"all\" ? this.currentTab : \"\", // online/offline/fault，可选\r\n        keyword: this.searchKeyword || \"\", // 搜索关键词，可选\r\n      };\r\n\r\n      console.log(\"加载换热站列表，参数:\", params);\r\n\r\n      // 调用API获取换热站列表\r\n      heatingStationApi\r\n        .getList(params)\r\n        .then((res) => {\r\n          console.log(\"换热站列表获取成功:\", res);\r\n          if (res && res.data && res.data.list) {\r\n            // 处理数据\r\n            const processedList = res.data.list.map((station) => {\r\n              // 为模拟设计添加示例数据（实际项目中应删除这些模拟数据，使用真实API返回的数据）\r\n              return {\r\n                ...station,\r\n                // 如果API没有返回这些字段，添加示例数据\r\n                last_connect_time: station.last_connect_time || new Date().toISOString(),\r\n                operation_mode:\r\n                  station.operation_mode || (Math.random() > 0.5 ? \"fixed\" : \"variable\"),\r\n                primary_supply_temp:\r\n                  station.primary_supply_temp || Math.floor(Math.random() * 15) + 70,\r\n                primary_return_temp:\r\n                  station.primary_return_temp || Math.floor(Math.random() * 10) + 50,\r\n                secondary_supply_temp:\r\n                  station.secondary_supply_temp || Math.floor(Math.random() * 10) + 50,\r\n                secondary_return_temp:\r\n                  station.secondary_return_temp || Math.floor(Math.random() * 10) + 30,\r\n                enabled: station.enabled !== undefined ? station.enabled : true,\r\n              };\r\n            });\r\n\r\n            if (this.page === 1) {\r\n              // 首次加载或刷新\r\n              this.stationList = processedList;\r\n            } else {\r\n              // 加载更多\r\n              this.stationList = [...this.stationList, ...processedList];\r\n            }\r\n\r\n            // 判断是否还有更多数据\r\n            this.hasMore = res.data.list.length >= this.pageSize;\r\n          } else {\r\n            // 没有数据或格式不正确\r\n            if (this.page === 1) {\r\n              this.stationList = [];\r\n            }\r\n            this.hasMore = false;\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          console.error(\"加载换热站列表失败:\", err);\r\n          uni.showToast({\r\n            title: \"加载失败，请重试\",\r\n            icon: \"none\",\r\n          });\r\n        })\r\n        .finally(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n\r\n    // 格式化日期时间\r\n    formatDateTime(dateTimeStr) {\r\n      if (!dateTimeStr) return \"暂无数据\";\r\n      try {\r\n        const date = new Date(dateTimeStr);\r\n        const year = date.getFullYear();\r\n        const month = String(date.getMonth() + 1).padStart(2, \"0\");\r\n        const day = String(date.getDate()).padStart(2, \"0\");\r\n        const hours = String(date.getHours()).padStart(2, \"0\");\r\n        const minutes = String(date.getMinutes()).padStart(2, \"0\");\r\n        const seconds = String(date.getSeconds()).padStart(2, \"0\");\r\n\r\n        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n      } catch (e) {\r\n        console.error(\"日期格式化错误:\", e);\r\n        return dateTimeStr || \"暂无数据\";\r\n      }\r\n    },\r\n\r\n    // 获取卡片颜色类\r\n    getCardColorClass(station) {\r\n      // 根据状态返回不同颜色\r\n      if (!station.enabled) return \"gray-card\"; // 未启用的站点显示为灰色\r\n      if (station.status === \"online\") return \"green-card\"; // 在线显示为绿色\r\n      if (station.status === \"offline\") return \"yellow-card\"; // 离线显示为黄色\r\n      return \"blue-card\"; // 其他状态显示为蓝色\r\n    },\r\n\r\n    // 搜索处理\r\n    handleSearch() {\r\n      console.log(`搜索关键词: ${this.searchKeyword}`);\r\n      this.page = 1; // 重置页码\r\n      this.loadStationList();\r\n    },\r\n\r\n    // 清除搜索\r\n    clearSearch() {\r\n      this.searchKeyword = \"\";\r\n      this.page = 1; // 重置页码\r\n      this.loadStationList();\r\n    },\r\n\r\n    // 切换状态标签\r\n    switchTab(tab) {\r\n      if (this.currentTab === tab) return;\r\n      this.currentTab = tab;\r\n      this.page = 1; // 重置页码\r\n      this.loadStationList();\r\n      console.log(`切换到标签: ${tab}`);\r\n    },\r\n\r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        online: \"在线\",\r\n        offline: \"离线\",\r\n        warning: \"异常\",\r\n        fault: \"故障\",\r\n      };\r\n      return statusMap[status] || \"未知\";\r\n    },\r\n\r\n    // 加载更多\r\n    loadMore() {\r\n      if (this.loading || !this.hasMore) return;\r\n      this.page++;\r\n      this.loadStationList();\r\n    },\r\n\r\n    // 导航到详情页\r\n    navigateToDetail(id) {\r\n      // 显示加载中\r\n      uni.showLoading({\r\n        title: \"加载详情...\",\r\n        mask: true,\r\n      });\r\n\r\n      // 调用API获取换热站详情\r\n      heatingStationApi\r\n        .getDetail(id)\r\n        .then((res) => {\r\n          console.log(\"换热站详情数据:\", res);\r\n          if (res.code === 200) {\r\n            // 成功获取数据后跳转到详情页，并将详情数据传递过去\r\n            uni.navigateTo({\r\n              url: `/pages/hes/detail?id=${id}`,\r\n              success: function (navigateRes) {\r\n                // 传递数据给打开的页面\r\n                navigateRes.eventChannel.emit(\"acceptStationDetail\", res.data);\r\n              },\r\n            });\r\n          } else {\r\n            uni.showToast({\r\n              title: res.message || \"获取详情失败\",\r\n              icon: \"none\",\r\n            });\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          console.error(\"获取换热站详情失败:\", err);\r\n          uni.showToast({\r\n            title: \"获取详情失败，请重试\",\r\n            icon: \"none\",\r\n          });\r\n        })\r\n        .finally(() => {\r\n          uni.hideLoading();\r\n        });\r\n    },\r\n\r\n    // 导航到控制页\r\n    navigateToControl(id) {\r\n      uni.navigateTo({\r\n        url: `/pages/hes/control?id=${id}`,\r\n      });\r\n    },\r\n\r\n    // 导航到告警页\r\n    navigateToAlarm(id) {\r\n      uni.navigateTo({\r\n        url: `/pages/hes/alarms?id=${id}`,\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.hes-list-container {\r\n  padding: 20rpx;\r\n  box-sizing: border-box;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n.search-bar {\r\n  margin-bottom: 20rpx;\r\n\r\n  .search-input-wrapper {\r\n    background-color: #fff;\r\n    border-radius: 12rpx;\r\n    padding: 16rpx 20rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\r\n\r\n    input {\r\n      flex: 1;\r\n      height: 40rpx;\r\n      font-size: 28rpx;\r\n      margin: 0 10rpx;\r\n    }\r\n\r\n    .iconfont {\r\n      font-size: 32rpx;\r\n      color: #999;\r\n    }\r\n  }\r\n}\r\n\r\n.filter-tabs {\r\n  display: flex;\r\n  background-color: #fff;\r\n  border-radius: 12rpx;\r\n  margin-bottom: 20rpx;\r\n  padding: 4rpx;\r\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\r\n\r\n  .filter-tab {\r\n    flex: 1;\r\n    text-align: center;\r\n    padding: 16rpx 0;\r\n    font-size: 28rpx;\r\n    position: relative;\r\n    color: #666;\r\n    transition: all 0.3s;\r\n    border-radius: 8rpx;\r\n\r\n    &.active {\r\n      color: #fff;\r\n      background-color: #1890ff;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n}\r\n\r\n// 卡片列表样式 - 按需求调整\r\n.hes-modern-list {\r\n  .hes-card {\r\n    border-radius: 12rpx;\r\n    padding: 20rpx 24rpx;\r\n    margin-bottom: 20rpx;\r\n    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);\r\n    position: relative;\r\n    overflow: hidden;\r\n\r\n    // 颜色样式按图示调整\r\n    &.yellow-card {\r\n      background-color: #ffd466;\r\n      color: #333;\r\n    }\r\n\r\n    &.green-card {\r\n      background-color: #2dcea3;\r\n      color: #fff;\r\n    }\r\n\r\n    &.blue-card {\r\n      background-color: #4fb5ee;\r\n      color: #fff;\r\n    }\r\n\r\n    &.gray-card {\r\n      background-color: #a0a0a0;\r\n      color: #fff;\r\n    }\r\n\r\n    .card-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 12rpx;\r\n\r\n      .station-name {\r\n        font-size: 32rpx;\r\n        font-weight: 600;\r\n        max-width: 60%;\r\n        white-space: nowrap;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n      }\r\n\r\n      .connection-time {\r\n        font-size: 24rpx;\r\n        opacity: 0.9;\r\n      }\r\n    }\r\n\r\n    .operation-mode {\r\n      font-size: 28rpx;\r\n      margin-bottom: 12rpx;\r\n    }\r\n\r\n    .temperature-info {\r\n      margin-bottom: 16rpx;\r\n\r\n      .network-row {\r\n        display: flex;\r\n        align-items: center;\r\n        flex-wrap: wrap;\r\n\r\n        .network-label {\r\n          font-size: 28rpx;\r\n          min-width: 90rpx;\r\n        }\r\n\r\n        .temp-value {\r\n          font-size: 28rpx;\r\n          font-weight: 500;\r\n          margin-right: 20rpx;\r\n        }\r\n\r\n        .second-label {\r\n          margin-left: 10rpx;\r\n        }\r\n      }\r\n    }\r\n\r\n    .card-actions {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n\r\n      .status-badge {\r\n        padding: 6rpx 20rpx;\r\n        border-radius: 30rpx;\r\n        font-size: 24rpx;\r\n        background-color: rgba(255, 255, 255, 0.25);\r\n      }\r\n\r\n      .action-buttons {\r\n        display: flex;\r\n\r\n        .action-btn {\r\n          padding: 6rpx 30rpx;\r\n          margin-left: 16rpx;\r\n          font-size: 26rpx;\r\n          border-radius: 30rpx;\r\n          background-color: rgba(255, 255, 255, 0.25);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.load-more,\r\n.no-more {\r\n  text-align: center;\r\n  padding: 30rpx 0;\r\n  font-size: 26rpx;\r\n  color: #999;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/hes/list.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "heatingStationApi"], "mappings": ";;;AAwGA,mBAAmB,MAAW;AAE9B,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,YAAY;AAAA,QACV,EAAE,OAAO,MAAM,OAAO,MAAO;AAAA,QAC7B,EAAE,OAAO,MAAM,OAAO,SAAU;AAAA,QAChC,EAAE,OAAO,MAAM,OAAO,UAAW;AAAA,QACjC,EAAE,OAAO,MAAM,OAAO,QAAS;AAAA,MAChC;AAAA,MACD,aAAa,CAAE;AAAA,MACf,MAAM;AAAA,MACN,UAAU;AAAA,MACV,SAAS;AAAA,MACT,SAAS;AAAA;EAEZ;AAAA,EACD,SAAS;AACP,SAAK,gBAAe;AAAA,EACrB;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,kBAAkB;AAChB,UAAI,KAAK;AAAS;AAClB,WAAK,UAAU;AAGf,YAAM,SAAS;AAAA,QACb,QAAQ,KAAK,eAAe,QAAQ,KAAK,aAAa;AAAA;AAAA,QACtD,SAAS,KAAK,iBAAiB;AAAA;AAAA;AAGjCA,oBAAA,MAAA,MAAA,OAAA,6BAAY,eAAe,MAAM;AAGjCC,gBAAgB,kBACb,QAAQ,MAAM,EACd,KAAK,CAAC,QAAQ;AACbD,sBAAA,MAAA,MAAA,OAAA,6BAAY,cAAc,GAAG;AAC7B,YAAI,OAAO,IAAI,QAAQ,IAAI,KAAK,MAAM;AAEpC,gBAAM,gBAAgB,IAAI,KAAK,KAAK,IAAI,CAAC,YAAY;AAEnD,mBAAO;AAAA,cACL,GAAG;AAAA;AAAA,cAEH,mBAAmB,QAAQ,sBAAqB,oBAAI,KAAI,GAAG,YAAa;AAAA,cACxE,gBACE,QAAQ,mBAAmB,KAAK,OAAS,IAAE,MAAM,UAAU;AAAA,cAC7D,qBACE,QAAQ,uBAAuB,KAAK,MAAM,KAAK,OAAO,IAAI,EAAE,IAAI;AAAA,cAClE,qBACE,QAAQ,uBAAuB,KAAK,MAAM,KAAK,OAAO,IAAI,EAAE,IAAI;AAAA,cAClE,uBACE,QAAQ,yBAAyB,KAAK,MAAM,KAAK,OAAO,IAAI,EAAE,IAAI;AAAA,cACpE,uBACE,QAAQ,yBAAyB,KAAK,MAAM,KAAK,OAAO,IAAI,EAAE,IAAI;AAAA,cACpE,SAAS,QAAQ,YAAY,SAAY,QAAQ,UAAU;AAAA;UAE/D,CAAC;AAED,cAAI,KAAK,SAAS,GAAG;AAEnB,iBAAK,cAAc;AAAA,iBACd;AAEL,iBAAK,cAAc,CAAC,GAAG,KAAK,aAAa,GAAG,aAAa;AAAA,UAC3D;AAGA,eAAK,UAAU,IAAI,KAAK,KAAK,UAAU,KAAK;AAAA,eACvC;AAEL,cAAI,KAAK,SAAS,GAAG;AACnB,iBAAK,cAAc;UACrB;AACA,eAAK,UAAU;AAAA,QACjB;AAAA,OACD,EACA,MAAM,CAAC,QAAQ;AACdA,sBAAA,MAAA,MAAA,SAAA,6BAAc,cAAc,GAAG;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,OACF,EACA,QAAQ,MAAM;AACb,aAAK,UAAU;AAAA,MACjB,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,eAAe,aAAa;AAC1B,UAAI,CAAC;AAAa,eAAO;AACzB,UAAI;AACF,cAAM,OAAO,IAAI,KAAK,WAAW;AACjC,cAAM,OAAO,KAAK;AAClB,cAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,cAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,cAAM,QAAQ,OAAO,KAAK,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AACrD,cAAM,UAAU,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACzD,cAAM,UAAU,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AAEzD,eAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,OAAO,IAAI,OAAO;AAAA,MAC/D,SAAS,GAAG;AACVA,sBAAc,MAAA,MAAA,SAAA,6BAAA,YAAY,CAAC;AAC3B,eAAO,eAAe;AAAA,MACxB;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB,SAAS;AAEzB,UAAI,CAAC,QAAQ;AAAS,eAAO;AAC7B,UAAI,QAAQ,WAAW;AAAU,eAAO;AACxC,UAAI,QAAQ,WAAW;AAAW,eAAO;AACzC,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,eAAe;AACbA,oEAAY,UAAU,KAAK,aAAa,EAAE;AAC1C,WAAK,OAAO;AACZ,WAAK,gBAAe;AAAA,IACrB;AAAA;AAAA,IAGD,cAAc;AACZ,WAAK,gBAAgB;AACrB,WAAK,OAAO;AACZ,WAAK,gBAAe;AAAA,IACrB;AAAA;AAAA,IAGD,UAAU,KAAK;AACb,UAAI,KAAK,eAAe;AAAK;AAC7B,WAAK,aAAa;AAClB,WAAK,OAAO;AACZ,WAAK,gBAAe;AACpBA,oBAAY,MAAA,MAAA,OAAA,6BAAA,UAAU,GAAG,EAAE;AAAA,IAC5B;AAAA;AAAA,IAGD,cAAc,QAAQ;AACpB,YAAM,YAAY;AAAA,QAChB,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,SAAS;AAAA,QACT,OAAO;AAAA;AAET,aAAO,UAAU,MAAM,KAAK;AAAA,IAC7B;AAAA;AAAA,IAGD,WAAW;AACT,UAAI,KAAK,WAAW,CAAC,KAAK;AAAS;AACnC,WAAK;AACL,WAAK,gBAAe;AAAA,IACrB;AAAA;AAAA,IAGD,iBAAiB,IAAI;AAEnBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAGDC,gBAAgB,kBACb,UAAU,EAAE,EACZ,KAAK,CAAC,QAAQ;AACbD,sBAAY,MAAA,MAAA,OAAA,6BAAA,YAAY,GAAG;AAC3B,YAAI,IAAI,SAAS,KAAK;AAEpBA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK,wBAAwB,EAAE;AAAA,YAC/B,SAAS,SAAU,aAAa;AAE9B,0BAAY,aAAa,KAAK,uBAAuB,IAAI,IAAI;AAAA,YAC9D;AAAA,UACH,CAAC;AAAA,eACI;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,IAAI,WAAW;AAAA,YACtB,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,OACD,EACA,MAAM,CAAC,QAAQ;AACdA,sBAAA,MAAA,MAAA,SAAA,6BAAc,cAAc,GAAG;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,OACF,EACA,QAAQ,MAAM;AACbA,sBAAG,MAAC,YAAW;AAAA,MACjB,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,kBAAkB,IAAI;AACpBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,yBAAyB,EAAE;AAAA,MAClC,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB,IAAI;AAClBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,wBAAwB,EAAE;AAAA,MACjC,CAAC;AAAA,IACF;AAAA,EACF;AACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpUA,GAAG,WAAW,eAAe;"}