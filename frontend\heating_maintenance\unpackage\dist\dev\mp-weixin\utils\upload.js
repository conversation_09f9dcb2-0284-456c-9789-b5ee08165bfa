"use strict";
const common_vendor = require("../common/vendor.js");
const utils_auth = require("./auth.js");
const BASE_URL = "http://43.139.65.175:8889";
const UPLOAD_IMAGE_URL = BASE_URL + "/api/upload/uploadImage/app";
const UPLOAD_VIDEO_URL = BASE_URL + "/api/upload/uploadVideo/app";
const uploadUtils = {
  /**
   * 上传图片到服务器
   * @param {String} filePath - 本地图片路径
   * @returns {Promise} - 返回上传结果，包含服务器文件路径
   */
  uploadImage(filePath) {
    return new Promise((resolve, reject) => {
      common_vendor.index.__f__("log", "at utils/upload.js:21", "开始上传图片:", filePath);
      common_vendor.index.__f__("log", "at utils/upload.js:22", "上传URL:", UPLOAD_IMAGE_URL);
      let token = utils_auth.getToken();
      if (token && !token.startsWith("Bearer ")) {
        token = "Bearer " + token;
      }
      common_vendor.index.uploadFile({
        url: UPLOAD_IMAGE_URL,
        filePath,
        name: "file",
        header: {
          "Authorization": token
        },
        success: (res) => {
          common_vendor.index.__f__("log", "at utils/upload.js:38", "图片上传响应:", res);
          try {
            const data = JSON.parse(res.data);
            if (data.code === 200) {
              resolve(data.data);
            } else {
              reject(new Error(data.message || "上传图片失败"));
            }
          } catch (e) {
            reject(new Error("解析上传响应失败"));
          }
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at utils/upload.js:56", "图片上传请求失败:", err);
          reject(new Error("上传图片失败: " + JSON.stringify(err)));
        }
      });
    });
  },
  /**
   * 上传视频到服务器
   * @param {String} filePath - 本地视频路径
   * @returns {Promise} - 返回上传结果，包含服务器文件路径
   */
  uploadVideo(filePath) {
    return new Promise((resolve, reject) => {
      common_vendor.index.__f__("log", "at utils/upload.js:70", "开始上传视频:", filePath);
      common_vendor.index.__f__("log", "at utils/upload.js:71", "上传URL:", UPLOAD_VIDEO_URL);
      let token = utils_auth.getToken();
      if (token && !token.startsWith("Bearer ")) {
        token = "Bearer " + token;
      }
      common_vendor.index.uploadFile({
        url: UPLOAD_VIDEO_URL,
        filePath,
        name: "file",
        header: {
          "Authorization": token
        },
        success: (res) => {
          common_vendor.index.__f__("log", "at utils/upload.js:87", "视频上传响应:", res);
          try {
            const data = JSON.parse(res.data);
            if (data.code === 200) {
              common_vendor.index.__f__("log", "at utils/upload.js:92", "视频上传成功, 服务器路径:", data.data);
              resolve(data.data);
            } else {
              common_vendor.index.__f__("error", "at utils/upload.js:95", "视频上传失败, 错误信息:", data.message);
              reject(new Error(data.message || "上传视频失败"));
            }
          } catch (e) {
            common_vendor.index.__f__("error", "at utils/upload.js:99", "解析上传响应失败:", e, "原始响应:", res.data);
            reject(new Error("解析上传响应失败"));
          }
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at utils/upload.js:104", "视频上传请求失败:", err);
          reject(new Error("上传视频失败: " + JSON.stringify(err)));
        }
      });
    });
  },
  /**
   * 获取完整的文件访问URL
   * @param {String} filePath - 服务器返回的文件路径
   * @returns {String} - 完整的文件访问URL
   */
  getFileUrl(filePath) {
    if (!filePath)
      return "";
    filePath = "/uploads" + filePath;
    common_vendor.index.__f__("log", "at utils/upload.js:119", "原始文件路径:", filePath);
    if (filePath.startsWith("blob:")) {
      return filePath;
    }
    if (filePath.startsWith("http")) {
      return filePath;
    }
    if (filePath.includes("/uploads/videos/") || filePath.endsWith(".mp4")) {
      let pathParts = filePath.split("/");
      let encodedParts = pathParts.map((part) => {
        if (part.includes(".mp4")) {
          return encodeURIComponent(part);
        }
        return part;
      });
      let encodedPath = encodedParts.join("/");
      let fullUrl2;
      if (encodedPath.startsWith("/")) {
        fullUrl2 = BASE_URL + encodedPath;
      } else {
        fullUrl2 = BASE_URL + "/" + encodedPath;
      }
      return fullUrl2;
    }
    let fullUrl;
    if (filePath.startsWith("/")) {
      fullUrl = BASE_URL + filePath;
    } else {
      fullUrl = BASE_URL + "/" + filePath;
    }
    return fullUrl;
  }
};
exports.uploadUtils = uploadUtils;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/upload.js.map
