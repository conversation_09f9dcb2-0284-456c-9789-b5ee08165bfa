<view class="page-container"><view class="order-info-card"><view class="order-header"><view class="order-number"><text>{{a}}</text></view><view class="{{['order-status', c]}}">{{b}}</view></view><view class="order-location"><view class="location-info"><text class="location-name">{{d}}</text></view></view><view class="patrol-info"><view class="patrol-executor"><text class="patrol-label">执行人员：</text><text class="patrol-value">{{e}}</text></view><view class="patrol-date"><text class="patrol-label">执行日期：</text><text class="patrol-value">{{f}}</text></view></view></view><view class="detail-tabs"><view wx:for="{{g}}" wx:for-item="tab" wx:key="b" class="{{['tab-item', tab.c && 'active']}}" bindtap="{{tab.d}}">{{tab.a}}</view></view><view wx:if="{{h}}" class="error-container"><view class="error-icon">!</view><text class="error-message">{{i}}</text><button class="retry-button" bindtap="{{j}}">重试</button></view><view wx:if="{{k}}" class="content-container"><view wx:if="{{l}}" class="tab-content"><view class="scroll-view-content"><view class="info-section"><view class="section-content"><view class="info-item"><text class="info-label">所属计划</text><text class="info-value">{{m}}</text></view><view wx:if="{{n}}" class="info-item"><text class="info-label">计划编号</text><text class="info-value">{{o}}</text></view><view wx:if="{{p}}" class="info-item"><text class="info-label">巡检类型</text><text class="info-value">{{q}}</text></view><view wx:if="{{r}}" class="info-item"><text class="info-label">周期类型</text><text class="info-value">{{s}}</text></view><view wx:if="{{t}}" class="info-item"><text class="info-label">执行日</text><text class="info-value">{{v}}</text></view><view wx:if="{{w}}" class="info-item"><text class="info-label">执行日</text><text class="info-value">{{x}}</text></view><view wx:if="{{y}}" class="info-item"><text class="info-label">执行间隔</text><text class="info-value">{{z}} 天</text></view><view class="info-item"><text class="info-label">开始日期</text><text class="info-value">{{A}}</text></view><view wx:if="{{B}}" class="info-item"><text class="info-label">结束日期</text><text class="info-value">{{C}}</text></view><view class="info-item"><text class="info-label">开始时间</text><text class="info-value">{{D}}</text></view><view class="info-item"><text class="info-label">结束时间</text><text class="info-value">{{E}}</text></view><view class="info-item"><text class="info-label">巡检地点</text><text class="info-value">{{F}}</text></view><view wx:if="{{G}}" class="info-item"><text class="info-label">联系电话</text><text class="info-value">{{H}}</text></view><view wx:if="{{I}}" class="info-item"><text class="info-label">备注</text><text class="info-value">{{J}}</text></view></view></view></view></view><view wx:if="{{K}}" class="tab-content"><view class="scroll-view-content"><view class="patrol-results-header"><text class="results-title">{{L}}</text><view class="results-summary"><text class="results-count">共 {{M}} 项</text><text wx:if="{{N}}" class="abnormal-count"> 异常 {{O}} 项 </text></view></view><view wx:if="{{P}}" class="empty-results"><image src="{{Q}}" mode="aspectFit"></image><text>{{R}}</text></view><view wx:else class="task-list"><view wx:for="{{S}}" wx:for-item="result" wx:key="z" class="task-item" bindtap="{{result.A}}"><view class="task-header"><view class="task-left"><view class="{{['task-status', result.a]}}"></view><text class="task-title">{{result.b}}</text></view><text class="{{['task-status-text', result.d]}}">{{result.c}}</text></view><view wx:if="{{result.e}}" class="task-details"><view wx:if="{{result.f}}" class="detail-row"><text class="detail-label">设备信息</text><text class="detail-value">{{result.g}}{{result.h}}</text></view><view wx:if="{{result.i}}" class="detail-row"><text class="detail-label">检查类别</text><text class="detail-value">{{result.j}}</text></view><view wx:if="{{result.k}}" class="detail-row"><text class="detail-label">检查方法</text><text class="detail-value">{{result.l}}</text></view><view wx:if="{{result.m}}" class="detail-row"><text class="detail-label">描述说明</text><text class="detail-value">{{result.n}}</text></view><view wx:if="{{result.o}}" class="detail-row"><text class="detail-label">参数值</text><text class="{{['detail-value', result.r && 'value-abnormal']}}">{{result.p}}{{result.q}}</text></view><view wx:if="{{result.s}}" class="detail-row"><text class="detail-label">正常范围</text><text class="detail-value">{{result.t}}</text></view><view wx:if="{{result.v}}" class="detail-row"><text class="detail-label">备注</text><text class="detail-value">{{result.w}}</text></view><view wx:if="{{result.x}}" class="task-images"><text class="images-title">巡检照片</text><view class="image-list"><image wx:for="{{result.y}}" wx:for-item="img" wx:key="a" src="{{img.b}}" bindtap="{{img.c}}" mode="aspectFill"></image></view></view></view></view></view></view></view><view wx:if="{{T}}" class="tab-content"><view class="scroll-view-content"><view wx:if="{{U}}" class="all-images"><view wx:for="{{V}}" wx:for-item="imageInfo" wx:key="c" class="image-card" bindtap="{{imageInfo.d}}"><image src="{{imageInfo.a}}" mode="aspectFill"></image><view class="image-item-name">{{imageInfo.b}}</view></view></view><view wx:else class="empty-results"><view class="empty-content"><image src="{{W}}" mode="aspectFit"></image><text>暂无巡检照片</text></view></view></view></view></view><permission-check wx:if="{{Z}}" u-s="{{['d']}}" u-i="4af47392-0" bind:__l="__l" u-p="{{Z}}"><view wx:if="{{X}}" class="action-buttons"><view class="action-btn start" bindtap="{{Y}}"><text>开始巡检</text></view></view></permission-check></view>