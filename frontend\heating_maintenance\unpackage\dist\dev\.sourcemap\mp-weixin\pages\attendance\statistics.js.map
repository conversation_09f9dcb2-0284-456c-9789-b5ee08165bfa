{"version": 3, "file": "statistics.js", "sources": ["pages/attendance/statistics.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYXR0ZW5kYW5jZS9zdGF0aXN0aWNzLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"statistics-container\">\r\n\t\t<!-- 顶部筛选区域 -->\r\n\t\t<view class=\"filter-section\">\r\n\t\t\t<view class=\"month-selector\">\r\n\t\t\t\t<text class=\"current-month\">{{ currentYear }}年{{ currentMonth }}月</text>\r\n\t\t\t\t<view class=\"month-arrows\">\r\n\t\t\t\t\t<uni-icons type=\"left\" size=\"20\" color=\"#333\" @click=\"prevMonth\"></uni-icons>\r\n\t\t\t\t\t<uni-icons type=\"right\" size=\"20\" color=\"#333\" @click=\"nextMonth\"></uni-icons>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"staff-selector\" @click=\"openStaffSelector\">\r\n\t\t\t\t<text>{{ selectedStaff ? selectedStaff.name : '全部人员' }}</text>\r\n\t\t\t\t<uni-icons type=\"bottom\" size=\"14\" color=\"#666\"></uni-icons>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 统计概览卡片 -->\r\n\t\t<view class=\"statistics-cards\">\r\n\t\t\t<view class=\"stat-card\">\r\n\t\t\t\t<view class=\"stat-value\">{{ statistics.normalDays }}</view>\r\n\t\t\t\t<view class=\"stat-label\">正常出勤</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"stat-card\">\r\n\t\t\t\t<view class=\"stat-value\">{{ statistics.lateDays }}</view>\r\n\t\t\t\t<view class=\"stat-label\">迟到</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"stat-card\">\r\n\t\t\t\t<view class=\"stat-value\">{{ statistics.earlyDays }}</view>\r\n\t\t\t\t<view class=\"stat-label\">早退</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"stat-card\">\r\n\t\t\t\t<view class=\"stat-value\">{{ statistics.absentDays }}</view>\r\n\t\t\t\t<view class=\"stat-label\">缺勤</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 日历区域 -->\r\n\t\t<view class=\"main-calendar\">\r\n\t\t\t<view class=\"calendar-view\">\r\n\t\t\t\t<view class=\"calendar-header\">\r\n\t\t\t\t\t<view v-for=\"(day, index) in weekDays\" :key=\"index\" class=\"header-item\">{{ day }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"calendar-days\">\r\n\t\t\t\t\t<view v-for=\"(day, index) in calendarDays\" :key=\"index\"\r\n\t\t\t\t\t\tclass=\"calendar-day\"\r\n\t\t\t\t\t\t:class=\"{\r\n\t\t\t\t\t\t\t'current-month': day.isCurrentMonth,\r\n\t\t\t\t\t\t\t'today': day.isToday,\r\n\t\t\t\t\t\t\t'normal': day.status === 'normal',\r\n\t\t\t\t\t\t\t'late': day.status === 'late',\r\n\t\t\t\t\t\t\t'early': day.status === 'early',\r\n\t\t\t\t\t\t\t'absent': day.status === 'absent'\r\n\t\t\t\t\t\t}\"\r\n\t\t\t\t\t\t@click=\"viewDayDetail(day)\">\r\n\t\t\t\t\t\t<text class=\"day-number\">{{ day.day }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 添加图表区域 -->\r\n\t\t<view class=\"charts-section\">\r\n\t\t\t<view class=\"section-title\">\r\n\t\t\t\t<text>考勤数据分析</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"charts-container\">\r\n\t\t\t\t<!-- 柱状图 -->\r\n\t\t\t\t<view class=\"chart-card\">\r\n\t\t\t\t\t<view class=\"chart-title\">月度考勤统计</view>\r\n\t\t\t\t\t<view class=\"chart-container\">\r\n\t\t\t\t\t\t<canvas canvas-id=\"attendanceBarChart\" style=\"width: 100%; height: 400rpx;\"></canvas>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 饼图 -->\r\n\t\t\t\t<view class=\"chart-card\">\r\n\t\t\t\t\t<view class=\"chart-title\">考勤类型分布</view>\r\n\t\t\t\t\t<view class=\"chart-container\">\r\n\t\t\t\t\t\t<canvas canvas-id=\"attendancePieChart\" style=\"width: 100%; height: 400rpx;\"></canvas>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 考勤详细记录 -->\r\n\t\t<view class=\"attendance-records\">\r\n\t\t\t<view class=\"record-header\">\r\n\t\t\t\t<text>考勤记录</text>\r\n\t\t\t\t<view class=\"header-actions\">\r\n\t\t\t\t\t<text class=\"export-btn\" @click=\"exportAttendanceData\">导出统计</text>\r\n\t\t\t\t\t<text class=\"view-all\" @click=\"viewAllRecords\">查看全部</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"records.length > 0\" class=\"record-list\">\r\n\t\t\t\t<view v-for=\"(record, index) in records\" :key=\"index\" class=\"record-item\">\r\n\t\t\t\t\t<view class=\"record-date\">\r\n\t\t\t\t\t\t<text class=\"day\">{{ record.date || formatDate(record.clockTime) }}</text>\r\n\t\t\t\t\t\t<text class=\"week\">{{ record.week || formatWeek(record.clockTime) }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"record-time\">\r\n\t\t\t\t\t\t<view class=\"time-item\">\r\n\t\t\t\t\t\t\t<text class=\"time-label\">上班打卡</text>\r\n\t\t\t\t\t\t\t<text class=\"time-value\" :class=\"{'abnormal': record.clockInStatus !== 'normal'}\">\r\n\t\t\t\t\t\t\t\t{{ record.clockInTime || '未打卡' }}\r\n\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t<text v-if=\"record.clockInStatus === 'late'\" class=\"status-tag late\">迟到</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"time-item\">\r\n\t\t\t\t\t\t\t<text class=\"time-label\">下班打卡</text>\r\n\t\t\t\t\t\t\t<text class=\"time-value\" :class=\"{'abnormal': record.clockOutStatus !== 'normal'}\">\r\n\t\t\t\t\t\t\t\t{{ record.clockOutTime || '未打卡' }}\r\n\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t<text v-if=\"record.clockOutStatus === 'early'\" class=\"status-tag early\">早退</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"record-location\">\r\n\t\t\t\t\t\t<text class=\"location-label\">打卡地点</text>\r\n\t\t\t\t\t\t<text class=\"location-value\">{{ record.location || formatLocation(record) }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view v-else class=\"empty-records\">\r\n\t\t\t\t<image src=\"/static/images/empty.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t<text>暂无考勤记录</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 人员选择器弹窗 -->\r\n\t\t<uni-popup ref=\"staffPopup\" type=\"bottom\">\r\n\t\t\t<view class=\"staff-popup\">\r\n\t\t\t\t<view class=\"popup-header\">\r\n\t\t\t\t\t<text>选择人员</text>\r\n\t\t\t\t\t<text @click=\"closeStaffSelector\" class=\"close-btn\">关闭</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"search-box\">\r\n\t\t\t\t\t<uni-icons type=\"search\" size=\"16\" color=\"#999\"></uni-icons>\r\n\t\t\t\t\t<input type=\"text\" v-model=\"searchKeyword\" placeholder=\"搜索人员姓名\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<scroll-view scroll-y=\"true\" class=\"staff-list\">\r\n\t\t\t\t\t<view class=\"staff-item\" @click=\"selectStaff(null)\">\r\n\t\t\t\t\t\t<text>全部人员</text>\r\n\t\t\t\t\t\t<uni-icons v-if=\"!selectedStaff\" type=\"checkmarkempty\" size=\"18\" color=\"#007AFF\"></uni-icons>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view \r\n\t\t\t\t\t\tv-for=\"(staff, index) in filteredStaffList\" \r\n\t\t\t\t\t\t:key=\"index\" \r\n\t\t\t\t\t\tclass=\"staff-item\"\r\n\t\t\t\t\t\t@click=\"selectStaff(staff)\">\r\n\t\t\t\t\t\t<text>{{ staff.name }}</text>\r\n\t\t\t\t\t\t<uni-icons \r\n\t\t\t\t\t\t\tv-if=\"selectedStaff && selectedStaff.id === staff.id\" \r\n\t\t\t\t\t\t\ttype=\"checkmarkempty\" \r\n\t\t\t\t\t\t\tsize=\"18\" \r\n\t\t\t\t\t\t\tcolor=\"#007AFF\">\r\n\t\t\t\t\t\t</uni-icons>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\r\n\t\t<!-- 日考勤详情弹窗 -->\r\n\t\t<uni-popup ref=\"dayDetailPopup\" type=\"bottom\">\r\n\t\t\t<view class=\"day-detail-popup\">\r\n\t\t\t\t<view class=\"popup-header\">\r\n\t\t\t\t\t<text>{{ selectedDate }} 考勤详情</text>\r\n\t\t\t\t\t<text class=\"close-btn\" @click=\"closeDayDetailPopup\">关闭</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 饼图统计 -->\r\n\t\t\t\t<canvas v-if=\"dayDetailData.length > 0\" canvas-id=\"dayAttendanceChart\" class=\"day-stats-chart\" @touchstart=\"touchChart\"></canvas>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 状态统计 -->\r\n\t\t\t\t<view v-if=\"dayDetailData.length > 0\" class=\"day-stats-summary\">\r\n\t\t\t\t\t<view class=\"stat-item normal\">\r\n\t\t\t\t\t\t<text class=\"stat-value\">{{ normalCount }}</text>\r\n\t\t\t\t\t\t<text class=\"stat-label\">正常</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"stat-item late\">\r\n\t\t\t\t\t\t<text class=\"stat-value\">{{ lateCount }}</text>\r\n\t\t\t\t\t\t<text class=\"stat-label\">迟到</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"stat-item early\">\r\n\t\t\t\t\t\t<text class=\"stat-value\">{{ earlyCount }}</text>\r\n\t\t\t\t\t\t<text class=\"stat-label\">早退</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"stat-item absent\">\r\n\t\t\t\t\t\t<text class=\"stat-value\">{{ absentCount }}</text>\r\n\t\t\t\t\t\t<text class=\"stat-label\">缺勤</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 选中人员详情 -->\r\n\t\t\t\t<view v-if=\"dayDetailData.length > 0 && selectedStaffDetail\" class=\"staff-detail-section\">\r\n\t\t\t\t\t<view class=\"detail-item\">\r\n\t\t\t\t\t\t<text class=\"detail-label\">姓名</text>\r\n\t\t\t\t\t\t<text class=\"detail-value\">{{ selectedStaffDetail.name }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail-item\">\r\n\t\t\t\t\t\t<text class=\"detail-label\">上班时间</text>\r\n\t\t\t\t\t\t<view class=\"detail-value-box\">\r\n\t\t\t\t\t\t\t<text class=\"detail-value\">{{ selectedStaffDetail.checkInTime || '无打卡' }}</text>\r\n\t\t\t\t\t\t\t<text v-if=\"selectedStaffDetail.isLate\" class=\"status-tag late\">迟到</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail-item\">\r\n\t\t\t\t\t\t<text class=\"detail-label\">下班时间</text>\r\n\t\t\t\t\t\t<view class=\"detail-value-box\">\r\n\t\t\t\t\t\t\t<text class=\"detail-value\">{{ selectedStaffDetail.checkOutTime || '无打卡' }}</text>\r\n\t\t\t\t\t\t\t<text v-if=\"selectedStaffDetail.isEarly\" class=\"status-tag early\">早退</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"detail-item\">\r\n\t\t\t\t\t\t<text class=\"detail-label\">工作时长</text>\r\n\t\t\t\t\t\t<text class=\"detail-value\">{{ selectedStaffDetail.workHours || '0小时' }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 人员列表 -->\r\n\t\t\t\t<view v-if=\"dayDetailData.length > 0\" class=\"staff-list-section\">\r\n\t\t\t\t\t<text class=\"section-title\">人员考勤列表</text>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 正常考勤人员 -->\r\n\t\t\t\t\t<view v-if=\"normalStaff.length > 0\" class=\"staff-category\">\r\n\t\t\t\t\t\t<view class=\"category-header normal\">\r\n\t\t\t\t\t\t\t<text>正常考勤</text>\r\n\t\t\t\t\t\t\t<text>{{ normalStaff.length }}人</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"staff-items\">\r\n\t\t\t\t\t\t\t<view class=\"staff-item\" v-for=\"(staff, index) in normalStaff\" :key=\"index\" @click=\"selectStaffDetail(staff)\">\r\n\t\t\t\t\t\t\t\t<text class=\"staff-name\">{{ staff.name }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"staff-time\">{{ staff.checkInTime }} - {{ staff.checkOutTime }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 迟到人员 -->\r\n\t\t\t\t\t<view v-if=\"lateStaff.length > 0\" class=\"staff-category\">\r\n\t\t\t\t\t\t<view class=\"category-header late\">\r\n\t\t\t\t\t\t\t<text>迟到人员</text>\r\n\t\t\t\t\t\t\t<text>{{ lateStaff.length }}人</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"staff-items\">\r\n\t\t\t\t\t\t\t<view class=\"staff-item\" v-for=\"(staff, index) in lateStaff\" :key=\"index\" @click=\"selectStaffDetail(staff)\">\r\n\t\t\t\t\t\t\t\t<text class=\"staff-name\">{{ staff.name }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"staff-time\">{{ staff.checkInTime }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 早退人员 -->\r\n\t\t\t\t\t<view v-if=\"earlyStaff.length > 0\" class=\"staff-category\">\r\n\t\t\t\t\t\t<view class=\"category-header early\">\r\n\t\t\t\t\t\t\t<text>早退人员</text>\r\n\t\t\t\t\t\t\t<text>{{ earlyStaff.length }}人</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"staff-items\">\r\n\t\t\t\t\t\t\t<view class=\"staff-item\" v-for=\"(staff, index) in earlyStaff\" :key=\"index\" @click=\"selectStaffDetail(staff)\">\r\n\t\t\t\t\t\t\t\t<text class=\"staff-name\">{{ staff.name }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"staff-time\">{{ staff.checkOutTime }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 缺勤人员 -->\r\n\t\t\t\t\t<view v-if=\"absentStaff.length > 0\" class=\"staff-category\">\r\n\t\t\t\t\t\t<view class=\"category-header absent\">\r\n\t\t\t\t\t\t\t<text>缺勤人员</text>\r\n\t\t\t\t\t\t\t<text>{{ absentStaff.length }}人</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"staff-items\">\r\n\t\t\t\t\t\t\t<view class=\"staff-item\" v-for=\"(staff, index) in absentStaff\" :key=\"index\" @click=\"selectStaffDetail(staff)\">\r\n\t\t\t\t\t\t\t\t<text class=\"staff-name\">{{ staff.name }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"staff-time\">无考勤记录</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 无数据提示 -->\r\n\t\t\t\t<view v-if=\"dayDetailData.length === 0\" class=\"empty-day-detail\">\r\n\t\t\t\t\t<image src=\"/static/images/empty.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t<text>暂无考勤记录</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\r\n\t\t<!-- 导出统计数据弹窗 -->\r\n\t\t<uni-popup ref=\"exportPopup\" type=\"bottom\">\r\n\t\t\t<view class=\"export-popup\">\r\n\t\t\t\t<view class=\"popup-header\">\r\n\t\t\t\t\t<text>导出考勤统计</text>\r\n\t\t\t\t\t<text @click=\"closeExportPopup\" class=\"close-btn\">关闭</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"export-form\">\r\n\t\t\t\t\t<view class=\"export-item\">\r\n\t\t\t\t\t\t<text class=\"export-label\">统计时间范围</text>\r\n\t\t\t\t\t\t<view class=\"export-date-range\">\r\n\t\t\t\t\t\t\t<view class=\"date-select\" @click=\"openDatePicker('start')\">\r\n\t\t\t\t\t\t\t\t<text>{{ exportForm.startDate || '开始日期' }}</text>\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"calendar\" size=\"16\" color=\"#999\"></uni-icons>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"range-separator\">至</text>\r\n\t\t\t\t\t\t\t<view class=\"date-select\" @click=\"openDatePicker('end')\">\r\n\t\t\t\t\t\t\t\t<text>{{ exportForm.endDate || '结束日期' }}</text>\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"calendar\" size=\"16\" color=\"#999\"></uni-icons>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"export-item\">\r\n\t\t\t\t\t\t<text class=\"export-label\">导出对象</text>\r\n\t\t\t\t\t\t<view class=\"export-radio-group\">\r\n\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"setExportType('all')\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'active': exportForm.type === 'all' }\"></view>\r\n\t\t\t\t\t\t\t\t<text>全部人员</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"setExportType('selected')\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'active': exportForm.type === 'selected' }\"></view>\r\n\t\t\t\t\t\t\t\t<text>选定人员</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"export-item\" v-if=\"exportForm.type === 'selected'\">\r\n\t\t\t\t\t\t<text class=\"export-label\">选择人员</text>\r\n\t\t\t\t\t\t<view class=\"selected-staff\" @click=\"openStaffMultiSelector\">\r\n\t\t\t\t\t\t\t<text>已选择 {{ selectedExportStaff.length }} 人</text>\r\n\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#999\"></uni-icons>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"export-item\">\r\n\t\t\t\t\t\t<text class=\"export-label\">导出格式</text>\r\n\t\t\t\t\t\t<view class=\"export-radio-group\">\r\n\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"exportForm.format = 'excel'\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'active': exportForm.format === 'excel' }\"></view>\r\n\t\t\t\t\t\t\t\t<text>Excel表格</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"radio-item\" @click=\"exportForm.format = 'pdf'\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-btn\" :class=\"{ 'active': exportForm.format === 'pdf' }\"></view>\r\n\t\t\t\t\t\t\t\t<text>PDF文档</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"export-footer\">\r\n\t\t\t\t\t<button class=\"cancel-btn\" @click=\"closeExportPopup\">取消</button>\r\n\t\t\t\t\t<button class=\"confirm-btn\" @click=\"confirmExport\">确认导出</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t\t\r\n\t\t<!-- 多选人员弹窗 -->\r\n\t\t<uni-popup ref=\"multiStaffPopup\" type=\"bottom\">\r\n\t\t\t<view class=\"staff-popup\">\r\n\t\t\t\t<view class=\"popup-header\">\r\n\t\t\t\t\t<text>选择导出人员</text>\r\n\t\t\t\t\t<text @click=\"closeMultiStaffSelector\" class=\"close-btn\">确定</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"search-box\">\r\n\t\t\t\t\t<uni-icons type=\"search\" size=\"16\" color=\"#999\"></uni-icons>\r\n\t\t\t\t\t<input type=\"text\" v-model=\"multiSearchKeyword\" placeholder=\"搜索人员姓名\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<scroll-view scroll-y=\"true\" class=\"staff-list\">\r\n\t\t\t\t\t<view \r\n\t\t\t\t\t\tv-for=\"(staff, index) in filteredMultiStaffList\" \r\n\t\t\t\t\t\t:key=\"index\" \r\n\t\t\t\t\t\tclass=\"staff-item\"\r\n\t\t\t\t\t\t@click=\"toggleExportStaff(staff)\">\r\n\t\t\t\t\t\t<text>{{ staff.name }}</text>\r\n\t\t\t\t\t\t<uni-icons \r\n\t\t\t\t\t\t\tv-if=\"isStaffSelected(staff)\" \r\n\t\t\t\t\t\t\ttype=\"checkbox-filled\" \r\n\t\t\t\t\t\t\tsize=\"18\" \r\n\t\t\t\t\t\t\tcolor=\"#007AFF\">\r\n\t\t\t\t\t\t</uni-icons>\r\n\t\t\t\t\t\t<uni-icons \r\n\t\t\t\t\t\t\tv-else\r\n\t\t\t\t\t\t\ttype=\"checkbox\" \r\n\t\t\t\t\t\t\tsize=\"18\" \r\n\t\t\t\t\t\t\tcolor=\"#999\">\r\n\t\t\t\t\t\t</uni-icons>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t\t\r\n\t\t<!-- 日期选择器 -->\r\n\t\t<uni-calendar \r\n\t\t\tref=\"calendar\"\r\n\t\t\t:insert=\"false\"\r\n\t\t\t@confirm=\"selectDate\"\r\n\t\t></uni-calendar>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { attendanceApi, userApi } from '@/utils/api.js';\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tcurrentYear: new Date().getFullYear(),\r\n\t\t\tcurrentMonth: new Date().getMonth() + 1,\r\n\t\t\tweekDays: ['日', '一', '二', '三', '四', '五', '六'],\r\n\t\t\tcalendarDays: [],\r\n\t\t\tselectedStaff: null,\r\n\t\t\tsearchKeyword: '',\r\n\t\t\tstaffList: [],\r\n\t\t\tisLoading: false,\r\n\t\t\tstatistics: {\r\n\t\t\t\tnormalDays: 0,\r\n\t\t\t\tlateDays: 0,\r\n\t\t\t\tearlyDays: 0,\r\n\t\t\t\tabsentDays: 0\r\n\t\t\t},\r\n\t\t\trecords: [],\r\n\t\t\tselectedDay: null,\r\n\t\t\tdayDetailRecord: null,\r\n\t\t\texportForm: {\r\n\t\t\t\tstartDate: '',\r\n\t\t\t\tendDate: '',\r\n\t\t\t\ttype: 'all',\r\n\t\t\t\tformat: 'excel'\r\n\t\t\t},\r\n\t\t\tselectedExportStaff: [],\r\n\t\t\tmultiSearchKeyword: '',\r\n\t\t\tcurrentPickerType: 'start',\r\n\t\t\texportLoading: false,\r\n\t\t\tdayRecords: [],\r\n\t\t\tdayStats: {\r\n\t\t\t\tnormal: 0,\r\n\t\t\t\tlate: 0,\r\n\t\t\t\tearly: 0,\r\n\t\t\t\tabsent: 0\r\n\t\t\t},\r\n\t\t\tselectedDate: '',\r\n\t\t\tdayDetailPopupVisible: false,\r\n\t\t\tdayDetailData: [],\r\n\t\t\tselectedStaffDetail: null\r\n\t\t};\r\n\t},\r\n\tcomputed: {\r\n\t\tfilteredStaffList() {\r\n\t\t\tif (!this.searchKeyword) return this.staffList;\r\n\t\t\t\r\n\t\t\treturn this.staffList.filter(staff => \r\n\t\t\t\tstaff.name.includes(this.searchKeyword) || \r\n\t\t\t\tstaff.department?.includes(this.searchKeyword)\r\n\t\t\t);\r\n\t\t},\r\n\t\tfilteredMultiStaffList() {\r\n\t\t\tif (!this.multiSearchKeyword) return this.staffList;\r\n\t\t\t\r\n\t\t\treturn this.staffList.filter(staff => \r\n\t\t\t\tstaff.name.includes(this.multiSearchKeyword) || \r\n\t\t\t\tstaff.department?.includes(this.multiSearchKeyword)\r\n\t\t\t);\r\n\t\t},\r\n\t\tnormalCount() {\r\n\t\t\treturn this.dayDetailData.filter(item => !item.isLate && !item.isEarly && !item.isAbsent).length;\r\n\t\t},\r\n\t\tlateCount() {\r\n\t\t\treturn this.dayDetailData.filter(item => item.isLate).length;\r\n\t\t},\r\n\t\tearlyCount() {\r\n\t\t\treturn this.dayDetailData.filter(item => item.isEarly).length;\r\n\t\t},\r\n\t\tabsentCount() {\r\n\t\t\treturn this.dayDetailData.filter(item => item.isAbsent).length;\r\n\t\t},\r\n\t\tnormalStaff() {\r\n\t\t\treturn this.dayDetailData.filter(item => !item.isLate && !item.isEarly && !item.isAbsent);\r\n\t\t},\r\n\t\tlateStaff() {\r\n\t\t\treturn this.dayDetailData.filter(item => item.isLate);\r\n\t\t},\r\n\t\tearlyStaff() {\r\n\t\t\treturn this.dayDetailData.filter(item => item.isEarly);\r\n\t\t},\r\n\t\tabsentStaff() {\r\n\t\t\treturn this.dayDetailData.filter(item => item.isAbsent);\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\t\tthis.generateCalendar();\r\n\t\tthis.loadStaffList();\r\n\t\tthis.loadAttendanceData();\r\n\t},\r\n\tmethods: {\r\n\t\t// 生成日历数据\r\n\t\tgenerateCalendar() {\r\n\t\t\tconst year = this.currentYear;\r\n\t\t\tconst month = this.currentMonth;\r\n\t\t\t\r\n\t\t\t// 获取当月第一天是星期几\r\n\t\t\tconst firstDay = new Date(year, month - 1, 1).getDay();\r\n\t\t\t// 获取当月的总天数\r\n\t\t\tconst daysInMonth = new Date(year, month, 0).getDate();\r\n\t\t\t// 获取上个月的总天数\r\n\t\t\tconst daysInPrevMonth = new Date(year, month - 1, 0).getDate();\r\n\t\t\t\r\n\t\t\tconst days = [];\r\n\t\t\t\r\n\t\t\t// 上个月的日期\r\n\t\t\tfor (let i = firstDay - 1; i >= 0; i--) {\r\n\t\t\t\tconst prevMonth = month === 1 ? 12 : month - 1;\r\n\t\t\t\tconst prevYear = month === 1 ? year - 1 : year;\r\n\t\t\t\tdays.push({\r\n\t\t\t\t\tday: daysInPrevMonth - i,\r\n\t\t\t\t\tmonth: prevMonth,\r\n\t\t\t\t\tyear: prevYear,\r\n\t\t\t\t\tisCurrentMonth: false,\r\n\t\t\t\t\tisToday: false,\r\n\t\t\t\t\tstatus: null\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 当月的日期\r\n\t\t\tconst today = new Date();\r\n\t\t\tconst todayDate = today.getDate();\r\n\t\t\tconst todayMonth = today.getMonth() + 1;\r\n\t\t\tconst todayYear = today.getFullYear();\r\n\t\t\t\r\n\t\t\tfor (let i = 1; i <= daysInMonth; i++) {\r\n\t\t\t\tdays.push({\r\n\t\t\t\t\tday: i,\r\n\t\t\t\t\tmonth,\r\n\t\t\t\t\tyear,\r\n\t\t\t\t\tisCurrentMonth: true,\r\n\t\t\t\t\tisToday: i === todayDate && month === todayMonth && year === todayYear,\r\n\t\t\t\t\tstatus: null // 初始无状态，将在加载数据后更新\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 下个月的日期\r\n\t\t\tconst remainingDays = 42 - days.length; // 6行7列 = 42格\r\n\t\t\tfor (let i = 1; i <= remainingDays; i++) {\r\n\t\t\t\tconst nextMonth = month === 12 ? 1 : month + 1;\r\n\t\t\t\tconst nextYear = month === 12 ? year + 1 : year;\r\n\t\t\t\tdays.push({\r\n\t\t\t\t\tday: i,\r\n\t\t\t\t\tmonth: nextMonth,\r\n\t\t\t\t\tyear: nextYear,\r\n\t\t\t\t\tisCurrentMonth: false,\r\n\t\t\t\t\tisToday: false,\r\n\t\t\t\t\tstatus: null\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tthis.calendarDays = days;\r\n\t\t},\r\n\t\t\r\n\t\t// 格式化日期为MM-DD格式\r\n\t\tformatDate(dateString) {\r\n\t\t\tif (!dateString) return '';\r\n\t\t\tconst date = new Date(dateString);\r\n\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\r\n\t\t\tconst day = String(date.getDate()).padStart(2, '0');\r\n\t\t\treturn `${month}-${day}`;\r\n\t\t},\r\n\t\t\r\n\t\t// 格式化星期为\"周X\"格式\r\n\t\tformatWeek(dateString) {\r\n\t\t\tif (!dateString) return '';\r\n\t\t\tconst date = new Date(dateString);\r\n\t\t\tconst weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];\r\n\t\t\treturn weekDays[date.getDay()];\r\n\t\t},\r\n\t\t\r\n\t\t// 格式化位置信息\r\n\t\tformatLocation(record) {\r\n\t\t\tif (!record || !record.latitude || !record.longitude) return '未知位置';\r\n\t\t\treturn `${record.latitude.toFixed(6)}, ${record.longitude.toFixed(6)}`;\r\n\t\t},\r\n\t\t\r\n\t\t// 加载员工列表\r\n\t\tloadStaffList() {\r\n\t\t\tattendanceApi.getAllStaff().then(res => {\r\n\t\t\t\tif (res.code === 200 && res.data) {\r\n\t\t\t\t\t// 根据接口返回的嵌套数据结构进行解析\r\n\t\t\t\t\tif (res.data.data && Array.isArray(res.data.data.staffList)) {\r\n\t\t\t\t\t\tthis.staffList = res.data.data.staffList;\r\n\t\t\t\t\t} else if (Array.isArray(res.data.staffList)) {\r\n\t\t\t\t\t\tthis.staffList = res.data.staffList;\r\n\t\t\t\t\t} else if (Array.isArray(res.data)) {\r\n\t\t\t\t\t\tthis.staffList = res.data;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.warn('员工列表数据格式异常:', res);\r\n\t\t\t\t\t\t// 降级使用用户API\r\n\t\t\t\t\t\tthis.loadInspectorList();\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 降级使用用户API\r\n\t\t\t\t\tthis.loadInspectorList();\r\n\t\t\t\t}\r\n\t\t\t}).catch(err => {\r\n\t\t\t\tconsole.error('获取人员列表失败:', err);\r\n\t\t\t\t// 降级方案：使用用户API获取人员列表\r\n\t\t\t\tthis.loadInspectorList();\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 从用户API加载检查员列表（降级方案）\r\n\t\tloadInspectorList() {\r\n\t\t\tuserApi.getInspectorList().then(userRes => {\r\n\t\t\t\tif (userRes.code === 200 && userRes.data) {\r\n\t\t\t\t\tif (Array.isArray(userRes.data)) {\r\n\t\t\t\t\t\tthis.staffList = userRes.data;\r\n\t\t\t\t\t} else if (userRes.data.data && Array.isArray(userRes.data.data.staffList)) {\r\n\t\t\t\t\t\tthis.staffList = userRes.data.data.staffList;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.warn('无法解析人员列表数据');\r\n\t\t\t\t\t\tthis.staffList = [];\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}).catch(userErr => {\r\n\t\t\t\tconsole.error('获取人员列表失败:', userErr);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '获取人员列表失败',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 上个月\r\n\t\tprevMonth() {\r\n\t\t\tif (this.currentMonth === 1) {\r\n\t\t\t\tthis.currentYear -= 1;\r\n\t\t\t\tthis.currentMonth = 12;\r\n\t\t\t} else {\r\n\t\t\t\tthis.currentMonth -= 1;\r\n\t\t\t}\r\n\t\t\tthis.generateCalendar();\r\n\t\t\tthis.loadAttendanceData();\r\n\t\t},\r\n\t\t\r\n\t\t// 下个月\r\n\t\tnextMonth() {\r\n\t\t\tif (this.currentMonth === 12) {\r\n\t\t\t\tthis.currentYear += 1;\r\n\t\t\t\tthis.currentMonth = 1;\r\n\t\t\t} else {\r\n\t\t\t\tthis.currentMonth += 1;\r\n\t\t\t}\r\n\t\t\tthis.generateCalendar();\r\n\t\t\tthis.loadAttendanceData();\r\n\t\t},\r\n\t\t\r\n\t\t// 打开人员选择器\r\n\t\topenStaffSelector() {\r\n\t\t\tthis.$refs.staffPopup.open();\r\n\t\t},\r\n\t\t\r\n\t\t// 关闭人员选择器\r\n\t\tcloseStaffSelector() {\r\n\t\t\tthis.$refs.staffPopup.close();\r\n\t\t},\r\n\t\t\r\n\t\t// 选择人员\r\n\t\tselectStaff(staff) {\r\n\t\t\tthis.selectedStaff = staff;\r\n\t\t\tthis.closeStaffSelector();\r\n\t\t\tthis.loadAttendanceData();\r\n\t\t},\r\n\t\t\r\n\t\t// 加载考勤数据\r\n\t\tloadAttendanceData() {\r\n\t\t\tthis.isLoading = true;\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载数据中...'\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 准备查询参数\r\n\t\t\tconst params = {\r\n\t\t\t\tyear: this.currentYear,\r\n\t\t\t\tmonth: this.currentMonth,\r\n\t\t\t\tday: this.selectedDay ? this.selectedDay.day : undefined\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 如果选择了特定员工，添加用户ID参数\r\n\t\t\tif (this.selectedStaff) {\r\n\t\t\t\tparams.userId = this.selectedStaff.id;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 获取考勤统计数据\r\n\t\t\tattendanceApi.getStats(params).then(res => {\r\n\t\t\t\tif (res.code === 200 && res.data) {\r\n\t\t\t\t\t// 检查不同的数据结构\r\n\t\t\t\t\tlet summaryData = res.data;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 如果数据在res.data.data中\r\n\t\t\t\t\tif (res.data.data && typeof res.data.data === 'object') {\r\n\t\t\t\t\t\tsummaryData = res.data.data;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 获取统计摘要数据\r\n\t\t\t\t\tconst summary = summaryData.summary || {};\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 直接使用API返回的统计值，而不是进行计算\r\n\t\t\t\t\tthis.statistics = {\r\n\t\t\t\t\t\tnormalDays: summary.normal || 0,\r\n\t\t\t\t\t\tlateDays: summary.late || 0,\r\n\t\t\t\t\t\tearlyDays: summary.early || 0,\r\n\t\t\t\t\t\tabsentDays: summary.absent || 0\r\n\t\t\t\t\t};\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 加载完成后重新初始化图表\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.initBarChart();\r\n\t\t\t\t\t\tthis.initPieChart();\r\n\t\t\t\t\t}, 500);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 如果有图表数据，更新日历状态\r\n\t\t\t\t\tif (summaryData.chart) {\r\n\t\t\t\t\t\tthis.updateCalendarStatus(summaryData.chart);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 获取考勤记录列表\r\n\t\t\t\treturn attendanceApi.getRecords(params);\r\n\t\t\t}).then(res => {\r\n\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\tlet recordsData = res.data;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 检查不同的数据结构\r\n\t\t\t\t\tif (res.data && res.data.data) {\r\n\t\t\t\t\t\trecordsData = res.data.data;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 更新考勤记录，处理多种可能的数据结构\r\n\t\t\t\t\tif (recordsData && recordsData.records) {\r\n\t\t\t\t\t\tthis.records = this.processAttendanceRecords(recordsData.records);\r\n\t\t\t\t\t} else if (recordsData && recordsData.list) {\r\n\t\t\t\t\t\tthis.records = this.processAttendanceRecords(recordsData.list);\r\n\t\t\t\t\t} else if (Array.isArray(recordsData)) {\r\n\t\t\t\t\t\tthis.records = this.processAttendanceRecords(recordsData);\r\n\t\t\t\t\t} else if (recordsData && typeof recordsData === 'object' && !Array.isArray(recordsData)) {\r\n\t\t\t\t\t\t// 可能是日期-记录映射对象\r\n\t\t\t\t\t\tthis.records = this.processAttendanceRecords(recordsData);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.warn('未能识别的考勤记录数据格式:', res.data);\r\n\t\t\t\t\t\tthis.records = [];\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.records = [];\r\n\t\t\t\t}\r\n\t\t\t}).catch(err => {\r\n\t\t\t\tconsole.error('获取考勤数据失败:', err);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '获取考勤数据失败',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}).finally(() => {\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 处理考勤记录数据\r\n\t\tprocessAttendanceRecords(records) {\r\n\t\t\tif (!Array.isArray(records) || records.length === 0) {\r\n\t\t\t\treturn [];\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 如果是对象结构的日期-记录映射，将其转为数组\r\n\t\t\tif (!Array.isArray(records) && typeof records === 'object') {\r\n\t\t\t\tconst recordArray = [];\r\n\t\t\t\tfor (const date in records) {\r\n\t\t\t\t\tif (records.hasOwnProperty(date)) {\r\n\t\t\t\t\t\t// 为每个日期创建一条记录\r\n\t\t\t\t\t\tconst dateObj = new Date(date);\r\n\t\t\t\t\t\tconst month = String(dateObj.getMonth() + 1).padStart(2, '0');\r\n\t\t\t\t\t\tconst day = String(dateObj.getDate()).padStart(2, '0');\r\n\t\t\t\t\t\tconst weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];\r\n\t\t\t\t\t\tconst week = weekDays[dateObj.getDay()];\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tconst record = records[date];\r\n\t\t\t\t\t\trecordArray.push({\r\n\t\t\t\t\t\t\tdate: date,\r\n\t\t\t\t\t\t\tday: `${month}-${day}`,\r\n\t\t\t\t\t\t\tweek: week,\r\n\t\t\t\t\t\t\tclockInTime: record.clockInTime || '未打卡',\r\n\t\t\t\t\t\t\tclockInStatus: record.clockInStatus || 'normal',\r\n\t\t\t\t\t\t\tclockOutTime: record.clockOutTime || '未打卡',\r\n\t\t\t\t\t\t\tclockOutStatus: record.clockOutStatus || 'normal',\r\n\t\t\t\t\t\t\tlocation: record.location || '未知位置'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn recordArray.slice(0, 5); // 仅显示最近5条记录\r\n\t\t\t}\r\n\r\n\t\t\t// 检查记录格式并适配\r\n\t\t\t// 后端现在返回合并后的记录，包含clockInTime和clockOutTime字段\r\n\t\t\tconst firstRecord = records[0];\r\n\t\t\t\r\n\t\t\t// 如果记录已包含上下班打卡时间字段，直接使用\r\n\t\t\tif (firstRecord.clockInTime !== undefined || firstRecord.clockOutTime !== undefined) {\r\n\t\t\t\treturn records.map(item => {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tdate: item.date || this.formatDate(item.clockTime),\r\n\t\t\t\t\t\tweek: item.week || this.formatWeek(item.clockTime),\r\n\t\t\t\t\t\tclockInTime: item.clockInTime || '未打卡',\r\n\t\t\t\t\t\tclockOutTime: item.clockOutTime || '未打卡',\r\n\t\t\t\t\t\tclockInStatus: item.clockInStatus || 'normal',\r\n\t\t\t\t\t\tclockOutStatus: item.clockOutStatus || 'normal',\r\n\t\t\t\t\t\tlocation: item.location || this.formatLocation(item)\r\n\t\t\t\t\t};\r\n\t\t\t\t}).slice(0, 5); // 仅显示最近5条记录\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 处理旧格式数据（向后兼容）\r\n\t\t\t// 如果是旧格式，包含clockType和clockTime\r\n\t\t\telse if (firstRecord.clockType && firstRecord.clockTime) {\r\n\t\t\t\t// 按日期分组\r\n\t\t\t\tconst recordsByDate = {};\r\n\t\t\t\t\r\n\t\t\t\trecords.forEach(record => {\r\n\t\t\t\t\tconst date = new Date(record.clockTime);\r\n\t\t\t\t\tconst dateStr = date.toISOString().split('T')[0]; // YYYY-MM-DD\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (!recordsByDate[dateStr]) {\r\n\t\t\t\t\t\trecordsByDate[dateStr] = {\r\n\t\t\t\t\t\t\tdate: this.formatDate(record.clockTime),\r\n\t\t\t\t\t\t\tweek: this.formatWeek(record.clockTime),\r\n\t\t\t\t\t\t\tclockInTime: '',\r\n\t\t\t\t\t\t\tclockOutTime: '',\r\n\t\t\t\t\t\t\tclockInStatus: 'normal',\r\n\t\t\t\t\t\t\tclockOutStatus: 'normal',\r\n\t\t\t\t\t\t\tlocation: record.location || this.formatLocation(record)\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 根据打卡类型设置上下班时间\r\n\t\t\t\t\tif (record.clockType === 'checkin') {\r\n\t\t\t\t\t\trecordsByDate[dateStr].clockInTime = this.formatTime(record.clockTime);\r\n\t\t\t\t\t\trecordsByDate[dateStr].clockInStatus = record.status;\r\n\t\t\t\t\t} else if (record.clockType === 'checkout') {\r\n\t\t\t\t\t\trecordsByDate[dateStr].clockOutTime = this.formatTime(record.clockTime);\r\n\t\t\t\t\t\trecordsByDate[dateStr].clockOutStatus = record.status;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 转换为数组并按日期降序排序\r\n\t\t\t\treturn Object.values(recordsByDate)\r\n\t\t\t\t\t.sort((a, b) => new Date(b.date) - new Date(a.date))\r\n\t\t\t\t\t.slice(0, 5);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 其他格式，尝试最基本的处理\r\n\t\t\telse {\r\n\t\t\t\tconsole.warn('未知的考勤记录数据格式，尝试基本处理:', firstRecord);\r\n\t\t\t\treturn records.map(record => {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tdate: record.date || this.formatDate(record.clockTime || record.createTime),\r\n\t\t\t\t\t\tweek: record.week || this.formatWeek(record.clockTime || record.createTime),\r\n\t\t\t\t\t\tclockInTime: record.clockInTime || '未打卡',\r\n\t\t\t\t\t\tclockOutTime: record.clockOutTime || '未打卡',\r\n\t\t\t\t\t\tclockInStatus: record.clockInStatus || 'normal',\r\n\t\t\t\t\t\tclockOutStatus: record.clockOutStatus || 'normal',\r\n\t\t\t\t\t\tlocation: record.location || this.formatLocation(record)\r\n\t\t\t\t\t};\r\n\t\t\t\t}).slice(0, 5);\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 更新日历状态\r\n\t\tupdateCalendarStatus(chartData) {\r\n\t\t\t// 从图表数据中提取每天的状态\r\n\t\t\tif (!chartData || !chartData.xaxis) return;\r\n\t\t\t\r\n\t\t\t// 如果有dailyStatus数据，优先使用它\r\n\t\t\tif (chartData.dailyStatus && Array.isArray(chartData.dailyStatus)) {\r\n\t\t\t\tthis.calendarDays.forEach(day => {\r\n\t\t\t\t\tif (day.isCurrentMonth) {\r\n\t\t\t\t\t\tconst dayIndex = day.day - 1; // 数组索引从0开始\r\n\t\t\t\t\t\tif (dayIndex >= 0 && dayIndex < chartData.dailyStatus.length) {\r\n\t\t\t\t\t\t\t// 根据dailyStatus设置状态\r\n\t\t\t\t\t\t\tday.status = chartData.dailyStatus[dayIndex] || null;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 回退到老的处理逻辑\r\n\t\t\tif (!chartData.series) return;\r\n\t\t\t\r\n\t\t\tconst attendanceSeries = chartData.series.find(s => s.name === '出勤率' || s.name === 'attendance');\r\n\t\t\tconst lateSeries = chartData.series.find(s => s.name === '迟到率' || s.name === 'late');\r\n\t\t\tconst absentSeries = chartData.series.find(s => s.name === '缺勤率' || s.name === 'absent');\r\n\t\t\tconst earlySeries = chartData.series.find(s => s.name === '早退率' || s.name === 'early');\r\n\t\t\t\r\n\t\t\t// 确保必要的数据系列存在\r\n\t\t\tif (!attendanceSeries && !lateSeries && !absentSeries && !earlySeries) return;\r\n\t\t\t\r\n\t\t\t// 更新日历天的状态\r\n\t\t\tthis.calendarDays.forEach(day => {\r\n\t\t\t\tif (day.isCurrentMonth) {\r\n\t\t\t\t\tconst dayIndex = day.day - 1; // 数组索引从0开始\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 检查日期索引是否有效\r\n\t\t\t\t\tif (dayIndex >= 0 && dayIndex < chartData.xaxis.length) {\r\n\t\t\t\t\t\t// 优先级: 缺勤 > 迟到 > 早退 > 正常出勤\r\n\t\t\t\t\t\tif (absentSeries && absentSeries.data[dayIndex] > 0) {\r\n\t\t\t\t\t\t\tday.status = 'absent';\r\n\t\t\t\t\t\t} else if (lateSeries && lateSeries.data[dayIndex] > 0) {\r\n\t\t\t\t\t\t\tday.status = 'late';\r\n\t\t\t\t\t\t} else if (earlySeries && earlySeries.data[dayIndex] > 0) {\r\n\t\t\t\t\t\t\tday.status = 'early';\r\n\t\t\t\t\t\t} else if (attendanceSeries && attendanceSeries.data[dayIndex] > 0) {\r\n\t\t\t\t\t\t\tday.status = 'normal';\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tday.status = null; // 无数据\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 格式化时间\r\n\t\tformatTime(timeString) {\r\n\t\t\tif (!timeString) return '未打卡';\r\n\t\t\t\r\n\t\t\ttry {\r\n\t\t\t\tconst date = new Date(timeString);\r\n\t\t\t\tconst hours = String(date.getHours()).padStart(2, '0');\r\n\t\t\t\tconst minutes = String(date.getMinutes()).padStart(2, '0');\r\n\t\t\t\treturn `${hours}:${minutes}`;\r\n\t\t\t} catch (e) {\r\n\t\t\t\treturn timeString;\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 查看某天的详细考勤记录\r\n\t\tviewDayDetail(day) {\r\n\t\t\tif (!day.isCurrentMonth) return;\r\n\t\t\t\r\n\t\t\tthis.selectedDay = day;\r\n\t\t\tthis.selectedDate = `${day.year}年${day.month}月${day.day}日`;\r\n\t\t\t\r\n\t\t\t// 查询当天的详细考勤记录\r\n\t\t\tconst params = {\r\n\t\t\t\tyear: day.year,\r\n\t\t\t\tmonth: day.month,\r\n\t\t\t\tday: day.day\r\n\t\t\t\t// 不传userId表示获取所有人员\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\tthis.isLoading = true;\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载数据中...'\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 使用getRecords而不是getDayAttendance\r\n\t\t\tattendanceApi.getRecords(params).then(res => {\r\n\t\t\t\tif (res.code === 200 && res.data) {\r\n\t\t\t\t\t// 处理返回数据\r\n\t\t\t\t\tlet records = [];\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 解析API返回的数据结构\r\n\t\t\t\t\tif (res.data.records) {\r\n\t\t\t\t\t\trecords = res.data.records;\r\n\t\t\t\t\t} else if (res.data.list) {\r\n\t\t\t\t\t\trecords = res.data.list;\r\n\t\t\t\t\t} else if (Array.isArray(res.data)) {\r\n\t\t\t\t\t\trecords = res.data;\r\n\t\t\t\t\t} else if (res.data.data && (res.data.data.records || res.data.data.list)) {\r\n\t\t\t\t\t\trecords = res.data.data.records || res.data.data.list;\r\n\t\t\t\t\t} else if (typeof res.data === 'object' && !Array.isArray(res.data)) {\r\n                        // 处理可能是用户ID为key的对象格式\r\n                        const recordsData = res.data;\r\n                        records = [];\r\n                        for (const key in recordsData) {\r\n                            if (recordsData.hasOwnProperty(key)) {\r\n                                // 添加用户ID和记录\r\n                                const record = recordsData[key];\r\n                                if (record && typeof record === 'object') {\r\n                                    // 确保record是对象且有userId和name属性\r\n                                    record.userId = record.userId || key;\r\n                                    record.userName = record.userName || record.name || '未知';\r\n                                    records.push(record);\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 保存当天考勤记录\r\n\t\t\t\t\tthis.dayRecords = records;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 将打卡记录按人员分组\r\n\t\t\t\t\tconst userClockMap = {};\r\n\t\t\t\t\t\r\n\t\t\t\t\trecords.forEach(record => {\r\n\t\t\t\t\t\t// 获取用户信息，优先使用user对象中的信息\r\n\t\t\t\t\t\tconst userId = record.user?.id || record.userId || record.id || '';\r\n\t\t\t\t\t\tconst userName = record.user?.name || record.userName || record.staffName || record.name || '未知';\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tif (!userClockMap[userId]) {\r\n\t\t\t\t\t\t\tuserClockMap[userId] = {\r\n\t\t\t\t\t\t\t\tid: userId,\r\n\t\t\t\t\t\t\t\tname: userName,\r\n\t\t\t\t\t\t\t\tdepartment: record.user?.department || record.department || '',\r\n\t\t\t\t\t\t\t\tcheckInTime: '',\r\n\t\t\t\t\t\t\t\tcheckOutTime: '',\r\n\t\t\t\t\t\t\t\tisLate: false,\r\n\t\t\t\t\t\t\t\tisEarly: false,\r\n\t\t\t\t\t\t\t\tisAbsent: false,\r\n\t\t\t\t\t\t\t\tworkHours: '--'\r\n\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 根据打卡类型设置上下班时间\r\n\t\t\t\t\t\tif (record.clockType === 'checkin') {\r\n\t\t\t\t\t\t\tuserClockMap[userId].checkInTime = this.formatTime(record.clockTime);\r\n\t\t\t\t\t\t\tuserClockMap[userId].isLate = record.status === 'late';\r\n\t\t\t\t\t\t} else if (record.clockType === 'checkout') {\r\n\t\t\t\t\t\t\tuserClockMap[userId].checkOutTime = this.formatTime(record.clockTime);\r\n\t\t\t\t\t\t\tuserClockMap[userId].isEarly = record.status === 'early';\r\n\t\t\t\t\t\t} else if (record.checkInTime || record.checkOutTime) {\r\n                            // 处理已经分离的上下班时间记录\r\n                            if (record.checkInTime) {\r\n                                userClockMap[userId].checkInTime = this.formatTime(record.checkInTime);\r\n                                userClockMap[userId].isLate = record.clockInStatus === 'late';\r\n                            }\r\n                            if (record.checkOutTime) {\r\n                                userClockMap[userId].checkOutTime = this.formatTime(record.checkOutTime);\r\n                                userClockMap[userId].isEarly = record.clockOutStatus === 'early';\r\n                            }\r\n                        }\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 设置缺勤状态\r\n\t\t\t\t\t\tif (record.status === 'absent' || (record.clockInStatus === 'absent' && record.clockOutStatus === 'absent')) {\r\n\t\t\t\t\t\t\tuserClockMap[userId].isAbsent = true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 处理工作时长\r\n\t\t\t\t\tObject.values(userClockMap).forEach(user => {\r\n\t\t\t\t\t\tif (user.checkInTime && user.checkOutTime && \r\n                           user.checkInTime !== '未打卡' && user.checkOutTime !== '未打卡') {\r\n\t\t\t\t\t\t\t// 找到对应的记录计算工作时长\r\n\t\t\t\t\t\t\tconst checkInRecord = records.find(r => \r\n\t\t\t\t\t\t\t\t(r.user?.id === user.id || r.userId === user.id) && r.clockType === 'checkin'\r\n\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\tconst checkOutRecord = records.find(r => \r\n\t\t\t\t\t\t\t\t(r.user?.id === user.id || r.userId === user.id) && r.clockType === 'checkout'\r\n\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tif (checkInRecord && checkOutRecord) {\r\n\t\t\t\t\t\t\t\tuser.workHours = this.calculateWorkHours(checkInRecord.clockTime, checkOutRecord.clockTime);\r\n\t\t\t\t\t\t\t} else if (records.find(r => r.userId === user.id && r.checkInTime && r.checkOutTime)) {\r\n                                // 直接使用记录中的时间\r\n                                const record = records.find(r => r.userId === user.id);\r\n                                user.workHours = this.calculateWorkHours(record.checkInTime, record.checkOutTime);\r\n                            }\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 检查缺勤员工（列表中不存在的人员）\r\n\t\t\t\t\t// 如果有所有员工列表信息，可以添加缺勤员工\r\n\t\t\t\t\tif (this.staffList && this.staffList.length > 0) {\r\n                        this.staffList.forEach(staff => {\r\n                            const userId = staff.id;\r\n                            // 如果员工不在打卡记录中，标记为缺勤\r\n                            if (userId && !userClockMap[userId]) {\r\n                                userClockMap[userId] = {\r\n                                    id: userId,\r\n                                    name: staff.name || '未知',\r\n                                    department: staff.department || '',\r\n                                    checkInTime: '',\r\n                                    checkOutTime: '',\r\n                                    isLate: false,\r\n                                    isEarly: false,\r\n                                    isAbsent: true,\r\n                                    workHours: '--'\r\n                                };\r\n                            }\r\n                        });\r\n                    }\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 转换为数组\r\n\t\t\t\t\tthis.dayDetailData = Object.values(userClockMap);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 统计各状态人数\r\n\t\t\t\t\tthis.dayStats = {\r\n\t\t\t\t\t\tnormal: this.dayDetailData.filter(item => !item.isLate && !item.isEarly && !item.isAbsent).length,\r\n\t\t\t\t\t\tlate: this.dayDetailData.filter(item => item.isLate).length,\r\n\t\t\t\t\t\tearly: this.dayDetailData.filter(item => item.isEarly).length,\r\n\t\t\t\t\t\tabsent: this.dayDetailData.filter(item => item.isAbsent).length\r\n\t\t\t\t\t};\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 渲染当天考勤饼图\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.renderDayPieChart();\r\n\t\t\t\t\t}, 300);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 如果是查看单个员工的详情\r\n\t\t\t\t\tif (this.selectedStaff) {\r\n\t\t\t\t\t\t// 查找当前选中员工的记录\r\n\t\t\t\t\t\tconst staffRecord = this.dayDetailData.find(r => \r\n\t\t\t\t\t\t\tr.id == this.selectedStaff.id\r\n\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tif (staffRecord) {\r\n\t\t\t\t\t\t\tthis.selectedStaffDetail = staffRecord;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.selectedStaffDetail = null;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 不显示具体员工详情\r\n\t\t\t\t\t\tthis.selectedStaffDetail = null;\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.dayRecords = [];\r\n\t\t\t\t\tthis.dayDetailData = [];\r\n\t\t\t\t\tthis.dayStats = {\r\n\t\t\t\t\t\tnormal: 0,\r\n\t\t\t\t\t\tlate: 0,\r\n\t\t\t\t\t\tearly: 0,\r\n\t\t\t\t\t\tabsent: 0\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthis.selectedStaffDetail = null;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthis.$refs.dayDetailPopup.open();\r\n\t\t\t}).catch(err => {\r\n\t\t\t\tconsole.error('获取考勤详情失败:', err);\r\n\t\t\t\t\r\n\t\t\t\tthis.dayRecords = [];\r\n\t\t\t\tthis.dayDetailData = [];\r\n\t\t\t\tthis.dayStats = {\r\n\t\t\t\t\tnormal: 0,\r\n\t\t\t\t\tlate: 0,\r\n\t\t\t\t\tearly: 0,\r\n\t\t\t\t\tabsent: 0\r\n\t\t\t\t};\r\n\t\t\t\tthis.selectedStaffDetail = null;\r\n\t\t\t\tthis.$refs.dayDetailPopup.open();\r\n\t\t\t}).finally(() => {\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 渲染当天考勤饼图\r\n\t\trenderDayPieChart() {\r\n\t\t\tif (this.dayDetailData.length === 0) return;\r\n\t\t\t\r\n\t\t\tconst ctx = uni.createCanvasContext('dayAttendanceChart');\r\n\t\t\t\r\n\t\t\t// 获取设备信息和屏幕宽度\r\n\t\t\tconst sysInfo = uni.getSystemInfoSync();\r\n\t\t\tconst screenWidth = sysInfo.windowWidth;\r\n\t\t\t\r\n\t\t\t// 饼图数据\r\n\t\t\tconst pieData = [\r\n\t\t\t\t{ name: '正常', value: this.normalCount, color: '#4cd964' },\r\n\t\t\t\t{ name: '迟到', value: this.lateCount, color: '#f0ad4e' },\r\n\t\t\t\t{ name: '早退', value: this.earlyCount, color: '#f56c6c' },\r\n\t\t\t\t{ name: '缺勤', value: this.absentCount, color: '#dd524d' }\r\n\t\t\t];\r\n\t\t\t\r\n\t\t\t// 过滤掉数值为0的数据\r\n\t\t\tconst filteredData = pieData.filter(item => item.value > 0);\r\n\t\t\t\r\n\t\t\tif (filteredData.length === 0) return;\r\n\t\t\t\r\n\t\t\t// 调整画布尺寸和位置，确保饼图完全显示\r\n\t\t\tconst canvasWidth = screenWidth - 60; // 减去padding\r\n\t\t\tconst canvasHeight = 240; // 固定高度\r\n\t\t\tconst centerX = canvasWidth / 2;\r\n\t\t\tconst centerY = canvasHeight / 2;\r\n\t\t\t// 使用较小的半径以确保图例有足够空间\r\n\t\t\tconst radius = Math.min(centerX, centerY) * 0.6;\r\n\t\t\t\r\n\t\t\t// 计算总数\r\n\t\t\tconst total = filteredData.reduce((sum, item) => sum + item.value, 0);\r\n\t\t\t\r\n\t\t\t// 清空画布\r\n\t\t\tctx.clearRect(0, 0, canvasWidth, canvasHeight);\r\n\t\t\t\r\n\t\t\t// 绘制饼图\r\n\t\t\tlet startAngle = 0;\r\n\t\t\tfilteredData.forEach(item => {\r\n\t\t\t\tconst sliceAngle = (item.value / total) * 2 * Math.PI;\r\n\t\t\t\t\r\n\t\t\t\tctx.beginPath();\r\n\t\t\t\tctx.moveTo(centerX, centerY);\r\n\t\t\t\tctx.arc(centerX, centerY, radius, startAngle, startAngle + sliceAngle);\r\n\t\t\t\tctx.closePath();\r\n\t\t\t\t\r\n\t\t\t\tctx.setFillStyle(item.color);\r\n\t\t\t\tctx.fill();\r\n\t\t\t\t\r\n\t\t\t\tstartAngle += sliceAngle;\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 为每个数据项添加图例，使用右侧布局\r\n\t\t\tlet legendY = 40;\r\n\t\t\tconst legendSpacing = 30;\r\n\t\t\t\r\n\t\t\tfilteredData.forEach(item => {\r\n\t\t\t\t// 绘制小色块\r\n\t\t\t\tctx.setFillStyle(item.color);\r\n\t\t\t\tctx.fillRect(canvasWidth - 100, legendY - 8, 16, 16);\r\n\t\t\t\t\r\n\t\t\t\t// 绘制文字\r\n\t\t\t\tctx.setFillStyle('#333');\r\n\t\t\t\tctx.setFontSize(12);\r\n\t\t\t\tctx.setTextAlign('right');\r\n\t\t\t\tctx.fillText(`${item.name}: ${item.value}`, canvasWidth - 10, legendY);\r\n\t\t\t\t\r\n\t\t\t\tlegendY += legendSpacing;\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 中心白色圆\r\n\t\t\tctx.beginPath();\r\n\t\t\tctx.arc(centerX, centerY, radius * 0.5, 0, 2 * Math.PI);\r\n\t\t\tctx.setFillStyle('#ffffff');\r\n\t\t\tctx.fill();\r\n\t\t\t\r\n\t\t\t// 中心文字\r\n\t\t\tctx.setFillStyle('#333333');\r\n\t\t\tctx.setFontSize(14);\r\n\t\t\tctx.setTextAlign('center');\r\n\t\t\tctx.fillText('考勤分布', centerX, centerY - 7);\r\n\t\t\tctx.setFontSize(16);\r\n\t\t\tctx.fillText(total + '人', centerX, centerY + 15);\r\n\t\t\t\r\n\t\t\tctx.draw();\r\n\t\t},\r\n\t\t\r\n\t\t// 计算工作时长\r\n\t\tcalculateWorkHours(clockInTime, clockOutTime) {\r\n\t\t\tif (!clockInTime || !clockOutTime) return '--';\r\n\t\t\t\r\n\t\t\ttry {\r\n\t\t\t\tconst clockIn = new Date(clockInTime);\r\n\t\t\t\tconst clockOut = new Date(clockOutTime);\r\n\t\t\t\t\r\n\t\t\t\tconst diffMs = clockOut - clockIn;\r\n\t\t\t\tif (diffMs <= 0) return '--';\r\n\t\t\t\t\r\n\t\t\t\tconst hours = Math.floor(diffMs / 3600000);\r\n\t\t\t\tconst minutes = Math.floor((diffMs % 3600000) / 60000);\r\n\t\t\t\t\r\n\t\t\t\treturn `${hours}小时${minutes}分钟`;\r\n\t\t\t} catch (e) {\r\n\t\t\t\treturn '--';\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 关闭日详情弹窗\r\n\t\tcloseDayDetailPopup() {\r\n\t\t\tthis.$refs.dayDetailPopup.close();\r\n\t\t\tthis.dayDetailPopupVisible = false;\r\n\t\t\tthis.selectedStaffDetail = null;\r\n\t\t},\r\n\t\t\r\n\t\t// 查看全部记录\r\n\t\tviewAllRecords() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/pages/attendance/all-records'\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 预览图片\r\n\t\tpreviewImage(current, urls) {\r\n\t\t\tuni.previewImage({\r\n\t\t\t\tcurrent,\r\n\t\t\t\turls\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 导出考勤数据\r\n\t\texportAttendanceData() {\r\n\t\t\t// 设置默认日期范围为当前月份第一天到最后一天\r\n\t\t\tconst year = this.currentYear;\r\n\t\t\tconst month = this.currentMonth;\r\n\t\t\tconst firstDay = new Date(year, month - 1, 1);\r\n\t\t\tconst lastDay = new Date(year, month, 0);\r\n\t\t\t\r\n\t\t\t// 格式化日期为 YYYY-MM-DD\r\n\t\t\tconst formatDate = (date) => {\r\n\t\t\t\tconst y = date.getFullYear();\r\n\t\t\t\tconst m = String(date.getMonth() + 1).padStart(2, '0');\r\n\t\t\t\tconst d = String(date.getDate()).padStart(2, '0');\r\n\t\t\t\treturn `${y}-${m}-${d}`;\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\tthis.exportForm.startDate = formatDate(firstDay);\r\n\t\t\tthis.exportForm.endDate = formatDate(lastDay);\r\n\t\t\tthis.exportForm.type = 'all';\r\n\t\t\tthis.exportForm.format = 'excel';\r\n\t\t\tthis.selectedExportStaff = [];\r\n\t\t\t\r\n\t\t\tthis.$refs.exportPopup.open();\r\n\t\t},\r\n\t\t\r\n\t\t// 关闭导出弹窗\r\n\t\tcloseExportPopup() {\r\n\t\t\tthis.$refs.exportPopup.close();\r\n\t\t},\r\n\t\t\r\n\t\t// 设置导出类型\r\n\t\tsetExportType(type) {\r\n\t\t\tthis.exportForm.type = type;\r\n\t\t\tif (type === 'all') {\r\n\t\t\t\tthis.selectedExportStaff = [];\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 打开日期选择器\r\n\t\topenDatePicker(type) {\r\n\t\t\tthis.currentPickerType = type;\r\n\t\t\tthis.$refs.calendar.open();\r\n\t\t},\r\n\t\t\r\n\t\t// 日期选择回调\r\n\t\tselectDate(e) {\r\n\t\t\tconst dateStr = e.fulldate;\r\n\t\t\tif (this.currentPickerType === 'start') {\r\n\t\t\t\tthis.exportForm.startDate = dateStr;\r\n\t\t\t\t\r\n\t\t\t\t// 如果开始日期晚于结束日期，更新结束日期\r\n\t\t\t\tif (this.exportForm.endDate && new Date(dateStr) > new Date(this.exportForm.endDate)) {\r\n\t\t\t\t\tthis.exportForm.endDate = dateStr;\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tthis.exportForm.endDate = dateStr;\r\n\t\t\t\t\r\n\t\t\t\t// 如果结束日期早于开始日期，更新开始日期\r\n\t\t\t\tif (this.exportForm.startDate && new Date(dateStr) < new Date(this.exportForm.startDate)) {\r\n\t\t\t\t\tthis.exportForm.startDate = dateStr;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 打开多选人员选择器\r\n\t\topenStaffMultiSelector() {\r\n\t\t\tthis.$refs.multiStaffPopup.open();\r\n\t\t},\r\n\t\t\r\n\t\t// 关闭多选人员选择器\r\n\t\tcloseMultiStaffSelector() {\r\n\t\t\tthis.$refs.multiStaffPopup.close();\r\n\t\t},\r\n\t\t\r\n\t\t// 切换选择导出人员\r\n\t\ttoggleExportStaff(staff) {\r\n\t\t\tconst index = this.selectedExportStaff.findIndex(item => item.id === staff.id);\r\n\t\t\tif (index >= 0) {\r\n\t\t\t\tthis.selectedExportStaff.splice(index, 1);\r\n\t\t\t} else {\r\n\t\t\t\tthis.selectedExportStaff.push(staff);\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 检查员工是否被选中\r\n\t\tisStaffSelected(staff) {\r\n\t\t\treturn this.selectedExportStaff.some(item => item.id === staff.id);\r\n\t\t},\r\n\t\t\r\n\t\t// 确认导出考勤数据\r\n\t\tconfirmExport() {\r\n\t\t\t// 验证表单\r\n\t\t\tif (!this.exportForm.startDate || !this.exportForm.endDate) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请选择完整的时间范围',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (this.exportForm.type === 'selected' && this.selectedExportStaff.length === 0) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请至少选择一名员工',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 防止重复点击\r\n\t\t\tif (this.exportLoading) return;\r\n\t\t\tthis.exportLoading = true;\r\n\t\t\t\r\n\t\t\t// 显示加载提示\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '正在导出数据...'\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 准备请求参数\r\n\t\t\tconst params = {\r\n\t\t\t\tstartDate: this.exportForm.startDate,\r\n\t\t\t\tendDate: this.exportForm.endDate,\r\n\t\t\t\tformat: this.exportForm.format\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 如果是选定人员，添加用户ID列表\r\n\t\t\tif (this.exportForm.type === 'selected') {\r\n\t\t\t\tparams.userIds = this.selectedExportStaff.map(staff => staff.id);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 调用导出API\r\n\t\t\tattendanceApi.exportAttendance(params).then(res => {\r\n\t\t\t\tif (res.code === 200 && res.data) {\r\n\t\t\t\t\t// 成功获取到导出文件URL\r\n\t\t\t\t\tconst fileUrl = res.data.fileUrl || res.data;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 下载文件\r\n\t\t\t\t\tthis.downloadFile(fileUrl);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 关闭弹窗\r\n\t\t\t\t\tthis.closeExportPopup();\r\n\t\t\t\t\t\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '导出成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthrow new Error(res.message || '导出失败');\r\n\t\t\t\t}\r\n\t\t\t}).catch(err => {\r\n\t\t\t\tconsole.error('导出考勤数据失败:', err);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: err.message || '导出失败，请稍后重试',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}).finally(() => {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tthis.exportLoading = false;\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 下载文件\r\n\t\tdownloadFile(url) {\r\n\t\t\t// 检查平台\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\t// APP端下载文件\r\n\t\t\tconst downloadTask = uni.downloadFile({\r\n\t\t\t\turl: url,\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.statusCode === 200) {\r\n\t\t\t\t\t\tconst filePath = res.tempFilePath;\r\n\t\t\t\t\t\t// 打开文件\r\n\t\t\t\t\t\tuni.openDocument({\r\n\t\t\t\t\t\t\tfilePath: filePath,\r\n\t\t\t\t\t\t\tshowMenu: true,\r\n\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\tconsole.log('打开文档成功');\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\tconsole.error('打开文档失败', err);\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '无法打开文件',\r\n\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tfail: (err) => {\r\n\t\t\t\t\tconsole.error('下载文件失败', err);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '下载文件失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 监听下载进度\r\n\t\t\tdownloadTask.onProgressUpdate((res) => {\r\n\t\t\t\tconsole.log('下载进度:', res.progress);\r\n\t\t\t});\r\n\t\t\t// #endif\r\n\t\t\t\r\n\t\t\t// #ifdef H5\r\n\t\t\t// H5端直接打开链接\r\n\t\t\twindow.open(url);\r\n\t\t\t// #endif\r\n\t\t\t\r\n\t\t\t// #ifdef MP-WEIXIN || MP-ALIPAY\r\n\t\t\t// 小程序端使用保存文件到本地\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '正在下载...'\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\tuni.downloadFile({\r\n\t\t\t\turl: url,\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.statusCode === 200) {\r\n\t\t\t\t\t\tconst tempFilePath = res.tempFilePath;\r\n\t\t\t\t\t\t// 保存文件到本地\r\n\t\t\t\t\t\tuni.saveFile({\r\n\t\t\t\t\t\t\ttempFilePath: tempFilePath,\r\n\t\t\t\t\t\t\tsuccess: (saveRes) => {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tconst savedFilePath = saveRes.savedFilePath;\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\ttitle: '导出成功',\r\n\t\t\t\t\t\t\t\t\tcontent: '文件已保存到本地，是否查看？',\r\n\t\t\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\t\t\t// 打开文件\r\n\t\t\t\t\t\t\t\t\t\t\tuni.openDocument({\r\n\t\t\t\t\t\t\t\t\t\t\t\tfilePath: savedFilePath,\r\n\t\t\t\t\t\t\t\t\t\t\t\tshowMenu: true,\r\n\t\t\t\t\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tconsole.log('打开文档成功');\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tconsole.error('打开文档失败', err);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: '无法打开该类型文件',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tconsole.error('保存文件失败', err);\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '保存文件失败',\r\n\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tfail: (err) => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tconsole.error('下载文件失败', err);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '下载文件失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\t// 页面准备好后的回调\r\n\t\tonReady() {\r\n\t\t\t// 页面加载完成后初始化图表\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.initBarChart();\r\n\t\t\t\tthis.initPieChart();\r\n\t\t\t}, 500);\r\n\t\t},\r\n\t\t// 初始化柱状图\r\n\t\tinitBarChart() {\r\n\t\t\t// 获取设备信息\r\n\t\t\tconst sysInfo = uni.getSystemInfoSync();\r\n\t\t\t// 按照设备像素比计算canvas大小\r\n\t\t\tconst pixelRatio = sysInfo.pixelRatio || 2;\r\n\t\t\tconst canvasWidth = sysInfo.windowWidth * 0.9; // 屏幕宽度的90%\r\n\t\t\tconst canvasHeight = 400 * (sysInfo.windowWidth / 750); // 根据rpx换算为px\r\n\t\t\t\r\n\t\t\t// 使用Canvas绘制简单的柱状图\r\n\t\t\tconst ctx = uni.createCanvasContext('attendanceBarChart', this);\r\n\t\t\t\r\n\t\t\t// 设置画布背景\r\n\t\t\tctx.setFillStyle('#FFFFFF');\r\n\t\t\tctx.fillRect(0, 0, canvasWidth, canvasHeight);\r\n\t\t\t\r\n\t\t\t// 图表数据\r\n\t\t\tconst data = [\r\n\t\t\t\t{ label: '正常', value: this.statistics.normalDays, color: '#4cd964' },\r\n\t\t\t\t{ label: '迟到', value: this.statistics.lateDays, color: '#f0ad4e' },\r\n\t\t\t\t{ label: '早退', value: this.statistics.earlyDays, color: '#5bc0de' },\r\n\t\t\t\t{ label: '缺勤', value: this.statistics.absentDays, color: '#dd524d' }\r\n\t\t\t];\r\n\t\t\t\r\n\t\t\t// 计算最大值\r\n\t\t\tconst maxValue = Math.max(...data.map(item => item.value), 1);\r\n\t\t\t\r\n\t\t\t// 图表布局参数\r\n\t\t\tconst padding = { left: 40, right: 20, top: 20, bottom: 60 };\r\n\t\t\tconst chartWidth = canvasWidth - padding.left - padding.right;\r\n\t\t\tconst chartHeight = canvasHeight - padding.top - padding.bottom;\r\n\t\t\tconst barWidth = chartWidth / data.length / 1.5; // 调整柱宽\r\n\t\t\tconst barSpacing = barWidth / 2;\r\n\t\t\t\r\n\t\t\t// 绘制Y轴\r\n\t\t\tctx.beginPath();\r\n\t\t\tctx.setStrokeStyle('#CCCCCC');\r\n\t\t\tctx.setLineWidth(1);\r\n\t\t\tctx.moveTo(padding.left, padding.top);\r\n\t\t\tctx.lineTo(padding.left, canvasHeight - padding.bottom);\r\n\t\t\tctx.stroke();\r\n\t\t\t\r\n\t\t\t// 绘制X轴\r\n\t\t\tctx.beginPath();\r\n\t\t\tctx.moveTo(padding.left, canvasHeight - padding.bottom);\r\n\t\t\tctx.lineTo(canvasWidth - padding.right, canvasHeight - padding.bottom);\r\n\t\t\tctx.stroke();\r\n\t\t\t\r\n\t\t\t// 绘制柱状图\r\n\t\t\tdata.forEach((item, index) => {\r\n\t\t\t\tconst x = padding.left + (barWidth + barSpacing * 2) * index + barSpacing * 2;\r\n\t\t\t\tconst barHeight = item.value > 0 ? (item.value / maxValue) * chartHeight : 0;\r\n\t\t\t\tconst y = canvasHeight - padding.bottom - barHeight;\r\n\t\t\t\t\r\n\t\t\t\t// 绘制柱子\r\n\t\t\t\tctx.setFillStyle(item.color);\r\n\t\t\t\tctx.fillRect(x, y, barWidth, barHeight);\r\n\t\t\t\t\r\n\t\t\t\t// 绘制数值\r\n\t\t\t\tctx.setFillStyle('#333333');\r\n\t\t\t\tctx.setFontSize(12);\r\n\t\t\t\tctx.setTextAlign('center');\r\n\t\t\t\tctx.fillText(item.value.toString(), x + barWidth / 2, y - 10);\r\n\t\t\t\t\r\n\t\t\t\t// 绘制标签\r\n\t\t\t\tctx.setFillStyle('#666666');\r\n\t\t\t\tctx.setFontSize(12);\r\n\t\t\t\tctx.fillText(item.label, x + barWidth / 2, canvasHeight - padding.bottom + 20);\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 绘制Y轴刻度\r\n\t\t\tconst yStep = maxValue / 5;\r\n\t\t\tfor (let i = 0; i <= 5; i++) {\r\n\t\t\t\tconst y = canvasHeight - padding.bottom - (i / 5) * chartHeight;\r\n\t\t\t\tconst value = Math.round(i * yStep);\r\n\t\t\t\t\r\n\t\t\t\tctx.setFillStyle('#999999');\r\n\t\t\t\tctx.setFontSize(10);\r\n\t\t\t\tctx.setTextAlign('right');\r\n\t\t\t\tctx.fillText(value.toString(), padding.left - 5, y + 3);\r\n\t\t\t\t\r\n\t\t\t\t// 绘制网格线\r\n\t\t\t\tctx.beginPath();\r\n\t\t\t\tctx.setStrokeStyle('#EEEEEE');\r\n\t\t\t\tctx.setLineWidth(0.5);\r\n\t\t\t\tctx.moveTo(padding.left, y);\r\n\t\t\t\tctx.lineTo(canvasWidth - padding.right, y);\r\n\t\t\t\tctx.stroke();\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 执行绘制\r\n\t\t\tctx.draw();\r\n\t\t},\r\n\t\t\r\n\t\t// 初始化饼图\r\n\t\tinitPieChart() {\r\n\t\t\t// 获取设备信息\r\n\t\t\tconst sysInfo = uni.getSystemInfoSync();\r\n\t\t\t// 按照设备像素比计算canvas大小\r\n\t\t\tconst pixelRatio = sysInfo.pixelRatio || 2;\r\n\t\t\tconst canvasWidth = sysInfo.windowWidth * 0.9; // 屏幕宽度的90%\r\n\t\t\tconst canvasHeight = 400 * (sysInfo.windowWidth / 750); // 根据rpx换算为px\r\n\t\t\tconst centerX = canvasWidth / 2;\r\n\t\t\tconst centerY = canvasHeight / 2;\r\n\t\t\tconst radius = Math.min(centerX, centerY) * 0.7;\r\n\t\t\t\r\n\t\t\t// 使用Canvas绘制简单的饼图\r\n\t\t\tconst ctx = uni.createCanvasContext('attendancePieChart', this);\r\n\t\t\t\r\n\t\t\t// 设置画布背景\r\n\t\t\tctx.setFillStyle('#FFFFFF');\r\n\t\t\tctx.fillRect(0, 0, canvasWidth, canvasHeight);\r\n\t\t\t\r\n\t\t\t// 图表数据\r\n\t\t\tconst total = this.statistics.normalDays + this.statistics.lateDays + \r\n\t\t\t\t\t\tthis.statistics.earlyDays + this.statistics.absentDays || 1;\r\n\t\t\t\r\n\t\t\tconst data = [\r\n\t\t\t\t{ label: '正常', value: this.statistics.normalDays, color: '#4cd964' },\r\n\t\t\t\t{ label: '迟到', value: this.statistics.lateDays, color: '#f0ad4e' },\r\n\t\t\t\t{ label: '早退', value: this.statistics.earlyDays, color: '#5bc0de' },\r\n\t\t\t\t{ label: '缺勤', value: this.statistics.absentDays, color: '#dd524d' }\r\n\t\t\t];\r\n\t\t\t\r\n\t\t\t// 只绘制有值的数据项\r\n\t\t\tconst validData = data.filter(item => item.value > 0);\r\n\t\t\t\r\n\t\t\t// 绘制标题\r\n\t\t\tctx.setFillStyle('#333333');\r\n\t\t\tctx.setFontSize(14);\r\n\t\t\tctx.setTextAlign('center');\r\n\t\t\tctx.fillText('考勤类型分布', centerX, 20);\r\n\t\t\t\r\n\t\t\t// 处理空数据的情况\r\n\t\t\tif (validData.length === 0) {\r\n\t\t\t\tctx.setFillStyle('#999999');\r\n\t\t\t\tctx.setFontSize(14);\r\n\t\t\t\tctx.setTextAlign('center');\r\n\t\t\t\tctx.fillText('暂无数据', centerX, centerY);\r\n\t\t\t\tctx.draw();\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 绘制饼图\r\n\t\t\tlet startAngle = 0;\r\n\t\t\tvalidData.forEach(item => {\r\n\t\t\t\tconst percentage = item.value / total;\r\n\t\t\t\tif (percentage <= 0) return; // 跳过零值\r\n\t\t\t\t\r\n\t\t\t\tconst endAngle = startAngle + percentage * 2 * Math.PI;\r\n\t\t\t\t\r\n\t\t\t\t// 绘制扇形\r\n\t\t\t\tctx.beginPath();\r\n\t\t\t\tctx.moveTo(centerX, centerY);\r\n\t\t\t\tctx.arc(centerX, centerY, radius, startAngle, endAngle, false);\r\n\t\t\t\tctx.setFillStyle(item.color);\r\n\t\t\t\tctx.fill();\r\n\t\t\t\t\r\n\t\t\t\t// 计算标签位置\r\n\t\t\t\tconst labelAngle = startAngle + (endAngle - startAngle) / 2;\r\n\t\t\t\tconst labelDistance = radius * 1.2;\r\n\t\t\t\tconst labelX = centerX + Math.cos(labelAngle) * labelDistance;\r\n\t\t\t\tconst labelY = centerY + Math.sin(labelAngle) * labelDistance;\r\n\t\t\t\t\r\n\t\t\t\t// 绘制标签连线\r\n\t\t\t\tif (percentage > 0.05) {\r\n\t\t\t\t\tconst lineEndX = centerX + Math.cos(labelAngle) * radius;\r\n\t\t\t\t\tconst lineEndY = centerY + Math.sin(labelAngle) * radius;\r\n\t\t\t\t\t\r\n\t\t\t\t\tctx.beginPath();\r\n\t\t\t\t\tctx.setStrokeStyle(item.color);\r\n\t\t\t\t\tctx.setLineWidth(1);\r\n\t\t\t\t\tctx.moveTo(lineEndX, lineEndY);\r\n\t\t\t\t\tctx.lineTo(labelX, labelY);\r\n\t\t\t\t\tctx.stroke();\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 绘制标签\r\n\t\t\t\t\tctx.setFillStyle('#333333');\r\n\t\t\t\t\tctx.setFontSize(12);\r\n\t\t\t\t\tctx.setTextAlign('center');\r\n\t\t\t\t\tctx.fillText(`${item.label}: ${Math.round(percentage * 100)}%`, labelX, labelY);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 更新起始角度\r\n\t\t\t\tstartAngle = endAngle;\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 绘制中心洞\r\n\t\t\tctx.beginPath();\r\n\t\t\tctx.arc(centerX, centerY, radius * 0.5, 0, 2 * Math.PI, false);\r\n\t\t\tctx.setFillStyle('#FFFFFF');\r\n\t\t\tctx.fill();\r\n\t\t\t\r\n\t\t\t// 绘制总天数\r\n\t\t\tctx.setFillStyle('#333333');\r\n\t\t\tctx.setFontSize(16);\r\n\t\t\tctx.setTextAlign('center');\r\n\t\t\tctx.fillText(total.toString(), centerX, centerY - 5);\r\n\t\t\tctx.setFontSize(12);\r\n\t\t\tctx.fillText('考勤总天数', centerX, centerY + 15);\r\n\t\t\t\r\n\t\t\t// 执行绘制\r\n\t\t\tctx.draw();\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\t// 页面显示时重新绘制图表\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.initBarChart();\r\n\t\t\t\tthis.initPieChart();\r\n\t\t\t}, 300);\r\n\t\t},\r\n\t\tonResize() {\r\n\t\t\t// 屏幕尺寸变化时重新绘制图表\r\n\t\t\tthis.initBarChart();\r\n\t\t\tthis.initPieChart();\r\n\t\t},\r\n\t\t// 打开日详情弹窗\r\n\t\topenDayDetailPopup(date) {\r\n\t\t\tthis.selectedDate = date;\r\n\t\t\tthis.dayDetailPopupVisible = true;\r\n\t\t\tthis.loadDayDetailData(date);\r\n\t\t\tthis.$refs.dayDetailPopup.open();\r\n\t\t},\r\n\t\t\r\n\t\t// 加载日详情数据\r\n\t\tloadDayDetailData(date) {\r\n\t\t\t// 清空之前的数据\r\n\t\t\tthis.dayDetailData = [];\r\n\t\t\tthis.selectedStaffDetail = null;\r\n\t\t\t\r\n\t\t\t// 模拟请求API数据\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中...'\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 请求数据，根据实际API调整\r\n\t\t\tthis.$http.get('/attendance/daily-detail', {\r\n\t\t\t\tparams: {\r\n\t\t\t\t\tdate: date,\r\n\t\t\t\t\tdeptId: this.currentDept.id\r\n\t\t\t\t}\r\n\t\t\t}).then(res => {\r\n\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\tthis.dayDetailData = res.data;\r\n\t\t\t\t\t// 初始化饼图\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tthis.renderDayPieChart();\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: res.msg || '获取日详情失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}).catch(err => {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '获取日详情失败',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}).finally(() => {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 选择人员详情\r\n\t\tselectStaffDetail(staff) {\r\n\t\t\tthis.selectedStaffDetail = staff;\r\n\t\t},\r\n\t\t\r\n\t\t// 点击饼图触摸事件\r\n\t\ttouchChart(e) {\r\n\t\t\t// 可以根据需要添加饼图的交互效果\r\n\t\t},\r\n\t\t\r\n\t\t// 日历日期点击事件，调用openDayDetailPopup\r\n\t\tdayClick(day) {\r\n\t\t\t// 根据日历组件的事件获取选中的日期，格式化为YYYY-MM-DD\r\n\t\t\tconst date = this.formatDate(day);\r\n\t\t\tthis.openDayDetailPopup(date);\r\n\t\t},\r\n\t\t\r\n\t\t// 格式化日期为YYYY-MM-DD\r\n\t\tformatDate(date) {\r\n\t\t\tconst year = date.getFullYear();\r\n\t\t\tconst month = (date.getMonth() + 1).toString().padStart(2, '0');\r\n\t\t\tconst day = date.getDate().toString().padStart(2, '0');\r\n\t\t\treturn `${year}-${month}-${day}`;\r\n\t\t},\r\n\t\tformatWeek(date) {\r\n\t\t\tconst days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];\r\n\t\t\treturn days[new Date(date).getDay()];\r\n\t\t},\r\n\t\tformatLocation(record) {\r\n\t\t\treturn `${record.latitude.toFixed(6)}, ${record.longitude.toFixed(6)}`;\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.statistics-container {\r\n\tpadding: 20rpx;\r\n\tbackground-color: #f5f5f5;\r\n\tmin-height: 100vh;\r\n}\r\n\r\n// 顶部筛选区域\r\n.filter-section {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tpadding: 20rpx 0;\r\n\t\r\n\t.month-selector {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\t\r\n\t\t.current-month {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tmargin-right: 10rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.month-arrows {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.staff-selector {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tbackground-color: #fff;\r\n\t\tpadding: 10rpx 20rpx;\r\n\t\tborder-radius: 30rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\t\r\n\t\ttext {\r\n\t\t\tmargin-right: 10rpx;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 统计概览卡片\r\n.statistics-cards {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tmargin-bottom: 20rpx;\r\n\t\r\n\t.stat-card {\r\n\t\tflex: 1;\r\n\t\tmargin: 0 10rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 10rpx;\r\n\t\tpadding: 20rpx;\r\n\t\ttext-align: center;\r\n\t\tbox-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);\r\n\t\t\r\n\t\t&:first-child {\r\n\t\t\tmargin-left: 0;\r\n\t\t}\r\n\t\t\r\n\t\t&:last-child {\r\n\t\t\tmargin-right: 0;\r\n\t\t}\r\n\t\t\r\n\t\t.stat-value {\r\n\t\t\tfont-size: 36rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tcolor: #333;\r\n\t\t}\r\n\t\t\r\n\t\t.stat-label {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: #999;\r\n\t\t\tmargin-top: 10rpx;\r\n\t\t}\r\n\t\t\r\n\t\t&:nth-child(1) .stat-value {\r\n\t\t\tcolor: #4cd964;\r\n\t\t}\r\n\t\t\r\n\t\t&:nth-child(2) .stat-value {\r\n\t\t\tcolor: #f0ad4e;\r\n\t\t}\r\n\t\t\r\n\t\t&:nth-child(3) .stat-value {\r\n\t\t\tcolor: #5bc0de;\r\n\t\t}\r\n\t\t\r\n\t\t&:nth-child(4) .stat-value {\r\n\t\t\tcolor: #dd524d;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 日历区域\r\n.main-calendar {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 10rpx;\r\n\tpadding: 20rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);\r\n\t\r\n\t.calendar-header {\r\n\t\tdisplay: flex;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\t\r\n\t\t.header-item {\r\n\t\t\tflex: 1;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #666;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.calendar-days {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\t\r\n\t\t.calendar-day {\r\n\t\t\twidth: calc(100% / 7);\r\n\t\t\theight: 80rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\tposition: relative;\r\n\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t\r\n\t\t\t.day-number {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.current-month {\r\n\t\t\t\t.day-number {\r\n\t\t\t\t\tcolor: #007AFF;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.today {\r\n\t\t\t\t.day-number {\r\n\t\t\t\t\twidth: 60rpx;\r\n\t\t\t\t\theight: 60rpx;\r\n\t\t\t\t\tbackground-color: #007AFF;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.normal {\r\n\t\t\t\t.day-number {\r\n\t\t\t\t\tcolor: #4cd964;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.late {\r\n\t\t\t\t.day-number {\r\n\t\t\t\t\tcolor: #f0ad4e;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.early {\r\n\t\t\t\t.day-number {\r\n\t\t\t\t\tcolor: #5bc0de;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.absent {\r\n\t\t\t\t.day-number {\r\n\t\t\t\t\tcolor: #dd524d;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 添加图表区域\r\n.charts-section {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 10rpx;\r\n\tpadding: 20rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);\r\n\t\r\n\t.section-title {\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t\r\n\t.charts-container {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\t\r\n\t\t.chart-card {\r\n\t\t\twidth: 100%;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\t\r\n\t\t\t.chart-title {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.chart-container {\r\n\t\t\t\theight: 400rpx;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tpadding: 10rpx 0 30rpx 0;\r\n\t\t\t\t\r\n\t\t\t\tcanvas {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 考勤详细记录\r\n.attendance-records {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 10rpx;\r\n\tpadding: 20rpx;\r\n\tbox-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);\r\n\t\r\n\t.record-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\t\r\n\t\ttext {\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t}\r\n\t\t\r\n\t\t.view-all {\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: #007AFF;\r\n\t\t\tfont-weight: normal;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.record-list {\r\n\t\t.record-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\tpadding: 20rpx 0;\r\n\t\t\tborder-bottom: 1rpx solid #f5f5f5;\r\n\t\t\t\r\n\t\t\t&:last-child {\r\n\t\t\t\tborder-bottom: none;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.record-date {\r\n\t\t\t\twidth: 100rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\t\r\n\t\t\t\t.day {\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.week {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\tmargin-top: 5rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.record-time {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t\t\r\n\t\t\t\t.time-item {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.time-label {\r\n\t\t\t\t\t\twidth: 120rpx;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.time-value {\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&.abnormal {\r\n\t\t\t\t\t\t\tcolor: #f0ad4e;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.status-tag {\r\n\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\tpadding: 2rpx 10rpx;\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&.late {\r\n\t\t\t\t\t\t\tbackground-color: #fef0e5;\r\n\t\t\t\t\t\t\tcolor: #f0ad4e;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&.early {\r\n\t\t\t\t\t\t\tbackground-color: #e5f5fa;\r\n\t\t\t\t\t\t\tcolor: #5bc0de;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.record-location {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tmargin-top: 10rpx;\r\n\t\t\t\t\r\n\t\t\t\t.location-label {\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\tmargin-bottom: 5rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.location-value {\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.empty-records {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tpadding: 60rpx 0;\r\n\t\t\r\n\t\timage {\r\n\t\t\twidth: 200rpx;\r\n\t\t\theight: 200rpx;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t}\r\n\t\t\r\n\t\ttext {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #999;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 弹窗样式\r\n.staff-popup {\r\n\tbackground-color: #fff;\r\n\tborder-top-left-radius: 20rpx;\r\n\tborder-top-right-radius: 20rpx;\r\n\tpadding-bottom: 30rpx;\r\n\t\r\n\t.popup-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 30rpx;\r\n\t\tborder-bottom: 1rpx solid #f5f5f5;\r\n\t\t\r\n\t\ttext {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t}\r\n\t\t\r\n\t\t.close-btn {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #007AFF;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.search-box {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tmargin: 20rpx 30rpx;\r\n\t\tpadding: 10rpx 20rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\t\r\n\t\tinput {\r\n\t\t\tflex: 1;\r\n\t\t\theight: 60rpx;\r\n\t\t\tmargin-left: 10rpx;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.staff-list {\r\n\t\tmax-height: 600rpx;\r\n\t\t\r\n\t\t.staff-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 20rpx 30rpx;\r\n\t\t\tborder-bottom: 1rpx solid #f5f5f5;\r\n\t\t\t\r\n\t\t\ttext {\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/* 日考勤详情弹窗样式 */\r\n.day-detail-popup {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 24rpx 24rpx 0 0;\r\n\tpadding: 30rpx;\r\n\tmax-height: 80vh;\r\n\toverflow-y: auto;\r\n}\r\n\r\n.popup-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tmargin-bottom: 30rpx;\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.close-btn {\r\n\tcolor: #999;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.day-stats-chart {\r\n\twidth: 100%;\r\n\theight: 350rpx;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.day-stats-summary {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tmargin-bottom: 30rpx;\r\n\tborder-radius: 10rpx;\r\n\tbackground-color: #f8f8f8;\r\n\tpadding: 20rpx 10rpx;\r\n}\r\n\r\n.stat-item {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\twidth: 25%;\r\n}\r\n\r\n.stat-value {\r\n\tfont-size: 34rpx;\r\n\tfont-weight: bold;\r\n\tmargin-bottom: 6rpx;\r\n}\r\n\r\n.stat-item.normal .stat-value {\r\n\tcolor: #4cd964;\r\n}\r\n\r\n.stat-item.late .stat-value {\r\n\tcolor: #f0ad4e;\r\n}\r\n\r\n.stat-item.early .stat-value {\r\n\tcolor: #f56c6c;\r\n}\r\n\r\n.stat-item.absent .stat-value {\r\n\tcolor: #dd524d;\r\n}\r\n\r\n.stat-label {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.section-title {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n\tmargin: 20rpx 0;\r\n\tcolor: #333;\r\n\tborder-left: 8rpx solid #007aff;\r\n\tpadding-left: 20rpx;\r\n}\r\n\r\n.staff-detail-section {\r\n\tmargin-bottom: 30rpx;\r\n\tbackground-color: #f8f8f8;\r\n\tborder-radius: 10rpx;\r\n\tpadding: 20rpx;\r\n}\r\n\r\n.staff-list-section {\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.staff-category {\r\n\tmargin-bottom: 20rpx;\r\n\tborder-radius: 10rpx;\r\n\toverflow: hidden;\r\n\tbackground-color: #f8f8f8;\r\n}\r\n\r\n.category-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tpadding: 15rpx 20rpx;\r\n\tfont-size: 26rpx;\r\n\tcolor: #fff;\r\n}\r\n\r\n.category-header.normal {\r\n\tbackground-color: #4cd964;\r\n}\r\n\r\n.category-header.late {\r\n\tbackground-color: #f0ad4e;\r\n}\r\n\r\n.category-header.early {\r\n\tbackground-color: #f56c6c;\r\n}\r\n\r\n.category-header.absent {\r\n\tbackground-color: #dd524d;\r\n}\r\n\r\n.staff-items {\r\n\tpadding: 10rpx 20rpx;\r\n}\r\n\r\n.staff-item {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tpadding: 15rpx 0;\r\n\tborder-bottom: 1px solid #eee;\r\n}\r\n\r\n.staff-item:last-child {\r\n\tborder-bottom: none;\r\n}\r\n\r\n.staff-name {\r\n\tfont-size: 26rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n.staff-time {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.empty-day-detail {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 50rpx 0;\r\n}\r\n\r\n.empty-day-detail image {\r\n\twidth: 200rpx;\r\n\theight: 200rpx;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.empty-day-detail text {\r\n\tfont-size: 28rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n/* 现有的日详情内容样式 */\r\n.day-detail-content {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 10rpx;\r\n}\r\n\r\n.detail-item {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tpadding: 15rpx 0;\r\n\tborder-bottom: 1px solid #eee;\r\n}\r\n\r\n.detail-label {\r\n\tcolor: #666;\r\n\tfont-size: 26rpx;\r\n}\r\n\r\n.detail-value {\r\n\tcolor: #333;\r\n\tfont-size: 26rpx;\r\n}\r\n\r\n.detail-value-box {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.status-tag {\r\n\tdisplay: inline-block;\r\n\tpadding: 2rpx 10rpx;\r\n\tfont-size: 22rpx;\r\n\tborder-radius: 6rpx;\r\n\tmargin-left: 10rpx;\r\n\tcolor: #fff;\r\n}\r\n\r\n.status-tag.late {\r\n\tbackground-color: #f0ad4e;\r\n}\r\n\r\n.status-tag.early {\r\n\tbackground-color: #f56c6c;\r\n}\r\n\r\n.detail-photos {\r\n\tmargin-top: 15rpx;\r\n}\r\n\r\n.photo-list {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tmargin-top: 10rpx;\r\n}\r\n\r\n.photo-list image {\r\n\twidth: 160rpx;\r\n\theight: 160rpx;\r\n\tmargin: 10rpx;\r\n\tborder-radius: 8rpx;\r\n}\r\n</style> ", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/attendance/statistics.vue'\nwx.createPage(MiniProgramPage)"], "names": ["attendanceApi", "uni", "userApi", "res"], "mappings": ";;;;AA8YA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,cAAa,oBAAI,KAAM,GAAC,YAAa;AAAA,MACrC,eAAc,oBAAI,QAAO,SAAS,IAAI;AAAA,MACtC,UAAU,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,MAC5C,cAAc,CAAE;AAAA,MAChB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,WAAW,CAAE;AAAA,MACb,WAAW;AAAA,MACX,YAAY;AAAA,QACX,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,WAAW;AAAA,QACX,YAAY;AAAA,MACZ;AAAA,MACD,SAAS,CAAE;AAAA,MACX,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,YAAY;AAAA,QACX,WAAW;AAAA,QACX,SAAS;AAAA,QACT,MAAM;AAAA,QACN,QAAQ;AAAA,MACR;AAAA,MACD,qBAAqB,CAAE;AAAA,MACvB,oBAAoB;AAAA,MACpB,mBAAmB;AAAA,MACnB,eAAe;AAAA,MACf,YAAY,CAAE;AAAA,MACd,UAAU;AAAA,QACT,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACR;AAAA,MACD,cAAc;AAAA,MACd,uBAAuB;AAAA,MACvB,eAAe,CAAE;AAAA,MACjB,qBAAqB;AAAA;EAEtB;AAAA,EACD,UAAU;AAAA,IACT,oBAAoB;AACnB,UAAI,CAAC,KAAK;AAAe,eAAO,KAAK;AAErC,aAAO,KAAK,UAAU;AAAA,QAAO;;AAC5B,uBAAM,KAAK,SAAS,KAAK,aAAa,OACtC,WAAM,eAAN,mBAAkB,SAAS,KAAK;AAAA;AAAA;IAEjC;AAAA,IACD,yBAAyB;AACxB,UAAI,CAAC,KAAK;AAAoB,eAAO,KAAK;AAE1C,aAAO,KAAK,UAAU;AAAA,QAAO;;AAC5B,uBAAM,KAAK,SAAS,KAAK,kBAAkB,OAC3C,WAAM,eAAN,mBAAkB,SAAS,KAAK;AAAA;AAAA;IAEjC;AAAA,IACD,cAAc;AACb,aAAO,KAAK,cAAc,OAAO,UAAQ,CAAC,KAAK,UAAU,CAAC,KAAK,WAAW,CAAC,KAAK,QAAQ,EAAE;AAAA,IAC1F;AAAA,IACD,YAAY;AACX,aAAO,KAAK,cAAc,OAAO,UAAQ,KAAK,MAAM,EAAE;AAAA,IACtD;AAAA,IACD,aAAa;AACZ,aAAO,KAAK,cAAc,OAAO,UAAQ,KAAK,OAAO,EAAE;AAAA,IACvD;AAAA,IACD,cAAc;AACb,aAAO,KAAK,cAAc,OAAO,UAAQ,KAAK,QAAQ,EAAE;AAAA,IACxD;AAAA,IACD,cAAc;AACb,aAAO,KAAK,cAAc,OAAO,UAAQ,CAAC,KAAK,UAAU,CAAC,KAAK,WAAW,CAAC,KAAK,QAAQ;AAAA,IACxF;AAAA,IACD,YAAY;AACX,aAAO,KAAK,cAAc,OAAO,UAAQ,KAAK,MAAM;AAAA,IACpD;AAAA,IACD,aAAa;AACZ,aAAO,KAAK,cAAc,OAAO,UAAQ,KAAK,OAAO;AAAA,IACrD;AAAA,IACD,cAAc;AACb,aAAO,KAAK,cAAc,OAAO,UAAQ,KAAK,QAAQ;AAAA,IACvD;AAAA,EACA;AAAA,EACD,SAAS;AACR,SAAK,iBAAgB;AACrB,SAAK,cAAa;AAClB,SAAK,mBAAkB;AAAA,EACvB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,mBAAmB;AAClB,YAAM,OAAO,KAAK;AAClB,YAAM,QAAQ,KAAK;AAGnB,YAAM,WAAW,IAAI,KAAK,MAAM,QAAQ,GAAG,CAAC,EAAE;AAE9C,YAAM,cAAc,IAAI,KAAK,MAAM,OAAO,CAAC,EAAE;AAE7C,YAAM,kBAAkB,IAAI,KAAK,MAAM,QAAQ,GAAG,CAAC,EAAE;AAErD,YAAM,OAAO,CAAA;AAGb,eAAS,IAAI,WAAW,GAAG,KAAK,GAAG,KAAK;AACvC,cAAM,YAAY,UAAU,IAAI,KAAK,QAAQ;AAC7C,cAAM,WAAW,UAAU,IAAI,OAAO,IAAI;AAC1C,aAAK,KAAK;AAAA,UACT,KAAK,kBAAkB;AAAA,UACvB,OAAO;AAAA,UACP,MAAM;AAAA,UACN,gBAAgB;AAAA,UAChB,SAAS;AAAA,UACT,QAAQ;AAAA,QACT,CAAC;AAAA,MACF;AAGA,YAAM,QAAQ,oBAAI;AAClB,YAAM,YAAY,MAAM;AACxB,YAAM,aAAa,MAAM,SAAQ,IAAK;AACtC,YAAM,YAAY,MAAM;AAExB,eAAS,IAAI,GAAG,KAAK,aAAa,KAAK;AACtC,aAAK,KAAK;AAAA,UACT,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA,gBAAgB;AAAA,UAChB,SAAS,MAAM,aAAa,UAAU,cAAc,SAAS;AAAA,UAC7D,QAAQ;AAAA;AAAA,QACT,CAAC;AAAA,MACF;AAGA,YAAM,gBAAgB,KAAK,KAAK;AAChC,eAAS,IAAI,GAAG,KAAK,eAAe,KAAK;AACxC,cAAM,YAAY,UAAU,KAAK,IAAI,QAAQ;AAC7C,cAAM,WAAW,UAAU,KAAK,OAAO,IAAI;AAC3C,aAAK,KAAK;AAAA,UACT,KAAK;AAAA,UACL,OAAO;AAAA,UACP,MAAM;AAAA,UACN,gBAAgB;AAAA,UAChB,SAAS;AAAA,UACT,QAAQ;AAAA,QACT,CAAC;AAAA,MACF;AAEA,WAAK,eAAe;AAAA,IACpB;AAAA;AAAA,IAGD,WAAW,YAAY;AACtB,UAAI,CAAC;AAAY,eAAO;AACxB,YAAM,OAAO,IAAI,KAAK,UAAU;AAChC,YAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,YAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,aAAO,GAAG,KAAK,IAAI,GAAG;AAAA,IACtB;AAAA;AAAA,IAGD,WAAW,YAAY;AACtB,UAAI,CAAC;AAAY,eAAO;AACxB,YAAM,OAAO,IAAI,KAAK,UAAU;AAChC,YAAM,WAAW,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAC1D,aAAO,SAAS,KAAK,OAAM,CAAE;AAAA,IAC7B;AAAA;AAAA,IAGD,eAAe,QAAQ;AACtB,UAAI,CAAC,UAAU,CAAC,OAAO,YAAY,CAAC,OAAO;AAAW,eAAO;AAC7D,aAAO,GAAG,OAAO,SAAS,QAAQ,CAAC,CAAC,KAAK,OAAO,UAAU,QAAQ,CAAC,CAAC;AAAA,IACpE;AAAA;AAAA,IAGD,gBAAgB;AACfA,gBAAAA,cAAc,YAAW,EAAG,KAAK,SAAO;AACvC,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAEjC,cAAI,IAAI,KAAK,QAAQ,MAAM,QAAQ,IAAI,KAAK,KAAK,SAAS,GAAG;AAC5D,iBAAK,YAAY,IAAI,KAAK,KAAK;AAAA,UAChC,WAAW,MAAM,QAAQ,IAAI,KAAK,SAAS,GAAG;AAC7C,iBAAK,YAAY,IAAI,KAAK;AAAA,UAC3B,WAAW,MAAM,QAAQ,IAAI,IAAI,GAAG;AACnC,iBAAK,YAAY,IAAI;AAAA,iBACf;AACNC,0BAAa,MAAA,MAAA,QAAA,0CAAA,eAAe,GAAG;AAE/B,iBAAK,kBAAiB;AAAA,UACvB;AAAA,eACM;AAEN,eAAK,kBAAiB;AAAA,QACvB;AAAA,MACD,CAAC,EAAE,MAAM,SAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,0CAAc,aAAa,GAAG;AAE9B,aAAK,kBAAiB;AAAA,MACvB,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB;AACnBC,gBAAAA,QAAQ,iBAAgB,EAAG,KAAK,aAAW;AAC1C,YAAI,QAAQ,SAAS,OAAO,QAAQ,MAAM;AACzC,cAAI,MAAM,QAAQ,QAAQ,IAAI,GAAG;AAChC,iBAAK,YAAY,QAAQ;AAAA,UAC1B,WAAW,QAAQ,KAAK,QAAQ,MAAM,QAAQ,QAAQ,KAAK,KAAK,SAAS,GAAG;AAC3E,iBAAK,YAAY,QAAQ,KAAK,KAAK;AAAA,iBAC7B;AACND,0BAAAA,MAAa,MAAA,QAAA,0CAAA,YAAY;AACzB,iBAAK,YAAY;UAClB;AAAA,QACD;AAAA,MACD,CAAC,EAAE,MAAM,aAAW;AACnBA,sBAAc,MAAA,MAAA,SAAA,0CAAA,aAAa,OAAO;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,YAAY;AACX,UAAI,KAAK,iBAAiB,GAAG;AAC5B,aAAK,eAAe;AACpB,aAAK,eAAe;AAAA,aACd;AACN,aAAK,gBAAgB;AAAA,MACtB;AACA,WAAK,iBAAgB;AACrB,WAAK,mBAAkB;AAAA,IACvB;AAAA;AAAA,IAGD,YAAY;AACX,UAAI,KAAK,iBAAiB,IAAI;AAC7B,aAAK,eAAe;AACpB,aAAK,eAAe;AAAA,aACd;AACN,aAAK,gBAAgB;AAAA,MACtB;AACA,WAAK,iBAAgB;AACrB,WAAK,mBAAkB;AAAA,IACvB;AAAA;AAAA,IAGD,oBAAoB;AACnB,WAAK,MAAM,WAAW;IACtB;AAAA;AAAA,IAGD,qBAAqB;AACpB,WAAK,MAAM,WAAW;IACtB;AAAA;AAAA,IAGD,YAAY,OAAO;AAClB,WAAK,gBAAgB;AACrB,WAAK,mBAAkB;AACvB,WAAK,mBAAkB;AAAA,IACvB;AAAA;AAAA,IAGD,qBAAqB;AACpB,WAAK,YAAY;AACjBA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACR,CAAC;AAGD,YAAM,SAAS;AAAA,QACd,MAAM,KAAK;AAAA,QACX,OAAO,KAAK;AAAA,QACZ,KAAK,KAAK,cAAc,KAAK,YAAY,MAAM;AAAA;AAIhD,UAAI,KAAK,eAAe;AACvB,eAAO,SAAS,KAAK,cAAc;AAAA,MACpC;AAGAD,gBAAAA,cAAc,SAAS,MAAM,EAAE,KAAK,SAAO;AAC1C,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAEjC,cAAI,cAAc,IAAI;AAGtB,cAAI,IAAI,KAAK,QAAQ,OAAO,IAAI,KAAK,SAAS,UAAU;AACvD,0BAAc,IAAI,KAAK;AAAA,UACxB;AAGA,gBAAM,UAAU,YAAY,WAAW;AAGvC,eAAK,aAAa;AAAA,YACjB,YAAY,QAAQ,UAAU;AAAA,YAC9B,UAAU,QAAQ,QAAQ;AAAA,YAC1B,WAAW,QAAQ,SAAS;AAAA,YAC5B,YAAY,QAAQ,UAAU;AAAA;AAI/B,qBAAW,MAAM;AAChB,iBAAK,aAAY;AACjB,iBAAK,aAAY;AAAA,UACjB,GAAE,GAAG;AAGN,cAAI,YAAY,OAAO;AACtB,iBAAK,qBAAqB,YAAY,KAAK;AAAA,UAC5C;AAAA,QACD;AAGA,eAAOA,UAAa,cAAC,WAAW,MAAM;AAAA,OACtC,EAAE,KAAK,SAAO;AACd,YAAI,IAAI,SAAS,KAAK;AACrB,cAAI,cAAc,IAAI;AAGtB,cAAI,IAAI,QAAQ,IAAI,KAAK,MAAM;AAC9B,0BAAc,IAAI,KAAK;AAAA,UACxB;AAGA,cAAI,eAAe,YAAY,SAAS;AACvC,iBAAK,UAAU,KAAK,yBAAyB,YAAY,OAAO;AAAA,UACjE,WAAW,eAAe,YAAY,MAAM;AAC3C,iBAAK,UAAU,KAAK,yBAAyB,YAAY,IAAI;AAAA,UAC9D,WAAW,MAAM,QAAQ,WAAW,GAAG;AACtC,iBAAK,UAAU,KAAK,yBAAyB,WAAW;AAAA,UACzD,WAAW,eAAe,OAAO,gBAAgB,YAAY,CAAC,MAAM,QAAQ,WAAW,GAAG;AAEzF,iBAAK,UAAU,KAAK,yBAAyB,WAAW;AAAA,iBAClD;AACNC,0BAAa,MAAA,MAAA,QAAA,0CAAA,kBAAkB,IAAI,IAAI;AACvC,iBAAK,UAAU;UAChB;AAAA,eACM;AACN,eAAK,UAAU;QAChB;AAAA,MACD,CAAC,EAAE,MAAM,SAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,0CAAc,aAAa,GAAG;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF,CAAC,EAAE,QAAQ,MAAM;AAChB,aAAK,YAAY;AACjBA,sBAAG,MAAC,YAAW;AAAA,MAChB,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,yBAAyB,SAAS;AACjC,UAAI,CAAC,MAAM,QAAQ,OAAO,KAAK,QAAQ,WAAW,GAAG;AACpD,eAAO;MACR;AAGA,UAAI,CAAC,MAAM,QAAQ,OAAO,KAAK,OAAO,YAAY,UAAU;AAC3D,cAAM,cAAc,CAAA;AACpB,mBAAW,QAAQ,SAAS;AAC3B,cAAI,QAAQ,eAAe,IAAI,GAAG;AAEjC,kBAAM,UAAU,IAAI,KAAK,IAAI;AAC7B,kBAAM,QAAQ,OAAO,QAAQ,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AAC5D,kBAAM,MAAM,OAAO,QAAQ,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AACrD,kBAAM,WAAW,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAC1D,kBAAM,OAAO,SAAS,QAAQ,OAAQ,CAAA;AAEtC,kBAAM,SAAS,QAAQ,IAAI;AAC3B,wBAAY,KAAK;AAAA,cAChB;AAAA,cACA,KAAK,GAAG,KAAK,IAAI,GAAG;AAAA,cACpB;AAAA,cACA,aAAa,OAAO,eAAe;AAAA,cACnC,eAAe,OAAO,iBAAiB;AAAA,cACvC,cAAc,OAAO,gBAAgB;AAAA,cACrC,gBAAgB,OAAO,kBAAkB;AAAA,cACzC,UAAU,OAAO,YAAY;AAAA,YAC9B,CAAC;AAAA,UACF;AAAA,QACD;AACA,eAAO,YAAY,MAAM,GAAG,CAAC;AAAA,MAC9B;AAIA,YAAM,cAAc,QAAQ,CAAC;AAG7B,UAAI,YAAY,gBAAgB,UAAa,YAAY,iBAAiB,QAAW;AACpF,eAAO,QAAQ,IAAI,UAAQ;AAC1B,iBAAO;AAAA,YACN,MAAM,KAAK,QAAQ,KAAK,WAAW,KAAK,SAAS;AAAA,YACjD,MAAM,KAAK,QAAQ,KAAK,WAAW,KAAK,SAAS;AAAA,YACjD,aAAa,KAAK,eAAe;AAAA,YACjC,cAAc,KAAK,gBAAgB;AAAA,YACnC,eAAe,KAAK,iBAAiB;AAAA,YACrC,gBAAgB,KAAK,kBAAkB;AAAA,YACvC,UAAU,KAAK,YAAY,KAAK,eAAe,IAAI;AAAA;QAEpD,CAAA,EAAE,MAAM,GAAG,CAAC;AAAA,MACd,WAIS,YAAY,aAAa,YAAY,WAAW;AAExD,cAAM,gBAAgB,CAAA;AAEtB,gBAAQ,QAAQ,YAAU;AACzB,gBAAM,OAAO,IAAI,KAAK,OAAO,SAAS;AACtC,gBAAM,UAAU,KAAK,YAAW,EAAG,MAAM,GAAG,EAAE,CAAC;AAE/C,cAAI,CAAC,cAAc,OAAO,GAAG;AAC5B,0BAAc,OAAO,IAAI;AAAA,cACxB,MAAM,KAAK,WAAW,OAAO,SAAS;AAAA,cACtC,MAAM,KAAK,WAAW,OAAO,SAAS;AAAA,cACtC,aAAa;AAAA,cACb,cAAc;AAAA,cACd,eAAe;AAAA,cACf,gBAAgB;AAAA,cAChB,UAAU,OAAO,YAAY,KAAK,eAAe,MAAM;AAAA;UAEzD;AAGA,cAAI,OAAO,cAAc,WAAW;AACnC,0BAAc,OAAO,EAAE,cAAc,KAAK,WAAW,OAAO,SAAS;AACrE,0BAAc,OAAO,EAAE,gBAAgB,OAAO;AAAA,UAC/C,WAAW,OAAO,cAAc,YAAY;AAC3C,0BAAc,OAAO,EAAE,eAAe,KAAK,WAAW,OAAO,SAAS;AACtE,0BAAc,OAAO,EAAE,iBAAiB,OAAO;AAAA,UAChD;AAAA,QACD,CAAC;AAGD,eAAO,OAAO,OAAO,aAAa,EAChC,KAAK,CAAC,GAAG,MAAM,IAAI,KAAK,EAAE,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI,CAAC,EAClD,MAAM,GAAG,CAAC;AAAA,MACb,OAGK;AACJA,oFAAa,uBAAuB,WAAW;AAC/C,eAAO,QAAQ,IAAI,YAAU;AAC5B,iBAAO;AAAA,YACN,MAAM,OAAO,QAAQ,KAAK,WAAW,OAAO,aAAa,OAAO,UAAU;AAAA,YAC1E,MAAM,OAAO,QAAQ,KAAK,WAAW,OAAO,aAAa,OAAO,UAAU;AAAA,YAC1E,aAAa,OAAO,eAAe;AAAA,YACnC,cAAc,OAAO,gBAAgB;AAAA,YACrC,eAAe,OAAO,iBAAiB;AAAA,YACvC,gBAAgB,OAAO,kBAAkB;AAAA,YACzC,UAAU,OAAO,YAAY,KAAK,eAAe,MAAM;AAAA;QAExD,CAAA,EAAE,MAAM,GAAG,CAAC;AAAA,MACd;AAAA,IACA;AAAA;AAAA,IAGD,qBAAqB,WAAW;AAE/B,UAAI,CAAC,aAAa,CAAC,UAAU;AAAO;AAGpC,UAAI,UAAU,eAAe,MAAM,QAAQ,UAAU,WAAW,GAAG;AAClE,aAAK,aAAa,QAAQ,SAAO;AAChC,cAAI,IAAI,gBAAgB;AACvB,kBAAM,WAAW,IAAI,MAAM;AAC3B,gBAAI,YAAY,KAAK,WAAW,UAAU,YAAY,QAAQ;AAE7D,kBAAI,SAAS,UAAU,YAAY,QAAQ,KAAK;AAAA,YACjD;AAAA,UACD;AAAA,QACD,CAAC;AACD;AAAA,MACD;AAGA,UAAI,CAAC,UAAU;AAAQ;AAEvB,YAAM,mBAAmB,UAAU,OAAO,KAAK,OAAK,EAAE,SAAS,SAAS,EAAE,SAAS,YAAY;AAC/F,YAAM,aAAa,UAAU,OAAO,KAAK,OAAK,EAAE,SAAS,SAAS,EAAE,SAAS,MAAM;AACnF,YAAM,eAAe,UAAU,OAAO,KAAK,OAAK,EAAE,SAAS,SAAS,EAAE,SAAS,QAAQ;AACvF,YAAM,cAAc,UAAU,OAAO,KAAK,OAAK,EAAE,SAAS,SAAS,EAAE,SAAS,OAAO;AAGrF,UAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,gBAAgB,CAAC;AAAa;AAGvE,WAAK,aAAa,QAAQ,SAAO;AAChC,YAAI,IAAI,gBAAgB;AACvB,gBAAM,WAAW,IAAI,MAAM;AAG3B,cAAI,YAAY,KAAK,WAAW,UAAU,MAAM,QAAQ;AAEvD,gBAAI,gBAAgB,aAAa,KAAK,QAAQ,IAAI,GAAG;AACpD,kBAAI,SAAS;AAAA,uBACH,cAAc,WAAW,KAAK,QAAQ,IAAI,GAAG;AACvD,kBAAI,SAAS;AAAA,uBACH,eAAe,YAAY,KAAK,QAAQ,IAAI,GAAG;AACzD,kBAAI,SAAS;AAAA,uBACH,oBAAoB,iBAAiB,KAAK,QAAQ,IAAI,GAAG;AACnE,kBAAI,SAAS;AAAA,mBACP;AACN,kBAAI,SAAS;AAAA,YACd;AAAA,UACD;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,YAAY;AACtB,UAAI,CAAC;AAAY,eAAO;AAExB,UAAI;AACH,cAAM,OAAO,IAAI,KAAK,UAAU;AAChC,cAAM,QAAQ,OAAO,KAAK,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AACrD,cAAM,UAAU,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACzD,eAAO,GAAG,KAAK,IAAI,OAAO;AAAA,MAC3B,SAAS,GAAG;AACX,eAAO;AAAA,MACR;AAAA,IACA;AAAA;AAAA,IAGD,cAAc,KAAK;AAClB,UAAI,CAAC,IAAI;AAAgB;AAEzB,WAAK,cAAc;AACnB,WAAK,eAAe,GAAG,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,GAAG;AAGvD,YAAM,SAAS;AAAA,QACd,MAAM,IAAI;AAAA,QACV,OAAO,IAAI;AAAA,QACX,KAAK,IAAI;AAAA;AAAA;AAIV,WAAK,YAAY;AACjBA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACR,CAAC;AAGDD,gBAAAA,cAAc,WAAW,MAAM,EAAE,KAAK,SAAO;AAC5C,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAEjC,cAAI,UAAU,CAAA;AAGd,cAAI,IAAI,KAAK,SAAS;AACrB,sBAAU,IAAI,KAAK;AAAA,qBACT,IAAI,KAAK,MAAM;AACzB,sBAAU,IAAI,KAAK;AAAA,UACpB,WAAW,MAAM,QAAQ,IAAI,IAAI,GAAG;AACnC,sBAAU,IAAI;AAAA,UACf,WAAW,IAAI,KAAK,SAAS,IAAI,KAAK,KAAK,WAAW,IAAI,KAAK,KAAK,OAAO;AAC1E,sBAAU,IAAI,KAAK,KAAK,WAAW,IAAI,KAAK,KAAK;AAAA,qBACvC,OAAO,IAAI,SAAS,YAAY,CAAC,MAAM,QAAQ,IAAI,IAAI,GAAG;AAElD,kBAAM,cAAc,IAAI;AACxB,sBAAU,CAAA;AACV,uBAAW,OAAO,aAAa;AAC3B,kBAAI,YAAY,eAAe,GAAG,GAAG;AAEjC,sBAAM,SAAS,YAAY,GAAG;AAC9B,oBAAI,UAAU,OAAO,WAAW,UAAU;AAEtC,yBAAO,SAAS,OAAO,UAAU;AACjC,yBAAO,WAAW,OAAO,YAAY,OAAO,QAAQ;AACpD,0BAAQ,KAAK,MAAM;AAAA,gBACvB;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AAGf,eAAK,aAAa;AAGlB,gBAAM,eAAe,CAAA;AAErB,kBAAQ,QAAQ,YAAU;;AAEzB,kBAAM,WAAS,YAAO,SAAP,mBAAa,OAAM,OAAO,UAAU,OAAO,MAAM;AAChE,kBAAM,aAAW,YAAO,SAAP,mBAAa,SAAQ,OAAO,YAAY,OAAO,aAAa,OAAO,QAAQ;AAE5F,gBAAI,CAAC,aAAa,MAAM,GAAG;AAC1B,2BAAa,MAAM,IAAI;AAAA,gBACtB,IAAI;AAAA,gBACJ,MAAM;AAAA,gBACN,cAAY,YAAO,SAAP,mBAAa,eAAc,OAAO,cAAc;AAAA,gBAC5D,aAAa;AAAA,gBACb,cAAc;AAAA,gBACd,QAAQ;AAAA,gBACR,SAAS;AAAA,gBACT,UAAU;AAAA,gBACV,WAAW;AAAA;YAEb;AAGA,gBAAI,OAAO,cAAc,WAAW;AACnC,2BAAa,MAAM,EAAE,cAAc,KAAK,WAAW,OAAO,SAAS;AACnE,2BAAa,MAAM,EAAE,SAAS,OAAO,WAAW;AAAA,YACjD,WAAW,OAAO,cAAc,YAAY;AAC3C,2BAAa,MAAM,EAAE,eAAe,KAAK,WAAW,OAAO,SAAS;AACpE,2BAAa,MAAM,EAAE,UAAU,OAAO,WAAW;AAAA,YAChD,WAAS,OAAO,eAAe,OAAO,cAAc;AAEhC,kBAAI,OAAO,aAAa;AACpB,6BAAa,MAAM,EAAE,cAAc,KAAK,WAAW,OAAO,WAAW;AACrE,6BAAa,MAAM,EAAE,SAAS,OAAO,kBAAkB;AAAA,cAC3D;AACA,kBAAI,OAAO,cAAc;AACrB,6BAAa,MAAM,EAAE,eAAe,KAAK,WAAW,OAAO,YAAY;AACvE,6BAAa,MAAM,EAAE,UAAU,OAAO,mBAAmB;AAAA,cAC7D;AAAA,YACJ;AAGlB,gBAAI,OAAO,WAAW,YAAa,OAAO,kBAAkB,YAAY,OAAO,mBAAmB,UAAW;AAC5G,2BAAa,MAAM,EAAE,WAAW;AAAA,YACjC;AAAA,UACD,CAAC;AAGD,iBAAO,OAAO,YAAY,EAAE,QAAQ,UAAQ;AAC3C,gBAAI,KAAK,eAAe,KAAK,gBACR,KAAK,gBAAgB,SAAS,KAAK,iBAAiB,OAAO;AAE/E,oBAAM,gBAAgB,QAAQ;AAAA,gBAAK;;AACjC,mCAAE,SAAF,mBAAQ,QAAO,KAAK,MAAM,EAAE,WAAW,KAAK,OAAO,EAAE,cAAc;AAAA;AAAA;AAErE,oBAAM,iBAAiB,QAAQ;AAAA,gBAAK;;AAClC,mCAAE,SAAF,mBAAQ,QAAO,KAAK,MAAM,EAAE,WAAW,KAAK,OAAO,EAAE,cAAc;AAAA;AAAA;AAGrE,kBAAI,iBAAiB,gBAAgB;AACpC,qBAAK,YAAY,KAAK,mBAAmB,cAAc,WAAW,eAAe,SAAS;AAAA,cAC3F,WAAW,QAAQ,KAAK,OAAK,EAAE,WAAW,KAAK,MAAM,EAAE,eAAe,EAAE,YAAY,GAAG;AAE9D,sBAAM,SAAS,QAAQ,KAAK,OAAK,EAAE,WAAW,KAAK,EAAE;AACrD,qBAAK,YAAY,KAAK,mBAAmB,OAAO,aAAa,OAAO,YAAY;AAAA,cACpF;AAAA,YACtB;AAAA,UACD,CAAC;AAID,cAAI,KAAK,aAAa,KAAK,UAAU,SAAS,GAAG;AAC9B,iBAAK,UAAU,QAAQ,WAAS;AAC5B,oBAAM,SAAS,MAAM;AAErB,kBAAI,UAAU,CAAC,aAAa,MAAM,GAAG;AACjC,6BAAa,MAAM,IAAI;AAAA,kBACnB,IAAI;AAAA,kBACJ,MAAM,MAAM,QAAQ;AAAA,kBACpB,YAAY,MAAM,cAAc;AAAA,kBAChC,aAAa;AAAA,kBACb,cAAc;AAAA,kBACd,QAAQ;AAAA,kBACR,SAAS;AAAA,kBACT,UAAU;AAAA,kBACV,WAAW;AAAA;cAEnB;AAAA,YACJ,CAAC;AAAA,UACL;AAGf,eAAK,gBAAgB,OAAO,OAAO,YAAY;AAG/C,eAAK,WAAW;AAAA,YACf,QAAQ,KAAK,cAAc,OAAO,UAAQ,CAAC,KAAK,UAAU,CAAC,KAAK,WAAW,CAAC,KAAK,QAAQ,EAAE;AAAA,YAC3F,MAAM,KAAK,cAAc,OAAO,UAAQ,KAAK,MAAM,EAAE;AAAA,YACrD,OAAO,KAAK,cAAc,OAAO,UAAQ,KAAK,OAAO,EAAE;AAAA,YACvD,QAAQ,KAAK,cAAc,OAAO,UAAQ,KAAK,QAAQ,EAAE;AAAA;AAI1D,qBAAW,MAAM;AAChB,iBAAK,kBAAiB;AAAA,UACtB,GAAE,GAAG;AAGN,cAAI,KAAK,eAAe;AAEvB,kBAAM,cAAc,KAAK,cAAc;AAAA,cAAK,OAC3C,EAAE,MAAM,KAAK,cAAc;AAAA;AAG5B,gBAAI,aAAa;AAChB,mBAAK,sBAAsB;AAAA,mBACrB;AACN,mBAAK,sBAAsB;AAAA,YAC5B;AAAA,iBACM;AAEN,iBAAK,sBAAsB;AAAA,UAC5B;AAAA,eACM;AACN,eAAK,aAAa;AAClB,eAAK,gBAAgB;AACrB,eAAK,WAAW;AAAA,YACf,QAAQ;AAAA,YACR,MAAM;AAAA,YACN,OAAO;AAAA,YACP,QAAQ;AAAA;AAET,eAAK,sBAAsB;AAAA,QAC5B;AAEA,aAAK,MAAM,eAAe;MAC3B,CAAC,EAAE,MAAM,SAAO;AACfC,sBAAA,MAAA,MAAA,SAAA,2CAAc,aAAa,GAAG;AAE9B,aAAK,aAAa;AAClB,aAAK,gBAAgB;AACrB,aAAK,WAAW;AAAA,UACf,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,OAAO;AAAA,UACP,QAAQ;AAAA;AAET,aAAK,sBAAsB;AAC3B,aAAK,MAAM,eAAe;MAC3B,CAAC,EAAE,QAAQ,MAAM;AAChB,aAAK,YAAY;AACjBA,sBAAG,MAAC,YAAW;AAAA,MAChB,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB;AACnB,UAAI,KAAK,cAAc,WAAW;AAAG;AAErC,YAAM,MAAMA,cAAAA,MAAI,oBAAoB,oBAAoB;AAGxD,YAAM,UAAUA,oBAAI;AACpB,YAAM,cAAc,QAAQ;AAG5B,YAAM,UAAU;AAAA,QACf,EAAE,MAAM,MAAM,OAAO,KAAK,aAAa,OAAO,UAAW;AAAA,QACzD,EAAE,MAAM,MAAM,OAAO,KAAK,WAAW,OAAO,UAAW;AAAA,QACvD,EAAE,MAAM,MAAM,OAAO,KAAK,YAAY,OAAO,UAAW;AAAA,QACxD,EAAE,MAAM,MAAM,OAAO,KAAK,aAAa,OAAO,UAAU;AAAA;AAIzD,YAAM,eAAe,QAAQ,OAAO,UAAQ,KAAK,QAAQ,CAAC;AAE1D,UAAI,aAAa,WAAW;AAAG;AAG/B,YAAM,cAAc,cAAc;AAClC,YAAM,eAAe;AACrB,YAAM,UAAU,cAAc;AAC9B,YAAM,UAAU,eAAe;AAE/B,YAAM,SAAS,KAAK,IAAI,SAAS,OAAO,IAAI;AAG5C,YAAM,QAAQ,aAAa,OAAO,CAAC,KAAK,SAAS,MAAM,KAAK,OAAO,CAAC;AAGpE,UAAI,UAAU,GAAG,GAAG,aAAa,YAAY;AAG7C,UAAI,aAAa;AACjB,mBAAa,QAAQ,UAAQ;AAC5B,cAAM,aAAc,KAAK,QAAQ,QAAS,IAAI,KAAK;AAEnD,YAAI,UAAS;AACb,YAAI,OAAO,SAAS,OAAO;AAC3B,YAAI,IAAI,SAAS,SAAS,QAAQ,YAAY,aAAa,UAAU;AACrE,YAAI,UAAS;AAEb,YAAI,aAAa,KAAK,KAAK;AAC3B,YAAI,KAAI;AAER,sBAAc;AAAA,MACf,CAAC;AAGD,UAAI,UAAU;AACd,YAAM,gBAAgB;AAEtB,mBAAa,QAAQ,UAAQ;AAE5B,YAAI,aAAa,KAAK,KAAK;AAC3B,YAAI,SAAS,cAAc,KAAK,UAAU,GAAG,IAAI,EAAE;AAGnD,YAAI,aAAa,MAAM;AACvB,YAAI,YAAY,EAAE;AAClB,YAAI,aAAa,OAAO;AACxB,YAAI,SAAS,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,cAAc,IAAI,OAAO;AAErE,mBAAW;AAAA,MACZ,CAAC;AAGD,UAAI,UAAS;AACb,UAAI,IAAI,SAAS,SAAS,SAAS,KAAK,GAAG,IAAI,KAAK,EAAE;AACtD,UAAI,aAAa,SAAS;AAC1B,UAAI,KAAI;AAGR,UAAI,aAAa,SAAS;AAC1B,UAAI,YAAY,EAAE;AAClB,UAAI,aAAa,QAAQ;AACzB,UAAI,SAAS,QAAQ,SAAS,UAAU,CAAC;AACzC,UAAI,YAAY,EAAE;AAClB,UAAI,SAAS,QAAQ,KAAK,SAAS,UAAU,EAAE;AAE/C,UAAI,KAAI;AAAA,IACR;AAAA;AAAA,IAGD,mBAAmB,aAAa,cAAc;AAC7C,UAAI,CAAC,eAAe,CAAC;AAAc,eAAO;AAE1C,UAAI;AACH,cAAM,UAAU,IAAI,KAAK,WAAW;AACpC,cAAM,WAAW,IAAI,KAAK,YAAY;AAEtC,cAAM,SAAS,WAAW;AAC1B,YAAI,UAAU;AAAG,iBAAO;AAExB,cAAM,QAAQ,KAAK,MAAM,SAAS,IAAO;AACzC,cAAM,UAAU,KAAK,MAAO,SAAS,OAAW,GAAK;AAErD,eAAO,GAAG,KAAK,KAAK,OAAO;AAAA,MAC5B,SAAS,GAAG;AACX,eAAO;AAAA,MACR;AAAA,IACA;AAAA;AAAA,IAGD,sBAAsB;AACrB,WAAK,MAAM,eAAe;AAC1B,WAAK,wBAAwB;AAC7B,WAAK,sBAAsB;AAAA,IAC3B;AAAA;AAAA,IAGD,iBAAiB;AAChBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,aAAa,SAAS,MAAM;AAC3BA,oBAAAA,MAAI,aAAa;AAAA,QAChB;AAAA,QACA;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,uBAAuB;AAEtB,YAAM,OAAO,KAAK;AAClB,YAAM,QAAQ,KAAK;AACnB,YAAM,WAAW,IAAI,KAAK,MAAM,QAAQ,GAAG,CAAC;AAC5C,YAAM,UAAU,IAAI,KAAK,MAAM,OAAO,CAAC;AAGvC,YAAM,aAAa,CAAC,SAAS;AAC5B,cAAM,IAAI,KAAK;AACf,cAAM,IAAI,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACrD,cAAM,IAAI,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAChD,eAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA;AAGtB,WAAK,WAAW,YAAY,WAAW,QAAQ;AAC/C,WAAK,WAAW,UAAU,WAAW,OAAO;AAC5C,WAAK,WAAW,OAAO;AACvB,WAAK,WAAW,SAAS;AACzB,WAAK,sBAAsB;AAE3B,WAAK,MAAM,YAAY;IACvB;AAAA;AAAA,IAGD,mBAAmB;AAClB,WAAK,MAAM,YAAY;IACvB;AAAA;AAAA,IAGD,cAAc,MAAM;AACnB,WAAK,WAAW,OAAO;AACvB,UAAI,SAAS,OAAO;AACnB,aAAK,sBAAsB;MAC5B;AAAA,IACA;AAAA;AAAA,IAGD,eAAe,MAAM;AACpB,WAAK,oBAAoB;AACzB,WAAK,MAAM,SAAS;IACpB;AAAA;AAAA,IAGD,WAAW,GAAG;AACb,YAAM,UAAU,EAAE;AAClB,UAAI,KAAK,sBAAsB,SAAS;AACvC,aAAK,WAAW,YAAY;AAG5B,YAAI,KAAK,WAAW,WAAW,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,KAAK,WAAW,OAAO,GAAG;AACrF,eAAK,WAAW,UAAU;AAAA,QAC3B;AAAA,aACM;AACN,aAAK,WAAW,UAAU;AAG1B,YAAI,KAAK,WAAW,aAAa,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,KAAK,WAAW,SAAS,GAAG;AACzF,eAAK,WAAW,YAAY;AAAA,QAC7B;AAAA,MACD;AAAA,IACA;AAAA;AAAA,IAGD,yBAAyB;AACxB,WAAK,MAAM,gBAAgB;IAC3B;AAAA;AAAA,IAGD,0BAA0B;AACzB,WAAK,MAAM,gBAAgB;IAC3B;AAAA;AAAA,IAGD,kBAAkB,OAAO;AACxB,YAAM,QAAQ,KAAK,oBAAoB,UAAU,UAAQ,KAAK,OAAO,MAAM,EAAE;AAC7E,UAAI,SAAS,GAAG;AACf,aAAK,oBAAoB,OAAO,OAAO,CAAC;AAAA,aAClC;AACN,aAAK,oBAAoB,KAAK,KAAK;AAAA,MACpC;AAAA,IACA;AAAA;AAAA,IAGD,gBAAgB,OAAO;AACtB,aAAO,KAAK,oBAAoB,KAAK,UAAQ,KAAK,OAAO,MAAM,EAAE;AAAA,IACjE;AAAA;AAAA,IAGD,gBAAgB;AAEf,UAAI,CAAC,KAAK,WAAW,aAAa,CAAC,KAAK,WAAW,SAAS;AAC3DA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,UAAI,KAAK,WAAW,SAAS,cAAc,KAAK,oBAAoB,WAAW,GAAG;AACjFA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAGA,UAAI,KAAK;AAAe;AACxB,WAAK,gBAAgB;AAGrBA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACR,CAAC;AAGD,YAAM,SAAS;AAAA,QACd,WAAW,KAAK,WAAW;AAAA,QAC3B,SAAS,KAAK,WAAW;AAAA,QACzB,QAAQ,KAAK,WAAW;AAAA;AAIzB,UAAI,KAAK,WAAW,SAAS,YAAY;AACxC,eAAO,UAAU,KAAK,oBAAoB,IAAI,WAAS,MAAM,EAAE;AAAA,MAChE;AAGAD,gBAAAA,cAAc,iBAAiB,MAAM,EAAE,KAAK,SAAO;AAClD,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAEjC,gBAAM,UAAU,IAAI,KAAK,WAAW,IAAI;AAGxC,eAAK,aAAa,OAAO;AAGzB,eAAK,iBAAgB;AAErBC,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,eACK;AACN,gBAAM,IAAI,MAAM,IAAI,WAAW,MAAM;AAAA,QACtC;AAAA,MACD,CAAC,EAAE,MAAM,SAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,2CAAc,aAAa,GAAG;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,IAAI,WAAW;AAAA,UACtB,MAAM;AAAA,QACP,CAAC;AAAA,MACF,CAAC,EAAE,QAAQ,MAAM;AAChBA,sBAAG,MAAC,YAAW;AACf,aAAK,gBAAgB;AAAA,MACtB,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,aAAa,KAAK;AAgDjBA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACR,CAAC;AAEDA,oBAAAA,MAAI,aAAa;AAAA,QAChB;AAAA,QACA,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,eAAe,KAAK;AAC3B,kBAAM,eAAe,IAAI;AAEzBA,0BAAAA,MAAI,SAAS;AAAA,cACZ;AAAA,cACA,SAAS,CAAC,YAAY;AACrBA,8BAAG,MAAC,YAAW;AACf,sBAAM,gBAAgB,QAAQ;AAE9BA,8BAAAA,MAAI,UAAU;AAAA,kBACb,OAAO;AAAA,kBACP,SAAS;AAAA,kBACT,SAAS,CAACE,SAAQ;AACjB,wBAAIA,KAAI,SAAS;AAEhBF,oCAAAA,MAAI,aAAa;AAAA,wBAChB,UAAU;AAAA,wBACV,UAAU;AAAA,wBACV,SAAS,MAAM;AACdA,wCAAAA,MAAY,MAAA,OAAA,2CAAA,QAAQ;AAAA,wBACpB;AAAA,wBACD,MAAM,CAAC,QAAQ;AACdA,wCAAc,MAAA,MAAA,SAAA,2CAAA,UAAU,GAAG;AAC3BA,wCAAAA,MAAI,UAAU;AAAA,4BACb,OAAO;AAAA,4BACP,MAAM;AAAA,0BACP,CAAC;AAAA,wBACF;AAAA,sBACD,CAAC;AAAA,oBACF;AAAA,kBACD;AAAA,gBACD,CAAC;AAAA,cACD;AAAA,cACD,MAAM,CAAC,QAAQ;AACdA,8BAAG,MAAC,YAAW;AACfA,8BAAc,MAAA,MAAA,SAAA,2CAAA,UAAU,GAAG;AAC3BA,8BAAAA,MAAI,UAAU;AAAA,kBACb,OAAO;AAAA,kBACP,MAAM;AAAA,gBACP,CAAC;AAAA,cACF;AAAA,YACD,CAAC;AAAA,UACF;AAAA,QACA;AAAA,QACD,MAAM,CAAC,QAAQ;AACdA,wBAAG,MAAC,YAAW;AACfA,wFAAc,UAAU,GAAG;AAC3BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AAAA,IAED;AAAA;AAAA,IAED,UAAU;AAET,iBAAW,MAAM;AAChB,aAAK,aAAY;AACjB,aAAK,aAAY;AAAA,MACjB,GAAE,GAAG;AAAA,IACN;AAAA;AAAA,IAED,eAAe;AAEd,YAAM,UAAUA,oBAAI;AAED,cAAQ,cAAc;AACzC,YAAM,cAAc,QAAQ,cAAc;AAC1C,YAAM,eAAe,OAAO,QAAQ,cAAc;AAGlD,YAAM,MAAMA,cAAG,MAAC,oBAAoB,sBAAsB,IAAI;AAG9D,UAAI,aAAa,SAAS;AAC1B,UAAI,SAAS,GAAG,GAAG,aAAa,YAAY;AAG5C,YAAM,OAAO;AAAA,QACZ,EAAE,OAAO,MAAM,OAAO,KAAK,WAAW,YAAY,OAAO,UAAW;AAAA,QACpE,EAAE,OAAO,MAAM,OAAO,KAAK,WAAW,UAAU,OAAO,UAAW;AAAA,QAClE,EAAE,OAAO,MAAM,OAAO,KAAK,WAAW,WAAW,OAAO,UAAW;AAAA,QACnE,EAAE,OAAO,MAAM,OAAO,KAAK,WAAW,YAAY,OAAO,UAAU;AAAA;AAIpE,YAAM,WAAW,KAAK,IAAI,GAAG,KAAK,IAAI,UAAQ,KAAK,KAAK,GAAG,CAAC;AAG5D,YAAM,UAAU,EAAE,MAAM,IAAI,OAAO,IAAI,KAAK,IAAI,QAAQ;AACxD,YAAM,aAAa,cAAc,QAAQ,OAAO,QAAQ;AACxD,YAAM,cAAc,eAAe,QAAQ,MAAM,QAAQ;AACzD,YAAM,WAAW,aAAa,KAAK,SAAS;AAC5C,YAAM,aAAa,WAAW;AAG9B,UAAI,UAAS;AACb,UAAI,eAAe,SAAS;AAC5B,UAAI,aAAa,CAAC;AAClB,UAAI,OAAO,QAAQ,MAAM,QAAQ,GAAG;AACpC,UAAI,OAAO,QAAQ,MAAM,eAAe,QAAQ,MAAM;AACtD,UAAI,OAAM;AAGV,UAAI,UAAS;AACb,UAAI,OAAO,QAAQ,MAAM,eAAe,QAAQ,MAAM;AACtD,UAAI,OAAO,cAAc,QAAQ,OAAO,eAAe,QAAQ,MAAM;AACrE,UAAI,OAAM;AAGV,WAAK,QAAQ,CAAC,MAAM,UAAU;AAC7B,cAAM,IAAI,QAAQ,QAAQ,WAAW,aAAa,KAAK,QAAQ,aAAa;AAC5E,cAAM,YAAY,KAAK,QAAQ,IAAK,KAAK,QAAQ,WAAY,cAAc;AAC3E,cAAM,IAAI,eAAe,QAAQ,SAAS;AAG1C,YAAI,aAAa,KAAK,KAAK;AAC3B,YAAI,SAAS,GAAG,GAAG,UAAU,SAAS;AAGtC,YAAI,aAAa,SAAS;AAC1B,YAAI,YAAY,EAAE;AAClB,YAAI,aAAa,QAAQ;AACzB,YAAI,SAAS,KAAK,MAAM,SAAQ,GAAI,IAAI,WAAW,GAAG,IAAI,EAAE;AAG5D,YAAI,aAAa,SAAS;AAC1B,YAAI,YAAY,EAAE;AAClB,YAAI,SAAS,KAAK,OAAO,IAAI,WAAW,GAAG,eAAe,QAAQ,SAAS,EAAE;AAAA,MAC9E,CAAC;AAGD,YAAM,QAAQ,WAAW;AACzB,eAAS,IAAI,GAAG,KAAK,GAAG,KAAK;AAC5B,cAAM,IAAI,eAAe,QAAQ,SAAU,IAAI,IAAK;AACpD,cAAM,QAAQ,KAAK,MAAM,IAAI,KAAK;AAElC,YAAI,aAAa,SAAS;AAC1B,YAAI,YAAY,EAAE;AAClB,YAAI,aAAa,OAAO;AACxB,YAAI,SAAS,MAAM,SAAU,GAAE,QAAQ,OAAO,GAAG,IAAI,CAAC;AAGtD,YAAI,UAAS;AACb,YAAI,eAAe,SAAS;AAC5B,YAAI,aAAa,GAAG;AACpB,YAAI,OAAO,QAAQ,MAAM,CAAC;AAC1B,YAAI,OAAO,cAAc,QAAQ,OAAO,CAAC;AACzC,YAAI,OAAM;AAAA,MACX;AAGA,UAAI,KAAI;AAAA,IACR;AAAA;AAAA,IAGD,eAAe;AAEd,YAAM,UAAUA,oBAAI;AAED,cAAQ,cAAc;AACzC,YAAM,cAAc,QAAQ,cAAc;AAC1C,YAAM,eAAe,OAAO,QAAQ,cAAc;AAClD,YAAM,UAAU,cAAc;AAC9B,YAAM,UAAU,eAAe;AAC/B,YAAM,SAAS,KAAK,IAAI,SAAS,OAAO,IAAI;AAG5C,YAAM,MAAMA,cAAG,MAAC,oBAAoB,sBAAsB,IAAI;AAG9D,UAAI,aAAa,SAAS;AAC1B,UAAI,SAAS,GAAG,GAAG,aAAa,YAAY;AAG5C,YAAM,QAAQ,KAAK,WAAW,aAAa,KAAK,WAAW,WACxD,KAAK,WAAW,YAAY,KAAK,WAAW,cAAc;AAE7D,YAAM,OAAO;AAAA,QACZ,EAAE,OAAO,MAAM,OAAO,KAAK,WAAW,YAAY,OAAO,UAAW;AAAA,QACpE,EAAE,OAAO,MAAM,OAAO,KAAK,WAAW,UAAU,OAAO,UAAW;AAAA,QAClE,EAAE,OAAO,MAAM,OAAO,KAAK,WAAW,WAAW,OAAO,UAAW;AAAA,QACnE,EAAE,OAAO,MAAM,OAAO,KAAK,WAAW,YAAY,OAAO,UAAU;AAAA;AAIpE,YAAM,YAAY,KAAK,OAAO,UAAQ,KAAK,QAAQ,CAAC;AAGpD,UAAI,aAAa,SAAS;AAC1B,UAAI,YAAY,EAAE;AAClB,UAAI,aAAa,QAAQ;AACzB,UAAI,SAAS,UAAU,SAAS,EAAE;AAGlC,UAAI,UAAU,WAAW,GAAG;AAC3B,YAAI,aAAa,SAAS;AAC1B,YAAI,YAAY,EAAE;AAClB,YAAI,aAAa,QAAQ;AACzB,YAAI,SAAS,QAAQ,SAAS,OAAO;AACrC,YAAI,KAAI;AACR;AAAA,MACD;AAGA,UAAI,aAAa;AACjB,gBAAU,QAAQ,UAAQ;AACzB,cAAM,aAAa,KAAK,QAAQ;AAChC,YAAI,cAAc;AAAG;AAErB,cAAM,WAAW,aAAa,aAAa,IAAI,KAAK;AAGpD,YAAI,UAAS;AACb,YAAI,OAAO,SAAS,OAAO;AAC3B,YAAI,IAAI,SAAS,SAAS,QAAQ,YAAY,UAAU,KAAK;AAC7D,YAAI,aAAa,KAAK,KAAK;AAC3B,YAAI,KAAI;AAGR,cAAM,aAAa,cAAc,WAAW,cAAc;AAC1D,cAAM,gBAAgB,SAAS;AAC/B,cAAM,SAAS,UAAU,KAAK,IAAI,UAAU,IAAI;AAChD,cAAM,SAAS,UAAU,KAAK,IAAI,UAAU,IAAI;AAGhD,YAAI,aAAa,MAAM;AACtB,gBAAM,WAAW,UAAU,KAAK,IAAI,UAAU,IAAI;AAClD,gBAAM,WAAW,UAAU,KAAK,IAAI,UAAU,IAAI;AAElD,cAAI,UAAS;AACb,cAAI,eAAe,KAAK,KAAK;AAC7B,cAAI,aAAa,CAAC;AAClB,cAAI,OAAO,UAAU,QAAQ;AAC7B,cAAI,OAAO,QAAQ,MAAM;AACzB,cAAI,OAAM;AAGV,cAAI,aAAa,SAAS;AAC1B,cAAI,YAAY,EAAE;AAClB,cAAI,aAAa,QAAQ;AACzB,cAAI,SAAS,GAAG,KAAK,KAAK,KAAK,KAAK,MAAM,aAAa,GAAG,CAAC,KAAK,QAAQ,MAAM;AAAA,QAC/E;AAGA,qBAAa;AAAA,MACd,CAAC;AAGD,UAAI,UAAS;AACb,UAAI,IAAI,SAAS,SAAS,SAAS,KAAK,GAAG,IAAI,KAAK,IAAI,KAAK;AAC7D,UAAI,aAAa,SAAS;AAC1B,UAAI,KAAI;AAGR,UAAI,aAAa,SAAS;AAC1B,UAAI,YAAY,EAAE;AAClB,UAAI,aAAa,QAAQ;AACzB,UAAI,SAAS,MAAM,SAAU,GAAE,SAAS,UAAU,CAAC;AACnD,UAAI,YAAY,EAAE;AAClB,UAAI,SAAS,SAAS,SAAS,UAAU,EAAE;AAG3C,UAAI,KAAI;AAAA,IACR;AAAA,IACD,SAAS;AAER,iBAAW,MAAM;AAChB,aAAK,aAAY;AACjB,aAAK,aAAY;AAAA,MACjB,GAAE,GAAG;AAAA,IACN;AAAA,IACD,WAAW;AAEV,WAAK,aAAY;AACjB,WAAK,aAAY;AAAA,IACjB;AAAA;AAAA,IAED,mBAAmB,MAAM;AACxB,WAAK,eAAe;AACpB,WAAK,wBAAwB;AAC7B,WAAK,kBAAkB,IAAI;AAC3B,WAAK,MAAM,eAAe;IAC1B;AAAA;AAAA,IAGD,kBAAkB,MAAM;AAEvB,WAAK,gBAAgB;AACrB,WAAK,sBAAsB;AAG3BA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACR,CAAC;AAGD,WAAK,MAAM,IAAI,4BAA4B;AAAA,QAC1C,QAAQ;AAAA,UACP;AAAA,UACA,QAAQ,KAAK,YAAY;AAAA,QAC1B;AAAA,OACA,EAAE,KAAK,SAAO;AACd,YAAI,IAAI,SAAS,KAAK;AACrB,eAAK,gBAAgB,IAAI;AAEzB,eAAK,UAAU,MAAM;AACpB,iBAAK,kBAAiB;AAAA,UACvB,CAAC;AAAA,eACK;AACNA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,IAAI,OAAO;AAAA,YAClB,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACD,CAAC,EAAE,MAAM,SAAO;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF,CAAC,EAAE,QAAQ,MAAM;AAChBA,sBAAG,MAAC,YAAW;AAAA,MAChB,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB,OAAO;AACxB,WAAK,sBAAsB;AAAA,IAC3B;AAAA;AAAA,IAGD,WAAW,GAAG;AAAA,IAEb;AAAA;AAAA,IAGD,SAAS,KAAK;AAEb,YAAM,OAAO,KAAK,WAAW,GAAG;AAChC,WAAK,mBAAmB,IAAI;AAAA,IAC5B;AAAA;AAAA,IAGD,WAAW,MAAM;AAChB,YAAM,OAAO,KAAK;AAClB,YAAM,SAAS,KAAK,aAAa,GAAG,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC9D,YAAM,MAAM,KAAK,QAAS,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACrD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,IAC9B;AAAA,IACD,WAAW,MAAM;AAChB,YAAM,OAAO,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AACtD,aAAO,KAAK,IAAI,KAAK,IAAI,EAAE,OAAQ,CAAA;AAAA,IACnC;AAAA,IACD,eAAe,QAAQ;AACtB,aAAO,GAAG,OAAO,SAAS,QAAQ,CAAC,CAAC,KAAK,OAAO,UAAU,QAAQ,CAAC,CAAC;AAAA,IACrE;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzzDA,GAAG,WAAW,eAAe;"}