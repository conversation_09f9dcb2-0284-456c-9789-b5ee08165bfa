/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.device-detail-container {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.detail-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.detail-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}
.detail-card .card-header .card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  position: relative;
  padding-left: 20rpx;
}
.detail-card .card-header .card-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 8rpx;
  width: 6rpx;
  height: 28rpx;
  background-color: #1890ff;
  border-radius: 3rpx;
}
.detail-card .card-header .device-status {
  padding: 4rpx 16rpx;
  font-size: 24rpx;
  border-radius: 20rpx;
  color: #fff;
}
.detail-card .card-header .device-status.online {
  background-color: #52c41a;
}
.detail-card .card-header .device-status.offline {
  background-color: #666;
}
.detail-card .card-header .device-status.fault {
  background-color: #f5222d;
}
.detail-card .card-header .refresh-btn, .detail-card .card-header .view-all {
  font-size: 26rpx;
  color: #1890ff;
}
.info-item {
  display: flex;
  margin-bottom: 20rpx;
}
.info-item .info-label {
  width: 180rpx;
  font-size: 28rpx;
  color: #666;
}
.info-item .info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.status-panel {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 30rpx;
}
.status-panel .status-item {
  width: 33.33%;
  text-align: center;
  margin-bottom: 30rpx;
}
.status-panel .status-item .metric-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.status-panel .status-item .metric-value.alert {
  color: #f5222d;
}
.status-panel .status-item .metric-label {
  font-size: 26rpx;
  color: #666;
}
.chart-container {
  margin-top: 20rpx;
}
.chart-container .chart-placeholder {
  height: 300rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.chart-container .chart-placeholder text {
  font-size: 28rpx;
  color: #666;
}
.alarm-list .alarm-item {
  display: flex;
  margin-bottom: 20rpx;
}
.alarm-list .alarm-item .alarm-icon {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-top: 12rpx;
  margin-right: 16rpx;
}
.alarm-list .alarm-item .alarm-icon.warning {
  background-color: #faad14;
}
.alarm-list .alarm-item .alarm-icon.error {
  background-color: #f5222d;
}
.alarm-list .alarm-item .alarm-content {
  flex: 1;
}
.alarm-list .alarm-item .alarm-content .alarm-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}
.alarm-list .alarm-item .alarm-content .alarm-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}
.alarm-list .alarm-item .alarm-content .alarm-time {
  font-size: 24rpx;
  color: #666;
}
.maintenance-list .maintenance-item {
  display: flex;
  margin-bottom: 30rpx;
}
.maintenance-list .maintenance-item .maintenance-time {
  width: 120rpx;
  margin-right: 20rpx;
}
.maintenance-list .maintenance-item .maintenance-time .date {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 4rpx;
}
.maintenance-list .maintenance-item .maintenance-time .time {
  display: block;
  font-size: 24rpx;
  color: #666;
}
.maintenance-list .maintenance-item .maintenance-content {
  flex: 1;
  position: relative;
  padding-left: 30rpx;
}
.maintenance-list .maintenance-item .maintenance-content::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2rpx;
  background-color: #ddd;
}
.maintenance-list .maintenance-item .maintenance-content::after {
  content: "";
  position: absolute;
  left: -4rpx;
  top: 10rpx;
  width: 10rpx;
  height: 10rpx;
  border-radius: 50%;
  background-color: #1890ff;
}
.maintenance-list .maintenance-item .maintenance-content .maintenance-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}
.maintenance-list .maintenance-item .maintenance-content .maintenance-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}
.maintenance-list .maintenance-item .maintenance-content .maintenance-operator {
  font-size: 24rpx;
  color: #666;
}
.maintenance-list .empty-list {
  text-align: center;
  padding: 40rpx 0;
}
.maintenance-list .empty-list text {
  font-size: 28rpx;
  color: #666;
}
.action-buttons {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
}
.action-buttons .action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  margin: 0 10rpx;
}
.action-buttons .action-btn .iconfont {
  margin-right: 8rpx;
}
.action-buttons .action-btn.primary {
  background-color: #1890ff;
  color: #fff;
}
.action-buttons .action-btn.warning {
  background-color: #fff;
  color: #faad14;
  border: 1px solid #faad14;
}