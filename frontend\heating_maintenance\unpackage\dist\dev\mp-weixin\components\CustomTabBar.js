"use strict";
const common_vendor = require("../common/vendor.js");
const utils_auth = require("../utils/auth.js");
require("../app.js");
const _sfc_main = {
  name: "CustomTabBar",
  data() {
    return {
      tabList: [
        {
          pagePath: "/pages/home/<USER>",
          text: "首页",
          iconPath: "/static/tab/home.png",
          selectedIconPath: "/static/tab/home-active.png"
        },
        {
          pagePath: "/pages/hes/list",
          text: "换热站",
          iconPath: "/static/tab/hes.png",
          selectedIconPath: "/static/tab/hes-active.png",
          permissionCode: "home:hes"
        },
        {
          pagePath: "/pages/message/center",
          text: "消息",
          iconPath: "/static/tab/notification.png",
          selectedIconPath: "/static/tab/notification-active.png"
        },
        {
          pagePath: "/pages/user/info",
          text: "我的",
          iconPath: "/static/tab/profile.png",
          selectedIconPath: "/static/tab/profile-active.png"
        }
      ]
    };
  },
  computed: {
    computedCurrentPath() {
      const pages = getCurrentPages();
      if (pages.length) {
        const currentPage = pages[pages.length - 1];
        return `/${currentPage.route}`;
      }
      return "";
    },
    filteredTabList() {
      return this.tabList.filter((item) => {
        return !item.permissionCode || utils_auth.hasPermission(item.permissionCode);
      });
    }
  },
  methods: {
    switchTab(path) {
      if (this.computedCurrentPath === path)
        return;
      common_vendor.index.switchTab({
        url: path,
        fail: (err) => {
          common_vendor.index.__f__("error", "at components/CustomTabBar.vue:82", `switchTab 失败: ${JSON.stringify(err)}`);
          common_vendor.index.navigateTo({ url: path });
        }
      });
    }
  },
  mounted() {
    common_vendor.index.hideTabBar();
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($options.filteredTabList, (item, index, i0) => {
      return common_vendor.e({
        a: $options.computedCurrentPath === item.pagePath ? item.selectedIconPath : item.iconPath,
        b: common_vendor.t(item.text),
        c: $options.computedCurrentPath === item.pagePath
      }, $options.computedCurrentPath === item.pagePath ? {} : {}, {
        d: index,
        e: $options.computedCurrentPath === item.pagePath ? 1 : "",
        f: common_vendor.o(($event) => $options.switchTab(item.pagePath), index)
      });
    })
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../.sourcemap/mp-weixin/components/CustomTabBar.js.map
