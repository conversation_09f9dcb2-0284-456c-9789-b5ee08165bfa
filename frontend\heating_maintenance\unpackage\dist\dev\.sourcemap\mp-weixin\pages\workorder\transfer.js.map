{"version": 3, "file": "transfer.js", "sources": ["pages/workorder/transfer.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvd29ya29yZGVyL3RyYW5zZmVyLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"transfer-container\">\r\n\t\t<view class=\"form-card\">\r\n\t\t\t<view class=\"form-title\">转派工单</view>\r\n\t\t\t\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<view class=\"form-label required\">转派原因</view>\r\n\t\t\t\t<textarea class=\"form-textarea\" v-model=\"formData.transferReason\" placeholder=\"请输入转派原因\"></textarea>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<view class=\"form-label required\">转派人员</view>\r\n\t\t\t\t<view class=\"picker-box\" @click=\"showUserPicker\">\r\n\t\t\t\t\t<text class=\"picker-text\" :class=\"{'placeholder': !formData.repairUserName}\">\r\n\t\t\t\t\t\t{{formData.repairUserName || '请选择转派人员'}}\r\n\t\t\t\t\t</text>\r\n\t\t\t\t\t<text class=\"iconfont icon-right\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"action-buttons\">\r\n\t\t\t<button class=\"btn-cancel\" @click=\"cancelTransfer\">取消</button>\r\n\t\t\t<button class=\"btn-submit\" @click=\"submitTransfer\">确认转派</button>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 人员选择弹窗 -->\r\n\t\t<uni-popup ref=\"userPopup\" type=\"bottom\">\r\n\t\t\t<view class=\"popup-container\">\r\n\t\t\t\t<view class=\"popup-header\">\r\n\t\t\t\t\t<text class=\"cancel\" @click=\"closeUserPicker\">取消</text>\r\n\t\t\t\t\t<text class=\"title\">选择转派人员</text>\r\n\t\t\t\t\t<text class=\"confirm\" @click=\"confirmUserSelect\">确认</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 搜索框 -->\r\n\t\t\t\t<view class=\"search-box\">\r\n\t\t\t\t\t<view class=\"search-input-wrap\">\r\n\t\t\t\t\t\t<text class=\"iconfont icon-search\"></text>\r\n\t\t\t\t\t\t<input \r\n\t\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\t\tclass=\"search-input\" \r\n\t\t\t\t\t\t\tv-model=\"searchKeyword\" \r\n\t\t\t\t\t\t\tplaceholder=\"搜索姓名或手机号\"\r\n\t\t\t\t\t\t\tconfirm-type=\"search\"\r\n\t\t\t\t\t\t\t@input=\"handleSearch\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t<text class=\"iconfont icon-close\" v-if=\"searchKeyword\" @click=\"clearSearch\"></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"popup-body\">\r\n\t\t\t\t\t<view \r\n\t\t\t\t\t\tv-for=\"(user, index) in filteredUsers\" \r\n\t\t\t\t\t\t:key=\"index\" \r\n\t\t\t\t\t\tclass=\"user-item\"\r\n\t\t\t\t\t\t:class=\"{'active': isUserSelected(user)}\"\r\n\t\t\t\t\t\t@click=\"selectUser(user)\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view class=\"user-info\">\r\n\t\t\t\t\t\t\t<text class=\"user-name\">{{user.name || user.userName}}</text>\r\n\t\t\t\t\t\t\t<text class=\"user-phone\">{{user.phone || ''}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"select-indicator\" v-if=\"isUserSelected(user)\">\r\n\t\t\t\t\t\t\t<text class=\"select-text\">已选</text>\r\n\t\t\t\t\t\t\t<text class=\"iconfont icon-check\"></text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"filteredUsers.length === 0\" class=\"empty-tip\">\r\n\t\t\t\t\t\t{{searchKeyword ? '未找到匹配的人员' : '暂无可选择的人员'}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { userApi } from \"@/utils/api.js\";\r\n\timport { workOrderApi } from \"@/utils/api.js\";\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\torderId: null,\r\n\t\t\t\tformData: {\r\n\t\t\t\t\ttransferReason: '',\r\n\t\t\t\t\trepairUserId: null,\r\n\t\t\t\t\trepairUserName: ''\r\n\t\t\t\t},\r\n\t\t\t\trepairUsers: [],\r\n\t\t\t\tfilteredUsers: [],\r\n\t\t\t\tsearchKeyword: '',\r\n\t\t\t\tselectedUser: null,\r\n\t\t\t\trules: {\r\n\t\t\t\t\ttransferReason: [\r\n\t\t\t\t\t\t{ required: true, message: '请输入转派原因' }\r\n\t\t\t\t\t],\r\n\t\t\t\t\trepairUserId: [\r\n\t\t\t\t\t\t{ required: true, message: '请选择转派人员' }\r\n\t\t\t\t\t]\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tif (options.id) {\r\n\t\t\t\tthis.orderId = options.id;\r\n\t\t\t\t// 实际项目中可以加载维修人员列表\r\n\t\t\t\t this.loadRepairUsers();\r\n\t\t\t} else {\r\n\t\t\t\tthis.showError('缺少工单ID');\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tuni.navigateBack();\r\n\t\t\t\t}, 1500);\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 加载维修人员列表\r\n\t\t\tloadRepairUsers() {\r\n\t\t\t\tuserApi.getInspectorList({ role: 'repair' })\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tif (res.code === 200 && res.data) {\r\n\t\t\t\t\t\t\t// 记录原始数据便于调试\r\n\t\t\t\t\t\t\tconsole.log('API返回的人员数据:', JSON.stringify(res.data));\r\n\t\t\t\t\t\t\t// 确保数据格式正确\r\n\t\t\t\t\t\t\tthis.repairUsers = res.data.map(user => ({\r\n\t\t\t\t\t\t\t\tuserId: user.userId || user.id,\r\n\t\t\t\t\t\t\t\tname: user.name || user.userName,\r\n\t\t\t\t\t\t\t\tphone: user.phone || user.phoneNumber || ''\r\n\t\t\t\t\t\t\t}));\r\n\t\t\t\t\t\t\t// 初始化过滤后的用户列表\r\n\t\t\t\t\t\t\tthis.filteredUsers = [...this.repairUsers];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tconsole.error('获取人员列表失败:', err);\r\n\t\t\t\t\t\tthis.showError('获取人员列表失败');\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 判断用户是否被选中\r\n\t\t\tisUserSelected(user) {\r\n\t\t\t\tif (!this.selectedUser) return false;\r\n\t\t\t\t\r\n\t\t\t\t// 优先比较userId，如果不存在则比较id\r\n\t\t\t\tconst userIdToCompare = user.userId || user.id;\r\n\t\t\t\tconst selectedUserId = this.selectedUser.userId || this.selectedUser.id;\r\n\t\t\t\t\r\n\t\t\t\treturn userIdToCompare === selectedUserId;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 处理搜索输入\r\n\t\t\thandleSearch() {\r\n\t\t\t\tif (!this.searchKeyword) {\r\n\t\t\t\t\tthis.filteredUsers = [...this.repairUsers];\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tconst keyword = this.searchKeyword.toLowerCase();\r\n\t\t\t\tthis.filteredUsers = this.repairUsers.filter(user => {\r\n\t\t\t\t\tconst name = (user.name || user.userName || '').toLowerCase();\r\n\t\t\t\t\tconst phone = (user.phone || '').toLowerCase();\r\n\t\t\t\t\treturn name.includes(keyword) || phone.includes(keyword);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 清除搜索\r\n\t\t\tclearSearch() {\r\n\t\t\t\tthis.searchKeyword = '';\r\n\t\t\t\tthis.filteredUsers = [...this.repairUsers];\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 显示人员选择器\r\n\t\t\tshowUserPicker() {\r\n\t\t\t\t// 重置搜索条件\r\n\t\t\t\tthis.searchKeyword = '';\r\n\t\t\t\tthis.filteredUsers = [...this.repairUsers];\r\n\t\t\t\tthis.$refs.userPopup.open();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 关闭人员选择器\r\n\t\t\tcloseUserPicker() {\r\n\t\t\t\tthis.$refs.userPopup.close();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 选择人员\r\n\t\t\tselectUser(user) {\r\n\t\t\t\tconsole.log('选择的用户:', JSON.stringify(user));\r\n\t\t\t\tthis.selectedUser = { ...user }; // 创建用户对象的副本，避免引用问题\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 确认人员选择\r\n\t\t\tconfirmUserSelect() {\r\n\t\t\t\tconsole.log('确认选择，当前选中:', JSON.stringify(this.selectedUser));\r\n\t\t\t\tif (this.selectedUser) {\r\n\t\t\t\t\t// 使用灵活的属性获取，避免字段名不匹配问题\r\n\t\t\t\t\tthis.formData.repairUserId = this.selectedUser.userId || this.selectedUser.id;\r\n\t\t\t\t\tthis.formData.repairUserName = this.selectedUser.name || this.selectedUser.userName;\r\n\t\t\t\t\t\r\n\t\t\t\t\tconsole.log('更新表单数据:', JSON.stringify(this.formData));\r\n\t\t\t\t\tthis.closeUserPicker();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.showError('请选择一个维修人员');\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 取消转派\r\n\t\t\tcancelTransfer() {\r\n\t\t\t\tuni.navigateBack();\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 验证表单\r\n\t\t\tvalidateForm() {\r\n\t\t\t\tlet isValid = true;\r\n\t\t\t\t\r\n\t\t\t\tfor (const key in this.rules) {\r\n\t\t\t\t\tconst value = this.formData[key];\r\n\t\t\t\t\tconst rules = this.rules[key];\r\n\t\t\t\t\t\r\n\t\t\t\t\tfor (const rule of rules) {\r\n\t\t\t\t\t\tif (rule.required && !value && value !== 0) {\r\n\t\t\t\t\t\t\tthis.showError(rule.message);\r\n\t\t\t\t\t\t\tisValid = false;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (!isValid) break;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\treturn isValid;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 提交转派\r\n\t\t\tsubmitTransfer() {\r\n\t\t\t\tif (!this.validateForm()) return;\r\n\t\t\t\t\r\n\t\t\t\t// 显示确认对话框\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '转派确认',\r\n\t\t\t\t\tcontent: '该工单仅可转派一次，请确认目标负责人无误后再进行操作。',\r\n\t\t\t\t\tconfirmText: '确认转派',\r\n\t\t\t\t\tcancelText: '再次确认',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tthis.doTransfer();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 执行转派操作\r\n\t\t\tdoTransfer() {\r\n\t\t\t\t// 显示加载提示\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '提交中...'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 构造请求参数\r\n\t\t\t\tconst params = {\r\n\t\t\t\t\torderId: this.orderId,\r\n\t\t\t\t\ttransferUserId: this.getCurrentUserId(),\r\n\t\t\t\t\trepairUserId: this.formData.repairUserId,\r\n\t\t\t\t\ttransferReason: this.formData.transferReason\r\n\t\t\t\t};\r\n\t\t\t\t\r\n\t\t\t\tconsole.log('提交的参数:', JSON.stringify(params));\r\n\t\t\t\t\r\n\t\t\t\t// 实际项目中应该调用API\r\n\t\t\t\tworkOrderApi.transferOrder(params)\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\t\t\tthis.showSuccess('工单已转派');\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\t\t\t\t\tdelta: 1\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}, 1500);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.showError(res.message || '提交失败');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tconsole.error('转派失败:', err);\r\n\t\t\t\t\t\tthis.showError('网络异常，请稍后重试');\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.finally(() => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取当前用户ID\r\n\t\t\tgetCurrentUserId() {\r\n\t\t\t\t// 实际项目中从全局状态或本地存储获取\r\n\t\t\t\treturn uni.getStorageSync(\"userId\") || 0;\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 显示成功提示\r\n\t\t\tshowSuccess(message) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: message,\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 显示错误提示\r\n\t\t\tshowError(message) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: message,\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.transfer-container {\r\n\t\tmin-height: 100vh;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tpadding: 20rpx;\r\n\t}\r\n\t\r\n\t.form-card {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 12rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tpadding: 30rpx;\r\n\t\t\r\n\t\t.form-title {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tcolor: $uni-text-color;\r\n\t\t\tmargin-bottom: 30rpx;\r\n\t\t\tpadding-bottom: 20rpx;\r\n\t\t\tborder-bottom: 1rpx solid #eee;\r\n\t\t}\r\n\t\t\r\n\t\t.form-item {\r\n\t\t\tmargin-bottom: 30rpx;\r\n\t\t\t\r\n\t\t\t.form-label {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\tmargin-bottom: 16rpx;\r\n\t\t\t\t\r\n\t\t\t\t&.required::before {\r\n\t\t\t\t\tcontent: '*';\r\n\t\t\t\t\tcolor: #f5222d;\r\n\t\t\t\t\tmargin-right: 6rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.form-textarea {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 200rpx;\r\n\t\t\t\tpadding: 20rpx;\r\n\t\t\t\tbackground-color: #f5f5f5;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.picker-box {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 80rpx;\r\n\t\t\t\tpadding: 0 20rpx;\r\n\t\t\t\tbackground-color: #f5f5f5;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\t\r\n\t\t\t\t.picker-text {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.placeholder {\r\n\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.action-buttons {\r\n\t\tdisplay: flex;\r\n\t\tpadding: 20rpx 0;\r\n\t\t\r\n\t\tbutton {\r\n\t\t\tflex: 1;\r\n\t\t\theight: 80rpx;\r\n\t\t\tline-height: 80rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tborder-radius: 40rpx;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tmargin: 0 10rpx;\r\n\t\t\t\r\n\t\t\t&::after {\r\n\t\t\t\tborder: none;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.btn-cancel {\r\n\t\t\tbackground-color: #f5f5f5;\r\n\t\t\tcolor: $uni-text-color;\r\n\t\t\tborder: 1px solid #ddd;\r\n\t\t}\r\n\t\t\r\n\t\t.btn-submit {\r\n\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\tcolor: #fff;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.popup-container {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 24rpx 24rpx 0 0;\r\n\t\toverflow: hidden;\r\n\t\t\r\n\t\t.popup-header {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tborder-bottom: 1rpx solid #eee;\r\n\t\t\t\r\n\t\t\t.title {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.cancel, .confirm {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.cancel {\r\n\t\t\t\tcolor: #999;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.confirm {\r\n\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.search-box {\r\n\t\t\tpadding: 20rpx 30rpx;\r\n\t\t\tborder-bottom: 1rpx solid #eee;\r\n\t\t\t\r\n\t\t\t.search-input-wrap {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tbackground-color: #f5f5f5;\r\n\t\t\t\tborder-radius: 36rpx;\r\n\t\t\t\tpadding: 0 20rpx;\r\n\t\t\t\theight: 72rpx;\r\n\t\t\t\t\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.search-input {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\theight: 72rpx;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.icon-close {\r\n\t\t\t\t\tpadding: 10rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.popup-body {\r\n\t\t\tmax-height: 60vh;\r\n\t\t\tpadding-bottom: 50rpx;\r\n\t\t\t\r\n\t\t\t.user-item {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\tpadding: 30rpx;\r\n\t\t\t\tborder-bottom: 1rpx solid #eee;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\t\r\n\t\t\t\t&.active {\r\n\t\t\t\t\tbackground-color: #e6f7ff;\r\n\t\t\t\t\tborder-left: 8rpx solid $uni-color-primary;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.user-info {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.user-name {\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.user-phone {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.select-indicator {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.select-text {\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.iconfont {\r\n\t\t\t\t\t\tfont-size: 40rpx;\r\n\t\t\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.empty-tip {\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tcolor: #999;\r\n\t\t\t\tpadding: 30rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style> ", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/workorder/transfer.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "userApi", "workOrderApi"], "mappings": ";;;AAgFC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,SAAS;AAAA,MACT,UAAU;AAAA,QACT,gBAAgB;AAAA,QAChB,cAAc;AAAA,QACd,gBAAgB;AAAA,MAChB;AAAA,MACD,aAAa,CAAE;AAAA,MACf,eAAe,CAAE;AAAA,MACjB,eAAe;AAAA,MACf,cAAc;AAAA,MACd,OAAO;AAAA,QACN,gBAAgB;AAAA,UACf,EAAE,UAAU,MAAM,SAAS,UAAU;AAAA,QACrC;AAAA,QACD,cAAc;AAAA,UACb,EAAE,UAAU,MAAM,SAAS,UAAU;AAAA,QACtC;AAAA,MACD;AAAA,IACD;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AACf,QAAI,QAAQ,IAAI;AACf,WAAK,UAAU,QAAQ;AAEtB,WAAK,gBAAe;AAAA,WACf;AACN,WAAK,UAAU,QAAQ;AACvB,iBAAW,MAAM;AAChBA,sBAAG,MAAC,aAAY;AAAA,MAChB,GAAE,IAAI;AAAA,IACR;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,kBAAkB;AACjBC,gBAAAA,QAAQ,iBAAiB,EAAE,MAAM,UAAU,EACzC,KAAK,SAAO;AACZ,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAEjCD,8BAAA,MAAA,OAAA,uCAAY,eAAe,KAAK,UAAU,IAAI,IAAI,CAAC;AAEnD,eAAK,cAAc,IAAI,KAAK,IAAI,WAAS;AAAA,YACxC,QAAQ,KAAK,UAAU,KAAK;AAAA,YAC5B,MAAM,KAAK,QAAQ,KAAK;AAAA,YACxB,OAAO,KAAK,SAAS,KAAK,eAAe;AAAA,UACzC,EAAC;AAEF,eAAK,gBAAgB,CAAC,GAAG,KAAK,WAAW;AAAA,QAC1C;AAAA,OACA,EACA,MAAM,SAAO;AACbA,kFAAc,aAAa,GAAG;AAC9B,aAAK,UAAU,UAAU;AAAA,MAC1B,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,eAAe,MAAM;AACpB,UAAI,CAAC,KAAK;AAAc,eAAO;AAG/B,YAAM,kBAAkB,KAAK,UAAU,KAAK;AAC5C,YAAM,iBAAiB,KAAK,aAAa,UAAU,KAAK,aAAa;AAErE,aAAO,oBAAoB;AAAA,IAC3B;AAAA;AAAA,IAGD,eAAe;AACd,UAAI,CAAC,KAAK,eAAe;AACxB,aAAK,gBAAgB,CAAC,GAAG,KAAK,WAAW;AACzC;AAAA,MACD;AAEA,YAAM,UAAU,KAAK,cAAc,YAAW;AAC9C,WAAK,gBAAgB,KAAK,YAAY,OAAO,UAAQ;AACpD,cAAM,QAAQ,KAAK,QAAQ,KAAK,YAAY,IAAI;AAChD,cAAM,SAAS,KAAK,SAAS,IAAI,YAAW;AAC5C,eAAO,KAAK,SAAS,OAAO,KAAK,MAAM,SAAS,OAAO;AAAA,MACxD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AACb,WAAK,gBAAgB;AACrB,WAAK,gBAAgB,CAAC,GAAG,KAAK,WAAW;AAAA,IACzC;AAAA;AAAA,IAGD,iBAAiB;AAEhB,WAAK,gBAAgB;AACrB,WAAK,gBAAgB,CAAC,GAAG,KAAK,WAAW;AACzC,WAAK,MAAM,UAAU;IACrB;AAAA;AAAA,IAGD,kBAAkB;AACjB,WAAK,MAAM,UAAU;IACrB;AAAA;AAAA,IAGD,WAAW,MAAM;AAChBA,0BAAY,MAAA,OAAA,uCAAA,UAAU,KAAK,UAAU,IAAI,CAAC;AAC1C,WAAK,eAAe,EAAE,GAAG;IACzB;AAAA;AAAA,IAGD,oBAAoB;AACnBA,0BAAY,MAAA,OAAA,uCAAA,cAAc,KAAK,UAAU,KAAK,YAAY,CAAC;AAC3D,UAAI,KAAK,cAAc;AAEtB,aAAK,SAAS,eAAe,KAAK,aAAa,UAAU,KAAK,aAAa;AAC3E,aAAK,SAAS,iBAAiB,KAAK,aAAa,QAAQ,KAAK,aAAa;AAE3EA,gFAAY,WAAW,KAAK,UAAU,KAAK,QAAQ,CAAC;AACpD,aAAK,gBAAe;AAAA,aACd;AACN,aAAK,UAAU,WAAW;AAAA,MAC3B;AAAA,IACA;AAAA;AAAA,IAGD,iBAAiB;AAChBA,oBAAG,MAAC,aAAY;AAAA,IAChB;AAAA;AAAA,IAGD,eAAe;AACd,UAAI,UAAU;AAEd,iBAAW,OAAO,KAAK,OAAO;AAC7B,cAAM,QAAQ,KAAK,SAAS,GAAG;AAC/B,cAAM,QAAQ,KAAK,MAAM,GAAG;AAE5B,mBAAW,QAAQ,OAAO;AACzB,cAAI,KAAK,YAAY,CAAC,SAAS,UAAU,GAAG;AAC3C,iBAAK,UAAU,KAAK,OAAO;AAC3B,sBAAU;AACV;AAAA,UACD;AAAA,QACD;AAEA,YAAI,CAAC;AAAS;AAAA,MACf;AAEA,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,iBAAiB;AAChB,UAAI,CAAC,KAAK,aAAY;AAAI;AAG1BA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAChB,iBAAK,WAAU;AAAA,UAChB;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,aAAa;AAEZA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACR,CAAC;AAGD,YAAM,SAAS;AAAA,QACd,SAAS,KAAK;AAAA,QACd,gBAAgB,KAAK,iBAAkB;AAAA,QACvC,cAAc,KAAK,SAAS;AAAA,QAC5B,gBAAgB,KAAK,SAAS;AAAA;AAG/BA,0BAAY,MAAA,OAAA,uCAAA,UAAU,KAAK,UAAU,MAAM,CAAC;AAG5CE,gBAAY,aAAC,cAAc,MAAM,EAC/B,KAAK,SAAO;AACZ,YAAI,IAAI,SAAS,KAAK;AACrB,eAAK,YAAY,OAAO;AACxB,qBAAW,MAAM;AAChBF,0BAAAA,MAAI,aAAa;AAAA,cAChB,OAAO;AAAA,YACR,CAAC;AAAA,UACD,GAAE,IAAI;AAAA,eACD;AACN,eAAK,UAAU,IAAI,WAAW,MAAM;AAAA,QACrC;AAAA,OACA,EACA,MAAM,SAAO;AACbA,sBAAc,MAAA,MAAA,SAAA,uCAAA,SAAS,GAAG;AAC1B,aAAK,UAAU,YAAY;AAAA,OAC3B,EACA,QAAQ,MAAM;AACdA,sBAAG,MAAC,YAAW;AAAA,MAChB,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,mBAAmB;AAElB,aAAOA,oBAAI,eAAe,QAAQ,KAAK;AAAA,IACvC;AAAA;AAAA,IAGD,YAAY,SAAS;AACpBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,UAAU,SAAS;AAClBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvTD,GAAG,WAAW,eAAe;"}