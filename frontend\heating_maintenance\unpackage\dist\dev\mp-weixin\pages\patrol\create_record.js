"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const _sfc_main = {
  data() {
    return {
      formData: {
        patrol_plan_id: "",
        // 巡检计划ID
        executor_ids: [],
        // 执行人ID数组
        start_time: "",
        // 开始时间
        end_time: "",
        // 结束时间
        status: "completed",
        // 状态：pending/processing/completed
        remark: "",
        // 备注
        patrol_results: []
        // 巡检结果
      },
      statusOptions: [
        { label: "待执行", value: "pending" },
        { label: "执行中", value: "processing" },
        { label: "已完成", value: "completed" }
      ],
      selectedPlanName: "",
      selectedExecutorNames: "",
      plans: [],
      executors: [],
      loading: false,
      submitting: false,
      currentResultIndex: -1,
      currentResult: {
        patrol_item_id: "",
        check_result: "normal",
        param_value: "",
        description: "",
        images: [],
        latitude: 0,
        longitude: 0
      },
      // 日期时间选择器相关数据
      currentDatetimeType: "start",
      // 当前正在选择的时间类型：start/end
      indicatorStyle: "height: 50px;",
      pickerValue: [0, 0, 0, 0, 0, 0],
      years: [],
      months: [],
      days: [],
      hours: [],
      minutes: [],
      seconds: [],
      year: 2020,
      month: 1,
      day: 1,
      hour: 0,
      minute: 0,
      second: 0,
      // 执行人选择器相关数据
      executorSearchKey: "",
      selectedExecutorIds: []
    };
  },
  computed: {
    // 获取经过搜索过滤的执行人列表
    filteredExecutors() {
      if (!this.executorSearchKey) {
        return this.executors;
      }
      const keyword = this.executorSearchKey.toLowerCase();
      return this.executors.filter(
        (exec) => exec.name.toLowerCase().includes(keyword) || exec.departmentName && exec.departmentName.toLowerCase().includes(keyword)
      );
    }
  },
  onLoad(options) {
    if (options.plan_id) {
      this.formData.patrol_plan_id = options.plan_id;
      this.loadPlanDetails(options.plan_id);
    }
    this.loadPatrolPlans();
    this.loadExecutors();
    const now = /* @__PURE__ */ new Date();
    this.formData.start_time = this.formatDateTime(now);
    this.formData.end_time = this.formatDateTime(now);
  },
  methods: {
    // 格式化日期时间
    formatDateTime(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    // 加载巡检计划列表
    loadPatrolPlans() {
      this.loading = true;
      utils_api.patrolApi.getPatrolPlans({}).then((res) => {
        if (res.code === 200 && res.data) {
          this.plans = res.data || [];
          if (this.formData.patrol_plan_id) {
            const selectedPlan = this.plans.find((plan) => plan.id == this.formData.patrol_plan_id);
            if (selectedPlan) {
              this.selectedPlanName = selectedPlan.name;
            }
          }
        } else {
          common_vendor.index.showToast({
            title: "获取巡检计划列表失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/patrol/create_record.vue:440", "获取巡检计划列表错误:", err);
        common_vendor.index.showToast({
          title: "获取巡检计划列表失败",
          icon: "none"
        });
      }).finally(() => {
        this.loading = false;
      });
    },
    // 加载巡检计划详情
    loadPlanDetails(planId) {
      utils_api.patrolApi.getPlanDetail(planId).then((res) => {
        if (res.code === 200 && res.data) {
          this.selectedPlanName = res.data.name;
          if (res.data.executorIds && res.data.executorIds.length > 0) {
            this.loadPlanExecutors(planId);
          }
          this.loadPatrolItems(planId);
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/patrol/create_record.vue:469", "获取巡检计划详情错误:", err);
      });
    },
    // 加载巡检项目列表
    loadPatrolItems(planId) {
      utils_api.patrolApi.getItemList({ planId }).then((res) => {
        if (res.code === 200 && res.data) {
          if (res.data && res.data.length > 0) {
            this.formData.patrol_results = res.data.map((item) => ({
              patrol_item_id: item.id,
              check_result: "normal",
              param_value: "",
              description: "",
              images: [],
              latitude: 0,
              longitude: 0,
              // 存储巡检项的详细信息，用于展示
              item_info: {
                deviceId: item.deviceId,
                deviceName: item.deviceName,
                itemName: item.itemName || `巡检项 ${item.id}`,
                categoryName: item.categoryName || "",
                paramType: item.paramType || "",
                unit: item.unit || "",
                normalRange: item.normalRange || "",
                checkMethod: item.checkMethod || "",
                importance: item.importance || "",
                description: item.description || ""
              }
            }));
          } else {
            common_vendor.index.showToast({
              title: "该巡检计划没有巡检项",
              icon: "none"
            });
            this.formData.patrol_results = [];
          }
        } else {
          common_vendor.index.showToast({
            title: "获取巡检项目失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/patrol/create_record.vue:518", "获取巡检项目列表错误:", err);
        common_vendor.index.showToast({
          title: "获取巡检项目失败",
          icon: "none"
        });
      });
    },
    // 加载计划指定的执行人
    loadPlanExecutors(planId) {
      utils_api.patrolApi.getPlanExecutors(planId).then((res) => {
        if (res.code === 200 && res.data) {
          this.executors = res.data || [];
          this.updateSelectedExecutorNames();
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/patrol/create_record.vue:538", "获取计划执行人列表错误:", err);
      });
    },
    // 更新已选择执行人的显示
    updateSelectedExecutorNames() {
      if (this.formData.executor_ids && this.formData.executor_ids.length > 0 && this.executors.length > 0) {
        const selectedUsers = this.executors.filter(
          (exec) => this.formData.executor_ids.includes(exec.id)
        );
        if (selectedUsers.length > 0) {
          this.selectedExecutorNames = selectedUsers.map((user) => user.name).join("，");
          if (this.selectedExecutorNames.length > 15) {
            this.selectedExecutorNames = `共${selectedUsers.length}人`;
          }
        } else {
          this.selectedExecutorNames = "";
        }
      } else {
        this.selectedExecutorNames = "";
      }
    },
    // 加载执行人列表
    loadExecutors() {
      utils_api.patrolApi.getPlanExecutors().then((res) => {
        if (res.code === 200 && res.data) {
          this.executors = res.data || [];
          const userInfo = common_vendor.index.getStorageSync("userInfo");
          if (userInfo && userInfo.userId) {
            this.formData.executor_ids = [userInfo.userId];
            this.updateSelectedExecutorNames();
          }
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/patrol/create_record.vue:580", "获取执行人列表错误:", err);
      });
    },
    // 显示巡检计划选择器
    showPlanSelector() {
      if (this.plans.length === 0) {
        common_vendor.index.showToast({
          title: "暂无可选巡检计划",
          icon: "none"
        });
        return;
      }
      const planItems = this.plans.map((plan) => plan.name);
      common_vendor.index.showActionSheet({
        itemList: planItems,
        success: (res) => {
          const index = res.tapIndex;
          this.formData.patrol_plan_id = this.plans[index].id;
          this.selectedPlanName = this.plans[index].name;
          this.loadPlanDetails(this.formData.patrol_plan_id);
        }
      });
    },
    // 显示执行人选择器
    showExecutorSelector() {
      if (this.executors.length === 0) {
        common_vendor.index.showToast({
          title: "暂无可选执行人",
          icon: "none"
        });
        return;
      }
      this.selectedExecutorIds = [...this.formData.executor_ids];
      this.$refs.executorPopup.open();
    },
    // 显示开始时间选择器
    showStartTimePicker() {
      this.currentDatetimeType = "start";
      this.initDateTimePicker(this.formData.start_time);
      this.$refs.datetimePopup.open();
    },
    // 显示结束时间选择器
    showEndTimePicker() {
      this.currentDatetimeType = "end";
      this.initDateTimePicker(this.formData.end_time);
      this.$refs.datetimePopup.open();
    },
    // 关闭时间选择器
    closeDatetimePicker() {
      this.$refs.datetimePopup.close();
    },
    // 初始化时间选择器
    initDateTimePicker(dateTimeStr) {
      let date;
      if (dateTimeStr) {
        date = new Date(dateTimeStr);
        if (isNaN(date.getTime())) {
          date = /* @__PURE__ */ new Date();
        }
      } else {
        date = /* @__PURE__ */ new Date();
      }
      this.year = date.getFullYear();
      this.month = date.getMonth() + 1;
      this.day = date.getDate();
      this.hour = date.getHours();
      this.minute = date.getMinutes();
      this.second = date.getSeconds();
      this.years = [];
      for (let i = this.year - 10; i <= this.year + 10; i++) {
        this.years.push(i);
      }
      this.months = [];
      for (let i = 1; i <= 12; i++) {
        this.months.push(i);
      }
      this.updateDays();
      this.hours = [];
      for (let i = 0; i <= 23; i++) {
        this.hours.push(i);
      }
      this.minutes = [];
      this.seconds = [];
      for (let i = 0; i <= 59; i++) {
        this.minutes.push(i);
        this.seconds.push(i);
      }
      this.updatePickerValue();
    },
    // 更新天数，根据年月确定
    updateDays() {
      let daysInMonth = 31;
      if (this.month === 2) {
        daysInMonth = this.year % 4 === 0 && this.year % 100 !== 0 || this.year % 400 === 0 ? 29 : 28;
      } else if ([4, 6, 9, 11].includes(this.month)) {
        daysInMonth = 30;
      }
      this.days = [];
      for (let i = 1; i <= daysInMonth; i++) {
        this.days.push(i);
      }
      if (this.day > daysInMonth) {
        this.day = daysInMonth;
      }
    },
    // 更新pickerValue
    updatePickerValue() {
      const yearIndex = this.years.indexOf(this.year);
      const monthIndex = this.months.indexOf(this.month);
      const dayIndex = this.days.indexOf(this.day);
      const hourIndex = this.hours.indexOf(this.hour);
      const minuteIndex = this.minutes.indexOf(this.minute);
      const secondIndex = this.seconds.indexOf(this.second);
      this.pickerValue = [
        yearIndex !== -1 ? yearIndex : 0,
        monthIndex !== -1 ? monthIndex : 0,
        dayIndex !== -1 ? dayIndex : 0,
        hourIndex !== -1 ? hourIndex : 0,
        minuteIndex !== -1 ? minuteIndex : 0,
        secondIndex !== -1 ? secondIndex : 0
      ];
    },
    // 日期时间选择器变化处理
    dateTimePickerChange(e) {
      const val = e.detail.value;
      this.year = this.years[val[0]];
      this.month = this.months[val[1]];
      this.updateDays();
      if (val[2] >= this.days.length) {
        val[2] = 0;
      }
      this.day = this.days[val[2]];
      this.hour = this.hours[val[3]];
      this.minute = this.minutes[val[4]];
      this.second = this.seconds[val[5]];
    },
    // 确认日期时间选择
    confirmDateTime() {
      const datetimeStr = this.formatPickerDateTime();
      if (this.currentDatetimeType === "start") {
        this.formData.start_time = datetimeStr;
        if (this.formData.end_time && new Date(this.formData.start_time) > new Date(this.formData.end_time)) {
          this.formData.end_time = datetimeStr;
        }
      } else {
        this.formData.end_time = datetimeStr;
        if (this.formData.start_time && new Date(this.formData.end_time) < new Date(this.formData.start_time)) {
          common_vendor.index.showToast({
            title: "结束时间不能早于开始时间",
            icon: "none"
          });
          this.formData.end_time = this.formData.start_time;
        }
      }
      this.closeDatetimePicker();
    },
    // 格式化选择器日期时间
    formatPickerDateTime() {
      const year = this.year;
      const month = String(this.month).padStart(2, "0");
      const day = String(this.day).padStart(2, "0");
      const hour = String(this.hour).padStart(2, "0");
      const minute = String(this.minute).padStart(2, "0");
      const second = String(this.second).padStart(2, "0");
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    },
    // 编辑巡检结果
    editPatrolResult(index) {
      this.currentResultIndex = index;
      const resultCopy = JSON.parse(JSON.stringify(this.formData.patrol_results[index]));
      if (!resultCopy.images) {
        resultCopy.images = [];
      }
      this.currentResult = resultCopy;
      this.$refs.resultPopup.open();
    },
    // 关闭结果弹窗
    closeResultPopup() {
      this.$refs.resultPopup.close();
    },
    // 保存结果
    saveResult() {
      if (this.currentResult.item_info && this.currentResult.item_info.normalRange && !this.currentResult.param_value) {
        common_vendor.index.showToast({
          title: "请输入当前测量值",
          icon: "none"
        });
        return;
      }
      if (this.currentResultIndex >= 0) {
        if (!this.currentResult.images) {
          this.currentResult.images = [];
        }
        this.formData.patrol_results[this.currentResultIndex] = JSON.parse(JSON.stringify(this.currentResult));
      }
      this.closeResultPopup();
    },
    // 选择图片
    chooseImage() {
      var _a;
      common_vendor.index.chooseImage({
        count: 4 - (((_a = this.currentResult.images) == null ? void 0 : _a.length) || 0),
        success: (res) => {
          const tempFilePaths = res.tempFilePaths;
          const newImages = tempFilePaths.map((path) => ({
            url: path,
            file: path
          }));
          if (!this.currentResult.images) {
            this.currentResult.images = [];
          }
          this.currentResult.images = [...this.currentResult.images, ...newImages];
        }
      });
    },
    // 删除图片
    removeImage(index) {
      this.currentResult.images.splice(index, 1);
    },
    // 获取位置信息
    getLocation() {
      common_vendor.index.getLocation({
        type: "gcj02",
        success: (res) => {
          this.currentResult.latitude = res.latitude;
          this.currentResult.longitude = res.longitude;
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/patrol/create_record.vue:881", "获取位置失败:", err);
          common_vendor.index.showToast({
            title: "获取位置失败，请检查位置权限",
            icon: "none"
          });
        }
      });
    },
    // 表单验证
    validateForm() {
      if (!this.formData.patrol_plan_id) {
        common_vendor.index.showToast({
          title: "请选择巡检计划",
          icon: "none"
        });
        return false;
      }
      if (this.formData.executor_ids.length === 0) {
        common_vendor.index.showToast({
          title: "请选择执行人",
          icon: "none"
        });
        return false;
      }
      if (!this.formData.start_time) {
        common_vendor.index.showToast({
          title: "请选择开始时间",
          icon: "none"
        });
        return false;
      }
      if (!this.formData.end_time) {
        common_vendor.index.showToast({
          title: "请选择结束时间",
          icon: "none"
        });
        return false;
      }
      if (new Date(this.formData.end_time) < new Date(this.formData.start_time)) {
        common_vendor.index.showToast({
          title: "结束时间不能早于开始时间",
          icon: "none"
        });
        return false;
      }
      return true;
    },
    // 提交巡检记录
    submitRecord() {
      if (!this.validateForm()) {
        return;
      }
      common_vendor.index.showModal({
        title: "提交确认",
        content: "确定要提交巡检记录吗？",
        success: (res) => {
          if (res.confirm) {
            this.doSubmit();
          }
        }
      });
    },
    // 执行提交操作
    doSubmit() {
      this.submitting = true;
      const submitData = {
        patrol_plan_id: this.formData.patrol_plan_id,
        // 巡检计划ID
        executor_id: this.formData.executor_ids[0] || "",
        // 执行人ID
        start_time: this.formData.start_time,
        // 开始时间
        end_time: this.formData.end_time,
        // 结束时间
        status: this.formData.status,
        // 状态:pending,processing,completed
        remark: this.formData.remark || "",
        // 备注
        patrol_results: []
        // 巡检结果数组
      };
      if (this.formData.patrol_results && this.formData.patrol_results.length > 0) {
        submitData.patrol_results = this.formData.patrol_results.map((result) => {
          const cleanResult = {
            patrol_item_id: result.patrol_item_id,
            // 巡检项ID
            check_result: result.check_result,
            // 检查结果:normal-正常,abnormal-异常
            param_value: result.param_value || result.parameter_value || "",
            // 参数值
            description: result.description || "",
            // 描述
            latitude: result.latitude ? Number(result.latitude) : 0,
            // 经度，确保为数字类型
            longitude: result.longitude ? Number(result.longitude) : 0
            // 纬度，确保为数字类型
          };
          try {
            if (result.images && result.images.length) {
              const imagesArray = Array.from(result.images);
              cleanResult.images = imagesArray.filter(Boolean).map(
                (img) => typeof img === "string" ? img : img && img.url ? img.url : ""
              ).filter((url) => url);
            } else {
              cleanResult.images = [];
            }
          } catch (e) {
            common_vendor.index.__f__("error", "at pages/patrol/create_record.vue:994", "处理图片字段出错:", e);
            cleanResult.images = [];
          }
          return cleanResult;
        });
      }
      common_vendor.index.__f__("log", "at pages/patrol/create_record.vue:1002", "提交数据:", JSON.stringify(submitData));
      utils_api.patrolApi.submitPatrolRecord(submitData).then((res) => {
        if (res.code === 200) {
          common_vendor.index.showToast({
            title: "巡检记录提交成功",
            icon: "success"
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1500);
        } else {
          common_vendor.index.showToast({
            title: res.message || "提交失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/patrol/create_record.vue:1024", "提交巡检记录错误:", err);
        common_vendor.index.showToast({
          title: "提交失败，请重试",
          icon: "none"
        });
      }).finally(() => {
        this.submitting = false;
      });
    },
    // 关闭执行人选择器
    closeExecutorSelector() {
      this.$refs.executorPopup.close();
      this.executorSearchKey = "";
    },
    // 处理执行人选择变化
    handleExecutorChange(e) {
      this.selectedExecutorIds = e.detail.value.map((id) => parseInt(id));
    },
    // 确认执行人选择
    confirmExecutorSelection() {
      this.formData.executor_ids = [...this.selectedExecutorIds];
      this.updateSelectedExecutorNames();
      this.closeExecutorSelector();
    },
    // 检查执行人是否被选中
    isExecutorSelected(executorId) {
      return this.selectedExecutorIds.includes(executorId);
    }
  }
};
if (!Array) {
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  _easycom_uni_popup2();
}
const _easycom_uni_popup = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.formData.patrol_plan_id
  }, $data.formData.patrol_plan_id ? {
    b: common_vendor.t($data.selectedPlanName)
  } : {}, {
    c: common_vendor.o((...args) => $options.showPlanSelector && $options.showPlanSelector(...args)),
    d: $data.formData.executor_ids && $data.formData.executor_ids.length > 0
  }, $data.formData.executor_ids && $data.formData.executor_ids.length > 0 ? {
    e: common_vendor.t($data.formData.executor_ids.length),
    f: common_vendor.t($data.selectedExecutorNames ? "：" + $data.selectedExecutorNames : "")
  } : {}, {
    g: common_vendor.o((...args) => $options.showExecutorSelector && $options.showExecutorSelector(...args)),
    h: $data.formData.start_time
  }, $data.formData.start_time ? {
    i: common_vendor.t($data.formData.start_time)
  } : {}, {
    j: common_vendor.o((...args) => $options.showStartTimePicker && $options.showStartTimePicker(...args)),
    k: $data.formData.end_time
  }, $data.formData.end_time ? {
    l: common_vendor.t($data.formData.end_time)
  } : {}, {
    m: common_vendor.o((...args) => $options.showEndTimePicker && $options.showEndTimePicker(...args)),
    n: common_vendor.f($data.statusOptions, (status, k0, i0) => {
      return {
        a: common_vendor.t(status.label),
        b: status.value,
        c: $data.formData.status === status.value ? 1 : "",
        d: common_vendor.o(($event) => $data.formData.status = status.value, status.value)
      };
    }),
    o: $data.formData.remark,
    p: common_vendor.o(($event) => $data.formData.remark = $event.detail.value),
    q: common_vendor.t($data.formData.remark.length),
    r: $data.formData.patrol_results.length > 0
  }, $data.formData.patrol_results.length > 0 ? {
    s: common_vendor.f($data.formData.patrol_results, (result, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(result.item_info.itemName),
        b: common_vendor.t(result.check_result === "normal" ? "正常" : "异常"),
        c: common_vendor.n(result.check_result),
        d: common_vendor.t(result.item_info.deviceName || "未知设备"),
        e: result.item_info.normalRange
      }, result.item_info.normalRange ? {
        f: common_vendor.t(result.item_info.normalRange)
      } : {}, {
        g: result.param_value
      }, result.param_value ? {
        h: common_vendor.t(result.param_value),
        i: common_vendor.t(result.item_info.unit ? " " + result.item_info.unit : "")
      } : {}, {
        j: result.description
      }, result.description ? {
        k: common_vendor.t(result.description)
      } : {}, {
        l: result.images && result.images.length > 0
      }, result.images && result.images.length > 0 ? {
        m: common_vendor.t(result.images.length)
      } : {}, {
        n: index,
        o: common_vendor.o(($event) => $options.editPatrolResult(index), index)
      });
    })
  } : {}, {
    t: common_vendor.t($data.submitting ? "提交中..." : "提交巡检记录"),
    v: $data.submitting ? 1 : "",
    w: $data.submitting,
    x: common_vendor.o((...args) => $options.submitRecord && $options.submitRecord(...args)),
    y: common_vendor.t($data.currentResult.item_info ? $data.currentResult.item_info.itemName : ""),
    z: common_vendor.o((...args) => $options.closeResultPopup && $options.closeResultPopup(...args)),
    A: $data.currentResult.item_info
  }, $data.currentResult.item_info ? common_vendor.e({
    B: common_vendor.t($data.currentResult.item_info.deviceName || "未知设备"),
    C: $data.currentResult.item_info.categoryName
  }, $data.currentResult.item_info.categoryName ? {
    D: common_vendor.t($data.currentResult.item_info.categoryName)
  } : {}) : {}, {
    E: $data.currentResult.item_info && $data.currentResult.item_info.normalRange
  }, $data.currentResult.item_info && $data.currentResult.item_info.normalRange ? common_vendor.e({
    F: common_vendor.t($data.currentResult.item_info.normalRange),
    G: $data.currentResult.item_info.checkMethod
  }, $data.currentResult.item_info.checkMethod ? {
    H: common_vendor.t($data.currentResult.item_info.checkMethod)
  } : {}) : {}, {
    I: $data.currentResult.item_info && $data.currentResult.item_info.normalRange
  }, $data.currentResult.item_info && $data.currentResult.item_info.normalRange ? common_vendor.e({
    J: $data.currentResult.param_value,
    K: common_vendor.o(($event) => $data.currentResult.param_value = $event.detail.value),
    L: $data.currentResult.item_info.unit
  }, $data.currentResult.item_info.unit ? {
    M: common_vendor.t($data.currentResult.item_info.unit)
  } : {}) : {}, {
    N: $data.currentResult.check_result === "normal" ? 1 : "",
    O: common_vendor.o(($event) => $data.currentResult.check_result = "normal"),
    P: $data.currentResult.check_result === "abnormal" ? 1 : "",
    Q: common_vendor.o(($event) => $data.currentResult.check_result = "abnormal"),
    R: $data.currentResult.item_info && !$data.currentResult.item_info.normalRange && $data.currentResult.item_info.hasValueInput
  }, $data.currentResult.item_info && !$data.currentResult.item_info.normalRange && $data.currentResult.item_info.hasValueInput ? common_vendor.e({
    S: $data.currentResult.param_value,
    T: common_vendor.o(($event) => $data.currentResult.param_value = $event.detail.value),
    U: $data.currentResult.item_info.unit
  }, $data.currentResult.item_info.unit ? {
    V: common_vendor.t($data.currentResult.item_info.unit)
  } : {}) : {}, {
    W: $data.currentResult.description,
    X: common_vendor.o(($event) => $data.currentResult.description = $event.detail.value),
    Y: $data.currentResult
  }, $data.currentResult ? common_vendor.e({
    Z: common_vendor.f($data.currentResult.images, (image, index, i0) => {
      return {
        a: image,
        b: common_vendor.o(($event) => $options.removeImage(index), index),
        c: index
      };
    }),
    aa: $data.currentResult.images.length < 4
  }, $data.currentResult.images.length < 4 ? {
    ab: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args))
  } : {}) : {}, {
    ac: common_vendor.o((...args) => $options.getLocation && $options.getLocation(...args)),
    ad: $data.currentResult.latitude && $data.currentResult.longitude
  }, $data.currentResult.latitude && $data.currentResult.longitude ? {
    ae: common_vendor.t($data.currentResult.latitude.toFixed(4)),
    af: common_vendor.t($data.currentResult.longitude.toFixed(4))
  } : {}, {
    ag: common_vendor.o((...args) => $options.saveResult && $options.saveResult(...args)),
    ah: common_vendor.sr("resultPopup", "a56eebf2-0"),
    ai: common_vendor.p({
      type: "bottom"
    }),
    aj: common_vendor.t($data.currentDatetimeType === "start" ? "选择开始时间" : "选择结束时间"),
    ak: common_vendor.o((...args) => $options.closeDatetimePicker && $options.closeDatetimePicker(...args)),
    al: common_vendor.f($data.years, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: "year-" + index
      };
    }),
    am: common_vendor.f($data.months, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: "month-" + index
      };
    }),
    an: common_vendor.f($data.days, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: "day-" + index
      };
    }),
    ao: common_vendor.f($data.hours, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: "hour-" + index
      };
    }),
    ap: common_vendor.f($data.minutes, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: "minute-" + index
      };
    }),
    aq: common_vendor.f($data.seconds, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: "second-" + index
      };
    }),
    ar: $data.indicatorStyle,
    as: $data.pickerValue,
    at: common_vendor.o((...args) => $options.dateTimePickerChange && $options.dateTimePickerChange(...args)),
    av: common_vendor.o((...args) => $options.confirmDateTime && $options.confirmDateTime(...args)),
    aw: common_vendor.sr("datetimePopup", "a56eebf2-1"),
    ax: common_vendor.p({
      type: "bottom"
    }),
    ay: common_vendor.o((...args) => $options.closeExecutorSelector && $options.closeExecutorSelector(...args)),
    az: $data.executorSearchKey,
    aA: common_vendor.o(($event) => $data.executorSearchKey = $event.detail.value),
    aB: common_vendor.f($options.filteredExecutors, (executor, index, i0) => {
      return common_vendor.e({
        a: executor.id.toString(),
        b: $options.isExecutorSelected(executor.id),
        c: common_vendor.t(executor.name),
        d: executor.departmentName
      }, executor.departmentName ? {
        e: common_vendor.t(executor.departmentName)
      } : {}, {
        f: index
      });
    }),
    aC: common_vendor.o((...args) => $options.handleExecutorChange && $options.handleExecutorChange(...args)),
    aD: common_vendor.t($data.formData.executor_ids.length),
    aE: common_vendor.o((...args) => $options.confirmExecutorSelection && $options.confirmExecutorSelection(...args)),
    aF: common_vendor.sr("executorPopup", "a56eebf2-2"),
    aG: common_vendor.p({
      type: "bottom"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/patrol/create_record.js.map
