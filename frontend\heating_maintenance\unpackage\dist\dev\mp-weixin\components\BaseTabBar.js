"use strict";
const common_vendor = require("../common/vendor.js");
const CustomTabBar = () => "./CustomTabBar.js";
const _sfc_main = {
  name: "BaseTabBar",
  components: {
    CustomTabBar
  },
  data() {
    return {
      showTabBar: true,
      tabBarPages: [
        "pages/home/<USER>",
        "pages/hes/list",
        "pages/message/center",
        "pages/user/info"
      ]
    };
  },
  onShow() {
    const pages = getCurrentPages();
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1];
      const currentRoute = currentPage.route;
      this.showTabBar = this.tabBarPages.includes(currentRoute);
    }
  }
};
if (!Array) {
  const _component_CustomTabBar = common_vendor.resolveComponent("CustomTabBar");
  _component_CustomTabBar();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.showTabBar
  }, $data.showTabBar ? {} : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../.sourcemap/mp-weixin/components/BaseTabBar.js.map
