{"version": 3, "file": "create.js", "sources": ["pages/patrol/create.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcGF0cm9sL2NyZWF0ZS52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"patrol-create-container\">\r\n\t\t<form @submit=\"submitPlan\">\r\n\t\t\t<!-- 基本信息 -->\r\n\t\t\t<view class=\"form-card\">\r\n\t\t\t\t<view class=\"card-title\">基本信息</view> \r\n\t\t\t\t\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<text class=\"form-label required\">计划名称</text>\r\n\t\t\t\t\t<input \r\n\t\t\t\t\t\tclass=\"form-input plan-name-input\" \r\n\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\tv-model=\"formData.name\" \r\n\t\t\t\t\t\tplaceholder=\"请输入计划名称\"\r\n\t\t\t\t\t\t:maxlength=\"50\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<text class=\"form-label required\">巡检周期</text>\r\n\t\t\t\t\t<picker \r\n\t\t\t\t\t\tclass=\"form-picker\" \r\n\t\t\t\t\t\t:range=\"scheduleTypeOptions\" \r\n\t\t\t\t\t\trange-key=\"label\"\r\n\t\t\t\t\t\t:value=\"scheduleTypeIndex\" \r\n\t\t\t\t\t\t@change=\"handleScheduleTypeChange\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view class=\"picker-value placeholder-style\" style=\"padding-left: 20rpx;\">{{ scheduleTypeOptions[scheduleTypeIndex].label }}</view>\r\n\t\t\t\t\t</picker>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<text class=\"form-label required\">巡检类型</text>\r\n\t\t\t\t\t<picker \r\n\t\t\t\t\t\tclass=\"form-picker\" \r\n\t\t\t\t\t\t:range=\"patrolTypeOptions\"\r\n\t\t\t\t\t\trange-key=\"label\"\r\n\t\t\t\t\t\t:value=\"patrolTypeIndex\" \r\n\t\t\t\t\t\t@change=\"handlePatrolTypeChange\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view class=\"picker-value placeholder-style\" style=\"padding-left: 20rpx;\">{{ patrolTypeOptions[patrolTypeIndex].label }}</view>\r\n\t\t\t\t\t</picker>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<text class=\"form-label required\">开始日期</text>\r\n\t\t\t\t\t<picker \r\n\t\t\t\t\t\tclass=\"form-picker\" \r\n\t\t\t\t\t\tmode=\"date\" \r\n\t\t\t\t\t\t:value=\"formData.startDate\" \r\n\t\t\t\t\t\tstart=\"2023-01-01\" \r\n\t\t\t\t\t\tend=\"2030-12-31\" \r\n\t\t\t\t\t\t@change=\"handleStartDateChange\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view class=\"picker-value placeholder-style\" style=\"padding-left: 20rpx;\">{{ formData.startDate || '请选择开始日期' }}</view>\r\n\t\t\t\t\t</picker>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<text class=\"form-label required\">结束日期</text>\r\n\t\t\t\t\t<picker \r\n\t\t\t\t\t\tclass=\"form-picker\" \r\n\t\t\t\t\t\tmode=\"date\" \r\n\t\t\t\t\t\t:value=\"formData.endDate\" \r\n\t\t\t\t\t\t:start=\"formData.startDate || '2023-01-01'\" \r\n\t\t\t\t\t\tend=\"2030-12-31\" \r\n\t\t\t\t\t\t@change=\"handleEndDateChange\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view class=\"picker-value placeholder-style\" style=\"padding-left: 20rpx;\">{{ formData.endDate || '请选择结束日期' }}</view>\r\n\t\t\t\t\t</picker>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 修改巡检地点为热用户选择 -->\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<text class=\"form-label required\">巡检地点</text>\r\n\t\t\t\t\t<view class=\"heatunit-selector\" @click=\"showHeatUnitSelector\">\r\n\t\t\t\t\t\t<view class=\"form-input location-input\">\r\n\t\t\t\t\t\t\t<text v-if=\"selectedHeatUnit\">{{ selectedHeatUnit.name }}</text>\r\n\t\t\t\t\t\t\t<text v-else>{{ filteredHeatUnits.length > 0 ? '请选择热用户' : '无可用热用户' }}</text>\r\n\t\t\t\t\t\t\t<text class=\"arrow-down\">▼</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<text class=\"form-label\">巡检人</text>\r\n\t\t\t\t\t<view class=\"staff-selector\" @click=\"showStaffSelector\">\r\n\t\t\t\t\t\t<view class=\"form-input location-input\">\r\n\t\t\t\t\t\t\t<text>{{ selectedExecutors.length > 0 ? `已选择 ${selectedExecutors.length} 个人员` : '请选择巡检人员' }}</text>\r\n\t\t\t\t\t\t\t<text class=\"arrow-down\">▼</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 已选人员显示区域 -->\r\n\t\t\t\t\t<view class=\"selected-items\" v-if=\"selectedExecutors.length > 0\">\r\n\t\t\t\t\t\t<view class=\"selected-item\" v-for=\"(executor, index) in selectedExecutors\" :key=\"'executor-' + index\">\r\n\t\t\t\t\t\t\t<text class=\"item-text\">{{ executor.name }}</text>\r\n\t\t\t\t\t\t\t<text class=\"remove-icon\" @click.stop=\"removeExecutor(executor.id)\">×</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t<!-- 修改巡检设备部分，关联到选择的热用户 -->\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<text class=\"form-label required\">巡检设备</text>\r\n\t\t\t\t\t<view class=\"device-selector\" @click=\"showDeviceSelector\">\r\n\t\t\t\t\t\t<view class=\"form-input location-input\">\r\n\t\t\t\t\t\t\t<text>{{ selectedDevices.length > 0 ? `已选择 ${selectedDevices.length} 个设备` : '请选择巡检设备' }}</text>\r\n\t\t\t\t\t\t\t<text class=\"arrow-down\">▼</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 已选设备显示区域 -->\r\n\t\t\t\t\t<view class=\"selected-items\" v-if=\"selectedDevices.length > 0\">\r\n\t\t\t\t\t\t<view class=\"selected-item\" v-for=\"(device, index) in selectedDevices\" :key=\"'device-' + index\">\r\n\t\t\t\t\t\t\t<text class=\"item-text\">{{ device.name }}</text>\r\n\t\t\t\t\t\t\t<text class=\"remove-icon\" @click.stop=\"removeDevice(device.id)\">×</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t<text class=\"form-label\">计划描述</text>\r\n\t\t\t\t\t<textarea \r\n\t\t\t\t\t\tclass=\"form-textarea\" \r\n\t\t\t\t\t\tv-model=\"formData.description\" \r\n\t\t\t\t\t\tplaceholder=\"请输入巡检计划描述（选填）\"\r\n\t\t\t\t\t\t:maxlength=\"500\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 添加周期类型相关的组件 -->\r\n\t\t\t\t<view class=\"form-item\" v-if=\"formData.scheduleType === 'custom'\">\r\n\t\t\t\t\t<text class=\"form-label required\">巡检间隔(天)</text>\r\n\t\t\t\t\t<input \r\n\t\t\t\t\t\tclass=\"form-input\" \r\n\t\t\t\t\t\ttype=\"number\" \r\n\t\t\t\t\t\tv-model=\"formData.scheduleInterval\" \r\n\t\t\t\t\t\tplaceholder=\"请输入巡检间隔天数\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"form-item\" v-if=\"formData.scheduleType === 'weekly'\">\r\n\t\t\t\t\t<text class=\"form-label required\">巡检星期</text>\r\n\t\t\t\t\t<view class=\"tag-group\">\r\n\t\t\t\t\t\t<view \r\n\t\t\t\t\t\t\tv-for=\"(day, index) in weekOptions\" \r\n\t\t\t\t\t\t\t:key=\"'week-' + index\"\r\n\t\t\t\t\t\t\tclass=\"tag\" \r\n\t\t\t\t\t\t\t:class=\"{ active: formData.scheduleWeekDays.includes(day.value) }\"\r\n\t\t\t\t\t\t\t@click=\"toggleWeekDay(day.value)\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{{ day.label }}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"form-item\" v-if=\"formData.scheduleType === 'monthly'\">\r\n\t\t\t\t\t<text class=\"form-label required\">巡检日期</text>\r\n\t\t\t\t\t<view class=\"tag-group\">\r\n\t\t\t\t\t\t<view \r\n\t\t\t\t\t\t\tv-for=\"(day, index) in filteredMonthOptions\" \r\n\t\t\t\t\t\t\t:key=\"'month-' + index\"\r\n\t\t\t\t\t\t\tclass=\"tag\" \r\n\t\t\t\t\t\t\t:class=\"{ active: formData.scheduleMonthDays.includes(day.value) }\"\r\n\t\t\t\t\t\t\t@click=\"toggleMonthDay(day.value)\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{{ day.label }}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 巡检任务 -->\r\n\t\t\t<view class=\"form-card\">\r\n\t\t\t\t<view class=\"card-header\">\r\n\t\t\t\t\t<view class=\"card-title\">巡检任务</view>\r\n\t\t\t\t\t<view class=\"add-button\" @click=\"showTaskSelector\" :class=\"{ disabled: selectedDevices.length === 0 }\">\r\n\t\t\t\t\t\t<text class=\"iconfont icon-add\"></text>\r\n\t\t\t\t\t\t<text>添加巡检项目</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"task-list\" v-if=\"formData.patrolItem.length > 0\">\r\n\t\t\t\t\t<view class=\"task-item\" v-for=\"(task, index) in formData.patrolItem\" :key=\"'task-' + index\">\r\n\t\t\t\t\t\t<view class=\"task-content\">\r\n\t\t\t\t\t\t\t<text class=\"task-title\">{{ task.itemName }}</text>\r\n\t\t\t\t\t\t\t<view class=\"task-info-row\">\r\n\t\t\t\t\t\t\t\t<text class=\"task-info\">所属设备: {{ getDeviceName(task.deviceId) }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"importance-tag\" :class=\"getImportanceClass(task.importance)\">{{ getImportanceLabel(task.importance) }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"task-info\">所属类型: {{ task.categoryName }}</text>\r\n\t\t\t\t\t\t\t<text class=\"task-info\">检测标准: {{ task.description }}</text>\r\n\t\t\t\t\t\t\t<text class=\"task-info\">检测方法: {{ task.checkMethod }}</text>\r\n\t\t\t\t\t\t\t<text class=\"task-info\" v-if=\"task.paramType === 'number'\">检测范围: {{ task.normalRange }} {{ task.unit }}</text>\r\n\t\t\t\t\t\t\t<text class=\"task-info\" v-else-if=\"task.paramType === 'selection'\">可选值: {{ task.normalRange }}</text>\r\n\t\t\t\t\t\t\t<text class=\"task-info\" v-else-if=\"task.paramType === 'boolean'\">期望值: {{ task.normalRange === 'true' ? '是' : '否' }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"task-actions\">\r\n\t\t\t\t\t\t\t<text class=\"delete-icon\" @click=\"removeTask(index)\">×</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"empty-tip\" v-else>\r\n\t\t\t\t\t<text>请添加巡检任务</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 提交按钮 -->\r\n\t\t\t<button class=\"submit-button\" form-type=\"submit\" :style=\"{ display: isPopupOpen ? 'none' : 'block' }\">创建巡检计划</button>\r\n\t\t</form>\r\n\t\t\r\n\t\t<!-- 热用户选择弹窗 -->\r\n\t\t<uni-popup ref=\"heatUnitSelector\" type=\"bottom\">\r\n\t\t\t<view class=\"selector heat-unit-selector\">\r\n\t\t\t\t<view class=\"selector-header\">\r\n\t\t\t\t\t<text class=\"selector-title\">选择热用户</text>\r\n\t\t\t\t\t<view class=\"header-actions\">\r\n\t\t\t\t\t\t<text class=\"confirm-button-header\" @click=\"confirmHeatUnitSelection\">确定</text>\r\n\t\t\t\t\t\t<text class=\"close-button\" @click=\"hideHeatUnitSelector\">关闭</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"selector-content\">\r\n\t\t\t\t\t<view class=\"template-search\">\r\n\t\t\t\t\t\t<input \r\n\t\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\t\tv-model=\"heatUnitSearchKeyword\" \r\n\t\t\t\t\t\t\tplaceholder=\"搜索热用户\" \r\n\t\t\t\t\t\t\tconfirm-type=\"search\"\r\n\t\t\t\t\t\t\t@input=\"handleHeatUnitSearch\"\r\n\t\t\t\t\t\t\tclass=\"search-input\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t<text \r\n\t\t\t\t\t\t\tclass=\"icon-clear\" \r\n\t\t\t\t\t\t\tv-if=\"heatUnitSearchKeyword\" \r\n\t\t\t\t\t\t\t@click=\"clearHeatUnitSearch\"\r\n\t\t\t\t\t\t></text>\r\n\t\t\t\t\t\t<view class=\"search-btn\" @click=\"handleHeatUnitSearch\">搜索</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"heatunit-list\">\r\n\t\t\t\t\t\t<view \r\n\t\t\t\t\t\t\tclass=\"heatunit-item\" \r\n\t\t\t\t\t\t\tv-for=\"heatUnit in filteredHeatUnits\" \r\n\t\t\t\t\t\t\t:key=\"heatUnit.id\"\r\n\t\t\t\t\t\t\t@click=\"selectHeatUnit(heatUnit)\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<text class=\"heatunit-name\">{{ heatUnit.name }}</text>\r\n\t\t\t\t\t\t\t<view class=\"checkbox\" :class=\"{ checked: selectedHeatUnit && selectedHeatUnit.id === heatUnit.id }\">\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont icon-check\" v-if=\"selectedHeatUnit && selectedHeatUnit.id === heatUnit.id\"></text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"empty-tip\" v-if=\"filteredHeatUnits.length === 0\">\r\n\t\t\t\t\t\t<text>没有找到相关热用户</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t\t\r\n\t\t<!-- 设备选择弹窗 -->\r\n\t\t<uni-popup ref=\"deviceSelector\" type=\"bottom\">\r\n\t\t\t<view class=\"selector device-selector-popup\">\r\n\t\t\t\t<view class=\"selector-header\">\r\n\t\t\t\t\t<text class=\"selector-title\">选择巡检设备</text>\r\n\t\t\t\t\t<view class=\"header-actions\">\r\n\t\t\t\t\t\t<text class=\"confirm-button-header\" @click=\"confirmDeviceSelection\">确定</text>\r\n\t\t\t\t\t\t<text class=\"close-button\" @click=\"hideDeviceSelector\">关闭</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"selector-content\">\r\n\t\t\t\t\t<!-- 设备全选功能 - 重新设计 -->\r\n\t\t\t\t\t<view class=\"device-select-all-wrapper\" v-if=\"deviceOptions.length > 0\">\r\n\t\t\t\t\t\t<view class=\"device-select-all-card\" @click=\"toggleDeviceSelectAll\">\r\n\t\t\t\t\t\t\t<view class=\"device-select-all-content\">\r\n\t\t\t\t\t\t\t\t<view class=\"device-select-checkbox\" :class=\"{ checked: isAllDevicesSelected }\">\r\n\t\t\t\t\t\t\t\t\t<text v-if=\"isAllDevicesSelected\">✓</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"device-select-info\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"device-select-title\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"select-title-text\">全选设备</text>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"select-status-badge\" :class=\"{ active: isAllDevicesSelected }\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"badge-text\">{{ tempSelectedDevices.length }}/{{ deviceOptions.length }}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"device-list\">\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tclass=\"device-item\"\r\n\t\t\t\t\t\t\tv-for=\"device in deviceOptions\"\r\n\t\t\t\t\t\t\t:key=\"device.id\"\r\n\t\t\t\t\t\t\t@click=\"toggleDeviceSelection(device)\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<text class=\"device-name\">{{ device.name }}</text>\r\n\t\t\t\t\t\t\t<view class=\"checkbox\" :class=\"{ checked: isDeviceSelected(device.id) }\">\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont icon-check\" v-if=\"isDeviceSelected(device.id)\"></text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"empty-tip\" v-if=\"deviceOptions.length === 0\">\r\n\t\t\t\t\t\t<text>暂无可选设备</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t\t\r\n\t\t<!-- 人员选择弹窗 -->\r\n\t\t<uni-popup ref=\"staffSelector\" type=\"bottom\">\r\n\t\t\t<view class=\"selector staff-selector-popup\">\r\n\t\t\t\t<view class=\"selector-header\">\r\n\t\t\t\t\t<text class=\"selector-title\">选择巡检人员</text>\r\n\t\t\t\t\t<view class=\"header-actions\">\r\n\t\t\t\t\t\t<text class=\"confirm-button-header\" @click=\"confirmStaffSelection\">确定</text>\r\n\t\t\t\t\t\t<text class=\"close-button\" @click=\"hideStaffSelector\">关闭</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"selector-content\">\r\n\t\t\t\t\t<view class=\"staff-list\">\r\n\t\t\t\t\t\t<view \r\n\t\t\t\t\t\t\tclass=\"staff-item\" \r\n\t\t\t\t\t\t\tv-for=\"staff in staffOptions\" \r\n\t\t\t\t\t\t\t:key=\"staff.id\"\r\n\t\t\t\t\t\t\t@click=\"toggleStaffSelection(staff)\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<text class=\"staff-name\">{{ staff.name }}</text>\r\n\t\t\t\t\t\t\t<view class=\"checkbox\" :class=\"{ checked: isStaffSelected(staff.id) }\">\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont icon-check\" v-if=\"isStaffSelected(staff.id)\"></text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"empty-tip\" v-if=\"staffOptions.length === 0\">\r\n\t\t\t\t\t\t<text v-if=\"selectedHeatUnit\">当前热用户\"{{ selectedHeatUnit.name }}\"暂无有权限的巡检人员</text>\r\n\t\t\t\t\t\t<text v-else>暂无可选人员</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t\t\r\n\t\t<!-- 巡检项选择弹窗 -->\r\n\t\t<uni-popup ref=\"taskSelector\" type=\"bottom\">\r\n\t\t\t<view class=\"task-selector task-item-selector\">\r\n\t\t\t\t<view class=\"selector-header\">\r\n\t\t\t\t\t<text class=\"selector-title\">选择巡检项目</text>\r\n\t\t\t\t\t<view class=\"header-actions\">\r\n\t\t\t\t\t\t<text class=\"confirm-button-header\" @click=\"confirmTaskSelection\">确定</text>\r\n\t\t\t\t\t\t<text class=\"close-button\" @click=\"hideTaskSelector\">关闭</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"selector-content\">\r\n\t\t\t\t\t<!-- 添加设备筛选选择 -->\r\n\t\t\t\t\t<view style=\"display: flex; align-items: center; padding: 0 10rpx 20rpx; position: relative;\">\r\n\t\t\t\t\t\t<picker\r\n\t\t\t\t\t\t\tstyle=\"flex: 1; height: 80rpx;\"\r\n\t\t\t\t\t\t\t:range=\"[{name: '全部设备'}, ...selectedDevices]\"\r\n\t\t\t\t\t\t\trange-key=\"name\"\r\n\t\t\t\t\t\t\t:value=\"deviceFilterIndex\"\r\n\t\t\t\t\t\t\t@change=\"handleDeviceFilterChange\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<view style=\"background: #f5f5f5; border-radius: 40rpx; padding: 0 30rpx; height: 80rpx; font-size: 28rpx; color: #333; display: flex; justify-content: space-between; align-items: center;\">\r\n\t\t\t\t\t\t\t\t<text>{{ deviceFilterIndex === 0 ? '全部设备' : selectedDevices[deviceFilterIndex-1]?.name }}</text>\r\n\t\t\t\t\t\t\t\t<text style=\"color: #999; font-size: 24rpx;\">▼</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"template-search\">\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\t\tv-model=\"searchKeyword\"\r\n\t\t\t\t\t\t\tplaceholder=\"搜索巡检项\"\r\n\t\t\t\t\t\t\tconfirm-type=\"search\"\r\n\t\t\t\t\t\t\t@input=\"handleSearch\"\r\n\t\t\t\t\t\t\tclass=\"search-input\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t<text\r\n\t\t\t\t\t\t\tclass=\"search-clear\"\r\n\t\t\t\t\t\t\tv-if=\"searchKeyword\"\r\n\t\t\t\t\t\t\t@click=\"clearSearch\"\r\n\t\t\t\t\t\t>×</text>\r\n\t\t\t\t\t\t<view class=\"search-btn\" @click=\"handleSearch\">搜索</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 全选巡检项功能 - 优化尺寸 -->\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tv-if=\"filteredTemplates.length > 0\"\r\n\t\t\t\t\t\t@click=\"toggleTaskSelectAll\"\r\n\t\t\t\t\t\tstyle=\"\r\n\t\t\t\t\t\t\tbackground: linear-gradient(135deg, #f0f8ff 0%, #ffffff 100%);\r\n\t\t\t\t\t\t\tborder: 2px solid #007aff;\r\n\t\t\t\t\t\t\tborder-radius: 8px;\r\n\t\t\t\t\t\t\tpadding: 16px;\r\n\t\t\t\t\t\t\tmargin-bottom: 16px;\r\n\t\t\t\t\t\t\tbox-shadow: 0 1px 4px rgba(0, 122, 255, 0.1);\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t:style=\"{\r\n\t\t\t\t\t\t\t\twidth: '20px',\t\r\n\t\t\t\t\t\t\t\theight: '20px',\r\n\t\t\t\t\t\t\t\tborder: '2px solid #007aff',\r\n\t\t\t\t\t\t\t\tborderRadius: '6px',\r\n\t\t\t\t\t\t\t\tdisplay: 'flex',\r\n\t\t\t\t\t\t\t\talignItems: 'center',\r\n\t\t\t\t\t\t\t\tjustifyContent: 'center',\r\n\t\t\t\t\t\t\t\tmarginRight: '12px',\r\n\t\t\t\t\t\t\t\tflexShrink: '0',\r\n\t\t\t\t\t\t\t\tbackgroundColor: isAllTasksSelected ? '#007aff' : '#fff'\r\n\t\t\t\t\t\t\t}\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<text\r\n\t\t\t\t\t\t\t\tv-if=\"isAllTasksSelected\"\r\n\t\t\t\t\t\t\t\tstyle=\"color: #fff; font-size: 18px; font-weight: bold;\"\r\n\t\t\t\t\t\t\t>✓</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view style=\"flex: 1;\">\r\n\t\t\t\t\t\t\t<view style=\"display: flex; justify-content: space-between; align-items: center;\">\r\n\t\t\t\t\t\t\t\t<text style=\"font-size: 14px; font-weight: bold; color: #007aff;\">全选巡检项</text>\r\n\t\t\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t\t\t:style=\"{\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: isAllTasksSelected ? '#007aff' : '#e6f7ff',\r\n\t\t\t\t\t\t\t\t\t\tcolor: isAllTasksSelected ? '#fff' : '#007aff',\r\n\t\t\t\t\t\t\t\t\t\tfontSize: '14px',\r\n\t\t\t\t\t\t\t\t\t\tpadding: '2px 8px',\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: '14px',\r\n\t\t\t\t\t\t\t\t\t\tfontWeight: '600',\r\n\t\t\t\t\t\t\t\t\t\tborder: isAllTasksSelected ? '1px solid #007aff' : '1px solid #91d5ff'\r\n\t\t\t\t\t\t\t\t\t}\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<text>{{ selectedTaskCount }}/{{ filteredTemplates.length }}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"task-templates\">\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tclass=\"template-item\"\r\n\t\t\t\t\t\t\tv-for=\"(template, index) in filteredTemplates\"\r\n\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\t:class=\"{ active: template.selected }\"\r\n\t\t\t\t\t\t\t@click=\"toggleTaskSelection(index)\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<view class=\"checkbox\" :class=\"{ checked: template.selected }\">\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont icon-check\" v-if=\"template.selected\"></text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"template-left\">\r\n\t\t\t\t\t\t\t\t<view class=\"template-header\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"template-title\">{{ template.itemName }}</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"importance-tag\" :class=\"getImportanceClass(template.importance)\">{{ getImportanceLabel(template.importance) }}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<text class=\"template-info\">所属设备: {{ getDeviceName(template.deviceId) }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"template-info\">所属类型: {{ template.categoryName }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"template-info template-description\">检测标准: {{ template.description }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"template-info\">检测方法: {{ template.checkMethod }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"template-info\" v-if=\"template.paramType === 'number'\">检测范围: {{ template.normalRange }} {{ template.unit }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"template-info\" v-else-if=\"template.paramType === 'selection'\">可选值: {{ template.normalRange }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"template-info\" v-else-if=\"template.paramType === 'boolean'\">期望值: {{ template.normalRange === 'true' ? '是' : '否' }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"empty-tip\" v-if=\"filteredTemplates.length === 0\">\r\n\t\t\t\t\t\t<text>没有找到相关巡检项模板</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"selector-content-footer\">\r\n\t\t\t\t\t<view style=\"padding: 15rpx 0; border-top: 1rpx solid #e5e5e5; background-color: #007aff; box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.1); position: fixed; bottom: 0; left: 0; right: 0; z-index: 999;\">\r\n\t\t\t\t\t\t<view style=\"display: flex; align-items: center; justify-content: center; padding: 15rpx 0;\">\r\n\t\t\t\t\t\t\t<view style=\"width: 48rpx; height: 48rpx; background-color: white; border-radius: 50%; margin-right: 16rpx; display: flex; align-items: center; justify-content: center;\">\r\n\t\t\t\t\t\t\t\t<text style=\"color: #007aff; font-size: 28rpx; font-weight: bold;\">{{ selectedCount }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text style=\"color: white; font-size: 30rpx; font-weight: bold;\">已选择项目</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { patrolApi, deviceApi, userApi, heatUnitApi, dictApi } from '@/utils/api.js';\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tformData: {\r\n\t\t\t\tplanNo: '',\r\n\t\t\t\tname: '',\r\n\t\t\t\tstartDate: '',\r\n\t\t\t\tendDate: '',\r\n\t\t\t\texecutorIds: [],\r\n\t\t\t\tscheduleType: 'daily',\r\n\t\t\t\tscheduleInterval: null,\r\n\t\t\t\tscheduleWeekDays: [],\r\n\t\t\t\tscheduleMonthDays: [],\r\n\t\t\t\tdeviceIds: [],\r\n\t\t\t\tlocations: '',\r\n\t\t\t\tpatrolItem: [],\r\n\t\t\t\tdescription: '',\r\n\t\t\t\theatUnitId: null\r\n\t\t\t},\r\n\t\t\tscheduleTypeOptions: [\r\n\t\t\t\t{ label: '每日', value: 'daily' },\r\n\t\t\t\t{ label: '每周', value: 'weekly' },\r\n\t\t\t\t{ label: '每月', value: 'monthly' }\r\n\t\t\t],\r\n\t\t\tpatrolTypeOptions: [], // 初始化为空数组，将通过API动态获取\r\n\t\t\tscheduleTypeIndex: 0,\r\n\t\t\tpatrolTypeIndex:0,\r\n\t\t\tstaffOptions: [],\r\n\t\t\tstaffIndex: 0,\r\n\t\t\tdeviceOptions: [],\r\n\t\t\tdeviceIndex: 0,\r\n\t\t\t\r\n\t\t\t// 任务模板\r\n\t\t\ttaskTemplates: [],\r\n\t\t\tdevicePatrolItems: {},\r\n\t\t\tsearchKeyword: '',\r\n\t\t\t\r\n\t\t\t// 周和月选项\r\n\t\t\tweekOptions: [\r\n\t\t\t\t{ label: '周一', value: 1 },\r\n\t\t\t\t{ label: '周二', value: 2 },\r\n\t\t\t\t{ label: '周三', value: 3 },\r\n\t\t\t\t{ label: '周四', value: 4 },\r\n\t\t\t\t{ label: '周五', value: 5 },\r\n\t\t\t\t{ label: '周六', value: 6 },\r\n\t\t\t\t{ label: '周日', value: 7 }\r\n\t\t\t],\r\n\t\t\tmonthOptions: Array.from({ length: 31 }, (_, i) => ({\r\n\t\t\t\tlabel: `${i + 1}`,\r\n\t\t\t\tvalue: i + 1\r\n\t\t\t})),\r\n\t\t\t\r\n\t\t\t// 已选执行人和设备的显示列表\r\n\t\t\tselectedExecutors: [],\r\n\t\t\tselectedDevices: [],\r\n\t\t\t\r\n\t\t\t// 热用户相关\r\n\t\t\tselectedHeatUnit: null,\r\n\t\t\theatUnitOptions: [],\r\n\t\t\theatUnitSearchKeyword: '',\r\n\t\t\tfilteredHeatUnits: [],\r\n\t\t\t\r\n\t\t\t// 设备筛选\r\n\t\t\tdeviceFilterIndex: 0,\r\n\t\t\tcurrentFilterDeviceId: null,\r\n\t\t\t\r\n\t\t\t// 弹窗状态\r\n\t\t\tisPopupOpen: false,\r\n\t\t\ttempSelectedDevices: [], // 临时存储选中的设备\r\n\t\t\ttempSelectedExecutors: [], // 临时存储选中的人员\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\tfilteredTemplates() {\r\n\t\t\tlet templates = [];\r\n\r\n\t\t\tif (this.currentFilterDeviceId) {\r\n\t\t\t\ttemplates = this.devicePatrolItems[this.currentFilterDeviceId] || [];\r\n\t\t\t} else {\r\n\t\t\t\ttemplates = [];\r\n\t\t\t\tthis.selectedDevices.forEach(device => {\r\n\t\t\t\t\tconst items = this.devicePatrolItems[device.id] || [];\r\n\t\t\t\t\ttemplates = templates.concat(items);\r\n\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t\tif (!this.searchKeyword || this.searchKeyword.trim() === '') {\r\n\t\t\t\treturn templates;\r\n\t\t\t}\r\n\r\n\t\t\tconst keyword = this.searchKeyword.toLowerCase().trim();\r\n\t\t\treturn templates.filter(template => {\r\n\t\t\t\treturn (template.itemName && template.itemName.toLowerCase().includes(keyword)) ||\r\n\t\t\t\t\t(template.description && template.description.toLowerCase().includes(keyword)) ||\r\n\t\t\t\t\t(template.checkMethod && template.checkMethod.toLowerCase().includes(keyword)) ||\r\n\t\t\t\t\t(template.categoryName && template.categoryName.toLowerCase().includes(keyword));\r\n\t\t\t});\r\n\t\t},\r\n\t\tselectedCount() {\r\n\t\t\tlet count = 0;\r\n\t\t\tObject.values(this.devicePatrolItems).forEach(items => {\r\n\t\t\t\tcount += items.filter(item => item.selected).length;\r\n\t\t\t});\r\n\t\t\treturn count;\r\n\t\t},\r\n\t\tfilteredMonthOptions() {\r\n\t\t\treturn this.monthOptions.filter((_, index) => index < 31);\r\n\t\t},\r\n\t\t// 设备全选相关计算属性\r\n\t\tisAllDevicesSelected() {\r\n\t\t\treturn this.deviceOptions.length > 0 && this.tempSelectedDevices.length === this.deviceOptions.length;\r\n\t\t},\r\n\t\t// 巡检项全选相关计算属性\r\n\t\tisAllTasksSelected() {\r\n\t\t\treturn this.filteredTemplates.length > 0 && this.selectedTaskCount === this.filteredTemplates.length;\r\n\t\t},\r\n\t\tselectedTaskCount() {\r\n\t\t\treturn this.filteredTemplates.filter(template => template.selected).length;\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\t\tthis.loadHeatUnitList();\r\n\t\t// 注意：不在这里直接加载人员列表，而是在热用户加载完成后加载\r\n\t\tthis.loadPatrolTypeOptions(); // 添加加载巡检类型选项的方法调用\r\n\r\n\t\t// 初始化已选设备和人员列表\r\n\t\tthis.selectedDevices = [];\r\n\t\tthis.selectedExecutors = [];\r\n\r\n\t\t// 确保设备筛选默认为\"全部设备\"\r\n\t\tthis.deviceFilterIndex = 0;\r\n\t\tthis.currentFilterDeviceId = null;\r\n\t},\r\n\tmethods: {\r\n\t\t// 加载热用户列表\r\n\t\tloadHeatUnitList() {\r\n\t\t\t// 获取用户项目权限\r\n\t\t\tconst userHeatUnitId = uni.getStorageSync(\"heatUnitId\") || \"\";\r\n\t\t\tconsole.log('用户项目权限:', userHeatUnitId);\r\n\t\t\t\r\n\t\t\t// 判断用户是否有全局权限(heatUnitId=0)\r\n\t\t\tconst hasAllPermission = userHeatUnitId === \"0\" || \r\n\t\t\t                        (userHeatUnitId.split(\",\").length > 0 && \r\n\t\t\t\t\t\t\t\t\t userHeatUnitId.split(\",\").includes(\"0\"));\r\n\t\t\t\r\n\t\t\theatUnitApi.getList()\r\n\t\t\t\t.then(res => {\r\n\t\t\t\t\tif (res.code === 200 && res.data) {\r\n\t\t\t\t\t\t// 所有热用户列表\r\n\t\t\t\t\t\tlet allHeatUnits = res.data;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 如果用户没有全局权限，则需要过滤出用户有权限的热用户\r\n\t\t\t\t\t\tif (!hasAllPermission && userHeatUnitId) {\r\n\t\t\t\t\t\t\t// 将权限ID字符串转换为数组\r\n\t\t\t\t\t\t\tconst authorizedIds = userHeatUnitId.split(\",\").map(id => parseInt(id.trim()));\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 过滤出用户有权限的热用户\r\n\t\t\t\t\t\t\tallHeatUnits = allHeatUnits.filter(unit => authorizedIds.includes(unit.id));\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t \r\n\t\t\t\t\t\tthis.heatUnitOptions = allHeatUnits;\r\n\t\t\t\t\t\tthis.filteredHeatUnits = [...allHeatUnits];\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 设置默认选中的热用户（如果有权限的热用户列表不为空）\r\n\t\t\t\t\t\tif (this.filteredHeatUnits.length > 0) {\r\n\t\t\t\t\t\t\tthis.selectedHeatUnit = this.filteredHeatUnits[0];\r\n\t\t\t\t\t\t\tthis.formData.heatUnitId = this.selectedHeatUnit.id;\r\n\r\n\t\t\t\t\t\t\t// 加载该热用户下的设备列表\r\n\t\t\t\t\t\t\tthis.loadDevicesByHeatUnit(this.selectedHeatUnit.id);\r\n\r\n\t\t\t\t\t\t\t// 加载该热用户对应的巡检人员列表\r\n\t\t\t\t\t\t\tthis.loadStaffOptions(this.selectedHeatUnit.id);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 如果没有可用的热用户，加载所有人员\r\n\t\t\t\t\t\t\tthis.loadStaffOptions();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tconsole.log('过滤后的热用户列表:', this.filteredHeatUnits);\r\n\t\t\t\t\t\tconsole.log('默认选中的热用户:', this.selectedHeatUnit);\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tconsole.error('获取热用户列表失败:', err);\r\n\t\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 加载人员列表 - 根据选中的热用户过滤\r\n\t\tloadStaffOptions(heatUnitId = null) {\r\n\t\t\tuserApi.getInspectorList({ role: 'inspector' })\r\n\t\t\t\t.then(res => {\r\n\t\t\t\t\tconsole.log('巡检人员原始数据:', res.data)\r\n\t\t\t\t\tif (res.code === 200 && res.data) {\r\n\t\t\t\t\t\t// 获取完整的人员信息，包含heatUnitId权限\r\n\t\t\t\t\t\tconst allStaff = res.data.map(user => ({\r\n\t\t\t\t\t\t\tid: user.id,\r\n\t\t\t\t\t\t\tname: user.name,\r\n\t\t\t\t\t\t\theatUnitId: user.heatUnitId, // 保存权限信息\r\n\t\t\t\t\t\t\tusername: user.username,\r\n\t\t\t\t\t\t\tphone: user.phone,\r\n\t\t\t\t\t\t\tdepartment: user.department\r\n\t\t\t\t\t\t}));\r\n\r\n\t\t\t\t\t\t// 如果指定了热用户ID，则过滤人员\r\n\t\t\t\t\t\tif (heatUnitId !== null) {\r\n\t\t\t\t\t\t\tthis.staffOptions = this.filterStaffByHeatUnit(allStaff, heatUnitId);\r\n\t\t\t\t\t\t\tconsole.log(`🔄 热用户ID ${heatUnitId} 对应的巡检人员:`, this.staffOptions);\r\n\t\t\t\t\t\t\tconsole.log(`📊 过滤前总人员数: ${allStaff.length}, 过滤后人员数: ${this.staffOptions.length}`);\r\n\r\n\t\t\t\t\t\t\t// 如果没有找到对应权限的人员，给出提示\r\n\t\t\t\t\t\t\tif (this.staffOptions.length === 0) {\r\n\t\t\t\t\t\t\t\tconsole.warn(`⚠️ 热用户ID ${heatUnitId} 没有对应权限的巡检人员`);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 如果没有指定热用户，显示所有人员\r\n\t\t\t\t\t\t\tthis.staffOptions = allStaff;\r\n\t\t\t\t\t\t\tconsole.log('📋 显示所有巡检人员:', this.staffOptions);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tconsole.error('获取人员列表失败:', err);\r\n\t\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 根据热用户ID过滤人员\r\n\t\tfilterStaffByHeatUnit(allStaff, heatUnitId) {\r\n\t\t\tconsole.log(`🔍 开始过滤人员，目标热用户ID: ${heatUnitId}`);\r\n\r\n\t\t\tconst filteredStaff = allStaff.filter(staff => {\r\n\t\t\t\t// 如果heatUnitId为空或未定义，跳过该人员\r\n\t\t\t\tif (!staff.heatUnitId) {\r\n\t\t\t\t\tconsole.log(`❌ ${staff.name}: 无权限信息`);\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 将heatUnitId转换为字符串数组\r\n\t\t\t\tconst staffHeatUnits = staff.heatUnitId.toString().split(',').map(id => id.trim());\r\n\t\t\t\tconsole.log(`👤 ${staff.name}: 权限范围 [${staffHeatUnits.join(', ')}]`);\r\n\r\n\t\t\t\t// 检查是否包含目标热用户ID或者包含\"0\"（全权限）\r\n\t\t\t\tconst hasPermission = staffHeatUnits.includes('0') || staffHeatUnits.includes(heatUnitId.toString());\r\n\t\t\t\tconsole.log(`${hasPermission ? '✅' : '❌'} ${staff.name}: ${hasPermission ? '有权限' : '无权限'}`);\r\n\r\n\t\t\t\treturn hasPermission;\r\n\t\t\t});\r\n\r\n\t\t\tconsole.log(`🎯 过滤结果: ${filteredStaff.length}/${allStaff.length} 人员有权限`);\r\n\t\t\treturn filteredStaff;\r\n\t\t},\r\n\t\t\r\n\t\t// 根据热用户ID加载设备列表\r\n\t\tloadDevicesByHeatUnit(heatUnitId) {\r\n\t\t\tif (!heatUnitId) {\r\n\t\t\t\tconsole.error('无效的热用户ID');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tconsole.log('开始加载热用户设备列表, 热用户ID:', heatUnitId);\r\n\t\t\t\r\n\t\t\t// 显示加载中提示\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载设备列表...'\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\tdeviceApi.getDevicesByHeatUnitId(heatUnitId)\r\n\t\t\t\t.then(res => {\r\n\t\t\t\t\tconsole.log('设备列表API响应:', res);\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (res.code === 200 && res.data) {\r\n\t\t\t\t\t\t// 处理API返回的设备数组\r\n\t\t\t\t\t\tthis.deviceOptions = res.data.map(device => ({\r\n\t\t\t\t\t\t\tid: device.id,\r\n\t\t\t\t\t\t\tname: device.name,\r\n\t\t\t\t\t\t\ttype: device.type,\r\n\t\t\t\t\t\t\tdevice_parent: device.deviceParent,\r\n\t\t\t\t\t\t\theat_unit_id: this.selectedHeatUnit.id\r\n\t\t\t\t\t\t}));\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tconsole.log('处理后的设备列表:', this.deviceOptions);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 清空已选设备\r\n\t\t\t\t\t\tthis.selectedDevices = [];\r\n\t\t\t\t\t\tthis.formData.deviceIds = [];\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 清空设备巡检项\r\n\t\t\t\t\t\tthis.devicePatrolItems = {};\r\n\t\t\t\t\t\tthis.formData.patrolItem = [];\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.error('获取设备列表失败:', res.message || '未知错误');\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '获取设备列表失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t}); \r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tconsole.error(`获取热用户ID:${heatUnitId})的设备列表失败`, err);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '获取设备列表失败，请重试',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\t \r\n\t\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 根据设备ID加载巡检项\r\n\t\tloadPatrolItemsByDevice(deviceId) {\r\n\t\t\tif (!deviceId) return; \r\n\t\t\tdeviceApi.getPatrolItemsByDeviceId(deviceId)\r\n\t\t\t\t.then(res => {\r\n\t\t\t\t\tif (res.code === 200 && res.data) {\r\n\t\t\t\t\t\tconst items = res.data.map(item => ({\r\n\t\t\t\t\t\t\tid: item.id,\r\n\t\t\t\t\t\t\titemId: item.patrolItemDictId,\r\n\t\t\t\t\t\t\tdeviceId: item.deviceId,\r\n\t\t\t\t\t\t\titemName: item.itemName,\r\n\t\t\t\t\t\t\tcategoryName: item.categoryName,\r\n\t\t\t\t\t\t\tparamType: item.paramType,\r\n\t\t\t\t\t\t\tunit: item.unit,\r\n\t\t\t\t\t\t\tnormalRange: item.normalRange,\r\n\t\t\t\t\t\t\tcheckMethod: item.checkMethod,\r\n\t\t\t\t\t\t\timportance: item.importance,\r\n\t\t\t\t\t\t\tdescription: item.description,\r\n\t\t\t\t\t\t\tselected: false\r\n\t\t\t\t\t\t})); \r\n\t\t\t\t\t\tthis.$set(this.devicePatrolItems, deviceId, items);\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tconsole.error(`获取设备(ID:${deviceId})的巡检项失败`, err);\r\n\t\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 处理设备选择变更\r\n\t\thandleDeviceChange(e) {\r\n\t\t\tthis.deviceIndex = e.detail.value;\r\n\t\t\tconst selectedDevice = this.deviceOptions[this.deviceIndex]; \r\n\t\t\tif (!this.formData.deviceIds.includes(selectedDevice.id)) {\r\n\t\t\t\tthis.formData.deviceIds.push(selectedDevice.id);\r\n\t\t\t\tthis.selectedDevices.push(selectedDevice);\r\n\t\t\t\t\r\n\t\t\t\tthis.loadPatrolItemsByDevice(selectedDevice.id);\r\n\t\t\t} else {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '该设备已添加',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 移除已选设备\r\n\t\tremoveDevice(deviceId) {\r\n\t\t\tconst index = this.formData.deviceIds.findIndex(id => id === deviceId);\r\n\t\t\tif (index !== -1) {\r\n\t\t\t\tthis.formData.deviceIds.splice(index, 1);\r\n\t\t\t\t\r\n\t\t\t\tconst deviceIndex = this.selectedDevices.findIndex(device => device.id === deviceId);\r\n\t\t\t\tif (deviceIndex !== -1) {\r\n\t\t\t\t\tthis.selectedDevices.splice(deviceIndex, 1);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (this.devicePatrolItems[deviceId]) {\r\n\t\t\t\t\tdelete this.devicePatrolItems[deviceId];\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthis.formData.patrolItem = this.formData.patrolItem.filter(item => item.deviceId !== deviceId);\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 多选执行人员变更\r\n\t\thandleStaffChange(e) {\r\n\t\t\tthis.staffIndex = e.detail.value;\r\n\t\t\tconst selectedStaff = this.staffOptions[this.staffIndex];\r\n\t\t\t\r\n\t\t\tif (!this.formData.executorIds.includes(selectedStaff.id)) {\r\n\t\t\t\tthis.formData.executorIds.push(selectedStaff.id);\r\n\t\t\t\tthis.selectedExecutors.push(selectedStaff);\r\n\t\t\t} else {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '该人员已添加',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 移除已选执行人\r\n\t\tremoveExecutor(executorId) {\r\n\t\t\tconst index = this.formData.executorIds.findIndex(id => id === executorId);\r\n\t\t\tif (index !== -1) {\r\n\t\t\t\tthis.formData.executorIds.splice(index, 1);\r\n\t\t\t\t\r\n\t\t\t\tconst staffIndex = this.selectedExecutors.findIndex(staff => staff.id === executorId);\r\n\t\t\t\tif (staffIndex !== -1) {\r\n\t\t\t\t\tthis.selectedExecutors.splice(staffIndex, 1);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 类型选择变更\r\n\t\thandleScheduleTypeChange(e) {\r\n\t\t\tthis.scheduleTypeIndex = e.detail.value;\r\n\t\t\tthis.formData.scheduleType = this.scheduleTypeOptions[this.scheduleTypeIndex].value;\r\n\t\t},\r\n\t\t// 巡检类型选择变更\r\n\t\thandlePatrolTypeChange(e) {\r\n\t\t\tthis.patrolTypeIndex = e.detail.value;\r\n\t\t\tthis.formData.patrolType = this.patrolTypeOptions[this.patrolTypeIndex].value;\r\n\t\t},\r\n\t\t// 开始日期变更\r\n\t\thandleStartDateChange(e) {\r\n\t\t\tthis.formData.startDate = e.detail.value;\r\n\t\t\t\r\n\t\t\tif (this.formData.endDate && this.formData.endDate < this.formData.startDate) {\r\n\t\t\t\tthis.formData.endDate = '';\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 结束日期变更\r\n\t\thandleEndDateChange(e) {\r\n\t\t\tthis.formData.endDate = e.detail.value;\r\n\t\t},\r\n\t\t\r\n\t\t// 显示任务选择\r\n\t\tshowTaskSelector() {\r\n\t\t\tif (this.selectedDevices.length === 0) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请先选择巡检设备',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 重置设备筛选为\"全部设备\"\r\n\t\t\tthis.deviceFilterIndex = 0;\r\n\t\t\tthis.currentFilterDeviceId = null;\r\n\t\t\tthis.isPopupOpen = true;\r\n\t\t\t\r\n\t\t\tthis.$refs.taskSelector.open();\r\n\t\t},\r\n\t\t\r\n\t\t// 隐藏任务选择\r\n\t\thideTaskSelector() {\r\n\t\t\t// 用户点击关闭按钮时，提示未保存选择\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '提示',\r\n\t\t\t\tcontent: '您尚未确认选择，关闭后已选项将不会保存，是否确认关闭？',\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\tthis.isPopupOpen = false;\r\n\t\t\t\t\t\tthis.$refs.taskSelector.close();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 切换任务选择状态\r\n\t\ttoggleTaskSelection(index) {\r\n\t\t\tconst template = this.filteredTemplates[index];\r\n\t\t\tconst deviceItems = this.devicePatrolItems[template.deviceId];\r\n\t\t\tif (deviceItems) {\r\n\t\t\t\tconst itemIndex = deviceItems.findIndex(item => item.id === template.id);\r\n\t\t\t\tif (itemIndex !== -1) {\r\n\t\t\t\t\tthis.$set(deviceItems[itemIndex], 'selected', !deviceItems[itemIndex].selected);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 切换周几选择\r\n\t\ttoggleWeekDay(day) {\r\n\t\t\tconst index = this.formData.scheduleWeekDays.indexOf(day);\r\n\t\t\tif (index === -1) {\r\n\t\t\t\tthis.formData.scheduleWeekDays.push(day);\r\n\t\t\t} else {\r\n\t\t\t\tthis.formData.scheduleWeekDays.splice(index, 1);\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 切换月日选择\r\n\t\ttoggleMonthDay(day) {\r\n\t\t\tconst index = this.formData.scheduleMonthDays.indexOf(day);\r\n\t\t\tif (index === -1) {\r\n\t\t\t\tthis.formData.scheduleMonthDays.push(day);\r\n\t\t\t} else {\r\n\t\t\t\tthis.formData.scheduleMonthDays.splice(index, 1);\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 确认任务选择\r\n\t\tconfirmTaskSelection() {\r\n\t\t\tlet selectedItems = [];\r\n\t\t\tObject.entries(this.devicePatrolItems).forEach(([deviceId, items]) => {\r\n\t\t\t\tconst selected = items.filter(item => item.selected);\r\n\t\t\t\tif (selected.length > 0) {\r\n\t\t\t\t\tselectedItems = selectedItems.concat(selected.map(item => ({\r\n\t\t\t\t\t\t...item,\r\n\t\t\t\t\t\tdeviceId: parseInt(deviceId)\r\n\t\t\t\t\t})));\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\tthis.formData.patrolItem = selectedItems.map(item => ({\r\n\t\t\t\tid: item.id,\r\n\t\t\t\titemId: item.itemId,\r\n\t\t\t\tdeviceId: item.deviceId,\r\n\t\t\t\titemName: item.itemName,\r\n\t\t\t\tcategoryName: item.categoryName,\r\n\t\t\t\tcheckMethod: item.checkMethod,\r\n\t\t\t\tnormalRange: item.normalRange,\r\n\t\t\t\tdescription: item.description,\r\n\t\t\t\tparamType: item.paramType,\r\n\t\t\t\tunit: item.unit,\r\n\t\t\t\timportance: item.importance\r\n\t\t\t}));\r\n\t\t\t\r\n\t\t\tthis.isPopupOpen = false;\r\n\t\t\tthis.$refs.taskSelector.close();\r\n\t\t\t\r\n\t\t\t// 显示选择成功提示\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: `已添加${selectedItems.length}个巡检项`,\r\n\t\t\t\ticon: 'success',\r\n\t\t\t\tduration: 2000\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 移除任务\r\n\t\tremoveTask(index) {\r\n\t\t\tconst task = this.formData.patrolItem[index];\r\n\t\t\t\r\n\t\t\tif (task && task.deviceId && this.devicePatrolItems[task.deviceId]) {\r\n\t\t\t\tconst items = this.devicePatrolItems[task.deviceId];\r\n\t\t\t\tconst itemIndex = items.findIndex(item => item.id === task.id);\r\n\t\t\t\tif (itemIndex !== -1) {\r\n\t\t\t\t\tthis.$set(items[itemIndex], 'selected', false);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tthis.formData.patrolItem.splice(index, 1);\r\n\t\t},\r\n\t\t\r\n\t\t// 提交表单\r\n\t\tsubmitPlan() {\r\n\t\t\t// 检查用户是否有可用的热用户权限\r\n\t\t\tif (this.filteredHeatUnits.length === 0) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '您没有任何热用户的权限，无法创建巡检计划',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (!this.selectedHeatUnit) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请选择热用户',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (!this.formData.name) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请输入计划名?',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (!this.formData.startDate) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请选择开始日期',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (!this.formData.endDate) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请选择结束日期',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (this.formData.executorIds.length === 0) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请选择执行人员',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (this.formData.deviceIds.length === 0) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请选择巡检设备',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (this.formData.scheduleType === 'custom' && !this.formData.scheduleInterval) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请输入巡检间隔天数',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (this.formData.scheduleType === 'weekly' && this.formData.scheduleWeekDays.length === 0) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请选择巡检星期',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (this.formData.scheduleType === 'monthly' && this.formData.scheduleMonthDays.length === 0) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请选择巡检日期',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif (!this.formData.patrolType) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请选择巡检类型',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (this.formData.patrolItem.length === 0) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请添加巡检任务',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tthis.formData.patrolItem.forEach(item => {\r\n\t\t\t\tif (!item.deviceId && this.formData.deviceIds.length > 0) {\r\n\t\t\t\t\titem.deviceId = this.formData.deviceIds[0];\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\tconst submitData = { \r\n\t\t\t\tname: this.formData.name,\r\n\t\t\t\tstart_date: this.formData.startDate,\r\n\t\t\t\tend_date: this.formData.endDate,\r\n\t\t\t\texecutor_ids: this.formData.executorIds,\r\n\t\t\t\tpatrol_type: this.formData.patrolType,\r\n\t\t\t\tlocations: this.selectedHeatUnit.name, \r\n\t\t\t\theat_unit_id: this.selectedHeatUnit.id, \r\n\t\t\t\tdevice_ids: this.formData.deviceIds,\r\n\t\t\t\tschedule_type: this.formData.scheduleType,\r\n\t\t\t\tschedule_interval: this.formData.scheduleInterval,\r\n\t\t\t\tschedule_week_days: this.formData.scheduleWeekDays,\r\n\t\t\t\tschedule_month_days: this.formData.scheduleMonthDays,\r\n\t\t\t\t\r\n\t\t\t\tpatrol_item: this.formData.patrolItem.map(item => ({\r\n\t\t\t\t\tdevice_patrol_item_id: item.id\r\n\t\t\t\t}))\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '提交中...'\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\tconsole.log('提交巡检计划数据:', JSON.stringify(submitData, null, 2));\r\n\t\t\t\r\n\t\t\tpatrolApi.createPlan(submitData)\r\n\t\t\t\t.then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '创建成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tuni.navigateBack();\r\n\t\t\t\t\t}, 1500);\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: err.errMsg || '创建失败，请重试',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 搜索巡检项\r\n\t\thandleSearch() {\r\n\t\t\tconsole.log('搜索关键?', this.searchKeyword);\r\n\t\t},\r\n\t\t\r\n\t\t// 清空搜索\r\n\t\tclearSearch() {\r\n\t\t\tthis.searchKeyword = '';\r\n\t\t},\r\n\t\t\r\n\t\t// 显示热用户选择\r\n\t\tshowHeatUnitSelector() {\r\n\t\t\tif (this.filteredHeatUnits.length === 0) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '无可用热用户',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tthis.isPopupOpen = true;\r\n\t\t\tthis.$refs.heatUnitSelector.open();\r\n\t\t},\r\n\t\t\r\n\t\t// 隐藏热用户选择\r\n\t\thideHeatUnitSelector() {\r\n\t\t\tthis.isPopupOpen = false;\r\n\t\t\tthis.$refs.heatUnitSelector.close();\r\n\t\t},\r\n\t\t\r\n\t\t// 选择热用户\r\n\t\tselectHeatUnit(heatUnit) {\r\n\t\t\tthis.selectedHeatUnit = heatUnit;\r\n\t\t\tconsole.log('选择热用户 ', heatUnit.name, 'ID:', heatUnit.id);\r\n\r\n\t\t\t// 立即更新表单数据\r\n\t\t\tthis.formData.heatUnitId = heatUnit.id;\r\n\r\n\t\t\t// 立即重新加载该热用户对应的巡检人员列表\r\n\t\t\tthis.loadStaffOptions(heatUnit.id);\r\n\r\n\t\t\t// 清空已选择的巡检人员（因为权限可能已变化）\r\n\t\t\tconst hadSelectedStaff = this.selectedExecutors.length > 0;\r\n\t\t\tthis.selectedExecutors = [];\r\n\t\t\tthis.formData.executorIds = [];\r\n\t\t\t// 立即加载该热用户下的设备列表\r\n\t\t\tthis.loadDevicesByHeatUnit(heatUnit.id);\r\n\t\t},\r\n\t\t\r\n\t\t// 确认热用户选择\r\n\t\tconfirmHeatUnitSelection() {\r\n\t\t\tif (this.selectedHeatUnit) {\r\n\t\t\t\tconsole.log('确认选择热用户', this.selectedHeatUnit.name, 'ID:', this.selectedHeatUnit.id);\r\n\r\n\t\t\t\t// 关闭弹窗（数据更新已在selectHeatUnit中完成）\r\n\t\t\t\tthis.isPopupOpen = false;\r\n\t\t\t\tthis.$refs.heatUnitSelector.close();\r\n\t\t\t} else {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请先选择热用户',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 搜索热用户\r\n\t\thandleHeatUnitSearch() {\r\n\t\t\tif (!this.heatUnitSearchKeyword || this.heatUnitSearchKeyword.trim() === '') {\r\n\t\t\t\tthis.filteredHeatUnits = [...this.heatUnitOptions];\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tconst keyword = this.heatUnitSearchKeyword.toLowerCase().trim();\r\n\t\t\tthis.filteredHeatUnits = this.heatUnitOptions.filter(unit => \r\n\t\t\t\tunit.name.toLowerCase().includes(keyword)\r\n\t\t\t);\r\n\t\t},\r\n\t\t\r\n\t\t// 清空热用户搜索\r\n\t\tclearHeatUnitSearch() {\r\n\t\t\tthis.heatUnitSearchKeyword = '';\r\n\t\t\t// 恢复到原始过滤后的热用户列表，而不是所有热用户\r\n\t\t\tthis.filteredHeatUnits = [...this.heatUnitOptions];\r\n\t\t},\r\n\t\t\r\n\t\t// 获取设备名称\r\n\t\tgetDeviceName(deviceId) {\r\n\t\t\tconst device = this.selectedDevices.find(d => d.id === deviceId);\r\n\t\t\treturn device ? device.name : '未知设备';\r\n\t\t},\r\n\t\t\r\n\t\t// 处理设备筛选\r\n\t\thandleDeviceFilterChange(e) {\r\n\t\t\tthis.deviceFilterIndex = parseInt(e.detail.value);\r\n\t\t\t\r\n\t\t\tif (this.deviceFilterIndex === 0) {\r\n\t\t\t\t// 选择\"全部设备\"\r\n\t\t\t\tthis.currentFilterDeviceId = null;\r\n\t\t\t} else {\r\n\t\t\t\t// 选择具体设备，索引需要减1（因为添加了\"全部设备\"选项）\r\n\t\t\t\tconst selectedDevice = this.selectedDevices[this.deviceFilterIndex - 1];\r\n\t\t\t\tthis.currentFilterDeviceId = selectedDevice ? selectedDevice.id : null;\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 获取重要性标签\r\n\t\tgetImportanceLabel(importance) {\r\n\t\t\tswitch (importance) {\r\n\t\t\t\tcase 'normal':\r\n\t\t\t\t\treturn '普通';\r\n\t\t\t\tcase 'important':\r\n\t\t\t\t\treturn '重要';\r\n\t\t\t\tcase 'critical':\r\n\t\t\t\t\treturn '关键';\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn '普通';\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 获取重要性类\r\n\t\tgetImportanceClass(importance) {\r\n\t\t\tswitch (importance) {\r\n\t\t\t\tcase 'normal':\r\n\t\t\t\t\treturn 'normal-importance';\r\n\t\t\t\tcase 'important':\r\n\t\t\t\t\treturn 'important-importance';\r\n\t\t\t\tcase 'critical':\r\n\t\t\t\t\treturn 'critical-importance';\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn 'normal-importance';\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 显示设备选择器\r\n\t\tshowDeviceSelector() {\r\n\t\t\tif (!this.selectedHeatUnit) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请先选择热用户',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 初始化临时选中设备列表\r\n\t\t\tthis.tempSelectedDevices = [...this.selectedDevices];\r\n\t\t\tthis.isPopupOpen = true;\r\n\t\t\tthis.$refs.deviceSelector.open();\r\n\t\t},\r\n\t\t\r\n\t\t// 隐藏设备选择器\r\n\t\thideDeviceSelector() {\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '提示',\r\n\t\t\t\tcontent: '您尚未确认选择，关闭后已选项将不会保存，是否确认关闭？',\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\tthis.isPopupOpen = false;\r\n\t\t\t\t\t\tthis.$refs.deviceSelector.close();\r\n\t\t\t\t\t\t// 恢复原来的选择\r\n\t\t\t\t\t\tthis.tempSelectedDevices = [...this.selectedDevices];\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 切换设备选择状态\r\n\t\ttoggleDeviceSelection(device) {\r\n\t\t\tconst index = this.tempSelectedDevices.findIndex(d => d.id === device.id);\r\n\t\t\tif (index === -1) {\r\n\t\t\t\tthis.tempSelectedDevices.push(device);\r\n\t\t\t} else {\r\n\t\t\t\tthis.tempSelectedDevices.splice(index, 1);\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 确认设备选择\r\n\t\tconfirmDeviceSelection() {\r\n\t\t\tif (this.tempSelectedDevices.length === 0) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请至少选择一个设备',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 更新选中的设备列表\r\n\t\t\tthis.selectedDevices = [...this.tempSelectedDevices];\r\n\t\t\tthis.formData.deviceIds = this.selectedDevices.map(device => device.id);\r\n\t\t\t\r\n\t\t\t// 清空已选中的巡检项\r\n\t\t\tthis.formData.patrolItem = [];\r\n\t\t\t\r\n\t\t\t// 重新加载所有选中设备的巡检项\r\n\t\t\tthis.selectedDevices.forEach(device => {\r\n\t\t\t\tthis.loadPatrolItemsByDevice(device.id);\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\tthis.isPopupOpen = false;\r\n\t\t\tthis.$refs.deviceSelector.close();\r\n\t\t\t\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: `已选择${this.selectedDevices.length}个设备`,\r\n\t\t\t\ticon: 'success'\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 检查设备是否被选中\r\n\t\tisDeviceSelected(deviceId) {\r\n\t\t\treturn this.tempSelectedDevices.some(device => device.id === deviceId);\r\n\t\t},\r\n\t\t\r\n\t\t// 显示人员选择\r\n\t\tshowStaffSelector() {\r\n\t\t\t// 初始化临时选中人员列表\r\n\t\t\tthis.tempSelectedExecutors = [...this.selectedExecutors];\r\n\t\t\tthis.isPopupOpen = true;\r\n\t\t\tthis.$refs.staffSelector.open();\r\n\t\t},\r\n\t\t\r\n\t\t// 隐藏人员选择\r\n\t\thideStaffSelector() {\r\n\t\t\t// 用户点击关闭按钮时，提示未保存选择\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '提示',\r\n\t\t\t\tcontent: '您尚未确认选择，关闭后已选项将不会保存，是否确认关闭？',\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\tthis.isPopupOpen = false;\r\n\t\t\t\t\t\tthis.$refs.staffSelector.close();\r\n\t\t\t\t\t\t// 恢复原来的选择\r\n\t\t\t\t\t\tthis.tempSelectedExecutors = [...this.selectedExecutors];\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 切换人员选择状态\r\n\t\ttoggleStaffSelection(staff) {\r\n\t\t\tconst index = this.tempSelectedExecutors.findIndex(s => s.id === staff.id);\r\n\t\t\tif (index === -1) {\r\n\t\t\t\tthis.tempSelectedExecutors.push(staff);\r\n\t\t\t} else {\r\n\t\t\t\tthis.tempSelectedExecutors.splice(index, 1);\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 检查人员是否被选中\r\n\t\tisStaffSelected(staffId) {\r\n\t\t\treturn this.tempSelectedExecutors.some(staff => staff.id === staffId);\r\n\t\t},\r\n\t\t\r\n\t\t// 确认人员选择\r\n\t\tconfirmStaffSelection() {\r\n\t\t\tif (this.tempSelectedExecutors.length === 0) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请至少选择一个人员',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 更新选中的人员列表\r\n\t\t\tthis.selectedExecutors = [...this.tempSelectedExecutors];\r\n\t\t\tthis.formData.executorIds = this.selectedExecutors.map(staff => staff.id);\r\n\t\t\t\r\n\t\t\tthis.isPopupOpen = false;\r\n\t\t\tthis.$refs.staffSelector.close();\r\n\t\t\t\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: `已选择${this.selectedExecutors.length}个人员`,\r\n\t\t\t\ticon: 'success'\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 加载巡检类型选项\r\n\t\tloadPatrolTypeOptions() {\r\n\t\t\tdictApi.getDictDataByDictId(11)\r\n\t\t\t\t.then(res => {\r\n\t\t\t\t\tif (res.code === 200 && res.data) {\r\n\t\t\t\t\t\tthis.patrolTypeOptions = res.data.map(item => ({\r\n\t\t\t\t\t\t\tlabel: item.name,\r\n\t\t\t\t\t\t\tvalue: item.name\r\n\t\t\t\t\t\t}));\r\n\r\n\t\t\t\t\t\t// 如果没有数据，使用默认选项\r\n\t\t\t\t\t\tif (this.patrolTypeOptions.length === 0) {\r\n\t\t\t\t\t\t\tthis.patrolTypeOptions = [\r\n\t\t\t\t\t\t\t\t{ label: \"日常巡检\", value: \"日常巡检\" },\r\n\t\t\t\t\t\t\t\t{ label: \"设备巡检\", value: \"设备巡检\" },\r\n\t\t\t\t\t\t\t\t{ label: \"管道巡检\", value: \"管道巡检\" },\r\n\t\t\t\t\t\t\t\t{ label: \"阀门巡检\", value: \"阀门巡检\" },\r\n\t\t\t\t\t\t\t\t{ label: \"换热站巡检\", value: \"换热站巡检\" }\r\n\t\t\t\t\t\t\t];\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 设置默认的巡检类型值（选择第一个选项）\r\n\t\t\t\t\t\tif (this.patrolTypeOptions.length > 0 && !this.formData.patrolType) {\r\n\t\t\t\t\t\t\tthis.formData.patrolType = this.patrolTypeOptions[0].value;\r\n\t\t\t\t\t\t\tthis.patrolTypeIndex = 0;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tconsole.error('获取巡检类型选项失败:', err);\r\n\t\t\t\t\t// API调用失败时，使用默认选项\r\n\t\t\t\t\tthis.patrolTypeOptions = [\r\n\t\t\t\t\t\t{ label: \"日常巡检\", value: \"日常巡检\" },\r\n\t\t\t\t\t\t{ label: \"设备巡检\", value: \"设备巡检\" },\r\n\t\t\t\t\t\t{ label: \"管道巡检\", value: \"管道巡检\" },\r\n\t\t\t\t\t\t{ label: \"阀门巡检\", value: \"阀门巡检\" },\r\n\t\t\t\t\t\t{ label: \"换热站巡检\", value: \"换热站巡检\" }\r\n\t\t\t\t\t];\r\n\t\t\t\t\t// 设置默认值\r\n\t\t\t\t\tif (this.patrolTypeOptions.length > 0 && !this.formData.patrolType) {\r\n\t\t\t\t\t\tthis.formData.patrolType = this.patrolTypeOptions[0].value;\r\n\t\t\t\t\t\tthis.patrolTypeIndex = 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 设备全选功能\r\n\t\ttoggleDeviceSelectAll() {\r\n\t\t\tif (this.isAllDevicesSelected) {\r\n\t\t\t\t// 当前是全选状态，执行取消全选\r\n\t\t\t\tthis.tempSelectedDevices = [];\r\n\t\t\t} else {\r\n\t\t\t\t// 当前不是全选状态，执行全选\r\n\t\t\t\tthis.tempSelectedDevices = [...this.deviceOptions];\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 巡检项全选功能\r\n\t\ttoggleTaskSelectAll() {\r\n\t\t\tconst shouldSelectAll = !this.isAllTasksSelected;\r\n\r\n\t\t\t// 遍历当前筛选的模板，设置选中状态\r\n\t\t\tthis.filteredTemplates.forEach(template => {\r\n\t\t\t\tconst deviceItems = this.devicePatrolItems[template.deviceId];\r\n\t\t\t\tif (deviceItems) {\r\n\t\t\t\t\tconst itemIndex = deviceItems.findIndex(item => item.id === template.id);\r\n\t\t\t\t\tif (itemIndex !== -1) {\r\n\t\t\t\t\t\tthis.$set(deviceItems[itemIndex], 'selected', shouldSelectAll);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t\t// 显示操作提示\r\n\t\t\tconst message = shouldSelectAll ?\r\n\t\t\t\t`已全选 ${this.filteredTemplates.length} 个巡检项` :\r\n\t\t\t\t`已取消全选 ${this.filteredTemplates.length} 个巡检项`;\r\n\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: message,\r\n\t\t\t\ticon: 'none',\r\n\t\t\t\tduration: 1500\r\n\t\t\t});\r\n\t\t},\r\n\t}\r\n}\r\n\r\n</script>\r\n\r\n<style lang=\"scss\" scoped> \r\n\r\n// 新增和调整的样式\r\n$uni-bg-color-grey: #f8f8f8; // 背景色\r\n$uni-text-color: #333; // 主要文字颜色\r\n$uni-text-color-placeholder: #999; // 占位文字颜色\r\n$uni-text-color-grey: #666; // 次要文字颜色\r\n$uni-border-color: #e5e5e5; // 边框颜色\r\n$uni-color-primary: #007aff; // 主题色\r\n\r\n.patrol-create-container {\r\n\tbackground-color: $uni-bg-color-grey;\r\n\tmin-height: 100vh;\r\n\tpadding-bottom: 140rpx; // 为底部提交按钮留出空间\r\n\r\n\t.form-card {\r\n\t\tbackground-color: #fff;\r\n\t\tmargin: 20rpx;\r\n\t\tpadding: 0 30rpx 30rpx;\r\n\t\tborder-radius: 16rpx;\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\r\n\r\n\t\t.card-title {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tcolor: $uni-text-color;\r\n\t\t\tpadding: 30rpx 0;\r\n\t\t\tborder-bottom: 1rpx solid $uni-border-color;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t}\r\n\t\t.card-header {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 30rpx 0;\r\n\t\t\tborder-bottom: 1rpx solid $uni-border-color;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t.card-title {\r\n\t\t\t\tpadding: 0;\r\n\t\t\t\tborder-bottom: none;\r\n\t\t\t\tmargin-bottom: 0;\r\n\t\t\t}\r\n\t\t\t.add-button {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t&.disabled {\r\n\t\t\t\t\tcolor: $uni-text-color-placeholder;\r\n\t\t\t\t\tpointer-events: none;\r\n\t\t\t\t}\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.form-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tpadding: 20rpx 0;\r\n\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\r\n\t\t&:last-child {\r\n\t\t\tborder-bottom: none;\r\n\t\t}\r\n\r\n\t\t.form-label {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\tmargin-bottom: 16rpx;\r\n\t\t\t&.required::before {\r\n\t\t\t\tcontent: '*';\r\n\t\t\t\tcolor: red;\r\n\t\t\t\tmargin-right: 4rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.form-input, .form-picker, .form-textarea, .location-input {\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tcolor: $uni-text-color;\r\n\t\t\tpadding: 20rpx 0; // 调整padding使其上下对称\r\n\t\t\tmin-height: 50rpx; // 确保即使内容为空也有一定高度\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\twidth: 100%;\r\n\t\t\tbox-sizing: border-box;\r\n\r\n\t\t\t&::placeholder {\r\n\t\t\t\tcolor: $uni-text-color-placeholder;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tinput.form-input {\r\n\t\t\tpadding: 20rpx 0;\r\n\t\t}\r\n\t\t\r\n\t\t.plan-name-input {\r\n\t\t\twidth: 100%; // Ensure it takes full width\r\n\t\t\ttext-align: left; // Align text to the left\r\n\t\t\tdisplay: block; // Ensure it behaves like a block element\r\n\t\t\tline-height: normal; // Reset line height if necessary\r\n\t\t\theight: auto; // Allow height to adjust to content\r\n\t\t\tmin-height: 50rpx; // Maintain a minimum height\r\n\t\t\twhite-space: normal; // Allow text to wrap if it's very long (though input usually doesn't wrap)\r\n\t\t\tword-break: break-all; // Break long words if necessary\r\n\t\t}\r\n\t\t\r\n\t\ttextarea.form-textarea {\r\n\t\t\tpadding: 20rpx 0;\r\n\t\t\tmin-height: 150rpx; // 文本域最小高度\r\n\t\t\tline-height: 1.5;\r\n\t\t}\r\n\r\n\t\t.form-picker {\r\n\t\t\t.picker-value {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\t&.placeholder-style {\r\n\t\t\t\t\tcolor: $uni-text-color-placeholder;\r\n\t\t\t\t\tpadding-left: 0 !important; // 移除内联样式带来的影响\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.location-input {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\twidth: 100%;\r\n\t\t}\r\n\t\t\r\n\t\t.arrow-down {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\tmargin-left: 10rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.picker-flex {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\twidth: 100%;\r\n\t\t\tcolor: $uni-text-color-placeholder; // 默认灰色\r\n\t\t\t& > text:first-child { // \"选择人员\" 等文字靠左\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tcolor: $uni-text-color-placeholder; // 确保占位符颜色\r\n\t\t\t}\r\n\t\t\t&.has-value > text:first-child {\r\n\t\t\t\tcolor: $uni-text-color; // 有值时变黑\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.device-selector {\r\n\t\t\t.location-input {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tpadding: 20rpx;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\r\n\t\t\t\ttext {\r\n\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.heatunit-selector{\r\n\t\t\t.location-input {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tpadding: 20rpx;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\r\n\t\t\t\ttext {\r\n\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.staff-selector {\r\n\t\t\t.location-input {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tpadding: 20rpx;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\t// border: 1rpx solid $uni-border-color;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\r\n\t\t\t\ttext {\r\n\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.selected-items {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tgap: 16rpx;\r\n\t\tmargin-top: 16rpx;\r\n\r\n\t\t.selected-item {\r\n\t\t\tbackground-color: #e6f7ff;\r\n\t\t\tcolor: $uni-color-primary;\r\n\t\t\tpadding: 8rpx 16rpx;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\r\n\t\t\t.item-text {\r\n\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.remove-icon {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.tag-group {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tgap: 16rpx;\r\n\t\tmargin-top: 10rpx;\r\n\t\t.tag {\r\n\t\t\tpadding: 10rpx 24rpx;\r\n\t\t\tborder: 1rpx solid $uni-border-color;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\t&.active {\r\n\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tborder-color: $uni-color-primary;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.task-list {\r\n\t\t.task-item {\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder: 1rpx solid $uni-border-color;\r\n\t\t\tborder-radius: 12rpx;\r\n\t\t\tpadding: 24rpx;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t\tbox-shadow: 0 2rpx 6rpx rgba(0,0,0,0.03);\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: flex-start;\r\n\r\n\t\t\t.task-content {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\t.task-title {\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t\tmargin-bottom: 12rpx;\r\n\t\t\t\t}\r\n\t\t\t\t.task-info-row {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\tmargin-bottom: 6rpx;\r\n\t\t\t\t}\r\n\t\t\t\t.task-info {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\tline-height: 1.5;\r\n\t\t\t\t\tmargin-bottom: 6rpx;\r\n\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\tmargin-bottom: 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.task-actions {\r\n\t\t\t\t.delete-icon {\r\n\t\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\t\tcolor: #ff5252; // 醒目的删除颜色\r\n\t\t\t\t\tpadding-left: 20rpx; // 增加点击区域\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.empty-tip {\r\n\t\ttext-align: center;\r\n\t\tpadding: 40rpx 0;\r\n\t\tcolor: $uni-text-color-placeholder;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.submit-button {\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbackground-color: $uni-color-primary;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\theight: 98rpx;\r\n\t\tline-height: 98rpx;\r\n\t\ttext-align: center;\r\n\t\tborder-radius: 0; // uni-app 按钮默认有圆角，如果需要贴合底部则去掉\r\n\t\tmargin: 0; // 确保按钮填满底部，uni-app 可能需要用important\r\n\t\t// 对于 H5 和App，可能需要处理安全区\r\n\t\tpadding-bottom: constant(safe-area-inset-bottom);  \r\n\t\tpadding-bottom: env(safe-area-inset-bottom);\r\n\t\tbox-sizing: border-box; // 确保 padding 不会增加总高度\r\n\t\tz-index: 1; // 确保不会覆盖弹窗\r\n\t}\r\n}\r\n\r\n// 弹窗统一样式 (uni-popup content)\r\n.selector { // 用于 <uni-popup><view class=\"selector\">...</view></uni-popup>\r\n\tbackground-color: #fff;\r\n\tborder-radius: 20rpx 20rpx 0 0;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tmax-height: 80vh; // 限制最大高度\r\n\toverflow: hidden; // 防止内容溢出圆角\r\n\r\n\t.selector-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 30rpx;\r\n\t\tborder-bottom: 1rpx solid $uni-border-color;\r\n\r\n\t\t.selector-title {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tcolor: $uni-text-color;\r\n\t\t}\r\n\t\t.header-actions {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t}\r\n\t\t.close-button {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: $uni-text-color-grey;\r\n\t\t}\r\n\t\t.confirm-button-header {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: $uni-color-primary;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tmargin-right: 20rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.selector-content {\r\n\t\tpadding: 0 30rpx 30rpx;\r\n\t\tflex: 1; // 使内容区域可滚动\r\n\t\toverflow-y: auto;\r\n\r\n\t\t.template-search { // 也可用于热用户搜索\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 20rpx 0;\r\n\t\t\t.search-input {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\theight: 72rpx;\r\n\t\t\t\tbackground-color: $uni-bg-color-grey;\r\n\t\t\t\tborder-radius: 36rpx;\r\n\t\t\t\tpadding: 0 30rpx;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t}\r\n\t\t\t.icon-clear {\r\n\t\t\t\t// 自行实现或使用图标\r\n\t\t\t}\r\n\t\t\t.search-btn {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.selector-content-footer {\r\n\t\t\tpadding: 15rpx 0;\r\n\t\t\tborder-top: 1rpx solid #e5e5e5;\r\n\t\t\tmargin-top: 20rpx;\r\n\t\t\tbackground-color: #f8f8f8;\r\n\t\t\tbox-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.1);\r\n\t\t\tposition: fixed;\r\n\t\t\tbottom: 0;\r\n\t\t\tleft: 0;\r\n\t\t\tright: 0;\r\n\t\t\tz-index: 100;\r\n\t\t\t\r\n\t\t\t.select-count-container {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tpadding: 15rpx 0;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\t\r\n\t\t\t\t.select-count-badge {\r\n\t\t\t\t\twidth: 48rpx;\r\n\t\t\t\t\theight: 48rpx;\r\n\t\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\tmargin-right: 16rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.count-number {\r\n\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.count-text {\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.device-filter { // 用于巡检项弹窗中的设备筛选\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\r\n\t\t\t.filter-title {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.form-picker {\r\n\t\t\t\tborder: 1rpx solid $uni-border-color;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\tpadding: 15rpx 20rpx;\r\n\t\t\t\t.picker-value {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t// 全选巡检项样式已改为内联样式实现，此处保留注释\r\n\t\t/* 全选巡检项功能使用内联样式确保正确显示 */\r\n\r\n\t\t// 设备全选功能样式 - 优化尺寸\r\n\t\t.device-select-all-wrapper {\r\n\t\t\tmargin-bottom: 16rpx;\r\n\t        margin-top: 25rpx;\r\n\t\t\t.device-select-all-card {\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tborder: 2rpx solid $uni-color-primary;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\tpadding: 16rpx;\r\n\t\t\t\tbox-shadow: 0 1rpx 4rpx rgba(0, 122, 255, 0.1);\r\n\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t// 添加渐变背景效果\r\n\t\t\t\tbackground: linear-gradient(135deg, #f0f8ff 0%, #ffffff 100%);\r\n\r\n\t\t\t\t.device-select-all-content {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n                    \r\n\t\t\t\t\t.device-select-checkbox {\r\n\t\t\t\t\t\twidth: 32rpx;\r\n\t\t\t\t\t\theight: 32rpx;\r\n\t\t\t\t\t\tborder: 2rpx solid $uni-color-primary;\r\n\t\t\t\t\t\tborder-radius: 6rpx;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\tmargin-right: 12rpx;\r\n\t\t\t\t\t\tflex-shrink: 0;\r\n\t\t\t\t\t\tbackground-color: #fff;\r\n\r\n\t\t\t\t\t\t&.checked {\r\n\t\t\t\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\t\t\t\tborder-color: $uni-color-primary;\r\n\r\n\t\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\t\t\tfont-size: 18rpx;\r\n\t\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.device-select-info {\r\n\t\t\t\t\t\tflex: 1;\r\n                        margin: 5px;\r\n\t\t\t\t\t\t.device-select-title {\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t\t\t\t.select-title-text {\r\n\t\t\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t.select-status-badge {\r\n\t\t\t\t\t\t\t\tbackground-color: #e6f7ff;\r\n\t\t\t\t\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\t\t\tpadding: 4rpx 12rpx;\r\n\t\t\t\t\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\t\t\t\tborder: 1rpx solid #91d5ff;\r\n\r\n\t\t\t\t\t\t\t\t&.active {\r\n\t\t\t\t\t\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\t\t\t\tborder-color: $uni-color-primary;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t.badge-text {\r\n\t\t\t\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 添加点击效果\r\n\t\t\t\t&:active {\r\n\t\t\t\t\ttransform: scale(0.98);\r\n\t\t\t\t\ttransition: transform 0.1s ease;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// 设备列表样式\r\n\t\t.device-list {\r\n\t\t\t.device-item {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tpadding: 25rpx 0;\r\n\t\t\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tborder-bottom: none;\r\n\t\t\t\t}\r\n\t\t\t\t.device-name {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t}\r\n\t\t\t\t.checkbox {\r\n\t\t\t\t\twidth: 40rpx;\r\n\t\t\t\t\theight: 40rpx;\r\n\t\t\t\t\tborder: 1rpx solid $uni-border-color;\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t&.checked {\r\n\t\t\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\t\t\tborder-color: $uni-color-primary;\r\n\t\t\t\t\t\t.iconfont {\r\n\t\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// 热用户列表项\r\n\t\t.heatunit-list {\r\n\t\t\t.heatunit-item {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tpadding: 25rpx 0;\r\n\t\t\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tborder-bottom: none;\r\n\t\t\t\t}\r\n\t\t\t\t.heatunit-name {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t}\r\n\t\t\t\t.checkbox {\r\n\t\t\t\t\twidth: 40rpx;\r\n\t\t\t\t\theight: 40rpx;\r\n\t\t\t\t\tborder: 1rpx solid $uni-border-color;\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t&.checked {\r\n\t\t\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\t\t\tborder-color: $uni-color-primary;\r\n\t\t\t\t\t\t.iconfont {\r\n\t\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// 巡检项模板列表\r\n\t\t.task-templates {\r\n\t\t\t.template-item {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: flex-start;\r\n\t\t\t\tpadding: 20rpx 0;\r\n\t\t\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tborder-bottom: none;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&.active {\r\n\t\t\t\t\tbackground-color: #f0f8ff;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.checkbox {\r\n\t\t\t\t\twidth: 40rpx;\r\n\t\t\t\t\theight: 40rpx;\r\n\t\t\t\t\tborder: 1rpx solid $uni-border-color;\r\n\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\tmargin-top: 5rpx;\r\n\t\t\t\t\tflex-shrink: 0;\r\n\r\n\t\t\t\t\t&.checked {\r\n\t\t\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\t\t\tborder-color: $uni-color-primary;\r\n\t\t\t\t\t\t.iconfont {\r\n\t\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.template-left {\r\n\t\t\t\t\tflex: 1;\r\n\r\n\t\t\t\t\t.template-header {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tmargin-bottom: 12rpx;\r\n\r\n\t\t\t\t\t\t.template-title {\r\n\t\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.template-info {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t\t\tline-height: 1.5;\r\n\t\t\t\t\t\tmargin-bottom: 6rpx;\r\n\r\n\t\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\t\tmargin-bottom: 0;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t&.template-description {\r\n\t\t\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.selector-footer {\r\n\t\tpadding: 25rpx 30rpx 40rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-top: 1rpx solid #e5e5e5;\r\n\t\tbox-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.08);\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\t// 处理 iPhone X 等底部安全区\r\n\t\tpadding-bottom: calc(40rpx + constant(safe-area-inset-bottom));  \r\n\t\tpadding-bottom: calc(40rpx + env(safe-area-inset-bottom));\r\n\t\tbackground-color: #fff; /* 确保背景色不是透明的 */\r\n\t\tposition: relative; /* 确保显示在内容之上 */\r\n\t\tz-index: 1;\r\n\r\n\t\t.select-count {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: $uni-text-color;\r\n\t\t}\r\n\t\t.confirm-button {\r\n\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\tcolor: #fff;\r\n\t\t\tpadding: 15rpx 40rpx;\r\n\t\t\tborder-radius: 30rpx;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t}\r\n\t}\r\n\t\r\n\t// 特别为热用户选择器添加的样式\r\n\t.heat-unit-selector {\r\n\t\tz-index: 100; // 确保覆盖底部按钮\r\n\t\t\r\n\t\t// 优化热用户选择器的整体样式\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 20rpx 20rpx 0 0;\r\n\t\tmax-height: 80vh;\r\n\t\theight: 80vh;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\r\n\t\t.selector-content {\r\n\t\t\tflex: 1;\r\n\t\t\toverflow-y: auto;\r\n\t\t\t-webkit-overflow-scrolling: touch;\r\n\t\t}\r\n\t}\r\n\t\r\n\t// 巡检项选择器样式\r\n\t/* 巡检项选择器样式已移至task-selector中 */\r\n}\r\n\r\n// 确保 .placeholder-style 生效并覆盖内联样式\r\n.placeholder-style {\r\n\tcolor: $uni-text-color-placeholder !important; \r\n\tpadding-left: 0 !important; // 覆盖内联样式\r\n}\r\n \r\n\r\n.iconfont {\r\n\t// font-family: \"iconfont\" !important;\r\n\tfont-size: 16px;\r\n\tfont-style: normal;\r\n\t-webkit-font-smoothing: antialiased;\r\n\t-moz-osx-font-smoothing: grayscale;\r\n}\r\n\r\n// 使用通用字符作为图标\r\n.icon-add:before {\r\n\tcontent: \"+\"; // 使用加号替代添加图标\r\n}\r\n.icon-check:before {\r\n\tcontent: \"✓\"; // 使用对勾符号替代勾选图标\r\n}\r\n.icon-clear:before {\r\n\tcontent: \"×\"; // 使用乘号替代清除图标\r\n}\r\n\r\n.importance-tag {\r\n\tpadding: 4rpx 8rpx;\r\n\tborder-radius: 4rpx;\r\n\tfont-size: 22rpx;\r\n\t&.normal-importance {\r\n\t\tbackground-color: #e6f7ff;\r\n\t\tcolor: #1890ff;\r\n\t\tborder: 1rpx solid #91d5ff;\r\n\t}\r\n\t&.important-importance {\r\n\t\tbackground-color: #fff7e6;\r\n\t\tcolor: #fa8c16;\r\n\t\tborder: 1rpx solid #ffd591;\r\n\t}\r\n\t&.critical-importance {\r\n\t\tbackground-color: #fff1f0;\r\n\t\tcolor: #f5222d;\r\n\t\tborder: 1rpx solid #ffa39e;\r\n\t}\r\n}\r\n\r\n.task-info-row {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tmargin-bottom: 6rpx;\r\n}\r\n\r\n.template-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 12rpx;\r\n\t\r\n\t.template-title {\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: $uni-text-color;\r\n\t\tword-break: break-all;\r\n\t\tmargin-right: 15rpx;\r\n\t\tline-height: 1.4;\r\n\t}\r\n\t\r\n\t.importance-tag {\r\n\t\tpadding: 4rpx 8rpx;\r\n\t\tborder-radius: 4rpx;\r\n\t\tfont-size: 22rpx;\r\n\t\t&.normal-importance {\r\n\t\t\tbackground-color: #e6f7ff;\r\n\t\t\tcolor: #1890ff;\r\n\t\t\tborder: 1rpx solid #91d5ff;\r\n\t\t}\r\n\t\t&.important-importance {\r\n\t\t\tbackground-color: #fff7e6;\r\n\t\t\tcolor: #fa8c16;\r\n\t\t\tborder: 1rpx solid #ffd591;\r\n\t\t}\r\n\t\t&.critical-importance {\r\n\t\t\tbackground-color: #fff1f0;\r\n\t\t\tcolor: #f5222d;\r\n\t\t\tborder: 1rpx solid #ffa39e;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.task-selector {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 20rpx 20rpx 0 0;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tmax-height: 80vh;\r\n\theight: 80vh;\r\n\toverflow: hidden;\r\n\tz-index: 100; \r\n\r\n\t.selector-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 30rpx;\r\n\t\tborder-bottom: 1rpx solid $uni-border-color;\r\n\t\t\r\n\t\t.selector-title {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tcolor: $uni-text-color;\r\n\t\t}\r\n\t\t\r\n\t\t.header-actions {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t}\r\n\t\t\r\n\t\t.close-button {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: $uni-text-color-grey;\r\n\t\t}\r\n\t\t\r\n\t\t.confirm-button-header {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: $uni-color-primary;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tmargin-right: 20rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.selector-content {\r\n\t\tflex: 1;\r\n\t\toverflow-y: auto;\r\n\t\tpadding: 0 20rpx;\r\n\t\t-webkit-overflow-scrolling: touch;\r\n\t\t\r\n\t\t.device-filter {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 0 10rpx 20rpx;\r\n\t\t\tmargin-bottom: 0;\r\n\t\t\tposition: relative;\r\n\t\t\t\r\n\t\t\t.filter-picker {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\theight: 80rpx;\r\n\t\t\t\t\r\n\t\t\t\t.picker-value {\r\n\t\t\t\t\tbackground: #f5f5f5;\r\n\t\t\t\t\tborder-radius: 40rpx;\r\n\t\t\t\t\tpadding: 0 30rpx;\r\n\t\t\t\t\theight: 80rpx;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.arrow-down {\r\n\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.template-search {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 0 10rpx 20rpx;\r\n\t\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t\tposition: relative;\r\n\t\t\t\r\n\t\t\t.search-input {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\theight: 80rpx;\r\n\t\t\t\tborder-radius: 40rpx;\r\n\t\t\t\tbackground: #f5f5f5;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tpadding: 0 80rpx 0 30rpx;\r\n\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.search-clear {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: 130rpx;\r\n\t\t\t\ttop: 50%;\r\n\t\t\t\ttransform: translateY(-50%);\r\n\t\t\t\twidth: 40rpx;\r\n\t\t\t\theight: 40rpx;\r\n\t\t\t\tline-height: 40rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tbackground: #ccc;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.search-btn {\r\n\t\t\t\tpadding: 0 30rpx;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t\theight: 80rpx;\r\n\t\t\t\tline-height: 80rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.task-templates {\r\n\t\t\tpadding-bottom: 20rpx;\r\n\t\t\t\r\n\t\t\t.template-item {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\talign-items: flex-start;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tborder: 1rpx solid $uni-border-color;\r\n\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\tpadding: 24rpx;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\tbox-shadow: 0 2rpx 6rpx rgba(0,0,0,0.03);\r\n\t\t\t\ttransition: all 0.2s ease-in-out;\r\n\r\n\t\t\t\t&.active {\r\n\t\t\t\t\tbackground-color: #e9f5ff;\r\n\t\t\t\t\tborder-color: $uni-color-primary;\r\n\t\t\t\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.1);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.checkbox {\r\n\t\t\t\t\twidth: 40rpx;\r\n\t\t\t\t\theight: 40rpx;\r\n\t\t\t\t\tmin-width: 40rpx;\r\n\t\t\t\t\tborder: 1rpx solid $uni-border-color;\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\tmargin-right: 15rpx;\r\n\t\t\t\t\talign-self: flex-start;\r\n\t\t\t\t\tmargin-top: 4rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.checked {\r\n\t\t\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\t\t\tborder-color: $uni-color-primary;\r\n\t\t\t\t\t\t.iconfont {\r\n\t\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.template-left {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\toverflow: hidden;\r\n\r\n\t\t\t\t\t.template-header {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\t\tmargin-bottom: 12rpx;\r\n\r\n\t\t\t\t\t\t.template-title {\r\n\t\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t\t\t\tword-break: break-all;\r\n\t\t\t\t\t\t\tmargin-right: 15rpx;\r\n\t\t\t\t\t\t\tline-height: 1.4;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.template-info {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\tline-height: 1.5;\r\n\t\t\t\t\t\tmargin-bottom: 6rpx;\r\n\t\t\t\t\t\tword-break: break-all;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\t\tmargin-bottom: 0;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t\r\n\t\t.empty-tip {\r\n\t\t\ttext-align: center;\r\n\t\t\tpadding: 40rpx 0;\r\n\t\t\tcolor: $uni-text-color-placeholder;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.device-selector-popup {\r\n    z-index: 100;\r\n    background-color: #fff;\r\n    border-radius: 20rpx 20rpx 0 0;\r\n    max-height: 60vh;\r\n    height: 60vh;\r\n    display: flex;\r\n    flex-direction: column;\r\n\r\n    .selector-content {\r\n        flex: 1;\r\n        overflow-y: auto;\r\n        -webkit-overflow-scrolling: touch;\r\n        padding: 0 30rpx;\r\n\r\n        .device-list {\r\n            .device-item {\r\n                display: flex;\r\n                justify-content: space-between;\r\n                align-items: center;\r\n                padding: 25rpx 0;\r\n                border-bottom: 1rpx solid #f0f0f0;\r\n                \r\n                &:last-child {\r\n                    border-bottom: none;\r\n                }\r\n                \r\n                .device-name {\r\n                    font-size: 28rpx;\r\n                    color: $uni-text-color;\r\n                }\r\n                \r\n                .checkbox {\r\n                    width: 40rpx;\r\n                    height: 40rpx;\r\n                    border: 1rpx solid $uni-border-color;\r\n                    border-radius: 50%;\r\n                    display: flex;\r\n                    align-items: center;\r\n                    justify-content: center;\r\n                    \r\n                    &.checked {\r\n                        background-color: $uni-color-primary;\r\n                        border-color: $uni-color-primary;\r\n                        \r\n                        .iconfont {\r\n                            color: #fff;\r\n                            font-size: 24rpx;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.staff-selector-popup {\r\n\tz-index: 100;\r\n\tbackground-color: #fff;\r\n\tborder-radius: 20rpx 20rpx 0 0;\r\n\tmax-height: 80vh;\r\n\theight: 80vh;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\r\n\t.selector-content {\r\n\t\tflex: 1;\r\n\t\toverflow-y: auto;\r\n\t\t-webkit-overflow-scrolling: touch;\r\n\t\tpadding: 0 30rpx;\r\n\r\n\t\t.staff-list {\r\n\t\t\t.staff-item {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tpadding: 25rpx 0;\r\n\t\t\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t\t\t\t\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tborder-bottom: none;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.staff-name {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.checkbox {\r\n\t\t\t\t\twidth: 40rpx;\r\n\t\t\t\t\theight: 40rpx;\r\n\t\t\t\t\tborder: 1rpx solid $uni-border-color;\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.checked {\r\n\t\t\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\t\t\tborder-color: $uni-color-primary;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.iconfont {\r\n\t\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n</style>", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/patrol/create.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "heatUnitApi", "userApi", "deviceApi", "patrolApi", "dictApi"], "mappings": ";;;AAkfA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,UAAU;AAAA,QACT,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,WAAW;AAAA,QACX,SAAS;AAAA,QACT,aAAa,CAAE;AAAA,QACf,cAAc;AAAA,QACd,kBAAkB;AAAA,QAClB,kBAAkB,CAAE;AAAA,QACpB,mBAAmB,CAAE;AAAA,QACrB,WAAW,CAAE;AAAA,QACb,WAAW;AAAA,QACX,YAAY,CAAE;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,MACZ;AAAA,MACD,qBAAqB;AAAA,QACpB,EAAE,OAAO,MAAM,OAAO,QAAS;AAAA,QAC/B,EAAE,OAAO,MAAM,OAAO,SAAU;AAAA,QAChC,EAAE,OAAO,MAAM,OAAO,UAAU;AAAA,MAChC;AAAA,MACD,mBAAmB,CAAE;AAAA;AAAA,MACrB,mBAAmB;AAAA,MACnB,iBAAgB;AAAA,MAChB,cAAc,CAAE;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe,CAAE;AAAA,MACjB,aAAa;AAAA;AAAA,MAGb,eAAe,CAAE;AAAA,MACjB,mBAAmB,CAAE;AAAA,MACrB,eAAe;AAAA;AAAA,MAGf,aAAa;AAAA,QACZ,EAAE,OAAO,MAAM,OAAO,EAAG;AAAA,QACzB,EAAE,OAAO,MAAM,OAAO,EAAG;AAAA,QACzB,EAAE,OAAO,MAAM,OAAO,EAAG;AAAA,QACzB,EAAE,OAAO,MAAM,OAAO,EAAG;AAAA,QACzB,EAAE,OAAO,MAAM,OAAO,EAAG;AAAA,QACzB,EAAE,OAAO,MAAM,OAAO,EAAG;AAAA,QACzB,EAAE,OAAO,MAAM,OAAO,EAAE;AAAA,MACxB;AAAA,MACD,cAAc,MAAM,KAAK,EAAE,QAAQ,MAAM,CAAC,GAAG,OAAO;AAAA,QACnD,OAAO,GAAG,IAAI,CAAC;AAAA,QACf,OAAO,IAAI;AAAA,MACZ,EAAE;AAAA;AAAA,MAGF,mBAAmB,CAAE;AAAA,MACrB,iBAAiB,CAAE;AAAA;AAAA,MAGnB,kBAAkB;AAAA,MAClB,iBAAiB,CAAE;AAAA,MACnB,uBAAuB;AAAA,MACvB,mBAAmB,CAAE;AAAA;AAAA,MAGrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA;AAAA,MAGvB,aAAa;AAAA,MACb,qBAAqB,CAAE;AAAA;AAAA,MACvB,uBAAuB,CAAE;AAAA;AAAA,IAC1B;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,oBAAoB;AACnB,UAAI,YAAY,CAAA;AAEhB,UAAI,KAAK,uBAAuB;AAC/B,oBAAY,KAAK,kBAAkB,KAAK,qBAAqB,KAAK,CAAA;AAAA,aAC5D;AACN,oBAAY,CAAA;AACZ,aAAK,gBAAgB,QAAQ,YAAU;AACtC,gBAAM,QAAQ,KAAK,kBAAkB,OAAO,EAAE,KAAK;AACnD,sBAAY,UAAU,OAAO,KAAK;AAAA,QACnC,CAAC;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,iBAAiB,KAAK,cAAc,KAAO,MAAI,IAAI;AAC5D,eAAO;AAAA,MACR;AAEA,YAAM,UAAU,KAAK,cAAc,YAAa,EAAC,KAAI;AACrD,aAAO,UAAU,OAAO,cAAY;AACnC,eAAQ,SAAS,YAAY,SAAS,SAAS,YAAa,EAAC,SAAS,OAAO,KAC3E,SAAS,eAAe,SAAS,YAAY,cAAc,SAAS,OAAO,KAC3E,SAAS,eAAe,SAAS,YAAY,cAAc,SAAS,OAAO,KAC3E,SAAS,gBAAgB,SAAS,aAAa,YAAW,EAAG,SAAS,OAAO;AAAA,MAChF,CAAC;AAAA,IACD;AAAA,IACD,gBAAgB;AACf,UAAI,QAAQ;AACZ,aAAO,OAAO,KAAK,iBAAiB,EAAE,QAAQ,WAAS;AACtD,iBAAS,MAAM,OAAO,UAAQ,KAAK,QAAQ,EAAE;AAAA,MAC9C,CAAC;AACD,aAAO;AAAA,IACP;AAAA,IACD,uBAAuB;AACtB,aAAO,KAAK,aAAa,OAAO,CAAC,GAAG,UAAU,QAAQ,EAAE;AAAA,IACxD;AAAA;AAAA,IAED,uBAAuB;AACtB,aAAO,KAAK,cAAc,SAAS,KAAK,KAAK,oBAAoB,WAAW,KAAK,cAAc;AAAA,IAC/F;AAAA;AAAA,IAED,qBAAqB;AACpB,aAAO,KAAK,kBAAkB,SAAS,KAAK,KAAK,sBAAsB,KAAK,kBAAkB;AAAA,IAC9F;AAAA,IACD,oBAAoB;AACnB,aAAO,KAAK,kBAAkB,OAAO,cAAY,SAAS,QAAQ,EAAE;AAAA,IACrE;AAAA,EACA;AAAA,EACD,SAAS;AACR,SAAK,iBAAgB;AAErB,SAAK,sBAAqB;AAG1B,SAAK,kBAAkB;AACvB,SAAK,oBAAoB;AAGzB,SAAK,oBAAoB;AACzB,SAAK,wBAAwB;AAAA,EAC7B;AAAA,EACD,SAAS;AAAA;AAAA,IAER,mBAAmB;AAElB,YAAM,iBAAiBA,cAAG,MAAC,eAAe,YAAY,KAAK;AAC3DA,oBAAY,MAAA,MAAA,OAAA,kCAAA,WAAW,cAAc;AAGrC,YAAM,mBAAmB,mBAAmB,OACnB,eAAe,MAAM,GAAG,EAAE,SAAS,KACrD,eAAe,MAAM,GAAG,EAAE,SAAS,GAAG;AAE7CC,gBAAAA,YAAY,QAAQ,EAClB,KAAK,SAAO;AACZ,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAEjC,cAAI,eAAe,IAAI;AAGvB,cAAI,CAAC,oBAAoB,gBAAgB;AAExC,kBAAM,gBAAgB,eAAe,MAAM,GAAG,EAAE,IAAI,QAAM,SAAS,GAAG,KAAI,CAAE,CAAC;AAG7E,2BAAe,aAAa,OAAO,UAAQ,cAAc,SAAS,KAAK,EAAE,CAAC;AAAA,UAC3E;AAEA,eAAK,kBAAkB;AACvB,eAAK,oBAAoB,CAAC,GAAG,YAAY;AAGzC,cAAI,KAAK,kBAAkB,SAAS,GAAG;AACtC,iBAAK,mBAAmB,KAAK,kBAAkB,CAAC;AAChD,iBAAK,SAAS,aAAa,KAAK,iBAAiB;AAGjD,iBAAK,sBAAsB,KAAK,iBAAiB,EAAE;AAGnD,iBAAK,iBAAiB,KAAK,iBAAiB,EAAE;AAAA,iBACxC;AAEN,iBAAK,iBAAgB;AAAA,UACtB;AAEAD,wBAAY,MAAA,MAAA,OAAA,kCAAA,cAAc,KAAK,iBAAiB;AAChDA,wBAAA,MAAA,MAAA,OAAA,kCAAY,aAAa,KAAK,gBAAgB;AAAA,QAC/C;AAAA,OACA,EACA,MAAM,SAAO;AACbA,sBAAA,MAAA,MAAA,SAAA,kCAAc,cAAc,GAAG;AAAA,MAChC,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB,aAAa,MAAM;AACnCE,gBAAAA,QAAQ,iBAAiB,EAAE,MAAM,aAAa,EAC5C,KAAK,SAAO;AACZF,sBAAA,MAAA,MAAA,OAAA,kCAAY,aAAa,IAAI,IAAI;AACjC,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAEjC,gBAAM,WAAW,IAAI,KAAK,IAAI,WAAS;AAAA,YACtC,IAAI,KAAK;AAAA,YACT,MAAM,KAAK;AAAA,YACX,YAAY,KAAK;AAAA;AAAA,YACjB,UAAU,KAAK;AAAA,YACf,OAAO,KAAK;AAAA,YACZ,YAAY,KAAK;AAAA,UACjB,EAAC;AAGF,cAAI,eAAe,MAAM;AACxB,iBAAK,eAAe,KAAK,sBAAsB,UAAU,UAAU;AACnEA,0BAAAA,MAAY,MAAA,OAAA,kCAAA,YAAY,UAAU,aAAa,KAAK,YAAY;AAChEA,0BAAAA,MAAY,MAAA,OAAA,kCAAA,eAAe,SAAS,MAAM,aAAa,KAAK,aAAa,MAAM,EAAE;AAGjF,gBAAI,KAAK,aAAa,WAAW,GAAG;AACnCA,kFAAa,YAAY,UAAU,cAAc;AAAA,YAClD;AAAA,iBACM;AAEN,iBAAK,eAAe;AACpBA,+EAAY,gBAAgB,KAAK,YAAY;AAAA,UAC9C;AAAA,QACD;AAAA,OACA,EACA,MAAM,SAAO;AACbA,6EAAc,aAAa,GAAG;AAAA,MAC/B,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,sBAAsB,UAAU,YAAY;AAC3CA,oBAAA,MAAA,MAAA,OAAA,kCAAY,sBAAsB,UAAU,EAAE;AAE9C,YAAM,gBAAgB,SAAS,OAAO,WAAS;AAE9C,YAAI,CAAC,MAAM,YAAY;AACtBA,8BAAY,MAAA,OAAA,kCAAA,KAAK,MAAM,IAAI,SAAS;AACpC,iBAAO;AAAA,QACR;AAGA,cAAM,iBAAiB,MAAM,WAAW,SAAU,EAAC,MAAM,GAAG,EAAE,IAAI,QAAM,GAAG,KAAM,CAAA;AACjFA,sBAAAA,MAAA,MAAA,OAAA,kCAAY,MAAM,MAAM,IAAI,WAAW,eAAe,KAAK,IAAI,CAAC,GAAG;AAGnE,cAAM,gBAAgB,eAAe,SAAS,GAAG,KAAK,eAAe,SAAS,WAAW,SAAQ,CAAE;AACnGA,2EAAY,GAAG,gBAAgB,MAAM,GAAG,IAAI,MAAM,IAAI,KAAK,gBAAgB,QAAQ,KAAK,EAAE;AAE1F,eAAO;AAAA,MACR,CAAC;AAEDA,oBAAAA,MAAY,MAAA,OAAA,kCAAA,YAAY,cAAc,MAAM,IAAI,SAAS,MAAM,QAAQ;AACvE,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,sBAAsB,YAAY;AACjC,UAAI,CAAC,YAAY;AAChBA,sBAAAA,MAAc,MAAA,SAAA,kCAAA,UAAU;AACxB;AAAA,MACD;AAEAA,oBAAA,MAAA,MAAA,OAAA,kCAAY,uBAAuB,UAAU;AAG7CA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACR,CAAC;AAEDG,gBAAS,UAAC,uBAAuB,UAAU,EACzC,KAAK,SAAO;AACZH,sBAAA,MAAA,MAAA,OAAA,kCAAY,cAAc,GAAG;AAC7BA,sBAAG,MAAC,YAAW;AAEf,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAEjC,eAAK,gBAAgB,IAAI,KAAK,IAAI,aAAW;AAAA,YAC5C,IAAI,OAAO;AAAA,YACX,MAAM,OAAO;AAAA,YACb,MAAM,OAAO;AAAA,YACb,eAAe,OAAO;AAAA,YACtB,cAAc,KAAK,iBAAiB;AAAA,UACpC,EAAC;AAEFA,6EAAY,aAAa,KAAK,aAAa;AAG3C,eAAK,kBAAkB;AACvB,eAAK,SAAS,YAAY;AAG1B,eAAK,oBAAoB;AACzB,eAAK,SAAS,aAAa;eACrB;AACNA,8BAAA,MAAA,SAAA,kCAAc,aAAa,IAAI,WAAW,MAAM;AAChDA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,OACA,EACA,MAAM,SAAO;AACbA,sBAAG,MAAC,YAAW;AACfA,4BAAc,MAAA,SAAA,kCAAA,WAAW,UAAU,YAAY,GAAG;AAClDA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MAEF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,wBAAwB,UAAU;AACjC,UAAI,CAAC;AAAU;AACfG,gBAAS,UAAC,yBAAyB,QAAQ,EACzC,KAAK,SAAO;AACZ,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AACjC,gBAAM,QAAQ,IAAI,KAAK,IAAI,WAAS;AAAA,YACnC,IAAI,KAAK;AAAA,YACT,QAAQ,KAAK;AAAA,YACb,UAAU,KAAK;AAAA,YACf,UAAU,KAAK;AAAA,YACf,cAAc,KAAK;AAAA,YACnB,WAAW,KAAK;AAAA,YAChB,MAAM,KAAK;AAAA,YACX,aAAa,KAAK;AAAA,YAClB,aAAa,KAAK;AAAA,YAClB,YAAY,KAAK;AAAA,YACjB,aAAa,KAAK;AAAA,YAClB,UAAU;AAAA,UACV,EAAC;AACF,eAAK,KAAK,KAAK,mBAAmB,UAAU,KAAK;AAAA,QAClD;AAAA,OACA,EACA,MAAM,SAAO;AACbH,4BAAc,MAAA,SAAA,kCAAA,WAAW,QAAQ,WAAW,GAAG;AAAA,MAChD,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,mBAAmB,GAAG;AACrB,WAAK,cAAc,EAAE,OAAO;AAC5B,YAAM,iBAAiB,KAAK,cAAc,KAAK,WAAW;AAC1D,UAAI,CAAC,KAAK,SAAS,UAAU,SAAS,eAAe,EAAE,GAAG;AACzD,aAAK,SAAS,UAAU,KAAK,eAAe,EAAE;AAC9C,aAAK,gBAAgB,KAAK,cAAc;AAExC,aAAK,wBAAwB,eAAe,EAAE;AAAA,aACxC;AACNA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,aAAa,UAAU;AACtB,YAAM,QAAQ,KAAK,SAAS,UAAU,UAAU,QAAM,OAAO,QAAQ;AACrE,UAAI,UAAU,IAAI;AACjB,aAAK,SAAS,UAAU,OAAO,OAAO,CAAC;AAEvC,cAAM,cAAc,KAAK,gBAAgB,UAAU,YAAU,OAAO,OAAO,QAAQ;AACnF,YAAI,gBAAgB,IAAI;AACvB,eAAK,gBAAgB,OAAO,aAAa,CAAC;AAAA,QAC3C;AAEA,YAAI,KAAK,kBAAkB,QAAQ,GAAG;AACrC,iBAAO,KAAK,kBAAkB,QAAQ;AAAA,QACvC;AAEA,aAAK,SAAS,aAAa,KAAK,SAAS,WAAW,OAAO,UAAQ,KAAK,aAAa,QAAQ;AAAA,MAC9F;AAAA,IACA;AAAA;AAAA,IAGD,kBAAkB,GAAG;AACpB,WAAK,aAAa,EAAE,OAAO;AAC3B,YAAM,gBAAgB,KAAK,aAAa,KAAK,UAAU;AAEvD,UAAI,CAAC,KAAK,SAAS,YAAY,SAAS,cAAc,EAAE,GAAG;AAC1D,aAAK,SAAS,YAAY,KAAK,cAAc,EAAE;AAC/C,aAAK,kBAAkB,KAAK,aAAa;AAAA,aACnC;AACNA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,eAAe,YAAY;AAC1B,YAAM,QAAQ,KAAK,SAAS,YAAY,UAAU,QAAM,OAAO,UAAU;AACzE,UAAI,UAAU,IAAI;AACjB,aAAK,SAAS,YAAY,OAAO,OAAO,CAAC;AAEzC,cAAM,aAAa,KAAK,kBAAkB,UAAU,WAAS,MAAM,OAAO,UAAU;AACpF,YAAI,eAAe,IAAI;AACtB,eAAK,kBAAkB,OAAO,YAAY,CAAC;AAAA,QAC5C;AAAA,MACD;AAAA,IACA;AAAA;AAAA,IAGD,yBAAyB,GAAG;AAC3B,WAAK,oBAAoB,EAAE,OAAO;AAClC,WAAK,SAAS,eAAe,KAAK,oBAAoB,KAAK,iBAAiB,EAAE;AAAA,IAC9E;AAAA;AAAA,IAED,uBAAuB,GAAG;AACzB,WAAK,kBAAkB,EAAE,OAAO;AAChC,WAAK,SAAS,aAAa,KAAK,kBAAkB,KAAK,eAAe,EAAE;AAAA,IACxE;AAAA;AAAA,IAED,sBAAsB,GAAG;AACxB,WAAK,SAAS,YAAY,EAAE,OAAO;AAEnC,UAAI,KAAK,SAAS,WAAW,KAAK,SAAS,UAAU,KAAK,SAAS,WAAW;AAC7E,aAAK,SAAS,UAAU;AAAA,MACzB;AAAA,IACA;AAAA;AAAA,IAGD,oBAAoB,GAAG;AACtB,WAAK,SAAS,UAAU,EAAE,OAAO;AAAA,IACjC;AAAA;AAAA,IAGD,mBAAmB;AAClB,UAAI,KAAK,gBAAgB,WAAW,GAAG;AACtCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAGA,WAAK,oBAAoB;AACzB,WAAK,wBAAwB;AAC7B,WAAK,cAAc;AAEnB,WAAK,MAAM,aAAa;IACxB;AAAA;AAAA,IAGD,mBAAmB;AAElBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAChB,iBAAK,cAAc;AACnB,iBAAK,MAAM,aAAa;UACzB;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB,OAAO;AAC1B,YAAM,WAAW,KAAK,kBAAkB,KAAK;AAC7C,YAAM,cAAc,KAAK,kBAAkB,SAAS,QAAQ;AAC5D,UAAI,aAAa;AAChB,cAAM,YAAY,YAAY,UAAU,UAAQ,KAAK,OAAO,SAAS,EAAE;AACvE,YAAI,cAAc,IAAI;AACrB,eAAK,KAAK,YAAY,SAAS,GAAG,YAAY,CAAC,YAAY,SAAS,EAAE,QAAQ;AAAA,QAC/E;AAAA,MACD;AAAA,IACA;AAAA;AAAA,IAGD,cAAc,KAAK;AAClB,YAAM,QAAQ,KAAK,SAAS,iBAAiB,QAAQ,GAAG;AACxD,UAAI,UAAU,IAAI;AACjB,aAAK,SAAS,iBAAiB,KAAK,GAAG;AAAA,aACjC;AACN,aAAK,SAAS,iBAAiB,OAAO,OAAO,CAAC;AAAA,MAC/C;AAAA,IACA;AAAA;AAAA,IAGD,eAAe,KAAK;AACnB,YAAM,QAAQ,KAAK,SAAS,kBAAkB,QAAQ,GAAG;AACzD,UAAI,UAAU,IAAI;AACjB,aAAK,SAAS,kBAAkB,KAAK,GAAG;AAAA,aAClC;AACN,aAAK,SAAS,kBAAkB,OAAO,OAAO,CAAC;AAAA,MAChD;AAAA,IACA;AAAA;AAAA,IAGD,uBAAuB;AACtB,UAAI,gBAAgB,CAAA;AACpB,aAAO,QAAQ,KAAK,iBAAiB,EAAE,QAAQ,CAAC,CAAC,UAAU,KAAK,MAAM;AACrE,cAAM,WAAW,MAAM,OAAO,UAAQ,KAAK,QAAQ;AACnD,YAAI,SAAS,SAAS,GAAG;AACxB,0BAAgB,cAAc,OAAO,SAAS,IAAI,WAAS;AAAA,YAC1D,GAAG;AAAA,YACH,UAAU,SAAS,QAAQ;AAAA,UAC3B,EAAC,CAAC;AAAA,QACJ;AAAA,MACD,CAAC;AAED,WAAK,SAAS,aAAa,cAAc,IAAI,WAAS;AAAA,QACrD,IAAI,KAAK;AAAA,QACT,QAAQ,KAAK;AAAA,QACb,UAAU,KAAK;AAAA,QACf,UAAU,KAAK;AAAA,QACf,cAAc,KAAK;AAAA,QACnB,aAAa,KAAK;AAAA,QAClB,aAAa,KAAK;AAAA,QAClB,aAAa,KAAK;AAAA,QAClB,WAAW,KAAK;AAAA,QAChB,MAAM,KAAK;AAAA,QACX,YAAY,KAAK;AAAA,MACjB,EAAC;AAEF,WAAK,cAAc;AACnB,WAAK,MAAM,aAAa;AAGxBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,MAAM,cAAc,MAAM;AAAA,QACjC,MAAM;AAAA,QACN,UAAU;AAAA,MACX,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,OAAO;AACjB,YAAM,OAAO,KAAK,SAAS,WAAW,KAAK;AAE3C,UAAI,QAAQ,KAAK,YAAY,KAAK,kBAAkB,KAAK,QAAQ,GAAG;AACnE,cAAM,QAAQ,KAAK,kBAAkB,KAAK,QAAQ;AAClD,cAAM,YAAY,MAAM,UAAU,UAAQ,KAAK,OAAO,KAAK,EAAE;AAC7D,YAAI,cAAc,IAAI;AACrB,eAAK,KAAK,MAAM,SAAS,GAAG,YAAY,KAAK;AAAA,QAC9C;AAAA,MACD;AAEA,WAAK,SAAS,WAAW,OAAO,OAAO,CAAC;AAAA,IACxC;AAAA;AAAA,IAGD,aAAa;AAEZ,UAAI,KAAK,kBAAkB,WAAW,GAAG;AACxCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACX,CAAC;AACD;AAAA,MACD;AAEA,UAAI,CAAC,KAAK,kBAAkB;AAC3BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,UAAI,CAAC,KAAK,SAAS,MAAM;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,UAAI,CAAC,KAAK,SAAS,WAAW;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,UAAI,CAAC,KAAK,SAAS,SAAS;AAC3BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,UAAI,KAAK,SAAS,YAAY,WAAW,GAAG;AAC3CA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,UAAI,KAAK,SAAS,UAAU,WAAW,GAAG;AACzCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,UAAI,KAAK,SAAS,iBAAiB,YAAY,CAAC,KAAK,SAAS,kBAAkB;AAC/EA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,UAAI,KAAK,SAAS,iBAAiB,YAAY,KAAK,SAAS,iBAAiB,WAAW,GAAG;AAC3FA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,UAAI,KAAK,SAAS,iBAAiB,aAAa,KAAK,SAAS,kBAAkB,WAAW,GAAG;AAC7FA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AACA,UAAI,CAAC,KAAK,SAAS,YAAY;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,UAAI,KAAK,SAAS,WAAW,WAAW,GAAG;AAC1CA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,WAAK,SAAS,WAAW,QAAQ,UAAQ;AACxC,YAAI,CAAC,KAAK,YAAY,KAAK,SAAS,UAAU,SAAS,GAAG;AACzD,eAAK,WAAW,KAAK,SAAS,UAAU,CAAC;AAAA,QAC1C;AAAA,MACD,CAAC;AAED,YAAM,aAAa;AAAA,QAClB,MAAM,KAAK,SAAS;AAAA,QACpB,YAAY,KAAK,SAAS;AAAA,QAC1B,UAAU,KAAK,SAAS;AAAA,QACxB,cAAc,KAAK,SAAS;AAAA,QAC5B,aAAa,KAAK,SAAS;AAAA,QAC3B,WAAW,KAAK,iBAAiB;AAAA,QACjC,cAAc,KAAK,iBAAiB;AAAA,QACpC,YAAY,KAAK,SAAS;AAAA,QAC1B,eAAe,KAAK,SAAS;AAAA,QAC7B,mBAAmB,KAAK,SAAS;AAAA,QACjC,oBAAoB,KAAK,SAAS;AAAA,QAClC,qBAAqB,KAAK,SAAS;AAAA,QAEnC,aAAa,KAAK,SAAS,WAAW,IAAI,WAAS;AAAA,UAClD,uBAAuB,KAAK;AAAA,QAC7B,EAAE;AAAA;AAGHA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,MACR,CAAC;AAEDA,oBAAAA,MAAA,MAAA,OAAA,mCAAY,aAAa,KAAK,UAAU,YAAY,MAAM,CAAC,CAAC;AAE5DI,gBAAS,UAAC,WAAW,UAAU,EAC7B,KAAK,SAAO;AACZJ,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAED,mBAAW,MAAM;AAChBA,wBAAG,MAAC,aAAY;AAAA,QAChB,GAAE,IAAI;AAAA,OACP,EACA,MAAM,SAAO;AACbA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,IAAI,UAAU;AAAA,UACrB,MAAM;AAAA,QACP,CAAC;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,eAAe;AACdA,oBAAY,MAAA,MAAA,OAAA,mCAAA,SAAS,KAAK,aAAa;AAAA,IACvC;AAAA;AAAA,IAGD,cAAc;AACb,WAAK,gBAAgB;AAAA,IACrB;AAAA;AAAA,IAGD,uBAAuB;AACtB,UAAI,KAAK,kBAAkB,WAAW,GAAG;AACxCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AACA,WAAK,cAAc;AACnB,WAAK,MAAM,iBAAiB;IAC5B;AAAA;AAAA,IAGD,uBAAuB;AACtB,WAAK,cAAc;AACnB,WAAK,MAAM,iBAAiB;IAC5B;AAAA;AAAA,IAGD,eAAe,UAAU;AACxB,WAAK,mBAAmB;AACxBA,oBAAAA,MAAY,MAAA,OAAA,mCAAA,UAAU,SAAS,MAAM,OAAO,SAAS,EAAE;AAGvD,WAAK,SAAS,aAAa,SAAS;AAGpC,WAAK,iBAAiB,SAAS,EAAE;AAGR,WAAK,kBAAkB,SAAS;AACzD,WAAK,oBAAoB;AACzB,WAAK,SAAS,cAAc;AAE5B,WAAK,sBAAsB,SAAS,EAAE;AAAA,IACtC;AAAA;AAAA,IAGD,2BAA2B;AAC1B,UAAI,KAAK,kBAAkB;AAC1BA,sBAAAA,MAAY,MAAA,OAAA,mCAAA,WAAW,KAAK,iBAAiB,MAAM,OAAO,KAAK,iBAAiB,EAAE;AAGlF,aAAK,cAAc;AACnB,aAAK,MAAM,iBAAiB;aACtB;AACNA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAAA,IACA;AAAA;AAAA,IAGD,uBAAuB;AACtB,UAAI,CAAC,KAAK,yBAAyB,KAAK,sBAAsB,WAAW,IAAI;AAC5E,aAAK,oBAAoB,CAAC,GAAG,KAAK,eAAe;AACjD;AAAA,MACD;AAEA,YAAM,UAAU,KAAK,sBAAsB,YAAa,EAAC,KAAI;AAC7D,WAAK,oBAAoB,KAAK,gBAAgB;AAAA,QAAO,UACpD,KAAK,KAAK,cAAc,SAAS,OAAO;AAAA;IAEzC;AAAA;AAAA,IAGD,sBAAsB;AACrB,WAAK,wBAAwB;AAE7B,WAAK,oBAAoB,CAAC,GAAG,KAAK,eAAe;AAAA,IACjD;AAAA;AAAA,IAGD,cAAc,UAAU;AACvB,YAAM,SAAS,KAAK,gBAAgB,KAAK,OAAK,EAAE,OAAO,QAAQ;AAC/D,aAAO,SAAS,OAAO,OAAO;AAAA,IAC9B;AAAA;AAAA,IAGD,yBAAyB,GAAG;AAC3B,WAAK,oBAAoB,SAAS,EAAE,OAAO,KAAK;AAEhD,UAAI,KAAK,sBAAsB,GAAG;AAEjC,aAAK,wBAAwB;AAAA,aACvB;AAEN,cAAM,iBAAiB,KAAK,gBAAgB,KAAK,oBAAoB,CAAC;AACtE,aAAK,wBAAwB,iBAAiB,eAAe,KAAK;AAAA,MACnE;AAAA,IACA;AAAA;AAAA,IAGD,mBAAmB,YAAY;AAC9B,cAAQ,YAAU;AAAA,QACjB,KAAK;AACJ,iBAAO;AAAA,QACR,KAAK;AACJ,iBAAO;AAAA,QACR,KAAK;AACJ,iBAAO;AAAA,QACR;AACC,iBAAO;AAAA,MACT;AAAA,IACA;AAAA;AAAA,IAGD,mBAAmB,YAAY;AAC9B,cAAQ,YAAU;AAAA,QACjB,KAAK;AACJ,iBAAO;AAAA,QACR,KAAK;AACJ,iBAAO;AAAA,QACR,KAAK;AACJ,iBAAO;AAAA,QACR;AACC,iBAAO;AAAA,MACT;AAAA,IACA;AAAA;AAAA,IAGD,qBAAqB;AACpB,UAAI,CAAC,KAAK,kBAAkB;AAC3BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAGA,WAAK,sBAAsB,CAAC,GAAG,KAAK,eAAe;AACnD,WAAK,cAAc;AACnB,WAAK,MAAM,eAAe;IAC1B;AAAA;AAAA,IAGD,qBAAqB;AACpBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAChB,iBAAK,cAAc;AACnB,iBAAK,MAAM,eAAe;AAE1B,iBAAK,sBAAsB,CAAC,GAAG,KAAK,eAAe;AAAA,UACpD;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,sBAAsB,QAAQ;AAC7B,YAAM,QAAQ,KAAK,oBAAoB,UAAU,OAAK,EAAE,OAAO,OAAO,EAAE;AACxE,UAAI,UAAU,IAAI;AACjB,aAAK,oBAAoB,KAAK,MAAM;AAAA,aAC9B;AACN,aAAK,oBAAoB,OAAO,OAAO,CAAC;AAAA,MACzC;AAAA,IACA;AAAA;AAAA,IAGD,yBAAyB;AACxB,UAAI,KAAK,oBAAoB,WAAW,GAAG;AAC1CA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAGA,WAAK,kBAAkB,CAAC,GAAG,KAAK,mBAAmB;AACnD,WAAK,SAAS,YAAY,KAAK,gBAAgB,IAAI,YAAU,OAAO,EAAE;AAGtE,WAAK,SAAS,aAAa;AAG3B,WAAK,gBAAgB,QAAQ,YAAU;AACtC,aAAK,wBAAwB,OAAO,EAAE;AAAA,MACvC,CAAC;AAED,WAAK,cAAc;AACnB,WAAK,MAAM,eAAe;AAE1BA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,MAAM,KAAK,gBAAgB,MAAM;AAAA,QACxC,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB,UAAU;AAC1B,aAAO,KAAK,oBAAoB,KAAK,YAAU,OAAO,OAAO,QAAQ;AAAA,IACrE;AAAA;AAAA,IAGD,oBAAoB;AAEnB,WAAK,wBAAwB,CAAC,GAAG,KAAK,iBAAiB;AACvD,WAAK,cAAc;AACnB,WAAK,MAAM,cAAc;IACzB;AAAA;AAAA,IAGD,oBAAoB;AAEnBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAChB,iBAAK,cAAc;AACnB,iBAAK,MAAM,cAAc;AAEzB,iBAAK,wBAAwB,CAAC,GAAG,KAAK,iBAAiB;AAAA,UACxD;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,qBAAqB,OAAO;AAC3B,YAAM,QAAQ,KAAK,sBAAsB,UAAU,OAAK,EAAE,OAAO,MAAM,EAAE;AACzE,UAAI,UAAU,IAAI;AACjB,aAAK,sBAAsB,KAAK,KAAK;AAAA,aAC/B;AACN,aAAK,sBAAsB,OAAO,OAAO,CAAC;AAAA,MAC3C;AAAA,IACA;AAAA;AAAA,IAGD,gBAAgB,SAAS;AACxB,aAAO,KAAK,sBAAsB,KAAK,WAAS,MAAM,OAAO,OAAO;AAAA,IACpE;AAAA;AAAA,IAGD,wBAAwB;AACvB,UAAI,KAAK,sBAAsB,WAAW,GAAG;AAC5CA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAGA,WAAK,oBAAoB,CAAC,GAAG,KAAK,qBAAqB;AACvD,WAAK,SAAS,cAAc,KAAK,kBAAkB,IAAI,WAAS,MAAM,EAAE;AAExE,WAAK,cAAc;AACnB,WAAK,MAAM,cAAc;AAEzBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,MAAM,KAAK,kBAAkB,MAAM;AAAA,QAC1C,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,wBAAwB;AACvBK,gBAAO,QAAC,oBAAoB,EAAE,EAC5B,KAAK,SAAO;AACZ,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AACjC,eAAK,oBAAoB,IAAI,KAAK,IAAI,WAAS;AAAA,YAC9C,OAAO,KAAK;AAAA,YACZ,OAAO,KAAK;AAAA,UACZ,EAAC;AAGF,cAAI,KAAK,kBAAkB,WAAW,GAAG;AACxC,iBAAK,oBAAoB;AAAA,cACxB,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,cAChC,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,cAChC,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,cAChC,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,cAChC,EAAE,OAAO,SAAS,OAAO,QAAQ;AAAA;UAEnC;AAGA,cAAI,KAAK,kBAAkB,SAAS,KAAK,CAAC,KAAK,SAAS,YAAY;AACnE,iBAAK,SAAS,aAAa,KAAK,kBAAkB,CAAC,EAAE;AACrD,iBAAK,kBAAkB;AAAA,UACxB;AAAA,QACD;AAAA,OACA,EACA,MAAM,SAAO;AACbL,sBAAA,MAAA,MAAA,SAAA,mCAAc,eAAe,GAAG;AAEhC,aAAK,oBAAoB;AAAA,UACxB,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,UAChC,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,UAChC,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,UAChC,EAAE,OAAO,QAAQ,OAAO,OAAQ;AAAA,UAChC,EAAE,OAAO,SAAS,OAAO,QAAQ;AAAA;AAGlC,YAAI,KAAK,kBAAkB,SAAS,KAAK,CAAC,KAAK,SAAS,YAAY;AACnE,eAAK,SAAS,aAAa,KAAK,kBAAkB,CAAC,EAAE;AACrD,eAAK,kBAAkB;AAAA,QACxB;AAAA,MACD,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,wBAAwB;AACvB,UAAI,KAAK,sBAAsB;AAE9B,aAAK,sBAAsB;aACrB;AAEN,aAAK,sBAAsB,CAAC,GAAG,KAAK,aAAa;AAAA,MAClD;AAAA,IACA;AAAA;AAAA,IAGD,sBAAsB;AACrB,YAAM,kBAAkB,CAAC,KAAK;AAG9B,WAAK,kBAAkB,QAAQ,cAAY;AAC1C,cAAM,cAAc,KAAK,kBAAkB,SAAS,QAAQ;AAC5D,YAAI,aAAa;AAChB,gBAAM,YAAY,YAAY,UAAU,UAAQ,KAAK,OAAO,SAAS,EAAE;AACvE,cAAI,cAAc,IAAI;AACrB,iBAAK,KAAK,YAAY,SAAS,GAAG,YAAY,eAAe;AAAA,UAC9D;AAAA,QACD;AAAA,MACD,CAAC;AAGD,YAAM,UAAU,kBACf,OAAO,KAAK,kBAAkB,MAAM,UACpC,SAAS,KAAK,kBAAkB,MAAM;AAEvCA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACX,CAAC;AAAA,IACD;AAAA,EACF;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7gDA,GAAG,WAAW,eAAe;"}