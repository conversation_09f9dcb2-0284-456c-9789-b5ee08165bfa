"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const _sfc_main = {
  data() {
    return {
      userInfo: {
        name: "",
        id: "",
        phone: "",
        email: "",
        role: "",
        department: ""
      }
    };
  },
  onLoad() {
    this.loadUserInfo();
  },
  onShow() {
    this.loadUserInfo();
  },
  methods: {
    // 加载用户信息
    loadUserInfo() {
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      common_vendor.index.getStorageSync("userId");
      const userRole = common_vendor.index.getStorageSync("userRole");
      if (userInfo) {
        common_vendor.index.__f__("log", "at pages/user/profile.vue:83", "本地存储的用户信息:", userInfo);
        const userData = userInfo.user;
        this.userInfo = {
          name: userData.name,
          phone: userData.phone || "",
          email: userData.email || "",
          role: this.getRoleName(userRole) || "普通用户",
          department: userData.department || "未分配"
        };
      } else {
        this.fetchUserInfoFromServer();
      }
    },
    // 从服务器获取用户信息
    fetchUserInfoFromServer() {
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      utils_api.userApi.getUserInfo().then((res) => {
        common_vendor.index.hideLoading();
        if (res.code === 200) {
          common_vendor.index.setStorageSync("userInfo", res.data);
          const userData = res.data.user || res.data;
          this.userInfo = {
            name: userData.name || userData.username || "用户",
            id: userData.id || res.data.userId || "USER-" + Date.now(),
            phone: userData.phone || "",
            email: userData.email || "",
            role: this.getRoleName(userData.role || res.data.role) || "普通用户",
            department: userData.department || "未分配"
          };
        } else {
          this.showError("获取用户信息失败");
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/user/profile.vue:130", "获取用户信息失败:", err);
        this.showError("网络错误，请稍后重试");
      });
    },
    // 角色名称转换
    getRoleName(role) {
      const roleMap = {
        "admin": "系统管理员",
        "manager": "主管",
        "engineer": "维修工程师",
        "operator": "操作员",
        "user": "普通用户"
      };
      return roleMap[role] || role || "普通用户";
    },
    // 保存用户资料
    saveProfile() {
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      if (!this.userInfo.name || this.userInfo.name.trim() === "") {
        common_vendor.index.hideLoading();
        this.showError("姓名不能为空");
        return;
      }
      if (this.userInfo.phone && !/^1\d{10}$/.test(this.userInfo.phone)) {
        common_vendor.index.hideLoading();
        this.showError("请输入正确的手机号码");
        return;
      }
      if (this.userInfo.email && !/^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.test(this.userInfo.email)) {
        common_vendor.index.hideLoading();
        this.showError("请输入正确的邮箱地址");
        return;
      }
      const updateData = {
        name: this.userInfo.name,
        phone: this.userInfo.phone,
        email: this.userInfo.email
      };
      utils_api.userApi.updateUserInfo(updateData).then((res) => {
        common_vendor.index.hideLoading();
        if (res.code === 200) {
          const userInfo = common_vendor.index.getStorageSync("userInfo") || {};
          if (userInfo.user) {
            userInfo.user.name = this.userInfo.name;
            userInfo.user.phone = this.userInfo.phone;
            userInfo.user.email = this.userInfo.email;
          } else {
            userInfo.name = this.userInfo.name;
            userInfo.phone = this.userInfo.phone;
            userInfo.email = this.userInfo.email;
          }
          common_vendor.index.setStorageSync("userInfo", userInfo);
          common_vendor.index.showToast({
            title: "保存成功",
            icon: "success"
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1500);
        } else {
          this.showError(res.message || "保存失败");
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/user/profile.vue:215", "保存用户资料失败:", err);
        this.showError("网络错误，请稍后重试");
      });
    },
    // 显示错误提示
    showError(message) {
      common_vendor.index.showToast({
        title: message,
        icon: "none"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $data.userInfo.name,
    b: common_vendor.o(($event) => $data.userInfo.name = $event.detail.value),
    c: $data.userInfo.phone,
    d: common_vendor.o(($event) => $data.userInfo.phone = $event.detail.value),
    e: $data.userInfo.email,
    f: common_vendor.o(($event) => $data.userInfo.email = $event.detail.value),
    g: common_vendor.t($data.userInfo.department),
    h: common_vendor.o((...args) => $options.saveProfile && $options.saveProfile(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/user/profile.js.map
