{"version": 3, "file": "upload.js", "sources": ["utils/upload.js"], "sourcesContent": ["/**\r\n * 文件上传工具类\r\n */\r\nimport { getToken } from './auth.js';\r\n\r\n// 直接使用后端API地址，不通过Vite代理\r\nconst BASE_URL = 'http://43.139.65.175:8889';\r\n//const BASE_URL = 'http://192.168.0.155:8889';\r\n// 修改上传URL，添加/api前缀与后端控制器路径匹配\r\nconst UPLOAD_IMAGE_URL = BASE_URL + '/api/upload/uploadImage/app';\r\nconst UPLOAD_VIDEO_URL = BASE_URL + '/api/upload/uploadVideo/app';\r\n\r\nexport default {\r\n  /**\r\n   * 上传图片到服务器\r\n   * @param {String} filePath - 本地图片路径\r\n   * @returns {Promise} - 返回上传结果，包含服务器文件路径\r\n   */\r\n  uploadImage(filePath) {\r\n    return new Promise((resolve, reject) => {\r\n      console.log('开始上传图片:', filePath);\r\n      console.log('上传URL:', UPLOAD_IMAGE_URL);\r\n      \r\n      // 获取token并添加Bearer前缀\r\n      let token = getToken();\r\n      if (token && !token.startsWith('Bearer ')) {\r\n        token = 'Bearer ' + token;\r\n      }\r\n      \r\n      uni.uploadFile({\r\n        url: UPLOAD_IMAGE_URL,\r\n        filePath: filePath,\r\n        name: 'file',\r\n        header: {\r\n          'Authorization': token\r\n        },\r\n        success: (res) => {\r\n          console.log('图片上传响应:', res);\r\n          try {\r\n            // 服务器返回的数据是字符串，需要转换为对象\r\n            const data = JSON.parse(res.data);\r\n            if (data.code === 200) {\r\n              // 返回服务器文件路径\r\n              //console.log('图片上传成功, 服务器路径:', data.data);\r\n              resolve(data.data);\r\n            } else {\r\n              //console.error('图片上传失败, 错误信息:', data.message);\r\n              reject(new Error(data.message || '上传图片失败'));\r\n            }\r\n          } catch (e) {\r\n            //console.error('解析上传响应失败:', e, '原始响应:', res.data);\r\n            reject(new Error('解析上传响应失败'));\r\n          }\r\n        },\r\n        fail: (err) => {\r\n          console.error('图片上传请求失败:', err);\r\n          reject(new Error('上传图片失败: ' + JSON.stringify(err)));\r\n        }\r\n      });\r\n    });\r\n  },\r\n\r\n  /**\r\n   * 上传视频到服务器\r\n   * @param {String} filePath - 本地视频路径\r\n   * @returns {Promise} - 返回上传结果，包含服务器文件路径\r\n   */\r\n  uploadVideo(filePath) {\r\n    return new Promise((resolve, reject) => {\r\n      console.log('开始上传视频:', filePath);\r\n      console.log('上传URL:', UPLOAD_VIDEO_URL);\r\n      \r\n      // 获取token并添加Bearer前缀\r\n      let token = getToken();\r\n      if (token && !token.startsWith('Bearer ')) {\r\n        token = 'Bearer ' + token;\r\n      }\r\n      \r\n      uni.uploadFile({\r\n        url: UPLOAD_VIDEO_URL,\r\n        filePath: filePath,\r\n        name: 'file',\r\n        header: {\r\n          'Authorization': token\r\n        },\r\n        success: (res) => {\r\n          console.log('视频上传响应:', res);\r\n          try {\r\n            const data = JSON.parse(res.data);\r\n            if (data.code === 200) {\r\n              // 返回服务器文件路径\r\n              console.log('视频上传成功, 服务器路径:', data.data);\r\n              resolve(data.data);\r\n            } else {\r\n              console.error('视频上传失败, 错误信息:', data.message);\r\n              reject(new Error(data.message || '上传视频失败'));\r\n            }\r\n          } catch (e) {\r\n            console.error('解析上传响应失败:', e, '原始响应:', res.data);\r\n            reject(new Error('解析上传响应失败'));\r\n          }\r\n        },\r\n        fail: (err) => {\r\n          console.error('视频上传请求失败:', err);\r\n          reject(new Error('上传视频失败: ' + JSON.stringify(err)));\r\n        }\r\n      });\r\n    });\r\n  },\r\n\r\n  /**\r\n   * 获取完整的文件访问URL\r\n   * @param {String} filePath - 服务器返回的文件路径\r\n   * @returns {String} - 完整的文件访问URL\r\n   */\r\n  getFileUrl(filePath) {\r\n    if (!filePath) return '';\r\n    filePath='/uploads'+filePath;\r\n    console.log('原始文件路径:', filePath);\r\n    \r\n    // 处理blob URL（本地预览URL）\r\n    if (filePath.startsWith('blob:')) {\r\n     // console.log('Blob URL，直接返回:', filePath);\r\n      return filePath;\r\n    }\r\n    \r\n    // 如果已经是完整URL，直接返回\r\n    if (filePath.startsWith('http')) {\r\n    //  console.log('已是完整URL，直接返回:', filePath);\r\n      return filePath;\r\n    }\r\n    \r\n    // 视频路径特殊处理 - 修复可能的中文编码问题\r\n    if (filePath.includes('/uploads/videos/') || filePath.endsWith('.mp4')) {\r\n      // 确保路径使用正确的编码\r\n      let pathParts = filePath.split('/');\r\n      let encodedParts = pathParts.map(part => {\r\n        // 只对文件名进行URL编码，避免对路径分隔符编码\r\n        if (part.includes('.mp4')) {\r\n          return encodeURIComponent(part);\r\n        }\r\n        return part;\r\n      });\r\n      let encodedPath = encodedParts.join('/');\r\n      \r\n      // 构建完整URL\r\n      let fullUrl;\r\n      if (encodedPath.startsWith('/')) {\r\n        fullUrl = BASE_URL + encodedPath;\r\n      } else {\r\n        fullUrl = BASE_URL + '/' + encodedPath;\r\n      }\r\n      \r\n     // console.log('视频完整URL(带编码):', fullUrl);\r\n      return fullUrl;\r\n    }\r\n    \r\n    // 处理相对路径，构建完整URL\r\n    // 注意：后端返回的路径可能是 /uploads/xxx 或 uploads/xxx\r\n    let fullUrl;\r\n    if (filePath.startsWith('/')) {\r\n      // 以/开头的路径，直接拼接\r\n      fullUrl = BASE_URL + filePath;\r\n    } else {\r\n      // 不以/开头的路径，添加/后拼接\r\n      fullUrl = BASE_URL + '/' + filePath;\r\n    }\r\n    \r\n   // console.log('生成的完整URL:', fullUrl);\r\n    return fullUrl;\r\n  }\r\n} "], "names": ["uni", "getToken", "fullUrl"], "mappings": ";;;AAMA,MAAM,WAAW;AAGjB,MAAM,mBAAmB,WAAW;AACpC,MAAM,mBAAmB,WAAW;AAEpC,MAAe,cAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMb,YAAY,UAAU;AACpB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,oBAAY,MAAA,MAAA,OAAA,yBAAA,WAAW,QAAQ;AAC/BA,oBAAY,MAAA,MAAA,OAAA,yBAAA,UAAU,gBAAgB;AAGtC,UAAI,QAAQC,WAAAA;AACZ,UAAI,SAAS,CAAC,MAAM,WAAW,SAAS,GAAG;AACzC,gBAAQ,YAAY;AAAA,MACrB;AAEDD,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,QACL;AAAA,QACA,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,iBAAiB;AAAA,QAClB;AAAA,QACD,SAAS,CAAC,QAAQ;AAChBA,wBAAY,MAAA,MAAA,OAAA,yBAAA,WAAW,GAAG;AAC1B,cAAI;AAEF,kBAAM,OAAO,KAAK,MAAM,IAAI,IAAI;AAChC,gBAAI,KAAK,SAAS,KAAK;AAGrB,sBAAQ,KAAK,IAAI;AAAA,YAC/B,OAAmB;AAEL,qBAAO,IAAI,MAAM,KAAK,WAAW,QAAQ,CAAC;AAAA,YAC3C;AAAA,UACF,SAAQ,GAAG;AAEV,mBAAO,IAAI,MAAM,UAAU,CAAC;AAAA,UAC7B;AAAA,QACF;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAA,MAAA,MAAA,SAAA,yBAAc,aAAa,GAAG;AAC9B,iBAAO,IAAI,MAAM,aAAa,KAAK,UAAU,GAAG,CAAC,CAAC;AAAA,QACnD;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,YAAY,UAAU;AACpB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,oBAAY,MAAA,MAAA,OAAA,yBAAA,WAAW,QAAQ;AAC/BA,oBAAY,MAAA,MAAA,OAAA,yBAAA,UAAU,gBAAgB;AAGtC,UAAI,QAAQC,WAAAA;AACZ,UAAI,SAAS,CAAC,MAAM,WAAW,SAAS,GAAG;AACzC,gBAAQ,YAAY;AAAA,MACrB;AAEDD,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,QACL;AAAA,QACA,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,iBAAiB;AAAA,QAClB;AAAA,QACD,SAAS,CAAC,QAAQ;AAChBA,wBAAY,MAAA,MAAA,OAAA,yBAAA,WAAW,GAAG;AAC1B,cAAI;AACF,kBAAM,OAAO,KAAK,MAAM,IAAI,IAAI;AAChC,gBAAI,KAAK,SAAS,KAAK;AAErBA,4BAAY,MAAA,MAAA,OAAA,yBAAA,kBAAkB,KAAK,IAAI;AACvC,sBAAQ,KAAK,IAAI;AAAA,YAC/B,OAAmB;AACLA,0EAAc,iBAAiB,KAAK,OAAO;AAC3C,qBAAO,IAAI,MAAM,KAAK,WAAW,QAAQ,CAAC;AAAA,YAC3C;AAAA,UACF,SAAQ,GAAG;AACVA,gCAAc,MAAA,SAAA,yBAAA,aAAa,GAAG,SAAS,IAAI,IAAI;AAC/C,mBAAO,IAAI,MAAM,UAAU,CAAC;AAAA,UAC7B;AAAA,QACF;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAA,MAAA,MAAA,SAAA,0BAAc,aAAa,GAAG;AAC9B,iBAAO,IAAI,MAAM,aAAa,KAAK,UAAU,GAAG,CAAC,CAAC;AAAA,QACnD;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,WAAW,UAAU;AACnB,QAAI,CAAC;AAAU,aAAO;AACtB,eAAS,aAAW;AACpBA,kBAAY,MAAA,MAAA,OAAA,0BAAA,WAAW,QAAQ;AAG/B,QAAI,SAAS,WAAW,OAAO,GAAG;AAEhC,aAAO;AAAA,IACR;AAGD,QAAI,SAAS,WAAW,MAAM,GAAG;AAE/B,aAAO;AAAA,IACR;AAGD,QAAI,SAAS,SAAS,kBAAkB,KAAK,SAAS,SAAS,MAAM,GAAG;AAEtE,UAAI,YAAY,SAAS,MAAM,GAAG;AAClC,UAAI,eAAe,UAAU,IAAI,UAAQ;AAEvC,YAAI,KAAK,SAAS,MAAM,GAAG;AACzB,iBAAO,mBAAmB,IAAI;AAAA,QAC/B;AACD,eAAO;AAAA,MACf,CAAO;AACD,UAAI,cAAc,aAAa,KAAK,GAAG;AAGvC,UAAIE;AACJ,UAAI,YAAY,WAAW,GAAG,GAAG;AAC/B,QAAAA,WAAU,WAAW;AAAA,MAC7B,OAAa;AACL,QAAAA,WAAU,WAAW,MAAM;AAAA,MAC5B;AAGD,aAAOA;AAAA,IACR;AAID,QAAI;AACJ,QAAI,SAAS,WAAW,GAAG,GAAG;AAE5B,gBAAU,WAAW;AAAA,IAC3B,OAAW;AAEL,gBAAU,WAAW,MAAM;AAAA,IAC5B;AAGD,WAAO;AAAA,EACR;AACH;;"}