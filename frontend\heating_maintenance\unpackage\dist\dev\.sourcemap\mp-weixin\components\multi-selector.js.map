{"version": 3, "file": "multi-selector.js", "sources": ["components/multi-selector.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/Y29tcG9uZW50cy9tdWx0aS1zZWxlY3Rvci52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- 页面头部 -->\r\n    <view class=\"header\">\r\n      <view class=\"header-left\" @click=\"cancel\">\r\n        <text class=\"header-icon\">×</text>\r\n        <text class=\"header-cancel\">取消</text>\r\n      </view>\r\n      <text class=\"header-title\">{{ title }}</text>\r\n      <view class=\"header-right\" @click=\"confirm\">\r\n        <text class=\"header-confirm\">确定</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 搜索框 -->\r\n    <view class=\"search-box\">\r\n      <view class=\"search-input-wrapper\">\r\n        <text class=\"search-icon\">🔍</text>\r\n        <input \r\n          type=\"text\" \r\n          placeholder=\"搜索\" \r\n          class=\"search-input\"\r\n          v-model=\"searchKeyword\"\r\n          @input=\"handleSearch\"\r\n        />\r\n        <text \r\n          class=\"clear-icon\" \r\n          v-if=\"searchKeyword\" \r\n          @click=\"clearSearch\"\r\n        >×</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 已选择项 -->\r\n    <view class=\"selected-area\" v-if=\"selectedItems.length > 0\">\r\n      <view class=\"selected-header\">\r\n        <text class=\"selected-title\">已选择 ({{ selectedItems.length }})</text>\r\n        <text class=\"selected-clear\" @click=\"clearSelection\">清空</text>\r\n      </view>\r\n      <scroll-view class=\"selected-list\" scroll-x>\r\n        <view \r\n          class=\"selected-item\" \r\n          v-for=\"item in selectedItems\" \r\n          :key=\"item[idField]\"\r\n          @click=\"toggleItem(item)\"\r\n        >\r\n          <text class=\"selected-item-name\">{{ item[nameField] }}</text>\r\n          <text class=\"selected-item-remove\">×</text>\r\n        </view>\r\n      </scroll-view>\r\n    </view>\r\n    \r\n    <!-- 列表区域 -->\r\n    <scroll-view \r\n      class=\"items-list\" \r\n      scroll-y \r\n      :style=\"{ height: listHeight }\"\r\n    >\r\n      <view \r\n        class=\"list-item\" \r\n        v-for=\"item in filteredItems\" \r\n        :key=\"item[idField]\"\r\n        @click=\"toggleItem(item)\"\r\n      >\r\n        <view class=\"item-content\">\r\n          <text class=\"item-name\">{{ item[nameField] }}</text>\r\n          <view \r\n            class=\"checkbox\" \r\n            :class=\"{ checked: isItemSelected(item) }\"\r\n          >\r\n            <text class=\"checkbox-inner\" v-if=\"isItemSelected(item)\">✓</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 空列表提示 -->\r\n      <view class=\"empty-tip\" v-if=\"filteredItems.length === 0\">\r\n        <text class=\"empty-text\">{{ searchKeyword ? '未找到匹配的结果' : '暂无可选项' }}</text>\r\n      </view>\r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 标题\r\n      title: '多选',\r\n      // 数据列表\r\n      items: [],\r\n      // 已选择的项\r\n      selectedIds: [],\r\n      // 名称字段\r\n      nameField: 'name',\r\n      // ID字段\r\n      idField: 'id',\r\n      // 搜索关键词\r\n      searchKeyword: '',\r\n      // 列表高度\r\n      listHeight: '70vh'\r\n    };\r\n  },\r\n  computed: {\r\n    // 过滤后的列表\r\n    filteredItems() {\r\n      if (!this.searchKeyword) {\r\n        return this.items;\r\n      }\r\n      \r\n      const keyword = this.searchKeyword.toLowerCase();\r\n      return this.items.filter(item => {\r\n        const name = item[this.nameField].toLowerCase();\r\n        return name.includes(keyword);\r\n      });\r\n    },\r\n    \r\n    // 已选择的项目\r\n    selectedItems() {\r\n      return this.items.filter(item => this.selectedIds.includes(item[this.idField]));\r\n    }\r\n  },\r\n  onLoad() {\r\n    // 获取传入的数据\r\n    const eventChannel = this.getOpenerEventChannel();\r\n    if (eventChannel) {\r\n      eventChannel.on('initData', (data) => {\r\n        if (data) {\r\n          this.title = data.title || '多选';\r\n          this.items = data.items || [];\r\n          this.selectedIds = data.selectedIds || [];\r\n          this.nameField = data.nameField || 'name';\r\n          this.idField = data.idField || 'id';\r\n        }\r\n      });\r\n    }\r\n    \r\n    // 根据屏幕高度计算列表高度\r\n    const systemInfo = uni.getSystemInfoSync();\r\n    const windowHeight = systemInfo.windowHeight;\r\n    const headerHeight = 100; // 头部和搜索框的大致高度 (单位: rpx)\r\n    const selectedAreaHeight = 160; // 已选择区域的大致高度 (单位: rpx)\r\n    \r\n    // 转换为 px\r\n    const headerPx = headerHeight / 750 * systemInfo.windowWidth;\r\n    const selectedPx = selectedAreaHeight / 750 * systemInfo.windowWidth;\r\n    \r\n    // 计算列表高度\r\n    this.listHeight = `${windowHeight - headerPx - selectedPx}px`;\r\n  },\r\n  methods: {\r\n    // 检查项目是否已选择\r\n    isItemSelected(item) {\r\n      return this.selectedIds.includes(item[this.idField]);\r\n    },\r\n    \r\n    // 切换项目选择状态\r\n    toggleItem(item) {\r\n      const id = item[this.idField];\r\n      const index = this.selectedIds.indexOf(id);\r\n      \r\n      if (index > -1) {\r\n        // 移除选择\r\n        this.selectedIds.splice(index, 1);\r\n      } else {\r\n        // 添加选择\r\n        this.selectedIds.push(id);\r\n      }\r\n    },\r\n    \r\n    // 处理搜索\r\n    handleSearch() {\r\n      // 防抖处理可以在这里添加\r\n    },\r\n    \r\n    // 清除搜索\r\n    clearSearch() {\r\n      this.searchKeyword = '';\r\n    },\r\n    \r\n    // 清空选择\r\n    clearSelection() {\r\n      this.selectedIds = [];\r\n    },\r\n    \r\n    // 取消操作\r\n    cancel() {\r\n      uni.navigateBack();\r\n    },\r\n    \r\n    // 确认选择\r\n    confirm() {\r\n      // 获取事件通道\r\n      const eventChannel = this.getOpenerEventChannel();\r\n      \r\n      // 传递选择结果\r\n      if (eventChannel) {\r\n        eventChannel.emit('selectResult', {\r\n          selectedItems: this.selectedItems,\r\n          selectedIds: this.selectedIds\r\n        });\r\n      }\r\n      \r\n      // 返回上一页\r\n      uni.navigateBack();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20rpx 30rpx;\r\n  background-color: #fff;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.header-left,\r\n.header-right {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.header-icon {\r\n  font-size: 40rpx;\r\n  color: #666;\r\n  margin-right: 6rpx;\r\n}\r\n\r\n.header-cancel {\r\n  font-size: 30rpx;\r\n  color: #666;\r\n}\r\n\r\n.header-title {\r\n  font-size: 34rpx;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n.header-confirm {\r\n  font-size: 30rpx;\r\n  color: #1890ff;\r\n  font-weight: 500;\r\n}\r\n\r\n.search-box {\r\n  padding: 20rpx 30rpx;\r\n  background-color: #fff;\r\n}\r\n\r\n.search-input-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  background-color: #f0f0f0;\r\n  border-radius: 30rpx;\r\n  padding: 10rpx 20rpx;\r\n}\r\n\r\n.search-icon {\r\n  font-size: 30rpx;\r\n  color: #999;\r\n  margin-right: 10rpx;\r\n}\r\n\r\n.search-input {\r\n  flex: 1;\r\n  height: 60rpx;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.clear-icon {\r\n  font-size: 36rpx;\r\n  color: #999;\r\n  padding: 0 6rpx;\r\n}\r\n\r\n.selected-area {\r\n  background-color: #fff;\r\n  padding: 20rpx 30rpx;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.selected-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 16rpx;\r\n}\r\n\r\n.selected-title {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  font-weight: 500;\r\n}\r\n\r\n.selected-clear {\r\n  font-size: 28rpx;\r\n  color: #1890ff;\r\n}\r\n\r\n.selected-list {\r\n  white-space: nowrap;\r\n  padding-bottom: 10rpx;\r\n}\r\n\r\n.selected-item {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  background-color: rgba(24, 144, 255, 0.1);\r\n  border-radius: 30rpx;\r\n  padding: 10rpx 20rpx;\r\n  margin-right: 16rpx;\r\n}\r\n\r\n.selected-item-name {\r\n  font-size: 26rpx;\r\n  color: #1890ff;\r\n  margin-right: 6rpx;\r\n}\r\n\r\n.selected-item-remove {\r\n  font-size: 28rpx;\r\n  color: #1890ff;\r\n}\r\n\r\n.items-list {\r\n  flex: 1;\r\n  background-color: #fff;\r\n}\r\n\r\n.list-item {\r\n  padding: 20rpx 30rpx;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.item-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.item-name {\r\n  font-size: 30rpx;\r\n  color: #333;\r\n}\r\n\r\n.checkbox {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  border-radius: 50%;\r\n  border: 2rpx solid #ddd;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.checkbox.checked {\r\n  background-color: #1890ff;\r\n  border-color: #1890ff;\r\n}\r\n\r\n.checkbox-inner {\r\n  color: #fff;\r\n  font-size: 24rpx;\r\n}\r\n\r\n.empty-tip {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 60rpx 0;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n}\r\n</style> ", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/components/multi-selector.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAoFA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA;AAAA,MAEL,OAAO;AAAA;AAAA,MAEP,OAAO,CAAE;AAAA;AAAA,MAET,aAAa,CAAE;AAAA;AAAA,MAEf,WAAW;AAAA;AAAA,MAEX,SAAS;AAAA;AAAA,MAET,eAAe;AAAA;AAAA,MAEf,YAAY;AAAA;EAEf;AAAA,EACD,UAAU;AAAA;AAAA,IAER,gBAAgB;AACd,UAAI,CAAC,KAAK,eAAe;AACvB,eAAO,KAAK;AAAA,MACd;AAEA,YAAM,UAAU,KAAK,cAAc,YAAW;AAC9C,aAAO,KAAK,MAAM,OAAO,UAAQ;AAC/B,cAAM,OAAO,KAAK,KAAK,SAAS,EAAE,YAAW;AAC7C,eAAO,KAAK,SAAS,OAAO;AAAA,MAC9B,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB;AACd,aAAO,KAAK,MAAM,OAAO,UAAQ,KAAK,YAAY,SAAS,KAAK,KAAK,OAAO,CAAC,CAAC;AAAA,IAChF;AAAA,EACD;AAAA,EACD,SAAS;AAEP,UAAM,eAAe,KAAK;AAC1B,QAAI,cAAc;AAChB,mBAAa,GAAG,YAAY,CAAC,SAAS;AACpC,YAAI,MAAM;AACR,eAAK,QAAQ,KAAK,SAAS;AAC3B,eAAK,QAAQ,KAAK,SAAS,CAAA;AAC3B,eAAK,cAAc,KAAK,eAAe,CAAA;AACvC,eAAK,YAAY,KAAK,aAAa;AACnC,eAAK,UAAU,KAAK,WAAW;AAAA,QACjC;AAAA,MACF,CAAC;AAAA,IACH;AAGA,UAAM,aAAaA,oBAAI;AACvB,UAAM,eAAe,WAAW;AAChC,UAAM,eAAe;AACrB,UAAM,qBAAqB;AAG3B,UAAM,WAAW,eAAe,MAAM,WAAW;AACjD,UAAM,aAAa,qBAAqB,MAAM,WAAW;AAGzD,SAAK,aAAa,GAAG,eAAe,WAAW,UAAU;AAAA,EAC1D;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,eAAe,MAAM;AACnB,aAAO,KAAK,YAAY,SAAS,KAAK,KAAK,OAAO,CAAC;AAAA,IACpD;AAAA;AAAA,IAGD,WAAW,MAAM;AACf,YAAM,KAAK,KAAK,KAAK,OAAO;AAC5B,YAAM,QAAQ,KAAK,YAAY,QAAQ,EAAE;AAEzC,UAAI,QAAQ,IAAI;AAEd,aAAK,YAAY,OAAO,OAAO,CAAC;AAAA,aAC3B;AAEL,aAAK,YAAY,KAAK,EAAE;AAAA,MAC1B;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AAAA,IAEd;AAAA;AAAA,IAGD,cAAc;AACZ,WAAK,gBAAgB;AAAA,IACtB;AAAA;AAAA,IAGD,iBAAiB;AACf,WAAK,cAAc;IACpB;AAAA;AAAA,IAGD,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA;AAAA,IAGD,UAAU;AAER,YAAM,eAAe,KAAK;AAG1B,UAAI,cAAc;AAChB,qBAAa,KAAK,gBAAgB;AAAA,UAChC,eAAe,KAAK;AAAA,UACpB,aAAa,KAAK;AAAA,QACpB,CAAC;AAAA,MACH;AAGAA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9MA,GAAG,WAAW,eAAe;"}