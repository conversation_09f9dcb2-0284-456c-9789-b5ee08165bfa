{"version": 3, "file": "BaseTabBar.js", "sources": ["components/BaseTabBar.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RTovdGFpYm9fY29tcGFueS90Yl9wcm9qZWN0L3NoYWFueGlfamllbWluZ19uZXdfZW5lcmd5X2NvbXBhbnkvNC1Tb3VyY2UvYXBwL2Zyb250ZW5kL2hlYXRpbmdfbWFpbnRlbmFuY2UvY29tcG9uZW50cy9CYXNlVGFiQmFyLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view>\r\n    <slot></slot>\r\n    <CustomTabBar v-if=\"showTabBar\"></CustomTabBar>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport CustomTabBar from \"@/components/CustomTabBar.vue\";\r\n\r\nexport default {\r\n  name: \"BaseTabBar\",\r\n  components: {\r\n    CustomTabBar,\r\n  },\r\n  data() {\r\n    return {\r\n      showTabBar: true,\r\n      tabBarPages: [\r\n        \"pages/home/<USER>\",\r\n        \"pages/hes/list\",\r\n        \"pages/message/center\",\r\n        \"pages/user/info\",\r\n      ],\r\n    };\r\n  },\r\n  onShow() {\r\n    const pages = getCurrentPages();\r\n    if (pages.length > 0) {\r\n      const currentPage = pages[pages.length - 1];\r\n      const currentRoute = currentPage.route;\r\n      this.showTabBar = this.tabBarPages.includes(currentRoute);\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style></style>\r\n", "import Component from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/components/BaseTabBar.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;AAQA,qBAAqB,MAAW;AAEhC,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,aAAa;AAAA,QACX;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA;EAEJ;AAAA,EACD,SAAS;AACP,UAAM,QAAQ;AACd,QAAI,MAAM,SAAS,GAAG;AACpB,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,eAAe,YAAY;AACjC,WAAK,aAAa,KAAK,YAAY,SAAS,YAAY;AAAA,IAC1D;AAAA,EACD;AACH;;;;;;;;;;;ACjCA,GAAG,gBAAgB,SAAS;"}