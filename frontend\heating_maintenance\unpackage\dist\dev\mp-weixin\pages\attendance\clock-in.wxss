/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.clock-in-container {
  padding: 30rpx;
  background-color: #f5f7fa;
  min-height: 100vh;
}
.status-header {
  margin-bottom: 40rpx;
}
.status-header .date-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}
.status-header .date-info .date {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}
.status-header .date-info .time {
  font-size: 60rpx;
  font-weight: bold;
  color: #333;
}
.status-header .admin-settings {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx;
  border-radius: 10rpx;
}
.status-header .admin-settings .settings-icon {
  font-size: 40rpx;
  margin-bottom: 5rpx;
}
.status-header .admin-settings .settings-text {
  font-size: 24rpx;
  color: #333;
}
.clock-status {
  margin-bottom: 40rpx;
}
.clock-status .status-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.clock-status .status-card .work-time {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.clock-status .status-card .work-time .time-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.clock-status .status-card .work-time .time-item .time-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.clock-status .status-card .work-time .time-item .time-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.clock-status .status-card .work-time .time-divider {
  width: 2rpx;
  height: 80rpx;
  background-color: #eee;
}
.clock-status .status-card .clock-records {
  display: flex;
  border-top: 2rpx solid #f5f5f5;
  padding-top: 30rpx;
}
.clock-status .status-card .clock-records .record-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.clock-status .status-card .clock-records .record-item .record-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.clock-status .status-card .clock-records .record-item .record-value {
  font-size: 32rpx;
  color: #52c41a;
  font-weight: bold;
}
.clock-status .status-card .clock-records .record-item .record-value.not-clocked {
  color: #999;
}
.clock-action {
  margin-bottom: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.clock-action .clock-circle {
  width: 300rpx;
  height: 300rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #4483e5, #6a9eef);
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 10rpx 30rpx rgba(106, 158, 239, 0.3);
  margin-bottom: 30rpx;
  transition: all 0.3s ease;
}
.clock-action .clock-circle .circle-inner {
  width: 260rpx;
  height: 260rpx;
  border-radius: 50%;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
}
.clock-action .clock-circle .clock-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #4483e5;
}
.clock-action .action-desc {
  font-size: 28rpx;
  color: #666;
  text-align: center;
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}
.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 8rpx;
  width: 8rpx;
  height: 32rpx;
  background-color: #1890ff;
  border-radius: 4rpx;
}
.recent-records .record-list {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.recent-records .record-list .record-empty {
  padding: 40rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}
.recent-records .record-list .record-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f5f5f5;
}
.recent-records .record-list .record-item:last-child {
  border-bottom: none;
}
.recent-records .record-list .record-item .record-date {
  width: 180rpx;
  display: flex;
  flex-direction: column;
}
.recent-records .record-list .record-item .record-date .date {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
}
.recent-records .record-list .record-item .record-date .week {
  font-size: 24rpx;
  color: #999;
}
.recent-records .record-list .record-item .record-details {
  flex: 1;
}
.recent-records .record-list .record-item .record-details .detail-item {
  display: flex;
  margin-bottom: 10rpx;
}
.recent-records .record-list .record-item .record-details .detail-item:last-child {
  margin-bottom: 0;
}
.recent-records .record-list .record-item .record-details .detail-item .detail-label {
  width: 80rpx;
  font-size: 28rpx;
  color: #666;
}
.recent-records .record-list .record-item .record-details .detail-item .detail-value {
  flex: 1;
  font-size: 28rpx;
  color: #52c41a;
}
.recent-records .record-list .record-item .record-details .detail-item .detail-value.abnormal {
  color: #faad14;
}
.recent-records .record-list .record-item .record-details .detail-item .detail-value .status-tag {
  display: inline-block;
  font-size: 22rpx;
  padding: 2rpx 10rpx;
  border-radius: 4rpx;
  background-color: rgba(250, 173, 20, 0.1);
  color: #faad14;
  margin-left: 10rpx;
}
.face-verify-popup {
  width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}
.face-verify-popup .popup-title {
  font-size: 32rpx;
  font-weight: bold;
  padding: 30rpx;
  text-align: center;
  border-bottom: 1rpx solid #eee;
}
.face-verify-popup .popup-content {
  padding: 30rpx;
}
.face-verify-popup .camera-container, .face-verify-popup .photo-preview {
  width: 400rpx;
  height: 400rpx;
  margin: 0 auto 30rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  overflow: hidden;
}
.face-verify-popup .popup-footer {
  display: flex;
  justify-content: space-between;
  padding-top: 20rpx;
}
.face-verify-popup .popup-footer button {
  flex: 1;
  margin: 0 20rpx;
  font-size: 28rpx;
  border-radius: 40rpx;
}
.face-verify-popup .popup-footer button.btn-cancel {
  background-color: #f5f5f5;
  color: #333;
}
.face-verify-popup .popup-footer button.btn-confirm {
  background-color: #007AFF;
  color: #fff;
}
.supplement-popup {
  width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}
.supplement-popup .popup-title {
  font-size: 32rpx;
  font-weight: bold;
  padding: 30rpx;
  text-align: center;
  border-bottom: 1rpx solid #eee;
}
.supplement-popup .popup-content {
  padding: 30rpx;
}
.supplement-popup .form-item {
  margin-bottom: 20rpx;
}
.supplement-popup .form-item .form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}
.supplement-popup .form-item .form-input {
  background-color: #f5f5f5;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
}
.supplement-popup .form-item .form-input textarea {
  width: 100%;
  height: 150rpx;
}
.supplement-popup .popup-footer {
  display: flex;
  justify-content: space-between;
  padding-top: 20rpx;
}
.supplement-popup .popup-footer button {
  flex: 1;
  margin: 0 20rpx;
  font-size: 28rpx;
  border-radius: 40rpx;
}
.supplement-popup .popup-footer button.btn-cancel {
  background-color: #f5f5f5;
  color: #333;
}
.supplement-popup .popup-footer button.btn-confirm {
  background-color: #007AFF;
  color: #fff;
}
.supplement-images {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10rpx;
}
.supplement-images .image-item {
  position: relative;
  width: 150rpx;
  height: 150rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  overflow: hidden;
}
.supplement-images .image-item .supplement-image {
  width: 100%;
  height: 100%;
}
.supplement-images .image-item .image-delete {
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.supplement-images .image-add {
  width: 150rpx;
  height: 150rpx;
  background-color: #f5f5f5;
  border: 1rpx dashed #ccc;
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.supplement-images .image-add .image-add-icon {
  font-size: 48rpx;
  color: #999;
  line-height: 1;
  margin-bottom: 10rpx;
}
.supplement-images .image-add .image-add-text {
  font-size: 24rpx;
  color: #999;
}
.clock-btn {
  margin-top: 40rpx;
  width: 90%;
  height: 90rpx;
  background: linear-gradient(135deg, #4B79A1, #283E51);
  color: white;
  font-size: 32rpx;
  border-radius: 45rpx;
  box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}
.clock-btn.disabled {
  background: linear-gradient(135deg, #ccc, #999);
  box-shadow: none;
}
.date-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.date-picker .icon-calendar {
  font-size: 32rpx;
  color: #999;
}