"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const utils_permissionMixin = require("../../utils/permission-mixin.js");
const common_assets = require("../../common/assets.js");
const PermissionCheck = () => "../../components/PermissionCheck.js";
const _sfc_main = {
  components: {
    PermissionCheck
    // 本地注册组件
  },
  mixins: [utils_permissionMixin.permissionMixin],
  // 使用权限混入
  data() {
    return {
      searchKeyword: "",
      patrolRecords: [],
      currentPage: 1,
      pageSize: 5,
      hasMore: true,
      isLoading: false,
      loadError: false,
      _needRefresh: false,
      // 标记是否需要刷新数据
      _lastLoadTime: 0,
      // 记录上次加载时间
      errorMsg: "",
      // 错误信息
      // 筛选相关
      timeFilter: "all",
      statusFilter: "all",
      // 筛选选项
      timeOptions: [
        { label: "全部时间", value: "all" },
        { label: "今天", value: "today" },
        { label: "本周", value: "this_week" },
        { label: "本月", value: "this_month" },
        { label: "上个月", value: "last_month" }
      ],
      statusOptions: [
        { label: "全部状态", value: "all" },
        { label: "待执行", value: "pending" },
        { label: "执行中", value: "processing" },
        { label: "已完成", value: "completed" },
        { label: "已超时", value: "overdue" }
      ],
      showError: false,
      errorMessage: ""
    };
  },
  computed: {
    timeFilterText() {
      const option = this.timeOptions.find((item) => item.value === this.timeFilter);
      return option ? option.label : "全部时间";
    },
    statusFilterText() {
      const option = this.statusOptions.find((item) => item.value === this.statusFilter);
      return option ? option.label : "全部状态";
    },
    emptyText() {
      if (this.timeFilter !== "all" || this.statusFilter !== "all" || this.searchKeyword) {
        return "没有符合筛选条件的巡检记录";
      }
      return "暂无巡检记录";
    }
  },
  onLoad() {
    this.loadPatrolRecords();
  },
  // 添加页面级下拉刷新
  onPullDownRefresh() {
    this.currentPage = 1;
    this.patrolRecords = [];
    this.hasMore = true;
    this.loadPatrolRecords().then(() => {
      common_vendor.index.stopPullDownRefresh();
    });
  },
  // 添加上拉加载更多
  onReachBottom() {
    if (this.hasMore && !this.isLoading) {
      this.currentPage++;
      this.loadPatrolRecords(true);
    }
  },
  onShow() {
    if (this.patrolRecords.length === 0 || this._needRefresh) {
      this._needRefresh = false;
      this.loadPatrolRecords();
    }
  },
  methods: {
    // 加载巡检记录列表
    async loadPatrolRecords(isLoadMore = false) {
      if (this.isLoading)
        return;
      const now = Date.now();
      if (!isLoadMore && now - this._lastLoadTime < 300) {
        return;
      }
      this._lastLoadTime = now;
      this.isLoading = true;
      if (!isLoadMore) {
        this.loadError = false;
      }
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      try {
        const params = {
          page: this.currentPage,
          pageSize: this.pageSize,
          keyword: this.searchKeyword,
          status: this.statusFilter !== "all" ? this.statusFilter : void 0,
          timeRange: this.timeFilter !== "all" ? this.timeFilter : void 0,
          // 只请求需要在界面上显示的字段
          fields: "id,planName,executorName,startTime,endTime,status,patrolType,executionDate,createTime,patrolPlanId"
        };
        common_vendor.index.__f__("log", "at pages/patrol/records.vue:311", "请求参数:", params);
        if (this.timeFilter !== "all") {
          const dateRange = this.getDateRangeByTimeFilter(this.timeFilter);
          if (dateRange) {
            params.startDate = dateRange.startDate;
            params.endDate = dateRange.endDate;
          }
        }
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => {
            reject(new Error("请求超时"));
          }, 8e3);
        });
        const res = await Promise.race([
          utils_api.patrolApi.getPatrolRecords(params),
          timeoutPromise
        ]);
        common_vendor.index.__f__("log", "at pages/patrol/records.vue:334", "API响应:", res);
        if (res.code === 200) {
          let recordsList = [];
          let totalPages = 1;
          if (Array.isArray(res.data)) {
            recordsList = res.data;
            totalPages = Math.ceil(recordsList.length / this.pageSize);
          } else if (res.data && res.data.list) {
            recordsList = res.data.list;
            totalPages = res.data.totalPages || Math.ceil(res.data.total / this.pageSize) || 1;
          } else if (res.data) {
            recordsList = Array.isArray(res.data) ? res.data : [res.data];
            totalPages = 1;
          }
          const records = recordsList.map((record) => ({
            id: record.id,
            planName: record.planName,
            patrolPlanId: record.patrolPlanId,
            executorName: record.executorName || "未分配",
            startTime: record.startTime,
            endTime: record.endTime,
            status: record.status,
            patrolType: record.patrolType,
            executionDate: record.executionDate,
            createTime: record.createTime
            // 移除abnormalCount字段，页面上没有使用
          }));
          if (isLoadMore) {
            this.patrolRecords = [...this.patrolRecords, ...records];
          } else {
            this.patrolRecords = records;
          }
          this.hasMore = this.currentPage < totalPages;
          common_vendor.index.hideLoading();
        } else {
          this.loadError = true;
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: res.message || "获取巡检记录失败",
            icon: "none",
            duration: 2e3
          });
        }
      } catch (err) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/patrol/records.vue:392", "获取巡检记录失败:", err);
        this.loadError = true;
        if (err.message === "请求超时") {
          this.errorMsg = "请求超时，请检查网络连接";
        } else if (err.response && err.response.status === 404) {
          this.errorMsg = "服务不可用，请稍后再试";
        } else {
          this.errorMsg = "网络异常，请稍后重试";
        }
        common_vendor.index.showToast({
          title: this.errorMsg,
          icon: "none",
          duration: 2e3
        });
      } finally {
        this.isLoading = false;
      }
    },
    // 根据时间筛选器获取日期范围
    getDateRangeByTimeFilter(timeFilter) {
      const now = /* @__PURE__ */ new Date();
      let startDate = null;
      let endDate = null;
      switch (timeFilter) {
        case "today":
          startDate = this.formatDate(now);
          endDate = this.formatDate(now);
          break;
        case "this_week":
          const dayOfWeek = now.getDay() || 7;
          const mondayDate = new Date(now);
          mondayDate.setDate(now.getDate() - dayOfWeek + 1);
          startDate = this.formatDate(mondayDate);
          endDate = this.formatDate(now);
          break;
        case "this_month":
          const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
          startDate = this.formatDate(firstDayOfMonth);
          endDate = this.formatDate(now);
          break;
        case "last_month":
          const firstDayOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
          const lastDayOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);
          startDate = this.formatDate(firstDayOfLastMonth);
          endDate = this.formatDate(lastDayOfLastMonth);
          break;
        default:
          return null;
      }
      return { startDate, endDate };
    },
    // 格式化日期为yyyy-MM-dd格式
    formatDate(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    // 搜索处理
    handleSearch() {
      this.loadPatrolRecords();
    },
    // 清除搜索关键词
    clearSearch() {
      this.searchKeyword = "";
      this.loadPatrolRecords();
    },
    // 加载更多记录
    loadMoreRecords() {
      if (!this.hasMore || this.isLoading)
        return;
      this.currentPage++;
      this.loadPatrolRecords(true);
    },
    // 显示时间筛选弹窗
    showTimeFilter() {
      this.$refs.timeFilterPopup.open();
    },
    // 显示状态筛选弹窗
    showStatusFilter() {
      this.$refs.statusFilterPopup.open();
    },
    // 选择时间筛选
    selectTimeFilter(value) {
      if (this.timeFilter === value) {
        this.$refs.timeFilterPopup.close();
        return;
      }
      this.timeFilter = value;
      this.currentPage = 1;
      this.patrolRecords = [];
      this.hasMore = true;
      this.$refs.timeFilterPopup.close();
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      setTimeout(() => {
        this.loadPatrolRecords();
      }, 100);
    },
    // 选择状态筛选
    selectStatusFilter(value) {
      if (this.statusFilter === value) {
        this.$refs.statusFilterPopup.close();
        return;
      }
      this.statusFilter = value;
      this.currentPage = 1;
      this.patrolRecords = [];
      this.hasMore = true;
      this.$refs.statusFilterPopup.close();
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      setTimeout(() => {
        this.loadPatrolRecords();
      }, 100);
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        pending: "待执行",
        processing: "执行中",
        completed: "已完成",
        overdue: "已超时"
      };
      return statusMap[status] || "未知";
    },
    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime)
        return "";
      if (Array.isArray(dateTime)) {
        if (dateTime.length >= 3) {
          const year = dateTime[0];
          const month = String(dateTime[1]).padStart(2, "0");
          const day = String(dateTime[2]).padStart(2, "0");
          if (dateTime.length >= 5) {
            const hour = String(dateTime[3]).padStart(2, "0");
            const minute = String(dateTime[4]).padStart(2, "0");
            let second = "00";
            if (dateTime.length > 5)
              second = String(dateTime[5]).padStart(2, "0");
            return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
          }
          return `${year}-${month}-${day}`;
        }
        return dateTime.join("-");
      }
      const dateTimeStr = String(dateTime);
      if (dateTimeStr.includes("T")) {
        const date = new Date(dateTimeStr);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        const seconds = String(date.getSeconds()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      }
      if (!isNaN(dateTime) && typeof dateTime === "number") {
        const date = new Date(dateTime);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        const seconds = String(date.getSeconds()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      }
      try {
        const date = new Date(dateTimeStr);
        if (!isNaN(date.getTime())) {
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, "0");
          const day = String(date.getDate()).padStart(2, "0");
          const hours = String(date.getHours()).padStart(2, "0");
          const minutes = String(date.getMinutes()).padStart(2, "0");
          const seconds = String(date.getSeconds()).padStart(2, "0");
          if (hours !== "00" || minutes !== "00" || seconds !== "00") {
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
          } else {
            return `${year}-${month}-${day}`;
          }
        }
        return dateTimeStr;
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/patrol/records.vue:625", "格式化日期时间出错:", e, dateTimeStr);
        return dateTimeStr;
      }
    },
    // 开始巡检
    startPatrol(id, planId) {
      this._needRefresh = true;
      common_vendor.index.navigateTo({
        url: `/pages/patrol/execute?id=${id}&&planId=${planId}`
      });
    },
    // 格式化日期（不含时间）
    formatDateOnly(date) {
      if (!date)
        return "";
      if (Array.isArray(date)) {
        if (date.length >= 3) {
          const year = date[0];
          const month = String(date[1]).padStart(2, "0");
          const day = String(date[2]).padStart(2, "0");
          return `${year}-${month}-${day}`;
        }
        return date.join("-");
      }
      const fullDateTime = this.formatDateTime(date);
      return fullDateTime.split(" ")[0];
    },
    // 格式化时间，只提取时间部分 (HH:MM:SS)
    formatTimeOnly(dateTime) {
      if (!dateTime)
        return "";
      const fullDateTime = this.formatDateTime(dateTime);
      if (fullDateTime.includes(" ")) {
        return fullDateTime.split(" ")[1];
      }
      return fullDateTime;
    },
    // 查看记录详情
    viewRecordDetail(id) {
      this._needRefresh = true;
      common_vendor.index.navigateTo({
        url: `/pages/patrol/record_detail?id=${id}`,
        success: (res) => {
          common_vendor.index.__f__("log", "at pages/patrol/records.vue:679", "跳转成功", res);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/patrol/records.vue:682", "跳转失败", err);
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "none",
            duration: 2e3
          });
        }
      });
    },
    retryLoading() {
      this.showError = false;
      this.errorMsg = "";
      common_vendor.index.showLoading({
        title: "正在重试..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        this.loadPatrolRecords();
      }, 1e3);
    },
    // 跳转到提交巡检页面
    goToCreatePatrol() {
      common_vendor.index.__f__("log", "at pages/patrol/records.vue:707", "点击提交巡检按钮");
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
      }, 500);
      common_vendor.index.navigateTo({
        url: "/pages/patrol/create_record",
        success: () => {
          common_vendor.index.__f__("log", "at pages/patrol/records.vue:720", "导航成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/patrol/records.vue:723", "导航失败:", err);
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "none",
            duration: 2e3
          });
        }
      });
    }
  }
};
if (!Array) {
  const _component_PermissionCheck = common_vendor.resolveComponent("PermissionCheck");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_component_PermissionCheck + _easycom_uni_popup2)();
}
const _easycom_uni_popup = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.showError
  }, $data.showError ? {
    b: common_vendor.t($data.errorMessage),
    c: common_vendor.o((...args) => $options.retryLoading && $options.retryLoading(...args))
  } : {}, {
    d: common_vendor.t($options.timeFilterText),
    e: common_vendor.o((...args) => $options.showTimeFilter && $options.showTimeFilter(...args)),
    f: common_vendor.t($options.statusFilterText),
    g: common_vendor.o((...args) => $options.showStatusFilter && $options.showStatusFilter(...args)),
    h: $data.patrolRecords.length > 0
  }, $data.patrolRecords.length > 0 ? {
    i: common_vendor.f($data.patrolRecords, (record, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(record.planName),
        b: common_vendor.t($options.getStatusText(record.status)),
        c: common_vendor.n(record.status),
        d: common_vendor.t($options.formatDateOnly(record.executionDate)),
        e: common_vendor.t(record.patrolType || "未指定"),
        f: common_vendor.t($options.formatTimeOnly(record.startTime)),
        g: common_vendor.t($options.formatTimeOnly(record.endTime)),
        h: common_vendor.t(record.executorName || "未分配"),
        i: common_vendor.t($options.formatDateTime(record.createTime)),
        j: record.status == "pending" || record.status == "overdue"
      }, record.status == "pending" || record.status == "overdue" ? {
        k: common_vendor.o(($event) => $options.startPatrol(record.id, record.patrolPlanId), index)
      } : {}, {
        l: "17c31495-0-" + i0,
        m: common_vendor.o(($event) => $options.viewRecordDetail(record.id), index),
        n: "17c31495-1-" + i0,
        o: index,
        p: common_vendor.o(($event) => $options.viewRecordDetail(record.id), index)
      });
    }),
    j: common_vendor.p({
      permission: "patrol:plans:execute"
    }),
    k: common_vendor.p({
      permission: "patrol:record:detail"
    })
  } : {}, {
    l: $data.loadError && !$data.isLoading
  }, $data.loadError && !$data.isLoading ? {
    m: common_vendor.t($data.errorMsg || "加载失败，请重试"),
    n: common_vendor.o((...args) => $options.loadPatrolRecords && $options.loadPatrolRecords(...args))
  } : {}, {
    o: !$data.isLoading && !$data.loadError && $data.patrolRecords.length === 0
  }, !$data.isLoading && !$data.loadError && $data.patrolRecords.length === 0 ? {
    p: common_assets._imports_0$1,
    q: common_vendor.t($options.emptyText)
  } : {}, {
    r: common_vendor.o(($event) => _ctx.$refs.timeFilterPopup.close()),
    s: common_vendor.f($data.timeOptions, (option, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(option.label),
        b: $data.timeFilter === option.value
      }, $data.timeFilter === option.value ? {} : {}, {
        c: index,
        d: $data.timeFilter === option.value ? 1 : "",
        e: common_vendor.o(($event) => $options.selectTimeFilter(option.value), index)
      });
    }),
    t: common_vendor.sr("timeFilterPopup", "17c31495-2"),
    v: common_vendor.p({
      type: "bottom"
    }),
    w: common_vendor.o(($event) => _ctx.$refs.statusFilterPopup.close()),
    x: common_vendor.f($data.statusOptions, (option, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(option.label),
        b: $data.statusFilter === option.value
      }, $data.statusFilter === option.value ? {} : {}, {
        c: index,
        d: $data.statusFilter === option.value ? 1 : "",
        e: common_vendor.o(($event) => $options.selectStatusFilter(option.value), index)
      });
    }),
    y: common_vendor.sr("statusFilterPopup", "17c31495-3"),
    z: common_vendor.p({
      type: "bottom"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/patrol/records.js.map
