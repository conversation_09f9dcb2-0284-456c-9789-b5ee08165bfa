package com.heating.repository;

import com.heating.entity.weather.TWeatherStationData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 气象数据仓储接口
 */
@Repository
public interface WeatherRepository extends JpaRepository<TWeatherStationData, Integer> {

    /**
     * 根据气象站名称获取最新一条气象数据
     * @param ws 气象站名称
     * @return 最新的气象数据
     */
    @Query("SELECT w FROM TWeatherStationData w WHERE w.ws LIKE %:ws% ORDER BY w.collectDt DESC  limit 1")
    Optional<TWeatherStationData> findLatestByWs(@Param("ws") String ws);

    /**
     * 获取最新一条气象数据（不限制气象站）
     * @return 最新的气象数据
     */
    @Query("SELECT w FROM TWeatherStationData w ORDER BY w.collectDt DESC limit 1")
    Optional<TWeatherStationData> findLatest();
}
