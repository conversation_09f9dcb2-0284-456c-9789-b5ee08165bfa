
.datetime-picker-container {
  background-color: #fff;
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #eee;
}
.title {
  font-size: 32rpx;
  font-weight: 500;
}
.close-btn {
  font-size: 28rpx;
  color: #999;
}
.picker-body {
  flex: 1;
  padding: 20rpx 0;
}
.picker {
  width: 100%;
  height: 400rpx;
}
.picker-item {
  line-height: 50px;
  text-align: center;
}
.btn-area {
  padding: 30rpx;
}
.confirm-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #1890ff;
  color: #fff;
  border-radius: 8rpx;
}
