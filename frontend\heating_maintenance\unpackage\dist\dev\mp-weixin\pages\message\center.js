"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const utils_auth = require("../../utils/auth.js");
const common_assets = require("../../common/assets.js");
const BaseTabBar = () => "../../components/BaseTabBar.js";
const PermissionCheck = () => "../../components/PermissionCheck.js";
const _sfc_main = {
  components: {
    BaseTabBar,
    PermissionCheck
  },
  data() {
    return {
      currentTab: "all",
      showFilterPanel: false,
      selectedPriority: "all",
      // 新增时间筛选相关数据
      showTimeFilterPanel: false,
      selectedTimeFilter: "all",
      timeFilterOptions: [
        { label: "全部时间", value: "all" },
        { label: "今天", value: "today" },
        { label: "昨天", value: "yesterday" },
        { label: "本周", value: "thisWeek" },
        { label: "本月", value: "thisMonth" }
      ],
      messageTabs: [
        { label: "全部", value: "all", count: 0, permissions: "" },
        // 全部消息不需要特定权限
        { label: "巡检", value: "inspection", count: 0, permissions: "message:patrol-message" },
        // 需要巡检记录权限
        { label: "维修", value: "workOrder", count: 0, permissions: "message:workorder-message" },
        // 需要工单列表权限
        { label: "告警", value: "alarm", count: 0, permissions: "message:alarm-message" },
        // 需要告警列表权限
        { label: "故障", value: "fault", count: 0, permissions: "message:fault-message" },
        // 需要故障列表权限
        { label: "系统", value: "system", count: 0, permissions: "message:system-message" }
        // 系统消息不需要特定权限
      ],
      priorityOptions: [
        { label: "全部", value: "all" },
        { label: "紧急", value: "urgent" },
        { label: "重要", value: "important" },
        { label: "常规", value: "normal" }
      ],
      messages: [],
      page: 1,
      pageSize: 10,
      hasMore: false,
      isLoading: false
    };
  },
  computed: {
    filteredMessages() {
      let result = [...this.messages];
      result = result.filter((msg) => {
        var _a;
        if (this.currentTab === "all") {
          if (msg.type === "system" || !((_a = this.getTabByValue(msg.type)) == null ? void 0 : _a.permissions)) {
            return true;
          }
          const tab = this.getTabByValue(msg.type);
          return tab && (!tab.permissions || this.hasPermission(tab.permissions));
        } else {
          return msg.type === this.currentTab;
        }
      });
      if (this.selectedPriority !== "all") {
        result = result.filter((msg) => msg.priority === this.selectedPriority);
      }
      if (this.selectedTimeFilter !== "all") {
        const now = /* @__PURE__ */ new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay());
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        result = result.filter((msg) => {
          const messageDate = new Date(
            msg.time.replace(/(\d{4}-\d{2}-\d{2}) (\d{2}:\d{2})/, "$1T$2:00")
          );
          switch (this.selectedTimeFilter) {
            case "today":
              return messageDate >= today;
            case "yesterday":
              return messageDate >= yesterday && messageDate < today;
            case "thisWeek":
              return messageDate >= startOfWeek;
            case "thisMonth":
              return messageDate >= startOfMonth;
            default:
              return true;
          }
        });
      }
      result.sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime());
      return result;
    }
  },
  onLoad() {
    common_vendor.index.$on("newMessageReceived", this.handleNewMessages);
    const defaultTab = this.currentTab;
    const tabConfig = this.getTabByValue(defaultTab);
    if (tabConfig && tabConfig.permissions && !this.hasPermission(tabConfig.permissions)) {
      common_vendor.index.__f__("log", "at pages/message/center.vue:267", `用户无权限查看默认标签 ${defaultTab}，尝试切换到有权限的标签`);
      const permittedTab = this.messageTabs.find((tab) => !tab.permissions || this.hasPermission(tab.permissions));
      if (permittedTab) {
        common_vendor.index.__f__("log", "at pages/message/center.vue:273", `自动切换到标签: ${permittedTab.value}`);
        this.currentTab = permittedTab.value;
      }
    }
    this.loadInitialMessages();
  },
  onShow() {
    this.messages = [];
    this.fetchAllMessages();
    this.updateAllTabCounts();
  },
  onUnload() {
    common_vendor.index.$off("newMessageReceived", this.handleNewMessages);
  },
  methods: {
    // 根据值获取对应的标签配置
    getTabByValue(value) {
      return this.messageTabs.find((tab) => tab.value === value);
    },
    // 检查是否有权限
    hasPermission(permission) {
      return utils_auth.hasPermission(permission);
    },
    handleNewMessages(payload) {
      common_vendor.index.__f__("log", "at pages/message/center.vue:302", "消息中心：收到新消息事件", payload);
      const { type, data } = payload;
      const frontendType = this.mapBackendChineseTypeToFrontend(type);
      const tab = this.getTabByValue(frontendType);
      if (tab && tab.permissions && !this.hasPermission(tab.permissions)) {
        common_vendor.index.__f__("log", "at pages/message/center.vue:311", `消息中心：用户无权限查看 ${type} 类型消息，已忽略`);
        return;
      }
      if (data && Array.isArray(data) && data.length > 0) {
        const newMessages = data.map((msg) => this.transformMessageFormat(msg, type)).filter((msg) => msg !== null);
        const existingIds = new Set(this.messages.map((m) => m.id));
        const uniqueNewMessages = newMessages.filter((nm) => !existingIds.has(nm.id));
        if (uniqueNewMessages.length > 0) {
          this.messages.unshift(...uniqueNewMessages);
          this.updateTabCounts(type, uniqueNewMessages.length);
          common_vendor.index.showToast({
            title: `收到 ${uniqueNewMessages.length} 条新的${this.mapBackendChineseTypeToFrontend(type)}消息`,
            icon: "none"
          });
        }
      }
    },
    transformMessageFormat(msg, backendChineseType) {
      if (!msg || !msg.id)
        return null;
      let frontendType = this.mapBackendChineseTypeToFrontend(backendChineseType);
      let title = `新${backendChineseType}消息`;
      let content = "详情请点击查看";
      let priority = "normal";
      let timeValue = null;
      let heatUnitName = "";
      let heatUnitId = -1;
      let alarmId = -1;
      let faultLevel = "一般";
      switch (backendChineseType) {
        case "巡检":
          title = msg.name || title;
          content = `请执行巡检任务: ${msg.name || "未指定"}`;
          priority = "normal";
          break;
        case "告警":
          title = msg.heatUnitName;
          content = msg.alarmDesc || content;
          timeValue = msg.alarmDt;
          priority = "urgent";
          heatUnitName = msg.heatUnitName || "";
          heatUnitId = msg.heatUnitId;
          alarmId = msg.id;
          faultLevel = msg.faultLevels || "一般";
          break;
        case "故障":
          title = msg.heatUnitName;
          content = msg.faultDesc || content;
          timeValue = msg.occurTime;
          priority = "important";
          heatUnitName = msg.heatUnitName || "";
          faultLevel = msg.faultLevel || "一般";
          break;
        case "维修":
          title = `${msg.heatUnitName}`;
          content = msg.fault_desc ? `来源[${msg.fault_source || "未知"}]：${msg.fault_desc}` : content;
          timeValue = msg.createdTime;
          priority = "important";
          heatUnitName = msg.heatUnitName || "";
          faultLevel = msg.faultLevel || "一般";
          break;
      }
      let timeString = "";
      if (typeof timeValue === "string") {
        timeString = timeValue;
      } else if (timeValue instanceof Date) {
        timeString = timeValue.toISOString();
      } else if (Array.isArray(timeValue) && timeValue.length >= 5) {
        try {
          const [year, month, day, hour, minute] = timeValue;
          const isoMonth = String(month).padStart(2, "0");
          const isoDay = String(day).padStart(2, "0");
          const isoHour = String(hour).padStart(2, "0");
          const isoMinute = String(minute).padStart(2, "0");
          const isoSeconds = timeValue.length > 5 ? String(timeValue[5]).padStart(2, "0") : "00";
          timeString = `${year}-${isoMonth}-${isoDay}T${isoHour}:${isoMinute}:${isoSeconds}`;
        } catch (e) {
          common_vendor.index.__f__("error", "at pages/message/center.vue:402", "消息中心：处理时间数组时出错:", timeValue, e);
          timeString = (/* @__PURE__ */ new Date()).toISOString();
        }
      } else if (timeValue) {
        timeString = String(timeValue);
      } else {
        timeString = (/* @__PURE__ */ new Date()).toISOString();
        common_vendor.index.__f__("warn", "at pages/message/center.vue:409", `消息 [${frontendType}-${msg.id}] 缺少有效时间，使用当前时间。`);
      }
      return {
        id: `${frontendType}-${msg.id}`,
        title,
        content,
        time: timeString.replace("T", " ").substring(0, 16),
        type: frontendType,
        priority,
        read: false,
        relatedId: msg.id,
        heatUnitName,
        heatUnitId,
        alarmId,
        faultLevel
      };
    },
    mapBackendChineseTypeToFrontend(backendType) {
      const map = {
        巡检: "inspection",
        告警: "alarm",
        故障: "fault",
        维修: "workOrder"
      };
      return map[backendType] || "system";
    },
    updateTabCounts(backendChineseType, countIncrement) {
      const frontendType = this.mapBackendChineseTypeToFrontend(backendChineseType);
      const tab = this.messageTabs.find((t) => t.value === frontendType);
      if (tab) {
        tab.count = (tab.count || 0) + countIncrement;
      }
      const allTab = this.messageTabs.find((t) => t.value === "all");
      if (allTab) {
        allTab.count = (allTab.count || 0) + countIncrement;
      }
    },
    updateAllTabCounts() {
      const counts = {
        all: 0,
        inspection: 0,
        workOrder: 0,
        alarm: 0,
        fault: 0,
        system: 0
      };
      const permittedTypes = this.messageTabs.filter((tab) => !tab.permissions || this.hasPermission(tab.permissions)).map((tab) => tab.value);
      if (!permittedTypes.includes("system")) {
        permittedTypes.push("system");
      }
      this.messages.forEach((msg) => {
        if (permittedTypes.includes(msg.type)) {
          counts.all++;
          if (counts[msg.type] !== void 0) {
            counts[msg.type]++;
          } else {
            counts.system++;
          }
        }
      });
      this.messageTabs.forEach((tab) => {
        if (!tab.permissions || this.hasPermission(tab.permissions)) {
          tab.count = counts[tab.value] || 0;
        } else {
          tab.count = 0;
        }
      });
    },
    loadInitialMessages() {
      if (this.isLoading)
        return;
      this.isLoading = true;
      common_vendor.index.__f__("log", "at pages/message/center.vue:497", "加载初始消息列表 - 第1页");
      this.messages = [];
      this.selectedPriority = "all";
      this.selectedTimeFilter = "all";
      this.updateAllTabCounts();
      this.isLoading = false;
    },
    loadMore() {
      if (this.isLoading || !this.hasMore)
        return;
      this.page++;
      this.isLoading = true;
      common_vendor.index.__f__("log", "at pages/message/center.vue:510", `加载更多消息 - 第${this.page}页`);
      setTimeout(() => {
        this.hasMore = false;
        this.isLoading = false;
      }, 500);
    },
    switchTab(tab) {
      const tabConfig = this.getTabByValue(tab);
      if (tabConfig && tabConfig.permissions && !this.hasPermission(tabConfig.permissions)) {
        common_vendor.index.showToast({
          title: "您没有权限查看此类消息",
          icon: "none"
        });
        return;
      }
      this.currentTab = tab;
      common_vendor.index.__f__("log", "at pages/message/center.vue:532", `切换到标签: ${tab}`);
      this.showFilterPanel = false;
      this.showTimeFilterPanel = false;
    },
    toggleFilterPanel() {
      this.showFilterPanel = !this.showFilterPanel;
      if (this.showFilterPanel) {
        this.showTimeFilterPanel = false;
      }
    },
    toggleTimeFilterPanel() {
      this.showTimeFilterPanel = !this.showTimeFilterPanel;
      if (this.showTimeFilterPanel) {
        this.showFilterPanel = false;
      }
    },
    selectPriority(priority) {
      this.selectedPriority = priority;
      this.showFilterPanel = false;
    },
    selectTimeFilter(timeFilter) {
      this.selectedTimeFilter = timeFilter;
      this.showTimeFilterPanel = false;
    },
    markAllRead() {
      this.messages.forEach((msg) => {
        msg.read = true;
      });
      common_vendor.index.showToast({
        title: "已全部标为已读",
        icon: "success"
      });
    },
    deleteSelected() {
      common_vendor.index.showModal({
        title: "确认操作",
        content: "确定要删除当前筛选出的所有消息吗？",
        success: (res) => {
          if (res.confirm) {
            const idsToDelete = this.filteredMessages.map((m) => m.id);
            this.messages = this.messages.filter((msg) => !idsToDelete.includes(msg.id));
            this.updateAllTabCounts();
            common_vendor.index.showToast({
              title: "删除成功",
              icon: "success"
            });
          }
        }
      });
    },
    viewMessageDetail(message) {
      if (!message)
        return;
      if (!message.read && message.type !== "alarm") {
        message.read = true;
      }
      if (message.type === "alarm") {
        this.showAlarmConfirmation(message);
      } else {
        let targetUrl = "";
        const relatedId = message.relatedId || "";
        switch (message.type) {
          case "fault":
            targetUrl = `/pages/fault/detail?id=${relatedId}`;
            break;
          case "workOrder":
            targetUrl = `/pages/workorder/detail?id=${relatedId}`;
            break;
          case "inspection":
            targetUrl = `/pages/patrol/record_detail?id=${relatedId}`;
            break;
        }
        if (targetUrl && relatedId) {
          common_vendor.index.__f__("log", "at pages/message/center.vue:624", `导航到: ${targetUrl}`);
          common_vendor.index.navigateTo({
            url: targetUrl,
            fail: (err) => {
              common_vendor.index.__f__("error", "at pages/message/center.vue:628", `导航失败: ${JSON.stringify(err)}`);
              common_vendor.index.showToast({ title: "页面跳转失败", icon: "none" });
            }
          });
        } else {
          common_vendor.index.__f__("log", "at pages/message/center.vue:633", "无明确跳转目标或缺少 relatedId，显示 Modal", message);
          common_vendor.index.showModal({
            title: message.title,
            content: message.content,
            showCancel: false
          });
        }
      }
    },
    showAlarmConfirmation(message) {
      common_vendor.index.showModal({
        title: "告警确认",
        content: `检测到告警：
${message.content}

是否确认此告警并直接上报故障？`,
        confirmText: "确认上报",
        cancelText: "取消",
        success: async (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({ title: "处理中..." });
            let reportUserId = common_vendor.index.getStorageSync("userId") || null;
            if (!reportUserId) {
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({ title: "无法获取用户信息，请重新登录", icon: "none" });
              return;
            }
            try {
              const reportData = {
                heat_unit_id: message.heatUnitId,
                // 可能为 null
                alarm_id: message.alarmId,
                fault_type: "设备故障",
                // 固定值
                fault_level: message.faultLevel || "一般",
                // 使用告警等级
                fault_desc: message.content,
                // 使用告警描述
                fault_source: "系统检测",
                // 固定值
                occur_time: message.time ? `${message.time}:00` : null,
                // 格式化时间，确保秒存在
                report_user_id: reportUserId,
                attachment: []
                // 无附件
              };
              common_vendor.index.__f__("log", "at pages/message/center.vue:673", "准备直接上报故障数据:", reportData);
              const reportRes = await utils_api.faultApi.reportFault(reportData);
              common_vendor.index.hideLoading();
              if (reportRes.code === 200) {
                common_vendor.index.showToast({ title: "故障已成功上报", icon: "success" });
                const alarmStatusRes = await utils_api.alarmApi.updateAlarmStatus(
                  message.relatedId,
                  1
                );
                if (alarmStatusRes.code !== 200) {
                  throw new Error(alarmStatusRes.message || "确认告警状态失败");
                }
                common_vendor.index.__f__("log", "at pages/message/center.vue:687", `告警 ${message.relatedId} 状态已更新为已确认`);
                const index = this.messages.findIndex((m) => m.id === message.id);
                if (index > -1) {
                  this.messages.splice(index, 1);
                  this.updateAllTabCounts();
                }
              } else {
                throw new Error(reportRes.message || "故障上报失败");
              }
            } catch (error) {
              common_vendor.index.hideLoading();
              common_vendor.index.__f__("error", "at pages/message/center.vue:700", "确认告警并上报故障过程中出错:", error);
              common_vendor.index.showToast({ title: error.message || "操作失败，请重试", icon: "none" });
            }
          }
        }
      });
    },
    ignoreAlarm(id) {
      const message = this.messages.find((msg) => msg.id === id);
      if (!message || message.type !== "alarm")
        return;
      common_vendor.index.showModal({
        title: "确认忽略",
        content: "确定要忽略这条告警信息吗？忽略后将不再提醒。",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.__f__(
              "log",
              "at pages/message/center.vue:717",
              `准备忽略告警，前端消息ID: ${id}, 后端告警ID: ${message.relatedId}`
            );
            common_vendor.index.showLoading({ title: "处理中..." });
            utils_api.alarmApi.updateAlarmStatus(message.relatedId, 3).then((apiRes) => {
              common_vendor.index.hideLoading();
              if (apiRes.code === 200) {
                common_vendor.index.showToast({ title: "告警已忽略", icon: "success" });
                const index = this.messages.findIndex((m) => m.id === id);
                if (index > -1) {
                  this.messages.splice(index, 1);
                  this.updateAllTabCounts();
                }
              } else {
                common_vendor.index.showToast({ title: apiRes.message || "忽略失败", icon: "none" });
              }
            }).catch((err) => {
              common_vendor.index.hideLoading();
              common_vendor.index.__f__("error", "at pages/message/center.vue:738", "忽略告警API调用失败:", err);
              common_vendor.index.showToast({ title: "操作失败，请重试", icon: "none" });
            });
          }
        }
      });
    },
    getTypeText(type) {
      const typeMap = {
        alarm: "告警",
        workOrder: "维修",
        inspection: "巡检",
        fault: "故障",
        system: "系统"
      };
      return typeMap[type] || "其他";
    },
    async fetchAllMessages() {
      common_vendor.index.__f__("log", "at pages/message/center.vue:758", "[MessageCenter] Fetching all messages onShow...");
      common_vendor.index.showLoading({ title: "加载中..." });
      try {
        const userId = common_vendor.index.getStorageSync("userId");
        const userRole = common_vendor.index.getStorageSync("userRole");
        common_vendor.index.__f__("log", "at pages/message/center.vue:766", `[MessageCenter] 当前用户ID: ${userId}, 角色: ${userRole}`);
        if (!userId) {
          common_vendor.index.__f__("warn", "at pages/message/center.vue:769", "[MessageCenter] 未获取到用户ID，可能需要重新登录");
        }
        const messageFetchers = [
          {
            api: () => utils_api.patrolApi.getPatrolPlansMessages(userId, userRole),
            // 修改为传递userId和userRole参数
            type: "巡检",
            backendTypeKey: "巡检",
            permission: "message:patrol-message"
            // 修改为正确的巡检消息权限
          },
          {
            api: () => utils_api.alarmApi.getAlarmList(userId, userRole),
            // 修改为传递userId和userRole参数
            type: "告警",
            backendTypeKey: "告警",
            permission: "message:alarm-message"
            // 告警消息权限
          },
          {
            api: () => utils_api.faultApi.getFaultMessages(userId, userRole),
            // 修改为传递userId和userRole参数
            type: "故障",
            backendTypeKey: "故障",
            permission: "message:fault-message"
            // 修改为正确的故障消息权限
          },
          {
            api: () => utils_api.workOrderApi.getWorkOrderMessages(userId, userRole),
            // 修改为传递userId和userRole参数
            type: "维修",
            backendTypeKey: "维修",
            permission: "message:workorder-message"
            // 修改为正确的工单消息权限
          }
        ];
        let allNewMessages = [];
        for (const fetcher of messageFetchers) {
          try {
            if (fetcher.permission && !this.hasPermission(fetcher.permission)) {
              common_vendor.index.__f__("log", "at pages/message/center.vue:806", `[MessageCenter] 用户无权限获取 ${fetcher.type} 消息，已跳过`);
              continue;
            }
            common_vendor.index.__f__("log", "at pages/message/center.vue:810", `[MessageCenter] Fetching ${fetcher.type} messages...`);
            const res = typeof fetcher.api === "function" ? await fetcher.api() : await fetcher.api();
            common_vendor.index.__f__(
              "log",
              "at pages/message/center.vue:813",
              `[MessageCenter] Fetched ${fetcher.type} messages response:`,
              res
            );
            if (res && res.code === 200 && res.data && Array.isArray(res.data)) {
              const transformed = res.data.map((msg) => this.transformMessageFormat(msg, fetcher.backendTypeKey)).filter((msg) => msg !== null);
              allNewMessages.push(...transformed);
              common_vendor.index.__f__(
                "log",
                "at pages/message/center.vue:822",
                `[MessageCenter] Transformed ${fetcher.type} messages:`,
                transformed.length
              );
            } else {
              common_vendor.index.__f__(
                "warn",
                "at pages/message/center.vue:827",
                `[MessageCenter] Failed to fetch or no data for ${fetcher.type} messages:`,
                res
              );
            }
          } catch (error) {
            common_vendor.index.__f__(
              "error",
              "at pages/message/center.vue:833",
              `[MessageCenter] Error fetching ${fetcher.type} messages:`,
              error
            );
          }
        }
        if (allNewMessages.length > 0) {
          const existingIds = new Set(this.messages.map((m) => m.id));
          const uniqueNewMessages = allNewMessages.filter(
            (nm) => !existingIds.has(nm.id)
          );
          if (uniqueNewMessages.length > 0) {
            this.messages.unshift(...uniqueNewMessages);
            this.messages.sort(
              (a, b) => new Date(b.time).getTime() - new Date(a.time).getTime()
            );
            common_vendor.index.__f__(
              "log",
              "at pages/message/center.vue:852",
              `[MessageCenter] Added ${uniqueNewMessages.length} new unique messages to the list.`
            );
            common_vendor.index.showToast({
              title: `加载了 ${uniqueNewMessages.length} 条新消息`,
              icon: "none"
            });
          } else {
            common_vendor.index.__f__("log", "at pages/message/center.vue:860", "[MessageCenter] No new unique messages to add.");
          }
        } else {
          common_vendor.index.__f__("log", "at pages/message/center.vue:863", "[MessageCenter] No messages fetched from APIs.");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/message/center.vue:866", "[MessageCenter] Error in fetchAllMessages:", error);
        common_vendor.index.showToast({ title: "加载消息失败", icon: "none" });
      } finally {
        this.updateAllTabCounts();
        this.isLoading = false;
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("log", "at pages/message/center.vue:872", "[MessageCenter] fetchAllMessages completed.");
      }
    }
  }
};
if (!Array) {
  const _component_PermissionCheck = common_vendor.resolveComponent("PermissionCheck");
  const _component_BaseTabBar = common_vendor.resolveComponent("BaseTabBar");
  (_component_PermissionCheck + _component_BaseTabBar)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.messageTabs, (tab, index, i0) => {
      return common_vendor.e({
        a: !tab.permissions
      }, !tab.permissions ? common_vendor.e({
        b: common_vendor.t(tab.label),
        c: tab.count > 0
      }, tab.count > 0 ? {
        d: common_vendor.t(tab.count)
      } : {}, {
        e: "tab-" + index,
        f: $data.currentTab === tab.value ? 1 : "",
        g: common_vendor.o(($event) => $options.switchTab(tab.value), "tab-" + index)
      }) : common_vendor.e({
        h: common_vendor.t(tab.label),
        i: tab.count > 0
      }, tab.count > 0 ? {
        j: common_vendor.t(tab.count)
      } : {}, {
        k: $data.currentTab === tab.value ? 1 : "",
        l: common_vendor.o(($event) => $options.switchTab(tab.value)),
        m: "perm-tab-" + index,
        n: "6d746b73-1-" + i0 + ",6d746b73-0",
        o: common_vendor.p({
          permission: tab.permissions
        })
      }));
    }),
    b: $data.showFilterPanel
  }, $data.showFilterPanel ? {
    c: common_vendor.f($data.priorityOptions, (item, index, i0) => {
      return {
        a: common_vendor.t(item.label),
        b: index,
        c: $data.selectedPriority === item.value ? 1 : "",
        d: common_vendor.o(($event) => $options.selectPriority(item.value), index)
      };
    })
  } : {}, {
    d: $data.showTimeFilterPanel
  }, $data.showTimeFilterPanel ? {
    e: common_vendor.f($data.timeFilterOptions, (item, index, i0) => {
      return {
        a: common_vendor.t(item.label),
        b: index,
        c: $data.selectedTimeFilter === item.value ? 1 : "",
        d: common_vendor.o(($event) => $options.selectTimeFilter(item.value), index)
      };
    })
  } : {}, {
    f: common_vendor.f($options.filteredMessages, (message, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.n(message.priority),
        b: common_vendor.t(message.title),
        c: common_vendor.t(message.time),
        d: common_vendor.t(message.content),
        e: common_vendor.t($options.getTypeText(message.type)),
        f: message.type === "alarm"
      }, message.type === "alarm" ? {
        g: common_vendor.o(($event) => $options.ignoreAlarm(message.id), message.id)
      } : {
        h: common_vendor.o(($event) => $options.viewMessageDetail(message), message.id)
      }, {
        i: message.id,
        j: !message.read ? 1 : "",
        k: message.priority,
        l: common_vendor.o(($event) => $options.viewMessageDetail(message), message.id)
      });
    }),
    g: $options.filteredMessages.length === 0
  }, $options.filteredMessages.length === 0 ? {
    h: common_assets._imports_0$3
  } : {}, {
    i: $data.hasMore && $options.filteredMessages.length > 0
  }, $data.hasMore && $options.filteredMessages.length > 0 ? {
    j: common_vendor.o((...args) => $options.loadMore && $options.loadMore(...args))
  } : {}, {
    k: !$data.hasMore && $options.filteredMessages.length > 0
  }, !$data.hasMore && $options.filteredMessages.length > 0 ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/message/center.js.map
