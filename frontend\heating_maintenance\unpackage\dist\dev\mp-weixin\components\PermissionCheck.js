"use strict";
const utils_auth = require("../utils/auth.js");
const common_vendor = require("../common/vendor.js");
const _sfc_main = {
  name: "PermissionCheck",
  // 移除函数式组件标记，使用普通组件以确保正确渲染
  // functional: true,
  props: {
    // 必需的权限编码，可以是单个权限编码字符串或权限编码数组（满足其中一个即可）
    permission: {
      type: [String, Array],
      required: true
    },
    // 是否显示无权限提示，默认不显示
    showTip: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 在这里可以添加组件状态
    };
  },
  computed: {
    hasRequiredPermission() {
      const result = Array.isArray(this.permission) ? this.permission.some((code) => utils_auth.hasPermission(code)) : utils_auth.hasPermission(this.permission);
      return result;
    }
  },
  mounted() {
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $options.hasRequiredPermission
  }, $options.hasRequiredPermission ? {} : $props.showTip ? {} : {}, {
    b: $props.showTip
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../.sourcemap/mp-weixin/components/PermissionCheck.js.map
