<view class="fault-detail-container"><view class="detail-card"><view class="card-header"><text class="card-title">故障信息</text><view class="{{['fault-status', b]}}">{{a}}</view></view><view class="info-group"><view class="info-item"><text class="info-label">故障编号</text><text class="info-value">{{c}}</text></view><view class="info-item"><text class="info-label">换热站</text><text class="info-value">{{d}}</text></view><view wx:if="{{e}}" class="info-item"><text class="info-label">详细地址</text><text class="info-value">{{f}}</text></view><view class="info-item"><text class="info-label">故障类型</text><text class="info-value">{{g}}</text></view><view class="info-item"><text class="info-label">故障等级</text><text class="{{['info-value', 'level-tag', i]}}">{{h}}</text></view><view class="info-item"><text class="info-label">故障来源</text><text class="info-value">{{j}}</text></view><view class="info-item"><text class="info-label">发生时间</text><text class="info-value">{{k}}</text></view><view class="info-item"><text class="info-label">上报人员</text><text class="info-value">{{l}}</text></view><view class="info-item"><text class="info-label">上报时间</text><text class="info-value">{{m}}</text></view><view class="info-item"><text class="info-label">创建时间</text><text class="info-value">{{n}}</text></view></view></view><view class="detail-card"><view class="card-header"><text class="card-title">故障描述</text></view><view class="fault-desc">{{o}}</view></view><view wx:if="{{p}}" class="detail-card"><view class="card-header"><text class="card-title">图片附件</text></view><view class="image-grid"><view wx:for="{{q}}" wx:for-item="image" wx:key="b" class="image-item" bindtap="{{image.c}}"><image src="{{image.a}}" mode="aspectFill"></image></view></view></view><view wx:if="{{r}}" class="detail-card"><view class="card-header"><text class="card-title">视频附件</text></view><view class="video-container"><video src="{{s}}" controls binderror="{{t}}" show-center-play-btn="true" enable-progress-gesture="true"></video></view></view><view wx:if="{{v}}" class="detail-card"><view class="card-header"><text class="card-title">工单信息</text><view class="{{['work-order-status', x]}}">{{w}}</view></view><view class="info-group"><view class="info-item"><text class="info-label">工单编号</text><text class="info-value">{{y}}</text></view><view class="info-item"><text class="info-label">生成时间</text><text class="info-value">{{z}}</text></view><view wx:if="{{A}}" class="info-item"><text class="info-label">维修人员</text><text class="info-value">{{B}}</text></view><view wx:if="{{C}}" class="info-item"><text class="info-label">联系电话</text><text class="info-value">{{D}}</text></view><view wx:if="{{E}}" class="info-item"><text class="info-label">维修内容</text><text class="info-value">{{F}}</text></view><view wx:if="{{G}}" class="info-item"><text class="info-label">维修结果</text><text class="info-value">{{H}}</text></view><view wx:if="{{I}}" class="info-item"><text class="info-label">维修耗材</text><text class="info-value">{{J}}</text></view><view wx:if="{{K}}" class="info-item"><text class="info-label">完成时间</text><text class="info-value">{{L}}</text></view></view></view><view wx:if="{{M}}" class="detail-card"><view class="card-header"><text class="card-title">处理记录</text></view><view class="timeline"><view wx:for="{{N}}" wx:for-item="log" wx:key="h" class="log-item"><view class="{{['timeline-dot', log.a]}}"></view><view wx:if="{{log.b}}" class="timeline-line"></view><view class="log-content"><view class="log-header"><text class="log-type">{{log.c}}</text><text class="log-time">{{log.d}}</text></view><view class="log-body"><text class="log-desc">{{log.e}}</text><text wx:if="{{log.f}}" class="log-operator">操作人：{{log.g}}</text></view></view></view></view></view><permission-check wx:if="{{R}}" u-s="{{['d']}}" u-i="2a219018-0" bind:__l="__l" u-p="{{R}}"><view wx:if="{{O}}" class="action-buttons"><button class="btn-confirm" bindtap="{{P}}">确认故障</button><button class="btn-reject" bindtap="{{Q}}">退回上报</button></view></permission-check><view wx:if="{{S}}" class="status-tip"><text>该故障上报已被退回，请检查信息准确性后重新上报。</text></view></view>