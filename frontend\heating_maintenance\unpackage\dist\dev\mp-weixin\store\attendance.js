"use strict";
const common_vendor = require("../common/vendor.js");
const utils_api = require("../utils/api.js");
const attendanceModule = {
  namespaced: true,
  // 明确设置为命名空间模块
  state: {
    // 定位上传相关
    locationUploadTimer: null,
    isUploadingLocation: false,
    useNativeGeolocation: true,
    locationUploadInterval: 1,
    // 默认1分钟
    latitude: 0,
    longitude: 0,
    // 打卡记录
    clockInTime: "",
    clockOutTime: ""
  },
  mutations: {
    // 设置定时器
    SET_LOCATION_UPLOAD_TIMER(state, timer) {
      state.locationUploadTimer = timer;
    },
    // 设置是否正在上传位置
    SET_IS_UPLOADING_LOCATION(state, status) {
      state.isUploadingLocation = status;
    },
    // 设置定位方式
    SET_USE_NATIVE_GEOLOCATION(state, value) {
      state.useNativeGeolocation = value;
    },
    // 设置位置上传间隔
    SET_LOCATION_UPLOAD_INTERVAL(state, minutes) {
      state.locationUploadInterval = minutes;
    },
    // 设置经纬度
    SET_LOCATION(state, { latitude, longitude }) {
      state.latitude = latitude;
      state.longitude = longitude;
    },
    // 设置打卡记录
    SET_CLOCK_RECORDS(state, { clockInTime, clockOutTime }) {
      state.clockInTime = clockInTime || "";
      state.clockOutTime = clockOutTime || "";
    }
  },
  actions: {
    // 获取今日打卡记录
    getTodayClockRecord({ commit, dispatch }) {
      return new Promise((resolve, reject) => {
        utils_api.attendanceApi.getTodayRecord().then((res) => {
          if (res.code === 200) {
            const data = res.data || {};
            const clockInTime = data.clockInTime || "";
            const clockOutTime = data.clockOutTime || "";
            commit("SET_CLOCK_RECORDS", {
              clockInTime,
              clockOutTime
            });
            const isPositioning = common_vendor.index.getStorageSync("isPositioning") || 0;
            if (isPositioning === 1) {
              if (clockInTime && clockInTime !== "" && (!clockOutTime || clockOutTime === "")) {
                dispatch("startLocationUpload");
              } else if (clockOutTime && clockOutTime !== "") {
                dispatch("stopLocationUpload");
              } else {
                dispatch("stopLocationUpload");
              }
            } else {
              common_vendor.index.__f__("log", "at store/attendance.js:97", "未开启定位上传功能，确保停止定位上传");
              dispatch("stopLocationUpload");
            }
            resolve(data);
          } else {
            common_vendor.index.__f__("error", "at store/attendance.js:103", "获取打卡记录失败:", res.message);
            reject(new Error("获取打卡记录失败: " + (res.message || "未知错误")));
          }
        }).catch((err) => {
          common_vendor.index.__f__("error", "at store/attendance.js:108", "获取今日打卡记录失败:", err);
          reject(err);
        });
      });
    },
    // 开始定时上传位置
    startLocationUpload({ commit, state, dispatch }) {
      if (state.isUploadingLocation) {
        return;
      }
      if (state.locationUploadTimer) {
        clearInterval(state.locationUploadTimer);
      }
      commit("SET_IS_UPLOADING_LOCATION", true);
      const storedPositionCycle = common_vendor.index.getStorageSync("positionCycle") || 1;
      const interval = storedPositionCycle;
      const intervalMs = interval * 60 * 1e3;
      common_vendor.index.__f__("log", "at store/attendance.js:134", `开始定时上传位置，间隔时间: ${interval}分钟（来源：系统配置）`);
      dispatch("uploadLocation");
      const timer = setInterval(() => {
        dispatch("uploadLocation");
      }, intervalMs);
      commit("SET_LOCATION_UPLOAD_TIMER", timer);
    },
    // 停止定时上传位置
    stopLocationUpload({ commit, state }) {
      if (state.locationUploadTimer) {
        common_vendor.index.__f__("log", "at store/attendance.js:151", "停止定时上传位置");
        clearInterval(state.locationUploadTimer);
        commit("SET_LOCATION_UPLOAD_TIMER", null);
      }
      commit("SET_IS_UPLOADING_LOCATION", false);
    },
    // 上传位置
    uploadLocation({ state, dispatch }) {
      common_vendor.index.__f__("log", "at store/attendance.js:162", "准备获取位置并上传轨迹...");
      if (state.useNativeGeolocation) {
        dispatch("getNativeLocation").then((position) => {
          const coords = position.coords;
          dispatch("uploadTrajectoryRecord", {
            latitude: coords.latitude,
            longitude: coords.longitude
          });
        }).catch((err) => {
          common_vendor.index.__f__("error", "at store/attendance.js:177", "获取原生位置失败，尝试使用uni定位:", err);
          dispatch("getUniLocation").then((res) => {
            dispatch("uploadTrajectoryRecord", {
              latitude: res.latitude,
              longitude: res.longitude
            });
          }).catch((err2) => {
            common_vendor.index.__f__("error", "at store/attendance.js:188", "位置上传失败，无法获取位置:", err2);
          });
        });
      } else {
        dispatch("getUniLocation").then((res) => {
          dispatch("uploadTrajectoryRecord", {
            latitude: res.latitude,
            longitude: res.longitude
          });
        }).catch((err) => {
          common_vendor.index.__f__("error", "at store/attendance.js:202", "位置上传失败，无法获取位置:", err);
        });
      }
    },
    // 使用原生定位API获取位置
    getNativeLocation({ dispatch }) {
      return new Promise((resolve, reject) => {
        dispatch("getUniLocation").then(resolve).catch(reject);
      });
    },
    // 使用uni.getLocation获取位置
    getUniLocation() {
      return new Promise((resolve, reject) => {
        common_vendor.index.getLocation({
          type: "gcj02",
          isHighAccuracy: true,
          highAccuracyExpireTime: 5e3,
          success: (res) => {
            common_vendor.index.__f__("log", "at store/attendance.js:260", "uni位置获取成功:", res);
            resolve(res);
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at store/attendance.js:264", "uni获取位置失败:", err);
            reject(err);
          }
        });
      });
    },
    // 上传位置轨迹记录
    uploadTrajectoryRecord({ commit }, { latitude, longitude }) {
      if (!longitude || !latitude || longitude === 0 || latitude === 0) {
        common_vendor.index.__f__("error", "at store/attendance.js:275", "经纬度无效，无法上传位置轨迹:", {
          longitude,
          latitude
        });
        return Promise.reject({ errMsg: "位置参数无效" });
      }
      commit("SET_LOCATION", { latitude, longitude });
      const userId = common_vendor.index.getStorageSync("userId");
      if (!userId) {
        common_vendor.index.__f__("warn", "at store/attendance.js:288", "未找到用户ID，无法上传位置轨迹");
        return Promise.reject({ errMsg: "未找到用户ID" });
      }
      common_vendor.index.__f__("log", "at store/attendance.js:292", "上传位置轨迹:", {
        userId,
        longitude,
        latitude
      });
      return utils_api.attendanceApi.uploadPersonTrajectory({
        userId,
        // 传递userId参数
        employeeId: userId,
        // 同时传递employeeId参数，值与userId相同
        longitude,
        latitude
      }).then((res) => {
        if (res && res.code === 200) {
          common_vendor.index.__f__("log", "at store/attendance.js:307", "位置轨迹上传成功:", res.data);
          return res.data;
        } else {
          const errorMsg = res && res.message ? res.message : "未知错误";
          common_vendor.index.__f__("error", "at store/attendance.js:311", "位置轨迹上传失败:", errorMsg, res);
          return Promise.reject({
            errMsg: "位置轨迹上传失败: " + errorMsg,
            detail: res
          });
        }
      }).catch((err) => {
        if (err && err.statusCode === 201 && err.data && err.data.code === 200) {
          common_vendor.index.__f__("log", "at store/attendance.js:320", "位置轨迹上传成功(201):", err.data.data);
          return err.data.data;
        }
        common_vendor.index.__f__("error", "at store/attendance.js:324", "位置轨迹上传请求异常:", err);
        return Promise.reject(err);
      });
    }
  }
};
exports.attendanceModule = attendanceModule;
//# sourceMappingURL=../../.sourcemap/mp-weixin/store/attendance.js.map
