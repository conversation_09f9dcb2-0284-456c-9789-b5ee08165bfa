/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.device-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}
.search-filter-bar {
  display: flex;
  padding: 20rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.search-filter-bar .search-box {
  flex: 1;
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  margin-right: 20rpx;
}
.search-filter-bar .search-box .iconfont {
  font-size: 36rpx;
  color: #999;
  margin-right: 10rpx;
}
.search-filter-bar .search-box input {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
}
.search-filter-bar .filter-btn {
  display: flex;
  align-items: center;
  padding: 0 20rpx;
}
.search-filter-bar .filter-btn .iconfont {
  font-size: 36rpx;
  color: #1890ff;
  margin-right: 6rpx;
}
.search-filter-bar .filter-btn text {
  font-size: 28rpx;
  color: #1890ff;
}
.status-tabs {
  display: flex;
  background-color: #fff;
  margin-bottom: 20rpx;
}
.status-tabs .status-tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
  position: relative;
}
.status-tabs .status-tab.active {
  color: #1890ff;
  font-weight: bold;
}
.status-tabs .status-tab.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #1890ff;
  border-radius: 2rpx;
}
.status-tabs .status-tab .tab-count {
  font-size: 24rpx;
  color: #666;
  margin-left: 4rpx;
}
.device-list {
  flex: 1;
  padding: 0 30rpx;
}
.device-list .device-card {
  display: flex;
  position: relative;
  background-color: #fff;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.device-list .device-card .device-status {
  position: absolute;
  top: 20rpx;
  left: 0;
  width: 8rpx;
  height: 36rpx;
  border-radius: 0 4rpx 4rpx 0;
}
.device-list .device-card .device-status.online {
  background-color: #52c41a;
}
.device-list .device-card .device-status.offline {
  background-color: #666;
}
.device-list .device-card .device-status.fault {
  background-color: #f5222d;
}
.device-list .device-card .device-info {
  flex: 1;
  padding-left: 16rpx;
}
.device-list .device-card .device-info .device-name-type {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.device-list .device-card .device-info .device-name-type .device-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 10rpx;
}
.device-list .device-card .device-info .device-name-type .device-type {
  font-size: 24rpx;
  color: #fff;
  background-color: #1890ff;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}
.device-list .device-card .device-info .device-model {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.device-list .device-card .device-info .device-location {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
}
.device-list .device-card .device-info .device-location .iconfont {
  margin-right: 6rpx;
}
.device-list .device-card .device-metrics {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-right: 20rpx;
}
.device-list .device-card .device-metrics .metric-item {
  display: flex;
  align-items: center;
  margin-bottom: 6rpx;
}
.device-list .device-card .device-metrics .metric-item .metric-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 10rpx;
}
.device-list .device-card .device-metrics .metric-item .metric-value {
  font-size: 24rpx;
  color: #333;
}
.device-list .device-card .device-metrics .metric-item .metric-value.alarm {
  color: #f5222d;
  font-weight: bold;
}
.device-list .device-card .device-action {
  display: flex;
  align-items: center;
}
.device-list .device-card .device-action .iconfont {
  font-size: 40rpx;
  color: #1890ff;
}
.device-list .loading-more, .device-list .no-more {
  text-align: center;
  font-size: 26rpx;
  color: #666;
  padding: 20rpx 0;
}
.device-list .empty-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 200rpx;
}
.device-list .empty-list image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.device-list .empty-list text {
  font-size: 28rpx;
  color: #666;
}
.filter-modal {
  background-color: #fff;
  border-radius: 16rpx 16rpx 0 0;
  overflow: hidden;
}
.filter-modal .filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}
.filter-modal .filter-header .filter-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.filter-modal .filter-header .filter-reset {
  font-size: 28rpx;
  color: #1890ff;
}
.filter-modal .filter-content {
  padding: 20rpx 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}
.filter-modal .filter-content .filter-section {
  margin-bottom: 30rpx;
}
.filter-modal .filter-content .filter-section .filter-section-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}
.filter-modal .filter-content .filter-section .filter-options {
  display: flex;
  flex-wrap: wrap;
}
.filter-modal .filter-content .filter-section .filter-options .filter-option {
  padding: 10rpx 30rpx;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #333;
  margin-right: 20rpx;
  margin-bottom: 16rpx;
}
.filter-modal .filter-content .filter-section .filter-options .filter-option.selected {
  background-color: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}
.filter-modal .filter-footer {
  display: flex;
  padding: 20rpx;
  border-top: 1rpx solid #eee;
}
.filter-modal .filter-footer button {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  border-radius: 40rpx;
}
.filter-modal .filter-footer button::after {
  border: none;
}
.filter-modal .filter-footer .btn-cancel {
  margin-right: 20rpx;
  background-color: #f5f5f5;
  color: #333;
}
.filter-modal .filter-footer .btn-apply {
  background-color: #1890ff;
  color: #fff;
}
.fab-button {
  position: fixed;
  right: 30rpx;
  bottom: 30rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #1890ff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.3);
}
.fab-button .iconfont {
  font-size: 48rpx;
  color: #fff;
}