package com.heating.service.impl;

import com.heating.dto.weather.WeatherRequest;
import com.heating.entity.weather.TWeatherStationData;
import com.heating.repository.WeatherRepository;
import com.heating.service.WeatherService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 气象数据服务实现类
 */
@Service
public class WeatherServiceImpl implements WeatherService {

    private static final Logger logger = LoggerFactory.getLogger(WeatherServiceImpl.class);

    @Autowired
    private WeatherRepository weatherRepository;

    /**
     * 根据地点获取最新气象数据
     * @param request 包含地点信息的请求
     * @return 最新气象数据
     */
    @Override
    public Map<String, Object> getLatestWeatherData(WeatherRequest request) {
        logger.info("获取气象数据，地点: {}", request.getLocation());
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            Optional<TWeatherStationData> weatherData;
            
            // 如果指定了地点，按地点查询；否则获取最新数据
            if (request.getLocation() != null && !request.getLocation().trim().isEmpty()) {
                weatherData = weatherRepository.findLatestByWs(request.getLocation());
            } else {
                weatherData = weatherRepository.findLatest();
            }
            
            if (weatherData.isPresent()) {
                TWeatherStationData data = weatherData.get();
                result.put("ws", data.getWs()); // 气象站
                result.put("temperature", data.getT()); // 温度
                result.put("feelTemperature", data.getF()); // 体感温度
                result.put("humidity", data.getH()); // 湿度
                result.put("windSpeed", data.getR()); // 风速
                result.put("solarRadiation", data.getS()); // 太阳辐射
                result.put("collectTime", data.getCollectDt()); // 采集时间
                
                logger.info("成功获取气象数据: 气象站={}, 温度={}°C", data.getWs(), data.getT());
            } else {
                logger.warn("未找到气象数据，地点: {}", request.getLocation());
                // 返回默认数据
                result.put("ws", request.getLocation() != null ? request.getLocation() : "未知");
                result.put("temperature", 23.0);
                result.put("feelTemperature", 25.0);
                result.put("humidity", 60.0);
                result.put("windSpeed", 2.5);
                result.put("solarRadiation", 800.0);
                result.put("collectTime", "暂无数据");
            }
            
        } catch (Exception e) {
            logger.error("获取气象数据失败: {}", e.getMessage(), e);
            // 返回默认数据
            result.put("ws", request.getLocation() != null ? request.getLocation() : "未知");
            result.put("temperature", 23.0);
            result.put("feelTemperature", 25.0);
            result.put("humidity", 60.0);
            result.put("windSpeed", 2.5);
            result.put("solarRadiation", 800.0);
            result.put("collectTime", "暂无数据");
        }
        
        return result;
    }
}
