{"version": 3, "file": "login.js", "sources": ["pages/user/login.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlci9sb2dpbi52dWU"], "sourcesContent": ["<template>\n  <view class=\"login-container\">\n    <!-- 背景圆形 -->\n    <view class=\"bg-circle bg-circle-1\"></view>\n    <view class=\"bg-circle bg-circle-2\"></view>\n    <view class=\"bg-circle bg-circle-3\"></view>\n\n    <!-- 顶部小方块 LOGO -->\n    <view class=\"logo-box\" v-if=\"!showRegister\">\n      <view class=\"logo-text\"></view>\n    </view>\n    <!-- 主标题 -->\n    <view class=\"title-box\" v-if=\"!showRegister\">\n      <text class=\"main-title\">洁明智慧供热</text>\n      <text class=\"sub-title\">AI连接未来</text>\n    </view>\n\n    <!-- 登录表单 -->\n    <view class=\"login-form\" v-if=\"!showRegister\">\n      <!-- 用户名/手机号输入框 -->\n      <view class=\"input-item\">\n        <input\n          type=\"text\"\n          v-model=\"loginAccount\"\n          placeholder=\"请输入用户名或手机号\"\n          maxlength=\"20\"\n          placeholder-class=\"placeholder\"\n        />\n      </view>\n\n      <!-- 密码输入框 -->\n      <view class=\"input-item\">\n        <input\n          type=\"password\"\n          v-model=\"password\"\n          placeholder=\"请输入密码\"\n          maxlength=\"20\"\n          password\n          placeholder-class=\"placeholder\"\n        />\n      </view>\n\n      <!-- 记住密码选项 -->\n      <view class=\"remember-password\">\n        <view class=\"checkbox-wrapper\" @click=\"toggleRememberPassword\">\n          <view class=\"checkbox\" :class=\"{ checked: rememberPassword }\">\n            <text class=\"checkbox-icon\" v-if=\"rememberPassword\">✓</text>\n          </view>\n          <text class=\"checkbox-label\">记住密码</text>\n        </view>\n      </view>\n\n      <!-- 登录按钮 -->\n      <view class=\"login-btn\" @click=\"handleLogin\">\n        <text>登 录</text>\n      </view>\n\n      <!-- 其他登录方式 -->\n      <!-- <view class=\"other-login\">\n        <text class=\"other-title\">其他登录方式</text>\n        <view class=\"login-icons\">\n          <view class=\"login-icon wechat-icon\" @click=\"loginWithWechat\"></view>\n          <view class=\"login-icon phone-icon\" @click=\"loginWithPhone\"></view>\n        </view>\n      </view> -->\n    </view>\n\n    <!-- 注册表单 -->\n    <view class=\"register-form\" v-if=\"showRegister\">\n      <!-- 用户名输入框 -->\n      <view class=\"input-item\">\n        <input\n          type=\"text\"\n          v-model=\"registerForm.username\"\n          placeholder=\"请输入用户名\"\n          placeholder-class=\"placeholder\"\n        />\n      </view>\n      <!-- 用户名输入框 -->\n      <view class=\"input-item\">\n        <input\n          type=\"text\"\n          v-model=\"registerForm.name\"\n          placeholder=\"请输入姓名\"\n          placeholder-class=\"placeholder\"\n        />\n      </view>\n      <!-- 手机号输入框 -->\n      <view class=\"input-item\">\n        <input\n          type=\"number\"\n          v-model=\"registerForm.phone\"\n          placeholder=\"请输入手机号\"\n          maxlength=\"11\"\n          placeholder-class=\"placeholder\"\n        />\n      </view>\n\n      <!-- 验证码输入框 -->\n     <!-- <view class=\"input-item verification-code\">\n        <input\n          type=\"number\"\n          v-model=\"registerForm.verificationCode\"\n          placeholder=\"请输入验证码\"\n          maxlength=\"6\"\n          placeholder-class=\"placeholder\"\n        />\n        <view\n          class=\"get-code-btn\"\n          :class=\"{ disabled: countDown > 0 }\"\n          @click=\"getVerificationCode\"\n        >\n          <text>{{ countDown > 0 ? `${countDown}秒后重新获取` : \"获取验证码\" }}</text>\n        </view>\n      </view> -->\n \n\n      <!-- 密码输入框 -->\n      <view class=\"input-item\">\n        <input\n          type=\"password\"\n          v-model=\"registerForm.password\"\n          placeholder=\"请设置密码\"\n          maxlength=\"20\"\n          password\n          placeholder-class=\"placeholder\"\n        />\n      </view>\n\n      <!-- 确认密码输入框 -->\n      <view class=\"input-item\">\n        <input\n          type=\"password\"\n          v-model=\"registerForm.confirmPassword\"\n          placeholder=\"请确认密码\"\n          maxlength=\"20\"\n          password\n          placeholder-class=\"placeholder\"\n        />\n      </view>\n\n      <!-- 注册按钮 -->\n      <view class=\"login-btn register-btn\" @click=\"handleRegister\">\n        <text>注 册</text>\n      </view>\n\n      <!-- 返回登录 -->\n      <view class=\"switch-action\" @click=\"toggleForm\">\n        <text>已有账号？返回登录</text>\n      </view>\n    </view>\n\n    <!-- 底部用户协议 -->\n    <view class=\"agreement\">\n      <text class=\"agreement-text\" v-if=\"!showRegister\">登录即表示同意</text>\n      <text class=\"agreement-text\" v-else>注册即表示同意</text>\n      <text class=\"agreement-link\" @click=\"openAgreement\">《用户协议》</text>\n      <text class=\"register-link\" v-if=\"!showRegister\" @click=\"toggleForm\"\n        >新用户注册</text>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { userApi, testApiConnection } from \"../../utils/api\";\n\nexport default {\n  data() {\n    return {\n      loginAccount: \"\", // 用户名或手机号\n      password: \"\", // 密码\n      rememberPassword: false, // 是否记住密码\n      apiConnected: true, // API连接状态\n      showRegister: false, // 是否显示注册表单\n      countDown: 0, // 验证码倒计时\n      registerForm: {\n        username: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        name: \"\",\n        phone: \"\",\n        verificationCode: \"\",\n      },\n    };\n  },\n  onLoad() {\n    // 检测API连接\n    this.checkApiConnection();\n    // 加载记住的登录信息\n    this.loadRememberedCredentials();\n  },\n  methods: {\n    // 检测API连接\n    async checkApiConnection() {\n      try {\n        // 检查API服务是否可连接\n        console.log(\"开始检测API连接...\");\n        this.apiConnected = await testApiConnection();\n        console.log(\"API连接检测结果:\", this.apiConnected);\n\n        if (!this.apiConnected) {\n          uni.showToast({\n            title: \"无法连接到服务器，请确认后端服务是否已启动\",\n            icon: \"none\",\n            duration: 3000,\n          });\n        }\n      } catch (err) {\n        console.error(\"API连接检测出错:\", err);\n        this.apiConnected = false;\n      }\n    },\n\n    // 加载记住的登录信息\n    loadRememberedCredentials() {\n      try {\n        const rememberedAccount = uni.getStorageSync('rememberedAccount');\n        const rememberedPassword = uni.getStorageSync('rememberedPassword');\n        const isRemembered = uni.getStorageSync('rememberPassword');\n\n        if (isRemembered && rememberedAccount && rememberedPassword) {\n          this.loginAccount = rememberedAccount;\n          this.password = rememberedPassword;\n          this.rememberPassword = true;\n          console.log(\"已加载记住的登录信息\");\n        }\n      } catch (err) {\n        console.error(\"加载记住的登录信息失败:\", err);\n      }\n    },\n\n    // 保存登录信息\n    saveCredentials() {\n      try {\n        if (this.rememberPassword) {\n          uni.setStorageSync('rememberedAccount', this.loginAccount);\n          uni.setStorageSync('rememberedPassword', this.password);\n          uni.setStorageSync('rememberPassword', true);\n          console.log(\"已保存登录信息\");\n        } else {\n          // 如果不记住密码，清除保存的信息\n          uni.removeStorageSync('rememberedAccount');\n          uni.removeStorageSync('rememberedPassword');\n          uni.removeStorageSync('rememberPassword');\n          console.log(\"已清除保存的登录信息\");\n        }\n      } catch (err) {\n        console.error(\"保存登录信息失败:\", err);\n      }\n    },\n\n    // 切换记住密码状态\n    toggleRememberPassword() {\n      this.rememberPassword = !this.rememberPassword;\n      // 如果取消记住密码，立即清除保存的信息\n      if (!this.rememberPassword) {\n        try {\n          uni.removeStorageSync('rememberedAccount');\n          uni.removeStorageSync('rememberedPassword');\n          uni.removeStorageSync('rememberPassword');\n          console.log(\"已清除保存的登录信息\");\n        } catch (err) {\n          console.error(\"清除登录信息失败:\", err);\n        }\n      }\n    },\n\n    // 处理登录\n    handleLogin() {\n      // 表单验证\n      if (!this.loginAccount.trim()) {\n        uni.showToast({\n          title: \"请输入用户名或手机号\",\n          icon: \"none\",\n          position: 'center'\n        });\n        return;\n      }\n\n      if (!this.password.trim()) {\n        uni.showToast({\n          title: \"请输入密码\",\n          icon: \"none\",\n          position: 'center'\n        });\n        return;\n      }\n\n      // 显示加载\n      uni.showLoading({\n        title: \"登录中...\",\n      });\n\n      console.log(\"准备提交登录请求:\", this.loginAccount);\n\n      // 判断输入的是手机号还是用户名\n      const isPhone = /^1\\d{10}$/.test(this.loginAccount);\n      \n      // 使用原生请求，避免跨域问题\n      uni.request({\n        url: `${this.$baseUrl}/api/auth/login`,\n        method: \"POST\",\n        data: {\n          username: this.loginAccount,\n          password: this.password,\n          loginType: isPhone ? 'phone' : 'username' // 添加登录类型标识\n        },\n        header: {\n          \"Content-Type\": \"application/json\",\n        },\n        withCredentials: false,\n        success: (res) => {\n          uni.hideLoading();\n          console.log(\"登录响应:\", res);\n\n          // 检查返回状态\n          if (res.statusCode === 200 && res.data.code === 200) {\n            // 登录成功，保存登录信息（如果选择记住密码）\n            this.saveCredentials();\n\n            const { token, userId, role, permissions, isPositioning, heatUnitId, positionCycle } = res.data.data;\n            console.log(\"userId=======\", userId);\n            // 存储登录信息\n            uni.setStorageSync(\"token\", token);\n            uni.setStorageSync(\"userId\", userId);\n            uni.setStorageSync(\"userRole\", role);\n            uni.setStorageSync(\"userPermissions\", permissions);\n            uni.setStorageSync(\"heatUnitId\", heatUnitId || \"\"); // 存储热力单位ID\n\n            // 存储定位设置参数\n            uni.setStorageSync(\"isPositioning\", isPositioning || 0);\n            // 存储定位上传周期参数（单位：分钟）\n            uni.setStorageSync(\"positionCycle\", positionCycle || 1);\n            console.log(\"是否开启定位上传: \", isPositioning || 0);\n            console.log(\"定位上传周期: \", positionCycle || 1, \"分钟\");\n            console.log(\"用户热力单位ID: \", heatUnitId || \"无\");\n\n            // 获取用户详细信息\n            this.getUserInfo();\n\n            // 提示登录成功\n            uni.showToast({\n              title: \"登录成功\",\n              icon: \"success\",\n            });\n\n            // 跳转到首页\n            setTimeout(() => {\n              uni.reLaunch({\n                url: \"/pages/home/<USER>\",\n              });\n            }, 1000);\n          } else {\n           // 登录失败\n           uni.showToast({\n             title: res.data?.message || \"登录失败，请检查用户名和密码\",\n             icon: \"none\",\n           });\n          }\n        },\n        fail: (err) => {\n          uni.hideLoading();\n\n          // 增强的错误处理\n          console.error(\"登录请求失败详情:\", err);\n\n          // 处理跨域错误\n          if (err.errMsg.includes(\"OPTIONS\")) {\n            uni.showToast({\n              title: \"跨域请求失败，请检查后端CORS配置\",\n              icon: \"none\",\n              duration: 3000,\n            });\n            return;\n          }\n\n          // 根据错误类型提供不同的错误提示\n          let errorMsg = \"网络异常，请稍后重试\";\n\n          if (err.errMsg.includes(\"timeout\")) {\n            errorMsg = \"请求超时，请检查网络连接\";\n          } else if (err.errMsg.includes(\"fail\")) {\n            errorMsg = \"无法连接到服务器，请检查网络或后端服务状态\";\n          }\n\n          // 显示错误提示\n          uni.showToast({\n            title: errorMsg,\n            icon: \"none\",\n            duration: 3000,\n          });\n        },\n      });\n    },\n\n    // 获取用户详细信息\n    getUserInfo() {\n      userApi\n        .getUserInfo()\n        .then((res) => {\n          if (res.code === 200) {\n            // 存储用户信息\n            uni.setStorageSync(\"userInfo\", res.data);\n          } else {\n            console.error(\"获取用户信息失败:\", res);\n          }\n        })\n        .catch((err) => {\n          console.error(\"获取用户信息请求失败:\", err);\n        });\n    },\n\n    // 打开用户协议\n    openAgreement() {\n      uni.navigateTo({\n        url: \"/pages/user/agreement\",\n      });\n    },\n\n    // 切换登录/注册表单\n    toggleForm() {\n      this.showRegister = !this.showRegister;\n    },\n\n    // 获取验证码\n    getVerificationCode() {\n      // 倒计时中不允许再次获取\n      if (this.countDown > 0) {\n        return;\n      }\n\n      // 验证手机号\n      if (!this.registerForm.phone.trim()) {\n        uni.showToast({\n          title: \"请输入手机号\",\n          icon: \"none\",\n        });\n        return;\n      }\n\n      if (!/^1\\d{10}$/.test(this.registerForm.phone)) {\n        uni.showToast({\n          title: \"请输入正确的手机号\",\n          icon: \"none\",\n        });\n        return;\n      }\n\n      // 显示加载\n      uni.showLoading({\n        title: \"发送中...\",\n      });\n\n      // 假设这里调用发送验证码的API\n      // 真实环境应当调用后端接口发送验证码\n      setTimeout(() => {\n        uni.hideLoading();\n\n        // 开始倒计时\n        this.countDown = 60;\n        const timer = setInterval(() => {\n          this.countDown--;\n          if (this.countDown <= 0) {\n            clearInterval(timer);\n          }\n        }, 1000);\n\n        uni.showToast({\n          title: \"验证码已发送\",\n          icon: \"success\",\n        });\n      }, 1500);\n    },\n\n    // 处理注册\n    handleRegister() {\n      // 表单验证\n      if (!this.registerForm.phone.trim()) {\n        uni.showToast({\n          title: \"请输入手机号\",\n          icon: \"none\",\n        });\n        return;\n      }\n\n      if (!/^1\\d{10}$/.test(this.registerForm.phone)) {\n        uni.showToast({\n          title: \"请输入正确的手机号\",\n          icon: \"none\",\n        });\n        return;\n      }\n\n      if (!this.registerForm.username.trim()) {\n        uni.showToast({\n          title: \"请输入用户名\",\n          icon: \"none\",\n        });\n        return;\n      }\n\n      if (!this.registerForm.password.trim()) {\n        uni.showToast({\n          title: \"请输入密码\",\n          icon: \"none\",\n        });\n        return;\n      }\n\n      if (this.registerForm.password !== this.registerForm.confirmPassword) {\n        uni.showToast({\n          title: \"两次输入的密码不一致\",\n          icon: \"none\",\n        });\n        return;\n      }\n\n      if (!this.registerForm.name.trim()) {\n        uni.showToast({\n          title: \"请输入姓名\",\n          icon: \"none\",\n        });\n        return;\n      }\n\n      // 显示加载\n      uni.showLoading({\n        title: \"注册中...\",\n      });\n\n      // 发送注册请求\n      uni.request({\n        url: `${this.$baseUrl}/api/auth/register`,\n        method: \"POST\",\n        data: {\n          username: this.registerForm.username,\n          password: this.registerForm.password,\n          name: this.registerForm.name,\n          phone: this.registerForm.phone,\n        },\n        header: {\n          \"Content-Type\": \"application/json\",\n        },\n        success: (res) => {\n          uni.hideLoading();\n          console.log(\"注册响应:\", res);\n\n          // 检查返回状态\n          if (res.statusCode === 200 && res.data.code === 200) {\n            // 注册成功\n            uni.hideLoading();\n\t\t\tuni.showToast({\n\t\t\t  title: \"该账号暂未审核,请等待管理员审核\",\n\t\t\t   duration: 3000,\n\t\t\t  icon: \"none\",\n\t\t\t});\n            // 清空表单并切换到登录页\n            setTimeout(() => {\n              this.loginAccount = this.registerForm.username;\n              this.password = this.registerForm.password;\n              this.registerForm = {\n                username: \"\",\n                password: \"\",\n                confirmPassword: \"\",\n                name: \"\",\n                phone: \"\",\n                verificationCode: \"\",\n              };\n              this.showRegister = false;\n            }, 2500);\n          } else {\n            // 注册失败\n            uni.showToast({\n              title: res.data?.message || \"注册失败，请稍后重试\",\n              icon: \"none\",\n              duration: 2000\n            });\n          }\n        },\n        fail: (err) => {\n          uni.hideLoading();\n          console.error(\"注册请求失败:\", err);\n\n          uni.showToast({\n            title: \"网络异常，请稍后重试\",\n            icon: \"none\",\n          });\n        },\n      });\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\">\n/* Add page-level styles to prevent scrolling */\npage {\n  height: 100%;\n  overflow: hidden; /* 改回hidden防止滚动 */\n}\n\n.login-container {\n  display: flex;\n  flex-direction: column;\n  height: 100vh; /* 使用固定高度而不是min-height */\n  background-color: #0a0e17;\n  position: relative;\n  overflow: hidden; /* 改回hidden防止滚动 */\n  padding: 0 40rpx;\n  box-sizing: border-box;\n  padding-top: 120rpx;\n  padding-bottom: 100rpx;\n}\n\n/* 背景圆形 */\n.bg-circle {\n  position: absolute;\n  border-radius: 50%;\n  z-index: 0;\n\n  &.bg-circle-1 {\n    width: 500rpx;\n    height: 500rpx;\n    background: linear-gradient(135deg, rgba(28, 40, 86, 0.6), rgba(41, 26, 87, 0.6));\n    top: -100rpx;\n    right: -150rpx;\n  }\n\n  &.bg-circle-2 {\n    width: 700rpx;\n    height: 700rpx;\n    background: linear-gradient(135deg, rgba(28, 40, 86, 0.4), rgba(41, 26, 87, 0.4));\n    bottom: -300rpx;\n    left: -300rpx;\n  }\n\n  &.bg-circle-3 {\n    width: 300rpx;\n    height: 300rpx;\n    background: linear-gradient(135deg, rgba(41, 26, 87, 0.3), rgba(28, 40, 86, 0.3));\n    top: 40%;\n    right: -100rpx;\n  }\n}\n\n/* LOGO 方块 */\n.logo-box {\n  width: 100rpx;\n  height: 100rpx;\n  background-color: transparent;\n  border-radius: 20rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-top: 80rpx; /* Reduced from 150rpx */\n  position: relative;\n  z-index: 1;\n\n  .logo-text {\n    width: 100%;\n    height: 100%;\n    background-image: url(\"@/static/images/logo4.png\");\n    background-size: contain;\n    background-position: center;\n    background-repeat: no-repeat;\n  }\n}\n\n/* 标题 */\n.title-box {\n  margin-top: 30rpx; /* Reduced from 60rpx */\n  margin-bottom: 40rpx; /* Reduced from 80rpx */\n  position: relative;\n  z-index: 1;\n\n  .main-title {\n    font-size: 48rpx; /* Reduced from 56rpx */\n    font-weight: bold;\n    color: #ffffff;\n    display: block;\n    margin-bottom: 10rpx; /* Reduced from 16rpx */\n  }\n\n  .sub-title {\n    font-size: 28rpx; /* Reduced from 32rpx */\n    color: rgba(255, 255, 255, 0.6);\n    display: block;\n  }\n}\n\n/* 登录表单 */\n.login-form {\n  position: relative;\n  z-index: 1;\n margin-top: 60rpx;\n  .input-item {\n    height: 90rpx; /* Reduced from 100rpx */\n    background-color: rgba(255, 255, 255, 0.05);\n    border-radius: 45rpx;\n    margin-bottom: 30rpx; /* Reduced from 40rpx */\n    padding: 0 40rpx;\n    display: flex;\n    align-items: center;\n\n    input {\n      flex: 1;\n      height: 100%;\n      color: #ffffff;\n      font-size: 28rpx; /* Reduced from 30rpx */\n    }\n\n    .placeholder {\n      color: rgba(255, 255, 255, 0.4);\n    }\n  }\n\n  .remember-password {\n    display: flex;\n    align-items: center;\n    margin-bottom: 20rpx;\n\n    .checkbox-wrapper {\n      display: flex;\n      align-items: center;\n      cursor: pointer;\n\n      .checkbox {\n        width: 32rpx;\n        height: 32rpx;\n        border: 2rpx solid rgba(255, 255, 255, 0.3);\n        border-radius: 6rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-right: 16rpx;\n        transition: all 0.3s ease;\n\n        &.checked {\n          background: linear-gradient(90deg, #0ab9fd, #2b79fb);\n          border-color: #0ab9fd;\n        }\n\n        .checkbox-icon {\n          color: #ffffff;\n          font-size: 20rpx;\n          font-weight: bold;\n        }\n      }\n\n      .checkbox-label {\n        color: rgba(255, 255, 255, 0.7);\n        font-size: 28rpx;\n      }\n    }\n  }\n\n  .login-btn {\n    height: 90rpx; /* Reduced from 100rpx */\n    background: linear-gradient(90deg, #0ab9fd, #2b79fb);\n    border-radius: 45rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin-top: 40rpx; /* Reduced from 60rpx */\n    margin-bottom: 40rpx; /* Reduced from 80rpx */\n    color:#2b79fb;\n    text {\n      color: #ffffff;\n      font-size: 32rpx;\n      font-weight: 500;\n    }\n  }\n}\n\n/* 其他登录方式 */\n.other-login {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n\n  .other-title {\n    color: rgba(255, 255, 255, 0.5);\n    font-size: 28rpx;\n    margin-bottom: 40rpx;\n  }\n\n  .login-icons {\n    display: flex;\n    justify-content: center;\n\n    .login-icon {\n      width: 80rpx;\n      height: 80rpx;\n      border-radius: 50%;\n      margin: 0 40rpx;\n\n      &.wechat-icon {\n        background-color: #33b347;\n        position: relative;\n\n        &::before {\n          content: \"\";\n          position: absolute;\n          top: 50%;\n          left: 50%;\n          transform: translate(-50%, -50%);\n          width: 50rpx;\n          height: 50rpx;\n          background-color: #ffffff;\n          mask-size: cover;\n          -webkit-mask-size: cover;\n          mask-repeat: no-repeat;\n          -webkit-mask-repeat: no-repeat;\n          mask-position: center;\n          -webkit-mask-position: center;\n          mask-image: url(\"data:image/svg+xml,%3Csvg t='1682517223093' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='2368' width='200' height='200'%3E%3Cpath d='M664.250054 368.541681c10.015098 0 19.892049 0.732687 29.67281 1.795606-26.647917-122.810047-159.358451-214.077703-310.826188-214.077703-169.353083 0-308.085774 114.232694-308.085774 259.274068 0 83.708494 46.165436 152.460344 123.281791 205.78483l-30.80868 91.730191 107.688651-53.455469c38.558178 7.53665 69.459978 15.308661 107.924012 15.308661 9.66308 0 19.230993-0.470721 28.752858-1.225921-6.025227-20.36584-9.521864-41.723264-9.521864-63.862493C402.328693 476.632491 517.908058 368.541681 664.250054 368.541681zM498.62897 285.87389c23.200398 0 38.557154 15.120372 38.557154 38.061874 0 22.846334-15.356756 38.156018-38.557154 38.156018-23.107277 0-46.260603-15.309684-46.260603-38.156018C452.368366 300.994262 475.522716 285.87389 498.62897 285.87389zM283.016307 362.090758c-23.107277 0-46.402843-15.309684-46.402843-38.156018 0-22.941502 23.295566-38.061874 46.402843-38.061874 23.081695 0 38.46301 15.120372 38.46301 38.061874C321.479317 346.782098 306.098002 362.090758 283.016307 362.090758zM945.448458 606.151333c0-121.888048-123.258255-221.236753-261.683954-221.236753-146.57838 0-262.015505 99.348706-262.015505 221.236753 0 122.06508 115.437126 221.200938 262.015505 221.200938 30.66644 0 61.617359-7.609305 92.423993-15.262612l84.513836 45.786813-23.178909-76.17082C899.379213 735.776599 945.448458 674.90216 945.448458 606.151333zM598.803483 567.994292c-15.332197 0-30.807656-15.096836-30.807656-30.501688 0-15.190981 15.47546-30.477129 30.807656-30.477129 23.295566 0 38.558178 15.286148 38.558178 30.477129C637.361661 552.897456 622.099049 567.994292 598.803483 567.994292zM768.25071 567.994292c-15.213493 0-30.594809-15.096836-30.594809-30.501688 0-15.190981 15.381315-30.477129 30.594809-30.477129 23.107277 0 38.558178 15.286148 38.558178 30.477129C806.808888 552.897456 791.357987 567.994292 768.25071 567.994292z' p-id='2369'%3E%3C/path%3E%3C/svg%3E\");\n          -webkit-mask-image: url(\"data:image/svg+xml,%3Csvg t='1682517223093' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='2368' width='200' height='200'%3E%3Cpath d='M664.250054 368.541681c10.015098 0 19.892049 0.732687 29.67281 1.795606-26.647917-122.810047-159.358451-214.077703-310.826188-214.077703-169.353083 0-308.085774 114.232694-308.085774 259.274068 0 83.708494 46.165436 152.460344 123.281791 205.78483l-30.80868 91.730191 107.688651-53.455469c38.558178 7.53665 69.459978 15.308661 107.924012 15.308661 9.66308 0 19.230993-0.470721 28.752858-1.225921-6.025227-20.36584-9.521864-41.723264-9.521864-63.862493C402.328693 476.632491 517.908058 368.541681 664.250054 368.541681zM498.62897 285.87389c23.200398 0 38.557154 15.120372 38.557154 38.061874 0 22.846334-15.356756 38.156018-38.557154 38.156018-23.107277 0-46.260603-15.309684-46.260603-38.156018C452.368366 300.994262 475.522716 285.87389 498.62897 285.87389zM283.016307 362.090758c-23.107277 0-46.402843-15.309684-46.402843-38.156018 0-22.941502 23.295566-38.061874 46.402843-38.061874 23.081695 0 38.46301 15.120372 38.46301 38.061874C321.479317 346.782098 306.098002 362.090758 283.016307 362.090758zM945.448458 606.151333c0-121.888048-123.258255-221.236753-261.683954-221.236753-146.57838 0-262.015505 99.348706-262.015505 221.236753 0 122.06508 115.437126 221.200938 262.015505 221.200938 30.66644 0 61.617359-7.609305 92.423993-15.262612l84.513836 45.786813-23.178909-76.17082C899.379213 735.776599 945.448458 674.90216 945.448458 606.151333zM598.803483 567.994292c-15.332197 0-30.807656-15.096836-30.807656-30.501688 0-15.190981 15.47546-30.477129 30.807656-30.477129 23.295566 0 38.558178 15.286148 38.558178 30.477129C637.361661 552.897456 622.099049 567.994292 598.803483 567.994292zM768.25071 567.994292c-15.213493 0-30.594809-15.096836-30.594809-30.501688 0-15.190981 15.381315-30.477129 30.594809-30.477129 23.107277 0 38.558178 15.286148 38.558178 30.477129C806.808888 552.897456 791.357987 567.994292 768.25071 567.994292z' p-id='2369'%3E%3C/path%3E%3C/svg%3E\");\n        }\n      }\n\n      &.phone-icon {\n        background-color: #ca5147;\n        position: relative;\n\n        &::before {\n          content: \"\";\n          position: absolute;\n          top: 50%;\n          left: 50%;\n          transform: translate(-50%, -50%);\n          width: 50rpx;\n          height: 50rpx;\n          background-color: #ffffff;\n          mask-size: cover;\n          -webkit-mask-size: cover;\n          mask-repeat: no-repeat;\n          -webkit-mask-repeat: no-repeat;\n          mask-position: center;\n          -webkit-mask-position: center;\n          mask-image: url(\"data:image/svg+xml,%3Csvg t='1682517275274' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='3196' width='200' height='200'%3E%3Cpath d='M736.277333 927.808c-0.362667 0-0.704 0.021333-1.066667 0.021333-61.557333 0-150.997333-33.28-250.346666-93.44-73.792-44.736-148.437333-102.912-210.112-164.586666-61.674667-61.674667-119.850667-136.32-164.586667-210.112-60.16-99.349333-93.461333-188.789333-93.461333-250.368 0-36.202667 8.661333-62.698667 27.477333-81.493334 13.653333-13.674667 34.752-29.12 78.805334-29.12 4.117333 0 8.384 0.170667 12.821333 0.512 29.589333 2.24 53.909333 25.28 78.72 74.176 13.909333 27.477333 27.477333 59.136 40.448 94.101334 8.832 23.722667 13.269333 47.466667 13.269333 70.805333 0 24.234667-5.12 44.437333-15.317333 60.202667-7.232 11.242667-16.192 22.314667-26.773333 33.024-3.989333 4.053333-4.373333 9.173333-3.626667 12.629333 15.957333 72.746667 58.026667 137.173333 125.162667 191.637333l1.066666 0.874667c30.613333 24.682667 62.250667 44.8 94.122667 59.904 7.616 3.626667 15.338667 3.221333 20.458667-1.066667a756.992 756.992 0 0 0 52.586666-44.373333c11.498667-11.157333 26.538667-17.301333 44.693334-17.301333 18.496 0 38.890667 6.421333 60.693333 19.072 29.546667 17.216 57.130667 37.162667 79.402667 57.472 44.672 40.746667 58.922667 70.122667 58.922666 122.346666 0 26.88-5.973333 47.957333-19.114666 67.413334-13.226667 19.562667-32.426667 38.826667-57.130667 57.216-18.816 13.973333-39.850667 20.949333-62.570667 20.949333z' p-id='3197'%3E%3C/path%3E%3C/svg%3E\");\n          -webkit-mask-image: url(\"data:image/svg+xml,%3Csvg t='1682517275274' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='3196' width='200' height='200'%3E%3Cpath d='M736.277333 927.808c-0.362667 0-0.704 0.021333-1.066667 0.021333-61.557333 0-150.997333-33.28-250.346666-93.44-73.792-44.736-148.437333-102.912-210.112-164.586666-61.674667-61.674667-119.850667-136.32-164.586667-210.112-60.16-99.349333-93.461333-188.789333-93.461333-250.368 0-36.202667 8.661333-62.698667 27.477333-81.493334 13.653333-13.674667 34.752-29.12 78.805334-29.12 4.117333 0 8.384 0.170667 12.821333 0.512 29.589333 2.24 53.909333 25.28 78.72 74.176 13.909333 27.477333 27.477333 59.136 40.448 94.101334 8.832 23.722667 13.269333 47.466667 13.269333 70.805333 0 24.234667-5.12 44.437333-15.317333 60.202667-7.232 11.242667-16.192 22.314667-26.773333 33.024-3.989333 4.053333-4.373333 9.173333-3.626667 12.629333 15.957333 72.746667 58.026667 137.173333 125.162667 191.637333l1.066666 0.874667c30.613333 24.682667 62.250667 44.8 94.122667 59.904 7.616 3.626667 15.338667 3.221333 20.458667-1.066667a756.992 756.992 0 0 0 52.586666-44.373333c11.498667-11.157333 26.538667-17.301333 44.693334-17.301333 18.496 0 38.890667 6.421333 60.693333 19.072 29.546667 17.216 57.130667 37.162667 79.402667 57.472 44.672 40.746667 58.922667 70.122667 58.922666 122.346666 0 26.88-5.973333 47.957333-19.114666 67.413334-13.226667 19.562667-32.426667 38.826667-57.130667 57.216-18.816 13.973333-39.850667 20.949333-62.570667 20.949333z' p-id='3197'%3E%3C/path%3E%3C/svg%3E\");\n        }\n      }\n    }\n  }\n}\n\n/* 注册表单样式 */\n.register-form {\n  position: relative;\n  z-index: 1;\n  padding-top: 250rpx; /* 增加顶部padding，使表单位置下移 */\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  \n  .input-item {\n    height: 80rpx; /* 减小高度 */\n    background-color: rgba(255, 255, 255, 0.05);\n    border-radius: 45rpx;\n    margin-bottom: 15rpx; /* 减少间距 */\n    padding: 0 40rpx;\n    display: flex;\n    align-items: center;\n\n    input {\n      flex: 1;\n      height: 100%;\n      color: #ffffff;\n      font-size: 28rpx; /* Reduced from 30rpx */\n    }\n\n    .placeholder {\n      color: rgba(255, 255, 255, 0.4);\n    }\n\n    &.verification-code {\n      padding-right: 0;\n\n      .get-code-btn {\n        height: 60rpx;\n        min-width: 180rpx;\n        background: linear-gradient(90deg, #0ab9fd, #2b79fb);\n        border-radius: 30rpx;\n        margin-right: 20rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n\n        text {\n          color: #ffffff;\n          font-size: 24rpx;\n          white-space: nowrap;\n        }\n\n        &.disabled {\n          background: rgba(255, 255, 255, 0.2);\n        }\n      }\n    }\n  }\n\n  .register-btn {\n    margin-top: 20rpx; /* 减少上边距 */\n    margin-bottom: 20rpx; /* 减少下边距 */\n    height: 90rpx; /* Reduced from 100rpx */\n    background: linear-gradient(90deg, #0ab9fd, #2b79fb);\n    border-radius: 45rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    color:#2b79fb;\n    text {\n      color: #ffffff;\n      font-size: 32rpx;\n      font-weight: 500;\n    }\n  }\n\n  .switch-action {\n    text-align: center;\n    font-size: 28rpx;\n    color: rgba(255, 255, 255, 0.6);\n    margin-bottom: 20rpx;\n  }\n}\n\n/* 底部注册链接 */\n.register-link {\n  color: #0abcfd;\n  margin-left: 20rpx;\n}\n\n/* 底部协议 */\n.agreement {\n  position: relative; /* 改为relative，不固定位置 */\n  margin-top: auto; /* 自动占据剩余空间 */\n  padding: 30rpx 0; /* 添加上下padding */\n  left: 0;\n  right: 0;\n  text-align: center;\n  font-size: 24rpx;\n  display: flex;\n  justify-content: center;\n  flex-wrap: wrap;\n  color: rgba(255, 255, 255, 0.6);\n\n  .agreement-text,\n  .agreement-link,\n  .register-link {\n    margin: 0 4rpx;\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaan<PERSON>_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/user/login.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "testApiConnection", "userApi"], "mappings": ";;;AAsKA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,cAAc;AAAA;AAAA,MACd,UAAU;AAAA;AAAA,MACV,kBAAkB;AAAA;AAAA,MAClB,cAAc;AAAA;AAAA,MACd,cAAc;AAAA;AAAA,MACd,WAAW;AAAA;AAAA,MACX,cAAc;AAAA,QACZ,UAAU;AAAA,QACV,UAAU;AAAA,QACV,iBAAiB;AAAA,QACjB,MAAM;AAAA,QACN,OAAO;AAAA,QACP,kBAAkB;AAAA,MACnB;AAAA;EAEJ;AAAA,EACD,SAAS;AAEP,SAAK,mBAAkB;AAEvB,SAAK,0BAAyB;AAAA,EAC/B;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,MAAM,qBAAqB;AACzB,UAAI;AAEFA,sBAAAA,MAAY,MAAA,OAAA,+BAAA,cAAc;AAC1B,aAAK,eAAe,MAAMC,UAAAA;AAC1BD,sBAAY,MAAA,MAAA,OAAA,+BAAA,cAAc,KAAK,YAAY;AAE3C,YAAI,CAAC,KAAK,cAAc;AACtBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACZ,CAAC;AAAA,QACH;AAAA,MACA,SAAO,KAAK;AACZA,sBAAc,MAAA,MAAA,SAAA,+BAAA,cAAc,GAAG;AAC/B,aAAK,eAAe;AAAA,MACtB;AAAA,IACD;AAAA;AAAA,IAGD,4BAA4B;AAC1B,UAAI;AACF,cAAM,oBAAoBA,cAAAA,MAAI,eAAe,mBAAmB;AAChE,cAAM,qBAAqBA,cAAAA,MAAI,eAAe,oBAAoB;AAClE,cAAM,eAAeA,cAAAA,MAAI,eAAe,kBAAkB;AAE1D,YAAI,gBAAgB,qBAAqB,oBAAoB;AAC3D,eAAK,eAAe;AACpB,eAAK,WAAW;AAChB,eAAK,mBAAmB;AACxBA,wBAAAA,MAAY,MAAA,OAAA,+BAAA,YAAY;AAAA,QAC1B;AAAA,MACA,SAAO,KAAK;AACZA,sBAAA,MAAA,MAAA,SAAA,+BAAc,gBAAgB,GAAG;AAAA,MACnC;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB;AAChB,UAAI;AACF,YAAI,KAAK,kBAAkB;AACzBA,wBAAAA,MAAI,eAAe,qBAAqB,KAAK,YAAY;AACzDA,wBAAAA,MAAI,eAAe,sBAAsB,KAAK,QAAQ;AACtDA,wBAAAA,MAAI,eAAe,oBAAoB,IAAI;AAC3CA,wBAAAA,MAAA,MAAA,OAAA,+BAAY,SAAS;AAAA,eAChB;AAELA,8BAAI,kBAAkB,mBAAmB;AACzCA,8BAAI,kBAAkB,oBAAoB;AAC1CA,8BAAI,kBAAkB,kBAAkB;AACxCA,wBAAAA,MAAY,MAAA,OAAA,+BAAA,YAAY;AAAA,QAC1B;AAAA,MACA,SAAO,KAAK;AACZA,sBAAA,MAAA,MAAA,SAAA,+BAAc,aAAa,GAAG;AAAA,MAChC;AAAA,IACD;AAAA;AAAA,IAGD,yBAAyB;AACvB,WAAK,mBAAmB,CAAC,KAAK;AAE9B,UAAI,CAAC,KAAK,kBAAkB;AAC1B,YAAI;AACFA,8BAAI,kBAAkB,mBAAmB;AACzCA,8BAAI,kBAAkB,oBAAoB;AAC1CA,8BAAI,kBAAkB,kBAAkB;AACxCA,wBAAAA,MAAY,MAAA,OAAA,+BAAA,YAAY;AAAA,QACxB,SAAO,KAAK;AACZA,wBAAc,MAAA,MAAA,SAAA,+BAAA,aAAa,GAAG;AAAA,QAChC;AAAA,MACF;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AAEZ,UAAI,CAAC,KAAK,aAAa,QAAQ;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACZ,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,SAAS,QAAQ;AACzBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACZ,CAAC;AACD;AAAA,MACF;AAGAA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAEDA,sEAAY,aAAa,KAAK,YAAY;AAG1C,YAAM,UAAU,YAAY,KAAK,KAAK,YAAY;AAGlDA,oBAAAA,MAAI,QAAQ;AAAA,QACV,KAAK,GAAG,KAAK,QAAQ;AAAA,QACrB,QAAQ;AAAA,QACR,MAAM;AAAA,UACJ,UAAU,KAAK;AAAA,UACf,UAAU,KAAK;AAAA,UACf,WAAW,UAAU,UAAU;AAAA;AAAA,QAChC;AAAA,QACD,QAAQ;AAAA,UACN,gBAAgB;AAAA,QACjB;AAAA,QACD,iBAAiB;AAAA,QACjB,SAAS,CAAC,QAAQ;;AAChBA,wBAAG,MAAC,YAAW;AACfA,wBAAY,MAAA,MAAA,OAAA,+BAAA,SAAS,GAAG;AAGxB,cAAI,IAAI,eAAe,OAAO,IAAI,KAAK,SAAS,KAAK;AAEnD,iBAAK,gBAAe;AAEpB,kBAAM,EAAE,OAAO,QAAQ,MAAM,aAAa,eAAe,YAAY,cAAc,IAAI,IAAI,KAAK;AAChGA,0BAAY,MAAA,MAAA,OAAA,+BAAA,iBAAiB,MAAM;AAEnCA,0BAAAA,MAAI,eAAe,SAAS,KAAK;AACjCA,0BAAAA,MAAI,eAAe,UAAU,MAAM;AACnCA,0BAAAA,MAAI,eAAe,YAAY,IAAI;AACnCA,0BAAAA,MAAI,eAAe,mBAAmB,WAAW;AACjDA,0BAAAA,MAAI,eAAe,cAAc,cAAc,EAAE;AAGjDA,0BAAAA,MAAI,eAAe,iBAAiB,iBAAiB,CAAC;AAEtDA,0BAAAA,MAAI,eAAe,iBAAiB,iBAAiB,CAAC;AACtDA,0BAAY,MAAA,MAAA,OAAA,+BAAA,cAAc,iBAAiB,CAAC;AAC5CA,4EAAY,YAAY,iBAAiB,GAAG,IAAI;AAChDA,0BAAY,MAAA,MAAA,OAAA,+BAAA,cAAc,cAAc,GAAG;AAG3C,iBAAK,YAAW;AAGhBA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAGD,uBAAW,MAAM;AACfA,4BAAAA,MAAI,SAAS;AAAA,gBACX,KAAK;AAAA,cACP,CAAC;AAAA,YACF,GAAE,GAAI;AAAA,iBACF;AAENA,0BAAAA,MAAI,UAAU;AAAA,cACZ,SAAO,SAAI,SAAJ,mBAAU,YAAW;AAAA,cAC5B,MAAM;AAAA,YACR,CAAC;AAAA,UACF;AAAA,QACD;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAG,MAAC,YAAW;AAGfA,wBAAc,MAAA,MAAA,SAAA,+BAAA,aAAa,GAAG;AAG9B,cAAI,IAAI,OAAO,SAAS,SAAS,GAAG;AAClCA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YACZ,CAAC;AACD;AAAA,UACF;AAGA,cAAI,WAAW;AAEf,cAAI,IAAI,OAAO,SAAS,SAAS,GAAG;AAClC,uBAAW;AAAA,UACb,WAAW,IAAI,OAAO,SAAS,MAAM,GAAG;AACtC,uBAAW;AAAA,UACb;AAGAA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACZ,CAAC;AAAA,QACF;AAAA,MACH,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,cAAc;AACZE,gBAAM,QACH,YAAY,EACZ,KAAK,CAAC,QAAQ;AACb,YAAI,IAAI,SAAS,KAAK;AAEpBF,wBAAAA,MAAI,eAAe,YAAY,IAAI,IAAI;AAAA,eAClC;AACLA,wBAAA,MAAA,MAAA,SAAA,+BAAc,aAAa,GAAG;AAAA,QAChC;AAAA,OACD,EACA,MAAM,CAAC,QAAQ;AACdA,sBAAA,MAAA,MAAA,SAAA,+BAAc,eAAe,GAAG;AAAA,MAClC,CAAC;AAAA,IACJ;AAAA;AAAA,IAGD,gBAAgB;AACdA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,aAAa;AACX,WAAK,eAAe,CAAC,KAAK;AAAA,IAC3B;AAAA;AAAA,IAGD,sBAAsB;AAEpB,UAAI,KAAK,YAAY,GAAG;AACtB;AAAA,MACF;AAGA,UAAI,CAAC,KAAK,aAAa,MAAM,KAAI,GAAI;AACnCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,YAAY,KAAK,KAAK,aAAa,KAAK,GAAG;AAC9CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAGAA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAID,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AAGf,aAAK,YAAY;AACjB,cAAM,QAAQ,YAAY,MAAM;AAC9B,eAAK;AACL,cAAI,KAAK,aAAa,GAAG;AACvB,0BAAc,KAAK;AAAA,UACrB;AAAA,QACD,GAAE,GAAI;AAEPA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACF,GAAE,IAAI;AAAA,IACR;AAAA;AAAA,IAGD,iBAAiB;AAEf,UAAI,CAAC,KAAK,aAAa,MAAM,KAAI,GAAI;AACnCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,YAAY,KAAK,KAAK,aAAa,KAAK,GAAG;AAC9CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,aAAa,SAAS,KAAI,GAAI;AACtCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,aAAa,SAAS,KAAI,GAAI;AACtCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,KAAK,aAAa,aAAa,KAAK,aAAa,iBAAiB;AACpEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,aAAa,KAAK,KAAI,GAAI;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAGAA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAGDA,oBAAAA,MAAI,QAAQ;AAAA,QACV,KAAK,GAAG,KAAK,QAAQ;AAAA,QACrB,QAAQ;AAAA,QACR,MAAM;AAAA,UACJ,UAAU,KAAK,aAAa;AAAA,UAC5B,UAAU,KAAK,aAAa;AAAA,UAC5B,MAAM,KAAK,aAAa;AAAA,UACxB,OAAO,KAAK,aAAa;AAAA,QAC1B;AAAA,QACD,QAAQ;AAAA,UACN,gBAAgB;AAAA,QACjB;AAAA,QACD,SAAS,CAAC,QAAQ;;AAChBA,wBAAG,MAAC,YAAW;AACfA,wBAAY,MAAA,MAAA,OAAA,+BAAA,SAAS,GAAG;AAGxB,cAAI,IAAI,eAAe,OAAO,IAAI,KAAK,SAAS,KAAK;AAEnDA,0BAAG,MAAC,YAAW;AACxBA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACN,UAAU;AAAA,cACX,MAAM;AAAA,YACR,CAAC;AAEQ,uBAAW,MAAM;AACf,mBAAK,eAAe,KAAK,aAAa;AACtC,mBAAK,WAAW,KAAK,aAAa;AAClC,mBAAK,eAAe;AAAA,gBAClB,UAAU;AAAA,gBACV,UAAU;AAAA,gBACV,iBAAiB;AAAA,gBACjB,MAAM;AAAA,gBACN,OAAO;AAAA,gBACP,kBAAkB;AAAA;AAEpB,mBAAK,eAAe;AAAA,YACrB,GAAE,IAAI;AAAA,iBACF;AAELA,0BAAAA,MAAI,UAAU;AAAA,cACZ,SAAO,SAAI,SAAJ,mBAAU,YAAW;AAAA,cAC5B,MAAM;AAAA,cACN,UAAU;AAAA,YACZ,CAAC;AAAA,UACH;AAAA,QACD;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAG,MAAC,YAAW;AACfA,wBAAA,MAAA,MAAA,SAAA,+BAAc,WAAW,GAAG;AAE5BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACF;AAAA,MACH,CAAC;AAAA,IACF;AAAA,EACF;AACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9kBA,GAAG,WAAW,eAAe;"}