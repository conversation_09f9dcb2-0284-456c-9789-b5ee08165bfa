{"version": 3, "file": "faq.js", "sources": ["pages/user/faq.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlci9mYXEudnVl"], "sourcesContent": ["<template>\n\t<view class=\"faq-container\">\n\t\n\t\t\n\t\t<!-- 搜索框 -->\n\t\t<view class=\"search-box\">\n\t\t\t<view class=\"search-input\">\n\t\t\t\t<text class=\"search-icon iconfont icon-search\"></text>\n\t\t\t\t<input type=\"text\" v-model=\"searchKeyword\" placeholder=\"搜索问题\" @input=\"searchFaq\" />\n\t\t\t\t<text class=\"clear-icon iconfont icon-close\" v-if=\"searchKeyword\" @click=\"clearSearch\"></text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 内容区域 -->\n\t\t<view class=\"faq-content\">\n\t\t\t<view class=\"no-result\" v-if=\"filteredFaqs.length === 0 && searchKeyword\">\n\t\t\t\t<image src=\"/static/images/empty.png\" mode=\"aspectFit\"></image>\n\t\t\t\t<text>暂无相关问题</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 搜索模式：直接显示结果列表 -->\n\t\t\t<block v-if=\"searchKeyword\">\n\t\t\t\t<view class=\"faq-item\" v-for=\"(item, index) in filteredFaqs\" :key=\"index\">\n\t\t\t\t\t<view class=\"faq-question\" @click=\"toggleSearchAnswer(index)\">\n\t\t\t\t\t\t<text class=\"question-marker\">Q:</text>\n\t\t\t\t\t\t<text class=\"question-text\">{{ item.question }}</text>\n\t\t\t\t\t\t<text class=\"question-arrow\" :class=\"{ 'arrow-up': item.isOpen }\">></text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"faq-answer\" v-if=\"item.isOpen\">\n\t\t\t\t\t\t<text class=\"answer-marker\">A:</text>\n\t\t\t\t\t\t<text class=\"answer-text\">{{ item.answer }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</block>\n\t\t\t\n\t\t\t<!-- 非搜索模式：按分组显示所有问题 -->\n\t\t\t<block v-else>\n\t\t\t\t<view class=\"faq-section\" v-for=\"(group, groupIndex) in allFaqs\" :key=\"groupIndex\">\n\t\t\t\t\t<view class=\"group-title\">\n\t\t\t\t\t\t<text class=\"group-title-text\">{{ group.groupTitle }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"faq-item\" v-for=\"(item, itemIndex) in group.items\" :key=\"itemIndex\">\n\t\t\t\t\t\t<view class=\"faq-question\" @click=\"toggleAnswer(groupIndex, itemIndex)\">\n\t\t\t\t\t\t\t<text class=\"question-marker\">Q:</text>\n\t\t\t\t\t\t\t<text class=\"question-text\">{{ item.question }}</text>\n\t\t\t\t\t\t\t<text class=\"question-arrow\" :class=\"{ 'arrow-up': item.isOpen }\">></text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"faq-answer\" v-if=\"item.isOpen\">\n\t\t\t\t\t\t\t<text class=\"answer-marker\">A:</text>\n\t\t\t\t\t\t\t<text class=\"answer-text\">{{ item.answer }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</block>\n\t\t</view>\n\t\t\n\t\t<!-- 底部联系客服区域 -->\n\t\t<view class=\"feedback-section\">\n\t\t\t<text class=\"feedback-text\">没有找到您需要的答案？</text>\n\t\t\t<view class=\"feedback-btn\" @click=\"contactSupport\">联系客服</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tsearchKeyword: '',\n\t\t\tfaqs: [\n\t\t\t\t{\n\t\t\t\t\tcategoryId: 'account',\n\t\t\t\t\tgroupTitle: '账号和登录',\n\t\t\t\t\titems: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tquestion: '如何修改密码？',\n\t\t\t\t\t\t\tanswer: '您可以在个人中心 -> 安全设置 -> 密码修改中修改您的账号密码。系统会要求您输入当前密码和新密码进行验证。',\n\t\t\t\t\t\t\tisOpen: false\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tquestion: '忘记密码怎么办？',\n\t\t\t\t\t\t\tanswer: '如果您忘记了登录密码，请点击登录页面的\"忘记密码\"按钮，按照提示使用手机号码接收验证码进行密码重置。如果您没有绑定手机号，请联系系统管理员重置密码。',\n\t\t\t\t\t\t\tisOpen: false\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tquestion: '如何更换手机号？',\n\t\t\t\t\t\t\tanswer: '您可以在个人中心 -> 个人设置 -> 账号绑定中更换您的手机号。系统会向新手机号发送验证码进行验证。',\n\t\t\t\t\t\t\tisOpen: false\n\t\t\t\t\t\t}\n\t\t\t\t\t]\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tcategoryId: 'workorder',\n\t\t\t\t\tgroupTitle: '工单处理',\n\t\t\t\t\titems: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tquestion: '如何接受工单？',\n\t\t\t\t\t\t\tanswer: '工单列表中显示的\"待接单\"工单，点击进入工单详情后，点击底部的\"接单\"按钮即可接受工单。接受后，工单状态会变为\"已接单\"。',\n\t\t\t\t\t\t\tisOpen: false\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tquestion: '如何完成工单？',\n\t\t\t\t\t\t\tanswer: '在工单详情页面，点击\"完成工单\"按钮，填写维修内容、维修结果、使用的耗材及数量，选择维修时间并上传相关附件，点击提交即可完成工单。',\n\t\t\t\t\t\t\tisOpen: false\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tquestion: '如何转派工单？',\n\t\t\t\t\t\t\tanswer: '在工单详情页面，点击\"转派工单\"按钮，选择要转派的人员，填写转派原因，点击确认即可转派工单。',\n\t\t\t\t\t\t\tisOpen: false\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tquestion: '如何查看历史工单？',\n\t\t\t\t\t\t\tanswer: '在工单列表页面，可以通过顶部的状态筛选查看不同状态的工单。选择\"已完成\"标签即可查看历史完成的工单。',\n\t\t\t\t\t\t\tisOpen: false\n\t\t\t\t\t\t}\n\t\t\t\t\t]\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tcategoryId: 'patrol',\n\t\t\t\t\tgroupTitle: '巡检管理',\n\t\t\t\t\titems: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tquestion: '如何开始巡检任务？',\n\t\t\t\t\t\t\tanswer: '在巡检计划列表中，点击\"开始巡检\"按钮，系统会生成巡检任务单，按照巡检点顺序进行巡检。',\n\t\t\t\t\t\t\tisOpen: false\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tquestion: '巡检过程中如何报告问题？',\n\t\t\t\t\t\t\tanswer: '在巡检执行过程中，如发现问题，可以点击巡检项旁的\"报告问题\"按钮，填写问题描述并上传照片，系统会自动生成故障工单。',\n\t\t\t\t\t\t\tisOpen: false\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tquestion: '如何查看历史巡检记录？',\n\t\t\t\t\t\t\tanswer: '在巡检记录页面，可以查看所有已完成的巡检任务记录，包括巡检时间、巡检人员、巡检路线等信息。',\n\t\t\t\t\t\t\tisOpen: false\n\t\t\t\t\t\t}\n\t\t\t\t\t]\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tcategoryId: 'system',\n\t\t\t\t\tgroupTitle: '系统使用',\n\t\t\t\t\titems: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tquestion: '如何更新APP？',\n\t\t\t\t\t\t\tanswer: '当有新版本时，系统会自动提示更新。您也可以在\"我的\" -> \"关于系统\"中检查更新，如有新版本会显示\"立即更新\"按钮。',\n\t\t\t\t\t\t\tisOpen: false\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tquestion: '系统支持哪些附件格式？',\n\t\t\t\t\t\t\tanswer: '系统支持上传图片(JPG, PNG, GIF)和视频(MP4, MOV)格式的附件，单个附件大小不超过20MB。',\n\t\t\t\t\t\t\tisOpen: false\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tquestion: '如何清理缓存？',\n\t\t\t\t\t\t\tanswer: '在\"我的\" -> \"设置\" -> \"清除缓存\"中可以清理应用缓存数据，释放手机存储空间。注意：清除缓存不会删除您的账号信息和历史数据。',\n\t\t\t\t\t\t\tisOpen: false\n\t\t\t\t\t\t}\n\t\t\t\t\t]\n\t\t\t\t}\n\t\t\t],\n\t\t\t// 搜索结果的展开状态\n\t\t\tsearchResults: []\n\t\t}\n\t},\n\tcomputed: {\n\t\t// 所有FAQ分组\n\t\tallFaqs() {\n\t\t\treturn this.faqs;\n\t\t},\n\t\t\n\t\t// 搜索结果\n\t\tfilteredFaqs() {\n\t\t\tif (this.searchKeyword) {\n\t\t\t\t// 搜索模式：搜索所有分类\n\t\t\t\tconst keyword = this.searchKeyword.toLowerCase();\n\t\t\t\tconst result = [];\n\t\t\t\t\n\t\t\t\tthis.faqs.forEach(group => {\n\t\t\t\t\tgroup.items.forEach(item => {\n\t\t\t\t\t\tif (item.question.toLowerCase().includes(keyword) || \n\t\t\t\t\t\t\titem.answer.toLowerCase().includes(keyword)) {\n\t\t\t\t\t\t\t// 创建一个新对象，避免修改原始数据\n\t\t\t\t\t\t\tresult.push({\n\t\t\t\t\t\t\t\t...item,\n\t\t\t\t\t\t\t\tisOpen: false // 默认不展开\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 更新搜索结果的展开状态\n\t\t\t\tthis.searchResults = result;\n\t\t\t\treturn result;\n\t\t\t}\n\t\t\treturn [];\n\t\t}\n\t},\n\tmethods: {\n\t\t// 切换问题答案展示（非搜索模式）\n\t\ttoggleAnswer(groupIndex, itemIndex) {\n\t\t\tthis.faqs[groupIndex].items[itemIndex].isOpen = !this.faqs[groupIndex].items[itemIndex].isOpen;\n\t\t},\n\t\t\n\t\t// 切换搜索结果中的问题答案展示\n\t\ttoggleSearchAnswer(index) {\n\t\t\tif (this.searchKeyword && this.filteredFaqs[index]) {\n\t\t\t\tthis.filteredFaqs[index].isOpen = !this.filteredFaqs[index].isOpen;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 重置所有问题的展开状态\n\t\tresetOpenState() {\n\t\t\tthis.faqs.forEach(group => {\n\t\t\t\tgroup.items.forEach(item => {\n\t\t\t\t\titem.isOpen = false;\n\t\t\t\t});\n\t\t\t});\n\t\t\t// 同时重置搜索结果的展开状态\n\t\t\tif (this.searchResults) {\n\t\t\t\tthis.searchResults.forEach(item => {\n\t\t\t\t\titem.isOpen = false;\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 搜索FAQ\n\t\tsearchFaq() {\n\t\t\t// 重置展开状态\n\t\t\tthis.resetOpenState();\n\t\t},\n\t\t\n\t\t// 清除搜索\n\t\tclearSearch() {\n\t\t\tthis.searchKeyword = '';\n\t\t\tthis.resetOpenState();\n\t\t},\n\t\t\n\t\t// 联系客服\n\t\tcontactSupport() {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '联系客服',\n\t\t\t\tcontent: '客服热线：029-85396651\\n工作时间：周一至周五 9:00-18:00',\n\t\t\t\tshowCancel: false,\n\t\t\t\tconfirmText: '我知道了'\n\t\t\t});\n\t\t}\n\t}\n}\n</script>\n\n<style lang=\"scss\">\n.faq-container {\n\tbackground-color: #f5f7fa;\n\tmin-height: 100vh;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.search-box {\n\tpadding: 20rpx 30rpx;\n\tbackground-color: #fff;\n\tposition: sticky;\n\ttop: 0;\n\tz-index: 100;\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\n\t\n\t.search-input {\n\t\tposition: relative;\n\t\theight: 80rpx;\n\t\tbackground-color: #f5f7fa;\n\t\tborder-radius: 40rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 0 30rpx;\n\t\t\n\t\t.search-icon {\n\t\t\tfont-size: 32rpx;\n\t\t\tcolor: #999;\n\t\t\tmargin-right: 10rpx;\n\t\t}\n\t\t\n\t\tinput {\n\t\t\tflex: 1;\n\t\t\theight: 80rpx;\n\t\t\tfont-size: 28rpx;\n\t\t}\n\t\t\n\t\t.clear-icon {\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #999;\n\t\t\tpadding: 10rpx;\n\t\t}\n\t}\n}\n\n.faq-content {\n\tflex: 1;\n\tpadding: 20rpx 0;\n\t\n\t.faq-section {\n\t\tmargin-bottom: 20rpx;\n\t\t\n\t\t.group-title {\n\t\t\tposition: relative;\n\t\t\tpadding: 20rpx 30rpx;\n\t\t\tbackground-color: #f0f5ff;\n\t\t\tborder-left: 8rpx solid #1890ff;\n\t\t\t\n\t\t\t.group-title-text {\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tfont-weight: bold;\n\t\t\t\tcolor: #333;\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.no-result {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding-top: 100rpx;\n\t\t\n\t\timage {\n\t\t\twidth: 200rpx;\n\t\t\theight: 200rpx;\n\t\t\tmargin-bottom: 20rpx;\n\t\t}\n\t\t\n\t\ttext {\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #999;\n\t\t}\n\t}\n}\n\n.faq-item {\n\tbackground-color: #fff;\n\tmargin-bottom: 2rpx;\n\t\n\t.faq-question {\n\t\tpadding: 30rpx;\n\t\tdisplay: flex;\n\t\talign-items: flex-start;\n\t\tposition: relative;\n\t\t\n\t\t.question-marker {\n\t\t\tcolor: $uni-color-primary;\n\t\t\tfont-weight: bold;\n\t\t\tfont-size: 30rpx;\n\t\t\tmargin-right: 20rpx;\n\t\t\tflex-shrink: 0;\n\t\t}\n\t\t\n\t\t.question-text {\n\t\t\tflex: 1;\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #333;\n\t\t\tline-height: 1.6;\n\t\t}\n\t\t\n\t\t.question-arrow {\n\t\t\tfont-size: 24rpx;\n\t\t\tcolor: #999;\n\t\t\tmargin-left: 20rpx;\n\t\t\ttransition: transform 0.3s;\n\t\t\ttransform: rotate(90deg);\n\t\t\t\n\t\t\t&.arrow-up {\n\t\t\t\ttransform: rotate(-90deg);\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.faq-answer {\n\t\tpadding: 0 30rpx 30rpx;\n\t\tdisplay: flex;\n\t\talign-items: flex-start;\n\t\tbackground-color: #f9f9f9;\n\t\t\n\t\t.answer-marker {\n\t\t\tcolor: #f5222d;\n\t\t\tfont-weight: bold;\n\t\t\tfont-size: 30rpx;\n\t\t\tmargin-right: 20rpx;\n\t\t\tflex-shrink: 0;\n\t\t}\n\t\t\n\t\t.answer-text {\n\t\t\tflex: 1;\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #666;\n\t\t\tline-height: 1.8;\n\t\t}\n\t}\n}\n\n.feedback-section {\n\tpadding: 30rpx;\n\tbackground-color: #fff;\n\tborder-top: 1rpx solid #eee;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tflex-direction: column;\n\twidth: 100%;\n\tbox-sizing: border-box;\n\tmargin-top: 20rpx;\n\t\n\t.feedback-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t.feedback-btn {\n\t\tpadding: 15rpx 60rpx;\n\t\tbackground-color: $uni-color-primary;\n\t\tcolor: #fff;\n\t\tfont-size: 28rpx;\n\t\tborder-radius: 40rpx;\n\t\t\n\t\t&:active {\n\t\t\topacity: 0.8;\n\t\t}\n\t}\n}\n</style> ", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/user/faq.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAkEA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,eAAe;AAAA,MACf,MAAM;AAAA,QACL;AAAA,UACC,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,OAAO;AAAA,YACN;AAAA,cACC,UAAU;AAAA,cACV,QAAQ;AAAA,cACR,QAAQ;AAAA,YACR;AAAA,YACD;AAAA,cACC,UAAU;AAAA,cACV,QAAQ;AAAA,cACR,QAAQ;AAAA,YACR;AAAA,YACD;AAAA,cACC,UAAU;AAAA,cACV,QAAQ;AAAA,cACR,QAAQ;AAAA,YACT;AAAA,UACD;AAAA,QACA;AAAA,QACD;AAAA,UACC,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,OAAO;AAAA,YACN;AAAA,cACC,UAAU;AAAA,cACV,QAAQ;AAAA,cACR,QAAQ;AAAA,YACR;AAAA,YACD;AAAA,cACC,UAAU;AAAA,cACV,QAAQ;AAAA,cACR,QAAQ;AAAA,YACR;AAAA,YACD;AAAA,cACC,UAAU;AAAA,cACV,QAAQ;AAAA,cACR,QAAQ;AAAA,YACR;AAAA,YACD;AAAA,cACC,UAAU;AAAA,cACV,QAAQ;AAAA,cACR,QAAQ;AAAA,YACT;AAAA,UACD;AAAA,QACA;AAAA,QACD;AAAA,UACC,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,OAAO;AAAA,YACN;AAAA,cACC,UAAU;AAAA,cACV,QAAQ;AAAA,cACR,QAAQ;AAAA,YACR;AAAA,YACD;AAAA,cACC,UAAU;AAAA,cACV,QAAQ;AAAA,cACR,QAAQ;AAAA,YACR;AAAA,YACD;AAAA,cACC,UAAU;AAAA,cACV,QAAQ;AAAA,cACR,QAAQ;AAAA,YACT;AAAA,UACD;AAAA,QACA;AAAA,QACD;AAAA,UACC,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,OAAO;AAAA,YACN;AAAA,cACC,UAAU;AAAA,cACV,QAAQ;AAAA,cACR,QAAQ;AAAA,YACR;AAAA,YACD;AAAA,cACC,UAAU;AAAA,cACV,QAAQ;AAAA,cACR,QAAQ;AAAA,YACR;AAAA,YACD;AAAA,cACC,UAAU;AAAA,cACV,QAAQ;AAAA,cACR,QAAQ;AAAA,YACT;AAAA,UACD;AAAA,QACD;AAAA,MACA;AAAA;AAAA,MAED,eAAe,CAAC;AAAA,IACjB;AAAA,EACA;AAAA,EACD,UAAU;AAAA;AAAA,IAET,UAAU;AACT,aAAO,KAAK;AAAA,IACZ;AAAA;AAAA,IAGD,eAAe;AACd,UAAI,KAAK,eAAe;AAEvB,cAAM,UAAU,KAAK,cAAc,YAAW;AAC9C,cAAM,SAAS,CAAA;AAEf,aAAK,KAAK,QAAQ,WAAS;AAC1B,gBAAM,MAAM,QAAQ,UAAQ;AAC3B,gBAAI,KAAK,SAAS,YAAW,EAAG,SAAS,OAAO,KAC/C,KAAK,OAAO,YAAa,EAAC,SAAS,OAAO,GAAG;AAE7C,qBAAO,KAAK;AAAA,gBACX,GAAG;AAAA,gBACH,QAAQ;AAAA;AAAA,cACT,CAAC;AAAA,YACF;AAAA,UACD,CAAC;AAAA,QACF,CAAC;AAGD,aAAK,gBAAgB;AACrB,eAAO;AAAA,MACR;AACA,aAAO;IACR;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,aAAa,YAAY,WAAW;AACnC,WAAK,KAAK,UAAU,EAAE,MAAM,SAAS,EAAE,SAAS,CAAC,KAAK,KAAK,UAAU,EAAE,MAAM,SAAS,EAAE;AAAA,IACxF;AAAA;AAAA,IAGD,mBAAmB,OAAO;AACzB,UAAI,KAAK,iBAAiB,KAAK,aAAa,KAAK,GAAG;AACnD,aAAK,aAAa,KAAK,EAAE,SAAS,CAAC,KAAK,aAAa,KAAK,EAAE;AAAA,MAC7D;AAAA,IACA;AAAA;AAAA,IAGD,iBAAiB;AAChB,WAAK,KAAK,QAAQ,WAAS;AAC1B,cAAM,MAAM,QAAQ,UAAQ;AAC3B,eAAK,SAAS;AAAA,QACf,CAAC;AAAA,MACF,CAAC;AAED,UAAI,KAAK,eAAe;AACvB,aAAK,cAAc,QAAQ,UAAQ;AAClC,eAAK,SAAS;AAAA,QACf,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,YAAY;AAEX,WAAK,eAAc;AAAA,IACnB;AAAA;AAAA,IAGD,cAAc;AACb,WAAK,gBAAgB;AACrB,WAAK,eAAc;AAAA,IACnB;AAAA;AAAA,IAGD,iBAAiB;AAChBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,aAAa;AAAA,MACd,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvPA,GAAG,WAAW,eAAe;"}