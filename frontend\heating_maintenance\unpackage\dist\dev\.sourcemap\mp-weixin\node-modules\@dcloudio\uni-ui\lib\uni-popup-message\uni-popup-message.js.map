{"version": 3, "file": "uni-popup-message.js", "sources": ["node_modules/@dcloudio/uni-ui/lib/uni-popup-message/uni-popup-message.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RTovdGFpYm9fY29tcGFueS90Yl9wcm9qZWN0L3NoYWFueGlfamllbWluZ19uZXdfZW5lcmd5X2NvbXBhbnkvNC1Tb3VyY2UvYXBwL2Zyb250ZW5kL2hlYXRpbmdfbWFpbnRlbmFuY2Uvbm9kZV9tb2R1bGVzL0BkY2xvdWRpby91bmktdWkvbGliL3VuaS1wb3B1cC1tZXNzYWdlL3VuaS1wb3B1cC1tZXNzYWdlLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"uni-popup-message\">\r\n\t\t<view class=\"uni-popup-message__box fixforpc-width\" :class=\"'uni-popup__'+type\">\r\n\t\t\t<slot>\r\n\t\t\t\t<text class=\"uni-popup-message-text\" :class=\"'uni-popup__'+type+'-text'\">{{message}}</text>\r\n\t\t\t</slot>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport popup from '../uni-popup/popup.js'\r\n\t/**\r\n\t * PopUp 弹出层-消息提示\r\n\t * @description 弹出层-消息提示\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=329\r\n\t * @property {String} type = [success|warning|info|error] 主题样式\r\n\t *  @value success 成功\r\n\t * \t@value warning 提示\r\n\t * \t@value info 消息\r\n\t * \t@value error 错误\r\n\t * @property {String} message 消息提示文字\r\n\t * @property {String} duration 显示时间，设置为 0 则不会自动关闭\r\n\t */\r\n\r\n\texport default {\r\n\t\tname: 'uniPopupMessage',\r\n\t\tmixins:[popup],\r\n\t\tprops: {\r\n\t\t\t/**\r\n\t\t\t * 主题 success/warning/info/error\t  默认 success\r\n\t\t\t */\r\n\t\t\ttype: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'success'\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 消息文字\r\n\t\t\t */\r\n\t\t\tmessage: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 显示时间，设置为 0 则不会自动关闭\r\n\t\t\t */\r\n\t\t\tduration: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 3000\r\n\t\t\t},\r\n\t\t\tmaskShow:{\r\n\t\t\t\ttype:Boolean,\r\n\t\t\t\tdefault:false\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.popup.maskShow = this.maskShow\r\n\t\t\tthis.popup.messageChild = this\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\ttimerClose(){\r\n\t\t\t\tif(this.duration === 0) return\r\n\t\t\t\tclearTimeout(this.timer) \r\n\t\t\t\tthis.timer = setTimeout(()=>{\r\n\t\t\t\t\tthis.popup.close()\r\n\t\t\t\t},this.duration)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style lang=\"scss\" >\r\n\t.uni-popup-message {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.uni-popup-message__box {\r\n\t\tbackground-color: #e1f3d8;\r\n\t\tpadding: 10px 15px;\r\n\t\tborder-color: #eee;\r\n\t\tborder-style: solid;\r\n\t\tborder-width: 1px;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t@media screen and (min-width: 500px) {\r\n\t\t.fixforpc-width {\r\n\t\t\tmargin-top: 20px;\r\n\t\t\tborder-radius: 4px;\r\n\t\t\tflex: none;\r\n\t\t\tmin-width: 380px;\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\tmax-width: 50%;\r\n\t\t\t/* #endif */\r\n\t\t\t/* #ifdef APP-NVUE */\r\n\t\t\tmax-width: 500px;\r\n\t\t\t/* #endif */\r\n\t\t}\r\n\t}\r\n\r\n\t.uni-popup-message-text {\r\n\t\tfont-size: 14px;\r\n\t\tpadding: 0;\r\n\t}\r\n\r\n\t.uni-popup__success {\r\n\t\tbackground-color: #e1f3d8;\r\n\t}\r\n\r\n\t.uni-popup__success-text {\r\n\t\tcolor: #67C23A;\r\n\t}\r\n\r\n\t.uni-popup__warn {\r\n\t\tbackground-color: #faecd8;\r\n\t}\r\n\r\n\t.uni-popup__warn-text {\r\n\t\tcolor: #E6A23C;\r\n\t}\r\n\r\n\t.uni-popup__error {\r\n\t\tbackground-color: #fde2e2;\r\n\t}\r\n\r\n\t.uni-popup__error-text {\r\n\t\tcolor: #F56C6C;\r\n\t}\r\n\r\n\t.uni-popup__info {\r\n\t\tbackground-color: #F2F6FC;\r\n\t}\r\n\r\n\t.uni-popup__info-text {\r\n\t\tcolor: #909399;\r\n\t}\r\n</style>\r\n", "import Component from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/node_modules/@dcloudio/uni-ui/lib/uni-popup-message/uni-popup-message.vue'\nwx.createComponent(Component)"], "names": ["popup"], "mappings": ";;AAyBC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAO,CAACA,cAAAA,KAAK;AAAA,EACb,OAAO;AAAA;AAAA;AAAA;AAAA,IAIN,MAAM;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA;AAAA;AAAA,IAID,SAAS;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA;AAAA;AAAA,IAID,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,UAAS;AAAA,MACR,MAAK;AAAA,MACL,SAAQ;AAAA,IACT;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO,CAAC;AAAA,EACR;AAAA,EACD,UAAU;AACT,SAAK,MAAM,WAAW,KAAK;AAC3B,SAAK,MAAM,eAAe;AAAA,EAC1B;AAAA,EACD,SAAS;AAAA,IACR,aAAY;AACX,UAAG,KAAK,aAAa;AAAG;AACxB,mBAAa,KAAK,KAAK;AACvB,WAAK,QAAQ,WAAW,MAAI;AAC3B,aAAK,MAAM,MAAM;AAAA,MAClB,GAAE,KAAK,QAAQ;AAAA,IAChB;AAAA,EACD;AACD;;;;;;;;;ACtED,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}