package com.heating.dto.patrol;

import java.time.LocalDate;
import lombok.Data;

/**
 * 巡检记录列表请求DTO
 */
@Data
public class PatrolRecordListRequest {
    /**
     * 巡检状态
     */
    private String status;

    /**
     * 执行人ID
     */
    private Long executorId;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

    /**
     * 页码，默认为1
     */
    private Integer page = 1;

    /**
     * 每页大小，默认为10
     */
    private Integer pageSize = 10;

    /**
     * 项目小区权限，格式为"1,3"或"0"
     * 包含0表示全部小区权限，否则根据权限查询对应的项目小区
     */
    private String heatUnitId;
}