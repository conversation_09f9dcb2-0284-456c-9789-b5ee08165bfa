"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const BaseTabBar = () => "../../components/BaseTabBar.js";
const _sfc_main = {
  components: {
    BaseTabBar
  },
  data() {
    return {
      searchKeyword: "",
      currentTab: "all",
      statusTabs: [
        { label: "全部", value: "all" },
        { label: "正常", value: "online" },
        { label: "异常", value: "warning" },
        { label: "故障", value: "fault" }
      ],
      stationList: [],
      page: 1,
      pageSize: 10,
      hasMore: true,
      loading: false
    };
  },
  onLoad() {
    this.loadStationList();
  },
  methods: {
    // 加载换热站列表
    loadStationList() {
      if (this.loading)
        return;
      this.loading = true;
      const params = {
        status: this.currentTab !== "all" ? this.currentTab : "",
        // online/offline/fault，可选
        keyword: this.searchKeyword || ""
        // 搜索关键词，可选
      };
      common_vendor.index.__f__("log", "at pages/hes/list.vue:143", "加载换热站列表，参数:", params);
      utils_api.heatingStationApi.getList(params).then((res) => {
        common_vendor.index.__f__("log", "at pages/hes/list.vue:149", "换热站列表获取成功:", res);
        if (res && res.data && res.data.list) {
          const processedList = res.data.list.map((station) => {
            return {
              ...station,
              // 如果API没有返回这些字段，添加示例数据
              last_connect_time: station.last_connect_time || (/* @__PURE__ */ new Date()).toISOString(),
              operation_mode: station.operation_mode || (Math.random() > 0.5 ? "fixed" : "variable"),
              primary_supply_temp: station.primary_supply_temp || Math.floor(Math.random() * 15) + 70,
              primary_return_temp: station.primary_return_temp || Math.floor(Math.random() * 10) + 50,
              secondary_supply_temp: station.secondary_supply_temp || Math.floor(Math.random() * 10) + 50,
              secondary_return_temp: station.secondary_return_temp || Math.floor(Math.random() * 10) + 30,
              enabled: station.enabled !== void 0 ? station.enabled : true
            };
          });
          if (this.page === 1) {
            this.stationList = processedList;
          } else {
            this.stationList = [...this.stationList, ...processedList];
          }
          this.hasMore = res.data.list.length >= this.pageSize;
        } else {
          if (this.page === 1) {
            this.stationList = [];
          }
          this.hasMore = false;
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/hes/list.vue:191", "加载换热站列表失败:", err);
        common_vendor.index.showToast({
          title: "加载失败，请重试",
          icon: "none"
        });
      }).finally(() => {
        this.loading = false;
      });
    },
    // 格式化日期时间
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr)
        return "暂无数据";
      try {
        const date = new Date(dateTimeStr);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        const seconds = String(date.getSeconds()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/hes/list.vue:216", "日期格式化错误:", e);
        return dateTimeStr || "暂无数据";
      }
    },
    // 获取卡片颜色类
    getCardColorClass(station) {
      if (!station.enabled)
        return "gray-card";
      if (station.status === "online")
        return "green-card";
      if (station.status === "offline")
        return "yellow-card";
      return "blue-card";
    },
    // 搜索处理
    handleSearch() {
      common_vendor.index.__f__("log", "at pages/hes/list.vue:232", `搜索关键词: ${this.searchKeyword}`);
      this.page = 1;
      this.loadStationList();
    },
    // 清除搜索
    clearSearch() {
      this.searchKeyword = "";
      this.page = 1;
      this.loadStationList();
    },
    // 切换状态标签
    switchTab(tab) {
      if (this.currentTab === tab)
        return;
      this.currentTab = tab;
      this.page = 1;
      this.loadStationList();
      common_vendor.index.__f__("log", "at pages/hes/list.vue:250", `切换到标签: ${tab}`);
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        online: "在线",
        offline: "离线",
        warning: "异常",
        fault: "故障"
      };
      return statusMap[status] || "未知";
    },
    // 加载更多
    loadMore() {
      if (this.loading || !this.hasMore)
        return;
      this.page++;
      this.loadStationList();
    },
    // 导航到详情页
    navigateToDetail(id) {
      common_vendor.index.showLoading({
        title: "加载详情...",
        mask: true
      });
      utils_api.heatingStationApi.getDetail(id).then((res) => {
        common_vendor.index.__f__("log", "at pages/hes/list.vue:283", "换热站详情数据:", res);
        if (res.code === 200) {
          common_vendor.index.navigateTo({
            url: `/pages/hes/detail?id=${id}`,
            success: function(navigateRes) {
              navigateRes.eventChannel.emit("acceptStationDetail", res.data);
            }
          });
        } else {
          common_vendor.index.showToast({
            title: res.message || "获取详情失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/hes/list.vue:301", "获取换热站详情失败:", err);
        common_vendor.index.showToast({
          title: "获取详情失败，请重试",
          icon: "none"
        });
      }).finally(() => {
        common_vendor.index.hideLoading();
      });
    },
    // 导航到控制页
    navigateToControl(id) {
      common_vendor.index.navigateTo({
        url: `/pages/hes/control?id=${id}`
      });
    },
    // 导航到告警页
    navigateToAlarm(id) {
      common_vendor.index.navigateTo({
        url: `/pages/hes/alarms?id=${id}`
      });
    }
  }
};
if (!Array) {
  const _component_BaseTabBar = common_vendor.resolveComponent("BaseTabBar");
  _component_BaseTabBar();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    b: $data.searchKeyword,
    c: common_vendor.o(($event) => $data.searchKeyword = $event.detail.value),
    d: $data.searchKeyword
  }, $data.searchKeyword ? {
    e: common_vendor.o((...args) => $options.clearSearch && $options.clearSearch(...args))
  } : {}, {
    f: common_vendor.f($data.statusTabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab.label),
        b: index,
        c: $data.currentTab === tab.value ? 1 : "",
        d: common_vendor.o(($event) => $options.switchTab(tab.value), index)
      };
    }),
    g: common_vendor.f($data.stationList, (station, index, i0) => {
      return {
        a: common_vendor.t(station.name),
        b: common_vendor.t(station.id),
        c: common_vendor.t($options.formatDateTime(station.last_connect_time)),
        d: common_vendor.t(station.operation_mode === "fixed" ? "定频" : "变频"),
        e: common_vendor.t(station.primary_supply_temp || "--"),
        f: common_vendor.t(station.primary_return_temp || "--"),
        g: common_vendor.t(station.secondary_supply_temp || "--"),
        h: common_vendor.t(station.secondary_return_temp || "--"),
        i: common_vendor.t($options.getStatusText(station.status)),
        j: common_vendor.n(station.status),
        k: common_vendor.o(($event) => $options.navigateToControl(station.id), index),
        l: common_vendor.o(($event) => $options.navigateToDetail(station.id), index),
        m: index,
        n: common_vendor.n($options.getCardColorClass(station)),
        o: common_vendor.o(($event) => $options.navigateToDetail(station.id), index)
      };
    }),
    h: $data.hasMore
  }, $data.hasMore ? {
    i: common_vendor.o((...args) => $options.loadMore && $options.loadMore(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/hes/list.js.map
