/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.patrol-plans-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  padding-top: 0;
  padding-bottom: 120rpx;
}

/* 页面标题 */
.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 90rpx;
  background-color: #0088ff;
  color: #ffffff;
  font-size: 34rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 999;
}

/* 搜索和筛选区域样式 */
.search-filter-area {
  background-color: #fff;
  border-bottom: 1rpx solid #eeeeee;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  position: -webkit-sticky;
  position: sticky;
  top: 0rpx;
  z-index: 999;
}

/* 搜索栏样式 */
.search-bar {
  padding: 20rpx 24rpx 16rpx;
}
.search-bar .search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  padding: 14rpx 24rpx;
}
.search-bar .search-input-wrapper .iconfont {
  font-size: 28rpx;
  color: #999;
}
.search-bar .search-input-wrapper input {
  flex: 1;
  height: 40rpx;
  font-size: 28rpx;
  margin: 0 16rpx;
  color: #333;
}

/* 筛选栏样式 */
.filter-bar {
  display: flex;
  padding: 0;
  margin: 0 12rpx 16rpx;
  height: 70rpx;
}
.filter-bar .filter-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 26rpx;
  color: #333;
  height: 100%;
  position: relative;
}
.filter-bar .filter-item:after {
  content: "";
  position: absolute;
  right: 0;
  top: 20%;
  height: 60%;
  width: 1rpx;
  background-color: #eee;
}
.filter-bar .filter-item:last-child:after {
  display: none;
}
.filter-bar .filter-item text {
  margin-right: 6rpx;
}
.filter-bar .filter-item .iconfont {
  font-size: 22rpx;
  color: #999;
  margin-left: 4rpx;
}
.plan-list {
  padding: 20rpx;
}
.plan-list .plan-item {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
  border: 1rpx solid #eeeeee;
}
.plan-list .plan-item .plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.plan-list .plan-item .plan-header .plan-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  max-width: 65%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.plan-list .plan-item .plan-header .plan-status {
  padding: 4rpx 16rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: normal;
}
.plan-list .plan-item .plan-header .plan-status.pending {
  background-color: rgba(24, 144, 255, 0.08);
  color: #FFA500;
}
.plan-list .plan-item .plan-header .plan-status.processing {
  background-color: rgba(24, 144, 255, 0.15);
  color: #1E90FF;
}
.plan-list .plan-item .plan-header .plan-status.completed {
  background-color: rgba(82, 196, 26, 0.08);
  color: #52c41a;
}
.plan-list .plan-item .plan-header .plan-status.overdue {
  background-color: rgba(245, 34, 45, 0.08);
  color: #f5222d;
}
.plan-list .plan-item .plan-header .plan-status.canceled {
  background-color: rgba(102, 102, 102, 0.08);
  color: #999999;
}
.plan-list .plan-item .plan-info {
  padding: 16rpx 0;
  border-top: 1rpx solid #f2f2f2;
  border-bottom: 1rpx solid #f2f2f2;
  margin-bottom: 16rpx;
}
.plan-list .plan-item .plan-info .info-row {
  display: flex;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
}
.plan-list .plan-item .plan-info .info-row:last-child {
  margin-bottom: 0;
}
.plan-list .plan-item .plan-info .info-row .info-item {
  flex: 0 0 50%;
  display: flex;
  align-items: flex-start;
  min-width: 0;
  box-sizing: border-box;
  padding-right: 15rpx;
  margin-bottom: 10rpx;
  overflow: hidden;
}
.plan-list .plan-item .plan-info .info-row .info-item.full-width {
  flex: 0 0 100%;
}
.plan-list .plan-item .plan-info .info-row .info-item .item-label {
  width: 130rpx;
  flex-shrink: 0;
  font-size: 26rpx;
  color: #888888;
}
.plan-list .plan-item .plan-info .info-row .info-item .item-value {
  flex: 1;
  font-size: 26rpx;
  color: #333333;
  overflow: visible;
  word-break: break-all;
  white-space: normal;
  max-width: calc(100% - 130rpx);
}
.plan-list .plan-item .plan-footer .plan-actions {
  display: flex;
  justify-content: flex-end;
  flex-wrap: wrap;
}
.plan-list .plan-item .plan-footer .plan-actions .action-button {
  display: flex;
  align-items: center;
  padding: 8rpx 20rpx;
  border-radius: 28rpx;
  margin-left: 16rpx;
  margin-bottom: 8rpx;
  transition: all 0.2s;
}
.plan-list .plan-item .plan-footer .plan-actions .action-button:active {
  opacity: 0.8;
}
.plan-list .plan-item .plan-footer .plan-actions .action-button .iconfont {
  font-size: 24rpx;
  margin-right: 8rpx;
}
.plan-list .plan-item .plan-footer .plan-actions .action-button text {
  font-size: 26rpx;
}
.plan-list .plan-item .plan-footer .plan-actions .action-button.start {
  background-color: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}
.plan-list .plan-item .plan-footer .plan-actions .action-button.continue {
  background-color: rgba(82, 196, 26, 0.15);
  color: #52c41a;
}
.plan-list .plan-item .plan-footer .plan-actions .action-button.view {
  background-color: #f5f5f5;
  color: #666666;
  margin-right: 0;
  padding: 8rpx 20rpx;
}
.loading-container {
  display: flex;
  justify-content: center;
  padding: 30rpx 0;
}
.loading-container .loading-text {
  font-size: 28rpx;
  color: #999;
}
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 50rpx 0;
}
.error-container .error-text {
  font-size: 28rpx;
  color: #f5222d;
  margin-bottom: 20rpx;
}
.error-container .retry-btn {
  font-size: 28rpx;
  color: #fff;
  background-color: #1890ff;
  padding: 8rpx 30rpx;
  border-radius: 30rpx;
}
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-state .empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-state .empty-text {
  font-size: 28rpx;
  color: #999;
}
.filter-popup {
  background-color: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
}
.filter-popup .popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}
.filter-popup .popup-header .popup-title {
  font-size: 32rpx;
  font-weight: bold;
}
.filter-popup .popup-header .popup-close {
  font-size: 28rpx;
  color: #1890ff;
}
.filter-popup .popup-content {
  padding: 20rpx 30rpx;
  max-height: 600rpx;
  overflow-y: auto;
}
.filter-popup .popup-content .filter-option {
  padding: 20rpx 0;
  font-size: 30rpx;
  color: #333;
  border-bottom: 1rpx solid #eee;
}
.filter-popup .popup-content .filter-option.active {
  color: #1890ff;
  font-weight: bold;
}
.filter-popup .popup-content .filter-option:last-child {
  border-bottom: none;
}
.floating-button {
  position: fixed;
  right: 30rpx;
  bottom: 30rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #0088ff;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 136, 255, 0.4);
  z-index: 10;
}
.floating-button .plus-icon {
  font-size: 60rpx;
  color: #fff;
  font-weight: normal;
  line-height: 60rpx;
  margin-top: -3rpx;
}