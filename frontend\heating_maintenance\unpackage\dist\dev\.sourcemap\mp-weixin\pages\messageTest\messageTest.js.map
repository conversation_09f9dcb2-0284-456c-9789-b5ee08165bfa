{"version": 3, "file": "messageTest.js", "sources": ["pages/messageTest/messageTest.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbWVzc2FnZVRlc3QvbWVzc2FnZVRlc3QudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"container\">\r\n\t\t<view class=\"title\">消息服务测试页面</view>\r\n\t\t<view class=\"status\">当前服务状态：<text :class=\"{ 'running': isServiceRunning, 'stopped': !isServiceRunning }\">{{ serviceStatusText }}</text></view>\r\n\t\t<button type=\"primary\" @click=\"startMsgService\" :disabled=\"isServiceRunning\">启动消息服务</button>\r\n\t\t<button type=\"warn\" @click=\"stopMsgService\" :disabled=\"!isServiceRunning\">停止消息服务</button>\r\n\t\t<view class=\"tips\">\r\n\t\t\t<text>请打开浏览器控制台查看服务运行日志和接口调用情况。</text>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// 引入消息服务\r\n\timport { messageService } from '../../utils/messageService.js';\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 用于在模板中显示服务状态\r\n\t\t\t\tisServiceRunning: false\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 计算属性，返回更友好的状态文本\r\n\t\t\tserviceStatusText() {\r\n\t\t\t\treturn this.isServiceRunning ? '运行中' : '已停止';\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t// 页面加载时，获取当前服务的实际运行状态\r\n\t\t\tthis.isServiceRunning = messageService.getStatus();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 启动服务按钮点击事件\r\n\t\t\tstartMsgService() {\r\n\t\t\t\tconsole.log(\"测试页面：尝试启动消息服务...\");\r\n\t\t\t\tmessageService.start();\r\n\t\t\t\t// 更新页面显示状态\r\n\t\t\t\tthis.isServiceRunning = messageService.getStatus();\r\n\t\t\t},\r\n\t\t\t// 停止服务按钮点击事件\r\n\t\t\tstopMsgService() {\r\n\t\t\t\tconsole.log(\"测试页面：尝试停止消息服务...\");\r\n\t\t\t\tmessageService.stop();\r\n\t\t\t\t// 更新页面显示状态\r\n\t\t\t\tthis.isServiceRunning = messageService.getStatus();\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped>\r\n\t.container {\r\n\t\tpadding: 20px;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.title {\r\n\t\tfont-size: 20px;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 20px;\r\n\t}\r\n\r\n\t.status {\r\n\t\tmargin-bottom: 20px;\r\n\t\tfont-size: 16px;\r\n\t}\r\n\r\n\t.status .running {\r\n\t\tcolor: #18bc37; /* 绿色 */\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.status .stopped {\r\n\t\tcolor: #e43d33; /* 红色 */\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\tbutton {\r\n\t\tmargin-top: 10px;\r\n\t}\r\n\r\n\t.tips {\r\n\t\tmargin-top: 30px;\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #888;\r\n\t}\r\n</style>\r\n", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaan<PERSON>_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/messageTest/messageTest.vue'\nwx.createPage(MiniProgramPage)"], "names": ["messageService", "uni"], "mappings": ";;;AAgBC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA;AAAA,MAEN,kBAAkB;AAAA;EAEnB;AAAA,EACD,UAAU;AAAA;AAAA,IAET,oBAAoB;AACnB,aAAO,KAAK,mBAAmB,QAAQ;AAAA,IACxC;AAAA,EACA;AAAA,EACD,SAAS;AAER,SAAK,mBAAmBA,oCAAe;EACvC;AAAA,EACD,SAAS;AAAA;AAAA,IAER,kBAAkB;AACjBC,oBAAAA,MAAY,MAAA,OAAA,2CAAA,kBAAkB;AAC9BD,2BAAc,eAAC,MAAK;AAEpB,WAAK,mBAAmBA,oCAAe;IACvC;AAAA;AAAA,IAED,iBAAiB;AAChBC,oBAAAA,MAAY,MAAA,OAAA,2CAAA,kBAAkB;AAC9BD,2BAAc,eAAC,KAAI;AAEnB,WAAK,mBAAmBA,oCAAe;IACxC;AAAA,EACD;AACD;;;;;;;;;;;;;AChDD,GAAG,WAAW,eAAe;"}