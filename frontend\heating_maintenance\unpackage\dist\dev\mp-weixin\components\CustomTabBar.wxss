/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.custom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 110rpx;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.06);
  z-index: 999;
}
.custom-tabbar .tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12rpx 0;
  position: relative;
  transition: all 0.3s;
}
.custom-tabbar .tab-item.active {
  transform: translateY(-6rpx);
}
.custom-tabbar .tab-item.active .tab-text {
  color: #1890ff;
  font-weight: 500;
}
.custom-tabbar .tab-item .icon-container {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 6rpx;
}
.custom-tabbar .tab-item .icon-container .tab-icon {
  width: 48rpx;
  height: 48rpx;
  transition: all 0.3s;
}
.custom-tabbar .tab-item .tab-text {
  font-size: 24rpx;
  color: #999;
  line-height: 1;
  transition: all 0.3s;
}
.custom-tabbar .tab-item .active-indicator {
  position: absolute;
  bottom: -3rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 16rpx;
  height: 6rpx;
  background: #1890ff;
  border-radius: 6rpx;
}
page {
  padding-bottom: calc(110rpx + env(safe-area-inset-bottom));
}