-- 为t_other_param表添加缺失的字段
ALTER TABLE `t_other_param` 
ADD COLUMN `price` VARCHAR(50) NULL DEFAULT NULL COMMENT '供热单价',
ADD COLUMN `rated_temp` VARCHAR(50) NULL DEFAULT NULL COMMENT '额定温度',
ADD COLUMN `collect_cycle` VARCHAR(20) NULL DEFAULT '10' COMMENT '阀门热表采集周期，单位分钟',
ADD COLUMN `indoor_cycle` VARCHAR(20) NULL DEFAULT '60' COMMENT '室内面板采集周期',
ADD COLUMN `heatmater_cycle` VARCHAR(20) NULL DEFAULT '30' COMMENT '热表采集周期',
ADD COLUMN `alarm_cycle` INT(10) NULL DEFAULT NULL COMMENT '告警周期',
ADD COLUMN `position_cycle` INT(10) NULL DEFAULT '1' COMMENT '定位上报周期 分钟',
ADD COLUMN `last_alarm_date` DATETIME NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
ADD COLUMN `create_time` DATETIME NULL DEFAULT NULL,
ADD COLUMN `create_user` INT(10) NULL DEFAULT NULL,
ADD COLUMN `update_time` DATETIME NULL DEFAULT NULL,
ADD COLUMN `update_user` INT(10) NULL DEFAULT NULL,
ADD COLUMN `mark` BIT(1) NULL DEFAULT NULL;

-- 更新现有记录，添加默认的position_cycle值
UPDATE `t_other_param` SET `position_cycle` = 1 WHERE `id` = 1;
