
.no-permission-item[data-v-9e1a7520] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0.5;
}
.no-permission-text[data-v-9e1a7520] {
  font-size: 0.75rem;
  color: #999;
  text-align: center;
}

/* 确保permission-check组件继承其父容器的布局特性 */
[data-v-9e1a7520] .permission-check {
  display: inherit;
  width: inherit;
  height: inherit;
  flex: inherit;
  position: inherit;
}

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.fault-list-container {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding-bottom: 3.75rem;
}

/* Tab切换 */
.tab-container {
  display: flex;
  background-color: #fff;
  border-bottom: 0.03125rem solid #eee;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 999;
}
.tab-container .tab-item {
  flex: 1;
  height: 2.5rem;
  line-height: 2.5rem;
  text-align: center;
  font-size: 0.9375rem;
  color: #666;
  position: relative;
}
.tab-container .tab-item.active {
  color: #007aff;
  font-weight: 500;
}
.tab-container .tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 1.875rem;
  height: 0.125rem;
  background-color: #007aff;
  border-radius: 0.0625rem;
}
.filter-section {
  background-color: #ffffff;
  display: flex;
  justify-content: space-between;
  padding: 0.625rem 0.9375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
  margin-bottom: 0.625rem;
  position: -webkit-sticky;
  position: sticky;
  top: 2.5rem;
  z-index: 998;
}
.filter-section .date-filter {
  flex: 1;
  height: 2.1875rem;
}
.filter-section .date-filter .date-picker {
  display: flex;
  align-items: center;
  height: 2.1875rem;
  border: 1px solid #e5e5e5;
  border-radius: 0.1875rem;
  padding: 0 0.625rem;
  font-size: 0.875rem;
  color: #333;
}
.filter-section .date-filter .date-picker .date-text {
  flex: 1;
}
.filter-section .date-filter .date-picker .iconfont {
  margin-left: 0.3125rem;
  color: #999;
}
.filter-section .date-filter {
  margin-right: 0.625rem;
}
.fault-list {
  padding: 0 0.9375rem;
}
.fault-list .fault-item {
  background-color: #ffffff;
  border-radius: 0.375rem;
  margin-bottom: 0.625rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
  overflow: hidden;
}
.fault-list .fault-item .fault-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem 0.9375rem;
  border-bottom: 1px solid #f0f0f0;
}
.fault-list .fault-item .fault-header .heat-unit-name {
  font-size: 1rem;
  font-weight: 500;
  color: #333;
}
.fault-list .fault-item .fault-header .fault-status {
  padding: 0.1875rem 0.5rem;
  border-radius: 0.1875rem;
  font-size: 0.75rem;
}
.fault-list .fault-item .fault-header .fault-status.pending {
  background-color: #fff7e6;
  color: #fa8c16;
}
.fault-list .fault-item .fault-header .fault-status.confirmed {
  background-color: #e6f7ff;
  color: #1890ff;
}
.fault-list .fault-item .fault-header .fault-status.completed {
  background-color: #f6ffed;
  color: #52c41a;
}
.fault-list .fault-item .fault-header .fault-status.returned {
  background-color: #fff1f0;
  color: #f5222d;
}
.fault-list .fault-item .fault-header .fault-status.default {
  background-color: #f5f5f5;
  color: #999;
}
.fault-list .fault-item .fault-content {
  padding: 0.9375rem;
}
.fault-list .fault-item .fault-content .fault-desc {
  font-size: 0.875rem;
  color: #333;
  line-height: 1.6;
  margin-bottom: 0.625rem;
}
.fault-list .fault-item .fault-content .fault-info {
  margin-bottom: 0.625rem;
}
.fault-list .fault-item .fault-content .fault-info .info-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.3125rem;
}
.fault-list .fault-item .fault-content .fault-info .info-item .info-label {
  font-size: 0.8125rem;
  color: #999;
  width: 4.6875rem;
}
.fault-list .fault-item .fault-content .fault-info .info-item .info-value {
  font-size: 0.8125rem;
  color: #333;
}
.fault-list .fault-item .fault-content .fault-info .info-item .level-tag {
  padding: 0.125rem 0.375rem;
  border-radius: 0.125rem;
  font-size: 0.75rem;
}
.fault-list .fault-item .fault-content .fault-info .info-item .level-tag.notice {
  background-color: #e6f7ff;
  color: #1890ff;
}
.fault-list .fault-item .fault-content .fault-info .info-item .level-tag.normal {
  background-color: #e6f7ff;
  color: #1890ff;
}
.fault-list .fault-item .fault-content .fault-info .info-item .level-tag.important {
  background-color: #fff7e6;
  color: #fa8c16;
}
.fault-list .fault-item .fault-content .fault-info .info-item .level-tag.serious {
  background-color: #ffebee;
  color: #EF5350;
}
.fault-list .fault-item .fault-content .fault-info .info-item .level-tag.critical {
  background-color: #fff1f0;
  color: #f5222d;
}
.fault-list .fault-item .fault-content .fault-info .info-item .level-tag.default {
  background-color: #f5f5f5;
  color: #999;
}
.fault-list .fault-item .fault-content .fault-footer {
  display: flex;
  justify-content: flex-end;
}
.fault-list .fault-item .fault-content .fault-footer .report-info {
  font-size: 0.75rem;
  color: #999;
}
.fault-list .fault-item .fault-content .fault-footer .report-info .reporter {
  margin-right: 0.3125rem;
}

/* 重置按钮 */
.refresh-button {
  font-size: 0.875rem;
  background-color: #fff;
  border-radius: 0.15625rem;
  align-items: center;
  height: 2.25rem;
  border-radius: 0.1875rem;
  padding: 0 0.625rem;
  font-size: 0.875rem;
  position: relative;
  min-width: 2.5rem;
}
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3.125rem 0;
}
.empty-state .empty-image {
  width: 7.5rem;
  height: 7.5rem;
  margin-bottom: 0.9375rem;
}
.empty-state .empty-text {
  font-size: 0.875rem;
  color: #999;
  margin-bottom: 1.25rem;
}
.empty-state .refresh-button {
  font-size: 0.875rem;
  color: #007aff;
  background-color: #fff;
  border: 0.0625rem solid #007aff;
  border-radius: 1.25rem;
  padding: 0.3125rem 1.875rem;
}
.loading-container {
  display: flex;
  justify-content: center;
  padding: 0.9375rem 0;
}
.loading-container .loading-text {
  font-size: 0.875rem;
  color: #999;
}
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5625rem 0;
}
.error-container .error-text {
  font-size: 0.875rem;
  color: #f5222d;
  margin-bottom: 0.625rem;
}
.error-container .retry-btn {
  font-size: 0.875rem;
  color: #fff;
  background-color: #1890ff;
  padding: 0.25rem 0.9375rem;
  border-radius: 0.9375rem;
}
.floating-button {
  position: fixed;
  right: 0.9375rem;
  bottom: 0.9375rem;
  width: 3.125rem;
  height: 3.125rem;
  border-radius: 50%;
  background-color: #0088ff;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 136, 255, 0.4);
  z-index: 10;
}
.floating-button .plus-icon {
  font-size: 1.875rem;
  color: #fff;
  font-weight: normal;
  line-height: 1.875rem;
  margin-top: -0.09375rem;
}
.heat-unit-filter-section {
  background-color: #ffffff;
  display: flex;
  justify-content: space-between;
  padding: 0.625rem 0.9375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
  margin-bottom: 0.625rem;
}
.heat-unit-filter {
  flex: 1;
  height: 2.1875rem;
}
.heat-unit-filter .heat-unit-picker {
  display: flex;
  align-items: center;
  height: 2.1875rem;
  border: 1px solid #e5e5e5;
  border-radius: 0.1875rem;
  padding: 0 0.625rem;
  font-size: 0.875rem;
  color: #333;
}
.heat-unit-filter .heat-unit-picker .heat-unit-text {
  flex: 1;
}
.heat-unit-filter .heat-unit-picker .iconfont {
  margin-left: 0.3125rem;
  color: #999;
}