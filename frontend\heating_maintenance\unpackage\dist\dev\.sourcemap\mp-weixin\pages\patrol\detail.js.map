{"version": 3, "file": "detail.js", "sources": ["pages/patrol/detail.vue", "F:/Software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcGF0cm9sL2RldGFpbC52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"patrol-detail-container\">\r\n\t\t<!-- 计划基本信息 -->\r\n\t\t<view class=\"detail-card\">\r\n\t\t\t<view class=\"card-header\">\r\n\t\t\t\t<text class=\"card-title\">{{ isRecord ? '巡检记录详情' : '巡检计划详情' }}</text>\r\n\t\t\t\t<view class=\"plan-status\" :class=\"planInfo.status\">{{ getStatusText(planInfo.status) }}</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"info-group\">\r\n\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t<text class=\"info-label\">计划名称</text>\r\n\t\t\t\t\t<text class=\"info-value\">{{ planInfo.title }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t<text class=\"info-label\">计划编号</text>\r\n\t\t\t\t\t<text class=\"info-value\">{{ planInfo.code }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t<text class=\"info-label\">巡检类型</text>\r\n\t\t\t\t\t<text class=\"info-value\">{{ planInfo.type }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t<text class=\"info-label\">开始时间</text>\r\n\t\t\t\t\t<text class=\"info-value\">{{ formatDate(planInfo.startTime) }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t<text class=\"info-label\">结束时间</text>\r\n\t\t\t\t\t<text class=\"info-value\">{{ formatDate(planInfo.endTime) }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-item\" v-if=\"planInfo.scheduleType\">\r\n\t\t\t\t\t<text class=\"info-label\">巡检周期</text>\r\n\t\t\t\t\t<text class=\"info-value\">{{ getScheduleText(planInfo.scheduleType, planInfo.scheduleWeekDays, planInfo.scheduleMonthDays) }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t<text class=\"info-label\">巡检地点</text>\r\n\t\t\t\t\t<text class=\"info-value\">{{ planInfo.location }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t<text class=\"info-label\">负责人</text>\r\n\t\t\t\t\t<text class=\"info-value\">{{ planInfo.manager }}</text>\r\n\t\t\t\t</view> \r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"plan-desc\" v-if=\"planInfo.description\">\r\n\t\t\t\t<text class=\"desc-title\">计划描述：</text>\r\n\t\t\t\t<text class=\"desc-content\">{{ planInfo.description }}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 巡检任务列表 -->\r\n\t\t<view class=\"detail-card\">\r\n\t\t\t<view class=\"card-header\">\r\n\t\t\t\t<text class=\"card-title\">巡检任务</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"task-list\">\r\n\t\t\t\t<view class=\"task-item\" v-for=\"(task, index) in planTasks\" :key=\"index\" @click=\"toggleTaskDetails(index)\">\r\n\t\t\t\t\t<view class=\"task-header\">\r\n\t\t\t\t\t\t<view class=\"task-left\">\r\n\t\t\t\t\t\t\t<view class=\"task-status\" :class=\"task.status\"></view>\r\n\t\t\t\t\t\t\t<text class=\"task-title\">{{ task.title }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"iconfont\" :class=\"task.showDetails ? 'icon-arrow-up' : 'icon-arrow-down'\"></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"task-details\" v-if=\"task.showDetails\">\r\n\t\t\t\t\t\t<view class=\"detail-row\">\r\n\t\t\t\t\t\t\t<text class=\"detail-label\">巡检对象:</text>\r\n\t\t\t\t\t\t\t<text class=\"detail-value\">{{ task.target }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"detail-row\">\r\n\t\t\t\t\t\t\t<text class=\"detail-label\">巡检点:</text>\r\n\t\t\t\t\t\t\t<text class=\"detail-value\">{{ task.point }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"detail-row\">\r\n\t\t\t\t\t\t\t<text class=\"detail-label\">巡检内容:</text>\r\n\t\t\t\t\t\t\t<text class=\"detail-value\">{{ task.content }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"detail-row\">\r\n\t\t\t\t\t\t\t<text class=\"detail-label\">标  准  值:</text>\r\n\t\t\t\t\t\t\t<text class=\"detail-value\">{{ task.standard }} {{ task.unit }}  </text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"detail-row\" v-if=\"task.status === 'completed'\">\r\n\t\t\t\t\t\t\t<text class=\"detail-label\">实际值</text>\r\n\t\t\t\t\t\t\t<text class=\"detail-value\" :class=\"{ 'value-abnormal': task.isAbnormal }\">{{ task.actualValue }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"detail-row\" v-if=\"task.status === 'completed' && task.completedTime\">\r\n\t\t\t\t\t\t\t<text class=\"detail-label\">完成时间</text>\r\n\t\t\t\t\t\t\t<text class=\"detail-value\">{{ task.completedTime }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"detail-row\" v-if=\"task.status === 'completed' && task.remark\">\r\n\t\t\t\t\t\t\t<text class=\"detail-label\">备注</text>\r\n\t\t\t\t\t\t\t<text class=\"detail-value\">{{ task.remark }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"task-images\" v-if=\"task.status === 'completed' && task.images && task.images.length > 0\">\r\n\t\t\t\t\t\t\t<text class=\"images-title\">巡检照片</text>\r\n\t\t\t\t\t\t\t<view class=\"image-list\">\r\n\t\t\t\t\t\t\t\t<image v-for=\"(img, imgIndex) in task.images\" :key=\"imgIndex\" :src=\"img\" @click=\"previewImage(task.images, imgIndex)\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 底部操作按钮 -->\r\n\t<!-- \t<view class=\"action-buttons\" v-if=\"!isRecord\">\r\n\t\t\t<view class=\"action-btn start\" v-if=\"planInfo.status === 'pending'\" @click=\"startPatrol\">\r\n\t\t\t\t<text>开始巡检</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"action-btn continue\" v-if=\"planInfo.status === 'processing'\" @click=\"continuePatrol\">\r\n\t\t\t\t<text>继续巡检</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"action-btn complete\" v-if=\"planInfo.status === 'processing'\" @click=\"completePatrol\">\r\n\t\t\t\t<text>完成巡检</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"action-btn export\" v-if=\"planInfo.status === 'completed'\" @click=\"exportReport\">\r\n\t\t\t\t<text>导出报告</text>\r\n\t\t\t</view>\r\n\t\t</view> -->\r\n\t\t\r\n\t\t<!-- 记录操作按钮 -->\r\n\t\t<!-- <view class=\"action-buttons\" v-if=\"isRecord\">\r\n\t\t\t<view class=\"action-btn export\" @click=\"exportReport\">\r\n\t\t\t\t<text>导出记录</text>\r\n\t\t\t</view>\r\n\t\t</view> -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { patrolApi } from '@/utils/api.js';\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tplanId: '',\r\n\t\t\tplanInfo: {\r\n\t\t\t\tid: '',\r\n\t\t\t\ttitle: '',\r\n\t\t\t\tcode: '',\r\n\t\t\t\ttype: '',\r\n\t\t\t\tstartTime: '',\r\n\t\t\t\tendTime: '',\r\n\t\t\t\tlocation: '',\r\n\t\t\t\tmanager: '',\r\n\t\t\t\tprogress: '',\r\n\t\t\t\tdescription: '',\r\n\t\t\t\tstatus: 'pending'\r\n\t\t\t},\r\n\t\t\tplanTasks: [],\r\n\t\t\t// stats: {\r\n\t\t\t// \ttotalTasks: 0,\r\n\t\t\t// \tcompletedTasks: 0,\r\n\t\t\t// \tabnormalCount: 0,\r\n\t\t\t// \tcompletionRate: 0\r\n\t\t\t// },\r\n\t\t\tisLoading: false,\r\n\t\t\t// 是否为巡检记录详情\r\n\t\t\tisRecord: false\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\tprogressPercent() {\r\n\t\t\tif (!this.planInfo.progress) return 0;\r\n\t\t\t\r\n\t\t\tconst progressParts = this.planInfo.progress.split('/');\r\n\t\t\tif (progressParts.length !== 2) return 0;\r\n\t\t\t\r\n\t\t\tconst completed = parseInt(progressParts[0]);\r\n\t\t\tconst total = parseInt(progressParts[1]);\r\n\t\t\t\r\n\t\t\tif (isNaN(completed) || isNaN(total) || total === 0) return 0;\r\n\t\t\t\r\n\t\t\treturn Math.floor((completed / total) * 100);\r\n\t\t}\r\n\t},\r\n\tonLoad(options) {\r\n\t\tthis.planId = options.id;\r\n\t\t// 判断是否为巡检记录\r\n\t\tthis.isRecord = options.type === 'record';\r\n\t\t\r\n\t\tif (this.isRecord) {\r\n\t\t\tthis.loadPatrolResultDetail();\r\n\t\t} else {\r\n\t\t\tthis.loadPlanDetail();\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t// 加载巡检结果详情\r\n\t\tloadPatrolResultDetail() {\r\n\t\t\tthis.isLoading = true;\r\n\t\t\t// 显示加载中\r\n\t\t\tuni.showLoading({\r\n\t\t\t   title: \"加载中...\",\r\n\t\t\t});\r\n\t\t\tpatrolApi.getPatrolResultDetail(this.planId)\r\n\t\t\t\t.then(res => {\r\n\t\t\t\t\tif (res.code === 200 && res.data) {\r\n\t\t\t\t\t\tconst data = res.data;\r\n\t\t\t\t\t\tconst recordInfo = data.recordInfo;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 设置巡检记录基本信息\r\n\t\t\t\t\t\tthis.planInfo = {\r\n\t\t\t\t\t\t\tid: recordInfo.id,\r\n\t\t\t\t\t\t\ttitle: recordInfo.planName,\r\n\t\t\t\t\t\t\tcode: `REC-${recordInfo.id}`,\r\n\t\t\t\t\t\t\ttype: '巡检记录',\r\n\t\t\t\t\t\t\tstartTime: recordInfo.startTime,\r\n\t\t\t\t\t\t\tendTime: recordInfo.endTime,\r\n\t\t\t\t\t\t\tlocation: recordInfo.locations,\r\n\t\t\t\t\t\t\tmanager: recordInfo.executorName || '未分配',\r\n\t\t\t\t\t\t\tdescription: recordInfo.remark || '',\r\n\t\t\t\t\t\t\tstatus: recordInfo.status,\r\n\t\t\t\t\t\t\tscheduleType: recordInfo.scheduleType,\r\n\t\t\t\t\t\t\tscheduleWeekDays: recordInfo.scheduleWeekDays,\r\n\t\t\t\t\t\t\tscheduleMonthDays: recordInfo.scheduleMonthDays\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 转换巡检结果为任务列表\r\n\t\t\t\t\t\tif (data.resultList && data.resultList.length > 0) {\r\n\t\t\t\t\t\t\tthis.planTasks = data.resultList.map(result => ({\r\n\t\t\t\t\t\t\t\tid: result.id,\r\n\t\t\t\t\t\t\t\ttitle: result.itemName,\r\n\t\t\t\t\t\t\t\ttarget: result.deviceName || '',\r\n\t\t\t\t\t\t\t\tpoint: result.categoryName || '',\r\n\t\t\t\t\t\t\t\tcontent: result.description || '',\r\n\t\t\t\t\t\t\t\tstandard: result.normalRange || '',\r\n\t\t\t\t\t\t\t\tunit: result.unit || '',\r\n\t\t\t\t\t\t\t\tactualValue: result.paramValue || '',\r\n\t\t\t\t\t\t\t\tisAbnormal: result.checkResult === 'abnormal',\r\n\t\t\t\t\t\t\t\tcompletedTime: this.formatDateTime(result.createTime),\r\n\t\t\t\t\t\t\t\tremark: result.description || '',\r\n\t\t\t\t\t\t\t\tstatus: 'completed', // 巡检记录中的项目都是已完成的\r\n\t\t\t\t\t\t\t\timages: result.images || [],\r\n\t\t\t\t\t\t\t\tshowDetails: false,\r\n\t\t\t\t\t\t\t\tlatitude: result.latitude,\r\n\t\t\t\t\t\t\t\tlongitude: result.longitude\r\n\t\t\t\t\t\t\t}));\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 更新统计信息\r\n\t\t\t\t\t\t// if (data.summary) {\r\n\t\t\t\t\t\t// \tthis.stats = {\r\n\t\t\t\t\t\t// \t\ttotalItems: data.summary.totalItems || 0,\r\n\t\t\t\t\t\t// \t\tcompletedTasks: data.summary.totalItems || 0, // 记录中的项目都是已完成的\r\n\t\t\t\t\t\t// \t\tabnormalCount: data.summary.abnormalCount || 0,\r\n\t\t\t\t\t\t// \t\tcompletionRate: data.summary.completionRate?.replace('%', '') || 100\r\n\t\t\t\t\t\t// \t};\r\n\t\t\t\t\t\t// } else {\r\n\t\t\t\t\t\t// \tthis.calculateStats();\r\n\t\t\t\t\t\t// }\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.message || '获取巡检结果详情失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tconsole.error('获取巡检结果详情失败:', err);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '获取巡检结果详情失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t\t.finally(() => {\r\n\t\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 加载巡检计划详情\r\n\t\tloadPlanDetail() {\r\n\t\t\tthis.isLoading = true;\r\n\t\t\t// 显示加载中\r\n\t\t\tuni.showLoading({\r\n\t\t\t   title: \"加载中...\",\r\n\t\t\t});\r\n\t\t\tconst apiCall = this.isRecord \r\n\t\t\t\t? patrolApi.getPatrolRecordDetail(this.planId)\r\n\t\t\t\t: patrolApi.getPlanDetail(this.planId);\r\n\t\t\t\t\r\n\t\t\tapiCall.then(res => {\r\n\t\t\t\tif (res.code === 200 && res.data) {\r\n\t\t\t\t\t// 转换API返回的数据到页面格式\r\n\t\t\t\t\tconst data = res.data;\r\n\t\t\t\t\tthis.planInfo = {\r\n\t\t\t\t\t\tid: this.planId,\r\n\t\t\t\t\t\ttitle: data.name,\r\n\t\t\t\t\t\tcode: data.planNo,\r\n\t\t\t\t\t\ttype: data.patrolType,\r\n\t\t\t\t\t\tstartTime: data.startDate,\r\n\t\t\t\t\t\tendTime: data.endDate,\r\n\t\t\t\t\t\tlocation: data.locations,\r\n\t\t\t\t\t\tmanager: data.executorNames?.join('、') || '未分配',\r\n\t\t\t\t\t\tprogress: `0/${data.patrolItems?.length || 0}`, // 进度信息需要计算\r\n\t\t\t\t\t\tdescription: data.description || '',\r\n\t\t\t\t\t\tstatus: data.status,\r\n\t\t\t\t\t\tscheduleType: data.scheduleType,\r\n\t\t\t\t\t\tscheduleWeekDays: data.scheduleWeekDays,\r\n\t\t\t\t\t\tscheduleMonthDays: data.scheduleMonthDays\r\n\t\t\t\t\t};\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 转换任务列表数据\r\n\t\t\t\t\tif (data.patrolItems && data.patrolItems.length > 0) {\r\n\t\t\t\t\t\tthis.planTasks = data.patrolItems.map(item => ({\r\n\t\t\t\t\t\t\tid: item.id,\r\n\t\t\t\t\t\t\ttitle: item.itemName,\r\n\t\t\t\t\t\t\ttarget: item.deviceName || '',\r\n\t\t\t\t\t\t\tpoint: item.checkMethod || '',\r\n\t\t\t\t\t\t\tcontent: item.description,\r\n\t\t\t\t\t\t\tstandard: item.normalRange || '',\r\n\t\t\t\t\t\t\tunit: item.unit,\r\n\t\t\t\t\t\t\tactualValue: '',\r\n\t\t\t\t\t\t\tisAbnormal: false,\r\n\t\t\t\t\t\t\tcompletedTime: '',\r\n\t\t\t\t\t\t\tremark: '',\r\n\t\t\t\t\t\t\tstatus: 'pending', // 默认为待执行\r\n\t\t\t\t\t\t\timages: [],\r\n\t\t\t\t\t\t\tshowDetails: false\r\n\t\t\t\t\t\t}));\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 更新进度信息\r\n\t\t\t\t\t\tconst completedTasks = this.isRecord ? this.planTasks.length : 0;\r\n\t\t\t\t\t\tthis.planInfo.progress = `${completedTasks}/${this.planTasks.length}`;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t//this.calculateStats();\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: res.message || '获取巡检计划详情失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch(err => {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tconsole.error('获取巡检计划详情失败:', err);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '获取巡检计划详情失败',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t})\r\n\t\t\t.finally(() => {\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 计算统计信息\r\n\t\tcalculateStats() {\r\n\t\t\tif (!this.planTasks || this.planTasks.length === 0) {\r\n\t\t\t\tthis.stats = {\r\n\t\t\t\t\ttotalTasks: 0,\r\n\t\t\t\t\tcompletedTasks: 0,\r\n\t\t\t\t\tabnormalCount: 0,\r\n\t\t\t\t\tcompletionRate: 0\r\n\t\t\t\t};\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tconst totalTasks = this.planTasks.length;\r\n\t\t\tconst completedTasks = this.planTasks.filter(task => task.status === 'completed').length;\r\n\t\t\tconst abnormalCount = this.planTasks.filter(task => task.isAbnormal).length;\r\n\t\t\tconst completionRate = totalTasks > 0 ? Math.floor((completedTasks / totalTasks) * 100) : 0;\r\n\t\t\t\r\n\t\t\tthis.stats = {\r\n\t\t\t\ttotalTasks,\r\n\t\t\t\tcompletedTasks,\r\n\t\t\t\tabnormalCount,\r\n\t\t\t\tcompletionRate\r\n\t\t\t};\r\n\t\t},\r\n\t\t\r\n\t\t// 获取状态文本\r\n\t\tgetStatusText(status) {\r\n\t\t\tconst statusMap = {\r\n\t\t\t\t'pending': '待执行',\r\n\t\t\t\t'processing': '进行中',\r\n\t\t\t\t'completed': '已完成',\r\n\t\t\t\t'overdue': '已超时',\r\n\t\t\t\t'canceled': '已取消'\r\n\t\t\t};\r\n\t\t\treturn statusMap[status] || '未知';\r\n\t\t},\r\n\t\t\r\n\t\t// 切换任务详情显示/隐藏\r\n\t\ttoggleTaskDetails(index) {\r\n\t\t\tthis.$set(this.planTasks[index], 'showDetails', !this.planTasks[index].showDetails);\r\n\t\t},\r\n\t\t\r\n\t\t// 预览图片\r\n\t\tpreviewImage(images, current) {\r\n\t\t\tuni.previewImage({\r\n\t\t\t\turls: images,\r\n\t\t\t\tcurrent: images[current]\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 开始巡检\r\n\t\tstartPatrol() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/patrol/execute?id=${this.planId}`\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 继续巡检\r\n\t\tcontinuePatrol() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/patrol/execute?id=${this.planId}`\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 完成巡检\r\n\t\tcompletePatrol() {\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '确认完成',\r\n\t\t\t\tcontent: '是否确认完成本次巡检计划？未完成的巡检项将被标记为已跳过。',\r\n\t\t\t\tconfirmText: '确认',\r\n\t\t\t\tcancelText: '取消',\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t// 实际应用中这里会调用API完成巡检计划\r\n\t\t\t\t\t\t// uni.request({\r\n\t\t\t\t\t\t//   url: `/api/patrol/plans/${this.planId}/complete`,\r\n\t\t\t\t\t\t//   method: 'POST',\r\n\t\t\t\t\t\t//   success: (res) => {\r\n\t\t\t\t\t\t//     if (res.data.success) {\r\n\t\t\t\t\t\t//       uni.showToast({\r\n\t\t\t\t\t\t//         title: '巡检计划已完成',\r\n\t\t\t\t\t\t//         icon: 'success'\r\n\t\t\t\t\t\t//       });\r\n\t\t\t\t\t\t//       this.loadPlanDetail();\r\n\t\t\t\t\t\t//     }\r\n\t\t\t\t\t\t//   }\r\n\t\t\t\t\t\t// });\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 模拟完成\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '巡检计划已完成',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tthis.planInfo.status = 'completed';\r\n\t\t\t\t\t\t\tthis.planInfo.progress = '12/12';\r\n\t\t\t\t\t\t\tthis.planTasks.forEach(task => {\r\n\t\t\t\t\t\t\t\tif (task.status === 'pending') {\r\n\t\t\t\t\t\t\t\t\ttask.status = 'skipped';\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tthis.calculateStats();\r\n\t\t\t\t\t\t}, 500);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 导出报告\r\n\t\texportReport() {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '报告已导出',\r\n\t\t\t\ticon: 'success'\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 格式化日期为yyyy-MM-dd\r\n\t\tformatDate(dateStr) {\r\n\t\t\tif (!dateStr) return '';\r\n\t\t\tconst date = new Date(dateStr);\r\n\t\t\tif (isNaN(date.getTime())) return dateStr;\r\n\t\t\t\r\n\t\t\tconst year = date.getFullYear();\r\n\t\t\tconst month = (date.getMonth() + 1).toString().padStart(2, '0');\r\n\t\t\tconst day = date.getDate().toString().padStart(2, '0');\r\n\t\t\t\r\n\t\t\treturn `${year}-${month}-${day}`;\r\n\t\t},\r\n\t\t\r\n\t\t// 格式化日期时间为yyyy-MM-dd HH:mm:ss\r\n\t\tformatDateTime(dateStr) {\r\n\t\t\tif (!dateStr) return '';\r\n\t\t\tconst date = new Date(dateStr);\r\n\t\t\tif (isNaN(date.getTime())) return dateStr;\r\n\t\t\t\r\n\t\t\tconst year = date.getFullYear();\r\n\t\t\tconst month = (date.getMonth() + 1).toString().padStart(2, '0');\r\n\t\t\tconst day = date.getDate().toString().padStart(2, '0');\r\n\t\t\tconst hour = date.getHours().toString().padStart(2, '0');\r\n\t\t\tconst minute = date.getMinutes().toString().padStart(2, '0');\r\n\t\t\tconst second = date.getSeconds().toString().padStart(2, '0');\r\n\t\t\t\r\n\t\t\treturn `${year}-${month}-${day} ${hour}:${minute}:${second}`;\r\n\t\t},\r\n\t\t\r\n\t\t// 获取巡检周期文本\r\n\t\tgetScheduleText(scheduleType, scheduleWeekDays, scheduleMonthDays) {\r\n\t\t\tconst scheduleMap = {\r\n\t\t\t\t'daily': '每日',\r\n\t\t\t\t'weekly': '每周',\r\n\t\t\t\t'monthly': '每月'\r\n\t\t\t};\r\n\t\t\tconst scheduleText = scheduleMap[scheduleType] || '未知';\r\n\t\t\t\r\n\t\t\tif (scheduleType === 'daily') {\r\n\t\t\t\treturn scheduleText;\r\n\t\t\t} else if (scheduleType === 'weekly' && scheduleWeekDays && scheduleWeekDays.length > 0) {\r\n\t\t\t\tconst weekDayMap = {\r\n\t\t\t\t\t1: '周一',\r\n\t\t\t\t\t2: '周二',\r\n\t\t\t\t\t3: '周三',\r\n\t\t\t\t\t4: '周四',\r\n\t\t\t\t\t5: '周五',\r\n\t\t\t\t\t6: '周六',\r\n\t\t\t\t\t7: '周日',\r\n\t\t\t\t};\r\n\t\t\t\t\r\n\t\t\t\tconst weekDays = scheduleWeekDays.map(day => weekDayMap[day] || `周${day}`).join('、');\r\n\t\t\t\treturn `${scheduleText}（${weekDays}）`;\r\n\t\t\t} else if (scheduleType === 'monthly' && scheduleMonthDays && scheduleMonthDays.length > 0) {\r\n\t\t\t\tconst monthDays = scheduleMonthDays.map(day => `${day}日`).join('、');\r\n\t\t\t\treturn `${scheduleText}（${monthDays}）`;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\treturn scheduleText;\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.patrol-detail-container {\r\n\t\tpadding: 20rpx;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\t// min-height: 100vh;\r\n\t\t// padding-bottom: 120rpx;\r\n\t}\r\n\t\r\n\t.detail-card {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 12rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);\r\n\t\t\r\n\t\t.card-header {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\r\n\t\t\t.card-title {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tpadding-left: 20rpx;\r\n\t\t\t\t\r\n\t\t\t\t&::before {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t\ttop: 8rpx;\r\n\t\t\t\t\twidth: 6rpx;\r\n\t\t\t\t\theight: 28rpx;\r\n\t\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\t\tborder-radius: 3rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.plan-status {\r\n\t\t\t\tpadding: 6rpx 16rpx;\r\n\t\t\t\tborder-radius: 4rpx;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\r\n\t\t\t\t&.pending {\r\n\t\t\t\t\tbackground-color: rgba(24, 144, 255, 0.1);\r\n\t\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&.processing {\r\n\t\t\t\t\tbackground-color: rgba(82, 196, 26, 0.1);\r\n\t\t\t\t\tcolor: $uni-color-success;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&.completed {\r\n\t\t\t\t\tbackground-color: rgba(82, 196, 26, 0.1);\r\n\t\t\t\t\tcolor: $uni-color-success;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&.overdue {\r\n\t\t\t\t\tbackground-color: rgba(245, 34, 45, 0.1);\r\n\t\t\t\t\tcolor: $uni-color-error;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&.canceled {\r\n\t\t\t\t\tbackground-color: rgba(102, 102, 102, 0.1);\r\n\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.info-group {\r\n\t\tmargin-bottom: 20rpx;\r\n\t\t\r\n\t\t.info-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\tmargin-bottom: 16rpx;\r\n\t\t\t\r\n\t\t\t&:last-child {\r\n\t\t\t\tmargin-bottom: 0;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.info-label {\r\n\t\t\t\twidth: 160rpx;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.info-value {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t\r\n\t\t\t\t&.progress {\r\n\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.progress-bar {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\theight: 10rpx;\r\n\t\t\t\tbackground-color: #eee;\r\n\t\t\t\tborder-radius: 5rpx;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\tmargin-top: 10rpx;\r\n\t\t\t\t\r\n\t\t\t\t.progress-inner {\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.plan-desc {\r\n\t\tborder-top: 1rpx solid #eee;\r\n\t\tpadding-top: 20rpx;\r\n\t\t\r\n\t\t.desc-title {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\tmargin-bottom: 8rpx;\r\n\t\t\tdisplay: block;\r\n\t\t}\r\n\t\t\r\n\t\t.desc-content {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: $uni-text-color;\r\n\t\t\tline-height: 1.6;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.task-list {\r\n\t\t.task-item {\r\n\t\t\tborder-bottom: 1rpx solid #eee;\r\n\t\t\tpadding: 20rpx 0;\r\n\t\t\t\r\n\t\t\t&:last-child {\r\n\t\t\t\tborder-bottom: none;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.task-header {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\t\r\n\t\t\t\t.task-left {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.task-status {\r\n\t\t\t\t\t\twidth: 12rpx;\r\n\t\t\t\t\t\theight: 12rpx;\r\n\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\tmargin-right: 16rpx;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&.pending {\r\n\t\t\t\t\t\t\tbackground-color: $uni-text-color-grey;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&.completed {\r\n\t\t\t\t\t\t\tbackground-color: $uni-color-success;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&.skipped {\r\n\t\t\t\t\t\t\tbackground-color: $uni-color-warning;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.task-title {\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.task-details {\r\n\t\t\t\tmargin-top: 16rpx;\r\n\t\t\t\tpadding: 16rpx;\r\n\t\t\t\tbackground-color: #f8f8f8;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\r\n\t\t\t\t.detail-row {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tmargin-bottom: 12rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\tmargin-bottom: 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.detail-label {\r\n\t\t\t\t\t\twidth: 120rpx;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.detail-value {\r\n\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: $uni-text-color;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&.value-abnormal {\r\n\t\t\t\t\t\t\tcolor: $uni-color-error;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.task-images {\r\n\t\t\t\t\tmargin-top: 16rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.images-title {\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.image-list {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tflex-wrap: wrap;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\timage {\r\n\t\t\t\t\t\t\twidth: 140rpx;\r\n\t\t\t\t\t\t\theight: 140rpx;\r\n\t\t\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.stats-container {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\t\r\n\t\t.stats-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t\t\r\n\t\t\t.stats-value {\r\n\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.stats-label {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.action-buttons {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tpadding: 20rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\tbox-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);\r\n\t\t\r\n\t\t.action-btn {\r\n\t\t\tflex: 1;\r\n\t\t\theight: 80rpx;\r\n\t\t\tline-height: 80rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tmargin: 0 10rpx;\r\n\t\t\t\r\n\t\t\t&.start {\r\n\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.continue {\r\n\t\t\t\tbackground-color: $uni-color-success;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.complete {\r\n\t\t\t\tbackground-color: $uni-color-warning;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.export {\r\n\t\t\t\tbackground-color: $uni-color-primary;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style> ", "import MiniProgramPage from 'E:/taibo_company/tb_project/shaanxi_jieming_new_energy_company/4-Source/app/frontend/heating_maintenance/pages/patrol/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "patrolApi"], "mappings": ";;;AAuIA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,QAAQ;AAAA,MACR,UAAU;AAAA,QACT,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,WAAW;AAAA,QACX,SAAS;AAAA,QACT,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,QACV,aAAa;AAAA,QACb,QAAQ;AAAA,MACR;AAAA,MACD,WAAW,CAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOb,WAAW;AAAA;AAAA,MAEX,UAAU;AAAA,IACX;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,kBAAkB;AACjB,UAAI,CAAC,KAAK,SAAS;AAAU,eAAO;AAEpC,YAAM,gBAAgB,KAAK,SAAS,SAAS,MAAM,GAAG;AACtD,UAAI,cAAc,WAAW;AAAG,eAAO;AAEvC,YAAM,YAAY,SAAS,cAAc,CAAC,CAAC;AAC3C,YAAM,QAAQ,SAAS,cAAc,CAAC,CAAC;AAEvC,UAAI,MAAM,SAAS,KAAK,MAAM,KAAK,KAAK,UAAU;AAAG,eAAO;AAE5D,aAAO,KAAK,MAAO,YAAY,QAAS,GAAG;AAAA,IAC5C;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AACf,SAAK,SAAS,QAAQ;AAEtB,SAAK,WAAW,QAAQ,SAAS;AAEjC,QAAI,KAAK,UAAU;AAClB,WAAK,uBAAsB;AAAA,WACrB;AACN,WAAK,eAAc;AAAA,IACpB;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,yBAAyB;AACxB,WAAK,YAAY;AAEjBA,oBAAAA,MAAI,YAAY;AAAA,QACb,OAAO;AAAA,MACV,CAAC;AACDC,0BAAU,sBAAsB,KAAK,MAAM,EACzC,KAAK,SAAO;AACZ,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AACjC,gBAAM,OAAO,IAAI;AACjB,gBAAM,aAAa,KAAK;AAGxB,eAAK,WAAW;AAAA,YACf,IAAI,WAAW;AAAA,YACf,OAAO,WAAW;AAAA,YAClB,MAAM,OAAO,WAAW,EAAE;AAAA,YAC1B,MAAM;AAAA,YACN,WAAW,WAAW;AAAA,YACtB,SAAS,WAAW;AAAA,YACpB,UAAU,WAAW;AAAA,YACrB,SAAS,WAAW,gBAAgB;AAAA,YACpC,aAAa,WAAW,UAAU;AAAA,YAClC,QAAQ,WAAW;AAAA,YACnB,cAAc,WAAW;AAAA,YACzB,kBAAkB,WAAW;AAAA,YAC7B,mBAAmB,WAAW;AAAA;AAI/B,cAAI,KAAK,cAAc,KAAK,WAAW,SAAS,GAAG;AAClD,iBAAK,YAAY,KAAK,WAAW,IAAI,aAAW;AAAA,cAC/C,IAAI,OAAO;AAAA,cACX,OAAO,OAAO;AAAA,cACd,QAAQ,OAAO,cAAc;AAAA,cAC7B,OAAO,OAAO,gBAAgB;AAAA,cAC9B,SAAS,OAAO,eAAe;AAAA,cAC/B,UAAU,OAAO,eAAe;AAAA,cAChC,MAAM,OAAO,QAAQ;AAAA,cACrB,aAAa,OAAO,cAAc;AAAA,cAClC,YAAY,OAAO,gBAAgB;AAAA,cACnC,eAAe,KAAK,eAAe,OAAO,UAAU;AAAA,cACpD,QAAQ,OAAO,eAAe;AAAA,cAC9B,QAAQ;AAAA;AAAA,cACR,QAAQ,OAAO,UAAU,CAAE;AAAA,cAC3B,aAAa;AAAA,cACb,UAAU,OAAO;AAAA,cACjB,WAAW,OAAO;AAAA,YAClB,EAAC;AAAA,UACH;AAAA,eAaM;AACND,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,IAAI,WAAW;AAAA,YACtB,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,OACA,EACA,MAAM,SAAO;AACbA,sBAAG,MAAC,YAAW;AACfA,sBAAA,MAAA,MAAA,SAAA,kCAAc,eAAe,GAAG;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,OACD,EACA,QAAQ,MAAM;AACd,aAAK,YAAY;AACjBA,sBAAG,MAAC,YAAW;AAAA,MAChB,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB;AAChB,WAAK,YAAY;AAEjBA,oBAAAA,MAAI,YAAY;AAAA,QACb,OAAO;AAAA,MACV,CAAC;AACD,YAAM,UAAU,KAAK,WAClBC,oBAAU,sBAAsB,KAAK,MAAM,IAC3CA,UAAAA,UAAU,cAAc,KAAK,MAAM;AAEtC,cAAQ,KAAK,SAAO;;AACnB,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAEjC,gBAAM,OAAO,IAAI;AACjB,eAAK,WAAW;AAAA,YACf,IAAI,KAAK;AAAA,YACT,OAAO,KAAK;AAAA,YACZ,MAAM,KAAK;AAAA,YACX,MAAM,KAAK;AAAA,YACX,WAAW,KAAK;AAAA,YAChB,SAAS,KAAK;AAAA,YACd,UAAU,KAAK;AAAA,YACf,WAAS,UAAK,kBAAL,mBAAoB,KAAK,SAAQ;AAAA,YAC1C,UAAU,OAAK,UAAK,gBAAL,mBAAkB,WAAU,CAAC;AAAA;AAAA,YAC5C,aAAa,KAAK,eAAe;AAAA,YACjC,QAAQ,KAAK;AAAA,YACb,cAAc,KAAK;AAAA,YACnB,kBAAkB,KAAK;AAAA,YACvB,mBAAmB,KAAK;AAAA;AAIzB,cAAI,KAAK,eAAe,KAAK,YAAY,SAAS,GAAG;AACpD,iBAAK,YAAY,KAAK,YAAY,IAAI,WAAS;AAAA,cAC9C,IAAI,KAAK;AAAA,cACT,OAAO,KAAK;AAAA,cACZ,QAAQ,KAAK,cAAc;AAAA,cAC3B,OAAO,KAAK,eAAe;AAAA,cAC3B,SAAS,KAAK;AAAA,cACd,UAAU,KAAK,eAAe;AAAA,cAC9B,MAAM,KAAK;AAAA,cACX,aAAa;AAAA,cACb,YAAY;AAAA,cACZ,eAAe;AAAA,cACf,QAAQ;AAAA,cACR,QAAQ;AAAA;AAAA,cACR,QAAQ,CAAE;AAAA,cACV,aAAa;AAAA,YACb,EAAC;AAGF,kBAAM,iBAAiB,KAAK,WAAW,KAAK,UAAU,SAAS;AAC/D,iBAAK,SAAS,WAAW,GAAG,cAAc,IAAI,KAAK,UAAU,MAAM;AAAA,UACpE;AAGAD,wBAAG,MAAC,YAAW;AAAA,eACT;AACNA,wBAAG,MAAC,YAAW;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,IAAI,WAAW;AAAA,YACtB,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,OACA,EACA,MAAM,SAAO;AACbA,sBAAG,MAAC,YAAW;AACfA,sBAAc,MAAA,MAAA,SAAA,kCAAA,eAAe,GAAG;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,OACD,EACA,QAAQ,MAAM;AACd,aAAK,YAAY;AACjBA,sBAAG,MAAC,YAAW;AAAA,MAChB,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB;AAChB,UAAI,CAAC,KAAK,aAAa,KAAK,UAAU,WAAW,GAAG;AACnD,aAAK,QAAQ;AAAA,UACZ,YAAY;AAAA,UACZ,gBAAgB;AAAA,UAChB,eAAe;AAAA,UACf,gBAAgB;AAAA;AAEjB;AAAA,MACD;AAEA,YAAM,aAAa,KAAK,UAAU;AAClC,YAAM,iBAAiB,KAAK,UAAU,OAAO,UAAQ,KAAK,WAAW,WAAW,EAAE;AAClF,YAAM,gBAAgB,KAAK,UAAU,OAAO,UAAQ,KAAK,UAAU,EAAE;AACrE,YAAM,iBAAiB,aAAa,IAAI,KAAK,MAAO,iBAAiB,aAAc,GAAG,IAAI;AAE1F,WAAK,QAAQ;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;IAED;AAAA;AAAA,IAGD,cAAc,QAAQ;AACrB,YAAM,YAAY;AAAA,QACjB,WAAW;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,WAAW;AAAA,QACX,YAAY;AAAA;AAEb,aAAO,UAAU,MAAM,KAAK;AAAA,IAC5B;AAAA;AAAA,IAGD,kBAAkB,OAAO;AACxB,WAAK,KAAK,KAAK,UAAU,KAAK,GAAG,eAAe,CAAC,KAAK,UAAU,KAAK,EAAE,WAAW;AAAA,IAClF;AAAA;AAAA,IAGD,aAAa,QAAQ,SAAS;AAC7BA,oBAAAA,MAAI,aAAa;AAAA,QAChB,MAAM;AAAA,QACN,SAAS,OAAO,OAAO;AAAA,MACxB,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AACbA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,4BAA4B,KAAK,MAAM;AAAA,MAC7C,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB;AAChBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,4BAA4B,KAAK,MAAM;AAAA,MAC7C,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB;AAChBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAiBhBA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACP,CAAC;AAED,uBAAW,MAAM;AAChB,mBAAK,SAAS,SAAS;AACvB,mBAAK,SAAS,WAAW;AACzB,mBAAK,UAAU,QAAQ,UAAQ;AAC9B,oBAAI,KAAK,WAAW,WAAW;AAC9B,uBAAK,SAAS;AAAA,gBACf;AAAA,cACD,CAAC;AACD,mBAAK,eAAc;AAAA,YACnB,GAAE,GAAG;AAAA,UACP;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACdA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,SAAS;AACnB,UAAI,CAAC;AAAS,eAAO;AACrB,YAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,UAAI,MAAM,KAAK,QAAO,CAAE;AAAG,eAAO;AAElC,YAAM,OAAO,KAAK;AAClB,YAAM,SAAS,KAAK,aAAa,GAAG,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC9D,YAAM,MAAM,KAAK,QAAS,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAErD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,IAC9B;AAAA;AAAA,IAGD,eAAe,SAAS;AACvB,UAAI,CAAC;AAAS,eAAO;AACrB,YAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,UAAI,MAAM,KAAK,QAAO,CAAE;AAAG,eAAO;AAElC,YAAM,OAAO,KAAK;AAClB,YAAM,SAAS,KAAK,aAAa,GAAG,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC9D,YAAM,MAAM,KAAK,QAAS,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACrD,YAAM,OAAO,KAAK,SAAU,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACvD,YAAM,SAAS,KAAK,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC3D,YAAM,SAAS,KAAK,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAE3D,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM,IAAI,MAAM;AAAA,IAC1D;AAAA;AAAA,IAGD,gBAAgB,cAAc,kBAAkB,mBAAmB;AAClE,YAAM,cAAc;AAAA,QACnB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,WAAW;AAAA;AAEZ,YAAM,eAAe,YAAY,YAAY,KAAK;AAElD,UAAI,iBAAiB,SAAS;AAC7B,eAAO;AAAA,MACR,WAAW,iBAAiB,YAAY,oBAAoB,iBAAiB,SAAS,GAAG;AACxF,cAAM,aAAa;AAAA,UAClB,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA;AAGJ,cAAM,WAAW,iBAAiB,IAAI,SAAO,WAAW,GAAG,KAAK,IAAI,GAAG,EAAE,EAAE,KAAK,GAAG;AACnF,eAAO,GAAG,YAAY,IAAI,QAAQ;AAAA,MACnC,WAAW,iBAAiB,aAAa,qBAAqB,kBAAkB,SAAS,GAAG;AAC3F,cAAM,YAAY,kBAAkB,IAAI,SAAO,GAAG,GAAG,GAAG,EAAE,KAAK,GAAG;AAClE,eAAO,GAAG,YAAY,IAAI,SAAS;AAAA,MACpC;AAEA,aAAO;AAAA,IACR;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACphBA,GAAG,WAAW,eAAe;"}