"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const utils_upload = require("../../utils/upload.js");
const _sfc_main = {
  data() {
    const now = /* @__PURE__ */ new Date();
    const year = now.getFullYear();
    const month = (now.getMonth() + 1).toString().padStart(2, "0");
    const day = now.getDate().toString().padStart(2, "0");
    now.getHours().toString().padStart(2, "0");
    now.getMinutes().toString().padStart(2, "0");
    return {
      // 故障类型
      faultTypes: [],
      typeIndex: -1,
      // 热力单元 
      heatUnits: [],
      unitIndex: -1,
      // 告警ID 默认为 -1
      alarmId: -1,
      // 故障来源
      faultSources: ["用户投诉", "巡检上报"],
      sourceIndex: 0,
      // 默认设置为"用户投诉"(索引0)
      // 故障等级 提示/一般/重要/严重	
      faultLevels: [
        { label: "提示", value: "提示" },
        { label: "一般", value: "一般" },
        { label: "重要", value: "重要" },
        { label: "严重", value: "严重" }
      ],
      faultLevel: "一般",
      // 故障描述
      faultDesc: "",
      // 附件
      imageList: [],
      // 本地临时路径
      serverImageList: [],
      // 服务器路径
      videoPath: "",
      // 本地临时路径
      serverVideoPath: "",
      // 服务器路径
      // 发生时间
      faultDate: "",
      faultTime: "",
      startDate: `${year - 1}-${month}-${day}`,
      endDate: `${year}-${month}-${day}`,
      // 设备ID（如果从设备详情页跳转过来）
      deviceId: "",
      // 加载状态
      isLoading: false,
      // 地址字段
      buildingNo: "",
      unitNo: "",
      roomNo: "",
      formattedAddress: "",
      // 格式化后的完整地址
      addressInputError: false,
      // 地址输入错误状态
      // 热力单位ID
      heatUnitId: null
    };
  },
  onShow() {
    this.loadFaultTypeOptions();
  },
  async onLoad(options) {
    if (options.heatUnitId) {
      this.heatUnitId = options.heatUnitId;
      common_vendor.index.__f__("log", "at pages/fault/report.vue:285", "从参数接收到热力单位ID:", this.heatUnitId);
    }
    try {
      await this.loadHeatUnits();
      common_vendor.index.__f__("log", "at pages/fault/report.vue:291", "热用户列表加载完毕");
    } catch (loadErr) {
      common_vendor.index.__f__("error", "at pages/fault/report.vue:293", "onLoad 中加载热用户列表失败:", loadErr);
    }
    const now = /* @__PURE__ */ new Date();
    const year = now.getFullYear();
    const month = (now.getMonth() + 1).toString().padStart(2, "0");
    const day = now.getDate().toString().padStart(2, "0");
    const hours = now.getHours().toString().padStart(2, "0");
    const minutes = now.getMinutes().toString().padStart(2, "0");
    this.startDate = `${year - 1}-${month}-${day}`;
    this.endDate = `${year}-${month}-${day}`;
    if (!this.faultDate)
      this.faultDate = `${year}-${month}-${day}`;
    if (!this.faultTime)
      this.faultTime = `${hours}:${minutes}`;
    if (options.deviceId) {
      this.deviceId = options.deviceId;
      common_vendor.index.__f__("log", "at pages/fault/report.vue:314", "从设备详情页跳转, 设备ID:", this.deviceId);
    }
    if (options.alarmData) {
      common_vendor.index.__f__("log", "at pages/fault/report.vue:320", "接收到告警传递的数据（原始）:", options.alarmData);
      try {
        const alarmInfo = JSON.parse(decodeURIComponent(options.alarmData));
        common_vendor.index.__f__("log", "at pages/fault/report.vue:323", "解析后的告警数据:", alarmInfo);
        const sourceIdx = this.faultSources.findIndex((source) => source === "系统检测");
        if (sourceIdx > -1)
          this.sourceIndex = sourceIdx;
        const typeIdx = this.faultTypes.findIndex((type) => type === "设备故障");
        if (typeIdx > -1)
          this.typeIndex = typeIdx;
        if (alarmInfo.faultDesc)
          this.faultDesc = alarmInfo.faultDesc;
        if (alarmInfo.occurTime) {
          const dateTimeParts = alarmInfo.occurTime.split(" ");
          if (dateTimeParts.length === 2) {
            this.faultDate = dateTimeParts[0];
            this.faultTime = dateTimeParts[1];
          } else {
            common_vendor.index.__f__("warn", "at pages/fault/report.vue:343", "无法解析告警传递的 occurTime 格式:", alarmInfo.occurTime);
          }
        }
        if (alarmInfo.faultLevel) {
          const levelObj = this.faultLevels.find((level) => level.value === alarmInfo.faultLevel);
          if (levelObj) {
            this.faultLevel = levelObj.value;
            common_vendor.index.__f__("log", "at pages/fault/report.vue:352", `已根据告警信息预选故障等级: ${this.faultLevel}`);
          } else {
            common_vendor.index.__f__("warn", "at pages/fault/report.vue:355", `告警传递的故障等级 '${alarmInfo.faultLevel}' 在选项中未找到，使用默认值 '一般'`);
            this.faultLevel = "一般";
          }
        } else {
          this.faultLevel = "一般";
        }
        if (alarmInfo.heatUnitId) {
          this.heatUnitId = alarmInfo.heatUnitId;
        } else if (alarmInfo.heatUnitName) {
          this.selectUnitByName(alarmInfo.heatUnitName);
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/fault/report.vue:371", "解析告警传递数据失败:", e);
      }
    }
  },
  methods: {
    // 加载热用户列表
    async loadHeatUnits() {
      this.isLoading = true;
      const userId = common_vendor.index.getStorageSync("userId");
      return utils_api.heatUnitApi.getByUserId(userId).then((res) => {
        common_vendor.index.__f__("log", "at pages/fault/report.vue:385", "热用户列表接口响应:", res);
        if (res.code === 200 && res.data) {
          this.heatUnits = res.data;
          if (this.heatUnits.length > 0) {
            if (this.heatUnitId) {
              const index = this.heatUnits.findIndex((unit) => unit.id === this.heatUnitId);
              if (index > -1) {
                this.unitIndex = index;
                common_vendor.index.__f__("log", "at pages/fault/report.vue:396", "根据传入的热力单位ID预选:", this.heatUnits[index].name);
              } else {
                this.unitIndex = 0;
                common_vendor.index.__f__("log", "at pages/fault/report.vue:400", "未找到匹配的热力单位，默认选择第一个:", this.heatUnits[0].name);
              }
            } else {
              this.unitIndex = 0;
              common_vendor.index.__f__("log", "at pages/fault/report.vue:405", "默认选择第一个热力单位:", this.heatUnits[0].name);
            }
          }
        } else {
          this.heatUnits = [];
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/fault/report.vue:413", "获取热用户列表失败:", err);
        this.heatUnits = [];
      }).finally(() => {
        this.isLoading = false;
      });
    },
    // 加载故障类型选项
    loadFaultTypeOptions() {
      utils_api.dictApi.getDictDataByDictId(5).then((res) => {
        if (res.code === 200 && res.data) {
          common_vendor.index.__f__("log", "at pages/fault/report.vue:425", res.data);
          this.faultTypes = res.data.map((item) => item.name);
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/fault/report.vue:431", "获取巡检类型选项失败:", err);
      });
    },
    // 故障类型选择
    handleTypeChange(e) {
      this.typeIndex = e.detail.value;
    },
    // 热用户选择
    handleUnitChange(e) {
      this.unitIndex = e.detail.value;
    },
    // 故障等级选择
    selectLevel(level) {
      this.faultLevel = level;
    },
    // 日期选择
    handleDateChange(e) {
      this.faultDate = e.detail.value;
    },
    // 时间选择
    handleTimeChange(e) {
      this.faultTime = e.detail.value;
    },
    // 选择图片
    chooseImage() {
      common_vendor.index.chooseImage({
        count: 6 - this.imageList.length,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          common_vendor.index.showLoading({
            title: "上传中...",
            mask: true
          });
          const token = common_vendor.index.getStorageSync("token");
          if (!token) {
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "请先登录",
              icon: "none"
            });
            return;
          }
          const uploadPromises = res.tempFilePaths.map((path) => {
            return utils_upload.uploadUtils.uploadImage(path).then((serverPath) => {
              this.imageList.push(path);
              this.serverImageList.push(serverPath);
              return serverPath;
            });
          });
          Promise.all(uploadPromises).then(() => {
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "上传成功",
              icon: "success"
            });
          }).catch((err) => {
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: err.message || "上传失败",
              icon: "none"
            });
          });
        }
      });
    },
    // 预览图片
    previewImage(index) {
      const previewUrls = this.imageList.map((path, i) => {
        if (this.serverImageList[i]) {
          return utils_upload.uploadUtils.getFileUrl(this.serverImageList[i]);
        }
        return path;
      });
      common_vendor.index.previewImage({
        urls: previewUrls,
        current: previewUrls[index]
      });
    },
    // 删除图片
    deleteImage(index) {
      this.imageList.splice(index, 1);
      this.serverImageList.splice(index, 1);
    },
    // 选择视频
    chooseVideo() {
      common_vendor.index.chooseVideo({
        sourceType: ["album", "camera"],
        maxDuration: 60,
        camera: "back",
        success: (res) => {
          common_vendor.index.showLoading({
            title: "上传中...",
            mask: true
          });
          const token = common_vendor.index.getStorageSync("token");
          if (!token) {
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "请先登录",
              icon: "none"
            });
            return;
          }
          utils_upload.uploadUtils.uploadVideo(res.tempFilePath).then((serverPath) => {
            this.videoPath = res.tempFilePath;
            this.serverVideoPath = serverPath;
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "上传成功",
              icon: "success"
            });
          }).catch((err) => {
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: err.message || "上传失败",
              icon: "none"
            });
          });
        }
      });
    },
    // 获取视频URL
    getVideoUrl() {
      if (this.serverVideoPath && this.serverVideoPath.startsWith("@http")) {
        const cleanUrl = this.serverVideoPath.substring(1);
        common_vendor.index.__f__("log", "at pages/fault/report.vue:594", "视频路径以@http开头，清理后:", cleanUrl);
        return cleanUrl;
      }
      if (this.serverVideoPath) {
        if (this.serverVideoPath.startsWith("http")) {
          return this.serverVideoPath;
        }
        const url = utils_upload.uploadUtils.getFileUrl(this.serverVideoPath);
        common_vendor.index.__f__("log", "at pages/fault/report.vue:605", "处理后的视频URL:", url);
        return url;
      }
      common_vendor.index.__f__("log", "at pages/fault/report.vue:610", "使用本地视频路径:", this.videoPath);
      return this.videoPath;
    },
    // 在浏览器中打开视频
    openInBrowser() {
      let url = this.getVideoUrl();
      if (!url) {
        common_vendor.index.showToast({
          title: "视频URL无效",
          icon: "none"
        });
        return;
      }
      common_vendor.index.__f__("log", "at pages/fault/report.vue:627", "在浏览器中打开视频:", url);
      plus.runtime.openURL(url, (err) => {
        if (err) {
          common_vendor.index.__f__("error", "at pages/fault/report.vue:632", "打开浏览器失败:", err);
          common_vendor.index.showToast({
            title: "打开浏览器失败",
            icon: "none"
          });
        }
      });
    },
    // 视频加载错误处理
    onVideoError(e) {
      common_vendor.index.__f__("error", "at pages/fault/report.vue:643", "视频加载失败:", e.detail);
      common_vendor.index.__f__("error", "at pages/fault/report.vue:644", "视频路径:", this.videoPath);
      common_vendor.index.__f__("error", "at pages/fault/report.vue:645", "服务器视频路径:", this.serverVideoPath);
      common_vendor.index.__f__("error", "at pages/fault/report.vue:646", "处理后URL:", this.getVideoUrl());
      common_vendor.index.showToast({
        title: "视频加载失败，请尝试其他方式查看",
        icon: "none",
        duration: 2e3
      });
    },
    // 视频加载成功
    onVideoLoad(e) {
      common_vendor.index.__f__("log", "at pages/fault/report.vue:657", "视频加载成功, 路径:", this.videoPath);
      common_vendor.index.__f__("log", "at pages/fault/report.vue:658", "视频元数据:", e.detail);
    },
    // 删除视频
    deleteVideo() {
      this.videoPath = "";
      this.serverVideoPath = "";
    },
    // 上传附件
    uploadAttachments() {
      return new Promise((resolve) => {
        const attachments = [];
        this.serverImageList.forEach((serverPath, index) => {
          attachments.push({
            file_type: "image",
            file_path: serverPath
          });
        });
        if (this.serverVideoPath) {
          attachments.push({
            file_type: "video",
            file_path: this.serverVideoPath
          });
        }
        resolve(attachments);
      });
    },
    // 表单验证
    validateForm() {
      if (this.typeIndex === -1) {
        common_vendor.index.showToast({
          title: "请选择故障类型",
          icon: "none"
        });
        return false;
      }
      if (this.unitIndex === -1) {
        common_vendor.index.showToast({
          title: "请选择热用户",
          icon: "none"
        });
        return false;
      }
      if ((this.buildingNo || this.unitNo || this.roomNo) && !(this.buildingNo && this.unitNo && this.roomNo)) {
        common_vendor.index.showToast({
          title: "请完整填写楼号、单元号和房号",
          icon: "none",
          duration: 3e3
        });
        return false;
      }
      if (!this.faultLevel) {
        common_vendor.index.showToast({
          title: "请选择故障等级",
          icon: "none"
        });
        return false;
      }
      if (!this.faultDesc.trim()) {
        common_vendor.index.showToast({
          title: "请填写故障描述",
          icon: "none"
        });
        return false;
      }
      if (!this.faultDate || !this.faultTime) {
        common_vendor.index.showToast({
          title: "请选择故障发生时间",
          icon: "none"
        });
        return false;
      }
      return true;
    },
    // 提交故障上报
    async submitReport() {
      if (!this.validateForm())
        return;
      common_vendor.index.showLoading({
        title: "正在提交..."
      });
      try {
        const attachments = await this.uploadAttachments();
        const userId = common_vendor.index.getStorageSync("userId") || 1;
        if (this.unitIndex === -1 || !this.heatUnits[this.unitIndex]) {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "请选择热用户",
            icon: "none"
          });
          return;
        }
        this.updateFormattedAddress();
        const reportData = {
          heat_unit_id: this.heatUnits[this.unitIndex].id,
          alarm_id: this.alarmId,
          fault_type: this.faultTypes[this.typeIndex],
          fault_level: this.faultLevel,
          fault_desc: this.faultDesc,
          fault_source: this.faultSources[this.sourceIndex],
          occur_time: `${this.faultDate} ${this.faultTime}:00`,
          report_user_id: userId,
          address: this.formattedAddress,
          attachment: attachments
        };
        common_vendor.index.__f__("log", "at pages/fault/report.vue:792", "提交故障上报数据:", reportData);
        utils_api.faultApi.reportFault(reportData).then((res) => {
          if (res.code === 200) {
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "故障上报成功",
              icon: "success"
            });
            setTimeout(() => {
              common_vendor.index.navigateBack();
            }, 1500);
          } else {
            throw new Error(res.message || "上报失败");
          }
        }).catch((err) => {
          common_vendor.index.hideLoading();
          const errorMsg = err.message || "网络异常，请稍后重试";
          const isHouseError = errorMsg.includes("未找到住户") || errorMsg.includes("房间号格式") || errorMsg.includes("楼号") || errorMsg.includes("单元号") || errorMsg.includes("房号");
          if (isHouseError) {
            this.highlightAddressInputs();
            common_vendor.index.showModal({
              title: "住户信息错误",
              content: errorMsg,
              showCancel: false,
              confirmText: "我知道了"
            });
          } else {
            common_vendor.index.showToast({
              title: errorMsg,
              icon: "none",
              duration: 3e3
            });
          }
        });
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "提交过程中出错，请重试",
          icon: "none"
        });
      }
    },
    // 高亮显示地址输入框
    highlightAddressInputs() {
      this.addressInputError = true;
      setTimeout(() => {
        this.addressInputError = false;
      }, 3e3);
    },
    // ----> 选择热用户通过名称 <---- -->
    selectUnitByName(unitName) {
      const unitNameTrim = unitName.trim();
      if (!this.heatUnits || !Array.isArray(this.heatUnits)) {
        common_vendor.index.__f__("error", "at pages/fault/report.vue:871", "selectUnitByName 调用时 heatUnits 列表无效");
        return;
      }
      const index = this.heatUnits.findIndex((unit) => unit.name === unitNameTrim);
      if (index > -1) {
        this.unitIndex = index;
        common_vendor.index.__f__("log", "at pages/fault/report.vue:877", `已根据名称 '${unitName}' 预选热用户，索引: ${index}`);
      } else {
        common_vendor.index.__f__("warn", "at pages/fault/report.vue:879", `未在热用户列表中找到名称为 '${unitName}' 的项`);
        if (this.heatUnits.length === 1) {
          this.unitIndex = 0;
          common_vendor.index.__f__("log", "at pages/fault/report.vue:884", `未找到名称匹配的热用户，但只有一个选项，已自动选择: ${this.heatUnits[0].name}`);
        } else {
          this.unitIndex = -1;
        }
      }
    },
    // 格式化地址字段
    formatAddressFields() {
      if (this.buildingNo) {
        this.buildingNo = this.buildingNo.toString().replace(/\D/g, "");
        if (this.buildingNo.length > 2) {
          this.buildingNo = this.buildingNo.substring(0, 2);
        }
        if (this.buildingNo.length === 1) {
          common_vendor.index.__f__("log", "at pages/fault/report.vue:903", "楼号已格式化:", this.buildingNo);
        }
      }
      if (this.unitNo) {
        this.unitNo = this.unitNo.toString().replace(/\D/g, "");
        if (this.unitNo.length > 2) {
          this.unitNo = this.unitNo.substring(0, 2);
        }
        if (this.unitNo.length === 1) {
          common_vendor.index.__f__("log", "at pages/fault/report.vue:917", "单元号已格式化:", this.unitNo);
        }
      }
      if (this.roomNo) {
        this.roomNo = this.roomNo.toString().replace(/\D/g, "");
        if (this.roomNo.length > 4) {
          this.roomNo = this.roomNo.substring(0, 4);
        }
        if (this.roomNo.length < 4) {
          common_vendor.index.__f__("log", "at pages/fault/report.vue:931", "房号已格式化:", this.roomNo);
        }
      }
      if ((this.buildingNo || this.unitNo || this.roomNo) && !(this.buildingNo && this.unitNo && this.roomNo)) {
        common_vendor.index.showToast({
          title: "请完整填写楼号、单元号和房号",
          icon: "none",
          duration: 2e3
        });
      }
    },
    // 更新格式化后的地址
    updateFormattedAddress() {
      if (this.buildingNo && this.unitNo && this.roomNo) {
        const paddedBuildingNo = this.buildingNo;
        const paddedUnitNo = this.unitNo;
        const paddedRoomNo = this.roomNo.padStart(4, "0");
        this.formattedAddress = `${paddedBuildingNo}-${paddedUnitNo}-${paddedRoomNo}`;
      } else {
        this.formattedAddress = "";
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.unitIndex === -1 ? "请选择热用户" : $data.heatUnits[$data.unitIndex].name),
    b: $data.unitIndex === -1 ? 1 : "",
    c: common_vendor.o((...args) => $options.handleUnitChange && $options.handleUnitChange(...args)),
    d: $data.unitIndex,
    e: $data.heatUnits,
    f: $data.buildingNo,
    g: common_vendor.o(($event) => $data.buildingNo = $event.detail.value),
    h: $data.addressInputError ? 1 : "",
    i: $data.unitNo,
    j: common_vendor.o(($event) => $data.unitNo = $event.detail.value),
    k: $data.addressInputError ? 1 : "",
    l: common_vendor.o((...args) => $options.formatAddressFields && $options.formatAddressFields(...args)),
    m: common_vendor.o([($event) => $data.roomNo = $event.detail.value, (...args) => $options.updateFormattedAddress && $options.updateFormattedAddress(...args)]),
    n: $data.roomNo,
    o: $data.addressInputError ? 1 : "",
    p: $data.addressInputError ? 1 : "",
    q: common_vendor.t($data.typeIndex === -1 ? "请选择故障类型" : $data.faultTypes[$data.typeIndex]),
    r: $data.typeIndex === -1 ? 1 : "",
    s: common_vendor.o((...args) => $options.handleTypeChange && $options.handleTypeChange(...args)),
    t: $data.typeIndex,
    v: $data.faultTypes,
    w: common_vendor.f($data.faultLevels, (level, index, i0) => {
      return {
        a: common_vendor.t(level.label),
        b: index,
        c: $data.faultLevel === level.value ? 1 : "",
        d: common_vendor.o(($event) => $options.selectLevel(level.value), index)
      };
    }),
    x: $data.faultDesc,
    y: common_vendor.o(($event) => $data.faultDesc = $event.detail.value),
    z: common_vendor.t($data.faultDesc.length),
    A: common_vendor.f($data.imageList, (image, index, i0) => {
      return {
        a: image,
        b: common_vendor.o(($event) => $options.previewImage(index), index),
        c: common_vendor.o(($event) => $options.deleteImage(index), index),
        d: index
      };
    }),
    B: $data.imageList.length < 6
  }, $data.imageList.length < 6 ? {
    C: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args))
  } : {}, {
    D: $data.videoPath
  }, $data.videoPath ? common_vendor.e({
    E: $options.getVideoUrl(),
    F: common_vendor.o((...args) => $options.onVideoError && $options.onVideoError(...args)),
    G: common_vendor.o((...args) => $options.onVideoLoad && $options.onVideoLoad(...args)),
    H: common_vendor.o((...args) => $options.deleteVideo && $options.deleteVideo(...args)),
    I: $data.serverVideoPath
  }, $data.serverVideoPath ? {
    J: common_vendor.o((...args) => $options.openInBrowser && $options.openInBrowser(...args))
  } : {}) : {}, {
    K: !$data.videoPath
  }, !$data.videoPath ? {
    L: common_vendor.o((...args) => $options.chooseVideo && $options.chooseVideo(...args))
  } : {}, {
    M: common_vendor.t($data.faultDate || "请选择日期"),
    N: !$data.faultDate ? 1 : "",
    O: $data.faultDate,
    P: $data.startDate,
    Q: $data.endDate,
    R: common_vendor.o((...args) => $options.handleDateChange && $options.handleDateChange(...args)),
    S: common_vendor.t($data.faultTime || "请选择时间"),
    T: !$data.faultTime ? 1 : "",
    U: $data.faultTime,
    V: common_vendor.o((...args) => $options.handleTimeChange && $options.handleTimeChange(...args)),
    W: common_vendor.o((...args) => $options.submitReport && $options.submitReport(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/fault/report.js.map
